#!/usr/bin/env bash

#### Run like below

# chmod +x ./deploy.sh
# _COMMENT_=$(cat <<-END
# %COMMENT%
# END
# )
# ./deploy.sh "$_COMMENT_" "%BRANCH%"

#### Additional steps

# 1. git clone
# 1. create span-web/app/config/parameters.yml
# 1. composer install
# 1. git branch --set-upstream-to=origin/master master
# 1. create the symlinks manually on the server

#### Available commands (type in commit message with space around)

# _clean_
# _encore_
# _frontend_
# _npm_
# _clear_cache_
# _usu_parcel_
# _mex_parcel_
# _no_test_
# _no_scan_

COMMENT=$1
BRANCH=$2
REVISION=$3

echo '=== Commit comment ==='
echo ${COMMENT}
echo '=== Branch ==='
echo ${BRANCH}
echo '=== Revision ==='
echo ${REVISION}

export SYMFONY_ENV=prod

echo '=== git fetch & checkout ==='
#git branch --set-upstream-to=origin/${BRANCH} ${BRANCH}

git checkout -- ./frontend/.quasar/app.styl
git checkout -- ./frontend/.quasar/variables.styl
git checkout -- ./frontend/package-lock.json

git --no-pager fetch origin ${BRANCH}
git --no-pager checkout -f ${REVISION}

echo '=== git log ==='
git --no-pager log -n 5

echo '=== git status ==='
git --no-pager status

cd span-web

echo '=== Run composer ==='
composer install --no-scripts --apcu-autoloader --no-interaction
composer dump-autoload --optimize --classmap-authoritative

## Fix vendor bug
echo '=== Fix vendor ==='
php bin/fix_vendor

if [[ ${COMMENT} == *_clear_cache_* ]]; then
  echo '=== Delete cache directory ==='
  rm -rf /var/cache/span/prod/*
  rm -rf /tmp/span_app_services_*
fi

echo '=== clear cache ==='
php bin/console cache:clear --env=prod --no-debug

if [[ ${COMMENT} == *_clear_cache_* ]]; then
  echo '=== Adjust cache directory permission ==='
  chmod -R 777 /var/cache/span/prod
fi

echo '=== Reload php-fpm to reset OpCache ==='
echo "" | sudo -S /bin/systemctl reload php8.2-fpm

echo '=== Execute database migrations ==='
php bin/console d:m:migrate --no-interaction --query-time --env=prod -vv

#echo '=== Reset more OpCache ==='
#php -r "opcache_reset();"
#host=$(grep -oP '^\s*host: (.+)$' config/parameters_p77LTy8MZcDB.yml|awk '{print $2 }')
#wget --no-check-certificate -q --spider $host/t/cron/opcache-reset

echo "=== Execute post command"
php bin/console span:dev:post-deploy --env=prod --no-debug -vv

echo '=== Install cron jobs ==='
php bin/console span:dev:install-cron-jobs --install --env=prod --no-debug -vv

# Clean
if [[ ${COMMENT} == *_clean_* ]]; then
    echo '=== Delete useless apt files ==='
    apt-get clean

    echo 'Empty useless span logs'
    true > /var/log/span/dev.log
    true > /var/log/span/prod.log

    echo 'Delete useless journal logs'
    journalctl --vacuum-size=500M
fi

# Build Encore
if [[ ${COMMENT} == *_encore_* ]]; then
    echo '=== run encore ==='

    npm install --no-save
    npm run build

    rm -rf web/build
    mv web/build-temp web/build
fi

# Build new Us Unlocked consumer portal source
if [[ ${COMMENT} == *_usu_parcel_* ]]; then
    echo '=== run usun parcel ==='

    # Temporary commands
#    npm install -g parcel-bundler
    npm install --no-save

    cd src/UsUnlockedBundle/Resources/views/res
    rm -rf .cache
    parcel build index.js --public-url /static/usun/

    rm -rf ../../../../../web/static/usun
    mv ./dist ../../../../../web/static/usun
    cd ../../../../../
fi

# Build TransferMex consumer portal source
if [[ ${COMMENT} == *_mex_parcel_* ]]; then
    echo '=== run mex parcel ==='

    # Temporary commands
#    npm install -g parcel-bundler
    npm install --no-save

    cd src/TransferMexBundle/Resources/views/res
    rm -rf .cache
    parcel build index.js --public-url /static/mexn/

    rm -rf ../../../../../web/static/mexn
    mv ./dist ../../../../../web/static/mexn
    cd ../../../../../
fi

# Build node modules
echo '=== go to web ==='
cd web/

if [[ ${COMMENT} == *_npm_* ]]; then
    echo '=== run npm install ==='

    npm install --no-save
fi

#echo '=== create TransferMex symlink ==='
#cd ./static/mex
#ln -s /var/www/transfermex/web
#cd ../../

# Build Frontend
if [[ ${COMMENT} == *_frontend_* ]]; then
    echo '=== go to frontend and npm install ==='

    cd ../../frontend/
    echo '=== install frontend ==='
    npm install --no-save
    if [ $? -eq 0 ]
    then
        echo "=== frontend installation successful ==="
        echo '=== build frontend ==='
        npm run build
        if [ $? -eq 0 ]
        then
            rm -rf ../span-web/web/mobile
            mv dist ../span-web/web/mobile
            echo "frontend compilation successful"
        else
            echo "frontend compilation failure"
        fi
    else
        echo "frontend installation failure"
    fi

    cd ../span-web/web/
fi

cd ../

if [[ ${COMMENT} == *_clear_cache_* ]]; then
  echo '=== Delete cache/dev directory ==='
  rm -rf /var/cache/span/dev/*
fi

echo '=== Clear dev cache ==='
php bin/console cache:clear --env=dev

if [[ ${COMMENT} == *_clear_cache_* ]]; then
  echo '=== Adjust cache/dev directory permission ==='
  chmod -R 777 /var/cache/span/dev
fi

if [[ ${BRANCH} == "dev" ]]; then
  if [[ ${COMMENT} != *_no_test_* ]]; then
    if [[ ${COMMENT} == *_clear_cache_* ]]; then
      echo '=== Delete cache/test directory ==='
      rm -rf /var/cache/span/test/*
    fi

    echo '=== Clear test cache ==='
    php bin/console cache:clear --env=test

    if [[ ${COMMENT} == *_clear_cache_* ]]; then
      echo '=== Adjust cache/test directory permission ==='
      chmod -R 777 /var/cache/span/test
    fi

    echo '=== Run automated test ==='
    nohup php bin/console span:dev:run-test --revision=${REVISION} &> /dev/null &
  fi
fi

if [[ ${COMMENT} != *_no_scan_* ]]; then
  echo '=== Run static code scan ==='
  nohup php bin/console span:dev:scan-code --revision=${REVISION} &> /dev/null &
fi
