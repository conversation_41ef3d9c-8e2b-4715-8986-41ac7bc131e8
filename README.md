# Tern Commerce SPAN platform

## VirtualCards.us

- Production: https://www.virtualcards.us
- Staging: https://staging.virtualcards.us

## Backend technology stack

- Language: PHP
- Web Framework: Symfony
- Application Server: NGINX
- Database: MariaDB
- Cache: Redis
- Message Queue: RabbitMQ (not in use right now)
- Background Service: Workerman (not in use right now)

## Frontend technology stack

- Consumer Portal
  - jQuery
  - BootStrap
  - Parcel
- Admin Portal
  - Vue
  - Quasar Framework v0.17
  - Webpack

## Mobile technology stack

- Flutter

## Test frameworks

- Codeception
- Jest
- Puppeteer

## DevOps

- Git + Gitflow
- Continuous Integration: DeployBot

## API document

- API doc: https://www.virtualcards.us/developer
- Swagger doc: https://www.virtualcards.us/api-doc

## Admin portal

### Frontend

Source code path: `frontend/src`

Links to the tech stack:

- https://vuejs.org/v2/guide/
- https://v0-17.quasar-framework.org/
- http://sass-lang.com/

Start the dev server:

```bash
cd frontend
yarn dev
```

### Backend

Source code path: `span-web/src/AdminBundle`

Links to the tech stack:

- https://symfony.com

## Legacy Consumer Portal

Install dependencies, and build the frontend assets.

```bash
cd span-web
npm install
npm run build
```

## US Unlocked

Frontend source code: `span-web/UsUnlockedBundle/Resources/views/res/`

Start the watch & build service:

```bash
cd span-web
npm run usu
```

Frontend assets path: `span-web/web/static/usu`

Links to the tech stack:

- https://parceljs.org/

Backend: `span-web/UsUnlockedBundle/`

## Dev Bundle

Path: `span-web/DevBundle/`

This is only for developers. There are many scripts that are helpful to check something or run some commands.

Many of them are one-time script, while others are tools.

## Core Bundle

Path `span-web/CoreBundle/`

- Most core entities are saving here.
- Also the cron job scripts in `span-web/CoreBundle/Controller/Cron/`.
- Handy global helpers:
  - `span-web/CoreBundle/Utils/Util`
  - `span-web/CoreBundle/Utils/Data`: Redis cache
  - `span-web/CoreBundle/Utils/Excel`
  - `span-web/CoreBundle/Utils/Log`
  - `span-web/CoreBundle/Utils/Money`
  - `span-web/CoreBundle/Utils/Traits/DbTrait`
  - `span-web/CoreBundle/Utils/Traits/ExcelTrait`

# Running on Local

There are some nuances to running the application locally, namely the need to recompile from time-to-time if your environment settings prevent automated methods from working. In particular, there are times on Macintosh computers using Apple Silicon (e.g.: M1, M1X, etc.) that you will need to recompile `node-sass` or perform other operations to ensure that the application works as expected.

## Recompile Application

### Backend

cd /path/to/your/install/of/tern/span
cd span-web
COMPOSER_ALLOW_SUPERUSER=1 composer install --no-scripts --apcu-autoloader
COMPOSER_ALLOW_SUPERUSER=1 composer run-script build-bootstrap
COMPOSER_ALLOW_SUPERUSER=1 composer dump-autoload --optimize --no-dev --classmap-authoritative
php bin/fix_vendor
php bin/console cache:clear --env=prod --no-debug
`php bin/console d:s:u --dump-sql --force`(not recommended) or `php bin/console d:m:migrate --query-time` 

### Frontend

cd /path/to/your/install/of/tern/span
cd frontend
#npm install
npm run build
sleep 5
rm -rf ../span-web/web/mobile
mv dist ../span-web/web/mobile
cd ../span-web
php -d memory_limit=1200M bin/console cache:clear --env=dev

### Create SSH Tunnel for RDS Read Replica

ssh -N -L 33336:prod-read-replica-1.cpfvj8dv3t0h.us-east-1.rds.amazonaws.com:3306 root@************ -i ~/.ssh/tern-key

### Magic Users

For local development, note that the API_KEY value in the USERS table must be updated to prevent the "API Token Expired" warning from showing when running the app. The IDs for these users are 500016626 (Staging) and 500018861 (Prod)

```
UPDATE users SET api_token = '<JWT_VALUE>' WHERE id = 500016626;
```
