// Configuration for your app
let env = {
  proxy: {
    // target: 'http://usu.span.local',
    target: 'http://mex.span.local',
    // target: 'http://localhost:8070',
    // target: 'http://fis.span.local',
    // target: 'http://clf.span.local',
    // target: 'http://leaflink.span.local',
    changeOrigin: true,
    secure: false
  },
  suffix: ''
}
try {
  env = require('./env.js')
} catch (e) {
  console.log('\x1b[41m%s\x1b[0m', '---- Please create a env.js file with the environment variables. ---- ' + e.message)
}

const proxy = env.proxy

module.exports = function (ctx) {
  return {
    // app plugins (/src/plugins)
    plugins: [
      'i18n',
      'axios',
      'vuelidate',
      'filters',
      'echarts',
      'other'
    ],
    css: [
      'app.styl'
    ],
    extras: [
      ctx.theme.mat ? 'roboto-font' : null,
      'material-icons', // optional, you are not bound to it
      // 'ionicons',
      'mdi'
      // 'fontawesome'
    ],
    supportIE: true,
    build: {
      scopeHoisting: true,
      distDir: 'dist/' + ctx.themeName,
      // vueRouterMode: 'history',
      // publicPath: '/admin',
      // vueCompiler: true,
      // gzip: true,
      // analyze: true,
      // extractCSS: false,
      extendWebpack (cfg) {
        try {
          cfg.plugins[4].options.env_suffix = env.suffix
        } catch (e) {
          console.error(e.message)
        }

        cfg.module.rules.push({
          enforce: 'pre',
          test: /\.(js|vue)$/,
          loader: 'eslint-loader',
          exclude: /node_modules/
        })
      }
    },
    devServer: {
      // https: true,
      port: 8080,
      open: true, // opens browser window automatically
      proxy: {
        // proxy all requests starting with /api
        '/account-service': proxy,
        '/admin': proxy,
        '/api': proxy,
        '/attachments': proxy,
        '/card-programs': proxy,
        '/provinces': proxy,
        '/build': proxy,
        '/bundles': proxy,
        '/clf': proxy,
        '/consumercheck': proxy,
        '/developer': proxy,
        '/files': proxy,
        '/mobile': proxy,
        '/login': proxy,
        '/logout': proxy,
        '/detect-session': proxy,
        '/resume-session': proxy,
        '/node_modules': proxy,
        '/static': proxy,
        '/user': proxy,
        '/faas/widget': proxy,
        '/spendr/merchant': proxy,
        '/t/': proxy,
        '/_profiler': proxy,
        '/_wdt': proxy,
      },
      watchOptions: {
        poll: 2000,
        ignored: ["node_modules"]
      }
    },
    framework: 'all',  // --- includes everything; for dev only!
    animations: 'all', // --- includes all animations
    ssr: {
      pwa: false
    },
    pwa: {
      // workboxPluginMode: 'InjectManifest',
      // workboxOptions: {},
      manifest: {
        // name: 'Quasar App',
        // short_name: 'Quasar-PWA',
        // description: 'Best PWA App in town!',
        display: 'standalone',
        orientation: 'portrait',
        background_color: '#ffffff',
        theme_color: '#027be3',
        icons: [
          {
            'src': 'statics/icons/icon-128x128.png',
            'sizes': '128x128',
            'type': 'image/png'
          },
          {
            'src': 'statics/icons/icon-192x192.png',
            'sizes': '192x192',
            'type': 'image/png'
          },
          {
            'src': 'statics/icons/icon-256x256.png',
            'sizes': '256x256',
            'type': 'image/png'
          },
          {
            'src': 'statics/icons/icon-384x384.png',
            'sizes': '384x384',
            'type': 'image/png'
          },
          {
            'src': 'statics/icons/icon-512x512.png',
            'sizes': '512x512',
            'type': 'image/png'
          }
        ]
      }
    },
    cordova: {
      // id: 'org.cordova.quasar.app'
    },
    electron: {
      // bundler: 'builder', // or 'packager'
      extendWebpack (cfg) {
        // do something with Electron process Webpack cfg
      },
      packager: {
        // https://github.com/electron-userland/electron-packager/blob/master/docs/api.md#options

        // OS X / Mac App Store
        // appBundleId: '',
        // appCategoryType: '',
        // osxSign: '',
        // protocol: 'myapp://path',

        // Window only
        // win32metadata: { ... }
      },
      builder: {
        // https://www.electron.build/configuration/configuration

        // appId: 'quasar-app'
      }
    }
  }
}
