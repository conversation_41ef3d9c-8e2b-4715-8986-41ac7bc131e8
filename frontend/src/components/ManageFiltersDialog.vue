<template>
  <q-dialog class="filters-dialog admin-root"
            v-model="dialog"
            @cancel="dialog = false">
    <template slot="title">
      <span>Manage filters</span>
      <q-btn class="close"
             round
             flat
             @click="dialog = false"
             icon="close" />
    </template>
    <div slot="body">
      <div v-for="(filter, index) in filters"
           :key="index"
           class="filter-box">
        <div class="row">
          <div class="col-11">
            <div class="row gutter-md">
              <div class="col">
                <q-select v-model="filter.field"
                          :ref="`filterField_${index}`"
                          :readonly="!!filter.readonly"
                          filter
                          autofocus-filter
                          placeholder="Field"
                          @blur="selectedOption(filter)"
                          :options="sortedOptions" />
              </div>
              <div class="col">
                <q-select v-model="filter.predicate"
                          placeholder=""
                          :readonly="!!filter.readonly || !!(option(filter) && option(filter).predicate)"
                          :options="availablePredicates(filter)" />
              </div>
            </div>
          </div>
          <q-btn flat
                 round
                 dense
                 icon="mdi-delete-outline"
                 v-if="!filter.readonly"
                 class="ml-10 mt-5"
                 @click="remove(index)" />
        </div>
        <div class="row mt-10">
          <template v-if="option(filter) && option(filter).and">
            <div class="col-11">
              <div class="row gutter-md">
                <div class="col">
                  <q-select v-if="option(filter) && option(filter).options"
                            v-model="filter.value"
                            filter
                            :readonly="!!filter.readonly"
                            autofocus-filter
                            :options="optionOptions(filter)"></q-select>
                  <q-search v-else
                            filter
                            :readonly="!!filter.readonly"
                            v-model="filter.value"
                            placeholder="Search..." />
                </div>
                <div class="col">
                  <q-select v-if="option(option(filter).and).options"
                            v-model="filter.value_and"
                            filter
                            :readonly="!!filter.readonly"
                            autofocus-filter
                            :clearable="true"
                            :placeholder="option(option(filter).and).label"
                            :options="optionOptions(option(filter).and)"></q-select>
                  <q-search v-else
                            filter
                            :readonly="!!filter.readonly"
                            v-model="filter.value_and"
                            :clearable="true"
                            :placeholder="option(option(filter).and).label" />
                </div>
              </div>
            </div>
          </template>
          <q-select v-else-if="option(filter) && option(filter).options"
                    class="col-11"
                    v-model="filter.value"
                    filter
                    :readonly="!!filter.readonly"
                    autofocus-filter
                    :options="optionOptions(filter)"></q-select>
          <template v-else-if="option(filter) && option(filter).range">
            <div class="col-11">
              <div class="row gutter-md">
                <div class="col"
                     v-for="(r, i) in option(filter).range"
                     :class="{'range-mark': i !== 0}"
                     :key="i">
                  <q-datetime v-if="isRangeDate(r)"
                              filter
                              :type="r.type"
                              format24h
                              :readonly="!!filter.readonly"
                              :max="max"
                              v-model="filter['value_' + i]"></q-datetime>
                  <q-input v-else
                           filter
                           v-model="filter['value_' + i]"
                           :readonly="!!filter.readonly"
                           :prefix="r.type === 'amount' ? '$' : ''"
                           :type="rangeInputType(r)"></q-input>
                </div>
              </div>
            </div>
          </template>
          <template v-else-if="option(filter) && option(filter).type">
            <q-datetime filter
                        class="col-11"
                        v-if="isRangeDate(option(filter))"
                        :type="filter.type"
                        format24h
                        :readonly="!!filter.readonly"
                        v-model="filter.value"></q-datetime>
          </template>
          <q-search v-else
                    filter
                    class="col-11"
                    :readonly="!!filter.readonly"
                    v-model="filter.value"
                    placeholder="Search..." />
        </div>
      </div>
      <q-btn class="mt-10 add-filter"
             flat
             icon="add"
             @click="add">ADD FILTER</q-btn>
    </div>
    <template slot="buttons">
      <div class="flex-center text-right">
        <q-btn flat
               label="CLEAR ALL"
               class="clear-btn"
               icon="mdi-notification-clear-all"
               @click="clear" />
        <q-btn label="Apply Filters"
               size="xm"
               color="positive"
               @click="submit" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import _ from 'lodash'
import { predicates } from '../const'
import DialogMixin from '../mixins/DialogMixin'
import { isEmpty } from '../common'

export default {
  name: 'ManageFiltersDialog',
  mixins: [
    DialogMixin
  ],
  props: [
    'options',
    'filters',
    'name',
    'orFields'
  ],
  data () {
    return {
      max: new Date(),
      keyword: null,
      predicates
    }
  },
  watch: {
    visible () {
      if (this.visible && this.filters.length <= 0) {
        this.filters.push({
          predicate: '='
        })
      }
    },
    filters: {
      deep: true,
      handler () {
        const savedOptions = this.savedOptions()
        this.updateOptions(savedOptions || {})
      }
    }
  },
  computed: {
    sortedOptions () {
      return _.map(_.sortBy(this.options, 'label'), o => {
        if (this.isFieldMultiple(o.value)) {
          o.rightIcon = 'mdi-plus'
        }
        return o
      })
    },
    internalName () {
      return this.name || 'filters'
    },
    cpKey () {
      return this.$store.state.User.cpKey
    }
  },
  methods: {
    add (field = null) {
      this.filters.push({
        field,
        predicate: '='
      })

      this.$nextTick(() => {
        const $scroll = this.$el.querySelector('.modal-scroll')
        $scroll.scrollTop = $scroll.scrollHeight

        if (!field) {
          setTimeout(() => {
            const index = this.filters.length - 1
            const ref = `filterField_${index}`
            this.$refs[ref][0].show()
          }, 100)
        }
      })
    },
    remove (index) {
      this.filters.splice(index, 1)
    },
    savedOptions () {
      const key = `${location.href}___${this.$store.state.User.id}___${this.internalName}`
      const value = sessionStorage.getItem(key)
      if (value) {
        let options = JSON.parse(value)
        if (!options || !options.filters) {
          return null
        }
        if (_.isArray(options.filters)) {
          options.filters = _.filter(options.filters, f => f.field)
          if (options.filters.length <= 0) {
            return null
          }
          return options
        }
      }
      return null
    },
    updateOptions ({ page = 1 } = {}) {
      // Remove the duplicates
      const keys = []
      const news = []
      _.forEach(this.filters, (v, k) => {
        const key = [v.field, v.predicate, v.value, v.value_0, v.value_1, v.readonly].join('_')
        if (!keys.includes(key)) {
          keys.push(key)
          news.push(v)
        }
      })
      if (news.length !== this.filters.length) {
        return this.$nextTick(() => {
          this.$emit('update:filters', news)
        })
      }

      const key = `${location.href}___${this.$store.state.User.id}___${this.internalName}`
      const value = JSON.stringify({
        filters: this.filters,
        page: page
      })
      sessionStorage.setItem(key, value)
    },
    init (filters, reset = false) {
      let options = this.savedOptions()
      let fix = false
      if (!options) {
        fix = true
        options = {}
      } else if (!options.filters) {
        fix = true
      } else if (options.filters && options.filters.length === 1) {
        const f = options.filters[0]
        if (f && f.readonly) {
          fix = true
        } else if (f && !f.field) {
          fix = true
          options.filters = []
        }
      }
      if (!options.filters) {
        options.filters = []
      }
      if (filters) {
        options.filters = options.filters.concat(filters)

        if (reset) {
          options.filters = filters
        }
      }
      if (fix && this.cpKey && this.internalName === 'filters') {
        if (this.cpKey !== 'cp_fis' && location.href.endsWith('/a/user-management/user')) {
          options.filters.push({
            field: 'accountselect',
            predicate: '=',
            value: 9
          })
        } else if (this.cpKey === 'cp_usu') {
          const host = location.hostname
          let cpId = null
          if (host === 'localhost' || host === 'span.hans' || host === 'usu.span.hans' || host === 'span.local' || host === 'usu.span.local') {
            cpId = 54
          } else if (host === 'virtualcards.us' || host === 'usunlocked.virtualcards.us' || host === 'account.usunlocked.com') {
            cpId = 66
          }
          let field = null
          if ([
            '/a/card-management/card',
            '/a/transactions/load',
            '/a/transactions/load-queue',
            '/a/transactions/decline',
            '/a/report/negative-balance'
          ].includes(this.$route.path)) {
            field = 'filter[cp.id=]'
          } else if ([
            '/a/transactions/list',
            '/a/transactions/voiding'
          ].includes(this.$route.path)) {
            field = 'cardProgram'
          }
          if (cpId && field) {
            options.filters.push({
              field: field,
              predicate: '=',
              value: cpId
            })
          }
        }
        if (this.cpKey === 'cp_usu' && location.href.endsWith('/a/report/rain-report')) {
          options.filters.push({
            field: 'isMigrated',
            predicate: '=',
            value: 1
          })
        }
        if (this.cpKey === 'cp_usu' && location.href.includes('/a/report/load-method')) {
          options.filters.push({
            field: 'time',
            predicate: '=',
            // value: ['2025-07-01'],
            value_0: new Date('2025-07-01')
          })
        }
        if (this.cpKey === 'cp_usu' && location.href.endsWith('/a/report/privacy-card-creation')) {
          options.filters.push({
            field: 'cardProvider',
            predicate: '=',
            value: 'RAIN'
          })
        }
        // if (this.cpKey === 'cp_usu' && location.href.endsWith('/a/report/loads_per_country')) {
        //   options.filters.push({
        //     field: 'initializedAt',
        //     predicate: '=',
        //     // value: ['2025-07-01'],
        //     value_0: new Date('2025-07-01')
        //   })
        // }
        if (this.cpKey === 'cp_usu' && location.href.endsWith('/a/transactions/load')) {
          options.filters.push({
            field: 'loadDate',
            predicate: '=',
            value: ['2025-07-01'],
            value_0: new Date('2025-07-01')
          })
        }
        if (this.cpKey === 'cp_usu' && location.href.endsWith('/a/transactions/list')) {
          options.filters.push({
            field: 'transactionDateFrom',
            predicate: '=',
            value: new Date('2025-07-01'),
            value_0: new Date('2025-07-01')
          })
        }
      }

      if (options.filters) {
        this.$emit('update:filters', options.filters)
        this.$nextTick(() => {
          this.$emit('submit')
        })
      }
    },
    submit () {
      this.dialog = false
      this.$emit('submit')
    },
    getFilters () {
      const all = {}
      _.forEach(this.filters, (v, k) => {
        if (!v.field || (isEmpty(v.value) && isEmpty(v.value_0) && isEmpty(v.value_1))) {
          return
        }
        const values = {}
        if (!isEmpty(v.value)) {
          const option = this.option(v)
          if (option && option.and) {
            values[v.field] = JSON.stringify([
              v.value,
              v.value_and
            ])
          } else {
            values[v.field] = v.value
          }
        } else {
          const filter = _.find(this.options, { value: v.field })
          if (!isEmpty(v.value_0)) {
            values[filter.range[0].value] = v.value_0
          }
          if (!isEmpty(v.value_1)) {
            values[filter.range[1].value] = v.value_1
          }
        }

        const predicate = v.predicate
        _.forEach(values, (v, k) => {
          let key = k
          if (predicate === '!=') {
            key = `__not[${k}]`
          }
          if (!all[key]) {
            all[key] = []
          }
          all[key].push(v)
        })
      })
      const result = {}
      _.forEach(all, (v, k) => {
        if (v.length > 1) {
          if (this.isFieldMultiple(k)) {
            if (k.startsWith('filter[')) {
              k = k.substring(0, k.length - 1) + '`]'
              result[k] = JSON.stringify(v)
            } else {
              result[k + '`'] = JSON.stringify(v)
            }
          } else {
            result[k] = v[v.length - 1]
          }
        } else {
          result[k] = v[0]
        }
      })
      return result
    },
    isFieldMultiple (k) {
      const orFields = this.orFields || []
      if (k.startsWith('filter[')) {
        return true
      } else if (this.cpKey === 'cp_fis' && [
        'currency',
        'bank',
        'type',
        'client',
        'start',
        'end'
      ].includes(k)) {
        return true
      } else if (orFields.includes(k)) {
        return true
      }
      return false
    },
    clear () {
      this.$emit('update:filters', [{
        predicate: '='
      }])
    },
    option (filter) {
      return _.find(this.options, { value: _.isString(filter) ? filter : filter.field })
    },
    optionOptions (filter) {
      const option = this.option(filter)
      if (option && option.map) {
        let options = option.options
        _.forEach(option.map, (target, self) => {
          const filter = _.find(this.filters, { field: target })
          if (filter && filter.value) {
            const where = {}
            where[self] = filter.value
            options = _.filter(options, where)
          } else {
            return false
          }
        })
        return options
      }
      if (option.filter) {
        return option.filter(this.filters, option.options || [])
      }
      return option.options
    },
    isRangeDate (r) {
      return ['date', 'datetime', 'time'].indexOf(r.type) >= 0
    },
    rangeInputType (r) {
      if (['number', 'amount'].indexOf(r.type) >= 0) {
        return 'number'
      }
      return 'text'
    },
    selectedOption (filter) {
      const option = this.option(filter)
      if (option && option.dependency) {
        const selected = _.find(this.filters, { field: option.dependency.value })
        if (!selected) {
          const index = this.filters.indexOf(filter)
          this.filters.splice(index, 0, {
            field: option.dependency.value,
            predicate: '=',
            value: option.dependency.default
          })
        }
      }
    },
    availablePredicates (filter) {
      const option = this.option(filter)
      if (option && option.predicate) {
        return predicates.filter(p => {
          return p.value === option.predicate
        })
      }
      return predicates
    }
  }
}
</script>

<style lang="scss">
@import "../css/variable";

.filters-dialog {
  .modal-header {
    text-align: center;

    .close {
      position: absolute;
      right: 10px;
      top: 10px;
    }
  }

  .modal-content {
    width: 621px;

    .modal-scroll {
      max-height: calc(80vh - 150px);
    }

    .filter-box {
      margin-top: 20px;
      padding-top: 20px;
      border-top: 1px solid $line-light;

      &:first-of-type {
        margin-top: 5px;
        padding-top: 0;
        border-top: none;
      }

      .q-search {
        width: 100%;

        &.col-11 {
          width: 91.6667%;
        }
      }

      .range-mark {
        position: relative;

        &:before {
          content: "-";
          position: absolute;
          left: 11px;
          bottom: 2px;
          font-size: 30px;
          line-height: 32px;
          font-weight: 300;
        }
      }
    }

    .add-filter {
      color: var(--q-color-positive);

      .q-icon {
        color: var(--q-color-positive);
        margin-right: 5px;
      }
    }
  }

  .modal-buttons:not(.modal-buttons-top) {
    border-top: none;
    padding-bottom: 20px;

    .q-btn {
      padding: 8px 10px;
    }

    .clear-btn {
      color: $text;
    }
  }

  .modal-buttons:not(.modal-buttons-top).column .q-btn + .q-btn {
    border-top: none;
    margin-left: 10px;
  }
}
</style>
