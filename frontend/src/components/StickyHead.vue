<template>
  <q-tr>
    <th v-for="(col, i) in props.cols"
        :style="columnStyles[i] || {}"
        :class="columnClasses[i] || {freeze: i <= freezeColumn, 'last-freeze': i === freezeColumn}"
        :key="col.field"
        :align="col.align">
      <slot :col="col">
        <q-checkbox v-model="selectAll" class="mh--10"
                    v-if="col.label === 'multi_check'">
          <q-tooltip>Toggle all on this page</q-tooltip>
        </q-checkbox>
        <span v-else>
          {{ labelReplaceData ? labelReplace(col.label) : col.label }}
          <template v-if="col.tooltip && masterAdmin">
            <q-icon name="mdi-help-circle-outline" class="ml-2 help"></q-icon>
            <q-tooltip v-html="col.tooltip"></q-tooltip>
          </template>
        </span>

        <i v-if="col.sortLabel && col.sortLabel === sortField && type === 'asc'"
           class="mdi mdi-arrow-up"></i>
        <i v-if="col.sortLabel && col.sortLabel === sortField && type === 'desc'"
           class="mdi mdi-arrow-down"></i>
      </slot>

      <q-btn-dropdown flat
                      round
                      v-if="!readonly || (sort && col.sortLabel)"
                      size="sm"
                      class="col-option"
                      icon="mdi-menu-down">
        <q-list link>
          <q-item v-close-overlay
                  v-if="!readonly"
                  @click.native="$emit('update:freezeColumn', i)">
            <q-item-side icon="mdi-format-columns"></q-item-side>
            <q-item-main>
              <q-item-tile label>Freeze this column</q-item-tile>
            </q-item-main>
          </q-item>
          <q-item-separator v-if="false"></q-item-separator>
          <q-item v-close-overlay
                  @click.native="sortReload(col, 'asc')"
                  v-if="sort">
            <q-item-side icon="mdi-sort-ascending"></q-item-side>
            <q-item-main>
              <q-item-tile label>Sort in ascending order</q-item-tile>
            </q-item-main>
          </q-item>
          <q-item v-close-overlay
                  @click.native="sortReload(col, 'desc')"
                  v-if="sort">
            <q-item-side icon="mdi-sort-descending"></q-item-side>
            <q-item-main>
              <q-item-tile label>Sort in descending order</q-item-tile>
            </q-item-main>
          </q-item>
        </q-list>
      </q-btn-dropdown>
    </th>
  </q-tr>
</template>

<script>
export default {
  name: 'StickyHead',
  props: {
    props: {
      type: Object,
      required: true
    },
    freezeColumn: {
      type: Number,
      default: 0
    },
    columnStyles: {
      type: Array,
      default () {
        return []
      }
    },
    columnClasses: {
      type: Array,
      default () {
        return []
      }
    },
    readonly: {
      type: Boolean,
      default: false
    },
    sort: {
      type: Boolean,
      default: false
    },
    labelReplaceData: {
      type: Object,
      default () {
        return {}
      }
    }
  },
  data () {
    return {
      type: 'asc',
      sortField: null,
      selectAll: false
    }
  },
  computed: {
    masterAdmin () {
      const user = this.$store.state.User
      return user && user.currentRole === 'MasterAdmin'
    }
  },
  watch: {
    selectAll () {
      this.$emit('selectAll', this.selectAll)
    }
  },
  methods: {
    sortReload (row, type) {
      this.type = type
      this.sortField = row.sortLabel
      this.$nextTick(() => {
        setTimeout(() => {
          this.$emit('change')
        }, 300)
      })
    },
    params () {
      const data = {}
      data.sortBy = this.sortField
      data.sortType = this.type
      return data
    },
    labelReplace (label) {
      let newLabel = label
      for (const key in this.labelReplaceData) {
        if (key === label && this.labelReplaceData.hasOwnProperty(key)) {
          newLabel = this.labelReplaceData[key]
        }
      }
      return newLabel
    }
  }
}
</script>
