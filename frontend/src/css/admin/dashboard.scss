.graph__dashboard__index_page {
  .page-content > .row {
    max-width: 1405px;
  }

  .draggable-container {
    .q-card-title, .sortable-chosen, .sortable-ghost {
      cursor: move;
    }
  }
}

@media (max-width: 1250px) {
  .graph__dashboard__index_page {
    .page-content > .row {
      max-width: 932px;

      > .col-4, > .col-8 {
        .common-dashboard-card {
          height: calc(100vw / 2 - 120px);
        }
      }

      > .col-4 {
        max-width: 50%;
        flex-basis: 50%;
      }

      > .col-8 {
        max-width: 100%;
        flex-basis: 100%;
      }
    }
  }
}

@media (max-width: 699px) {
  .graph__dashboard__index_page {
    .page-content > .row {
      > .col-4, > .col-6, .col-8 {
        max-width: 100%;
        flex-basis: 100%;

        .common-dashboard-card {
          height: calc(100vw - 120px);
        }
      }
    }
  }
}

@media (max-width: 413px) {
  .graph__dashboard__index_page {
    .page-content > .row {
      > .col-4, > .col-6, > .col-8 {
        .common-dashboard-card {
          min-height: 300px;
        }
      }
    }
  }
}

.q-card.common-dashboard-card {
  border-radius: 10px;
  background: white;
  height: calc(100vw / 3 - 120px);
  position: relative;
  max-width: 450px;
  max-height: 380px;
  min-height: 270px;

  .q-card-container {
    padding: 14px;

    h4 {
      font-size: 16px;
      font-weight: normal;
      line-height: 1.5em;
      margin: 0 auto 6px 0;
      color: #333;
    }

    .chart-container {
      height: calc(100% - 10px);
      margin-top: 10px;

      > div:nth-of-type(2), > div:not(:first-of-type) {
        z-index: 3000 !important;
      }
    }
  }

  .q-card-title {
    display: flex;
    justify-content: space-between;
    font-size: 16px;
    color: #333;

    a {
      font-size: 12px;
      text-decoration: none;
      color: #2C5FAD !important;

      &:hover {
        color: #1e3b75 !important;
        text-decoration: underline;
      }
    }
  }

  .row.summary {
    .col-8 {
      font-size: 24px;
      font-weight: 600;
      color: green;
    }

    .col-4 {
      font-size: 18px;
      font-weight: 600;
      white-space: nowrap;
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
  }

  .q-card-primary.q-card-container {
    padding: 10px 14px 5px;
  }

  .q-card-main.q-card-container {
    padding-top: 0;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: calc(100% - 43px);
    overflow: visible;
  }

  .btn-reload {
    position: absolute;
    right: 0;
    bottom: 0;
    padding: 4px !important;
    opacity: 0.5;
  }
}

.chart-tooltip-title {
  text-align: center;
  font-weight: 500;
  margin-bottom: 6px;
  color: #666;
}

.chart-tooltip-table {
  th {
    color: #333;
    font-size: 12px;
  }

  th, td {
    padding: 2px 7px;
  }

  tr.active td {
    font-weight: 500;
    color: black;
  }

  tr.total td {
    border-top: 1px solid #ddd;
  }

  &.chart-tooltip-table-dense {
    th, td {
      padding: 0 5px;
    }
  }
}

.chart-tooltip-box {
  display: inline-block;
  width: 15px;
  height: 15px;
  background: silver;
  margin: auto 4px 2px 3px;
  vertical-align: middle;
}

.chart-tooltip-circle {
  display: inline-block;
  width: 12px;
  height: 12px;
  background: silver;
  margin: auto 3px 2px;
  vertical-align: middle;
  border-radius: 50%;
  box-shadow: 0 2px 5px #999;
}

.chart-container {
  .field-row {
    display: flex;
    font-size: 12px;

    .field-label {
      width: 60px;
      color: gray;
      margin-right: 4px;
      text-align: right;
    }
  }
}

@media (max-width: 992px) {
  .common-dashboard-card {
    height: calc(100vw / 3 - 50px);
  }
}
