<template>
  <q-page id="system__platform_branding_page">
    <div class="row">
      <div class="col">
        <div class="page-header column">
          <div class="title mb-10">{{ title }}</div>
          <div class="subtitle-line font-14">Choose a platform to configure</div>
        </div>
      </div>
      <div class="col text-right">
        <q-btn-dropdown size="sm"
                        class="mt-15"
                        color="grey-3"
                        text-color="black"
                        label="Actions">
          <q-list link>
            <q-item v-close-overlay @click.native="invalidateCache()">
              <q-item-main>Invalidate API doc cache</q-item-main>
            </q-item>
          </q-list>
        </q-btn-dropdown>
      </div>
    </div>
    <div class="page-content">
      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               :rows-per-page-options="[5, 10, 20, 50]"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-right">
          <q-btn flat round dense
                 class="q-mr-md"
                 @click="reload"
                 icon="refresh"/>
          <ColumnFilter :columns="columns"></ColumnFilter>
        </template>

        <q-tr slot="body"
              slot-scope="props" :class="`status_${props.row.status}`">
          <q-td v-for="col in props.cols" :key="col.field">
            <template v-if="col.field === 'subDomain'">
              <a :href="props.row.link" target="_blank">{{ props.row.link }}</a>
            </template>
            <template v-else-if="col.field === 'cardPrograms'">
              <div v-if="col.value && col.value.length">
                {{ col.value[0] }}

                <template v-if="col.value.length > 1">
                  ...
                  <q-tooltip>
                    <div class="mb-5" v-for="v in col.value" :key="v">{{ v }}</div>
                  </q-tooltip>
                </template>
              </div>
            </template>
            <template v-else-if="!col.field">
              <q-btn-dropdown size="xs"
                              color="grey-3"
                              text-color="black"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          :to="`/a/system/platform-branding/${props.row.id}`">
                    <q-item-main>Customize Appearance</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          :to="`/a/system/platform-parameter/${props.row.id}`">
                    <q-item-main>Customize Parameters</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          :to="`/a/system/platform-email/${props.row.id}`">
                    <q-item-main>Email Template Values</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          :to="`/a/system/api-documentation/${props.row.id}`">
                    <q-item-main>API Documentation</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          :to="`/a/i/admin__card-program__chart-settings?platform_id=${props.row.id}`">
                    <q-item-main>Chart Setting</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              {{ col.value }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination" slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { request, notifySuccess } from '../../../common'

export default {
  name: 'PlatformBrandingIndex',
  mixins: [
    ListPageMixin
  ],
  data () {
    return {
      title: 'Platform Branding',
      requestUrl: '/admin/system/platform-branding',
      autoLoad: true,
      columns: [
        {
          field: 'id',
          label: 'ID',
          align: 'left'
        }, {
          field: 'name',
          label: 'Name',
          align: 'left'
        }, {
          field: 'subDomain',
          label: 'Sub Domain',
          align: 'left'
        }, {
          field: 'cardPrograms',
          label: 'Card Programs',
          align: 'left'
        }, {
          field: '',
          label: 'Action',
          align: 'left'
        }
      ]
    }
  },
  methods: {
    async invalidateCache () {
      this.$q.loading.show()
      const resp = await request(`/admin/system/platform-branding/invalidate/cache`, 'post')
      this.$q.loading.hide()
      if (resp) {
        notifySuccess(resp)
      }
    }
  }
}
</script>
