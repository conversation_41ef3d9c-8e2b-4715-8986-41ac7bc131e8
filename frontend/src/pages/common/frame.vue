<template>
  <q-page id="frame-page">
    <iframe :src="url" frameborder="0"></iframe>

    <q-inner-loading :visible="loading" @click.native="loading = false">
      <q-spinner size="50px" color="primary"></q-spinner>
    </q-inner-loading>
  </q-page>
</template>

<script>
import qs from 'qs'

let app = null

export default {
  data () {
    return {
      loading: true
    }
  },
  props: [
    'src'
  ],
  computed: {
    url () {
      if (this.src) {
        return this.src
      }
      if (!this.$route.params.url) {
        return ''
      }
      let url = this.$route.params.url.replace(/__/g, '/')
      if (!url.startsWith('/')) {
        url = `/${url}`
      }
      return `${url}?${qs.stringify(this.$route.query)}`
    }
  },
  watch: {
    url () {
      this.loading = true
    }
  },
  mounted () {
    app = this

    this.$root.$on('frame-navigate-reload', () => {
      this.$el.querySelector('iframe').src = this.url
    })
  },
  beforeDestroy () {
    this.$root.$off('frame-navigate-reload')
  }
}

window.embedInMd = true

if (!window.installedFrameMessageHandler) {
  window.addEventListener('message', function (evt) {
    const arg = evt.data
    if (!arg) {
      return
    }
    if (arg === 'onMdFrameLoaded') {
      app.loading = false
    } else if (arg === 'onMdFrameDirecting') {
      app.loading = true
    } else if (arg === 'onMdFrameBodyClicked') {
      document.querySelector('body').click()
      app.$root.$emit('admin-hide-all-nav')
    } else if (arg.action === 'route') {
      app.$router.push(arg.data)
    }
  })
  window.installedFrameMessageHandler = true
}
</script>

<style lang="scss">
  #frame-page {
    background: #F5F5F5;
    flex-direction: row !important;
    padding: 0 !important;

    iframe {
      background: #F5F5F5;
      width: 100%;
    }
  }
</style>
