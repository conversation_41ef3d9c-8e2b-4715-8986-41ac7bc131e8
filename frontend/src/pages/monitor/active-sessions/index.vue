<template>
  <q-page id="monitor__active_sessions_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
    </div>
    <div class="page-content">
      <div class="quick-result">
        <div>
          <h5>{{ quick.count | number }}</h5>
          <div class="description">Total # of sessions</div>
        </div>
      </div>

      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               :rows-per-page-options="[5, 10, 20, 50]"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <q-btn flat round dense
                 icon="mdi-filter-variant"
                 @click="filtersDialog = true"/>
          <FilterChips :filters.sync="filters"
                       :options="filterOptions"
                       v-show="!loadingFilters"
                       @reload="reload"></FilterChips>
        </template>
        <template slot="top-right">
          <q-btn flat round dense
                 class="q-mr-md"
                 @click="reload"
                 icon="refresh"/>
          <ColumnFilter :columns="columns"></ColumnFilter>
        </template>

        <q-tr slot="body"
              slot-scope="props" :class="`status_${props.row.status}`">
          <q-td v-for="col in props.cols" :key="col.field">
            <template v-if="col.field === 'id' && p('user')">
              <a :href="`/admin#/a/i/admin__user_modify__modify?user_id=${col.value}`"
                 target="_blank">{{ col.value }}</a>
            </template>
            <template v-else-if="col.field === 'cardProgram' || col.field === 'teams'">
              <div v-if="col.value && col.value.length">
                {{ col.value[0] }}

                <template v-if="col.value.length > 1">
                  ...
                  <q-tooltip>
                    <div class="mb-5" v-for="v in col.value" :key="v">{{ v }}</div>
                  </q-tooltip>
                </template>
              </div>
            </template>
            <template v-else-if="col.field === 'activatedAt'">
              {{ col.value | date('L LT') }}
            </template>
            <template v-else-if="col.field === 'expireIn'">
              <div class="text-right">{{ expireIn(props.row.activatedAt) }}</div>
            </template>
            <template v-else-if="col.field === 'sessionStatus'">
              <q-chip class="font-12" :class="statusClass(props.row['sessionStatus'].toLowerCase())">
                {{ props.row['sessionStatus'] }}
              </q-chip>
            </template>
            <template v-else-if="['accountBalance', 'legacyBalance'].includes(col.field)">
              <span v-if="col.value">{{ col.value | moneyFormat }}</span>
            </template>
            <template v-else>
              {{ col.value }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination" slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>
  </q-page>
</template>

<script>
import { request } from '../../../common'
import ListPageMixin from '../../../mixins/ListPageMixin'
import moment from 'moment'

export default {
  mixins: [
    ListPageMixin
  ],
  data () {
    let columns = [
      {
        field: 'id',
        label: 'User ID',
        align: 'left'
      }, {
        field: 'name',
        label: 'User Name',
        align: 'left'
      }, {
        field: 'email',
        label: 'Email',
        align: 'left'
      }, {
        field: 'activatedAt',
        label: 'Active since',
        align: 'left'
      }, {
        field: 'expireIn',
        label: 'Expire in',
        align: 'left'
      }, {
        field: 'cardProgram',
        label: 'Card Program',
        align: 'left'
      }, {
        field: 'country',
        label: 'Country',
        align: 'left'
      }, {
        field: 'teams',
        label: 'Roles',
        align: 'left'
      }, {
        field: 'legacyBalance',
        label: 'Legacy Balance',
        align: 'right'
      }, {
        field: 'accountBalance',
        label: 'Account Balance',
        align: 'right'
      }
    ]
    if (this.isFis()) {
      columns = columns.filter(item => ['country', 'accountBalance'].indexOf(item.field) === -1)
      columns.splice(5, 0, {
        field: 'sessionStatus',
        label: 'Session Status',
        align: 'left'
      })
    }
    return {
      title: 'Active Sessions',
      filtersUrl: '/admin/monitor/active-sessions-filters',
      autoLoad: true,
      columns: columns,
      quick: {
        count: 0
      },
      filterOptions: [
        {
          value: 'filter[p.id=]',
          label: 'Processor',
          options: [],
          source: 'processor'
        }, {
          value: 'filter[pm.id=]',
          label: 'Program Manager',
          options: [],
          source: 'programmanager'
        }, {
          value: 'filter[country.id=]',
          label: 'Country',
          search: true,
          options: [],
          source: 'countrys'
        }, {
          value: 'filter[t.id=]',
          label: 'Role',
          options: [],
          source: 'roles'
        }, {
          value: 'filter[cp.id=]',
          label: 'Card Program',
          options: [],
          source: 'cardprograms'
        }, {
          value: 'sessionStatus',
          label: 'Session status',
          predicate: '=',
          options: [
            {
              label: 'Active',
              value: null
            },
            {
              label: 'Inactive',
              value: 'timedOut'
            },
            {
              label: 'All',
              value: 'all'
            }
          ]
        }
      ]
    }
  },
  watch: {
    '$route.fullPath' () {
      this.init()
      this.initFilters()
    }
  },
  methods: {
    async request ({ pagination }) {
      this.beforeRequest({ pagination })

      this.loading = true
      const data = this.getQueryParams()
      const resp = await request(`/admin/monitor/active-sessions/${pagination.page}/${pagination.rowsPerPage}`, 'form', data)
      this.loading = false
      if (resp.success) {
        this.pagination = pagination
        this.data = resp.data.data
        this.pagination.rowsNumber = resp.data.total
        this.quick = resp.data.quick
      }
    },
    postInitFilters () {
      if (this.queue) {
        this.reload()
      }
    },
    init () {
      this.data = []
      this.quick = {}
      this.filters = []
      this.keyword = ''
    },
    expireIn (time) {
      return moment.unix(time).add(20, 'minutes').fromNow()
    },
    statusClass (status) {
      return {
        'active': 'positive',
        'inactive': 'pansy'
      }[status] || status
    },
    isFis () {
      return this.$store.state.User && this.$store.state.User.dashboard === 'fis'
    }
  }
}
</script>

<style lang="scss">
  @import '../../../css/variable';

  #monitor__active_sessions_page {
  }
</style>
