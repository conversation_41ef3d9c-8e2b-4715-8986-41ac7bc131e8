<template>
  <q-page id="load_transactions__refund_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-btn class="mr-10"
               color="black"
               @click="download"
               label="Export"></q-btn>
      </div>
    </div>
    <div class="page-content">
      <div class="quick-result">
        <div>
          <h5>{{ quick.count | number }}</h5>
          <div class="description">Refund Count</div>
        </div>
        <div>
          <h5>{{ quick.amount | moneyFormat('USD', true) }}</h5>
          <div class="description">Refund $</div>
        </div>
        <div>
          <h5>{{ quick.fee | moneyFormat('USD', true) }}</h5>
          <div class="description">Refund Fees</div>
        </div>
        <div>
          <h5>{{ quick.avgAmount | moneyFormat('USD', true) }}</h5>
          <div class="description">Avg. Refund $</div>
        </div>
      </div>

      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               :rows-per-page-options="[5, 10, 20, 50]"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <q-btn flat round dense
                 icon="mdi-filter-variant"
                 @click="filtersDialog = true"/>
          <FilterChips :filters.sync="filters"
                       :options="filterOptions"
                       v-show="!loadingFilters"
                       @reload="reload"></FilterChips>
        </template>
        <template slot="top-right">
          <q-btn flat round dense
                 class="q-mr-md"
                 @click="reload"
                 icon="refresh"/>
          <ColumnFilter :columns="columns"></ColumnFilter>
        </template>

        <q-tr slot="body"
              slot-scope="props" :class="`status_${props.row.status}`">
          <q-td v-for="col in props.cols" :key="col.field">
            <template v-if="col.field === 'Action'">
              <q-btn-dropdown size="xs" :label="col.label" v-if="['Pending', 'Failed'].includes(props.row['Refund Status'])">
                <q-list link>
                  <q-item v-close-overlay @click.native="execute(props.row)">
                    <q-item-main>Refund Now</q-item-main>
                  </q-item>
                  <q-item v-close-overlay @click.native="cancel(props.row)">
                    <q-item-main>Cancel</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else-if="col.field === 'userId' && p('user')">
              <a :href="`/admin#/a/i/admin__user_modify__modify?user_id=${col.value}`"
                 target="_blank">{{ col.value }}</a>
            </template>
            <template v-else-if="['Create Date', 'Refund At'].includes(col.field)">
              {{ col.value | date('L LT') }}
            </template>
            <template v-else-if="['Account Balance', 'Paid Amount', 'Refund Amount', 'Refund Fee'].includes(col.field)">
              {{ col.value | moneyFormat }}
            </template>
            <template v-else>
              {{ col.value }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination" slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>
import { generateColumns, request } from '../../../common'
import ListPageMixin from '../../../mixins/ListPageMixin'

export default {
  mixins: [
    ListPageMixin
  ],
  data () {
    return {
      title: 'Refund List / Queue',
      filtersUrl: '/admin/refund/filters',
      requestUrl: '/admin/refund/search',
      downloadUrl: '/admin/refund/export',
      columns: generateColumns([
        'Create Date', 'Refund ID', 'Customer ID', 'First Name', 'Last Name', 'Account Balance', 'Email', 'Country',
        'Transaction NO', 'Load Method', 'Paid Amount',
        'Refund Amount', 'Refund Fee', 'Refund Status', 'Refund At', 'Error', 'Action'
      ], [
        'Account Balance', 'Paid Amount', 'Refund Amount', 'Refund Fee'
      ]),
      quick: {
        count: 0,
        amount: 0,
        fee: 0,
        avgAmount: 0
      },
      filterOptions: [
        {
          value: 'filter[u.id=]',
          label: 'User ID'
        }, {
          value: 'filter[u.email=]',
          label: 'User Email'
        }, {
          value: 'filter[ucl.transactionNo=]',
          label: 'Transaction NO.'
        }, {
          value: 'filter[ucl.paymentReference=]',
          label: 'Payment Reference'
        }, {
          value: 'filter[ucl.paymentId=]',
          label: 'Payment ID'
        }, {
          value: 'filter[cp.id=]',
          label: 'Card program',
          options: [],
          source: 'cardPrograms'
        }, {
          value: 'filter[country.id=]',
          label: 'Country',
          options: [],
          search: true,
          source: 'countries'
        }, {
          value: 'filter[ucl.partner=]',
          label: 'Load partner',
          options: [],
          source: 'loadPartners'
        }, {
          value: 'filter[ucl.method=]',
          label: 'Load method',
          options: [],
          source: 'loadMethods'
        }, {
          value: 'createData',
          label: 'Date',
          range: [
            {
              value: 'range[ucr.initializedAt][start]',
              type: 'date'
            }, {
              value: 'range[ucl.initializedAt][end]',
              type: 'date'
            }
          ]
        }, {
          value: 'filter[ucr.status=]',
          label: 'Refund status',
          options: [
            {
              value: '',
              label: 'All'
            }, {
              value: 'pending',
              label: 'Pending'
            }, {
              value: 'success',
              label: 'Success'
            }, {
              value: 'failed',
              label: 'Failed'
            }, {
              value: 'cancelled',
              label: 'Cancelled'
            }
          ]
        }
      ]
    }
  },
  methods: {
    init () {
      this.data = []
      this.quick = {}
      this.filters = []
      this.keyword = ''
    },
    cancel (item) {
      this.$q.dialog({
        title: 'Confirm',
        message: `Are you sure that you want to cancel and remove it from the queue?`,
        color: 'negative',
        cancel: true
      }).then(async () => {
        const resp = await request(`/admin/refund/cancel/${item['Refund ID']}`, 'post')
        if (resp.success) {
          this.reload()
        }
      }).catch(() => {})
    },
    async execute (item) {
      this.$q.dialog({
        title: 'Confirm',
        message: 'Are you sure that you want to refund it now?',
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        await request(`/admin/refund/execute/${item['Refund ID']}`, 'post', {}, true)
        this.$q.loading.hide()
        this.$q.notify({
          type: 'positive',
          message: 'Submitted the request. Please refresh the list later to see the result.'
        })
      }).catch(() => {})
    }
  }
}
</script>
