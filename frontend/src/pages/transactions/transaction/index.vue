<template>
  <q-page id="transactions__list_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-field class="input-button-group inline-flex mr-20">
          <div class="row no-wrap">
            <q-input v-model="keyword"
                     clearable
                     class="w-250"
                     @clear="reload"
                     placeholder="ID, User ID..."></q-input>
            <q-btn label="Search"
                   color="grey-4"
                   class="no-shadow"
                   @click="reload"
                   text-color="black"></q-btn>
          </div>
        </q-field>

        <q-btn class="mr-10"
               color="black"
               @click="download"
               label="Export"></q-btn>
      </div>
    </div>
    <div class="page-content">
      <div class="quick-result">
        <div>
          <h5>{{ quick.totalSize | number }}</h5>
          <div class="description">Total # of txns</div>
        </div>
        <div>
          <h5>{{ quick.totalAmountUSD | moneyFormat('USD', true) }}</h5>
          <div class="description">Total Transaction Amount</div>
        </div>
        <div>
          <h5>{{ quick.totalSize ? (quick.totalAmountUSD / quick.totalSize) : 0 | moneyFormat('USD', true) }}</h5>
          <div class="description">Average Transaction Amount</div>
        </div>
      </div>

      <TopChart :chart-id="chartId"
                v-if="chartData">
      </TopChart>
      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               :rows-per-page-options="[5, 10, 20, 50]"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <q-btn flat
                 round
                 dense
                 icon="mdi-filter-variant"
                 @click="filtersDialog = true" />
          <FilterChips :filters.sync="filters"
                       :options="filterOptions"
                       v-show="!loadingFilters"
                       @reload="reload"></FilterChips>
        </template>
        <template slot="top-right">
          <q-btn flat
                 round
                 dense
                 class="q-mr-md"
                 @click="reload"
                 icon="refresh" />
          <ColumnFilter :columns="columns"></ColumnFilter>
        </template>

        <q-tr slot="body"
              slot-scope="props"
              :class="`status_${props.row.status}`">
          <q-td v-for="col in props.cols"
                :key="col.field">
            <template v-if="!col.field">
              <q-btn-dropdown size="xs"
                              color="grey-3"
                              text-color="black"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          v-if="isPrivacyTxn(props.row)"
                          @click.native="viewDetails(props.row)">
                    <q-item-main>View Details</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="canChangeToLoad(props.row)"
                          @click.native="changeToLoad(props.row)">
                    <q-item-main>Load to the account</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="isPrivacyTxn(props.row)"
                          @click.native="submitDisputeRequest(props.row)">
                    <q-item-main>Submit dispute request</q-item-main>
                  </q-item>
                  <q-item v-if="isPrivacyTxn(props.row) && masterAdmin">
                    <q-item-main label="Change Dispute Status"></q-item-main>
                    <q-item-side right>
                      <q-item-tile icon="mdi-menu-right"></q-item-tile>
                    </q-item-side>
                    <q-popover anchor="bottom left"
                               self="top right"
                               :offset="[0, -40]">
                      <q-list link>
                        <template v-for="(s, si) in disputeStatuses">
                          <q-item v-close-overlay
                                  v-if="s !== props.row['Dispute Status']"
                                  :key="'ds_action_' + si"
                                  @click.native="changeDisputeStatus(props.row, s)">
                            <q-item-main>{{ s }}</q-item-main>
                          </q-item>
                        </template>
                      </q-list>
                    </q-popover>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="props.row.status && (props.row.status.startsWith('VOIDING') || props.row.status.startsWith('RETURNING'))"
                          @click.native="manualRelease(props.row)">
                    <q-item-main>Manual Release</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="props.row.sandbox"
                          @click.native="simulate(props.row, 'void')">
                    <q-item-main>Simulate: Void</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="props.row.sandbox"
                          @click.native="simulate(props.row, 'void', {status: 'AUTHORIZATION_EXPIRY'})">
                    <q-item-main>Simulate: Void (Expiry)</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="props.row.sandbox"
                          @click.native="simulate(props.row, 'void_s')">
                    <q-item-main>Simulate: Void * 0.8</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="props.row.sandbox"
                          @click.native="simulate(props.row, 'clearing')">
                    <q-item-main>Simulate: Clearing</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="props.row.sandbox"
                          @click.native="simulate(props.row, 'clearing_a')">
                    <q-item-main>Simulate: Clearing * 1.2</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="props.row.sandbox"
                          @click.native="simulate(props.row, 'clearing_s')">
                    <q-item-main>Simulate: Clearing * 0.8</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="props.row.sandbox"
                          @click.native="simulate(props.row, 'return_reversal')">
                    <q-item-main>Simulate: Return Reversal</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else-if="col.field === 'userId' && p('user')">
              <a :href="`/admin#/a/i/admin__user_modify__modify?user_id=${col.value}`"
                 target="_blank">{{ col.value }}</a>
            </template>
            <template v-else>
              {{ col.value }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         :or-fields="['status']"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>

import { request, notifySuccess } from '../../../common'
import ListPageMixin from '../../../mixins/ListPageMixin'
import { chartName } from '../../../const'
import eventBus from '../../../eventBus'
import _ from 'lodash'

export default {
  mixins: [
    ListPageMixin
  ],
  data () {
    return {
      filtersUrl: '/admin/card_transaction/filters',
      downloadUrl: '/admin/card_transaction_export',
      title: 'Card Transaction List',
      timeField: 'transactionDateFrom',
      cardProgramField: 'cardProgram',
      chartId: chartName.USAGE_CHART,
      baseColumns: [{
        field: 'tranId',
        label: 'Transaction Id',
        align: 'left',
        hidden: true
      }, {
        field: 'txnTime',
        label: 'Txn Time',
        align: 'left'
      }, {
        field: 'postTime',
        label: 'Post Time',
        align: 'left',
        hidden: true
      }, {
        field: 'userId',
        label: 'User ID',
        align: 'left'
      }, {
        field: 'fullName',
        label: 'User name',
        align: 'left',
        hidden: true
      }, {
        field: 'email',
        label: 'Email',
        align: 'left'
      }, {
        field: 'accountNumber',
        label: 'Account Number',
        align: 'left'
      }, {
        field: 'txnAmount',
        label: 'Amount',
        align: 'left'
      }, {
        field: 'status',
        label: 'Status',
        align: 'left'
      }, {
        field: 'billingAddressType',
        label: 'Billing Address Type',
        align: 'left',
        hidden: true
      }, {
        field: 'defaultAddress',
        label: 'Addr1',
        align: 'left',
        hidden: true
      }, {
        field: 'secondAddress',
        label: 'Addr2',
        align: 'left',
        hidden: true
      }, {
        field: 'city',
        label: 'City',
        align: 'left',
        hidden: true
      }, {
        field: 'addressState',
        label: 'State',
        align: 'left',
        hidden: true
      }, {
        field: 'countryRegion',
        label: 'Region',
        align: 'left',
        hidden: true
      }, {
        field: 'addressCountry',
        label: 'Country',
        align: 'left',
        hidden: true
      }, {
        field: 'merchant',
        label: 'MID',
        align: 'left',
        hidden: true
      }, {
        field: 'desc',
        label: 'Transaction Type',
        align: 'left'
      }, {
        field: 'merchantType',
        label: 'Merchant type',
        align: 'left',
        hidden: true
      }, {
        field: 'merchantNameAndLocation',
        label: 'MerchantCustomName',
        align: 'left',
        hidden: true
      }, {
        field: 'mcc',
        label: 'MCC',
        align: 'left',
        hidden: true
      }, {
        field: 'affId',
        label: 'Affiliate',
        align: 'left',
        hidden: true
      }, {
        field: 'cardProgramName',
        label: 'Program',
        align: 'left',
        hidden: true
      }, {
        field: 'brandPartnerName',
        label: 'Brand partner',
        align: 'left',
        hidden: true
      }, {
        field: 'issuingBankName',
        label: 'Issuing bank',
        align: 'left',
        hidden: true
      }, {
        field: 'marketingPartnerName',
        label: 'Marketing partner',
        align: 'left',
        hidden: true
      }, {
        field: 'programManagerName',
        label: 'Program manager',
        align: 'left',
        hidden: true
      }, {
        field: 'processorName',
        label: 'Processor',
        align: 'left',
        hidden: true
      }, {
        field: 'cardTypeName',
        label: 'Card Type',
        align: 'left',
        hidden: true
      }, {
        field: 'privacyCardType',
        label: 'Privacy Card Type',
        align: 'left',
        hidden: true
      }, {
        field: 'cardCustName',
        label: 'Card custom name',
        align: 'left',
        hidden: true
      }, {
        field: 'Dispute Status',
        label: 'Dispute Status',
        align: 'left'
      }],
      quick: {
        loadCount: 0,
        loadAmount: 0,
        loadMembershipFee: 0,
        loadCost: 0
      },
      filterOptions: [
        {
          value: 'tranId',
          label: 'Transaction Id'
        },
        {
          value: 'id',
          label: 'Id'
        },
        {
          value: 'ids',
          label: 'IDs'
        },
        {
          value: 'status',
          label: 'Status',
          options: [
            {
              value: '',
              label: 'All'
            }, {
              value: 'PENDING',
              label: 'PENDING'
            }, {
              value: 'VOIDING',
              label: 'VOIDING'
            }, {
              value: 'RETURNING',
              label: 'RETURNING'
            }, {
              value: 'VOIDED',
              label: 'VOIDED'
            }, {
              value: 'EXPIRED',
              label: 'EXPIRED'
            }, {
              value: 'SETTLING',
              label: 'SETTLING'
            }, {
              value: 'SETTLED',
              label: 'SETTLED'
            }, {
              value: 'BOUNCED',
              label: 'BOUNCED'
            }
          ]
        },
        {
          value: 'transactionDateFrom',
          label: 'Transaction Time',
          range: [
            {
              value: 'transactionDateFrom',
              type: 'date'
            }, {
              value: 'transactionDateTo',
              type: 'date'
            }
          ]
        }, {
          value: 'transactionType',
          label: 'Transaction Type',
          options: [
            {
              value: '',
              label: 'All'
            }, {
              value: '10 = Monthly Fee',
              label: '10 = Monthly Fee'
            }, {
              value: '11 = Monthly Fee Reversal',
              label: '11 = Monthly Fee Reversal'
            }, {
              value: '37 = ACH / Payroll Deposit',
              label: '37 = ACH / Payroll Deposit'
            }, {
              value: '40 = Signature (POS) Purchase',
              label: '40 = Signature (POS) Purchase'
            }, {
              value: '41 = Purchase Return',
              label: '41 = Purchase Return'
            }, {
              value: '51 = Flaged for Charge-off',
              label: '51 = Flaged for Charge-off'
            }, {
              value: '148 = Card to Card Transfer Debit',
              label: '148 = Card to Card Transfer Debit'
            }, {
              value: '149 = Card to Card Transfer Credit',
              label: '149 = Card to Card Transfer Credit'
            }, {
              value: '214 = Purchase Declined Transaction Fee',
              label: '214 = Purchase Declined Transaction Fee'
            }, {
              value: '215 = POS Declined Transaction Fee Refund',
              label: '215 = POS Declined Transaction Fee Refund'
            }, {
              value: '220 = Signature (POS) Purchase Fee',
              label: '220 = Signature (POS) Purchase Fee'
            }, {
              value: '404 = Signature (POS) Purchase (International)',
              label: '404 = Signature (POS) Purchase (International)'
            }, {
              value: '461 = Reserve funding deposit',
              label: '461 = Reserve funding deposit'
            }, {
              value: '462 = Reserve funding deposit reversal',
              label: '462 = Reserve funding deposit reversal'
            }, {
              value: '5032 = ACH Debit',
              label: '5032 = ACH Debit'
            }, {
              value: '00220 = Signature (POS) Purchase Fee',
              label: '00220 = Signature (POS) Purchase Fee'
            }, {
              value: '00613 = Reserve Deposit',
              label: '00613 = Reserve Deposit'
            }, {
              value: '00614 = Reserve Deposit Reversal/Withdrawal',
              label: '00614 = Reserve Deposit Reversal/Withdrawal'
            },
            {
              value: 'Force Post',
              label: 'Force Post'
            }
          ]
        }, {
          value: 'merchantCustName',
          label: 'Merchant Cust Name'
        }, {
          value: 'accountNumber',
          label: 'Account Number'
        }, {
          value: 'userIds',
          label: 'User ID'
        }, {
          value: 'country',
          label: 'Country',
          options: [],
          source: 'countries'
        }, {
          value: 'state',
          label: 'State',
          options: [],
          search: true,
          source: 'states',
          map: {
            country: 'country'
          }
        }, {
          value: 'city',
          label: 'City'
        }, {
          value: 'region',
          label: 'Region',
          options: [],
          source: 'regions'
        }, {
          value: 'billingAddressType',
          label: 'Billing Address Type',
          options: [],
          source: 'reshippers'
        }, {
          value: 'cardProgram',
          label: 'Card Program',
          options: [],
          source: 'cardPrograms'
        }, {
          value: 'cardType',
          label: 'Card Type',
          options: [],
          source: 'cardTypes'
        }, {
          value: 'privacyCardType',
          label: 'Privacy Card Type',
          options: [
            {
              label: 'Single Use',
              value: 'SINGLE_USE'
            },
            {
              label: 'Merchant Locked',
              value: 'MERCHANT_LOCKED'
            }
          ]
        }, {
          value: 'cardCustName',
          label: 'Card Cust Name'
        }, {
          value: 'programManager',
          label: 'Program Manager',
          options: [],
          source: 'programManagers'
        }, {
          value: 'brandPartner',
          label: 'Brand Partner',
          options: [],
          source: 'brandPartners'
        }, {
          value: 'marketingPartner',
          label: 'Marketing Partner',
          options: [],
          source: 'marketingPartners'
        }, {
          value: 'processor',
          label: 'Processor',
          options: [],
          source: 'processors'
        }, {
          value: 'issuingBank',
          label: 'Issuing bank',
          options: [],
          source: 'issuingBanks'
        }, {
          value: 'merchantListType',
          label: 'Merchant List Type',
          options: [],
          source: 'listTypes'
        }, {
          value: 'mcc',
          label: 'Mcc',
          options: [],
          source: 'mccs'
        }, {
          value: 'affiliate_tenant_type',
          label: 'Affiliate Type',
          options: [],
          source: 'affiliateTypes'
        }, {
          value: 'affiliate_select',
          label: 'Affiliate',
          options: [],
          search: true,
          source: 'affiliates',
          map: {
            type: 'affiliate_tenant_type'
          }
        }, {
          value: 'txn_amount',
          label: 'Transaction Amount',
          range: [{
            value: 'txn_amount_min',
            type: 'amount'
          }, {
            value: 'txn_amount_max',
            type: 'amount'
          }]
        }
      ],
      disputeStatuses: [
        'None', 'Expired', 'Sent',
        'Resolved', 'Failed'
      ]
    }
  },
  computed: {
    voiding () {
      return this.$route.path === '/a/transactions/voiding'
    },
    returning () {
      return this.$route.path === '/a/transactions/returning'
    },
    force () {
      return this.$route.path === '/a/transactions/force-posts'
    },
    user () {
      return this.$store.state.User
    },
    masterAdmin () {
      const user = this.user
      return user && user.currentRole === 'MasterAdmin'
    }
  },
  watch: {
    '$route.fullPath' () {
      this.init()
      this.initFilters()
    }
  },
  methods: {
    async request ({ pagination }) {
      this.beforeRequest({ pagination })

      this.loading = true
      const data = this.$refs.filtersDialog.getFilters()
      data.keyword = this.keyword
      if (!_.isEmpty(this.$route.query)) {
        data.isFromDashboard = true
      }
      const resp = await request(`/admin/card-transaction/search/${pagination.page}/${pagination.rowsPerPage}`, 'form', data)
      this.loading = false
      if (resp.success) {
        this.pagination = pagination
        this.data = resp.data.data
        this.pagination.rowsNumber = resp.data.total
        this.quick = resp.data.quick
        this.chartData = resp.data.chartData
        if (this.chartData) {
          eventBus.$emit('reload-top-chart', this.chartData)
        }
      }
    },
    init () {
      this.title = 'Card Transaction List'
      if (this.voiding) {
        this.title = 'Transactions Voiding Queue'
      } else if (this.returning) {
        this.title = 'Return Queue'
      } else if (this.force) {
        this.title = 'Force Posts'
      }

      this.data = []
      this.quick = {}
      this.filters = []
      this.keyword = ''

      if (this.voiding) {
        this.filters = [{
          field: 'status',
          predicate: '=',
          value: 'VOIDING',
          readonly: true
        }]
        this.columns = this.baseColumns.concat([{
          field: 'voidedAt',
          label: 'Voided At',
          align: 'left'
        }, {
          field: 'voidedDates',
          label: 'Voided # of Days',
          align: 'right'
        }, {
          field: '',
          label: 'Action',
          align: 'left'
        }])
      } else if (this.returning) {
        this.filters = [{
          field: 'status',
          predicate: '=',
          value: 'RETURNING',
          readonly: true
        }]
        this.columns = this.baseColumns.concat([{
          field: 'returnedAt',
          label: 'Returned At',
          align: 'left'
        }, {
          field: 'returnedDates',
          label: 'Returned # of Days',
          align: 'right'
        }, {
          field: '',
          label: 'Action',
          align: 'left'
        }])
      } else if (this.force) {
        this.filters = [{
          field: 'transactionType',
          predicate: '=',
          value: 'Force Post',
          readonly: true
        }]
        this.columns = this.baseColumns.concat([])
      } else {
        this.columns = this.baseColumns.concat([{
          field: '',
          label: 'Action',
          align: 'left'
        }])
      }
    },
    async simulate (row, type, params = {}) {
      this.$q.loading.show()
      const resp = await request(`/admin/card-transaction/simulate/${row.id}/${type}`, 'post', params)
      this.$q.loading.hide()
      if (resp.success) {
        notifySuccess(resp)
      }
    },
    async manualRelease (row) {
      this.$q.dialog({
        title: 'Confirm',
        message: 'Are you sure that you want to refund the transaction amount to the account balance now?',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await request(`/admin/card-transaction/manual-release/${row.id}`, 'post')
        this.$q.loading.hide()
        if (resp.success) {
          notifySuccess(resp)
          this.reload()
        }
      }).catch(() => {})
    },
    fromDashboardEx (query) {
      if (query.ids) {
        this.filters.push({
          field: 'ids',
          value: query.ids,
          predicate: '='
        })
      }
    },
    isPrivacyTxn (row) {
      if (!row || !row.tranId) {
        return false
      }
      if (row.cardProgramName !== 'US Unlocked') {
        return false
      }
      if (row.tranId.indexOf('-') < 0) {
        return false
      }
      return true
    },
    viewDetails (row) {
      window.open(`/admin/card-transaction/view-details/${row.tranId}`, '_blank')
    },

    async changeDisputeStatus (row, status) {
      this.$q.loading.show()
      const resp = await this.c.request(`/admin/card-transaction/change-dispute-status/${row.id}`, 'post', {
        status
      })
      this.$q.loading.hide()
      if (resp.success) {
        this.reload()
      }
    },

    async submitDisputeRequest (row) {
      this.$q.dialog({
        title: `Submit Dispute Request`,
        message: `Are you sure that you want to send a dispute request email to Privacy for this transaction?`,
        cancel: true,
        color: 'primary'
      }).then(async () => {
        this.$q.loading.show()
        const resp = await request(`/admin/card-transaction/submit-dispute-request/${row.id}`, 'post')
        this.$q.loading.hide()
        if (resp.success) {
          notifySuccess(resp)
        }
      }).catch(() => {})
    },

    canChangeToLoad (row) {
      if (parseInt(row.amount) >= 0) {
        return false
      }
      if (row.status !== 'SETTLED') {
        return false
      }
      const desc = row.description
      return desc.startsWith('PAYPAL*')
    },

    async changeToLoad (row) {
      this.$q.loading.show()
      const resp = await this.c.request(`/admin/card-transaction/load-paypal-credit/${row.id}`, 'post', {
        status
      })
      this.$q.loading.hide()
      if (resp.success) {
        this.reload()
      }
    }
  },
  mounted () {
    if (this.voiding) {
      this.filterOptions.push({
        value: 'voidingType',
        label: 'Voiding Type',
        options: [
          {
            label: 'All',
            value: ''
          },
          {
            label: 'Partially clearing',
            value: 'clearing'
          }
        ]
      })
    }
  }
}
</script>
