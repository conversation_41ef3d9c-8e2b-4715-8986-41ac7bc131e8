<template>
  <q-page id="load_transactions__load_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-field class="input-button-group inline-flex mr-20">
          <div class="row no-wrap">
            <q-input v-model="keyword"
                     clearable
                     class="w-300"
                     @clear="reload"
                     placeholder="Txn ID, Payment ID, User ID..."></q-input>
            <q-btn label="Search"
                   color="grey-4"
                   class="no-shadow"
                   @click="reload"
                   text-color="black"></q-btn>
          </div>
        </q-field>

        <q-btn class="mr-10"
               color="black"
               @click="download"
               label="Export"></q-btn>
        <q-btn class="mr-10"
               color="black"
               @click="grayCountries"
               label="Gray Countries"></q-btn>

        <q-btn v-if="queue"
               color="positive"
               @click="loadNow()"
               label="Load now"></q-btn>
      </div>
    </div>
    <div class="page-content">
      <div class="quick-result">
        <div>
          <h5>{{ quick.loadCount | number }}</h5>
          <div class="description">Total # of loads</div>
        </div>
        <div v-if="usu">
          <h5>{{ quick.totalBalance | moneyFormat('USD', true) }}</h5>
          <div class="description">Current User Account Balances</div>
        </div>
        <div v-if="usu">
          <h5>{{ quick.usersWithBalance | number }}</h5>
          <div class="description">Total Users with a Balance</div>
        </div>
        <div>
          <h5>{{ quick.loadAmount | moneyFormat('USD', true) }}</h5>
          <div class="description">Gross Load Volume</div>
        </div>
        <div v-if="!privacy">
          <h5>{{ quick.loadMembershipFee | moneyFormat('USD', true) }}</h5>
          <div class="description">Total Membership Fees</div>
        </div>
        <div v-if="!privacy">
          <h5>{{ quick.loadFee | moneyFormat('USD', true) }}</h5>
          <div class="description">Total Load Fees</div>
        </div>
        <div v-if="!privacy">
          <h5>{{ quick.loadCost | moneyFormat('USD', true) }}</h5>
          <div class="description">Total Load Cost</div>
        </div>
        <div v-if="!privacy">
          <h5>{{ quick.revenue | moneyFormat('USD', true) }}</h5>
          <div class="description">Load Net Revenue</div>
        </div>
      </div>

      <TopChart :chart-id="chartId"
                v-if="chartData">
      </TopChart>

      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               :rows-per-page-options="[5, 10, 20, 50]"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <q-btn flat
                 round
                 dense
                 icon="mdi-filter-variant"
                 @click="filtersDialog = true" />
          <FilterChips :filters.sync="filters"
                       :options="filterOptions"
                       v-show="!loadingFilters"
                       @reload="reload"></FilterChips>
        </template>
        <template slot="top-right">
          <q-btn flat
                 round
                 dense
                 class="q-mr-md"
                 @click="reload"
                 icon="refresh" />
          <ColumnFilter :columns="columns"></ColumnFilter>
        </template>

        <q-tr slot="body"
              slot-scope="props"
              :class="`status_${props.row.status}`">
          <q-td v-for="col in props.cols"
                :key="col.field">
            <template v-if="!col.field">
              <q-btn-dropdown size="xs"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          @click.native="loadNow(props.row)">
                    <q-item-main>Load Now</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="refund(props.row)">
                    <q-item-main>Refunded</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="markError(props.row)">
                    <q-item-main>Mark error</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else-if="col.field === 'userId' && p('user')">
              <a :href="`/admin#/a/i/admin__user_modify__modify?user_id=${col.value}`"
                 target="_blank">{{ col.value }}</a>
            </template>
            <template v-else>
              {{ col.value }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <GrayCountries></GrayCountries>
    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>
import { request } from '../../../common'
import ListPageMixin from '../../../mixins/ListPageMixin'
import { chartName } from '../../../const'
import eventBus from '../../../eventBus'
import GrayCountries from './gray_countries'

export default {
  mixins: [
    ListPageMixin
  ],
  components: {
    GrayCountries
  },
  data () {
    return {
      filtersUrl: '/admin/load_transaction/filters',
      downloadUrl: '/admin/load_transaction_export',
      timeField: 'loadDate',
      cardProgramField: 'filter[cp.id=]',
      chartId: chartName.LOAD_CHART,
      baseColumns: [{
        field: 'transactionNo',
        label: 'Txn ID',
        align: 'left'
      }, {
        field: 'paymentReference',
        label: 'Payment Reference',
        align: 'left',
        hidden: true
      }, {
        field: 'paymentId',
        label: 'Payment Id',
        align: 'left',
        hidden: true
      }, {
        field: 'createdAt',
        label: 'Create Time',
        align: 'left',
        hidden: true
      }, {
        field: 'type',
        label: 'Type',
        align: 'left',
        hidden: true
      }, {
        field: 'loadStatus',
        label: 'Status',
        align: 'left'
      }, {
        field: 'cardType',
        label: 'Card Type',
        align: 'left',
        hidden: true
      }, {
        field: 'userId',
        label: 'User ID',
        align: 'left'
      }, {
        field: 'accountNumber',
        label: 'Account Number',
        align: 'left',
        hidden: true
      }, {
        field: 'billingAddressType',
        label: 'Billing Address Type',
        align: 'left',
        hidden: true
      }, {
        field: 'partnerName',
        label: 'Load Partner',
        align: 'left',
        hidden: true
      }, {
        field: 'methodName',
        label: 'loadMethod',
        align: 'left'
      }, {
        field: 'region',
        label: 'Region',
        align: 'left',
        hidden: true
      }, {
        field: 'country',
        label: 'Country',
        align: 'left',
        hidden: true
      }, {
        field: 'state',
        label: 'State',
        align: 'left',
        hidden: true
      }, {
        field: 'city',
        label: 'City',
        align: 'left',
        hidden: true
      }, {
        field: 'initialAmount',
        label: 'Initial Amount',
        align: 'left'
      }, {
        field: 'loadFeeUSD',
        label: 'Load Fee',
        align: 'left',
        hidden: true
      }, {
        field: 'membershipFeeUSD',
        label: 'Membership Fee',
        align: 'left',
        hidden: true
      }, {
        field: 'replacementFeeUSD',
        label: 'Replacement Fee',
        align: 'left',
        hidden: true
      }, {
        field: 'unloadFeeUSD',
        label: 'Unload Fee',
        align: 'left',
        hidden: true
      }, {
        field: 'discount',
        label: 'Discount',
        align: 'left',
        hidden: true
      }, {
        field: 'currencyRate',
        label: 'Currency Rate',
        align: 'left',
        hidden: true
      }, {
        field: 'receivedAt',
        label: 'Received Time',
        align: 'left',
        hidden: true
      }, {
        field: 'receivedLocalAmount',
        label: 'Received Amount',
        align: 'left',
        hidden: true
      }, {
        field: 'receivedAmountUSD',
        label: 'Received Amount(USD)',
        align: 'left',
        hidden: true
      }, {
        field: 'loadAmountUSD',
        label: 'Load Amount',
        align: 'left',
        hidden: true
      }, {
        field: 'commissionUSD',
        label: 'Affiliate Commission',
        align: 'left'
      }, {
        field: 'cost',
        label: 'Cost',
        align: 'left'
      }, {
        field: 'revenue',
        label: 'Net Revenue',
        align: 'right'
      }],
      quick: {
        loadCount: 0,
        totalBalance: 0,
        usersWithBalance: 0,
        loadAmount: 0,
        loadMembershipFee: 0,
        loadFee: 0,
        loadCost: 0
      },
      filterOptions: [
        {
          value: 'filter[ucl.loadStatus=]',
          label: 'Status',
          options: [],
          source: 'loadStatuses'
        }, {
          value: 'filter[u.id=]',
          label: 'User ID'
        }, {
          value: 'filter[ucl.transactionNo=]',
          label: 'Transaction NO.'
        }, {
          value: 'filter[ucl.reload=]',
          label: 'First/Reload',
          options: [
            {
              value: '',
              label: 'All'
            }, {
              value: '0',
              label: 'First time loading'
            }, {
              value: '1',
              label: 'Reloads'
            }
          ]
        }, {
          value: 'filter[ucl.paymentReference=]',
          label: 'Payment Reference'
        }, {
          value: 'filter[ucl.paymentId=]',
          label: 'Payment ID'
        }, {
          value: 'filter[cp.id=]',
          label: 'Card program',
          options: [],
          source: 'cardPrograms'
        }, {
          value: 'filter[ct.id=]',
          label: 'Card type',
          options: [],
          source: 'cardTypes'
        }, {
          value: 'filter[c.id=]',
          label: 'Card nick name',
          options: [],
          source: 'cpCardTypes'
        }, {
          value: 'filter[pm.id=]',
          label: 'Program manager',
          options: [],
          source: 'programManagers'
        }, {
          value: 'filter[bp.id=]',
          label: 'Brand partner',
          options: [],
          source: 'brandPartners'
        }, {
          value: 'filter[mp.id=]',
          label: 'Marketing partner',
          options: [],
          source: 'marketingPartners'
        }, {
          value: 'filter[p.id=]',
          label: 'Processor',
          options: [],
          source: 'processors'
        }, {
          value: 'filter[ib.id=]',
          label: 'Issuing bank',
          options: [],
          source: 'issuingBanks'
        }, {
          value: 'filter[ba.reshipper=]',
          label: 'Billing address type',
          options: [],
          source: 'reshippers'
        }, {
          value: 'filter[country.region=]',
          label: 'Region',
          options: [],
          source: 'regions'
        }, {
          value: 'filter[country.id=]',
          label: 'Country',
          options: [],
          search: true,
          source: 'countries'
        }, {
          value: 'grayCountryId',
          label: 'Gray Country',
          options: [],
          search: true,
          source: 'grayCountries'
        }, {
          value: 'filter[state.id=]',
          label: 'State',
          options: [],
          search: true,
          source: 'states',
          map: {
            country: 'filter[country.id=]'
          }
        }, {
          value: 'filter[u.city]',
          label: 'City'
        }, {
          value: 'filter[ucl.partner=]',
          label: 'Load partner',
          options: [],
          source: 'loadPartners'
        }, {
          value: 'filter[ucl.method=]',
          label: 'Load method',
          options: [],
          source: 'loadMethods'
        }, {
          value: 'filter[ucl.type=]',
          label: 'Type',
          options: [],
          source: 'types'
        }, {
          value: 'filter[aff.affType=]',
          label: 'Affiliate Type',
          options: [],
          source: 'affiliateTypes'
        }, {
          value: 'filter[u.affiliate=]',
          label: 'Affiliate',
          options: [],
          search: true,
          source: 'affiliates',
          map: {
            type: 'filter[aff.affType=]'
          }
        }, {
          value: 'filter[ucl.receivedLocalCurrency=]',
          label: 'Received currency',
          options: [],
          source: 'currencies',
          search: true
        }, {
          value: 'createData',
          label: 'Create date',
          range: [
            {
              value: 'range[ucl.createdAt][start]',
              type: 'date'
            }, {
              value: 'range[ucl.createdAt][end]',
              type: 'date'
            }
          ]
        }, {
          value: 'loadDate',
          label: 'Load date',
          range: [
            {
              value: 'range[ucl.loadAt][start]',
              type: 'date'
            }, {
              value: 'range[ucl.loadAt][end]',
              type: 'date'
            }
          ]
        }, {
          value: 'fee_type',
          label: 'Has fee',
          options: [
            {
              value: '',
              label: 'All'
            }, {
              value: 'loadFee',
              label: 'Load fee'
            }, {
              value: 'membershipFee',
              label: 'Membership fee'
            }, {
              value: 'replacementFee',
              label: 'Replacement fee'
            }, {
              value: 'unloadFee',
              label: 'Unload fee'
            }
          ]
        }, {
          value: 'fee_type_amount',
          label: 'Has fee amount',
          range: [
            {
              value: 'fee_type_min',
              type: 'amount'
            }, {
              value: 'fee_type_max',
              type: 'amount'
            }
          ],
          dependency: {
            value: 'fee_type',
            default: ''
          }
        }
      ]
    }
  },
  watch: {
    '$route.fullPath' () {
      this.init()
      this.initFilters()
    }
  },
  computed: {
    queue () {
      return this.$route.path === '/a/transactions/load-queue'
    },
    usu () {
      return this.$store.state.User.cpKey === 'cp_usu'
    },
    privacy () {
      return this.$store.state.User.currentRole === 'Privacy.com'
    }
  },
  methods: {
    async request ({ pagination }) {
      this.beforeRequest({ pagination })

      this.loading = true
      const data = this.getQueryParams()
      const resp = await request(`/admin/load_transaction_list/${pagination.page}/${pagination.rowsPerPage}`, 'form', data)
      this.loading = false
      if (resp.success) {
        this.pagination = pagination
        this.data = resp.data.data
        this.pagination.rowsNumber = resp.data.total
        this.quick = resp.data.quick
        this.chartData = resp.data.chartData
        if (this.chartData) {
          eventBus.$emit('reload-top-chart', this.chartData)
        }
      }
    },
    postInitFilters () {
      if (this.queue) {
        this.reload()
      }
    },
    init () {
      this.title = this.queue ? 'Card Load Queue' : 'Card Load Transactions'
      this.data = []
      this.quick = {}
      this.filters = []
      this.keyword = ''

      if (this.queue) {
        this.columns = this.baseColumns.concat([{
          field: 'error',
          label: 'Error',
          align: 'left'
        }, {
          label: 'Actions',
          align: 'center'
        }])

        this.filters = [{
          field: 'filter[ucl.loadStatus=]',
          predicate: '=',
          value: 'received',
          readonly: true
        }]
      } else {
        this.columns = this.baseColumns.concat([{
          field: 'loadAmount',
          label: 'Total Loaded',
          align: 'left'
        }, {
          field: 'loadAt',
          label: 'Load Date',
          align: 'left',
          hidden: true
        }])
      }
    },
    refund (item) {
      this.$q.dialog({
        title: 'Confirm',
        message: `Are you sure that the load "${item.transactionNo}" has been refunded?`,
        color: 'negative',
        cancel: true
      }).then(async () => {
        const resp = await request(`/admin/load_transaction/mark_refunded/${item.id}`, 'post')
        if (resp.success) {
          this.reload()
        }
      }).catch(() => {})
    },
    grayCountries () {
      this.$root.$emit('show-gray-countries-management-dialog')
    },
    markError (item) {
      this.$q.dialog({
        title: 'Confirm',
        message: `Are you sure that you want to mark load "${item.transactionNo}" error and remove from the load queue?`,
        color: 'negative',
        cancel: true
      }).then(async () => {
        const resp = await request(`/admin/load_transaction/mark_error/${item.id}`, 'post')
        if (resp.success) {
          this.reload()
        }
      }).catch(() => {})
    },
    async loadNow (item) {
      const message = item
        ? 'Are you sure that you want to load this record now?'
        : 'Are you sure that you want to load all the records in the load queue now?'
      this.$q.dialog({
        title: 'Confirm',
        message,
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        // const url = item ? `/t/cron/load-card/${item.id}` : `/t/cron/load-card-batch`
        const url = item ? `/admin/load_transaction/force_load/${item.id}` : `/t/cron/load-card-batch`
        await request(url, 'get', {}, true)
        this.$q.loading.hide()
        this.$q.notify({
          type: 'positive',
          message: 'Submitted the request. Please refresh the list later to see the result.'
        })
      }).catch(() => {})
    }
  }
}
</script>
