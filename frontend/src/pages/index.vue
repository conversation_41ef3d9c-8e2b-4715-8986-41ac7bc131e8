<template>
  <q-page class="flex flex-center q-px-sm">
    <q-btn color="negative"
           v-for="uc in userCards" :key="uc.id"
           push
           @click="$router.push('/load-card/' + uc.id)"
           size="lg"
           :label="'Load ' + uc.card.name"></q-btn>
  </q-page>
</template>

<script>
import { request, isStaging } from '../common'

export default {
  data () {
    return {
      userCards: []
    }
  },
  methods: {
    async init () {
      const resp = await request('/api/card/list')
      if (resp.success) {
        this.userCards = resp.data

        if (isStaging() && this.userCards.length === 1) {
          this.$router.push(`/load-card/${this.userCards[0].id}`)
        }
      }
    }
  },
  mounted () {
    this.init()
  }
}
</script>
