<template>
  <q-page id="fis__report_monetary__index_page"
          class="fis_page fis_report_page">
    <div class="page-header">
      <div class="title">
        {{ title }}
      </div>
    </div>
    <div class="page-content">
      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <q-btn flat
                 round
                 dense
                 icon="mdi-filter-variant"
                 @click="filtersDialog = true" />
          <FilterChips :filters.sync="filters"
                       :options="filterOptions"
                       v-show="!loadingFilters"
                       @reload="reload"></FilterChips>
        </template>
        <template slot="top-right">
          <q-btn flat
                 round
                 dense
                 class="q-mr-md"
                 @click="reload"
                 icon="refresh" />
          <ColumnFilter :columns="columns"></ColumnFilter>
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freezeColumn.sync="freezeColumn"
                    :columnStyles="columnStyles"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Actions'">
              <q-btn-dropdown :label="col.label"
                              class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark">
                <q-list link>
                  <q-item v-close-overlay
                          v-for="(action, name) in reportActions"
                          :key="action"
                          @click.native="takeReportAction(action, props.row)">
                    <q-item-main>{{ name }}</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="!$c.isLive()"
                          @click.native="optIn(props.row)">
                    <q-item-main>Opt-In Cashback (test)</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              {{ _.get(props.row, col.field) }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload">
            <q-btn color="dark"
                   class="btn-export"
                   @click="download"
                   label="Export" />
          </Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import FreezeColumnMixin from '../../../../mixins/FreezeColumnMixin'
import FisReportActionsMixin from '../../../../mixins/fis/FisReportActionsMixin'
import { notify, request } from '../../../../common'

export default {
  name: 'fis-monetary-report',
  mixins: [
    ListPageMixin,
    FreezeColumnMixin,
    FisReportActionsMixin
  ],
  data () {
    return {
      title: 'Monetary Activity',
      downloadUrl: `/admin/fis/report/monetary/export`,
      requestUrl: `/admin/fis/report/monetary/list`,
      filtersUrl: `/admin/fis/report/monetary/filters`,
      timeField: 'date_range',
      columns: [
        {
          field: 'Date Time',
          label: 'Date Time',
          align: 'left'
        },
        {
          field: 'Tran ID',
          label: 'Tran ID',
          align: 'left'
        },
        {
          field: 'Tran Type',
          label: 'Tran Type',
          align: 'left'
        },
        {
          field: 'Reason Code',
          label: 'Reason Code',
          align: 'left'
        },
        {
          field: 'Derived Response',
          label: 'Derived Response',
          align: 'left'
        },
        {
          field: 'Response',
          label: 'Response',
          align: 'left'
        },
        {
          field: 'Issuing Bank',
          label: 'Issuing Bank',
          align: 'left'
        },
        {
          field: 'Issuing Client',
          label: 'Issuing Client',
          align: 'left'
        },
        {
          field: 'BIN',
          label: 'BIN',
          align: 'left'
        },
        {
          field: 'BIN Currency',
          label: 'BIN Currency',
          align: 'left'
        },
        {
          field: 'PAN',
          label: 'PAN',
          align: 'left'
        },
        {
          field: 'Card Proxy Number',
          label: 'Card Proxy Number',
          align: 'left'
        },
        {
          field: 'Debit/Credit',
          label: 'Debit/Credit',
          align: 'left'
        },
        {
          field: 'Auth Amount',
          label: 'Auth Amount',
          align: 'right'
        },
        {
          field: 'Auth Code',
          label: 'Auth Code',
          align: 'left'
        },
        {
          field: 'Txn Local Amount',
          label: 'Txn Local Amount',
          align: 'right'
        },
        {
          field: 'Transaction Currency',
          label: 'Transaction Currency',
          align: 'left'
        },
        {
          field: 'Settle Amount',
          label: 'Settle Amount',
          align: 'right'
        },
        {
          field: 'MCC',
          label: 'MCC',
          align: 'left'
        },
        {
          field: 'MCC Description',
          label: 'MCC Description',
          align: 'left'
        },
        {
          field: 'Merchant',
          label: 'Merchant',
          align: 'left'
        },
        {
          field: 'Merchant Number',
          label: 'Merchant Number',
          align: 'left'
        },
        {
          field: 'Merchant City',
          label: 'Merchant City',
          align: 'left'
        },
        {
          field: 'Merchant Country',
          label: 'Merchant Country',
          align: 'left'
        },
        {
          field: 'Request Code',
          label: 'Request Code',
          align: 'left'
        },
        {
          field: 'Request Code Description',
          label: 'Request Code Description',
          align: 'left'
        },
        {
          field: 'Acquirer Reference Number',
          label: 'Acquirer Reference Number',
          align: 'left'
        },
        {
          field: 'Acquirer Id',
          label: 'Acquirer Id',
          align: 'left'
        },
        {
          field: 'Actions',
          label: 'Actions',
          align: 'left'
        }
      ],
      freezeColumn: 0,
      freezeColumnRight: 1,
      filterOptions: [
        {
          value: 'currency',
          label: 'Currency',
          options: [],
          source: 'currencies',
          search: true
        },
        {
          value: 'bank',
          label: 'Bank',
          options: [],
          source: 'banks',
          search: true
        },
        {
          value: 'client',
          label: 'Issuing Client',
          options: [],
          source: 'clients',
          search: true
        },
        {
          value: 'date_range',
          label: 'Date Range',
          range: [
            {
              value: 'start',
              type: 'date'
            }, {
              value: 'end',
              type: 'date'
            }
          ]
        },
        {
          value: 'filter[a.cardNumberProxy]',
          label: 'Card Proxy Number'
        },
        {
          value: 'filter[a.txnUid]',
          label: 'Tran ID'
        },
        {
          value: 'filter[a.authorizationCode=]',
          label: 'Auth Code'
        },
        {
          value: 'transaction_amount',
          label: 'Transaction Amount',
          range: [
            {
              value: 'range[a.txnLocalAmount][min]',
              type: 'localAmount'
            },
            {
              value: 'range[a.txnLocalAmount][max]',
              type: 'localAmount'
            }
          ]
        },
        {
          value: 'filter[a.transactionCurrencyAlpha=]',
          label: 'Transaction Currency',
          options: [],
          source: 'transactionCurrencies'
        },
        {
          value: 'filter[a.txnTypeName=]',
          label: 'Tran Type',
          options: [],
          source: 'tranTypes'
        },
        {
          value: 'filter[a.reasonCodeDescription=]',
          label: 'Reason Code Description',
          options: [],
          source: 'reasonCodes'
        },
        {
          value: 'filter[a.derivedRequestCodeDescription=]',
          label: 'Derived Request Code Description',
          options: [],
          source: 'derivedResponses'
        },
        {
          value: 'filter[a.responseDescription=]',
          label: 'Response',
          options: [],
          source: 'responses'
        },
        {
          value: 'filter[a.txnSign=]',
          label: 'Credit/Debit',
          options: [
            {
              label: 'Debit',
              value: '-1'
            },
            {
              label: 'Credit',
              value: '1'
            }
          ]
        },
        {
          value: 'ach_type',
          label: 'ACH Type',
          options: [
            {
              label: 'ACH IN',
              value: 'in'
            },
            {
              label: 'ACH OUT',
              value: 'out'
            }
          ]
        },
        {
          value: 'filter[a.mcc=]',
          label: 'MCC'
        },
        {
          value: 'filter[a.mccDescription=]',
          label: 'MCC Description',
          options: [],
          source: 'mccDescriptions'
        },
        {
          value: 'filter[a.merchantName]',
          label: 'Merchant Name'
        },
        {
          value: 'filter[a.merchantNumber]',
          label: 'Merchant Number'
        },
        {
          value: 'filter[a.merchantCity]',
          label: 'Merchant City'
        },
        {
          value: 'filter[a.merchantCountryName]',
          label: 'Merchant Country',
          options: [],
          source: 'merchantCountries'
        },
        {
          value: 'filter[a.actualRequestCodeDescription]',
          label: 'Request Code Description'
        },
        {
          value: 'filter[a.actualRequestCode=+a.txnSign=]',
          label: 'Request Code',
          and: 'filter[a.txnSign=]'
        },
        {
          value: 'filter[a.acquirerReferenceNumber]',
          label: 'Acquirer Reference Number'
        },
        {
          value: 'filter[a.acquirerId]',
          label: 'Acquirer ID'
        }
      ],
      reportProxyField: 'Card Proxy Number'
    }
  },
  methods: {
    async optIn (row) {
      this.$q.loading.show()
      const resp = await request(`/admin/fis/cashback/cards/opt-in`, 'post', row)
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp.message)
      }
    }
  }
}
</script>
