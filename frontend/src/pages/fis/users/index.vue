<template>
  <q-page id="fis__users__index_page" class="fis_page fis_report_page">
    <div class="page-header">
      <div class="title">
        {{ title }}
      </div>
      <div class="fun-group">
        <q-btn label="Add User" to="/j/fis/users/add"
               v-if="c.isSuperAdmin()"
               color="primary"></q-btn>
      </div>
    </div>
    <div class="page-content mt-15">
      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <q-btn flat round dense
                 icon="mdi-filter-variant"
                 @click="filtersDialog = true"/>
          <FilterChips :filters.sync="filters"
                       :options="filterOptions"
                       v-show="!loadingFilters"
                       @reload="reload"></FilterChips>
        </template>
        <template slot="top-right">
          <q-btn flat round dense
                 class="q-mr-md"
                 @click="reload"
                 icon="refresh"/>
          <ColumnFilter :columns="columns"></ColumnFilter>
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freezeColumn.sync="freezeColumn"
                    :columnStyles="columnStyles"
                    :props="props"></StickyHead>
        <q-tr slot="body" slot-scope="props" :props="props"
              :class="{even: props.row.__index % 2}">
          <q-td v-for="(col, i) in props.cols" :key="col.field" :align="col.align"
                :style="columnStyle(i)"
                :class="{freeze: i <= freezeColumn, 'last-freeze': i === freezeColumn, 'tr_inactive': props.row.Status === 'Inactive'}">
            <template v-if="col.field === 'Date Time'">
              {{ props.row[col.field] | date }}
            </template>
            <template v-else-if="col.field === 'Action'">
              <q-btn-dropdown size="xs"
                              color="grey-3"
                              text-color="black"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay :to="`/j/fis/users/${props.row['ID']}/edit`">
                    <q-item-main>Edit</q-item-main>
                  </q-item>
                  <q-item v-close-overlay v-if="props.row['Status'] === 'Active'"
                          @click.native="deleteItem(props.row)">
                    <q-item-main>Delete</q-item-main>
                  </q-item>
                  <q-item v-close-overlay v-if="props.row['Status'] === 'Inactive'"
                          @click.native="recoverItem(props.row)">
                    <q-item-main>Recover</q-item-main>
                  </q-item>
                  <q-item v-if="p('user_managment__user__login_at') && c.isSuperAdmin() && props.row['User Type'] !== user.currentRole"
                          v-close-overlay
                          @click.native="$c.loginAs(props.row['ID'])">
                    <q-item-main>Login As</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              {{ _.get(props.row, col.field) }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination" slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload">
            <q-btn color="dark"
                   class="btn-export"
                   @click="download"
                   label="Export"/>
          </Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'
import { generateColumns } from '../../../common'

export default {
  name: 'fis-users',
  mixins: [
    ListPageMixin,
    FreezeColumnMixin
  ],
  data () {
    return {
      title: 'Users',
      filtersUrl: `/admin/fis/users/filters`,
      requestUrl: `/admin/fis/users/list`,
      downloadUrl: `/admin/fis/users/export`,
      columns: generateColumns([
        'ID', 'First Name', 'Last Name',
        'Email', 'Account Create Date', 'Last Login Date',
        'Status', 'User Type', 'Company', 'Platforms', 'Action'
      ]),
      freezeColumn: 0,
      filterOptions: [
        {
          value: 'filter[u.id=]',
          label: 'User ID'
        }, {
          value: 'filter[u.firstName=]',
          label: 'First Name'
        }, {
          value: 'filter[u.lastName=]',
          label: 'Last Name'
        }, {
          value: 'filter[u.email=]',
          label: 'Email'
        }, {
          value: 'filter[u.status=]',
          label: 'Status'
        }, {
          value: 'filter[r.id=]',
          label: 'User Type',
          options: [],
          source: 'roles'
        }
      ]
    }
  },
  computed: {
    user () {
      return this.$store.state.User
    }
  },
  methods: {
    async deleteItem (item) {
      this.$q.dialog({
        title: 'Confirm',
        message: 'Are you sure that you want to delete this user?',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await this.c.request(`/admin/fis/users/${item.ID}/delete`, 'delete')
        this.$q.loading.hide()
        if (resp.success) {
          item.Status = 'Inactive'
        }
      }).catch(() => {})
    },
    async recoverItem (item) {
      this.$q.loading.show()
      const resp = await this.c.request(`/admin/fis/users/${item.ID}/recover`, 'post')
      this.$q.loading.hide()
      if (resp.success) {
        item.Status = 'Active'
      }
    }
  }
}
</script>
