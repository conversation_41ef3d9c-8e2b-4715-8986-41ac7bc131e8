<template>
  <div id="cashbackRewardsDashboard"
       class="row gutter-sm">
    <div class="col-xs-6 col-sm-3"
         v-for="(n, i) in numbers"
         :key="i">
      <q-card>
        <q-card-title>
          <span>{{ n.title }}</span>
        </q-card-title>
        <q-card-main>
          <div v-if="n.title != 'Total Transactions'"
               class="font-22 heavy mb-10">{{ n.value }} <q-chip dense
                    :class="n.delta >= 0 ? 'positive' : 'negative'">{{ n.delta | percent(1, true) }}</q-chip>
          </div>
          <div v-else
               class="font-22 heavy mb-10">{{ n.value | moneyFormat}} <q-chip dense
                    :class="n.delta >= 0 ? 'positive' : 'negative'">{{ n.delta | percent(1, true) }}</q-chip>
          </div>
          <a :href="n.url"
             @click="viewReport(n.title)"
             class="link bold font-12">View Report <i class="mdi mdi-arrow-right"></i></a>
        </q-card-main>
      </q-card>
    </div>
    <div class="col-xs-12">
      <TopRewards></TopRewards>
    </div>
    <div class="col-xs-6">
      <TotalSpend></TotalSpend>
    </div>
    <div class="col-xs-6">
      <PointsEarned></PointsEarned>
    </div>
    <div class="col-xs-6">
      <DollarsEarned></DollarsEarned>
    </div>
    <div class="col-xs-6">
      <Creditted></Creditted>
    </div>
  </div>
</template>
<script >
import TotalSpend from './cashbackRewardsCommon/totalSpend'
import Creditted from './cashbackRewardsCommon/creditted'
import DollarsEarned from './cashbackRewardsCommon/dollarsEarned'
import PointsEarned from './cashbackRewardsCommon/pointsEarned'
import TopRewards from './cashbackRewardsCommon/topRewards'
export default {
  components: {
    TotalSpend,
    PointsEarned,
    DollarsEarned,
    Creditted,
    TopRewards
  },
  data () {
    return {
      numbers: [
        {
          title: 'Active Accounts',
          delta: 0,
          value: 0
        },
        {
          title: 'Total Transactions',
          delta: 0,
          value: 0
        }
      ]
    }
  }
}
</script>
<style lang="scss">
#cashbackRewardsDashboard {
  .cashback-rewards-chart {
    height: 400px;
  }
  .chart-title {
    p {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
    }
    span {
      font-size: 28px;
      font-weight: 600;
    }
  }
  .chart-item {
    position: relative;
  }
  .loading {
    height: 400px;
    line-height: 400px;
    width: calc(100% - 32px);
    text-align: center;
    position: absolute;
    top: 0;
    z-index: 100;
    background: rgba($color: #000000, $alpha: 0.1);
  }
  .tooltip-area-left :after {
    content: "";
    position: absolute;
    border: 10px solid transparent;
    border-top-color: #fff;
    top: 100%;
    left: 40px;
  }
  .tooltip-area-right :after {
    content: "";
    position: absolute;
    border: 10px solid transparent;
    border-top-color: #fff;
    top: 100%;
    right: 40px;
  }
  .cashback-rewards-btn {
    max-height: 32px !important;
    color: #fff;
    text-transform: capitalize;
  }
  .report-btn {
    background: #0062ff;
  }
  .down-btn {
    background: #00de00;
  }
}
</style>
