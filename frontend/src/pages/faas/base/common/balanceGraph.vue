<template>
  <q-card class="partnerBalance mt-16">
    <q-card-title>
      <div class="row">
        <div class="chart-title">
          <p>{{ title }}</p>
          <span>{{delta | moneyFormat}}</span>
          <q-btn v-if="($store.state.User.cpKey === 'cp_spendr') &&
          ($store.state.User.merchantStatus === 'Approved' || $store.state.User.merchantActive) &&
          !$store.state.User.isSpendrAdminLogin &&
          ($store.state.User.teams.indexOf('Spendr Merchant Other Admin') === -1) &&
          (
            !$store.state.User.isLocationHasDummyMerchant ||
            (
              $store.state.User.isLocationHasDummyMerchant &&
              $store.state.User.teams.indexOf('Spendr Merchant Admin') !== -1
            )
          ) &&
          (chartId === 'spendrMerchant')"
                 color="primary"
                 label="Remove Funds"
                 @click="removeFunds"
                 class="btn-mini ml-8 remove-funds-btn"
                 no-caps></q-btn>
        </div>
        <q-btn v-if="($store.state.User.cpKey === 'cp_spendr') && (chartId === 'spendrMerchant')"
               color="primary"
               class="graph-btn ml-auto down-chart-btn btn-mini square"
               @click="gotoReportPage"
               icon="mdi-chart-bar"></q-btn>
      </div>
    </q-card-title>
    <q-card-main class="chart-item" v-if="$store.state.User.cpKey === 'cp_spendr'"></q-card-main>
    <q-card-main class="chart-item" v-else>
      <div :id="chartId"
           class="chart-item"></div>
      <div v-if="visible"
           class="loading">
        <q-spinner-hourglass color="primary"
                             size="3em"
                             :thickness="2" />
      </div>
    </q-card-main>
    <RemoveFunds v-if="chartId === 'spendrMerchant'"></RemoveFunds>
  </q-card>
</template>

<script>
import FaasChartMixin from '../../../../mixins/faas/FaasChartMixin'
import $ from 'jquery'
import RemoveFunds from '../../../spendr/dashboard/removeFunds'
import { EventHandlerMixin } from '../../../../common'

export default {
  mixins: [
    FaasChartMixin,
    EventHandlerMixin('reload-balance-graph', 'getData')
  ],
  model: {
    prop: 'value',
    event: 'change'
  },
  components: {
    RemoveFunds
  },
  watch: {
    value () {
      this.getData()
    }
  },
  data () {
    return {
    }
  },
  async mounted () {
    this.getData()
  },
  methods: {
    resize () {
      this.chart.resize()
    },
    async getData () {
      this.visible = true
      if (this.$store.state.User.cpKey !== 'cp_spendr') {
        this.initChart()
      }
      await this.reloadData()
      this.visible = false
      if (this.$store.state.User.cpKey !== 'cp_spendr') {
        $(window).on('resize', this.resize)
      }
    },
    removeFunds () {
      this.$root.$emit('show-spendr-remove-funds-dialog')
    },
    gotoReportPage () {
      if (this.$store.state.User.teams.indexOf('Spendr Merchant Admin') !== -1) {
        this.$router.push('/h/spendr/merchant/loads')
      } else {
        this.$router.push('/h/spendr/balance-history')
      }
    }
  }
}
</script>
<style lang="scss">
.partnerBalance {
  width: 100%;
  .chart-title {
    p {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
    }
    span {
      font-size: 28px;
      font-weight: 600;
    }
  }
  .chart-item {
    height: 400px;
  }
  .chart-item {
    position: relative;
  }
  .loading {
    height: 400px;
    line-height: 400px;
    width: calc(100% - 32px);
    text-align: center;
    position: absolute;
    top: 0;
    z-index: 100;
    background: rgba($color: #000000, $alpha: 0.1);
  }
  .tooltip-area-left :after {
    content: "";
    position: absolute;
    border: 10px solid transparent;
    border-top-color: #fff;
    top: 100%;
    left: 40px;
  }
  .tooltip-area-right :after {
    content: "";
    position: absolute;
    border: 10px solid transparent;
    border-top-color: #fff;
    top: 100%;
    right: 40px;
  }
  .remove-funds-btn {
    padding: 2px 10px !important;
    margin-top: -12px;
  }
}
</style>
