<template>
  <q-page id="card_management__inventory_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-btn color="primary"
               to="/a/card-management/inventory/import"
               icon="mdi-upload"
               label="Import"></q-btn>
      </div>
    </div>
    <div class="page-content">
      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               :rows-per-page-options="[5, 10, 20, 50]"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <q-btn flat round dense
                 icon="mdi-filter-variant"
                 @click="filtersDialog = true"/>
          <FilterChips :filters.sync="filters"
                       :options="filterOptions"
                       v-show="!loadingFilters"
                       @reload="reload"></FilterChips>
        </template>
        <template slot="top-right">
          <q-btn flat round dense
                 class="q-mr-md"
                 @click="reload"
                 icon="refresh"/>
          <ColumnFilter :columns="columns"></ColumnFilter>
        </template>

        <q-tr slot="body" slot-scope="props" :props="props">
          <q-td v-for="col in props.cols" :key="col.field" :align="col.align">
            <template v-if="['user', 'platform', 'cardProgram', 'clientId', 'bin', 'hash'].includes(col.field)">
              <span v-html="$options.filters.searchMatch(_.get(props.row, col.field), keyword)"></span>
            </template>
            <template v-else-if="!col.field">
              <q-btn-dropdown size="xs"
                              color="grey-3"
                              text-color="black"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          v-if="!props.row.card"
                          @click.native="remove(props.row)">
                    <q-item-main>Delete this card</q-item-main>
                  </q-item>
                  <q-item v-close-overlay @click.native="remove(props.row, true)">
                    <q-item-main>Delete all unused cards in this batch</q-item-main>
                  </q-item>
                  <q-item v-close-overlay v-if="!props.row.user" @click.native="invalidate(props.row)">
                    <q-item-main>Mark as invalid</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              {{ _.get(props.row, col.field) }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination" slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { EventHandlerMixin, request, notifySuccess } from '../../../common'

export default {
  name: 'CardInventoryIndex',
  mixins: [
    ListPageMixin,
    EventHandlerMixin('admin_card_inventory_search', 'search')
  ],
  data () {
    return {
      title: 'Card Inventory',
      requestUrl: '/admin/card_management/inventory/list',
      filtersUrl: '/admin/card_management/inventory/filters',
      autoLoad: true,
      autoReloadWhenUrlChanges: true,
      columns: [
        {
          field: 'id',
          label: 'ID',
          align: 'left'
        }, {
          field: 'platform',
          label: 'Platform',
          align: 'left'
        }, {
          field: 'cardProgram',
          label: 'Card Program',
          align: 'left'
        }, {
          field: 'batchId',
          label: 'Batch ID',
          align: 'left'
        }, {
          field: 'row',
          label: 'Row No.',
          align: 'left'
        }, {
          field: 'bin',
          label: 'BIN',
          align: 'left'
        }, {
          field: 'clientId',
          label: 'Client ID',
          align: 'left'
        }, {
          field: 'hash',
          label: 'Registration Key (Hash)',
          align: 'left'
        }, {
          field: 'distributionCenter',
          label: 'Distribution Center',
          align: 'left'
        }, {
          field: 'createdAt',
          label: 'Imported At',
          align: 'left'
        }, {
          field: 'user',
          label: 'User',
          align: 'left'
        }, {
          field: 'card',
          label: 'Card',
          align: 'left'
        }, {
          field: 'status',
          label: 'Status',
          align: 'left'
        }, {
          field: '',
          label: 'Action',
          align: 'left'
        }
      ],
      quick: {
        count: 0
      },
      filterOptions: [
        {
          value: 'filter[p.id=]',
          label: 'Platform',
          options: [],
          source: 'platforms'
        },
        {
          value: 'filter[cp.id=]',
          label: 'Card program',
          options: [],
          source: 'cardPrograms'
        }
      ]
    }
  },
  methods: {
    remove (row, all = false) {
      let msg = 'Are you sure that you want to delete this card?'
      let url = `/admin/card_management/inventory/delete/${row.id}/card`
      if (all) {
        msg = `Are you sure that you want to delete *** ALL unused cards *** in the batch ${row.batchId}?`
        url = `/admin/card_management/inventory/delete/${row.batchId}/batch`
      }
      this.$q.dialog({
        title: `Delete cards`,
        message: msg,
        cancel: true,
        color: 'negative'
      }).then(async () => {
        this.$q.loading.show()
        const resp = await request(url, 'delete')
        this.$q.loading.hide()
        if (resp.success) {
          notifySuccess(resp)
          this.reload()
        }
      }).catch(() => {})
    },
    invalidate (row) {
      this.$q.dialog({
        title: 'Invalidate card',
        message: 'Please input the error/reason:',
        cancel: true,
        prompt: {
          model: '',
          type: 'text'
        }
      }).then(async error => {
        this.$q.loading.show()
        const resp = await request(`/admin/card_management/inventory/invalidate/${row.id}/card`, 'post', {
          error
        })
        this.$q.loading.hide()
        if (resp.success) {
          notifySuccess(resp)
          this.reload()
        }
      }).catch(() => {})
    }
  }
}
</script>

<style lang="scss">
  @import '../../../css/variable';

  #card_management__inventory_page {
  }
</style>
