<template>
  <q-page id="card_management__card_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-btn class="mr-10"
               color="black"
               @click="download"
               label="Export"></q-btn>
      </div>
    </div>
    <div class="page-content">
      <div class="quick-result">
        <div class="max-w-300">
          <h5>{{ quick.count | number }}</h5>
          <div class="description">Total # of cards</div>
        </div>
      </div>
      <TopChart :chart-id="chartId"
                v-if="chartData">
      </TopChart>
      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               :rows-per-page-options="[5, 10, 20, 50]"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <q-btn flat round dense
                 icon="mdi-filter-variant"
                 @click="filtersDialog = true"/>
          <FilterChips :filters.sync="filters"
                       :options="filterOptions"
                       v-show="!loadingFilters"
                       @reload="reload"></FilterChips>
        </template>
        <template slot="top-right">
          <q-btn flat round dense
                 class="q-mr-md"
                 @click="reload"
                 icon="refresh"/>
          <ColumnFilter :columns="columns"></ColumnFilter>
        </template>

        <q-tr slot="body"
              slot-scope="props" :class="`status_${props.row.status}`">
          <q-td v-for="col in props.cols" :key="col.field" :class="`text-${col.align || 'left'}`">
            <template v-if="col.field === 'status' && props.row.status === 'Closed'">
              <span class="text-negative">{{ col.value }}</span>
            </template>
            <template v-else-if="col.field === 'userInfo' && p('user')">
              <a :href="`/admin#/a/i/admin__user_modify__modify?user_id=${props.row.userId}`"
                 target="_blank">{{ col.value }}</a>
            </template>
            <template v-else-if="!col.field">
              <q-btn-dropdown size="xs"
                              color="grey-3"
                              text-color="black"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          :to="`/a/i/admin__card-balance?user_card_id=${props.row.id}`">
                    <q-item-main>View Balance History</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="props.row.sandbox"
                          @click.native="simulate(props.row, 'authorize')">
                    <q-item-main>Simulate: Authorization</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="props.row.sandbox"
                          @click.native="simulate(props.row, 'authorize', {status: 'BALANCE_INQUIRY'})">
                    <q-item-main>Simulate: Authorization (BALANCE_INQUIRY)</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="props.row.sandbox"
                          @click.native="simulate(props.row, 'authorize', {status: 'CREDIT_AUTHORIZATION'})">
                    <q-item-main>Simulate: Authorization (CREDIT_AUTHORIZATION)</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="props.row.sandbox"
                          @click.native="simulate(props.row, 'authorize', {status: 'FINANCIAL_AUTHORIZATION'})">
                    <q-item-main>Simulate: Authorization (FINANCIAL_AUTHORIZATION)</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="props.row.sandbox"
                          @click.native="simulate(props.row, 'authorize', {status: 'FINANCIAL_CREDIT_AUTHORIZATION'})">
                    <q-item-main>Simulate: Authorization (FINANCIAL_CREDIT_AUTHORIZATION)</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="props.row.sandbox"
                          @click.native="simulate(props.row, 'credit')">
                    <q-item-main>Simulate: Credit Authorization Advice</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="props.row.sandbox"
                          @click.native="simulate(props.row, 'return')">
                    <q-item-main>Simulate: Return</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="props.row.manualLoad"
                          @click.native="manualLoad('load', props.row)">
                    <q-item-main>Manual Load</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="props.row.manualLoad"
                          @click.native="manualLoad('unload', props.row)">
                    <q-item-main>Manual Unload</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              {{ col.value }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination" slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>
    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>
import { notifySuccess, request } from '../../../common'
import ListPageMixin from '../../../mixins/ListPageMixin'
import eventBus from '../../../eventBus'
import { chartName } from '../../../const'
import _ from 'lodash'

export default {
  mixins: [
    ListPageMixin
  ],
  data () {
    return {
      title: 'Cards View',
      filtersUrl: '/admin/card_management/filters',
      downloadUrl: '/admin/card_export',
      chartId: chartName.ACTIVE_CARDS_CHART,
      autoLoad: true,
      autoReloadWhenUrlChanges: true,
      columns: [
        {
          field: 'accountNumber',
          label: 'Account Number',
          align: 'left'
        }, {
          field: 'pan',
          label: 'PAN',
          align: 'left'
        }, {
          field: 'type',
          label: 'Privacy Card Type',
          align: 'left'
        }, {
          field: 'spendLimit',
          label: 'Spend Limit',
          align: 'left'
        }, {
          field: 'spendLimitDuration',
          label: 'Spend Limit Duration',
          align: 'left'
        }, {
          field: 'fullName',
          label: 'Card Name',
          align: 'left'
        }, {
          field: 'userInfo',
          label: 'User Id & Name',
          align: 'left'
        }, {
          field: 'balanceText',
          label: 'Balance',
          align: 'left'
        }, {
          field: 'issued',
          label: 'Issued',
          align: 'left'
        }, {
          field: 'currency',
          label: 'Currency',
          align: 'left'
        }, {
          field: 'createdAt',
          label: 'Create Date',
          align: 'left'
        }, {
          field: 'expireAt',
          label: 'Expire Date',
          align: 'left'
        }, {
          field: 'status',
          label: 'Status',
          align: 'left'
        }, {
          label: 'Actions',
          align: 'center'
        }
      ],
      quick: {
        count: 0
      },
      filterOptions: [
        {
          value: 'filter[u.status=]',
          label: 'Account Status',
          options: [],
          source: 'accountStatus'
        }, {
          value: 'filter[uc.accountNumber=]',
          label: 'Card Account Number',
          search: true
        }, {
          value: 'filter[uc.type=]',
          label: 'Privacy Card Type',
          options: [
            {
              label: 'All',
              value: ''
            }, {
              label: 'SINGLE_USE',
              value: 'SINGLE_USE'
            }, {
              label: 'MERCHANT_LOCKED',
              value: 'MERCHANT_LOCKED'
            }, {
              label: 'UNLOCKED',
              value: 'UNLOCKED'
            }, {
              label: 'PHYSICAL',
              value: 'PHYSICAL'
            }
          ]
        }, {
          value: 'filter[uc.spendLimitDuration=]',
          label: 'Spend Limit Duration',
          options: [
            {
              label: 'All',
              value: ''
            }, {
              label: 'TRANSACTION',
              value: 'TRANSACTION'
            }, {
              label: 'MONTHLY',
              value: 'MONTHLY'
            }, {
              label: 'ANNUALLY',
              value: 'ANNUALLY'
            }, {
              label: 'FOREVER',
              value: 'FOREVER'
            }
          ]
        }, {
          value: 'filter[cp.id=]',
          label: 'Card program',
          options: [],
          source: 'cardPrograms'
        }, {
          value: 'filter[uc.status=]',
          label: 'Card status',
          options: [
            {
              label: 'Active',
              value: 'active'
            }, {
              label: 'Inactive',
              value: 'inactive'
            }, {
              label: 'Closed',
              value: 'closed'
            }
          ]
        }, {
          value: 'filter[ct.id=]',
          label: 'Card type',
          options: [],
          source: 'cardTypes'
        }, {
          value: 'filter[u.firstName=]',
          label: 'First Name'
        }, {
          value: 'filter[u.lastName=]',
          label: 'Last Name'
        }, {
          value: 'filter[ct.network=]',
          label: 'Network type',
          options: [],
          source: 'networks'
        }, {
          value: 'filter[ctpr.id=]',
          label: 'Processor',
          options: [],
          source: 'processors'
        }, {
          value: 'filter[pm.id=]',
          label: 'Program manager',
          options: [],
          source: 'programManagers'
        }, {
          value: 'filter[u.id=]',
          label: 'User ID'
        }, {
          value: 'cardbalance',
          label: 'Card balance',
          range: [{
            value: 'card_balance_min',
            type: 'amount'
          }, {
            value: 'card_balance_max',
            type: 'amount'
          }]
        }
      ]
    }
  },
  methods: {
    fromDashboard () {},
    async request ({ pagination }) {
      this.beforeRequest({ pagination })

      this.loading = true
      const data = this.getQueryParams()
      data.keyword = this.keyword
      if (!_.isEmpty(this.$route.query)) {
        data.isFromDashboard = true
        data.start = this.$route.query.start
        data.end = this.$route.query.end
        data.cardProgram = this.$route.query.cardProgram
      }
      const resp = await request(`/admin/card-index/${pagination.page}/${pagination.rowsPerPage}`, 'form', data)
      this.loading = false
      if (resp.success) {
        this.pagination = pagination
        this.data = resp.data.data
        this.quick.count = resp.data.total
        this.pagination.rowsNumber = resp.data.total
        this.chartData = resp.data.chartData
        if (this.chartData) {
          eventBus.$emit('reload-top-chart', this.chartData)
        }
      }
    },
    manualLoad (type, card) {
      this.$q.dialog({
        title: `Manual ${_.upperFirst(type)}`,
        message: `Please input the amount (USD) you want to ${type} ${type === 'load' ? 'to' : 'from'} this card?`,
        prompt: {
          model: '',
          type: 'number'
        },
        cancel: true
      }).then(async amount => {
        if (!amount || _.trim(amount) === '') {
          return this.$q.notify(`Invalid ${type} amount!`)
        }
        this.$q.loading.show()
        const resp = await request(`/admin/card/manual/action/${type}/${card.id}`, 'post', {
          amount
        })
        this.$q.loading.hide()
        if (resp.success) {
          this.reload()
        }
      }).catch(() => {})
    },
    async simulate (card, type, params = {}) {
      this.$q.loading.show()
      const resp = await request(`/admin/card/simulate/${card.id}/${type}`, 'post', params)
      this.$q.loading.hide()
      if (resp.success) {
        notifySuccess(resp)
      }
    }
  }
}
</script>

<style lang="scss">
  @import '../../../css/variable';

  #card_management__card_page {
    .input-button-group {
      min-width: 420px;
    }
  }
</style>
