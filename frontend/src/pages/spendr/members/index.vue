<template>
  <q-page id="spendr_members__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm top-statics-row top-statics-row-sm">
        <div class="col-sm-3 col-4">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-account"
                        color="positive"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.total || 0 }}</div>
                  <div class="description">Total Members</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-4" v-if="!bankAdmin">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash-multiple"
                        color="blue"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.totalBalance || 0 | moneyFormat }}</div>
                  <div class="description">Total Active Balances</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-4" v-if="!bankAdmin">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash-multiple"
                        color="warning"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.averageBalance || 0 | moneyFormat }}</div>
                  <div class="description">Average Active Balance</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-4" v-if="!bankAdmin">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-account"
                        color="warning"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.averageMemberAge || 0 }}</div>
                  <div class="description">Average Member Age</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
               :columns="filteredVisibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-right">
          <q-btn v-if="!bankAdmin && !spendrEmployee"
                 icon="mdi-account-group-outline"
                 color="positive"
                 label="Create members"
                 @click="add"
                 class="btn-sm mr-8"
                 no-caps></q-btn>
          <q-btn v-if="!bankAdmin && !spendrCustomerSupport"
                 icon="mdi-file-download-outline"
                 color="blue"
                 label="Zendesk Export"
                 @click="downloadBraze"
                 class="btn-sm mr-8"
                 no-caps></q-btn>
          <q-btn v-if="!bankAdmin && !spendrCustomerSupport"
                 icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>
<!--          <q-btn icon="mdi-account-group-outline"-->
<!--                 color="blue"-->
<!--                 label="Negative Balance Members"-->
<!--                 @click="negativeBalanceMember"-->
<!--                 class="btn-sm mr-8"-->
<!--                 no-caps></q-btn>-->
          <q-select v-model="balance"
                    :options="balanceOptions"
                    @input="reload"
                    class="mr-10 earn-type"></q-select>
          <q-select v-model="loadStatus"
                    :options="loadStatusOptions"
                    @input="reload"
                    class="mr-10 earn-type"></q-select>
          <q-select v-model="status"
                    :options="statusOptions"
                    @input="reload"
                    class="mr-10 earn-type"></q-select>
          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Status'">
              <q-chip class="font-12"
                      :class="styleClass(props.row['Status'])">
                {{ props.row['Status'] }}
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Load Status'">
              <span class="capitalize">{{ props.row['Load Status'] }}</span>
            </template>
            <template v-else-if="col.field === 'Actions'">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          @click.native="viewConsumers(props.row)">
                    <q-item-main>View</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="!bankAdmin && !spendrAccountant && !spendrCustomerSupport"
                          @click.native="edit(props.row)">
                    <q-item-main>Edit</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="!bankAdmin && !spendrAccountant && !spendrCustomerSupport && props.row['Delete Apply At']"
                          @click.native="handleDelete(props.row)">
                    <q-item-main>Handle Delete Apply</q-item-main>
                  </q-item>
                  <q-item v-if="isMasterAdmin() || agentAdmin || spendrCompliance"
                          v-close-overlay
                          @click.native="manualLoad(props.row)">
                    <q-item-main>Manually load</q-item-main>
                  </q-item>
                  <q-item v-if="isMasterAdmin() || agentAdmin || spendrCompliance"
                          v-close-overlay
                          @click.native="refundMember(props.row)">
                    <q-item-main>Manually unload</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="isMasterAdmin() || agentAdmin || spendrCompliance"
                          @click.native="changeStatus(props.row,props.row['Status'] === 'Active' ? 'Inactive' : 'Active')">
                    <q-item-main>Change to {{props.row['Status'] === 'Active' ? 'Inactive' : 'Active'}}</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="isMasterAdmin() || agentAdmin || spendrCompliance"
                          @click.native="manuallyChangeLoadStatus(props.row)">
                    <q-item-main>Manually Change Load Status</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="(isMasterAdmin() || agentAdmin || spendrCompliance)
                           && props.row['changeManualCardStatus']"
                          @click.native="changeManualCardPrefund(props.row)">
                    <q-item-main>Change Manual Card Status</q-item-main>
                  </q-item>
                  <q-item v-if="isMasterAdmin() || agentAdmin || spendrCompliance"
                          v-close-overlay
                          @click.native="disableIdentityMatch(props.row)">
                    <q-item-main>{{ props.row['Identity Match'] ? 'Disable Identity Match' : 'Enable Identity Match' }}</q-item-main>
                  </q-item>
                  <q-item v-if="isMasterAdmin() || agentAdmin || spendrCompliance"
                          v-close-overlay
                          @click.native="allowSameday(props.row)">
                    <q-item-main>Allow Sameday Link</q-item-main>
                  </q-item>
<!--                  <q-item v-close-overlay-->
<!--                          v-if="(isMasterAdmin() || agentAdmin) && props.row['isCanReactiveLoadStatus']"-->
<!--                          @click.native="reactivateLoadStatus(props.row)">-->
<!--                    <q-item-main>Reactivate Load Status</q-item-main>-->
<!--                  </q-item>-->
                  <q-item v-if="!bankAdmin && !spendrAccountant"
                          v-close-overlay
                          @click.native="$c.loginAs(props.row['User ID'])">
                    <q-item-main>Login as</q-item-main>
                  </q-item>
<!--                  <q-item v-if="(isMasterAdmin() || agentAdmin) && !props.row['Email Verified']"-->
<!--                          v-close-overlay-->
<!--                          @click.native="resendInvitation(props.row)">-->
<!--                    <q-item-main>Resend Invitation Email</q-item-main>-->
<!--                  </q-item>-->
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
    <BatchExportDialog ref="exportZendesk" :show-all="true"></BatchExportDialog>
    <RefundMember></RefundMember>
    <ManualLoad></ManualLoad>
    <DetailDialog></DetailDialog>
    <ManualCardStatus/>
    <DeleteAccountDialog/>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns, notify, request } from '../../../common'
import SpendrPageMixin from '../../../mixins/spendr/SpendrPageMixin'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'
import MexMemberMixin from '../../../mixins/mex/MexMemberMixin'
import DetailDialog from './detail'
import RefundMember from './refund_member'
import ManualLoad from './manual_load'
import ManualCardStatus from './changeManualCardStatus'
import DeleteAccountDialog from './closeAccount'

export default {
  name: 'spendr-members',
  mixins: [
    SpendrPageMixin,
    MexMemberMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-spendr-members')
  ],
  components: {
    DetailDialog,
    RefundMember,
    ManualLoad,
    ManualCardStatus,
    DeleteAccountDialog
  },
  data () {
    const columns = [
      'User ID', 'First Name', 'Last Name', 'Email',
      'Phone', 'Active Balance', 'Accept T&Cs At',
      'Last Login', 'ACH Return Times (Insufficient Funds)', 'Load Status', 'Status',
      'Actions'
    ]
    if (!this.isMasterAdmin()) {
      columns.splice(columns.indexOf('Active Balance'), 1)
    }
    return {
      title: 'Members',
      requestUrl: `/admin/spendr/members/list`,
      downloadUrl: `/admin/spendr/members/export`,
      downloadBrazeUrl: '/admin/spendr/members/export-braze',
      columns: generateColumns(columns),
      filterOptions: [
        {
          value: 'filter[u.id=]',
          label: 'User ID'
        },
        {
          value: 'filter[u.firstName=]',
          label: 'First Name'
        },
        {
          value: 'filter[u.lastName=]',
          label: 'Last Name'
        },
        {
          value: 'filter[u.email=]',
          label: 'Email'
        }
      ],
      balanceOptions: [
        { label: 'Balance', value: 'all' },
        { label: 'Active Balance', value: 'active' },
        { label: 'Negative Balance', value: 'negative' }
      ],
      balance: 'all',
      loadStatusOptions: [
        { label: 'Load Status', value: 'all' },
        { label: 'Instant', value: 'instant' },
        { label: 'Prefund', value: 'prefund' },
        { label: 'Freeze', value: 'freeze' }
      ],
      loadStatus: 'all',
      statusOptions: [
        { label: 'Status', value: 'all' },
        { label: 'Active', value: 'active' },
        { label: 'Inactive', value: 'inactive' },
        { label: 'Onboard (SMS)', value: 'register_consumer_created' },
        { label: 'Apply Delete', value: 'apply_delete' }
      ],
      status: 'all',
      freezeColumn: -1,
      freezeColumnRight: 3,
      autoLoad: true,
      filterNegativeBalanceMember: 'all'
    }
  },
  methods: {
    styleClass (status) {
      if (status === 'Onboard (email)') {
        return 'blue'
      } else {
        return this.statusClass(status)
      }
    },
    downloadBraze () {
      const data = this.mergeQuerySortParams(this.pagination)
      data.query_type = 'all'
      this.$refs.exportZendesk.show(this.downloadBrazeUrl, data)
    },
    add () {
      this.$root.$emit('show-spendr-member-detail-dialog')
    },
    edit (row) {
      this.$root.$emit('show-spendr-member-detail-dialog', row)
    },
    handleDelete (row) {
      this.$root.$emit('show-spendr-close-account-dialog', row)
    },
    viewConsumers (row) {
      window.open(`/admin#/h/spendr/consumers/${row['User ID']}`, '_blank')
    },
    refundMember (row) {
      this.$root.$emit('show-spendr-refund-member-dialog', row)
    },
    manualLoad (row) {
      this.$root.$emit('show-spendr-manual-load-dialog', row)
    },
    async resendInvitation (row) {
      this.$q.loading.show()
      const resp = await request(
        `/admin/spendr/members/resend-invitation`,
        'post',
        { id: row['User ID'] }
      )
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp)
      }
    },
    changeStatus (row, status) {
      if (row['Status'] === 'Onboard (SMS)' && status === 'Active') {
        this.$q.dialog({
          title: 'Confirm',
          message: 'Do you want to change the register step to Active also?',
          color: 'negative',
          cancel: true
        }).then(() => {
          this.saveStatus(row, status, true)
        }).catch(() => {
          this.saveStatus(row, status)
        })
      } else {
        this.saveStatus(row, status)
      }
    },
    async saveStatus (row, status, activeRegisterStep = false) {
      this.$q.loading.show()
      const resp = await request(`/admin/spendr/members/${row['User ID']}/toggle-status`, 'post', {
        Status: status,
        activeRegisterStep: activeRegisterStep
      })
      this.$q.loading.hide()
      if (resp.success) {
        this.reload()
      }
    },
    async disableIdentityMatch (row) {
      this.$q.loading.show()
      const resp = await request(`/admin/spendr/members/${row['User ID']}/disable-identity-match`, 'post', {
        'disableMatch': row['Identity Match'] === true
      })
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp)
        this.reload()
      }
    },
    async allowSameday (row) {
      this.$q.dialog({
        title: 'Confirm',
        message: 'Are you sure to allow this consumer link bank card via the same-day micro-deposits(Only enable the same-day micro-deposits in 1 day)?',
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await request(`/admin/spendr/members/${row['User ID']}/allow-sameday`, 'post')
        this.$q.loading.hide()
        if (resp.success) {
          notify(resp)
        }
      })
    },
    // Change manually linked card`s load type to prefund or allow $200 instant amount
    async changeManualCardPrefund (row) {
      this.$root.$emit('show-spendr-change-manual-card-status-dialog', row)
    },
    getOtherQueryParams () {
      return {
        // 'negativeBalanceMember': this.filterNegativeBalanceMember,
        'balance': this.balance,
        'loadStatus': this.loadStatus,
        'status': this.status
      }
    },
    // negativeBalanceMember () {
    //   this.filterNegativeBalanceMember = this.filterNegativeBalanceMember === 'all' ? 'negative' : 'all'
    //   this.reload()
    // },
    async manuallyChangeLoadStatus (row) {
      this.$q.loading.show()
      const resp = await request(`/admin/spendr/members/${row['User ID']}/current-load-status`)
      this.$q.loading.hide()
      if (resp.success) {
        const newLoadStatus = resp.data.newLoadStatus
        const promptMessage = resp.data.promptMessage
        this.$q.dialog({
          title: 'Confirm',
          message: promptMessage,
          color: 'negative',
          cancel: true
        }).then(async () => {
          this.$q.loading.show()
          const resp = await request(
            `/admin/spendr/members/${row['User ID']}/toggle-load-status`,
            'post',
            { newLoadStatus }
          )
          this.$q.loading.hide()
          if (resp.success) {
            notify(resp)
            this.reload()
          }
        })
      }
    }
  }
}
</script>

<style lang="scss">
  .spendr_members__index_page {
    .capitalize {
      text-transform: capitalize;
    }
  }
  .modal.minimized .modal-content {
    max-width: 600px;
  }
  .earn-type {
    min-width: 100px;
  }
</style>
