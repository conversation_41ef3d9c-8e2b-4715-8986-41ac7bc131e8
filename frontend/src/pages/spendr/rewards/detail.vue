<template>
  <q-dialog
    class="spendr-reward-detail-dialog"
    v-model="visible"
    prevent-close
  >
    <template slot="title">
      <div class="font-16 mb-2">{{ edit ? 'Edit Promo Code' : 'Create new Promo Code' }}</div>
      <div class="font-12 normal text-dark">Please fill in the information below about the Promo Code.</div>
      <q-btn class="close" round flat @click="_hide" icon="close"/>
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5">
        <div class="col-sm-6">
          <q-input float-label="Campaign Name" autocomplete="no"
                   :error="$v.entity['Campaign Name'].$error"
                   @blur="$v.entity['Campaign Name'].$touch"
                   v-model="entity['Campaign Name']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Campaign Budget" autocomplete="no"
                   type="number"
                   prefix="$"
                   :error="$v.entity['Campaign Budget'].$error"
                   @blur="$v.entity['Campaign Budget'].$touch"
                   v-model="entity['Campaign Budget']"></q-input>
        </div>
        <div class="col-sm-12">
          <q-input float-label="Campaign Description" autocomplete="no"
                   type="textarea"
                   :error="$v.entity['Campaign Description'].$error"
                   @blur="$v.entity['Campaign Description'].$touch"
                   v-model="entity['Campaign Description']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-datetime float-label="Start Date" type="datetime"
                      format="MM/DD/YYYY HH:mm"
                      :error="$v.entity['Begin Date'].$error"
                      @blur="$v.entity['Begin Date'].$touch"
                      v-model="entity['Begin Date']"></q-datetime>
        </div>
        <div class="col-sm-6">
          <q-datetime float-label="End Date" type="datetime"
                      format="MM/DD/YYYY HH:mm"
                      :min="entity['Begin Date']"
                      :error="$v.entity['End Date'].$error"
                      @blur="$v.entity['End Date'].$touch"
                      v-model="entity['End Date']"></q-datetime>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Promo Code" autocomplete="no"
                   :readonly="edit"
                   :error="$v.entity['Promo Code'].$error"
                   @blur="$v.entity['Promo Code'].$touch"
                   v-model="entity['Promo Code']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Promo Code Amount" autocomplete="no"
                   :readonly="edit"
                   type="number"
                   prefix="$"
                   :error="$v.entity['Promo Amount'].$error"
                   @blur="$v.entity['Promo Amount'].$touch"
                   v-model="entity['Promo Amount']"></q-input>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <div class="row wp-100">
        <q-btn label="Cancel" no-caps
               color="grey-3"
               text-color="tertiary"
               @click="_hide" />
        <q-btn v-if="entity['Status'] != 'completed'"
               :label="edit ? 'Save Promo Code' : 'Create Promo Code'"
               no-caps
               color="positive" class="main"
               @click="save" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { notifyForm, notify, request } from '../../../common'
import { helpers, minValue, required } from 'vuelidate/lib/validators'

export default {
  name: 'spendr-reward-detail-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      defaultEntity: {
        'Promo Code ID': 0
      }
    }
  },
  computed: {
    edit () {
      return !!this.entity['Promo Code ID']
    },
    user () {
      return this.$store.state.User
    }
  },
  validations: function () {
    const amountValidator = helpers.regex('Up to two decimal places', /^(([1-9]{1}\d*)|(0{1}))(\.\d{1,2})?$/)
    const promoCodeValidator = helpers.regex(
      'Must include at least 1 number & 1 letter(6-16 length)',
      /^(?=.*[a-zA-Z])(?=.*\d)[^]{6,16}$/i)
    const validations = {
      entity: {}
    }
    const fields = [
      'Campaign Name', 'Campaign Budget', 'Campaign Description',
      'Begin Date', 'End Date', 'Promo Code', 'Promo Amount'
    ]
    for (const field of fields) {
      validations.entity[field] = { required }
    }
    validations.entity['Campaign Budget'] = { required, minValue: minValue(0.01), amountValidator }
    validations.entity['Promo Amount'] = { required, minValue: minValue(0.01), amountValidator }
    validations.entity['Promo Code'] = { required, promoCodeValidator }
    return validations
  },
  methods: {
    show () {
      if (!this.entity || !this.entity['Promo Code ID']) {
        return
      }
      this.entity['Campaign Budget'] = this.entity['Budget']
      this.entity['Promo Amount'] = this.entity['Amount']
    },
    async save () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }
      this.$q.loading.show({
        message: 'Saving changes...'
      })
      const resp = await request(`/admin/spendr/rewards/save`, 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp.message)
        this.$root.$emit('reload-spendr-rewards')
        this._hide()
      }
    }
  }
}
</script>

<style lang="scss">
.spendr-reward-detail-dialog {
  .modal-content {
    width: 580px;
  }

  .modal-scroll {
    max-height: none;
  }
}
</style>
