<template>
  <q-page id="spendr_rewards__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm top-statics-row top-statics-row-sm">
        <div class="col-xl-3 col-sm-4 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-account"
                        color="positive"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.total || 0 }}</div>
                  <div class="description">Total Active Promo Codes </div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
<!--        <div class="col-xl-3 col-sm-4 col-6">-->
<!--          <q-card class="top-statics">-->
<!--            <q-card-main>-->
<!--              <div class="row">-->
<!--                <q-icon name="mdi-cash-multiple"-->
<!--                        color="blue"></q-icon>-->
<!--                <div class="column">-->
<!--                  <div class="value">{{ quick.budget || 0 | moneyFormat }}</div>-->
<!--                  <div class="description">Total Promo Code Budget</div>-->
<!--                </div>-->
<!--              </div>-->
<!--            </q-card-main>-->
<!--          </q-card>-->
<!--        </div>-->
        <div class="col-xl-3 col-sm-4 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash-multiple"
                        color="blue"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.redeem_total || 0 | moneyFormat }}</div>
                  <div class="description">Total Promo Code Redemptions</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-xl-3 col-sm-4 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash-multiple"
                        color="warning"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.avg_redeem || 0 | moneyFormat }}</div>
                  <div class="description">Average Promo Code Redemption</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
               :columns="filteredVisibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>{{ title }} Report</strong>
        </template>
        <template slot="top-right">
          <q-btn v-if="!bankAdmin"
                 icon="mdi-file-download-outline"
                 color="orange"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>
          <q-btn v-if="isMasterAdmin() || agentAdmin"
                 icon="mdi-account-group-outline"
                 color="blue"
                 label="Create Promo Code"
                 @click="add"
                 class="btn-sm mr-8"
                 no-caps></q-btn>
          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Status'">
              <q-chip class="font-12"
                      :class="styleClass(props.row)">
                {{ proStatus(props.row) }}
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Actions'
             && (isMasterAdmin() || agentAdmin)">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
<!--                  <q-item v-close-overlay-->
<!--                          @click.native="viewConsumers(props.row)">-->
<!--                    <q-item-main>View</q-item-main>-->
<!--                  </q-item>-->
                  <q-item v-close-overlay
                          @click.native="edit(props.row)">
                    <q-item-main>Edit</q-item-main>
                  </q-item>
                  <q-item v-if="props.row['Status'] != 'completed'" v-close-overlay
                          @click.native="changeStatus(props.row,props.row['Status'] === 'stopped' ? 'active' : 'stopped')">
                    <q-item-main>{{props.row['Status'] === 'stopped' ? 'Start' : 'Stop'}}</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>
    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
    <DetailDialog></DetailDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns, request } from '../../../common'
import SpendrPageMixin from '../../../mixins/spendr/SpendrPageMixin'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'
import MexMemberMixin from '../../../mixins/mex/MexMemberMixin'
import DetailDialog from './detail'

export default {
  name: 'spendr-rewards',
  mixins: [
    SpendrPageMixin,
    MexMemberMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-spendr-rewards')
  ],
  components: {
    DetailDialog
  },
  data () {
    const columns = [
      'Promo Code ID', 'Campaign Name', 'Campaign Budget', 'Promo Code',
      'Promo Amount', '# Of Redemption', 'Total $ Redeemed', 'Begin Date',
      'End Date', 'Status', 'Actions'
    ]
    return {
      title: 'Promotion Code',
      requestUrl: `/admin/spendr/rewards/list`,
      downloadUrl: `/admin/spendr/rewards/export`,
      filtersUrl: `/admin/spendr/rewards/filters`,
      columns: generateColumns(columns),
      filterOptions: [
        {
          value: 'filter[c.status=]',
          label: 'Status',
          options: [],
          source: 'rewardStatuses'
        },
        {
          value: 'filter[c.name=]',
          label: 'Campaign Name'
        },
        {
          value: 'filter[c.promoCode=]',
          label: 'Promo Code'
        },
        {
          value: 'filter[c.id=]',
          label: 'Promo Code ID'
        }
      ],
      freezeColumn: -1,
      freezeColumnRight: 2,
      autoLoad: true
    }
  },
  methods: {
    styleClass (row) {
      let status = row['Status']
      if (status === 'active' && !row['Started']) {
        status = 'pending'
      }
      if (row['Ended']) {
        status = 'completed'
      }
      return {
        'active': 'positive',
        'pending': 'dark',
        'stopped': 'negative',
        'completed': 'blue'
      }[status] || status
    },
    proStatus (row) {
      let status = row['Status']
      if (status === 'active' && !row['Started']) {
        status = 'pending'
      }
      if (row['Ended']) {
        status = 'completed'
      }
      return status
    },
    // viewPromoCodeInfo (row) {
    //   console.log(row['Promo Code Id'])
    // },
    // viewPromoCodeRedemption (row) {
    //   console.log(row['Promo Code Id'])
    //   // window.open(`/admin#/h/spendr/rewards/${row['Promo Code ID']}`, '_blank')
    // },
    add () {
      this.$root.$emit('show-spendr-reward-detail-dialog')
    },
    edit (row) {
      this.$root.$emit('show-spendr-reward-detail-dialog', row)
    },
    async changeStatus (row, status) {
      this.$q.loading.show()
      const resp = await request(`/admin/spendr/rewards/${row['Promo Code ID']}/toggle-status`, 'post', {
        Status: status
      })
      this.$q.loading.hide()
      if (resp.success) {
        this.reload()
      }
    }
  }
}
</script>

<style lang="scss">
.spendr_rewards__index_page {
  .capitalize {
    text-transform: capitalize;
  }
}
.modal.minimized .modal-content {
  max-width: 600px;
}
</style>
