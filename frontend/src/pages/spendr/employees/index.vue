<template>
  <q-page id="spendr_employees__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
    </div>
    <div class="page-content top-statics-style">
      <div class="row gutter-sm top-statics-row top-statics-row-sm">
        <div class="col-xl-3 col-sm-4 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-account-outline"
                        color="positive"></q-icon>
                <div class="column">
                  <div class="description">Total Employees</div>
                  <div class="value">{{ quick.total || 0 }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-xl-3 col-sm-4 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-rocket"
                        color="purple"></q-icon>
                <div class="column">
                  <div class="description">Total Managers</div>
                  <div class="value">{{ quick.manager || 0  }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-xl-3 col-sm-4 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cellphone-iphone"
                        color="orange"></q-icon>
                <div class="column">
                  <div class="description">Total Clerks</div>
                  <div class="value">{{ quick.clerk || 0 }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
               :columns="filteredVisibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>{{ title }} Report</strong>
        </template>
        <template slot="top-right">
          <q-btn v-if="$store.state.User.merchantStatus === 'Approved' &&
          !spendrLogin &&
          !merchantAccountantAdmin &&
          !merchantOtherAdmin"
                 icon="mdi-account-outline"
                 color="positive"
                 label="Create employee(s)"
                 @click="add"
                 class="btn-sm mr-8"
                 no-caps></q-btn>
          <q-btn v-if="!spendrCustomerSupportLogin && !merchantOtherAdmin"
                 icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>
          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <div notranslate="">
              <template v-if="col.field === 'User ID'">
                <span>{{ _.get(props.row, col.field) }}</span>
              </template>
              <template v-else-if="col.field === 'Employee Status'">
                <q-chip class="font-12"
                        :class="props.row['Status'] === 'Active' ? 'positive' : 'negative'">
                  {{ props.row['Status'] }}
                </q-chip>
              </template>
              <template v-else-if="col.field === 'Locations'">
                <template v-for="(v, k) in props.row['Locations']">
                  <br v-if="(k !== 0) && (k % 3 === 0) && isShow(props.row['User ID'])"
                  :key="k + '_br'"
                  />
                  <q-chip class="font-12 mr-5 mb-5" v-if="k < 3" :key="k">
                    {{ v }}
                  </q-chip>
                  <q-chip class="font-12 mr-5 mb-5" v-else-if="isShow(props.row['User ID'])" :key="k">
                    {{ v }}
                  </q-chip>
                </template>
                <br/>
                <a v-if="props.row['Locations'].length > 3"
                  @click="showMore(props.row['User ID'], !isShow(props.row['User ID']))"
                  class="cursor-pointer text-primary underline"
                >
                  {{ isShow(props.row['User ID']) ? 'Hide' : 'Multiple locations' }}
                </a>
              </template>

              <template v-else-if="col.field === 'Actions' &&
               !spendrLogin &&
               !merchantAccountantAdmin &&
               !merchantOtherAdmin">
                <q-btn-dropdown class="btn-sm"
                                no-caps
                                color="grey-2"
                                text-color="dark"
                                :label="col.label">
                  <q-list link>
                    <q-item v-close-overlay
                            @click.native="edit(props.row)">
                      <q-item-main>Edit</q-item-main>
                    </q-item>
<!--                    <q-item v-close-overlay-->
<!--                            @click.native="$c.loginAs(props.row['User ID'])">-->
<!--                      <q-item-main>Login as</q-item-main>-->
<!--                    </q-item>-->
                  </q-list>
                </q-btn-dropdown>
              </template>
              <template v-else>{{ _.get(props.row, col.field) }}</template>
            </div>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
    <DetailDialog>
    </DetailDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns } from '../../../common'
import SpendrPageMixin from '../../../mixins/spendr/SpendrPageMixin'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'
import DetailDialog from './detail'

export default {
  name: 'spendr-locations',
  mixins: [
    SpendrPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-spendr-employees')
  ],
  components: {
    DetailDialog
  },
  data () {
    const columns = [
      'User ID', 'First Name', 'Last Name', 'Email',
      'Phone', 'Employee Type', 'Locations', 'Employee Status', 'Actions'
    ]
    if (this.isSpendrCustomerSupportLogin() || this.isMerchantAccountantAdmin() || this.isMerchantOtherAdmin()) {
      columns.splice(columns.indexOf('Actions'), 1)
    }
    return {
      title: 'Employees',
      requestUrl: `/admin/spendr/merchant/employees/list`,
      downloadUrl: `/admin/spendr/merchant/employees/export`,
      columns: generateColumns(columns),
      filterOptions: [
        {
          value: 'filter[u.id=]',
          label: 'User ID'
        },
        {
          value: 'filter[u.firstName=]',
          label: 'First Name'
        },
        {
          value: 'filter[u.lastName=]',
          label: 'Last Name'
        },
        {
          value: 'filter[u.email=]',
          label: 'Email'
        }
      ],
      keyword: '',
      freezeColumn: -1,
      freezeColumnRight: 2,
      autoLoad: true,
      showMoreUserIds: []
    }
  },
  methods: {
    add () {
      this.$root.$emit('show-spendr-employee-detail-dialog')
    },
    edit (row) {
      this.$root.$emit('show-spendr-employee-detail-dialog', row)
    },
    showMore (userId, show = false) {
      if (show) {
        this.showMoreUserIds.push(userId)
      } else {
        this.showMoreUserIds.splice(this.showMoreUserIds.indexOf(userId), 1)
      }
    },
    isShow (userId) {
      return this.showMoreUserIds.indexOf(userId) !== -1
    }
  }
}
</script>
