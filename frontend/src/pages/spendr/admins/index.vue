<template>
  <q-page id="spendr_admins__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm top-statics-row top-statics-row-sm">
        <div class="col-sm-3 col-4">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-account" color="positive"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.total || 0 }}</div>
                  <div class="description">Total Admins</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
               :columns="filteredVisibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-right">
          <q-btn icon="mdi-account-group-outline"
                 color="positive"
                 label="Create Admins"
                 v-if="!bankAdmin && !spendrEmployee"
                 @click="add"
                 class="btn-sm mr-8"
                 no-caps></q-btn>
          <q-btn icon="mdi-file-download-outline"
                 v-if="!bankAdmin"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>
          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Status'">
              <q-chip class="font-12"
                      :class="statusClass(props.row['Status'])">
                {{ props.row['Status'] }}
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Actions' && !bankAdmin">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          @click.native="edit(props.row)">
                    <q-item-main>Edit</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="changeStatus(props.row, 'Active')"
                          v-if="props.row['Status'] === 'Inactive'">
                    <q-item-main>Change to Active</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="changeStatus(props.row, 'Inactive')"
                          v-else>
                    <q-item-main>Change to Inactive</q-item-main>
                  </q-item>
                  <q-item v-if="!props.row['Last Login']"
                          v-close-overlay
                          @click.native="resendInvitation(props.row)">
                    <q-item-main>Resend Invitation Email</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="masterAdmin"
                          @click.native="$c.loginAs(props.row['User ID'])">
                    <q-item-main>Login As</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>

    <DetailDialog></DetailDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns, notify } from '../../../common'
import SpendrPageMixin from '../../../mixins/spendr/SpendrPageMixin'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'
import MexMemberMixin from '../../../mixins/mex/MexMemberMixin'
import DetailDialog from './detail'

export default {
  name: 'spendr-admins',
  mixins: [
    SpendrPageMixin,
    MexMemberMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-spendr-admins')
  ],
  components: {
    DetailDialog
  },
  data () {
    const columns = [
      'User ID', 'First Name', 'Last Name', 'Email',
      'Phone', 'User Type', 'Last Login', 'Status',
      'Actions'
    ]
    if (this.isBankAdmin()) {
      columns.splice(columns.indexOf('Actions'), 1)
    }
    return {
      title: 'Admins',
      requestUrl: `/admin/spendr/admins/list`,
      downloadUrl: `/admin/spendr/admins/export`,
      columns: generateColumns(columns),
      filterOptions: [
        {
          value: 'filter[u.id=]',
          label: 'User ID'
        },
        {
          value: 'filter[u.firstName=]',
          label: 'First Name'
        },
        {
          value: 'filter[u.lastName=]',
          label: 'Last Name'
        },
        {
          value: 'filter[u.email=]',
          label: 'Email'
        },
        {
          value: 'filter[t.name=]',
          label: 'User Type',
          options: [
            { label: 'Program Owner', value: 'Spendr Program Owner' },
            { label: 'Bank', value: 'Spendr Bank' },
            { label: 'Customer Support', value: 'Spendr Customer Support' },
            { label: 'Compliance', value: 'Spendr Compliance' }
          ]
        }
      ],
      freezeColumn: -1,
      freezeColumnRight: 2,
      autoLoad: true
    }
  },
  methods: {
    add () {
      this.$root.$emit('show-spendr-admin-detail-dialog')
    },
    edit (row) {
      this.$root.$emit('show-spendr-admin-detail-dialog', row)
    },
    async changeStatus (row, status) {
      this.$q.loading.show()
      const resp = await this.c.request(`/admin/spendr/admins/${row['User ID']}/toggle-status`, 'post', {
        Status: status
      })
      this.$q.loading.hide()
      if (resp.success) {
        this.reload()
      }
    },
    async resendInvitation (row) {
      this.$q.loading.show()
      const resp = await this.c.request(`/admin/spendr/admins/${row['User ID']}/resend-invitation`, 'post')
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp)
      }
    }
  }
}
</script>
