<template>
  <q-page id="spendr_locations__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
    </div>
    <div class="page-content top-statics-style">
      <div class="row gutter-sm top-statics-row top-statics-row-sm">
        <div class="col-xl-3 col-sm-4 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-map-marker"
                        color="positive"></q-icon>
                <div class="column">
                  <div class="description">Total Locations</div>
                  <div class="value">{{ quick.total || 0 }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-xl-3 col-sm-4 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash"
                        color="purple"></q-icon>
                <div class="column">
                  <div class="description">Total Location Revenue</div>
                  <div class="value">{{ quick.totalRevenue || 0 | moneyFormat }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-xl-3 col-sm-4 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash"
                        color="orange"></q-icon>
                <div class="column">
                  <div class="description">Average Location Revenue</div>
                  <div class="value">{{ quick.averageRevenue || 0 | moneyFormat }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
               :columns="filteredVisibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>{{ title }} Report</strong>
        </template>
        <template slot="top-right">
          <q-btn v-if="$store.state.User.merchantStatus === 'Approved' &&
          !spendrLogin &&
          !merchantAccountantAdmin &&
          !merchantOtherAdmin"
                 icon="mdi-map-marker"
                 color="positive"
                 label="Create locations"
                 @click="add"
                 class="btn-sm mr-8"
                 no-caps></q-btn>
          <q-btn v-if="!spendrCustomerSupportLogin && !merchantOtherAdmin"
                 icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>
          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <div notranslate="">
              <template v-if="col.field === 'Location Name'">
                {{ _.get(props.row, 'Name') }}
              </template>
              <template v-else-if="col.field === 'Location Address'">
                {{ _.get(props.row, 'Address') }}
              </template>
              <template v-else-if="col.field === 'Status'">
                <q-chip class="font-12"
                        :class="props.row['Status'] === 'Active' ? 'positive' : 'negative'">
                  {{ props.row['Status'] }}
                </q-chip>
              </template>
              <template v-else-if="col.field === 'Actions' &&
               !spendrLogin &&
               !merchantAccountantAdmin &&
               !merchantOtherAdmin">
                <q-btn-dropdown class="btn-sm"
                                no-caps
                                color="grey-2"
                                text-color="dark"
                                :label="col.label">
                  <q-list link>
                    <q-item v-close-overlay
                            @click.native="edit(props.row)">
                      <q-item-main>Edit</q-item-main>
                    </q-item>
                  </q-list>
                </q-btn-dropdown>
              </template>
              <template v-else>{{ _.get(props.row, col.field) }}</template>
            </div>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
    <DetailDialog>
    </DetailDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns } from '../../../common'
import SpendrPageMixin from '../../../mixins/spendr/SpendrPageMixin'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'
import DetailDialog from './detail'

export default {
  name: 'spendr-locations',
  mixins: [
    SpendrPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-spendr-locations')
  ],
  components: {
    DetailDialog
  },
  data () {
    const columns = [
      'Location ID', 'Location Name', 'Location Address', 'Phone', '# Of Employees',
      'Balance', 'Country', 'State/Province', 'City', 'Zip', 'Business Hours',
      'Business Hours Saturday & Sunday', 'Status', 'Actions'
    ]
    if (
      this.isSpendrCustomerSupportLogin() ||
      this.isMerchantAccountantAdmin() ||
      this.isMerchantOtherAdmin()
    ) {
      columns.splice(columns.indexOf('Actions'), 1)
    }
    return {
      title: 'Locations',
      requestUrl: `/admin/spendr/merchant/locations/list`,
      downloadUrl: `/admin/spendr/merchant/locations/export`,
      columns: generateColumns(columns),
      filterOptions: [
        {
          value: 'filter[l.id=]',
          label: 'Location ID'
        },
        {
          value: 'filter[l.name=]',
          label: 'Location Name'
        },
        {
          value: 'filter[a.phone=]',
          label: 'Phone'
        },
        {
          value: 'filter[a.country=]',
          label: 'Country',
          search: true,
          options: [],
          source: 'countrys'
        }
      ],
      keyword: '',
      freezeColumn: -1,
      freezeColumnRight: 2,
      autoLoad: true
    }
  },
  methods: {
    add () {
      this.$root.$emit('show-spendr-location-detail-dialog')
    },
    edit (row) {
      this.$root.$emit('show-spendr-location-detail-dialog', row)
    }
  }
}
</script>
