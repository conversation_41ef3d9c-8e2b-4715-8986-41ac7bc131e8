<template>
  <q-dialog
    class="spendr-group-detail-dialog"
    v-model="visible"
    prevent-close
  >
    <template slot="title">
      <div class="font-16 mb-2">{{ edit ? 'Edit Group' : 'Create New Group' }}</div>
      <div class="font-12 normal text-dark">Please fill in the information below about your group.</div>
      <q-btn class="close" round flat @click="_hide" icon="close"/>
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5">
        <div class="col-sm-6">
          <q-input float-label="Name" autocomplete="no"
                   :error="$v.entity['Name'].$error"
                   @input="$v.entity['Name'].$touch"
                   v-model="entity['Name']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Contact Name" autocomplete="no"
                   :error="$v.entity['Contact Name'].$error"
                   @input="$v.entity['Contact Name'].$touch"
                   v-model="entity['Contact Name']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Contact Email" autocomplete="no"
                   :error="$v.entity['Contact Email'].$error"
                   @input="$v.entity['Contact Email'].$touch"
                   v-model="entity['Contact Email']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Contact Phone" autocomplete="no"
                   :error="$v.entity['Contact Phone'].$error"
                   @input="$v.entity['Contact Phone'].$touch"
                   v-model="entity['Contact Phone']"></q-input>
        </div>
        <div class="col-sm-12 mb-20">
          <q-select float-label="Merchants"
                    autocomplete="no"
                    :options="merchantList"
                    filter
                    chips
                    autofocus-filter
                    multiple
                    v-model="entity['groupMerchantIds']"></q-select>
        </div>
        <!-- <div class="col-sm-12 pt-20" v-show="edit">
          <div class="bold">Status</div>
          <div class="mt-8">
            <q-radio v-model="entity['Status']"
                     color="blue"
                     :error="$v.entity['Status'].$error"
                     @change="$v.entity['Status'].$touch"
                     val="Active"
                     label="Active"></q-radio>
            <q-radio v-model="entity['Status']"
                     color="blue"
                     class="ml-15"
                     :error="$v.entity['Status'].$error"
                     @change="$v.entity['Status'].$touch"
                     val="Inactive"
                     label="Inactive"></q-radio>
          </div>
        </div> -->
      </div>
    </template>
    <template slot="buttons">
      <div class="row wp-100">
        <q-btn label="Cancel" no-caps
               color="grey-3"
               text-color="tertiary"
               @click="_hide" />
        <q-btn :label="edit ? 'Save Changes' : 'Create Group'"
               no-caps
               color="positive" class="main"
               @click="save" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import {
  notifyForm,
  notify,
  request
} from '../../../common'
import MexStateListMixin from '../../../mixins/mex/MexStateListMixin'
import _ from 'lodash'
import { required } from 'vuelidate/lib/validators'

export default {
  name: 'spendr-group-detail-dialog',
  mixins: [
    Singleton,
    MexStateListMixin
  ],
  data () {
    return {
      defaultEntity: {
        'ID': 0,
        'Status': 'Active',
        'Contact Name': null,
        'Contact Email': null,
        'Contact Phone': null,
        'groupMerchantIds': []
      },
      merchantList: []
    }
  },
  computed: {
    edit () {
      return this.entity['ID']
    },
    user () {
      return this.$store.state.User
    }
  },
  validations: {
    entity: {
      Name: { required },
      // Status: { required },
      'Contact Name': { required },
      'Contact Email': { required },
      'Contact Phone': { required }
    }
  },
  methods: {
    async show () {
      if (!this.entity || !this.entity['ID']) {
        this.groupList()
        return
      }
      this.$q.loading.show()
      const resp = await request(`/admin/spendr/groups/${this.entity['ID']}/detail`)
      this.$q.loading.hide()
      if (resp.success) {
        this.entity = _.assignIn({}, this.entity, resp.data)
        this.merchantList = resp.data.merchantList || []
      }
    },
    async groupList () {
      this.merchantList = []
      this.$q.loading.show()
      const resp = await request(`/admin/spendr/groups/merchant-list`)
      this.$q.loading.hide()
      if (resp.success) {
        this.merchantList = resp.data || []
      }
    },
    async save () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }

      this.$q.loading.show({
        message: 'Saving changes...'
      })
      const resp = await request(`/admin/spendr/groups/save`, 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp.message)
        this.$root.$emit('reload-spendr-groups')
        this._hide()
      }
    }
  }
}
</script>

<style lang="scss">
.spendr-group-detail-dialog {
  .modal-content {
    width: 580px;
  }

  .modal-scroll {
    max-height: none;
  }

  .q-chip {
    border-radius: 2rem;
  }
}
</style>
