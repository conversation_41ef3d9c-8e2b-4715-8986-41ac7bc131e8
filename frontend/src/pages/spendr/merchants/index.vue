<template>
  <q-page id="spendr_merchants__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm top-statics-row top-statics-row-sm">
        <div class="col-sm-3 col-4">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-account" color="positive"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.total || 0 }}</div>
                  <div class="description">Total Merchants</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-4" v-if="!bankAdmin">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash-multiple" color="blue"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.totalBalance || 0 | moneyFormat }}</div>
                  <div class="description">Total Active Balances</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-4" v-if="!bankAdmin">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash-multiple" color="warning"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.averageBalance || 0 | moneyFormat }}</div>
                  <div class="description">Average Active Balance</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
               :columns="filteredVisibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-right">
<!--          <q-btn-->
<!--                 label="Reencrypt cards"-->
<!--                 color="blue"-->
<!--                 @click="encryptData"-->
<!--                 class="btn-sm mr-8" no-caps></q-btn>-->
          <!-- <q-btn v-if="masterAdmin"
                 color="warning"
                 label="Create Location Balance Info"
                 @click="createLocationBalanceInfo"
                 class="btn-sm mr-8"
                 no-caps></q-btn> -->
          <q-btn v-if="!bankAdmin && !spendrEmployee"
                 icon="mdi-account-group-outline"
                 color="positive"
                 label="Create merchants"
                 @click="add"
                 class="btn-sm mr-8"
                 no-caps></q-btn>
          <q-btn v-if="!bankAdmin && !spendrCustomerSupport"
                 icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>
          <q-btn-dropdown v-if="!bankAdmin && !spendrCustomerSupport"
                          class="btn-sm mr-8"
                          no-caps
                          color="grey-2"
                          text-color="dark"
                          label="Export To Braze">
            <q-list link>
              <q-item v-close-overlay @click.native="downloadBraze('Admins')">
                <q-item-main>Export Admins</q-item-main>
              </q-item>
              <q-item v-close-overlay @click.native="downloadBraze('Employees')">
                <q-item-main>Export Employees</q-item-main>
              </q-item>
            </q-list>
          </q-btn-dropdown>
          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Status'">
              <q-chip class="font-12"
                      :class="statusClass(props.row['Status'])">
                {{ props.row['Status'] }}
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Actions'">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          v-if="!bankAdmin && !spendrAccountant && !spendrCustomerSupport"
                          @click.native="edit(props.row)">
                    <q-item-main>Edit</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="view(props.row)">
                    <q-item-main>View Profile</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="!spendrAccountant"
                          @click.native="viewOnboardApplication(props.row)">
                    <q-item-main>View Onboard Application</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="!bankAdmin && !spendrAccountant &&!spendrCustomerSupport &&
                            props.row['Status'] === 'Pending'"
                          @click.native="sendToBank(props.row)">
                    <q-item-main>Send to Bank</q-item-main>
                  </q-item>
                  <!-- <q-item v-close-overlay
                          v-if="masterAdmin && props.row['Status'] === 'Approved'"
                          @click.native="withdraw(props.row)">
                    <q-item-main>Withdraw</q-item-main>
                  </q-item> -->
                  <q-item v-close-overlay
                          v-if="masterAdmin && props.row['Status'] === 'Approved' && props.row['needManuallyLoad']"
                          @click.native="manuallyLoad(props.row)">
                    <q-item-main>Manually Load</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="(masterAdmin || agentAdmin || spendrCompliance) && props.row['canConfigTip']"
                          @click.native="configTipping(props.row)">
                    <q-item-main>Config Tipping</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="(masterAdmin || agentAdmin || spendrCompliance) && props.row['Status'] === 'Approved' && props.row['canConfigBrazePush']"
                          @click.native="configPush(props.row)">
                    <q-item-main>Config Braze Pushed</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="changeStatus(props.row, 'Active')"
                          v-if="!bankAdmin &&
                           !spendrEmployee &&
                           ['Inactive', 'Archived'].includes(props.row['Status']) && props.row['canChangeStatus']"
                  >
                    <q-item-main>Change to Active</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="changeStatus(props.row, 'Inactive')"
                          v-if="!bankAdmin && !spendrEmployee && props.row['Status'] !== 'Inactive' && props.row['canChangeStatus']">
                    <q-item-main>Change to Inactive</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="changeStatus(props.row, 'Archived')"
                          v-if="!bankAdmin && !spendrEmployee && props.row['Status'] !== 'Archived' && props.row['canChangeStatus']">
                    <q-item-main>Change to Archived</q-item-main>
                  </q-item>
                  <q-item v-if="!bankAdmin && !spendrEmployee && !props.row['Last Login']"
                          v-close-overlay
                          @click.native="resendInvitation(props.row)">
                    <q-item-main>Resend Invitation Email</q-item-main>
                  </q-item>
                  <!-- <q-item v-if="masterAdmin"
                          v-close-overlay
                          @click.native="associateLocationAdmin(props.row)">
                    <q-item-main>Associate location admin</q-item-main>
                  </q-item> -->
                  <q-item v-close-overlay
                          v-if="masterAdmin || agentAdmin || spendrCompliance || spendrCustomerSupport"
                          @click.native="$c.loginAs(props.row['Admin ID'])">
                    <q-item-main>Login As</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>
    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
    <BatchExportDialog ref="exportBraze" :show-all="true"></BatchExportDialog>
    <DetailDialog></DetailDialog>
    <!-- <Withdraw></Withdraw> -->
    <ManuallyLoad></ManuallyLoad>
    <TippingConfig></TippingConfig>
    <ConfigBrazePushed></ConfigBrazePushed>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns, notify, notifySuccess, request, toSelectOptions } from '../../../common'
import SpendrPageMixin from '../../../mixins/spendr/SpendrPageMixin'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'
import MexMemberMixin from '../../../mixins/mex/MexMemberMixin'
import DetailDialog from './detail'
// import Withdraw from './withdraw'
import ManuallyLoad from './manuallyLoad'
import TippingConfig from './tippingConfig'
import ConfigBrazePushed from './brazePushed'

export default {
  name: 'spendr-merchants',
  mixins: [
    SpendrPageMixin,
    MexMemberMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-spendr-merchants')
  ],
  components: {
    DetailDialog,
    // Withdraw,
    ManuallyLoad,
    TippingConfig,
    ConfigBrazePushed
  },
  data () {
    const columns = [
      'Merchant ID', 'Merchant Name', 'Merchant Type', 'Email',
      'Locations', 'Balance', 'Transactions', 'Group', 'Created At',
      'Status', 'Actions'
    ]
    if (this.isBankAdmin()) {
      columns.splice(columns.indexOf('Locations'), 1)
      columns.splice(columns.indexOf('Balance'), 1)
      columns.splice(columns.indexOf('Transactions'), 1)
    }
    return {
      title: 'Merchants',
      requestUrl: `/admin/spendr/merchants/list`,
      downloadUrl: `/admin/spendr/merchants/export`,
      downloadBrazeUrl: '/admin/spendr/merchants/export-braze',
      columns: generateColumns(columns),
      filterOptions: [
        {
          value: 'filter[m.id=]',
          label: 'Merchant ID'
        },
        {
          value: 'filter[m.name=]',
          label: 'Merchant Name'
        },
        {
          value: 'filter[m.businessType=]',
          label: 'Merchant Type',
          options: toSelectOptions([
            'Cannabis', 'Non-Cannabis'
          ])
        },
        {
          value: 'filter[u.email=]',
          label: 'Email'
        },
        {
          value: 'filter[m.onboardingStatus=]',
          label: 'Status',
          options: toSelectOptions([
            'Pending', 'Approved', 'Denied', 'Inactive', 'Archived'
          ])
        }
      ],
      freezeColumn: -1,
      freezeColumnRight: 2,
      autoLoad: true,
      showEncrypt: false
    }
  },
  methods: {
    downloadBraze (type) {
      const data = this.mergeQuerySortParams(this.pagination)
      data.type = type
      data.query_type = 'all'
      this.$refs.exportBraze.show(this.downloadBrazeUrl, data)
    },
    add () {
      this.$root.$emit('show-spendr-merchant-detail-dialog')
    },
    edit (row) {
      this.$root.$emit('show-spendr-merchant-detail-dialog', row)
    },
    view (row) {
      window.open(`/admin#/h/spendr/merchant/${row['Merchant ID']}/profile/${row['Admin ID']}`, '_blank')
    },
    viewOnboardApplication (row) {
      window.open(`/admin#/h/spendr/merchant/${row['Merchant ID']}/onboard`, '_blank')
    },
    sendToBank (row) {
      this.$q.dialog({
        title: 'Confirm',
        message: 'Are you sure you want to send it to the bank?',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await request(`/admin/spendr/merchants/${row['Merchant ID']}/send-to-bank`)
        this.$q.loading.hide()
        if (resp.success) {
          notifySuccess(resp)
          this.reload()
        }
      }).catch(() => {})
    },
    async changeStatus (row, status) {
      this.$q.loading.show()
      const resp = await this.c.request(`/admin/spendr/merchants/${row['Merchant ID']}/toggle-status`, 'post', {
        Status: status
      })
      this.$q.loading.hide()
      if (resp.success) {
        this.reload()
      }
    },
    async resendInvitation (row) {
      this.$q.loading.show()
      const resp = await this.c.request(`/admin/spendr/merchants/${row['Admin ID']}/resend-invitation`, 'post')
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp)
      }
    },
    // async encryptData () {
    //   this.$q.loading.show()
    //   const resp = await this.c.request('/admin/spendr/merchants/encrypt-data', 'post')
    //   this.$q.loading.hide()
    //   if (resp.success) {
    //     notify(resp)
    //   }
    // },
    // withdraw (row) {
    //   this.$root.$emit('show-spendr-merchant-withdraw-dialog', row)
    // },
    manuallyLoad (row) {
      this.$root.$emit('show-spendr-merchant-manually-load-dialog', row)
    },
    configTipping (row) {
      this.$root.$emit('show-spendr-merchant-tipping-dialog', {
        'Merchant ID': row['Merchant ID']
      })
    },
    configPush (row) {
      this.$root.$emit('show-spendr-merchant-braze-pushed-dialog', {
        'Merchant ID': row['Merchant ID'],
        'Subscribed': row['Employee Subscribed'] ? row['Employee Subscribed'] : [],
        'Roles': this.quick.employeeRoles ? this.quick.employeeRoles : []
      })
    }
    // async createLocationBalanceInfo () {
    //   this.$q.loading.show()
    //   const resp = await this.c.request(`/admin/spendr/merchants/create-balance-info`, 'post')
    //   this.$q.loading.hide()
    //   if (resp.success) {
    //     notifySuccess(resp)
    //     this.reload()
    //   }
    // },
    // async associateLocationAdmin (row) {
    //   this.$q.loading.show()
    //   const resp = await this.c.request(`/admin/spendr/merchants/${row['Merchant ID']}/associate-location-admin`, 'post')
    //   this.$q.loading.hide()
    //   if (resp.success) {
    //     notifySuccess(resp)
    //     this.reload()
    //   }
    // }
  }
}
</script>
