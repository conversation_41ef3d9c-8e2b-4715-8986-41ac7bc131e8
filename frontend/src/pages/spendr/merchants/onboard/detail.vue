<template>
  <q-card class="spendr-merchant-onboard-detail q-card-lg shadow-lg">
    <q-card-title>
      <div class="font-18 mb-2">Business Information</div>
      <div class="font-12 normal text-dark" v-if="!reviewing">Please fill in the information below about your merchant.</div>
    </q-card-title>
    <q-card-main>
      <div class="row gutter-form mt--10 mb-5">
        <div class="col-sm-6">
          <q-input float-label="Company Name" autocomplete="no"
                   :readonly="readonly"
                   :error="$v.entity['Merchant Name'].$error"
                   @input="$v.entity['Merchant Name'].$touch"
                   v-model="entity['Merchant Name']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Doing Business As" autocomplete="no"
                   :readonly="readonly"
                   :error="$v.entity['Doing Business As'].$error"
                   @input="$v.entity['Doing Business As'].$touch"
                   v-model="entity['Doing Business As']"></q-input>
        </div>
        <div class="col-sm-12">
          <q-input float-label="Business Street Address" autocomplete="no"
                   :readonly="readonly"
                   :error="$v.entity['Business Street Address'].$error"
                   @input="$v.entity['Business Street Address'].$touch"
                   v-model="entity['Business Street Address']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="City"
                   autocomplete="no"
                   :readonly="readonly"
                   :error="$v.entity['City'].$error"
                   @blur="$v.entity['City'].$touch"
                   v-model="entity['City']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Postal Code"
                   autocomplete="no"
                   :readonly="readonly"
                   :error="$v.entity['Postal Code'].$error"
                   @blur="$v.entity['Postal Code'].$touch"
                   v-model="entity['Postal Code']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-select float-label="Country"
                    autocomplete="no"
                    :options="countries"
                    filter
                    autofocus-filter
                    :readonly="readonly"
                    :error="$v.entity['CountryId'].$error"
                    @blur="$v.entity['CountryId'].$touch"
                    v-model="entity['CountryId']"></q-select>
        </div>
        <div class="col-sm-6">
          <q-select float-label="State / Province"
                    autocomplete="no"
                    :options="states"
                    filter
                    autofocus-filter
                    :readonly="readonly"
                    :error="$v.entity['State'].$error"
                    :before="stateBefore"
                    @blur="$v.entity['State'].$touch"
                    v-model="entity['State']"></q-select>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Email" autocomplete="no"
                   :readonly="entity.id !== undefined || readonly"
                   :error="$v.entity['Email'].$error"
                   @input="$v.entity['Email'].$touch"
                   v-model="entity['Email']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Phone Number" autocomplete="no"
                   :readonly="readonly"
                   :error="$v.entity['Phone'].$error"
                   @input="$v.entity['Phone'].$touch"
                   v-model="entity['Phone']"></q-input>
        </div>
<!--        <div class="col-sm-6">-->
<!--          <q-input float-label="Tax ID/EIN" autocomplete="no"-->
<!--                   :readonly="readonly"-->
<!--                   :error="$v.entity['Tax ID'].$error"-->
<!--                   @input="$v.entity['Tax ID'].$touch"-->
<!--                   v-model="entity['Tax ID']"></q-input>-->
<!--        </div>-->
<!--        <div class="col-sm-6">-->
<!--          <q-input float-label="EIN" autocomplete="no"-->
<!--                   :readonly="readonly"-->
<!--                   :error="$v.entity['EIN'].$error"-->
<!--                   @input="$v.entity['EIN'].$touch"-->
<!--                   v-model="entity['EIN']"></q-input>-->
<!--        </div>-->
        <div class="col-sm-6">
          <q-input float-label="Average Ticket Estimate ($)"
                   autocomplete="no"
                   type="number"
                   prefix="$"
                   :readonly="readonly"
                   :error="$v.entity['Average Ticket Estimate Amount'].$error"
                   @input="$v.entity['Average Ticket Estimate Amount'].$touch"
                   v-model="entity['Average Ticket Estimate Amount']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Monthly Ticket Estimate (Count)"
                   autocomplete="no"
                   type="number"
                   :readonly="readonly"
                   :error="$v.entity['Monthly Ticket Estimate Count'].$error"
                   @input="$v.entity['Monthly Ticket Estimate Count'].$touch"
                   v-model="entity['Monthly Ticket Estimate Count']"></q-input>
        </div>
      </div>
      <q-btn label="Next"
             v-if="!readonly"
             no-caps class="mt-20 wp-100"
             color="primary"
             @click="save" />
    </q-card-main>
  </q-card>
</template>

<script>
import {
  notifyForm,
  notify,
  commonUserEntityValidator,
  request
} from '../../../../common'
import MexStateListMixin from '../../../../mixins/mex/MexStateListMixin'
import SpendrMerchantOnboardMixin from '../../../../mixins/spendr/SpendrMerchantOnboardMixin'

export default {
  name: 'spendr-merchant-onboard-detail',
  mixins: [
    MexStateListMixin,
    SpendrMerchantOnboardMixin
  ],
  props: {
    reviewing: {
      type: Boolean,
      default: false
    },
    profilePage: {
      type: Boolean,
      default: false
    },
    recaptchaKey: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      entity: {}
    }
  },
  validations: commonUserEntityValidator([
    'Merchant Name', 'Doing Business As', 'Business Street Address',
    'City', 'Postal Code', 'CountryId', 'State',
    'Average Ticket Estimate Amount', 'Monthly Ticket Estimate Count'
  ]),
  methods: {
    save () {
      if (this.profilePage) {
        this.saveAction(this)
      } else {
        const _this = this
        // this.$refs.recaptcha.execute()
        window.grecaptcha.ready(function () {
          window.grecaptcha.execute(
            this.recaptchaKey,
            { action: 'spendr_merchant_onboard_save' }
          ).then(function (token) {
            _this.entity.recaptcha = token
            _this.saveAction(_this)
          }).catch(function (e) {
            console.log('reCAPTCHA Error', 'Failed to load reCAPTCHA. Please refresh the page and try again.')
          })
        })
      }
    },
    async saveAction (_this) {
      if (_this.$v.$invalid) {
        _this.$v.$touch()
        return notifyForm()
      }
      _this.$q.loading.show({ message: 'Saving...' })
      const resp = await request(`/spendr/merchant/onboard/detail`, 'post', _this.entity)
      _this.$q.loading.hide()
      if (resp.success) {
        notify(resp.message)
        _this.$emit('done', resp.data)
      }
    }
  }
}
</script>

<style lang="scss">
.spendr-merchant-onboard-detail {
  max-width: 550px;
}
</style>
