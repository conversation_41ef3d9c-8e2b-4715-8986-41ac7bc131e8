<template>
  <q-card class="spendr-merchant-onboard-bank q-card-lg shadow-lg">
    <q-card-title>
      <div class="font-18 mb-2">Connect Bank Account</div>
      <div class="font-12 normal text-dark"><PERSON><PERSON><PERSON> uses Plaid to link your bank for deposits & withdraws.</div>
      <div class="font-12 normal text-warning mt-15" v-if="merchant.pendingCard">
        You have bank account which is pending micro-deposits verification. Click on the 'Verify Manual Card' in 1-2 business days to complete linking to Spendr.
      </div>
    </q-card-title>
    <q-card-main class="relative">
      <q-list no-border v-if="banks && banks.length">
        <q-item v-for="(c, i) in banks" :key="i" class="pl-0 pr-0 mb-5">
          <q-item-main>
            <div class="card-details">
              <div>
                <span class="label">Bank Name:</span>
                <span class="value ">{{ c.bankName }}</span>
              </div>
              <div class="mt-5">
                <span class="label">Account Number:</span>
                <span class="value">{{ c.accountNum }}</span>
              </div>
              <div class="mt-5">
                <span class="label">Routing Number:</span>
                <span class="value">{{ c.routing ? c.routing : '-'}}</span>
              </div>
              <div class="mt-5" v-if="c.bindLocations && c.bindLocations.length > 0">
                <span class="label">Related Locations:</span>
                <span class="value location" v-for="(location, j) in c.bindLocations" :key="j">
                  {{ location.name }}
                </span>
              </div>
            </div>
          </q-item-main>
          <q-item-side v-if="!spendrAdmin && !spendrLogin">
            <q-btn v-if="c.type && c.type === 'Plaid' && !c.manualLink && !bankReadonly"
                   color="primary" class="mr-10"
                   size="sm" no-caps
                   label="Relink"
                   @click="relinkCard(c)"
            ></q-btn>
            <q-btn color="grey-3"
                   text-color="tertiary"
                   size="sm" no-caps
                   @click="inactiveCard(c, i)"
                   v-if="!bankReadonly"
                   label="Make Inactive"></q-btn>
            <q-btn v-if="!bankReadonly"
                   color="positive" class="ml-10"
                   size="sm" no-caps
                   @click="bindLocation(c)"
                   label="Related Locations"></q-btn>
          </q-item-side>
        </q-item>
      </q-list>
      <plaid-link ref="plainLink" v-if="linkToken"
                  v-bind="{
            env,
            product,
            webhook,
            token: linkToken,
            onSuccess,
            onEvent,
            onLoad
          }"
      >{{ linkText }}</plaid-link>
      <q-btn v-if="!spendrAdmin && !spendrLogin && !linkToken"
             :label="linkText"
             no-caps
             :disable="bankReadonly"
             class="mt-20"
             color="primary"
             @click="changeLinkedBank(true)" />
      <q-btn v-if="!merchant.accountNum && !banks.length"
             label="Set Up Bank Details Later"
             no-caps
             :disable="bankReadonly"
             class="mt-20 wp-100"
             color="primary"
             @click="$emit('skip')" />
    </q-card-main>
    <BindLocation @done="getActiveCards"></BindLocation>
  </q-card>
</template>

<script>
import { notify, notifySuccess, request } from '../../../../../common'
import copy from 'copy-to-clipboard'
import SpendrPageMixin from '../../../../../mixins/spendr/SpendrMerchantOnboardMixin'
import PlaidLink from 'vue-plaid-link'
import BindLocation from './bind-location'

export default {
  name: 'spendr-merchant-onboard-plain-bank',
  mixins: [
    SpendrPageMixin
  ],
  props: {
    merchant: {
      type: Object,
      required: true
    }
  },
  components: {
    PlaidLink,
    BindLocation
  },
  data () {
    return {
      entity: {},
      keyword: null,
      banks: [],
      relinkId: null,
      resetBank: false,
      env: 'sandbox',
      product: ['auth'],
      webhook: null,
      linkToken: null
    }
  },
  computed: {
    linkText () {
      if (this.merchant.pendingCard) {
        return 'Verify Manual Card'
      }
      return 'Add Bank Account'
    }
  },
  methods: {
    async init () {
      if (
        (
          this.isSpendrEmployee() ||
          this.isSpendrEmployeeRoleLogin()
        ) &&
        !this.resetBank
      ) {
        return
      }
      await this.getLinkToken()
    },
    async getActiveCards () {
      this.$q.loading.show()
      const resp = await request(`/admin/spendr/merchant/active-cards`, 'get', {
        'merchantId': this.$route.params.id
      })
      this.$q.loading.hide()
      if (resp.success) {
        this.banks = resp.data
      }
    },
    relinkCard (card) {
      this.relinkId = card.id
      this.changeLinkedBank(true)
    },
    async inactiveCard (card, idx) {
      this.$q.dialog({
        title: 'Confirm',
        message: 'Are you sure that you want to inactive this card?',
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await request(`/admin/spendr/merchant/inactive-card/${card.id}`, 'post')
        this.$q.loading.hide()
        if (resp.success) {
          this.banks.splice(idx, 1)
        }
      }).catch(() => {})
    },
    bindLocation (card) {
      this.$root.$emit('show-spendr-card-bind-location-dialog', {
        merchantId: this.merchant.id,
        cardId: card.id,
        bindLocationIds: card.bindLocationIds
      })
    },
    // If merchant.token is null. Call this function to get
    async getLinkToken () {
      const resp = await request(`/spendr/merchant/${this.merchant.id}/plaid/create-link-token`, 'get', {
        'bankId': this.relinkId
      })
      if (resp.success) {
        if (resp.data && resp.data.link_token) {
          this.env = resp.data.env
          this.product = resp.data.product
          this.linkToken = resp.data.link_token
        }
      } else {
        this.linkToken = null
      }
    },
    async onLoad () {
      console.log('onload')
      this.$refs.plainLink.handleOnClick()
    },
    async onEvent (event, data) {
      console.log(event, data)
    },
    // Handle account data after selected bank account
    async onSuccess (token, data) {
      console.log('Success Token', token)
      console.log('Success Data', data)
      this.linkToken = null
      if (this.relinkId) {
        this.relinkId = null
        notifySuccess('Relink successfully')
        return
      }
      this.$q.loading.show({
        message: 'Setup bank ...'
      })
      const resp = await request(`/spendr/merchant/${this.merchant.id}/plaid/exchange-access-token`, 'post', {
        publicToken: token,
        data: data
      })
      this.$q.loading.hide()
      if (resp.success) {
        this.resetBank = false
        this.merchant.pendingCard = resp.data.pendingCard
        if (resp.data.pendingCard) {
          notifySuccess(resp.message)
          return
        }
        this.banks.push(...resp.data)
        if (this.resetBank) {
          this.$emit('done')
        }
      } else {
        notify(resp.message)
      }
    },
    copy (content) {
      copy(content)
      notify('Copied to the clipboard')
    },
    enterManually () {
      this.$root.$emit('show-spendr-enter-manually-dialog')
    },
    bankEnterManuallyDone () {
      this.$emit('done')
    },
    changeLinkedBank (reset) {
      this.resetBank = reset
      setTimeout(() => {
        this.init()
      }, 300)
    }
  },
  mounted () {
    this.getActiveCards()
  }
}
</script>

<style lang="scss">
.spendr-merchant-onboard-bank {
  max-width: 680px !important;
  .detail-list {
    display: block;
    padding-left: 20%;
    p {
      font-size: 16px;
      font-weight: 600;
      color: #191d3d;
      margin: 0;
      text-align: left;
    }

    .title {
      text-align: left;
      color: rgba($color: #191d3d, $alpha: 0.5);
    }
  }
  #container-fastlink {
    min-height: 300px;
  }
  .banks {
    margin: 20px 0;
    padding: 30px;
    max-height: 430px;
    overflow-x: hidden;
    overflow-y: auto;
  }

  .bank {
    border: 1px solid white;
    border-radius: 12px;
    box-shadow: 0 0 5px 5px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    padding: 5px 15px;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      border: 1px solid var(--span-color-primary);
      box-shadow: 0 0 5px 5px rgba(0, 0, 0, 0.15);
    }
  }

  .bank-icon-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 60px;
    position: relative;
    overflow: hidden;

    img {
      height: 90px;
      position: absolute;
      overflow: hidden;
      top: -15px;
      left: 50%;
      transform: translate(-50%, 0);

      &.small {
        height: 50px;
        top: 0;
      }
    }
  }
  .enter-manually {
    font-weight: 500;
  }
  .plaid-link-button {
    border-radius: 10px;
    padding: 10px 16px;
    border-width: 0;
    color: white;
    background: var(--q-color-primary);
  }
  .card-details {
    .label {
      font-weight: 500;
      font-size: 13px;
      margin-right: 8px;
      text-align: right;
      display: inline-block;
      width: 120px;
    }
    .value {
      font-size: 13px;
      &.location {
        background: #f5f5f5;
        border-radius: 8px;
        padding: 2px 5px;
        margin-right: 4px;
        display: inline-block;
        margin-bottom: 4px;
      }
    }
  }
}
</style>
