<template>
  <div class="row gutter-form mt--10 mb-5 spendr-merchant-onboard-rep">
    <div class="col-sm-12">
      <q-input float-label="First Name" autocomplete="no"
               :readonly="readonly"
               :error="$v.entity['First Name'].$error"
               @blur="$v.entity['First Name'].$touch"
               v-model="entity['First Name']"></q-input>
    </div>
    <div class="col-sm-12">
      <q-input float-label="Last Name" autocomplete="no"
               :readonly="readonly"
               :error="$v.entity['Last Name'].$error"
               @blur="$v.entity['Last Name'].$touch"
               v-model="entity['Last Name']"></q-input>
    </div>
    <div class="col-sm-12">
      <q-input float-label="Email" autocomplete="no"
               :readonly="readonly"
               :error="$v.entity['Email'].$error"
               @blur="$v.entity['Email'].$touch"
               v-model="entity['Email']"></q-input>
    </div>
    <div class="col-sm-12">
      <q-input float-label="Phone Number" autocomplete="no"
               :readonly="readonly"
               :error="$v.entity['Phone'].$error"
               @blur="$v.entity['Phone'].$touch"
               v-model="entity['Phone']"></q-input>
    </div>
    <div class="col-sm-12">
      <q-datetime float-label="Date of Birth"
                  autocomplete="no"
                  :readonly="readonly"
                  type="date"
                  format="MM/DD/YYYY"
                  :error="$v.entity['Date of Birth'].$error"
                  @change="$v.entity['Date of Birth'].$touch"
                  v-model="entity['Date of Birth']"></q-datetime>
    </div>
    <div class="col-sm-12">
      <q-input float-label="Occupation" autocomplete="no"
               :readonly="readonly"
               :error="$v.entity['Occupation'].$error"
               @input="$v.entity['Occupation'].$touch"
               v-model="entity['Occupation']"></q-input>
    </div>
    <div class="col-sm-12">
      <q-select float-label="Choose a Role"
                autocomplete="no"
                :readonly="readonly"
                :options="companyRoleList"
                :error="$v.entity['Company Role'].$error"
                @change="$v.entity['Company Role'].$touch"
                v-model="entity['Company Role']">
      </q-select>
    </div>
    <div class="col-sm-12" v-if="entity['Company Role'] === 'Owner'">
      <q-input float-label="% of ownership" autocomplete="no"
               type="number"
               :readonly="readonly"
               v-model="entity['Ownership Percentage']"></q-input>
    </div>
    <div class="col-sm-12">
      <q-input float-label="Last 4 of SSN" autocomplete="no"
               :readonly="readonly"
               :error="$v.entity['Last 4 of SSN'].$error"
               @input="$v.entity['Last 4 of SSN'].$touch"
               v-model="entity['Last 4 of SSN']"></q-input>
    </div>
    <div class="col-sm-12">
      <q-input float-label="Street Address" autocomplete="no"
               :readonly="readonly"
               :error="$v.entity['Street Address'].$error"
               @input="$v.entity['Street Address'].$touch"
               v-model="entity['Street Address']"></q-input>
    </div>
    <div class="col-sm-12">
      <q-input float-label="City"
               autocomplete="no"
               :readonly="readonly"
               :error="$v.entity['City'].$error"
               @blur="$v.entity['City'].$touch"
               v-model="entity['City']"></q-input>
    </div>
    <div class="col-sm-12">
      <q-input float-label="Postal Code"
               autocomplete="no"
               :readonly="readonly"
               :error="$v.entity['Postal Code'].$error"
               @blur="$v.entity['Postal Code'].$touch"
               v-model="entity['Postal Code']"></q-input>
    </div>
    <div class="col-sm-12">
      <q-select float-label="Country"
                autocomplete="no"
                :options="countries"
                filter
                autofocus-filter
                :readonly="readonly"
                :error="$v.entity['CountryId'].$error"
                @blur="$v.entity['CountryId'].$touch"
                v-model="entity['CountryId']"></q-select>
    </div>
    <div class="col-sm-12">
      <q-select float-label="State / Province"
                autocomplete="no"
                :options="states"
                filter
                autofocus-filter
                :readonly="readonly"
                :error="$v.entity['State'].$error"
                :before="stateBefore"
                @blur="$v.entity['State'].$touch"
                v-model="entity['State']"></q-select>
    </div>
    <div class="col-sm-12">
      <q-select float-label="Select a Document"
                autocomplete="no"
                :options="$c.docTypes"
                filter
                autofocus-filter
                :readonly="readonly"
                :error="$v.entity['Doc'].$error"
                @blur="$v.entity['Doc'].$touch"
                v-model="entity['Doc']"></q-select>
      <div class="upload-area mt-20"
           @click="readonly ? $c.noop() : selectFile()"
           :class="{selected: file || entity.file}">
        <q-icon name="mdi-file-document-outline"
                v-if="!entity.file || !$c.isImage(entity.file.name)"></q-icon>
        <template v-if="entity.file">
          <template v-if="$c.isImage(entity.file.name)">
            <a :href="entity.file.url" target="_blank">
              <img :src="entity.file.url" :alt="entity.file.name">
            </a>
            <a href="javascript:" class="delete-btn"
               v-if="!readonly"
               @click.stop="entity.file = null">
              <q-icon name="mdi-close-circle-outline"
                      class="font-20"
                      color="negative"></q-icon>
              <q-tooltip>Remove and reselect</q-tooltip>
            </a>
          </template>
          <template v-else>
            <div class="mt-10">Uploaded file:</div>
            <div class="font-13 text-blue">
              <a :href="entity.file.url" target="_blank">{{ entity.file.name }}</a>
              <a href="javascript:"
                 v-if="!readonly"
                 class="ml-5 link"
                 @click.stop="entity.file = null">
                <q-icon name="mdi-close-circle-outline"
                        class="font-20"
                        color="negative"></q-icon>
                <q-tooltip>Remove and reselect</q-tooltip>
              </a>
            </div>
          </template>
        </template>
        <template v-else-if="file">
          <div class="mt-10">Selected file:</div>
          <div class="font-13 text-blue">
            <span>{{ file.name }}</span>
            <a href="javascript:"
               v-if="!readonly"
               class="ml-5 link"
               @click.stop="file = null">
              <q-icon name="mdi-close-circle-outline"
                      class="font-20"
                      color="negative"></q-icon>
              <q-tooltip>Remove and reselect</q-tooltip>
            </a>
          </div>
        </template>
        <template v-else>
          <div class="font-13 text-dark mt-10">Click here or drag a file here to upload a photo of the document.</div>
          <div class="font-12 text-dark">(Accepted file formats: .JPG, .PNG, .PDF)</div>
        </template>
      </div>
      <input type="file"
             class="hide"
             :accept="fileAccept"
             ref="file"
             @change="selectedFile">

      <q-btn color="red" class="w-120 mt-20"
             outline no-caps
             @click="remove"
             icon="mdi-close"
             v-if="!readonly"
             label="Delete"></q-btn>
    </div>
  </div>
</template>

<script>
import { commonUserEntityValidator, notifyResponse, uploadAttachment } from '../../../../common'
import MexStateListMixin from '../../../../mixins/mex/MexStateListMixin'
import DndUploadFileMixin from '../../../../mixins/DndUploadFileMixin'
import SpendrMerchantOnboardMixin from '../../../../mixins/spendr/SpendrMerchantOnboardMixin'

export default {
  name: 'spendr-merchant-onboard-rep',
  mixins: [
    MexStateListMixin,
    DndUploadFileMixin,
    SpendrMerchantOnboardMixin
  ],
  props: {
    merchant: {
      type: Object,
      required: true
    },
    entity: {
      type: Object,
      required: true
    },
    reviewing: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      acceptFileTypes: [
        '.jpeg',
        '.jpg',
        '.png',
        '.pdf'
      ],
      companyRoleList: [
        {
          value: 'Owner',
          label: 'Owner'
        },
        {
          value: 'Managing Member/President',
          label: 'Managing Member/President'
        }
      ]
    }
  },
  validations: commonUserEntityValidator([
    'First Name', 'Last Name', 'Date of Birth', 'Occupation', 'Company Role',
    'Last 4 of SSN', 'Street Address', 'City', 'Postal Code', 'CountryId', 'State', 'Doc'
  ]),
  methods: {
    isFileUploaded () {
      return this.entity.file
    },
    isValid () {
      if (this.$v.$invalid) {
        return false
      }
      if (this.entity['Company Role'] === 'Owner' && !this.entity['Ownership Percentage']) {
        return false
      }
      if (this.entity['Last 4 of SSN'].toString().trim().length !== 4) {
        return false
      }
      if (!this.file && !this.entity.file) {
        return false
      }
      return true
    },
    async getData () {
      if (this.file) {
        this.$q.loading.show({ message: `Uploading the document of ${this.entity['First Name']}...` })
        const resp = await uploadAttachment(this.file, 'spendr_merchant_rep')
        this.$q.loading.hide()
        if (typeof resp === 'string') {
          notifyResponse(`Failed to upload: ${resp}`)
          return null
        }
        this.entity.file = resp
        this.file = null
      }
      return this.entity
    },
    remove () {
      this.$q.dialog({
        title: 'Confirm',
        message: 'Are you sure that you want to delete this representative?',
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.$emit('delete')
      }).catch(() => {})
    }
  }
}
</script>

<style lang="scss">
.spendr-merchant-onboard-rep {
  position: relative;
  padding-bottom: 40px;
  margin-bottom: 20px !important;

  &:after {
    content: '';
    width: calc(100% + 33px);
    height: 1px;
    background: #ccc;
    position: absolute;
    bottom: 0;
    left: -8px
  }

  &:last-child {
    margin-bottom: 0 !important;

    &:after {
      content: none;
    }
  }
}
</style>
