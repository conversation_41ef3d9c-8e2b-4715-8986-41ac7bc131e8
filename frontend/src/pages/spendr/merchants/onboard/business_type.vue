<template>
  <q-dialog class="spendr-merchant-onboard-biz-type-dialog"
            v-model="visible">
    <template slot="title">
      <div class="font-16 mb-2">Business Type</div>
      <div class="font-12 normal">Select your business type below. Different business type has different document requirements.</div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <a href="javascript:" class="type-btn" v-for="t in types"
         @click="select(t.label)"
         :key="t.label">
        <q-icon :name="t.icon"></q-icon> {{ t.label }}
      </a>
    </template>
    <template slot="buttons"><div></div></template>
  </q-dialog>
</template>

<script>
import { notify, request } from '../../../../common'
import Singleton from '../../../../mixins/Singleton'

export default {
  name: 'spendr-merchant-biz-type-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      types: [
        {
          label: 'Cannabis',
          icon: 'mdi-cannabis'
        },
        {
          label: 'Non-Cannabis',
          icon: 'mdi-cart-outline'
        }
      ]
    }
  },
  methods: {
    async select (type) {
      // if (type === this.entity['Merchant Type']) {
      //   this._hide()
      //   return
      // }
      this.$q.loading.show({
        message: 'Saving as "' + type + '"...'
      })
      const resp = await request(`/spendr/merchant/${this.entity.id}/onboard/business-type`, 'post', {
        type
      })
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp.message)
        this._hideAndEmit('change-spendr-merchant-biz-type', resp.data)
      }
    }
  }
}
</script>

<style lang="scss">
.spendr-merchant-onboard-biz-type-dialog {
  .modal-content {
    max-width: 440px !important;
  }

  .type-btn {
    display: block;
    padding: 24px;
    border: 1px solid #ccc;
    border-radius: 8px;
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 500;
    text-decoration: none;
    color: #333 !important;

    .q-icon {
      font-size: 25px;
      margin-top: -3px;
      margin-right: 5px;
    }

    &:hover, &.active {
      border-color: var(--span-color-primary);
      color: var(--span-color-primary) !important;
      box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
    }

    &:last-child {
      margin-bottom: 5px;
    }
  }
}
</style>
