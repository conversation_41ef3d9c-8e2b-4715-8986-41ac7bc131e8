<template>
  <q-card class="spendr-merchant-docs q-card-lg shadow-lg">
    <q-card-title>
      <div class="font-18 mb-2">Upload Documents</div>
      <div class="font-12 normal text-dark" v-if="!reviewing">Upload all applicable documents. (Accepted file types: .PDF, .xlsx, .PNG, .JPG)</div>
    </q-card-title>
    <q-card-main>
      <div class="mv-10 border-1 p-10 rd-10">
        Merchant Type: <strong>{{ merchant['Merchant Type'] }}</strong>
        <a href="javascript:" @click="changeBizType"
           v-if="!readonly && !merchant['isActive']"
           class="ml-10 text-primary">Change</a>
      </div>

      <q-list no-border v-if="merchant.docs">
        <q-item v-for="(d, i) in filteredDocs" :key="i" class="pl-0 pr-0">
          <q-item-main>
            <q-icon :name="merchant.docs[d.label] && (merchant.docs[d.label].length > 0)
             ? 'mdi-check-circle' : 'mdi-circle-outline'"
                    class="font-18"
                    v-if="!readonly"
                    :color="merchant.docs[d.label] && (merchant.docs[d.label].length > 0)
                     ? 'positive' : 'light'"></q-icon>
            <span class="ml-5">{{ d.label }}</span>
            <span v-if="d.optional" class="ml-5 text-faded">(optional)</span>
            <q-icon name="mdi-information-outline"
                    color="blue" size="sm" class="ml-5">
              <q-tooltip>{{ d.desc }}</q-tooltip>
            </q-icon>
            <a href="javascript:" class="ml-5 font-12" v-if="d.template"
               @click="download(d.template)">Download Template</a>
            <div v-if="d.multiple && merchant.docs[d.label] && (merchant.docs[d.label].length > 0)" class="mul-doc-list">
              <div v-for="(md, j) in merchant.docs[d.label]" :key="j" class="mt-5">
                <a v-if="md" :href="md.url" target="_blank" class="text-blue">{{ md.name }}</a>
                <q-icon name="close"
                        v-if="md"
                        color="blue"
                        size="sm"
                        class="ml-5 cursor-pointer"
                        @click.native="remove(d, md.id)"
                > </q-icon>
              </div>
            </div>
          </q-item-main>
          <q-item-side>
            <template v-if="merchant.docs[d.label] && (merchant.docs[d.label].length > 0)">
              <span v-if="!d.multiple">
                <span v-for="(md, j) in merchant.docs[d.label]" :key="j">
                  <q-btn color="dark"
                         type="a"
                         :href="md.url"
                         target="_blank"
                         size="sm" no-caps
                         label="View"></q-btn>
                </span>
              </span>
              <q-btn color="negative" class="ml-5"
                     size="sm" no-caps
                     @click="remove(d)"
                     v-if="!readonly"
                     label="Remove"></q-btn>
            </template>

            <q-btn color="primary"
                   class="ml-5"
                   v-if="!readonly && (d.multiple || !(merchant.docs[d.label] && (merchant.docs[d.label].length > 0)))"
                   @click="upload(d)"
                   size="sm" no-caps
                   label="Upload"></q-btn>
          </q-item-side>
        </q-item>
      </q-list>

      <input type="file"
             class="hide"
             :accept="fileAccept"
             ref="file"
             @change="selectedFile">

      <div class="row gutter-sm mt-10" v-if="!readonly">
        <div class="col-4">
          <q-btn label="Back" outline
                 no-caps class="wp-100"
                 color="primary"
                 @click="$emit('back')" />
        </div>
        <div class="col-8">
          <q-btn label="Next"
                 no-caps class="wp-100"
                 color="primary"
                 @click="$emit('done')" />
        </div>
      </div>
    </q-card-main>

    <BusinessType></BusinessType>
  </q-card>
</template>

<script>
import BusinessType from './business_type'
import { EventHandlerMixin, isEmpty, notify, notifyResponse, request, uploadAttachment } from '../../../../common'
import DndUploadFileMixin from '../../../../mixins/DndUploadFileMixin'
import SpendrMerchantOnboardMixin from '../../../../mixins/spendr/SpendrMerchantOnboardMixin'

export default {
  name: 'spendr-merchant-docs',
  mixins: [
    EventHandlerMixin('change-spendr-merchant-biz-type', 'updateBizType'),
    DndUploadFileMixin,
    SpendrMerchantOnboardMixin
  ],
  components: {
    BusinessType
  },
  props: {
    merchant: {
      type: Object,
      required: true
    },
    reviewing: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      docs: [
        {
          type: 'All',
          label: 'Articles of Incorporation/Organization',
          desc: 'State recorded copy of organizational documents'
        },
        {
          type: 'All',
          label: 'EIN Verification',
          desc: 'IRS document verifying Tax ID or EIN number'
        },
        // {
        //   type: 'Cannabis',
        //   label: 'Beneficial Ownership Worksheet',
        //   desc: 'Spreadsheet listing owners and obtain Driver’s',
        //   template: 'Beneficial Ownership Worksheet.xlsx'
        // },
        // {
        //   type: 'Non-Cannabis',
        //   label: 'Beneficial Ownership Worksheet',
        //   desc: 'Complete spreadsheet listing beneficial owners (15% or more)',
        //   template: 'Beneficial Ownership Worksheet.xlsx'
        // },
        {
          type: 'Cannabis',
          label: 'Bank Statements',
          desc: '3 months (all pages)',
          multiple: true
        },
        // {
        //   type: 'Cannabis',
        //   label: 'Voided Check Copy',
        //   desc: 'Copy of voided check'
        // },
        {
          type: 'Cannabis',
          label: 'Certificate of Operation or License',
          desc: 'State/Government Issued Cannabis License/Certificate Authorizing Retail Operation'
        },
        {
          type: 'Cannabis',
          label: 'Financial Institution Disclosure',
          desc: 'Document verifying merchant has disclosed the cannabis nature of their business to their bank',
          template: 'Merchant Attestation.pdf'
        },
        // {
        //   type: 'Cannabis',
        //   label: 'Most Recent Tax Return',
        //   desc: "Full Tax Return copy including any K-1's"
        // },
        // {
        //   type: 'Cannabis',
        //   label: 'Financial Statements',
        //   desc: 'Balance Sheet, Income Statement and Cashflow report for most recent year and quarter',
        //   multiple: true
        // },
        {
          type: 'All',
          label: 'Trade Name Filing (if applicable)',
          desc: 'State authorization showing name registration including DBA',
          optional: true
        },
        {
          type: 'All',
          label: 'Organizational Charts (if applicable)',
          desc: 'Document showing ownership structure if more than one entity is involved',
          optional: true
        }
        // {
        //   type: 'All',
        //   label: 'By-Laws/Operating Agreement',
        //   desc: 'Agreement between members outlining how the business is managed'
        // },
        // {
        //   type: 'Cannabis',
        //   label: 'Copies of MRB License(s)',
        //   desc: 'Copy of your state license'
        // },
        // {
        //   type: 'All',
        //   label: 'Fictitious Business Name Filing, if applicable',
        //   desc: 'State authorization showing name registration',
        //   optional: true
        // },
        // {
        //   type: 'All',
        //   label: 'Copies of Beneficial Owner(s) Identification',
        //   desc: 'Drivers License or Passport copy of any owner that owns 15% or more of the business'
        // },
        // {
        //   type: 'Cannabis',
        //   label: 'Certificate of Good Standing',
        //   desc: 'Certificate verifying the business is in good standing with the state'
        // },
        // {
        //   type: 'All',
        //   label: 'Organizational Charts',
        //   desc: 'Document showing ownership structure if more than one entity is involved'
        // },
        // {
        //   type: 'Cannabis',
        //   label: 'Disclosure to Financial Institution',
        //   desc: 'Bank signed document verifying account information and MRB certification',
        //   template: 'Attestation by Merchant vF.pdf'
        // },
        // {
        //   type: 'Cannabis',
        //   label: 'Photos of Operation',
        //   desc: 'Pictures of the inside and outside of the business'
        // }
      ],
      doc: null,
      acceptFileTypes: [
        '.jpeg',
        '.jpg',
        '.png',
        '.pdf',
        '.xlsx'
      ]
    }
  },
  computed: {
    filteredDocs () {
      const type = this.merchant ? this.merchant['Merchant Type'] : null
      if (!type) {
        return []
      }
      return this.docs.filter(item => {
        return item.type === 'All' || item.type === type
      })
    }
  },
  watch: {
    file () {
      if (!this.file) {
        return
      }
      this.doUpload(this.file)
    }
  },
  methods: {
    isFinished () {
      if (!this.filteredDocs.length) {
        return false
      }
      for (const d of this.filteredDocs) {
        if (d.optional) {
          continue
        }
        if (!this.merchant.docs[d.label]) {
          return false
        }
      }
      return true
    },
    changeBizType () {
      this.$root.$emit('show-spendr-merchant-biz-type-dialog', this.merchant)
    },
    updateBizType (merchant) {
      this.$emit('update', merchant)
    },
    init () {
      this.$nextTick(() => {
        if (!this.merchant['Merchant Type']) {
          this.merchant['Merchant Type'] = 'Cannabis'
          this.changeBizType()
        }
        if (!this.merchant.docs) {
          this.$set(this.merchant, 'docs', {})
        }
      })
    },
    upload (item) {
      this.doc = item
      this.selectFile()
    },
    async doUpload (file) {
      this.$q.loading.show({ message: `Uploading the document of "${this.doc.label}"...` })
      const resp = await uploadAttachment(file, 'spendr_merchant_doc')
      this.$q.loading.hide()
      this.file = null
      if (typeof resp === 'string') {
        notifyResponse(`Failed to upload: ${resp}. Please try again.`)
        return
      }
      this.$set(this.merchant.docs, this.doc.label, resp)
      return this.save(this.doc, resp)
    },
    async save (doc, file) {
      this.$q.loading.show({
        message: 'Saving...'
      })
      const resp = await request(`/spendr/merchant/${this.merchant.id}/onboard/docs`, 'post', {
        type: doc.label,
        file
      })
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp.message)
        this.$set(this.merchant.docs, doc.label, resp.data[doc.label])
        this.$emit('change')
      }
    },
    remove (doc, docId = null) {
      this.$q.dialog({
        title: 'Confirm',
        message: 'Are you sure that you want to delete this document?',
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.$q.loading.show({
          message: 'Saving...'
        })
        const resp = await request(`/spendr/merchant/${this.merchant.id}/onboard/docs/remove`, 'post', {
          type: doc.label,
          docId
        })
        this.$q.loading.hide()
        if (resp.success) {
          notify(resp.message)
          if (!isEmpty(resp.data[doc.label])) {
            this.$set(this.merchant.docs, doc.label, resp.data[doc.label])
          } else {
            this.$set(this.merchant.docs, doc.label, null)
          }
          this.$emit('change')
        }
      }).catch(() => {})
    },
    download (file) {
      location.href = `/download?path=static/spendr/template/${file}`
    }
  }
}
</script>

<style lang="scss">
.spendr-merchant-docs {
  max-width: 600px;

  .q-list {
    .q-item {
      padding: 10px 16px;
    }
  }
  .mul-doc-list {
    margin-left: 23px;
    margin-top: 5px;
  }
}
</style>
