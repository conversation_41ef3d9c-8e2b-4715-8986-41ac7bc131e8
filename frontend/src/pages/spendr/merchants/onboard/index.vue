<template>
  <div id="spendr-merchant-onboard"
       :class="{profile: profilePage}">
    <div class="logo">
      <img :src="logo"
           alt="">
    </div>

    <div class="mb-25 mt--10 font-16">
      <template v-if="reviewing">
        Reviewing merchant "<strong>{{ merchant['Merchant Name'] }}"</strong>
      </template>
      <template v-else-if="viewing">{{ merchant['Merchant Name'] }}</template>
    </div>

    <div class="steppers">
      <div class="stepper"
           v-for="(ss, s) in steps"
           v-show="!reviewing || s !== 'bank'"
           :class="{active: s === step, disabled: ss.enable === false}"
           @click="step = s"
           :key="s">
        <div class="text">
          {{ s === 'review' ? reviewLabel : ss.label }}
          <q-icon class="ml-3"
                  name="mdi-check"
                  color="primary"
                  v-if="ss.status === 'done'"></q-icon>
        </div>
        <div class="indicator"></div>
      </div>
    </div>

    <Detail v-show="step === 'detail'"
            :reviewing="reviewing"
            :recaptchaKey="recaptchaKey"
            :profilePage="profilePage"
            ref="detail"
            @done="detailDone"></Detail>
    <Email v-show="step === 'email'"
           :reviewing="reviewing"
           :recaptchaKey="recaptchaKey"
           :merchant="merchant"
           :profilePage="profilePage"
           @update="emailUpdate"></Email>

    <template v-if="profilePage">
      <Reps v-show="step === 'rep'"
            ref="rep"
            @done="repsDone"
            :reviewing="reviewing"
            :merchant="merchant"></Reps>
      <Docs v-show="step === 'docs'"
            ref="docs"
            @back="step = 'rep'"
            @update="merchantUpdate"
            @change="updateSteps"
            @done="step = 'bank'"
            :reviewing="reviewing"
            :merchant="merchant"></Docs>
      <Bank v-show="step === 'bank'"
            v-if="!reviewing"
            :merchant="merchant"
            @done="bankDone"
            @skip="bankDone"
            ref="bank"></Bank>

      <Review v-show="step === 'review'"
              :merchant="merchant"
              v-if="reviewing"
              @reviewed="init"></Review>
      <Submit v-show="step === 'review'"
              :can-submit="canSubmit"
              :merchant="merchant"
              v-else
              @done="merchantUpdate"></Submit>
    </template>
    <template v-if="recaptchaKey">
      <vue-recaptcha
              ref="recaptcha"
              size="invisible"
              :sitekey="recaptchaKey"
              :badge="'bottomleft'"
              :loadRecaptchaScript="true"
              :recaptchaHost="'www.recaptcha.net'"
              @verify="onVerify"
              @expired="onExpired"
              @error="onError"
      />
    </template>
  </div>
</template>

<script>
import SpendrPageMixin from '../../../../mixins/spendr/SpendrPageMixin'
import Detail from './detail'
import Email from './email'
import Reps from './reps'
import Docs from './docs'
// import Bank from './bank'
import Bank from './third-plaid/bank'
import Review from './review'
import Submit from './submit'
import { request } from '../../../../common'
import VueRecaptcha from 'vue-recaptcha'

export default {
  name: 'SpendrMerchantOnboard',
  mixins: [
    SpendrPageMixin
  ],
  components: {
    Detail,
    Email,
    Reps,
    Docs,
    Bank,
    Submit,
    Review,
    VueRecaptcha
  },
  data () {
    return {
      title: 'Merchant Onboarding Wizard',
      step: 'detail',
      merchant: {},
      steps: {
        detail: {
          label: 'Business Information', status: 'pending', enable: true
        },
        rep: {
          label: 'Representatives', status: 'pending', enable: false
        },
        docs: {
          label: 'Upload Documents', status: 'pending', enable: false
        },
        bank: {
          label: 'Bank Account', status: 'pending', enable: false
        },
        review: {
          label: 'Submit & Review', status: 'pending', enable: false
        }
      },
      initialized: false,
      recaptchaKey: null
    }
  },
  computed: {
    logo () {
      return this.$store.state.User.platformLogoUrl
    },
    profilePage () {
      return this.$route.fullPath.startsWith('/h/spendr/merchant/')
    },
    managing () {
      return this.$route.params.id
    },
    mode () {
      if (this.reviewing) {
        return 'reviewing'
      }
      if (this.viewing) {
        return 'viewing'
      }
      return 'editing'
    },
    viewing () {
      return this.managing && !this.bankAdmin
    },
    reviewing () {
      return this.managing && this.bankAdmin
    },
    id () {
      return this.$store.state.User.merchantId || this.$route.params.id
    },
    canSubmit () {
      return this.steps.detail.status === 'done' &&
        this.steps.rep.status === 'done' &&
        this.steps.docs.status === 'done' &&
        !this.isSpendrEmployeeRoleLogin()
    },
    reviewLabel () {
      if (this.merchant) {
        const status = this.merchant.Status
        if (status === 'Pending') {
          return 'Under review'
        }
        if (status !== 'Initial') {
          return 'Application ' + status
        }
        if (this.reviewing) {
          return 'Not Submitted'
        }
      }
      return this.steps.review.label
    }
    // initBusinessType () {
    //   console.log(this.$route.params, this.$route.params.businessType)
    //   return this.$route.params.businessType
    // }
  },
  watch: {
    step () {
      if (this.step === 'docs') {
        this.$refs.docs.init()
      }
      // if (this.step === 'bank') {
      //   this.$refs.bank.init()
      // }
    }
  },
  methods: {
    detailDone (merchant) {
      if (this.profilePage) {
        this.commonDone(merchant)
        this.step = 'rep'
      } else {
        this.merchant = merchant
        this.step = 'email'
      }
    },
    emailUpdate (merchant) {
      this.merchant = merchant
    },
    repsDone (merchant) {
      this.commonDone(merchant)
      this.step = 'docs'
    },
    merchantUpdate (merchant) {
      this.merchant = merchant
      this.updateSteps()
    },
    commonDone (merchant) {
      this.$refs.detail.entity = merchant
      this.merchantUpdate(merchant)
    },
    bankDone (merchant) {
      if (merchant && merchant.id) {
        this.commonDone(merchant)
      }
      this.step = 'review'
    },
    async init () {
      this.$q.loading.show()
      const resp = await request(`/spendr/merchant/${this.id}/onboard/data`)
      this.$q.loading.hide()
      if (resp.success) {
        this.commonDone(resp.data)
        this.merchant = resp.data
        this.$refs.detail.entity = resp.data
        this.updateSteps()

        if (this.reviewing) {
          this.title = `Reviewing "${this.merchant['Merchant Name']}"`
        } else if (this.viewing) {
          this.title = this.merchant['Merchant Name']
        }
      }
    },
    updateSteps () {
      this.$nextTick(() => {
        const merchant = this.merchant
        this.steps.detail.status = merchant.id ? 'done' : 'pending'
        this.steps.rep.status = (merchant.representatives && merchant.representatives.length) ? 'done' : 'pending'
        this.steps.docs.status = this.$refs.docs.isFinished() ? 'done' : 'pending'
        this.steps.bank.status = merchant.bank ? 'done' : 'pending'

        let reviewStatus = 'pending'
        if (merchant['Status'] === 'Approved') {
          reviewStatus = 'done'
        } else if (merchant['Status'] === 'Denied') {
          reviewStatus = 'failed'
        }
        this.steps.review.status = reviewStatus

        this.steps.rep.enable = !!merchant.id
        this.steps.docs.enable = !!merchant['Merchant Type']
        this.steps.bank.enable = !!merchant.id
        this.steps.review.enable = !!merchant.id

        if (this.initialized) {
          return
        }

        this.initialized = true
        if (this.$route.query && this.$route.query.step) {
          this.step = this.$route.query.step
        } else if (!this.managing) {
          this.step = 'detail'
          for (const step in this.steps) {
            if (this.steps.hasOwnProperty(step)) {
              if (this.steps[step].status !== 'done') {
                this.step = step
                break
              }
            }
          }
          if (this.step === 'detail' && merchant.Status === 'Approved') {
            this.step = 'review'
          }
        }
      })
    },
    async getRecaptchaKey () {
      this.$q.loading.show()
      const resp = await request(`/spendr/merchant/onboard/prepare`)
      this.$q.loading.hide()
      if (resp.success) {
        setTimeout(() => {
          this.recaptchaKey = resp.data
        }, 200)
      }
    },
    onVerify: async function (response) {
      // console.log('Verify: ' + response)
    },
    onExpired: function () {
      console.log('Expired')
    },
    onError: function () {
      console.log('Error')
    },
    resetRecaptcha () {
      this.$refs.recaptcha.reset() // Direct call reset method
    }
  },
  mounted () {
    if (!this.profilePage && !this.recaptchaKey) {
      this.getRecaptchaKey()
    }

    if (this.id) {
      this.init()
      // this.$router.push(`/h/spendr/merchant/${this.id}/onboard`)
    }

    const user = this.$store.state.User
    if (user && user.merchantStatus) {
      this.$root.$emit('admin-hide-drawer')
    }
  }
}
</script>

<style lang="scss">
#spendr-merchant-onboard {
  margin: 80px auto;
  padding-bottom: 40px;
  text-align: center;

  .steppers {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    margin: 0 auto 20px;

    .stepper {
      display: flex;
      flex-direction: column;
      padding: 0 10px;
      margin-bottom: 10px;
      cursor: pointer;

      .text {
        color: #999;
        font-size: 13px;
      }

      .indicator {
        margin-top: 4px;
        width: 100%;
        height: 6px;
        border-radius: 6px;
        background: var(--span-color-primary);
        opacity: 0.25;
      }

      &.active {
        .text {
          color: #333;
        }

        .indicator {
          opacity: 1;
        }
      }

      &:hover {
        .text {
          color: #555;
        }

        .indicator {
          opacity: 0.7;
        }
      }

      &.disabled {
        pointer-events: none;
        opacity: 0.35 !important;
      }
    }
  }

  &.profile {
    margin-top: 30px;
  }

  > .logo {
    margin-bottom: 22px;

    img {
      max-width: 250px;
      height: 50px;
    }
  }

  > div {
    margin-left: auto;
    margin-right: auto;
  }
}
</style>
