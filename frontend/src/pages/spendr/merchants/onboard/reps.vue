<template>
  <q-card class="spendr-merchant-onboard-representative q-card-lg shadow-lg">
    <q-card-title>
      <div class="font-18 mb-2">Representatives & Beneficial Owners</div>
      <div class="font-12 normal text-dark" v-if="!reviewing">
        Please fill in the information below for anyone who owns 15% or more of the Company AND the individual who is the designated President or Managing Member of the Company.
      </div>
    </q-card-title>
    <q-card-main>
      <Rep v-for="(rep, i) in reps" :key="i"
           ref="reps"
           @delete="reps.splice(i, 1)"
           :reviewing="reviewing"
           :merchant="merchant"
           :entity="rep"></Rep>

      <q-btn label="Add another Representative" outline
             v-if="!readonly"
             no-caps class="mt-20 wp-100"
             color="purple" icon="mdi-plus"
             @click="add" />
      <q-btn label="Save and Next"
             v-if="!readonly"
             no-caps class="mt-20 wp-100"
             color="primary"
             @click="save" />
    </q-card-main>
  </q-card>
</template>

<script>
import { notify, notifyForm, request } from '../../../../common'
import Rep from './rep'
import SpendrMerchantOnboardMixin from '../../../../mixins/spendr/SpendrMerchantOnboardMixin'

export default {
  name: 'spendr-merchant-onboard-reps',
  mixins: [
    SpendrMerchantOnboardMixin
  ],
  components: {
    Rep
  },
  props: {
    merchant: {
      type: Object,
      required: true
    },
    reviewing: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      reps: []
    }
  },
  watch: {
    merchant () {
      this.init()
    }
  },
  methods: {
    async save () {
      const reps = []
      for (const ref of this.$refs.reps) {
        if (ref) {
          if (!ref.isValid()) {
            ref.$v.$touch()
            return notifyForm()
          }
          try {
            const data = await ref.getData()
            if (!data) {
              return
            }
            reps.push(data)
          } catch (e) {
            return
          }
        }
      }
      if (reps.length <= 0) {
        return notifyForm('Please add at least one representative!')
      }
      this.$q.loading.show({
        message: 'Saving...'
      })
      const resp = await request(`/spendr/merchant/${this.merchant.id}/onboard/representative`, 'post', {
        reps
      })
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp.message)
        this.$emit('done', resp.data)
      }
    },
    add () {
      this.reps.push({})
    },
    init () {
      this.$nextTick(() => {
        if (this.merchant) {
          this.reps = this.merchant.representatives || []
          if (this.reps.length <= 0) {
            this.add()
          }
        }
      })
    }
  }
}
</script>

<style lang="scss">
.spendr-merchant-onboard-representative {
  max-width: 420px;
}
</style>
