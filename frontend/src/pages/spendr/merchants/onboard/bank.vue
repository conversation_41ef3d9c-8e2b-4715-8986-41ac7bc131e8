<template>
  <q-card class="spendr-merchant-onboard-bank q-card-lg shadow-lg">
    <q-card-title>
      <div class="font-18 mb-2">Connect Bank Account</div>
      <div class="font-12 normal text-dark"><PERSON><PERSON><PERSON> uses <PERSON><PERSON><PERSON> to link your bank for deposits & withdraws.</div>
    </q-card-title>
    <!--  <q-card-title v-else-if="bank">
      <div class="font-18 mb-2 bank-icon-title">
        <q-btn icon="mdi-arrow-left"
               @click="bank = null"
               round></q-btn>
        <img :src="bank.icon"
             alt="">
        <q-btn flat
               round
               disable></q-btn>
      </div>
      <div class="font-12 normal text-dark"><PERSON><PERSON><PERSON> uses <PERSON><PERSON><PERSON> to link your bank account.</div>
    </q-card-title>
    <q-card-title v-else>
      <div class="font-18 mb-2">Connect Bank Account</div>
      <div class="font-12 normal text-dark"><PERSON><PERSON><PERSON> uses <PERSON><PERSON><PERSON> to link your bank for deposits & withdraws.</div>
    </q-card-title>
    <q-card-main class="relative">
      <template v-if="manual">
        <q-input v-model="entity.routing"
                 class="mt-15 mb-30"
                 float-label="Routing Number"></q-input>
        <q-input v-model="entity.account"
                 class="mb-30"
                 float-label="Account Number"></q-input>
        <q-input v-model="entity.account2"
                 class="mb-30"
                 float-label="Confirm Account Number"></q-input>
        <q-btn label="Continue"
               color="primary"
               class="mb-15 wp-100"
               no-caps></q-btn>
      </template>
      <template v-else-if="bank">
        <q-input v-model="entity.username"
                 class="mt-15 mb-30"
                 float-label="Username"></q-input>
        <q-input v-model="entity.password"
                 class="mb-30"
                 float-label="Password"
                 type="password"></q-input>
        <q-btn label="Login"
               color="primary"
               class="mb-15 wp-100"
               no-caps></q-btn>
        <q-btn label="Manually Enter Information"
               outline
               class="mb-30 wp-100"
               @click="manual = true"
               color="primary"
               no-caps></q-btn>
      </template>

      <template v-if="!bank && !manual">
        <q-input v-model="keyword"
                 placeholder="Search for your institution"
                 :before="[{icon: 'mdi-magnify'}]"></q-input>

        <q-inner-loading :visible="loading"></q-inner-loading>

        <div class="banks">
          <div class="row gutter-md">
            <div class="col-6"
                 v-for="b in banks"
                 :key="b.id">
              <div class="bank"
                   @click="bank = b">
                <img :src="b.icon"
                     alt="">
              </div>
            </div>
          </div>
        </div>

        <div class="mv-15">
          My bank isn't here, <a href="javascript:"
             @click="manual = true">enter it manually</a>
        </div>

        <img src="/static/faas/img/bank_widget_yodlee.svg"
             alt="">
      </template>
      <template v-else>
        <div class="mv-15 text-faded font-12">
          By providing your credentials, we verify in real time that you own the account you want to link.
          We then use this information to establish a secure connection with your financial institution.
        </div>
      </template>

      <div class="mv-5 text-faded font-12">
        <q-icon name="mdi-shield-check"
                class="font-14"></q-icon>
        Transfer of your information is encrypted end-to-end
      </div>

      <div class="mv-5 text-faded font-12">
        <q-icon name="mdi-lock"
                class="font-14"></q-icon>
        Your credentials will never be shared with Spendr
      </div>

      <div class="mv-15 font-12">
        By continuing you agree to Yodlee's
        <a href="javascript:">Terms, Data Policy and Cookie Privacy</a>
      </div>

      <q-btn label="Set Up Bank Details Later"
             flat
             no-caps
             class="mt-20 wp-100"
             color="primary"
             @click="$emit('skip')" />
    </q-card-main> -->
    <q-card-main class="relative">
      <q-inner-loading :visible="loading"></q-inner-loading>
      <div v-if="(!merchant.accountNum || resetBank) && !readonly"
           id="container-fastlink">
      </div>
      <div v-if="merchant.accountNum && !resetBank"
           class="detail-list">
        <div class="detial-item">
          <p class="title">Bank Name:</p>
          <p><span>{{ merchant.bankName || '-' }}</span>
          </p>
        </div>
        <div class="detial-item">
          <p class="title">Routing Number:</p>
          <p><span ref="rounting">{{ merchant.routing || '-' }}</span>
            <q-icon v-if="merchant.routing"
                    @click.native="copy(user.routing)"
                    name="mdi-content-copy light-grey font-14 ml-10"> </q-icon>
          </p>
        </div>
        <div class="detail-item">
          <p class="title">Account Number:</p>
          <p><span ref="account">{{ merchant.accountNum || '-' }}</span>
            <q-icon v-if="merchant.accountNum"
                    @click.native="copy(merchant.accountNum)"
                    name="mdi-content-copy light-grey font-14 ml-10"> </q-icon>
          </p>
        </div>
      </div>
<!--      <q-btn v-if="merchant.accountNum && !resetBank"-->
<!--             label="Change Linked Bank"-->
<!--             no-caps-->
<!--             :disable="readonly"-->
<!--             class="mt-20"-->
<!--             color="primary"-->
<!--             @click="changeLinkedBank(true)" />-->
      <div class="enter-manually mt-10"
           v-if="(!merchant.accountNum || resetBank) && !readonly">
        My bank isn’t here,
        <span @click="enterManually()"
              class="text-primary cursor-pointer">
          enter it manually
        </span>
      </div>
      <q-btn v-if="!merchant.accountNum || resetBank"
             label="Set Up Bank Details Later"
             no-caps
             :disable="readonly"
             class="mt-20 wp-100"
             color="primary"
             @click="$emit('skip')" />
<!--      <q-btn v-if="merchant.accountNum && resetBank"-->
<!--             label="Back"-->
<!--             no-caps-->
<!--             :disable="readonly"-->
<!--             class="mt-20 wp-100"-->
<!--             color="grey-3"-->
<!--             text-color="dark"-->
<!--             @click="changeLinkedBank(false)" />-->
    </q-card-main>
    <EnterManually :merchant="merchant"
                   @done="bankEnterManuallyDone"></EnterManually>
  </q-card>
</template>

<script>
import { request, notify } from '../../../../common'
import copy from 'copy-to-clipboard'
import EnterManually from './enter_manually'
import SpendrPageMixin from '../../../../mixins/spendr/SpendrMerchantOnboardMixin'

export default {
  name: 'spendr-merchant-onboard-bank',
  mixins: [
    SpendrPageMixin
  ],
  props: {
    merchant: {
      type: Object,
      required: true
    }
  },
  components: {
    EnterManually
  },
  data () {
    return {
      entity: {},
      keyword: null,
      loading: false,
      banks: [],
      bank: null,
      manual: false,
      resetBank: false
    }
  },
  methods: {
    async init () {
      if (
        (
          this.merchant.accountNum ||
          this.isSpendrEmployee() ||
          this.isSpendrEmployeeRoleLogin()
        ) &&
        !this.resetBank
      ) {
        return
      }
      let that = this
      let str = 'https://development.node.yodlee.com/authenticate/USDevexPreProd2-13/?channelAppName=usdevexpreprod2'
      if (this.merchant.isLive) {
        str = 'https://production.node.yodlee.com/authenticate/USDevexProd2-290/?channelAppName=usdevexprod2'
      }
      window.fastlink.open({
        fastLinkURL: str,
        accessToken: 'Bearer ' + this.merchant.token + '',
        params: {
          userExperienceFlow: 'Verification'
        },
        forceIframe: true,
        onSuccess: function (data) {
          // this.$emit('done', data)
        },
        onError: function (data) {
          that.loading = false
          // Will use built in error display for now
          // window.location.href = 'https://leaflink.virtualcards.us/l/onboard/error'
        },
        onExit: function (data) {
          if (data.sites) {
            let res = null
            for (let i in data.sites) {
              if (data.sites[i].status === 'SUCCESS') {
                res = data.sites[i]
                break
              }
            }
            if (res) {
              console.log(res)
              that.save(res)
            }
          }
        },
        onEvent: function (data) {
        }
      }, 'container-fastlink')
      this.loading = false
    },
    async save (res) {
      this.$q.loading.show({
        message: 'Setup bank ...'
      })
      const resp = await request(`/spendr/merchant/${this.merchant.id}/onboard/bankSetup`, 'post', res)
      this.$q.loading.hide()
      if (resp.success) {
        if (this.resetBank) {
          this.resetBank = false
          this.merchant.accountNum = resp.data.accountNum
          this.merchant.routing = resp.data.routing
          this.merchant.bankName = resp.data.bankName
        } else {
          this.$emit('done', resp.data)
        }
      }
    },
    copy (content) {
      copy(content)
      notify('Copied to the clipboard')
    },
    enterManually () {
      this.$root.$emit('show-spendr-enter-manually-dialog')
    },
    bankEnterManuallyDone () {
      this.$emit('done')
    },
    changeLinkedBank (reset) {
      this.resetBank = reset
      setTimeout(() => {
        this.init()
      }, 300)
    }
  },
  mounted () {
    // this.init()
  }
}
</script>

<style lang="scss">
.spendr-merchant-onboard-bank {
  max-width: 500px;
  .detail-list {
    display: block;
    padding-left: 20%;
    p {
      font-size: 16px;
      font-weight: 600;
      color: #191d3d;
      margin: 0;
      text-align: left;
    }

    .title {
      text-align: left;
      color: rgba($color: #191d3d, $alpha: 0.5);
    }
  }
  #container-fastlink {
    min-height: 300px;
  }
  .banks {
    margin: 20px 0;
    padding: 30px;
    max-height: 430px;
    overflow-x: hidden;
    overflow-y: auto;
  }

  .bank {
    border: 1px solid white;
    border-radius: 12px;
    box-shadow: 0 0 5px 5px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    padding: 5px 15px;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      border: 1px solid var(--span-color-primary);
      box-shadow: 0 0 5px 5px rgba(0, 0, 0, 0.15);
    }
  }

  .bank-icon-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 60px;
    position: relative;
    overflow: hidden;

    img {
      height: 90px;
      position: absolute;
      overflow: hidden;
      top: -15px;
      left: 50%;
      transform: translate(-50%, 0);

      &.small {
        height: 50px;
        top: 0;
      }
    }
  }
  .enter-manually {
    font-weight: 500;
  }
}
</style>
