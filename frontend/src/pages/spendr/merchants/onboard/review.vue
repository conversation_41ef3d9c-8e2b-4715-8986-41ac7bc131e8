<template>
  <q-card class="spendr-merchant-onboard-review q-card-lg shadow-lg">
    <template v-if="merchant.Status === 'Initial'">
      <q-card-title>
        <div class="font-18 mb-2">Not Submitted</div>
      </q-card-title>
      <q-card-main class="relative">
        <div class="mv-10">This merchant's application has not been submitted yet.</div>
      </q-card-main>
    </template>
    <template v-else-if="merchant.Status === 'Pending'">
      <q-card-title>
        <q-icon name="mdi-note-edit-outline" color="primary" class="font-40 mb-15"></q-icon>
        <div class="font-18 mb-15">Reviewing</div>
        <div class="font-12 normal text-dark">Please check this merchant's information and approve or deny the application.</div>
      </q-card-title>
      <q-card-main class="relative">
        <q-btn label="Approve"
               no-caps class="mt-20 wp-100"
               color="positive"
               @click="done('approve')" />
        <div class="mv-30">- OR -</div>
        <q-input v-model="message"
                 placeholder="Enter the deny reason"></q-input>
        <q-btn label="Deny"
               no-caps class="mt-20 wp-100"
               color="negative"
               @click="done('deny')" />
      </q-card-main>
    </template>
    <template v-else-if="merchant.Status === 'Approved'">
      <q-card-title>
        <q-icon name="mdi-check-circle" color="positive" class="font-40 mb-15"></q-icon>
        <div class="font-18 mb-15">Application Approved</div>
      </q-card-title>
      <q-card-main class="relative">
        <div class="mv-10">This merchant application has been approved.</div>
      </q-card-main>
    </template>
    <template v-else-if="merchant.Status === 'Denied'">
      <q-card-title>
        <q-icon name="mdi-close-circle" color="negative" class="font-40 mb-15"></q-icon>
        <div class="font-18 mb-15">Application Denied</div>
        <div class="font-12 normal text-dark">The below issues need to be fixed by the merchant.</div>
      </q-card-title>
      <q-card-main class="relative">
        <div class="mv-10" v-html="merchant.deniedMessage"></div>
      </q-card-main>
    </template>
    <template v-else>
      <q-card-title>
        <div class="font-18 mb-2">Not Active</div>
      </q-card-title>
      <q-card-main class="relative">
        <div class="mv-10 text-negative">This merchant is not active.</div>
      </q-card-main>
    </template>
  </q-card>
</template>

<script>
import { notifyResponse, notifySuccess, request } from '../../../../common'

export default {
  name: 'spendr-merchant-onboard-review',
  props: {
    merchant: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      message: ''
    }
  },
  methods: {
    done (action) {
      if (action === 'deny' && !this.message) {
        return notifyResponse('Please enter the deny reason!')
      }
      this.$q.dialog({
        title: 'Confirm',
        message: `Are you sure that you want to ${action} this application?`,
        color: action === 'approve' ? 'positive' : 'negative',
        cancel: true
      }).then(async () => {
        this.submit(action)
      }).catch(() => { })
    },
    async submit (action) {
      this.$q.loading.show()
      const resp = await request(`/admin/spendr/merchants/${this.merchant.id}/review/${action}`, 'post', {
        message: this.message
      })
      this.$q.loading.hide()
      if (resp.success) {
        notifySuccess(resp.message)
        this.$emit('reviewed')
      }
    }
  }
}
</script>

<style lang="scss">
.spendr-merchant-onboard-review {
  max-width: 450px;
}
</style>
