<template>
  <q-dialog
    class="spendr-merchant-detail-dialog"
    v-model="visible"
    prevent-close
  >
    <template slot="title">
      <div class="font-16 mb-2">{{ edit ? 'Edit Merchant' : 'Create New Merchant' }}</div>
      <div class="font-12 normal text-dark">Please fill in the information below about your merchant.</div>
      <q-btn class="close" round flat @click="_hide" icon="close"/>
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5">
        <div class="col-sm-12" v-if="
        groupListOptions.length > 0
        ">
          <q-select
            float-label="Select Group"
            v-model="entity['Group ID']"
            :options="groupListOptions"
          />
        </div>
        <div class="col-sm-6">
          <q-input float-label="Company Name" autocomplete="no"
                   :error="$v.entity['Merchant Name'].$error"
                   @input="$v.entity['Merchant Name'].$touch"
                   v-model="entity['Merchant Name']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Doing Business As" autocomplete="no"
                   :error="$v.entity['Doing Business As'].$error"
                   @input="$v.entity['Doing Business As'].$touch"
                   v-model="entity['Doing Business As']"></q-input>
        </div>
        <div class="col-sm-12">
          <q-input float-label="Business Street Address" autocomplete="no"
                   :error="$v.entity['Business Street Address'].$error"
                   @input="$v.entity['Business Street Address'].$touch"
                   v-model="entity['Business Street Address']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="City"
                   autocomplete="no"
                   :error="$v.entity['City'].$error"
                   @blur="$v.entity['City'].$touch"
                   v-model="entity['City']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Postal Code"
                   autocomplete="no"
                   :error="$v.entity['Postal Code'].$error"
                   @blur="$v.entity['Postal Code'].$touch"
                   v-model="entity['Postal Code']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-select float-label="Country"
                    autocomplete="no"
                    :options="countries"
                    filter
                    autofocus-filter
                    :error="$v.entity['CountryId'].$error"
                    @blur="$v.entity['CountryId'].$touch"
                    v-model="entity['CountryId']"></q-select>
        </div>
        <div class="col-sm-6">
          <q-select float-label="State / Province"
                    autocomplete="no"
                    :options="states"
                    filter
                    autofocus-filter
                    :error="$v.entity['State'].$error"
                    :before="stateBefore"
                    @blur="$v.entity['State'].$touch"
                    v-model="entity['State']"></q-select>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Email" autocomplete="no"
                   :error="$v.entity['Email'].$error"
                   @input="$v.entity['Email'].$touch"
                   v-model="entity['Email']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Phone Number" autocomplete="no"
                   :error="$v.entity['Phone'].$error"
                   @input="$v.entity['Phone'].$touch"
                   v-model="entity['Phone']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Average Ticket Estimate ($)"
                   autocomplete="no"
                   type="number"
                   prefix="$"
                   :error="$v.entity['Average Ticket Estimate Amount'].$error"
                   @input="$v.entity['Average Ticket Estimate Amount'].$touch"
                   v-model="entity['Average Ticket Estimate Amount']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Monthly Ticket Estimate (Count)"
                   autocomplete="no"
                   type="number"
                   :error="$v.entity['Monthly Ticket Estimate Count'].$error"
                   @input="$v.entity['Monthly Ticket Estimate Count'].$touch"
                   v-model="entity['Monthly Ticket Estimate Count']"></q-input>
        </div>
<!--        <div class="col-sm-6">-->
<!--          <q-input float-label="Tax ID/EIN" autocomplete="no"-->
<!--                   :error="$v.entity['Tax ID'].$error"-->
<!--                   @input="$v.entity['Tax ID'].$touch"-->
<!--                   v-model="entity['Tax ID']"></q-input>-->
<!--        </div>-->
<!--        <div class="col-sm-6">-->
<!--          <q-input float-label="EIN" autocomplete="no"-->
<!--                   :error="$v.entity['EIN'].$error"-->
<!--                   @input="$v.entity['EIN'].$touch"-->
<!--                   v-model="entity['EIN']"></q-input>-->
<!--        </div>-->
      </div>
    </template>
    <template slot="buttons">
      <div class="row wp-100">
        <q-btn label="Cancel" no-caps
               color="grey-3"
               text-color="tertiary"
               @click="_hide" />
        <q-btn :label="edit ? 'Save Changes' : 'Create Merchant'"
               no-caps
               color="positive" class="main"
               @click="save" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { notifyForm, notify, commonUserEntityValidator, request } from '../../../common'
import MexStateListMixin from '../../../mixins/mex/MexStateListMixin'
import _ from 'lodash'

export default {
  name: 'spendr-merchant-detail-dialog',
  mixins: [
    Singleton,
    MexStateListMixin
  ],
  data () {
    return {
      defaultEntity: {
        'Merchant ID': 0,
        'Group ID': null
      },
      groupListOptions: []
    }
  },
  computed: {
    edit () {
      return this.entity['Merchant ID']
    },
    user () {
      return this.$store.state.User
    }
  },
  validations: commonUserEntityValidator([
    'Merchant Name', 'Doing Business As', 'Business Street Address',
    'City', 'Postal Code', 'CountryId', 'State',
    'Average Ticket Estimate Amount', 'Monthly Ticket Estimate Count'
  ]),
  methods: {
    async show () {
      if (!this.entity || !this.entity['Merchant ID']) {
        return
      }
      this.$q.loading.show()
      const resp = await request(`/admin/spendr/merchants/${this.entity['Merchant ID']}/detail`)
      this.$q.loading.hide()
      if (resp.success) {
        this.entity = _.assignIn({}, this.entity, resp.data)
        this.groupListOptions = resp.data.groupList
      }
    },
    async save () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }
      this.$q.loading.show({
        message: 'Saving changes...'
      })
      const resp = await request(`/admin/spendr/merchants/save`, 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp.message)
        this.$root.$emit('reload-spendr-merchants')
        this.$root.$emit('reload-spendr-merchants-profile')
        this._hide()
      }
    }
  }
}
</script>

<style lang="scss">
.spendr-merchant-detail-dialog {
  .modal-content {
    width: 580px;
  }

  .modal-scroll {
    max-height: none;
  }
}
</style>
