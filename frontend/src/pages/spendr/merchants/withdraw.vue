<template>
  <q-dialog class="spendr-merchant-withdraw-dialog"
            v-model="visible"
            prevent-close
  >
    <template slot="title">
      <div class="font-16 mb-2">
        Withdraw
      </div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5">
        <div class="col-sm-12">
          <q-input float-label="Amount"
                   autocomplete="no"
                   type="number"
                   :error="$v.entity['Amount'].$error"
                   @input="$v.entity['Amount'].$touch"
                   v-model="entity['Amount']"></q-input>
        </div>
        <div class="col-sm-12">
          <q-select float-label="Bank Account"
                    :options="bankOptions"
                    :error="$v.entity['bankId'].$error"
                    @input="$v.entity['bankId'].$touch"
                    v-model="entity['bankId']"></q-select>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <div class="row wp-100">
        <q-btn label="Continue"
               no-caps
               color="primary"
               class="main"
               @click="Withdraw" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { notify, notifyForm, notifyResponse, request } from '../../../common'
import { required } from 'vuelidate/lib/validators'

export default {
  name: 'spendr-merchant-withdraw-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      merchant: null,
      defaultEntity: {
        'Merchant ID': null,
        Amount: null,
        bankId: null
      },
      bankList: [],
      bankOptions: []
    }
  },
  validations: {
    entity: {
      Amount: {
        required
      },
      bankId: {
        required
      }
    }
  },
  methods: {
    async show (merchant) {
      this.merchant = merchant
      this.entity['Merchant ID'] = merchant.id
      await this.getActiveCards()
    },
    async getActiveCards () {
      this.$q.loading.show()
      const resp = await request(`/admin/spendr/merchant/active-cards`, 'get', { 'merchantId': this.merchant.id })
      this.$q.loading.hide()
      if (resp.success) {
        this.bankOptions = []
        this.bankList = resp.data
        resp.data.forEach(element => {
          this.bankOptions.push({
            label: element.bankName + ': ' + element.accountNum + (element.location ? ` (${element.location.name})` : ''),
            value: element.id
          })
        })
      }
    },
    async Withdraw () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        console.log(this.$v)
        return notifyForm()
      }
      this.$q.loading.show({
        message: 'Saving changes...'
      })
      const resp = await request(`/admin/spendr/merchants/${this.merchant.id}/withdraw`, 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp.message)
        this._hide()
      } else {
        notifyResponse(resp.message)
      }
    }
  }
}
</script>
<style lang="scss">
  .spendr-merchant-withdraw-dialog {
    .modal-content {
      width: 500px;
    }
    /*.avatar {*/
    /*  width: 50px;*/
    /*  height: 50px;*/
    /*  border-radius: 25px;*/
    /*  overflow: hidden;*/
    /*  margin: 0 auto;*/
    /*}*/
    .modal-scroll {
      max-height: none;
    }
  }
</style>
