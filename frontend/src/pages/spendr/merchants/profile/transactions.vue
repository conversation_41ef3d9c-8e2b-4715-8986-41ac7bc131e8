<template>
  <q-card class="wide-card">
    <q-card-main>
      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="inner-table m--15"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <div class="font-16 bold">
            <span class="va-m">Merchant Report</span>
          </div>
        </template>
        <template slot="top-right">
          <q-search icon="search" v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat round dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating class="dot" v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat round dense
                 color="faded"
                 @click="reload"
                 icon="refresh"/>
        </template>
        <q-tr slot="body" slot-scope="props" :props="props">
          <q-td v-for="col in props.cols" :key="col.field" :align="col.align">
            <template v-if="col.field === 'Status'">
              <q-chip class="font-12" :class="statusClass(props.row)">
                {{ props.row['Status'] }}
              </q-chip>
            </template>
            <template v-else>
              {{ _.get(props.row, col.field) }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination" slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </q-card-main>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>
  </q-card>
</template>

<script>
import MexPageMixin from '../../../../mixins/mex/MexPageMixin'
import ListPageMixin from '../../../../mixins/ListPageMixin'
import { generateColumns } from '../../../../common'

export default {
  name: 'mex-members-profile-transactions',
  mixins: [
    MexPageMixin,
    ListPageMixin
  ],
  props: {
    uid: {
      type: String
    }
  },
  data () {
    const columns = [
      'Member ID', 'First Name', 'Last Name', 'Email',
      'Location', 'Clerk ID', 'Amount'
    ]
    if (!this.isBankAdmin()) {
      columns.push('Spendr Fee')
    }
    if (this.isMasterAdmin()) {
      columns.push('Tern Fee')
    }
    for (const col of ['Merchant Balance Before', 'Merchant Balance After', 'Type', 'Status']) {
      columns.push(col)
    }
    return {
      title: 'Merchant Profile',
      requestUrl: `/admin/spendr/merchants/transactions/list`,
      columns: generateColumns(columns, []),
      pagination: {
        rowsNumber: 0,
        rowsPerPage: 5,
        page: 1
      },
      filterOptions: [
        {
          value: 'filter[t.id=]',
          label: 'Transaction ID'
        }
      ],
      autoLoad: true,
      force: false
    }
  },
  methods: {
    getOtherQueryParams () {
      return {
        member: this.uid
      }
    },
    statusClass (row) {
      const s = row['Status']
      return {
        Failed: 'negative',
        Refunded: 'blue',
        Completed: 'positive',
        Canceled: 'orange',
        Error: 'negative'
      }[s] || s
    }
  }
}
</script>
