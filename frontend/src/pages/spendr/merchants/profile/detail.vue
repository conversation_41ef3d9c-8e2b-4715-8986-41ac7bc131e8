<template>
  <q-card id="spendr-merchants-profile-detail" class="high-card">
    <q-card-title>
      <div class="row flex-center">
        <div class="font-18 bold">Merchant</div>
        <q-btn icon="mdi-refresh" outline
               @click="$emit('reload')"
               class="btn-mini ml-auto"></q-btn>
        <q-btn icon="mdi-pencil-outline" outline
               v-if="!bankAdmin && !spendrAccountant && !spendrCustomerSupport"
               @click="edit"
               class="btn-mini ml-5"></q-btn>
      </div>
    </q-card-title>
    <q-card-main>
      <div class="flex mv-15">
        <q-icon name="mdi-cart-outline" class="text-dark font-40 mr-15"></q-icon>
        <div class="col">
          <div class="bold font-20 mb-5">{{ entity['Merchant Name'] }}</div>
          <div class="flex wp-100">
            <q-chip class="font-12 ph-10" dense
                    :class="statusClass(entity['Status'])">
              {{ entity['Status'] }}
            </q-chip>

            <q-btn size="sm" color="primary"
                   v-if="!spendrAccountant"
                   class="ml-auto mr-10" no-caps dense
                   @click="viewDocs"
                   label="View Onboarding Docs"></q-btn>
            <q-btn v-if="!spendrCustomerSupport"
                   size="sm" color="primary"
                   no-caps dense
                   @click="downloadDocs"
                   label="Download ZIP"></q-btn>
          </div>
        </div>
      </div>

      <table class="common-fields-table mt-10 mb-auto">
        <tr v-for="(r, i) in fields" :key="i">
          <th>
            <q-icon :name="r.icon"></q-icon>
          </th>
          <th>
            {{ r.label }}
          </th>
          <td>
            {{ entity[r.label] }}
          </td>
        </tr>
      </table>
    </q-card-main>

    <DetailDialog></DetailDialog>
    <form ref="download"
          method="post"
          action="/admin/download">
      <input type="hidden"
             name="path"
             v-model="url">
    </form>
  </q-card>
</template>

<script>
import DetailDialog from '../detail'
import MexMemberMixin from '../../../../mixins/mex/MexMemberMixin'
import SpendrPageMixin from '../../../../mixins/spendr/SpendrPageMixin'
import { request } from '../../../../common'

export default {
  name: 'spendr-merchants-profile-detail',
  mixins: [
    MexMemberMixin,
    SpendrPageMixin
  ],
  components: {
    DetailDialog
  },
  props: {
    entity: {
      type: Object
    }
  },
  data () {
    return {
      fields: [
        { label: 'Number of Locations', icon: 'mdi-map-marker-multiple-outline' },
        { label: 'Purchases', icon: 'mdi-shopping-outline' },
        { label: 'Average Order', icon: 'mdi-order-bool-descending-variant' },
        { label: 'Merchant Balance', icon: 'mdi-cash' },
        { label: 'Merchant Manager Email', icon: 'mdi-email-outline' },
        { label: 'Merchant Manager Phone', icon: 'mdi-cellphone' }
      ],
      url: ''
    }
  },
  methods: {
    edit () {
      this.$root.$emit('show-spendr-merchant-detail-dialog', this.entity)
    },
    viewDocs () {
      window.open(`/admin#/h/spendr/merchant/${this.entity['Merchant ID']}/onboard?step=docs`, '_blank')
    },
    async downloadDocs () {
      this.$q.loading.show({
        message: 'Downloading file...'
      })
      const resp = await request(`/admin/spendr/merchants/${this.entity['Merchant ID']}/download-docs`, 'post')
      this.$q.loading.hide()
      if (resp.success) {
        this.download(resp.data)
      }
    },
    download (url) {
      if (!url) {
        return
      }
      this.url = url

      this.$nextTick(() => {
        this.$q.loading.show({
          message: 'Downloading file...'
        })
        this.$refs.download.submit()

        setTimeout(() => {
          this.$q.loading.hide()
          this.$root.$emit('reload-ach-file')
        }, 1000)
      })
    }
  }
}
</script>

<style lang="scss">
#spendr-merchants-profile-detail {
  .q-card-main {
    display: flex;
    flex-direction: column;
    justify-content: start;
    overflow: auto;
  }
}
</style>
