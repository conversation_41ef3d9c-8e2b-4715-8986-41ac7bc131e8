<template>
  <q-page id="spendr_merchants_profile_page" class="common-profile-page">
    <div class="row gutter-sm mt-0">
      <div class="col-sm-12 col-md-6">
        <DetailCard :entity="entity" @reload="reload"></DetailCard>
      </div>
      <div class="col-sm-12 col-md-6">
        <NotesCard :uid="uid"></NotesCard>
      </div>
      <div class="col-12">
        <TransactionsCard :uid="uid"></TransactionsCard>
      </div>
      <div class="col-12" v-if="!bankAdmin">
        <BalanceReportCard :adminId="merchantAdminId"></BalanceReportCard>
      </div>
    </div>
  </q-page>
</template>

<script>
import SpendrPageMixin from '../../../../mixins/spendr/SpendrPageMixin'
import DetailCard from './detail'
import NotesCard from './notes'
import TransactionsCard from './transactions'
import BalanceReportCard from './balanceReport'
import { EventHandlerMixin, notifySuccess, request } from '../../../../common'

export default {
  name: 'spendr-merchants-profile',
  mixins: [
    SpendrPageMixin,
    EventHandlerMixin('reload-spendr-merchants-profile')
  ],
  components: {
    DetailCard,
    NotesCard,
    TransactionsCard,
    BalanceReportCard
  },
  data () {
    return {
      title: 'Merchant Profile',
      entity: {}
    }
  },
  computed: {
    uid () {
      return this.$route.params.id
    },
    merchantAdminId () {
      return this.$route.params.adminId
    }
  },
  watch: {
    uid () {
      this.reload()
    },
    merchantAdminId () {
      this.reload()
    }
  },
  methods: {
    async reload (indicator = true, force = false) {
      indicator && this.$q.loading.show()
      const resp = await request(`/admin/spendr/merchants/${this.uid}/profile`, 'get', {
        force
      })
      indicator && this.$q.loading.hide()
      if (resp.success) {
        this.entity = resp.data

        if (force) {
          notifySuccess(resp)
        }
      }
    }
  },
  mounted () {
    this.reload()
  }
}
</script>
