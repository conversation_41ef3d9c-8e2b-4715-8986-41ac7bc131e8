<template>
  <q-dialog
      class="spendr-reward-setting-dialog"
      v-model="visible"
      prevent-close
  >
    <template slot="title">
      <div class="font-16 mb-2">{{ entity['ID'] ? 'Edit' : 'Create' }} Earn <PERSON>ward</div>
      <q-btn class="close" round flat @click="_hide" icon="close"/>
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5">
        <div class="col-sm-6">
          <q-select :disable="entity['ID'] > 0" stack-label="Reward Type" placeholder="Please select the reward type"
                    v-model="entity['Type']"
                    :options="types"
                    class="rewards-select"></q-select>
        </div>
        <div class="col-sm-6">
          <q-input stack-label="Reward Name" autocomplete="no" placeholder="Reward Name"
                   :error="$v.entity['Title'].$error"
                   @blur="$v.entity['Title'].$touch"
                   v-model="entity['Title']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input stack-label="Reward Amount" autocomplete="no"
                   type="number" placeholder="Reward amount" prefix="$"
                   :error="$v.entity['Amount'].$error"
                   @blur="$v.entity['Amount'].$touch"
                   v-model="entity['Amount']"></q-input>
        </div>
        <div  v-if="entity['Type'] !== 'Bank Link'" class="col-sm-6">
          <q-input stack-label="Total Spent Amount" autocomplete="no"
                   type="number" placeholder="Total Spend Amount" prefix="$"
                   :error="$v.entity['Total Amount'].$error"
                   @blur="$v.entity['Total Amount'].$touch"
                   v-model="entity['Total Amount']"></q-input>
        </div>
        <div v-if="entity['Type'] === 'Bank Link'" class="col-sm-6">
          <q-input stack-label="Reward Count Limit" autocomplete="no"
                   type="number" placeholder="Reward Count Limit"
                   :error="$v.entity['Count Limit'].$error"
                   @blur="$v.entity['Count Limit'].$touch"
                   v-model="entity['Count Limit']"></q-input>
        </div>
        <div v-if="entity['Type'] === 'Spend Reward'" class="col-sm-6">
          <q-select stack-label="Date Type" placeholder="Date type of reward"
                    :error="$v.entity['Date Type'].$error"
                    @blur="$v.entity['Date Type'].$touch"
                    v-model="entity['Date Type']"
                    :options="date_types"
                    class="rewards-select"></q-select>
        </div>
        <div v-if="entity['Date Type'] === 'date_range'" class="col-sm-6">
          <q-datetime stack-label="Start Date" type="datetime" placeholder="Start date of reward"
                      format="MM/DD/YYYY hh:mm A"
                      :error="$v.entity['Start Date'].$error"
                      @blur="$v.entity['Start Date'].$touch"
                      v-model="entity['Start Date']"></q-datetime>
        </div>
        <div v-if="entity['Date Type'] === 'date_range'" class="col-sm-6">
          <q-datetime stack-label="End Date" type="datetime" placeholder="End date of reward"
                      format="MM/DD/YYYY hh:mm A"
                      :min="entity['Begin Date']"
                      :error="$v.entity['End Date'].$error"
                      @blur="$v.entity['End Date'].$touch"
                      v-model="entity['End Date']"></q-datetime>
        </div>
      </div>
      <div  v-show="entity['Type'] === 'Spend Reward'" class="row mt-10">
        <div class="col-4" style="text-align: left; font-size: 16px;">Target Members</div>
        <div class="col-8">
          <div class="upload-area"
               @click="select"
               :class="{selected: file}">
            <q-icon name="mdi-file-document-outline"></q-icon>
            <template v-if="file">
              <div class="mt-10">Selected file:</div>
              <div class="font-13 text-blue">
                <span>{{ file.name }}</span>
                <a href="javascript:"
                   class="ml-5 link"
                   @click.stop="file = null">
                  <q-icon name="mdi-close-circle-outline"
                          class="font-20"
                          color="negative"></q-icon>
                  <q-tooltip>Remove and reselect</q-tooltip>
                </a>
              </div>
            </template>
            <template v-else>
              <div class="mt-10">Upload .xlsx</div>
              <div class="font-13 text-dark">(Drop or click here)</div>
            </template>
          </div>
          <input type="file"
                 class="hide"
                 accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                 ref="file"
                 @change="selectedFile">
        </div>
      </div>
    </template>
    <template slot="buttons">
      <div class="row wp-100">
        <q-btn label="Cancel" no-caps
               color="grey-3"
               text-color="tertiary"
               @click="_hide" />
        <q-btn label="Save"
               no-caps
               color="positive" class="main"
               @click="save" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { notifyForm, notify, request, uploadAttachment, notifyResponse } from '../../../common'
import _ from 'lodash'
import { helpers, integer, maxValue, minValue, required, requiredIf } from 'vuelidate/lib/validators'
import $ from 'jquery'

export default {
  name: 'spendr-reward-setting-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      defaultEntity: {
        'ID': 0
      },
      types: [
        { label: 'Bank Link Reward', value: 'Bank Link' },
        { label: 'Milestone Reward', value: 'First $100 Spend' },
        { label: 'Spend Reward', value: 'Spend Reward' }
      ],
      date_types: [
        // { label: 'Recent Days', value: 'recent_days' },
        { label: 'Per Month', value: 'per_month' },
        { label: 'Date Range', value: 'date_range' }
      ],
      file: null,
      attachment: null
    }
  },
  watch: {
    file () {
      this.attachment = null
    }
  },
  validations: function () {
    const amountValidator = helpers.regex('Up to two decimal places', /^(([1-9]{1}\d*)|(0{1}))(\.\d{1,2})?$/)
    return {
      entity: {
        'Type': { required },
        'Title': { required },
        'Amount': {
          minValue: minValue(0),
          maxValue: maxValue(500),
          amountValidator
        },
        'Total Amount': {
          minValue: minValue(0),
          maxValue: maxValue(10000),
          amountValidator
        },
        'Count Limit': {
          integer,
          minValue: minValue(0),
          maxValue: maxValue(1000000)
        },
        'Date Type': {
          required: requiredIf(function () {
            return this.entity['Type'] === 'Spend Reward'
          })
        },
        'Start Date': {
          required: requiredIf(function () {
            return this.entity['Date Type'] === 'date_range'
          })
        },
        'End Date': {
          required: requiredIf(function () {
            return this.entity['Date Type'] === 'date_range'
          })
        }
      }
    }
  },
  methods: {
    async show () {
      this.file = null
      if (!this.entity || !this.entity['User ID']) {
        return
      }
      this.$q.loading.show()
      const resp = await request(`/admin/spendr/members/${this.entity['User ID']}/detail`)
      this.$q.loading.hide()
      if (resp.success) {
        this.entity = _.assignIn({}, this.entity, resp.data)
      }
    },
    select () {
      if (this.file) {
        return
      }
      $(this.$refs.file).click()
    },
    selectedFile () {
      const input = this.$refs.file
      if (input.files.length) {
        this.file = input.files[0]
      }
      input.type = 'text'
      input.type = 'file'
    },
    getValidDragFile (e) {
      if (e.dataTransfer && e.dataTransfer.files && e.dataTransfer.files.length) {
        const file = e.dataTransfer.files[0]
        if (file && file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
          return file
        }
      }
      return null
    },
    setupDnd () {
      const area = $(this.$el).find('.upload-area'),
        dom = area.get(0)

      dom.addEventListener('dragover', e => {
        e.stopPropagation()
        e.preventDefault()
        area.addClass('dropping')
      }, false)

      dom.addEventListener('dragleave', e => {
        e.stopPropagation()
        e.preventDefault()
        area.removeClass('dropping')
      }, false)

      dom.addEventListener('drop', e => {
        e.stopPropagation()
        e.preventDefault()
        area.removeClass('dropping')

        const file = this.getValidDragFile(e)
        if (file) {
          this.file = file
        } else {
          notify('Please select a .xlsx file.', 'negative')
        }
      }, false)
    },
    async save () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }
      if (this.file && !this.attachment) {
        this.$q.loading.show({ message: 'Uploading...' })
        const category = 'spendr_reward_targets'
        const resp = await uploadAttachment(this.file, category)
        if (typeof resp === 'string') {
          this.$q.loading.hide()
          return notifyResponse(`Failed to upload: ${resp}`)
        }
        this.attachment = resp
      }
      this.$q.loading.show({
        message: 'Saving changes...'
      })
      const resp = await request(`/admin/spendr/earn-rewards/save`, 'post', {
        ...this.entity,
        attachment: this.attachment ? this.attachment.id : null
      })
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp.message)
        this.$root.$emit('reload-spendr-earn-rewards')
        this._hide()
      }
    }
  },
  mounted () {
    this.setupDnd()
  }
}
</script>

<style lang="scss">
.spendr-reward-setting-dialog {
  .modal-content {
    width: 600px;
  }
  .modal-scroll {
    max-height: none;
  }
  .gutter-form>div {
    padding-top: 40px !important;
  }
  .q-if-label-above {
    padding-bottom: 5px;
    font-size: 16px !important;
  }
  .rewards-select {
    min-width: 100px;
  }
}
</style>
