<template>
  <q-page id="spendr_earn_rewards__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
    </div>
    <div class="text-faded mt-10" v-html="subtitle"></div>
    <div class="page-content">
      <q-table :data="data"
               :columns="filteredVisibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <div class="font-16 bold">
            <span class="va-m">{{ title }}</span>
          </div>
        </template>
        <template slot="top-right">
          <q-btn v-if="!bankAdmin && !spendrEmployee"
                 icon="mdi-account-group-outline"
                 color="positive"
                 label="Create Reward Setting"
                 @click="add"
                 class="btn-sm mr-8"
                 no-caps></q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="(isMasterAdmin() || agentAdmin || spendrCompliance) &&
            col.field === 'Actions'">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-item v-close-overlay
                        class="cursor-pointer"
                        @click.native="edit(props.row)">
                  <q-item-main>Edit</q-item-main>
                </q-item>
                <q-item v-if="props.row['Attachment']" v-close-overlay
                        class="cursor-pointer"
                        @click.native="downloadTheFile(props.row['Attachment'])">
                  <q-item-main>Download Attachment</q-item-main>
                </q-item>
                <q-item v-close-overlay
                        class="cursor-pointer"
                        @click.native="changeToInactive(props.row)">
                  <q-item-main>Change to Inactive</q-item-main>
                </q-item>
              </q-btn-dropdown>
            </template>
            <template v-else-if="col.field === 'Reward Amount'">
              <div>{{ '$' + props.row['Amount'] }}</div>
            </template>
            <template v-else-if="col.field === 'Total Spent Amount'">
              <div>{{  '$' + props.row['Total Amount'] }}</div>
            </template>
            <template v-else-if="col.field === 'Date Type'">
              <div v-if="props.row['Date Type'] === 'per_month'">Per Month</div>
              <div v-else-if="props.row['Date Type'] === 'date_range'">Date Range</div>
              <div v-else></div>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>
        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>
    <RewardSetting></RewardSetting>
  </q-page>
</template>

<script>
import { EventHandlerMixin, generateColumns, notify, request } from '../../../common'
import ListPageMixin from '../../../mixins/ListPageMixin'
import SpendrPageMixin from '../../../mixins/spendr/SpendrPageMixin'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'
import RewardSetting from './rewardSetting'

export default {
  name: 'spendr-earn-rewards',
  mixins: [
    SpendrPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-spendr-earn-rewards')
  ],
  components: {
    RewardSetting
  },
  data () {
    return {
      title: 'Earn Rewards',
      subtitle: 'Bank Link Reward means when consumers firstly link bank card, they will earn the reward amount(There are quotas).<br/>' +
          'First Spend Reward means when consumers\' total spend amount over the total amount, they will earn the reward amount.<br/>' +
          'Spend Reward means when consumers\' total spend amount over the total amount from start date to end date, they will earn the reward amount.',
      requestUrl: `/admin/spendr/earn-rewards/list`,
      downloadUrl: `/admin/spendr/earn-rewards/export`,
      columns: generateColumns(['ID', 'Created At', 'Title', 'Reward Amount', 'Total Spent Amount', 'Count Limit', 'Date Type', 'Date Count', 'Start Date', 'End Date', 'Actions']),
      filterOptions: [
      ],
      freezeColumn: -1,
      freezeColumnRight: 1,
      autoLoad: true
    }
  },
  methods: {
    add () {
      this.$root.$emit('show-spendr-reward-setting-dialog')
    },
    edit (row) {
      this.$root.$emit('show-spendr-reward-setting-dialog', row)
    },
    downloadTheFile (hash) {
      if (!hash) {
        return
      }
      window.open(`/attachments/${hash}/download`, '_blank')
    },
    async changeToInactive (row) {
      this.$q.dialog({
        title: 'Confirm',
        message: 'Are you sure to inactive this earn reward, it will not show in list?',
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await request(`/admin/spendr/earn-rewards/${row['ID']}/toggle-status`, 'post', {
          'status': 'inactive'
        })
        this.$q.loading.hide()
        if (resp.success) {
          notify(resp)
          this.reload()
        }
      })
    }
  }
}
</script>

<style lang="scss">
#spendr_earn_rewards__index_page {
  .capitalize {
    text-transform: capitalize;
  }
}
</style>
