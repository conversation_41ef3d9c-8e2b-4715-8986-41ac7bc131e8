<template>
  <q-page id="spendr_plaid__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
    </div>
    <div class="page-content">
      <div class="row gutter-form">
        <template v-if="staging">
          <div class="col-sm-4 mt-20">
            <q-select float-label="Plaid Env"
                      autocomplete="no"
                      :options="envs"
                      v-model="plaidEnv">
            </q-select>
          </div>
          <div class="col-sm-4 offset-sm-2 mt-20">
            <q-btn label="Change Env" outline
                   no-caps class="wp-100"
                   color="primary"
                   @click="saveEnv" />
          </div>
        </template>
        <div class="col-sm-4 mt-20">
          <q-input stack-label="Manual Card Instant Amount"
                   autocomplete="no"
                   type="number"
                   placeholder="Instant amount"
                   prefix="$"
                   v-model="instantAmount"></q-input>
        </div>
        <div class="col-sm-4 offset-sm-2 mt-20">
          <q-btn label="Save Instant Amount" outline
                 no-caps class="wp-100"
                 color="primary"
                 @click="saveManualInstant" />
        </div>
        <div class="col-sm-4 mt-20">
          <q-input stack-label="Non-Manual Card Instant Amount"
                   autocomplete="no"
                   type="number"
                   placeholder="Instant amount"
                   prefix="$"
                   v-model="amount"></q-input>
        </div>
        <div class="col-sm-4 offset-sm-2 mt-20">
          <q-btn label="Save Instant Amount" outline
                 no-caps class="wp-100"
                 color="primary"
                 @click="saveInstant" />
        </div>
      </div>
    </div>
  </q-page>
</template>

<script>
import { isStaging, notify, request } from '../../../common'
import SpendrPageMixin from '../../../mixins/spendr/SpendrPageMixin'

export default {
  mixins: [
    SpendrPageMixin
  ],
  data () {
    return {
      title: 'Card Setting',
      plaidEnv: '',
      envs: [
        { label: 'sandbox', value: 'sandbox' },
        { label: 'development', value: 'development' },
        { label: 'production', value: 'production' }
      ],
      instantAmount: null,
      amount: null
    }
  },
  computed: {
    staging () {
      return isStaging()
    }
  },
  methods: {
    async getSetting () {
      this.$q.loading.show()
      const resp = await request(`/admin/spendr/setting`, 'get', {
        'type': 'Plaid'
      })
      this.$q.loading.hide()
      if (resp.success) {
        this.plaidEnv = resp.data['Plaid Env']
        this.instantAmount = resp.data['Manual Instant']
        this.amount = resp.data['Auth Instant']
      }
    },
    async saveEnv () {
      this.$q.loading.show({
        message: 'Saving...'
      })
      const resp = await request('/admin/spendr/update-setting', 'post', {
        'type': 'Plaid Env',
        'plaid_env': this.plaidEnv
      })
      this.$q.loading.hide()
      if (resp.success) {
        notify('Save successfully')
      }
    },
    async saveManualInstant () {
      this.$q.loading.show({
        message: 'Saving...'
      })
      const resp = await request('/admin/spendr/update-setting', 'post', {
        'type': 'Manual Instant',
        'Manual Instant': this.instantAmount
      })
      this.$q.loading.hide()
      if (resp.success) {
        notify('Save successfully')
      }
    },
    async saveInstant () {
      this.$q.loading.show({
        message: 'Saving...'
      })
      const resp = await request('/admin/spendr/update-setting', 'post', {
        'type': 'Auth Instant',
        'Auth Instant': this.amount
      })
      this.$q.loading.hide()
      if (resp.success) {
        notify('Save successfully')
      }
    }
  },
  created () {
    this.getSetting()
  }
}
</script>

<style lang="scss">
#spendr_plaid__index_page {
  max-width: 1000px !important;
  .tipping-select {
    min-width: 100px;
  }
  .q-if-label-above {
    padding-bottom: 5px;
    font-size: 16px !important;
  }
}
</style>
