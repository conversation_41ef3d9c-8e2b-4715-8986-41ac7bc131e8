<template>
  <q-page class="spendr__dashboard__index_page">
    <div class="page-header">
      <div class="title">Dashboard Overview</div>
<!--      <div class="title" v-if="isMasterAdmin()">-->
<!--        <q-btn color="blue"-->
<!--               label="Check Summary Data"-->
<!--               @click="checkSummaryData"-->
<!--               class="btn-sm ml-8"-->
<!--               no-caps></q-btn>-->
<!--      </div>-->
      <div class="fun-group">
        <q-select v-if="!merchantAdmin && !bankAdmin"
                  v-model="selectedCp"
                  class="dense"
                  stack-label="Card Program"
                  :options="cardPrograms"></q-select>
        <q-select v-if="merchantAdmin && locations.length > 0"
                  v-model="selectedLocation"
                  class="dense"
                  stack-label="Location"
                  @input="reload"
                  :options="locations"></q-select>
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :iso-week="true"
                         :default="dateRange"
                         :ranges="datesWithDaily"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content top-statics-style">
      <div class="row gutter-sm top-statics-row top-statics-row-sm">
        <div class="col-xl-3 col-6" v-if="!bankAdmin">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-account-outline"
                        color="positive"></q-icon>
                <div class="column">
                  <div v-if="merchantAdmin"
                       class="description">Total Consumers</div>
                  <div v-if="!merchantAdmin"
                       class="description">Total Members</div>
                  <div class="value">{{ quick.totalConsumers || 0 }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-xl-3 col-6" v-if="!merchantAdmin && !bankAdmin">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-account-outline"
                        color="negative"></q-icon>
                <div class="column">
                  <div class="description">Total Negative Members</div>
                  <div class="value">{{ quick.totalNegativeMembers || 0 }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div v-if="!merchantAdmin && !bankAdmin"
             class="col-xl-3 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash"
                        color="negative"></q-icon>
                <div class="column">
                  <div class="description">Total Negative Member Balance</div>
                  <div class="value">{{ quick.totalNegativeMemberBalance || 0  | moneyFormat}}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div v-if="isMasterAdmin()"
             class="col-xl-3 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash"
                        color="fuschia"></q-icon>
                <div class="column">
                  <div class="description">Estimated Tern Fees</div>
                  <div class="value">{{ quick.estimatedTernFees || 0  | moneyFormat}}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div v-if="!merchantAdmin && !bankAdmin"
             class="col-xl-3 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash"
                        color="positive"></q-icon>
                <div class="column">
                  <div class="description">Spendr Revenue</div>
                  <div class="value">{{ quick.spendrRevenue || 0  | moneyFormat}}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
<!--        <div v-if="!merchantAdmin && !bankAdmin"-->
<!--             class="col-xl-3 col-6">-->
<!--          <q-card class="top-statics">-->
<!--            <q-card-main>-->
<!--              <div class="row">-->
<!--                <q-icon name="mdi-cash"-->
<!--                        color="positive"></q-icon>-->
<!--                <div class="column">-->
<!--                  <div class="description">{{ 'Spendr Gross Profit' }}</div>-->
<!--                  <div class="value">{{ quick.spendrGrossProfit || 0  | moneyFormat}}</div>-->
<!--                </div>-->
<!--              </div>-->
<!--            </q-card-main>-->
<!--          </q-card>-->
<!--        </div>-->
        <div v-if="!merchantAdmin && !bankAdmin"
             class="col-xl-3 col-6 partner-balance">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash"
                        color="fuschia"></q-icon>
                <div class="column">
                  <div class="description">Partner Balance</div>
                  <div class="value">{{ quick.spendrBalance || 0  | moneyFormat}}</div>
                </div>
              </div>
              <div class="row" v-if="isMasterAdmin || agentAdmin">
                <div class="col-12">
                  <q-btn color="blue"
                         :label="partnerRealCard && partnerRealCard.accountNum ? 'Change Card' : 'Bound Card'"
                         @click="partnerBoundCard()"
                         class="btn-sm mr-8 bound-card"
                         no-caps></q-btn>
                  <q-btn v-if="partnerRealCard && partnerRealCard.accountNum"
                         color="positive"
                         label="Load/Unload"
                         @click="partnerLoad()"
                         class="btn-sm load-unload"
                         no-caps></q-btn>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div v-if="!merchantAdmin && !bankAdmin"
             class="col-xl-3 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash"
                        color="positive"></q-icon>
                <div class="column">
                  <div class="description">Bank Fees</div>
                  <div class="value">{{ quick.bankFees || 0  | moneyFormat}}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div v-if="!bankAdmin"
          class="col-xl-3 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash"
                        color="positive"></q-icon>
                <div class="column">
                  <div class="description">{{ merchantAdmin ? 'Total Revenue' : 'Merchants Revenue' }}</div>
                  <div class="value">{{ quick.totalMerchantRevenue || 0  | moneyFormat}}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div v-if="merchantAdmin && $store.state.User.isLocationHasDummyMerchant"
             class="col-xl-3 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash"
                        color="positive"></q-icon>
                <div class="column">
                  <div class="description">Total Location Balance</div>
                  <div class="value">{{ quick.totalLocationBalance || 0  | moneyFormat}}</div>
                </div>
              </div>
              <div class="row" v-if="
              ($store.state.User.merchantStatus === 'Approved' || $store.state.User.merchantActive) &&
              !$store.state.User.isSpendrAdminLogin &&
              !merchantOtherAdmin &&
              !merchantBalanceAdmin
              ">
                <div class="col-12">
                  <q-btn color="primary"
                         label="Remove Funds"
                         @click="merchantUnload"
                         class="btn-mini ml-8 remove-funds-btn"
                         no-caps></q-btn>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-xl-3 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-format-list-bulleted"
                        color="positive"></q-icon>
                <div class="column">
                  <div class="description"># of TRX</div>
                  <div class="value">{{ quick.totalTransactions || 0 }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div v-if="bankAdmin" class="col-xl-3 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cash"
                        color="positive"></q-icon>
                <div class="column">
                  <div class="description">$ of total TRX</div>
                  <div class="value">{{ quick.totalMerchantRevenue || 0  | moneyFormat}}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div v-if="!merchantAdmin && !bankAdmin"
             class="col-xl-3 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-store"
                        color="positive"></q-icon>
                <div class="column">
                  <div class="description"># of Merchants</div>
                  <div class="value">{{ quick.totalMerchants || 0 }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-xl-3 col-6" v-if="!bankAdmin">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-map-marker"
                        color="orange"></q-icon>
                <div class="column">
                  <div class="description"># of Locations</div>
                  <div class="value">{{ quick.totalLocations || 0 }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-xl-3 col-6" v-if="!bankAdmin">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-cellphone-iphone"
                        color="purple"></q-icon>
                <div class="column">
                  <div class="description"># of Terminals</div>
                  <div class="value">{{ quick.totalTerminals || 0 }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>
      <div class="row gutter-sm"
           v-if="!merchantAdmin && !bankAdmin">
        <div class="col-sm-6 col-xs-12">
          <TopGraph v-model="chartDateRange"
                    title="Merchant"
                    chartId="spendrTopMerchant"></TopGraph>
        </div>
        <div class="col-sm-6 col-xs-12">
          <TopGraph v-model="chartDateRange"
                    title="Location"
                    chartId="spendrTopLocation"></TopGraph>
        </div>
      </div>
      <div class="row"
           v-if="!merchantAdmin && !bankAdmin">
        <BalanceGraph v-model="chartDateRange"
                      :chartSetting="chartSettingList('adminPartner')"
                      title="Partner"
                      chartId="spendrPartner"></BalanceGraph>
      </div>
      <div class="row" v-if="!bankAdmin && (
        ($store.state.User.isLocationHasDummyMerchant && merchantBalanceAdmin) ||
        !$store.state.User.isLocationHasDummyMerchant
      )
      ">
        <BalanceGraph v-model="chartDateRange"
                      :chartSetting="chartSettingList('adminMerchant')"
                      title="Merchant"
                      chartId="spendrMerchant"></BalanceGraph>
      </div>
      <div class="row"
           v-if="!merchantAdmin && !bankAdmin">
        <BalanceGraph v-model="chartDateRange"
                      :chartSetting="chartSettingList('adminMember')"
                      title="Member"
                      chartId="spendrMember"></BalanceGraph>
      </div>
    </div>

    <q-inner-loading :visible="loading">
      <q-spinner :size="50"></q-spinner>
    </q-inner-loading>
    <LoadDialog :partner-bound-card="partnerRealCard"></LoadDialog>
    <PartnerBoundCardDialog
      :partner-real-card="partnerRealCard"
      @done="partnerBoundCardDone"
    ></PartnerBoundCardDialog>
    <RemoveFunds v-if="merchantAdmin && !merchantBalanceAdmin && $store.state.User.isLocationHasDummyMerchant"></RemoveFunds>
  </q-page>
</template>

<script>
import SpendrPageMixin from '../../../mixins/spendr/SpendrPageMixin'
import ListPageMixin from '../../../mixins/ListPageMixin'
import BalanceGraph from '../../faas/base/common/balanceGraph'
import TopGraph from '../common/top5Graph'
import { EventHandlerMixin, request } from '../../../common'
import LoadDialog from './load'
import PartnerBoundCardDialog from './partnerBoundCard'
import RemoveFunds from './removeFunds'

export default {
  name: 'spendr-dashboard',
  mixins: [
    SpendrPageMixin,
    ListPageMixin,
    EventHandlerMixin('reload-spendr-dashboard')
  ],
  components: {
    BalanceGraph,
    TopGraph,
    LoadDialog,
    PartnerBoundCardDialog,
    RemoveFunds
  },
  data () {
    return {
      loading: false,
      chartDateRange: {},
      selectedLocation: 'all',
      locations: [],
      partnerRealCard: null
    }
  },
  methods: {
    async reload () {
      this.$q.loading.show()
      let data = {}
      if (this.$refs && this.$refs.dateRangeFilter) {
        data = this.$refs.dateRangeFilter.params()
      }
      data.locationId = this.selectedLocation
      this.chartDateRange = data
      const resp = await request('/admin/spendr/merchant/dashboard/summaryData', 'get', data)
      this.$q.loading.hide()
      if (resp) {
        this.quick = resp.data.quick
        this.partnerRealCard = resp.data.quick.partnerBoundCard
        this.locations = resp.data.quick.locations ? Object.values(resp.data.quick.locations) : []
      }
    },
    chartSettingList (type) {
      if (type === 'adminPartner') {
        return {
          colorList: ['74, 204, 61', '0, 98, 255', '252, 90, 90', '255, 144, 97', '239, 93, 168', '255, 233, 0'],
          fieldList: [
            { name: 'bankDeposit', value: 1 },
            { name: 'partnerCommissions', value: 1 },
            { name: 'memberRewards', value: 1 },
            { name: 'memberDepostis', value: 1 },
            { name: 'memberPayout', value: -1 }
          ],
          itemList: ['Bank Deposits', 'Partner Commissions', 'Member Rewards', 'Member Depostis', 'Merchant Payout', 'Partner Balance'],
          totalField: 'partnerBalance'
        }
      } else if (type === 'adminMerchant') {
        return {
          colorList: ['74, 204, 61', '0, 98, 255', '252, 90, 90', '255, 144, 97', '239, 93, 168', '255, 233, 0'],
          fieldList: [
            { name: 'memberSpend', value: 1 },
            { name: 'merchantCommissions', value: 1 },
            { name: 'memberRewards', value: 1 },
            { name: 'merchantPayouts', value: 1 },
            { name: 'merchantFees', value: -1 }
          ],
          itemList: ['Member Spend', 'Merchant Commissions', 'Member Rewards', 'Merchant Payouts', 'Merchant Fees', 'Merchant Balance'],
          totalField: 'merchantBalance'
        }
      } else if (type === 'adminMember') {
        return {
          colorList: ['74, 204, 61', '0, 98, 255', '252, 90, 90', '255, 144, 97', '239, 93, 168', '255, 233, 0'],
          fieldList: [
            { name: 'memberDeposits', value: 1 },
            { name: 'memberRewards', value: 1 },
            { name: 'memberSpend', value: 1 },
            { name: 'memberWithdrawls', value: 1 },
            { name: 'memberFees', value: -1 }
          ],
          itemList: ['Member Deposits', 'Member Rewards', 'Member Spend', 'Member Withdrawls', 'Merchant Fees', 'Merchant Balance'],
          totalField: 'memberBalance'
        }
      } else if (type === 'merchantMember') {
        return {
          colorList: [],
          fieldList: [],
          itemList: [],
          totalField: ''
        }
      }
    },
    async checkSummaryData () {
      this.$q.loading.show()
      await request('/admin/spendr/merchant/dashboard/check-summary-data', 'get')
      this.$q.loading.hide()
    },
    partnerBoundCard () {
      this.$root.$emit('show-spendr-bound-card-dialog')
    },
    partnerBoundCardDone (partnerRealCard) {
      if (partnerRealCard && partnerRealCard.accountNum) {
        this.partnerRealCard = partnerRealCard
      }
    },
    partnerLoad () {
      this.$root.$emit('show-spendr-load-dialog')
    },
    merchantUnload () {
      this.$root.$emit('show-spendr-remove-funds-dialog')
    }
  },
  async mounted () {
    this.reload()
  }
}
</script>
<style lang="scss">
.spendr__dashboard__index_page {
  .partner-balance {
    .bound-card, .load-unload {
      padding: 2px 8px !important;
      border-radius: 7px;
    }
  }
}
</style>
