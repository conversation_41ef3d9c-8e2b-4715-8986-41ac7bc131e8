<template>
  <q-dialog class="spendr-bound-card-dialog"
            v-model="visible"
            prevent-close
  >
    <template slot="title">
      <div class="font-16 mb-2">
        Bound Card
      </div>
      <div
        class="font-12 normal text-warning mt-15"
        v-if="partnerRealCard && partnerRealCard.pendingCard">
        New bank account is pending micro-deposits verification. Click on the 'Verify Manual Card' in 1-2 business days to complete linking.
      </div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5">
        <div v-if="partnerRealCard && partnerRealCard.accountNum"
             class="detail-list">
          <div class="detail-item">
            <p class="title">Bank Name:</p>
            <p><span>{{ partnerRealCard.bankName || '-' }}</span>
            </p>
          </div>
          <div class="detail-item">
            <p class="title">Routing Number:</p>
            <p><span ref="rounting">{{ partnerRealCard.routingNum || '-' }}</span>
              <q-icon v-if="partnerRealCard.routingNum"
                      @click.native="copy(partnerRealCard.routingNum)"
                      name="mdi-content-copy light-grey font-14 ml-10"> </q-icon>
            </p>
          </div>
          <div class="detail-item">
            <p class="title">Account Number:</p>
            <p><span ref="account">{{ partnerRealCard.accountNum || '-' }}</span>
              <q-icon v-if="partnerRealCard.accountNum"
                      @click.native="copy(partnerRealCard.accountNum)"
                      name="mdi-content-copy light-grey font-14 ml-10"> </q-icon>
            </p>
          </div>
        </div>
        <div class="col-sm-12" v-if="!(partnerRealCard && partnerRealCard.accountNum) || resetBank">
          <plaid-link ref="plainLink" v-if="linkToken"
                      v-bind="{
            env,
            product,
            token: linkToken,
            onSuccess,
            onEvent,
            onLoad
          }"
          >{{ linkText }}</plaid-link>
          <div class="accounts-actions" v-else>
            <q-btn color="primary" no-caps @click="getLinkToken">{{ linkText }}</q-btn>
          </div>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <div class="row">
        <q-btn v-if="partnerRealCard && partnerRealCard.accountNum && !resetBank"
               :label="linkText"
               no-caps
               class="mb-20"
               color="primary"
               @click="changeLinkedBank(true)" />
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { notify, notifySuccess, request } from '../../../common'
import { required } from 'vuelidate/lib/validators'
import PlaidLink from 'vue-plaid-link'
import copy from 'copy-to-clipboard'

export default {
  name: 'spendr-bound-card-dialog',
  mixins: [
    Singleton
  ],
  props: {
    partnerRealCard: {
      type: Object,
      required: false
    }
  },
  components: {
    PlaidLink
  },
  data () {
    return {
      resetBank: null,
      env: 'sandbox',
      product: ['auth'],
      linkToken: null
    }
  },
  validations: {
    entity: {
      amount: {
        required
      },
      type: {
        required
      }
    }
  },
  computed: {
    linkText () {
      if (this.partnerRealCard && this.partnerRealCard.pendingCard) {
        return 'Verify Manual Card'
      }
      return 'Choose Bank Account'
    }
  },
  methods: {
    async init () {
      if (this.partnerRealCard && this.partnerRealCard.accountNum && !this.resetBank) {
        return
      }
      await this.getLinkToken()
    },
    // If partner.token is null. Call this function to get
    async getLinkToken () {
      this.loading = true
      const resp = await request(`/admin/spendr/dashboard/partner/plaid/create-link-token`)
      this.loading = false
      if (resp.success) {
        if (resp.data && resp.data.link_token) {
          this.env = resp.data.env
          this.product = resp.data.product
          this.linkToken = resp.data.link_token
        }
      } else {
        this.linkToken = null
      }
    },
    async onLoad () {
      this.$refs.plainLink.handleOnClick()
    },
    async onEvent (event, data) {
      console.log(event, data)
    },
    // Handle account data after selected bank account
    async onSuccess (token, data) {
      console.log(data)
      this.linkToken = null
      this.$q.loading.show({
        message: 'Setup bank ...'
      })
      const resp = await request(`/admin/spendr/dashboard/partner/plaid/exchange-access-token`, 'post', {
        publicToken: token,
        data: data
      })
      this.$q.loading.hide()
      if (resp.success) {
        this.resetBank = false
        this.partnerRealCard.pendingCard = resp.data.pendingCard
        if (resp.data.pendingCard) {
          notifySuccess(resp.message)
          return
        }
        this.partnerRealCard = resp.data
        this.$emit('done', resp.data)
      } else {
        notify(resp.message)
      }
    },
    copy (content) {
      copy(content)
      notify('Copied to the clipboard')
    },
    changeLinkedBank (reset) {
      this.resetBank = reset
      setTimeout(() => {
        this.init()
      }, 300)
    }
  }
}
</script>
<style lang="scss">
  .spendr-bound-card-dialog {
    .modal-content {
      width: 500px !important;
    }
    .modal-scroll {
      max-height: none;
    }
    .detail-list {
      display: block;
      padding-left: 20%;
      p {
        font-size: 16px;
        font-weight: 600;
        color: #191d3d;
        margin: 0;
        text-align: left;
      }
      .title {
        text-align: left;
        color: rgba($color: #191d3d, $alpha: 0.5);
      }
    }
  }
</style>
