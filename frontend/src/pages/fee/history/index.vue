<template>
  <q-page id="fee_history__list_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-btn class="mr-10"
               color="black"
               @click="download"
               label="Export"></q-btn>
      </div>
    </div>
    <div class="page-content">
      <div class="quick-result">
        <div>
          <h5>{{ quick.totalSize | number }}</h5>
          <div class="description">Total # of fees</div>
        </div>
        <div>
          <h5>{{ quick.totalAmountUSD | moneyFormat('USD', true) }}</h5>
          <div class="description">Total Fee Amount</div>
        </div>
      </div>

      <TopChart :chart-id="chartId"
                v-if="chartData">
      </TopChart>
      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               :rows-per-page-options="[5, 10, 20, 50]"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <q-btn flat round dense
                 icon="mdi-filter-variant"
                 @click="filtersDialog = true"/>
          <FilterChips :filters.sync="filters"
                       :options="filterOptions"
                       v-show="!loadingFilters"
                       @reload="reload"></FilterChips>
        </template>
        <template slot="top-right">
          <q-btn flat round dense
                 class="q-mr-md"
                 @click="reload"
                 icon="refresh"/>
          <ColumnFilter :columns="columns"></ColumnFilter>
        </template>

        <q-tr slot="body"
              slot-scope="props" :class="`status_${props.row.status}`">
          <q-td v-for="col in props.cols" :key="col.field">
            <template v-if="col.field === 'User ID' && p('user')">
              <a :href="`/admin#/a/i/admin__user_modify__modify?user_id=${col.value}`"
                 target="_blank">{{ col.value }}</a>
            </template>
            <template v-else-if="col.field === 'Time'">
              {{ col.value | date('L LT') }}
            </template>
            <template v-else-if="col.field === 'Amount'">
              {{ col.value | moneyFormat }}
            </template>
            <template v-else>
              {{ col.value }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination" slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>

import { request } from '../../../common'
import ListPageMixin from '../../../mixins/ListPageMixin'
import { chartName } from '../../../const'
import eventBus from '../../../eventBus'
import _ from 'lodash'

export default {
  mixins: [
    ListPageMixin
  ],
  data () {
    return {
      filtersUrl: '/admin/fee/history/filters',
      downloadUrl: '/admin/fee/history/export',
      title: 'Fee History List',
      timeField: 'time',
      cardProgramField: 'filter[cp.id=]',
      chartId: chartName.CARD_FEES_CHART,
      autoReloadWhenUrlChanges: true,
      baseColumns: [{
        field: 'ID',
        label: 'ID',
        align: 'left'
      }, {
        field: 'Time',
        label: 'Time',
        align: 'left'
      }, {
        field: 'User ID',
        label: 'User ID',
        align: 'left'
      }, {
        field: 'User Name',
        label: 'User Name',
        align: 'left'
      }, {
        field: 'Email',
        label: 'Email',
        align: 'left'
      }, {
        field: 'Fee Name',
        label: 'Fee Name',
        align: 'left'
      }, {
        field: 'Amount',
        label: 'Amount',
        align: 'right'
      }, {
        field: 'Merchant',
        label: 'Merchant',
        align: 'left'
      }, {
        field: 'Decline Reason',
        label: 'Decline Reason',
        align: 'left'
      }, {
        field: 'Comment',
        label: 'Comment',
        align: 'left'
      }, {
        field: 'Card Type',
        label: 'Card Type',
        align: 'left'
      }],
      quick: {
        totalSize: 0,
        totalAmountUSD: 0
      },
      filterOptions: [
        {
          value: 'filter[cp.id=]',
          label: 'Card program',
          options: [],
          source: 'cardPrograms'
        },
        {
          value: 'time',
          label: 'Time',
          range: [
            {
              value: 'range[ufh.time][start]',
              type: 'date'
            }, {
              value: 'range[ufh.time][end]',
              type: 'date'
            }
          ]
        }, {
          value: 'filter[ufh.feeName]',
          label: 'Fee Name',
          options: [],
          source: 'feeNames'
        }, {
          value: 'filter[u.id]',
          label: 'User ID'
        }
      ]
    }
  },
  methods: {
    async request ({ pagination }) {
      this.beforeRequest({ pagination })

      this.loading = true
      const data = this.$refs.filtersDialog.getFilters()
      data.keyword = this.keyword
      if (!_.isEmpty(this.$route.query)) {
        data.isFromDashboard = true
      }
      const resp = await request(`/admin/fee/history/search/${pagination.page}/${pagination.rowsPerPage}`, 'form', data)
      this.loading = false
      if (resp.success) {
        this.pagination = pagination
        this.data = resp.data.data
        this.pagination.rowsNumber = resp.data.count
        this.quick = resp.data.quick
        this.chartData = resp.data.chartData
        if (this.chartData) {
          eventBus.$emit('reload-top-chart', this.chartData)
        }
      }
    },
    init () {
      this.data = []
      this.quick = {}
      this.filters = []
      this.keyword = ''

      this.columns = this.baseColumns
    }
  }
}
</script>

<style lang="scss">
  @import '../../../css/variable';

  #fee_history__list_page {
  }
</style>
