<template>
  <q-page id="dashboard-analytics"
          class="graph__dashboard__index_page">
    <div class="row fit header page-header">
      <div class="col-3">
        <div class="q-headline">Dashboard</div>
      </div>
      <div class="col-9 row justify-end fun-group">
        <DateRangeFilter class="mr-20"
                         ref="dateRangeFilter"></DateRangeFilter>

        <q-field label="Card Program"
                 class="w-250 mr-20 cp-selector">
          <q-select v-model="cp"
                    :options="cps"></q-select>
        </q-field>

        <q-btn color="positive"
               size="sm"
               label="RELOAD"
               @click="reload()"></q-btn>
      </div>
    </div>
    <div class="page-content mt-20">
      <div class="row gutter-sm">
        <div class="col-4"
             v-if="chartVisible('Load_Activity')">
          <chart ref="loadActivity"
                 title="Load Activity"
                 chart-id="load"
                 :params="params"></chart>
        </div>
        <div class="col-4"
             v-if="chartVisible('Active_cards')">
          <chart ref="activeCards"
                 title="Active Cards"
                 chart-id="active"
                 :params="params"></chart>
        </div>
        <div class="col-4"
             v-if="chartVisible('New_Sign_Ups')">
          <chart ref="users"
                 title="Users"
                 chart-id="user"
                 :params="params"></chart>
        </div>
        <div class="col-4"
             v-if="chartVisible('Card_fees_collected')">
          <chart ref="cardFeeCollected"
                 title="Card fees collected"
                 chart-id="card_fees_collected"
                 :params="params"></chart>
        </div>
        <div class="col-4"
             v-if="chartVisible('Card_spend')">
          <chart ref="usageActivity"
                 title="Usage Activity"
                 chart-id="usage"
                 :params="params"></chart>
        </div>
        <div class="col-4"
             v-if="chartVisible('Top_Merchants')">
          <m-chart-with-summary ref="merchant"
                                title="Merchant"
                                chart-id="merchant"
                                :params="params"></m-chart-with-summary>
        </div>
        <div class="col-4"
             v-if="chartVisible('Top_MCC')">
          <m-chart-with-summary ref="mcc"
                                title="Mcc"
                                chart-id="mcc"
                                :params="params"></m-chart-with-summary>
        </div>
        <div class="col-4"
             v-if="chartVisible('Revenue')">
          <chart ref="revenue"
                 title="Net Revenue"
                 chart-id="revenue"
                 :params="params"></chart>
        </div>
        <div class="col-4"
             v-if="chartVisible('Total_Account_Balance')">
          <chart ref="totalAccountBalance"
                 title="Total Account Balance"
                 chart-id="total_account_balance"
                 :params="params"></chart>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script>
import Chart from './chart'
import MChartWithSummary from './MChartWithSummary'
import { request } from '../../../common'
import _ from 'lodash'
import AnalyticsMixin from '../../../mixins/AnalyticsMixin'
import DateRangeFilter from '../../../components/DateRangeFilter'

export default {
  name: 'dashboard-analytics-index',
  mixins: [
    AnalyticsMixin
  ],
  components: {
    Chart,
    MChartWithSummary,
    DateRangeFilter
  },
  data () {
    return {
      cp: null,
      cps: [],
      isMounted: false,
      chartSetting: null
    }
  },
  computed: {
    params () {
      let data = {}
      data.cardProgram = this.cp
      if (this.isMounted) {
        _.assignIn(data, this.$refs.dateRangeFilter.params())
      }
      return data
    }
  },
  methods: {
    chartVisible (type) {
      if (!this.chartSetting) {
        return false
      }
      var item = this.chartSetting[type]
      if (!item || item.visible === undefined) {
        return true
      }
      return item.visible === '1'
    },
    async getCardProgram () {
      this.$q.loading.show()
      const resp = await request('/admin/get/card-program')
      this.$q.loading.hide()
      if (resp) {
        this.cps.push({
          'value': null,
          'label': 'All'
        })
        let selected = null
        _.forEach(resp.data, v => {
          v.label = v.name
          v.value = v.id

          if (this.$store.state.User.cpKey === 'cp_usu' && v.name === 'US Unlocked') {
            selected = v.id
          }
        })
        this.cps = _.concat(this.cps, resp.data)

        if (selected && !this.cp) {
          this.cp = selected
        }
      }
    },
    async reloadType () {
      this.$q.loading.show()
      const resp = await request('/admin/analytics/card-program/chart-setting')
      this.$q.loading.hide()
      if (resp) {
        this.chartSetting = resp.data

        this.$nextTick(() => {
          this.$refs.loadActivity && this.$refs.loadActivity.initData(resp.data.Load_Activity)
          this.$refs.activeCards && this.$refs.activeCards.initData(resp.data.Active_cards)
          this.$refs.usageActivity && this.$refs.usageActivity.initData(resp.data.Card_spend)
          this.$refs.cardFeeCollected && this.$refs.cardFeeCollected.initData(resp.data.Card_fees_collected)
          this.$refs.users && this.$refs.users.initData(resp.data.New_Sign_Ups)
          this.$refs.revenue && this.$refs.revenue.initData(resp.data.Revenue)
          this.$refs.mcc && this.$refs.mcc.initData(resp.data.Top_MCC)
          this.$refs.merchant && this.$refs.merchant.initData(resp.data.Top_Merchants)
          this.$refs.totalAccountBalance && this.$refs.totalAccountBalance.initData(resp.data.total_account_balance)

          this.reload()
        })
      }
    },
    reload (ref = null) {
      let data = {}
      data.cardProgram = this.cp
      if (this.$refs.dateRangeFilter) {
        _.assignIn(data, this.$refs.dateRangeFilter.reloadParams())
      }

      if (ref) {
        this.$refs[ref] && this.$refs[ref].load(data)
      } else {
        if (this.$refs.loadActivity) {
          this.$refs.loadActivity.load(data)
        }
        if (this.$refs.activeCards) {
          this.$refs.activeCards.load(data)
        }
        if (this.$refs.usageActivity) {
          this.$refs.usageActivity.load(data)
        }
        if (this.$refs.revenue) {
          this.$refs.revenue.load(data)
        }
        if (this.$refs.users) {
          this.$refs.users.load(data)
        }
        if (this.$refs.cardFeeCollected) {
          this.$refs.cardFeeCollected.load(data)
        }
        if (this.$refs.mcc) {
          this.$refs.mcc.load(data)
        }
        if (this.$refs.merchant) {
          this.$refs.merchant.load(data)
        }
        if (this.$refs.totalAccountBalance) {
          this.$refs.totalAccountBalance.load(data)
        }
      }
    },

    reloadChart (id) {
      const refs = {
        revenue: 'revenue',
        mcc: 'mcc',
        merchant: 'merchant',
        usage: 'usageActivity',
        card_fees_collected: 'cardFeeCollected',
        user: 'users',
        active: 'activeCards',
        load: 'loadActivity',
        total_account_balance: 'totalAccountBalance'
      }
      const ref = refs[id]
      this.reload(ref)
    }
  },
  async mounted () {
    this.isMounted = true

    await this.getCardProgram()
    this.$nextTick(() => {
      this.reloadType()
    })

    this.$root.$on('dashboard-reload-chart', this.reloadChart)
  },
  beforeDestroy () {
    this.$root.$off('dashboard-reload-chart', this.reloadChart)
  }
}
</script>

<style lang="scss">
@import "../../../css/variable";
#dashboard-analytics {
  display: block !important;

  .chart-box {
    padding: 10px;

    .content-box {
      position: relative;
      background: white;
      width: 100%;
      padding: 10px;
      height: 100%;

      .content {
        padding: 4px 10px;
      }

      .mcc-value {
        color: var(--q-color-positive);
      }

      .chart-header {
        display: flex;

        .title {
          font-size: 18px;
          padding: 4px 10px;
        }

        .fun-group {
          margin-left: auto;
          align-items: center;
          color: var(--q-color-positive);
        }
      }
      .total-money {
        height: 30px;
        margin: 10px 10px 0 10px;
        font-size: 24px;
      }
      .chart {
        height: calc(100% - 70px);
      }

      .btn-reload {
        position: absolute;
        right: 0;
        bottom: 0;
        padding: 4px !important;
        opacity: 0.5;
      }
    }
  }
  .q-field-label {
    flex-basis: 85px;
  }
  .chart-height {
    height: calc(100vw / 3 - 120px);
  }
}

@media (max-width: 434px) {
  #dashboard-analytics {
    .cp-selector {
      .q-field-content {
        width: calc(100% - 93px);
      }
    }
  }
}
</style>
