<template>
  <q-page id="alerts_alert_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-btn flat
               v-if="c.isSuperAdmin(user) || p('alert_add_alert')"
               @click="$router.push('/a/i/admin__alert__new')"
               icon="mdi-plus"
               label="Create Alert"></q-btn>
      </div>
    </div>
    <div class="page-content">
      <div class="quick-result">
        <div>
          <h5>{{ quick.totalSize | number }}</h5>
          <div class="description"># of Alerts</div>
        </div>
      </div>

      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               :rows-per-page-options="[5, 10, 20, 50]"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <q-btn flat
                 round
                 dense
                 icon="mdi-filter-variant"
                 @click="filtersDialog = true" />
          <FilterChips :filters.sync="filters"
                       :options="filterOptions"
                       v-show="!loadingFilters"
                       @reload="reload"></FilterChips>
        </template>
        <template slot="top-right">
          <q-btn flat
                 round
                 dense
                 class="q-mr-md"
                 @click="reload"
                 icon="refresh" />
          <ColumnFilter :columns="columns"></ColumnFilter>
        </template>

        <q-tr slot="body"
              slot-scope="props">
          <q-td v-for="col in props.cols"
                :key="col.field"
                :class="`text-${col.align || 'left'}`">
            <template v-if="col.field === 'Actions'">
              <q-btn-dropdown size="xs"
                              color="grey-3"
                              text-color="black"
                              :label="col.label">
                <q-list link>
                  <q-item v-if="props.row['Unable to Close'] == false && props.row['Status'] == 'Active'"
                          v-close-overlay
                          :to="`/a/alert_job_creation?target=${props.row['Alert ID']}`">
                    <q-item-main>New Job</q-item-main>
                  </q-item>
                  <q-item v-if="props.row['Status'] == 'Active'"
                          v-close-overlay
                          @click.native="archiveAlert(props.row['Alert ID'])">
                    <q-item-main>Archive</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              {{ col.value }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"
                       :max="999999"></BatchExportDialog>
  </q-page>
</template>

<script>
import { generateColumns, request } from '../../../common'
import ListPageMixin from '../../../mixins/ListPageMixin'
import _ from 'lodash'

export default {
  mixins: [
    ListPageMixin
  ],
  data () {
    return {
      filtersUrl: '/admin/alert/filters',
      downloadUrl: '/admin/alert/export',
      title: 'Alerts',
      timeField: 'time',
      // cardProgramField: 'filter[cp.id=]',
      autoReloadWhenUrlChanges: true,
      baseColumns: generateColumns([
        'Alert ID', 'Name', 'Description', 'Type', 'Message', 'Creation Date', 'Author', 'Unable to Close', 'Status', 'Actions'
      ], [
        // 'Legacy Balance', 'Account Balance', 'Days Negative', 'Related Transactions' TODO: This may be needed.
      ]),
      quick: {
        totalSize: 0
      },
      filterOptions: [
        {
          value: 'filter[alert.id=]',
          label: 'Alert',
          options: [],
          source: 'alert'
        }, {
          value: 'filter[alert.name=]',
          label: 'Name',
          options: [],
          source: 'alert'
        }, {
          value: 'filter[alert.description=]',
          label: 'Description',
          options: [],
          source: 'alert'
        }, {
          value: 'filter[alert.type=]',
          label: 'Type',
          options: [],
          source: 'alert'
        }, {
          value: 'filter[alert.creationDate]',
          label: 'Creation Date',
          options: [],
          source: 'alert'
        }, {
          value: 'filter[alert.authorId]',
          label: 'Author',
          options: [],
          source: 'alert'
        }, {
          value: 'filter[alert.message]',
          label: 'Message',
          options: [],
          source: 'alert'
        }
      ]
    }
  },
  computed: {
    user () {
      return this.$store.state.User
    }
  },
  methods: {

    async archiveAlert (alert) {
      console.log('run')
      const resp = await request(`/admin/alert/archive/${alert}`, 'GET')
      this.loading = true
      if (resp.success) {
        console.log(resp)
        this.reload()
      }
    },

    async request ({ pagination }) {
      this.beforeRequest({ pagination })

      this.loading = true
      const data = this.$refs.filtersDialog.getFilters()
      data.keyword = this.keyword
      if (!_.isEmpty(this.$route.query)) {
        data.isFromDashboard = true
      }
      const resp = await request(`/admin/alert/search/${pagination.page}/${pagination.rowsPerPage}`, 'form', data)
      this.loading = false
      if (resp.success) {
        this.pagination = pagination
        this.data = resp.data.data
        this.pagination.rowsNumber = resp.data.quick.totalSize
        this.quick = resp.data.quick
        console.log(resp)
      }
    },
    init () {
      this.data = []
      this.quick = {}
      this.filters = []
      this.keyword = ''

      this.columns = this.baseColumns
    }
  }
}
</script>
