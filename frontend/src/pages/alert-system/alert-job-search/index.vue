<template>
  <q-page id="alerts_alert_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <!--        <q-btn flat-->
        <!--               v-if="c.isSuperAdmin(user) || p('alert_add_alert')"-->
        <!--               @click="$router.push('/a/i/admin__alert__new')"-->
        <!--               icon="mdi-plus"-->
        <!--               label="Create Alert"></q-btn>-->
      </div>
    </div>
    <div class="page-content">
      <div class="quick-result">
        <div>
          <h5>{{ quick.totalSize | number }}</h5>
          <div class="description"># of Jobs</div>
        </div>
      </div>

      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               :rows-per-page-options="[5, 10, 20, 50]"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <q-btn flat
                 round
                 dense
                 icon="mdi-filter-variant"
                 @click="filtersDialog = true" />
          <FilterChips :filters.sync="filters"
                       :options="filterOptions"
                       v-show="!loadingFilters"
                       @reload="reload"></FilterChips>
        </template>
        <template slot="top-right">
          <q-btn flat
                 round
                 dense
                 class="q-mr-md"
                 @click="reload"
                 icon="refresh" />
          <ColumnFilter :columns="columns"></ColumnFilter>
        </template>

        <q-tr slot="body"
              slot-scope="props">
          <q-td v-for="col in props.cols"
                :key="col.field"
                :class="`text-${col.align || 'left'}`">
            <template v-if="col.field === 'Actions'">
              <q-btn-dropdown size="xs"
                              color="grey-3"
                              text-color="black"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          @click.native="deleteJob(props.row['Alert Job ID'])">
                    <q-item-main>Delete</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              {{ col.value }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"
                       :max="999999"></BatchExportDialog>
  </q-page>
</template>

<script>
import { generateColumns, request } from '../../../common'
import ListPageMixin from '../../../mixins/ListPageMixin'
import _ from 'lodash'

export default {
  mixins: [
    ListPageMixin
  ],
  data () {
    return {
      filtersUrl: '/admin/alert_job/filters',
      downloadUrl: '/admin/alert_job/export',
      title: 'Alert Jobs',
      timeField: 'time',
      // cardProgramField: 'filter[cp.id=]',
      autoReloadWhenUrlChanges: true,
      baseColumns: generateColumns([
        'Alert Job ID', 'Alert ID', 'Description', 'Criteria', 'Creation Date', 'Creator', 'Total Sent', 'Total Viewed', 'Actions'
      ], [
        // 'Legacy Balance', 'Account Balance', 'Days Negative', 'Related Transactions' TODO: This may be needed.
      ]),
      quick: {
        totalSize: 0
      },
      filterOptions: [
        {
          value: 'filter[alertJob.id=]',
          label: 'Alert Job',
          options: [],
          source: 'alertJob'
        }, {
          value: 'filter[alertJob.alert=]',
          label: 'Alert',
          options: [],
          source: 'alertJob'
        }, {
          value: 'filter[alertJob.description=]',
          label: 'Description',
          options: [],
          source: 'description'
        }, {
          value: 'filter[alertJob.criteria=]',
          label: 'Criteria',
          options: [],
          source: 'alertJob'
        }, {
          value: 'filter[alertJob.creationDate]',
          label: 'Creation Date',
          options: [],
          source: 'alertJob'
        }, {
          value: 'filter[alertJob.createdBy]',
          label: 'Author',
          options: [],
          source: 'alert'
        }, {
          value: 'filter[alertJob.totalSent]',
          label: 'Total Sent',
          options: [],
          source: 'alert'
        }, {
          value: 'filter[alertJob.totalViewed]',
          label: 'Total Sent',
          options: [],
          source: 'alert'
        }
      ]
    }
  },
  computed: {
    user () {
      return this.$store.state.User
    }
  },
  methods: {

    async deleteJob (job) {
      // let requestOptions = {
      //   method: 'GET'
      // }
      //
      // fetch(`/admin/alert_job/delete_job/${job}`, requestOptions)
      //   .then(response => response.text())
      //   .then(result => console.log(result))
      //   .catch(error => console.log('error', error))
      console.log('run')
      const resp = await request(`/admin/alert_job/delete_job/${job}`, 'GET')
      this.loading = true
      if (resp.success) {
        console.log(resp)
        this.reload()
      }
    },

    async request ({ pagination }) {
      this.beforeRequest({ pagination })

      this.loading = true
      const data = this.$refs.filtersDialog.getFilters()
      data.keyword = this.keyword
      if (!_.isEmpty(this.$route.query)) {
        data.isFromDashboard = true
      }
      const resp = await request(`/admin/alert_job/search/${pagination.page}/${pagination.rowsPerPage}`, 'form', data)
      this.loading = false
      if (resp.success) {
        this.pagination = pagination
        this.data = resp.data.data
        this.pagination.rowsNumber = resp.data.quick.totalSize
        this.quick = resp.data.quick
      }
    },
    init () {
      this.data = []
      this.quick = {}
      this.filters = []
      this.keyword = ''

      this.columns = this.baseColumns
    }
  }
}
</script>
