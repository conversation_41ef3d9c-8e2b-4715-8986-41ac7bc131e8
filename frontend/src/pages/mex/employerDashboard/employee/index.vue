<template>
  <q-page id="mex__employees__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm">
        <div class="col-sm-3 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <div class="icon-item total-employee">
                  <img src="/static/abel/icons/mex_total_employee.svg">
                </div>
                <div class="column">
                  <div class="description">Total {{ textEmployees }}</div>
                  <div class="value">{{ quick.total || 0 }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <div class="icon-item total-balance">
                  <img src="/static/abel/icons/mex_total_balance.svg">
                </div>
                <div class="column">
                  <div class="description">Total Active Balances</div>
                  <div class="value">{{ quick.totalBalance || 0 | moneyFormat }}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <div class="icon-item ave-balance">
                  <img src="/static/abel/icons/mex_ave_balance.svg">
                </div>
                <div class="column">
                  <div class="description">Average Active Balance</div>
                  <div class="value">{{ quick.avgBalance || 0 | moneyFormat}}</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div v-if="$store.state.User.balanceFilter"
             class="col-sm-3 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <div class="icon-item total-employee">
                  <img src="/static/abel/icons/mex_total_employee.svg">
                </div>
                <div class="column">
                  <div class="description">Employees with high Balance</div>
                  <div class="value">
                    {{ quick.bigBalanceMember || 0 }}
                    <span @click="viewHighBalanceReport()" class="view">View</span>
                  </div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>
      <p class="import-info"
         v-if="quick.lastImport && quick.lastImport.createdAt">
        Last Import Members: {{ quick.lastImport.createdAt}}
        <span class="success">Status: {{quick.lastImport.status }} </span>
        <span class="success">Success: {{ quick.lastImport.success}}</span>
        <span class="failed"> Failed: {{ quick.lastImport.fail}}</span>
        <a @click="viewErrors(quick.lastImport)"
           v-if="quick.lastImport.fail">Detail</a>
      </p>
      <p class="import-info"
         v-if="quick.lastImportPayout && quick.lastImportPayout.createdAt">
        Last Import Payout: {{ quick.lastImportPayout.createdAt}}
        <span class="success">Status: {{quick.lastImportPayout.status }} </span>
        <span class="success">Success: {{ quick.lastImportPayout.success}}</span>
        <span class="failed"> Failed: {{ quick.lastImportPayout.fail}}</span>
        <a @click="viewErrors(quick.lastImportPayout, 'payout')"
           v-if="quick.lastImportPayout.fail">Detail</a>
      </p>
      <q-table :data="data"
               :columns="filteredVisibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>{{ textEmployee }} Report</strong>
          <span class="ml-10">Migration progress {{quick.migrated || 0}}/{{quick.total || 0}}</span>
        </template>
        <template slot="top-right">
          <q-btn-dropdown class="btn-sm ml-10 mr-10"
                          no-caps
                          v-if="multiSelActionLabel"
                          color="primary"
                          text-color="white"
                          :label="multiSelActionLabel">
            <q-list link>
              <q-item v-close-overlay
                      @click.native="moveToAnotherEmployer()">
                <q-item-main>Move to another employer</q-item-main>
              </q-item>
            </q-list>
          </q-btn-dropdown>
          <q-btn v-if="$store.state.User.cpKey == 'cp_faas'"
                 @click="createMembers"
                 class="btn-sm mr-10 import-member-btn"
                 no-caps>
            <img src="/static/abel/icons/mex_employer_payments.svg">
            Create Member(s)
          </q-btn>
          <q-btn v-if="$store.state.User.group && $store.state.User.group.fundingType !== 'ACH' && !$store.state.User.isReadOnlyAdmin"
                 @click="payEmployee"
                 class="btn-sm mr-10 pay-employee-btn"
                 no-caps>
            <img src="/static/abel/icons/mex_employer_payments.svg">
            Pay {{ textEmployee }}(s)
          </q-btn>
          <q-btn v-if="$store.state.User.cpKey === 'cp_mex' && !$store.state.User.isReadOnlyAdmin"
                 @click="uploadDdZip(true)"
                 class="btn-sm mr-10"
                 color="purple"
                 icon="mdi-folder-zip-outline"
                 label="Upload W2s"
                 no-caps />
          <q-btn v-if="$store.state.User.cpKey === 'cp_mex' && !$store.state.User.isReadOnlyAdmin"
                 @click="uploadDdZip(false)"
                 class="btn-sm mr-10"
                 color="orange"
                 icon="mdi-folder-zip-outline"
                 label="Upload Direct Deposit PDFs"
                 no-caps />
          <q-btn icon="mdi-file-download-outline"
                 label="Export"
                 @click="download"
                 class="btn-sm mr-8 export-btn h-40"
                 no-caps></q-btn>
          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10 font-18"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="font-18"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    @selectAll="onHeaderMultiSelectAll"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'multi_check'">
              <div class="checkbox-expander"
                   @click="toggleMultiSelRow(props.row)">
                <q-checkbox v-model="multiSel.ids[props.row['Employee ID']]"></q-checkbox>
              </div>
            </template>
            <template v-else-if="col.field === 'Status'">
              <q-chip v-if="props.row['SkipCheckCardStatus']"
                      class="font-12"
                      :class="statusClass(props.row['Status'])">
                {{ props.row['Status'] }} (Payable)
              </q-chip>
              <q-chip v-else
                      class="font-12"
                      :class="statusClass(props.row['Status'])">
                {{ props.row['Status'] }}
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Account Status'">
              <q-chip v-if="props.row['SkipCheckCardStatus']"
                      class="font-12"
                      :class="statusClass(props.row['Account Status'])">
                {{ props.row['Account Status'] }} (Payable)
              </q-chip>
              <q-chip v-else
                      class="font-12"
                      :class="statusClass(props.row['Account Status'])">
                {{ props.row['Account Status'] }}
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Card Status' || col.field === 'App Activation' ">
              <q-chip class="font-12"
                      :class="statusClass(props.row[col.field])">
                {{ props.row[col.field] }}
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Refund Member ATM Fees'">
              {{ props.row[col.field] ? 'Yes' : 'No' }}
            </template>
            <template v-else-if="col.field === 'Actions'">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list v-if="$store.state.User.group && $store.state.User.group.fundingType !== 'ACH'"
                        link>
                  <q-item v-close-overlay
                          @click.native="view(props.row)">
                    <q-item-main>View</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="$store.state.User.cpKey == 'cp_faas'"
                          @click.native="edit(props.row)">
                    <q-item-main>Edit</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="$store.state.User.cpKey == 'cp_faas'"
                          @click.native="$c.loginAs(props.row['Employee ID'])">
                    <q-item-main>Login As</q-item-main>
                  </q-item>
                  <q-item v-if="props.row['Status'] !== 'Inactive' && $store.state.User.cpKey == 'cp_faas'">
                    <q-item-main label="Change Status"></q-item-main>
                    <q-item-side right>
                      <q-item-tile icon="mdi-menu-right"></q-item-tile>
                    </q-item-side>
                    <q-popover anchor="bottom left"
                               self="top right"
                               :offset="[0, -40]">
                      <q-list link>
                        <q-item v-close-overlay
                                @click.native="changeCardStatus(props.row, 'Unsuspended')"
                                v-if="props.row['Card Status'] == 'Suspended' && !$store.state.User.isReadOnlyAdmin">
                          <q-item-main>Unsuspended</q-item-main>
                        </q-item>
                        <q-item v-close-overlay
                                @click.native="changeCardStatus(props.row, 'Suspended')"
                                v-if="props.row['Card Status'] == 'Active' && !$store.state.User.isReadOnlyAdmin">
                          <q-item-main>Suspended</q-item-main>
                        </q-item>
                        <q-item v-close-overlay
                                @click.native="changeStatus(props.row, 'Active')"
                                v-if="['On Hold'].includes(props.row['Status']) && !$store.state.User.isReadOnlyAdmin">
                          <q-item-main>Active</q-item-main>
                        </q-item>
                        <q-item v-close-overlay
                                @click.native="changeStatus(props.row, 'On Hold')"
                                v-if="props.row['Status'] !== 'On Hold'"
                                &&
                                !$store.state.User.isReadOnlyAdmin>
                          <q-item-main>On Hold</q-item-main>
                        </q-item>
                        <q-item v-close-overlay
                                v-if="!$store.state.User.isReadOnlyAdmin"
                                @click.native="changeStatus(props.row, 'Closed')">
                          <q-item-main>Inactive</q-item-main>
                        </q-item>
                      </q-list>
                    </q-popover>
                  </q-item>
                  <q-item v-if="!$store.state.User.isReadOnlyAdmin && ((props.row['Status'] == 'Active' || props.row['Status'] == 'Onboarded' || props.row['SkipCheckCardStatus']) && ($store.state.User.enableLoadTypeConfigure && (((!props.row['Last Payout ID'] || props.row['Enrolled'] !== 'Yes') && !$store.state.User.isReloadable) || $store.state.User.isReloadable)) || !$store.state.User.enableLoadTypeConfigure)"
                          v-close-overlay
                          @click.native="pay(props.row)">
                    <q-item-main>Pay</q-item-main>
                  </q-item>
                  <!-- <q-item v-if="props.row['Card Status'] == 'Active' && $store.state.User.cpKey == 'cp_faas'"
                          v-close-overlay
                          @click.native="changeCardStatus(props.row, 'Suspended')">
                    <q-item-main>Suspended</q-item-main>
                  </q-item>
                  <q-item v-if="props.row['Card Status'] == 'Suspended' && $store.state.User.cpKey == 'cp_faas'"
                          v-close-overlay
                          @click.native="changeCardStatus(props.row, 'Active')">
                    <q-item-main>Unsuspended</q-item-main>
                  </q-item> -->
                  <q-item v-if="!$store.state.User.isReadOnlyAdmin && props.row['Account Number'] &&  $store.state.User.cpKey === 'cp_faas'"
                          v-close-overlay
                          @click.native="replaceCard(props.row)">
                    <q-item-main>Replace Card</q-item-main>
                  </q-item>
                  <q-item v-if="!$store.state.User.isReadOnlyAdmin && (props.row['Last Payout Amount'] && ($store.state.User.cpKey !== 'cp_faas' || (props.row['Enrolled'] !== 'Yes' && $store.state.User.cpKey === 'cp_faas' )))"
                          v-close-overlay
                          @click.native="editDeposit(props.row)">
                    <q-item-main>Edit Last Deposit </q-item-main>
                  </q-item>

                  <q-item v-if="!$store.state.User.isReadOnlyAdmin && $store.state.User.cpKey === 'cp_mex'"
                          v-close-overlay
                          @click.native="viewDeposit(props.row)">
                    <q-item-main>View Direct Deposit Info</q-item-main>
                  </q-item>
                  <q-item v-if="!$store.state.User.isReadOnlyAdmin && $store.state.User.cpKey === 'cp_mex' && $store.state.User.subEmployers.length"
                          v-close-overlay
                          @click.native="changeEmployer(props.row)">
                    <q-item-main>Change Employer</q-item-main>
                  </q-item>
                </q-list>
                <q-list v-else>
                  <q-item v-close-overlay
                          @click.native="view(props.row)">
                    <q-item-main>View</q-item-main>
                  </q-item>
                  <q-item v-if="!$store.state.User.isReadOnlyAdmin && $store.state.User.cpKey === 'cp_mex'"
                          v-close-overlay
                          @click.native="viewDeposit(props.row)">
                    <q-item-main>View Direct Deposit Info</q-item-main>
                  </q-item>
                  <q-item v-if="!$store.state.User.isReadOnlyAdmin && $store.state.User.cpKey === 'cp_mex' && $store.state.User.subEmployers.length"
                          v-close-overlay
                          @click.native="changeEmployer(props.row)">
                    <q-item-main>Change Employer</q-item-main>
                  </q-item>

                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
    <PayEmployee></PayEmployee>
    <HtmlListDialog></HtmlListDialog>
    <EditDeposit></EditDeposit>
    <ImportMember></ImportMember>
    <ImportMemberError></ImportMemberError>
    <MemberDetail></MemberDetail>
    <UploadDirectDeposit></UploadDirectDeposit>
    <DepositDialog></DepositDialog>
    <q-dialog v-model="changeStatusDialogModel"
              prevent-close>
      <span slot="title">
        Change Status
        <q-btn class="close"
               round
               flat
               @click="onCancel"
               icon="close" />
      </span>

      <span v-if="selectRow"
            slot="message">
        <p v-if="status ==='Closed'">Please enter the reason you want to inactivate the card belonging to {{$c.fullName(selectRow)}}.</p>
        <p v-if="status ==='Closed'">This will set the card status to inactive and sweep the funds back to the master funding account. This action is irreversible.</p>
        <p v-if="status !=='Closed'">Please enter the reason why you want to change the card's status to {{status}} belonging to {{$c.fullName(selectRow)}}</p>
      </span>

      <div slot="body">
        <q-input v-model="reason" />
      </div>

      <template slot="buttons">
        <q-btn color="negative"
               label="Cancel"
               @click="onCancel" />
        <q-btn color="positive"
               label="Submit"
               @click="onOk" />
      </template>
    </q-dialog>
    <BatchMoveDialog></BatchMoveDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns, notifySuccess, notify, request } from '../../../../common'
import MexPageMixin from '../../../../mixins/mex/MexPageMixin'
import FreezeColumnMixin from '../../../../mixins/FreezeColumnMixin'
import PayEmployee from './pay'
import HtmlListDialog from '../../../../components/HtmlListDialog'
import EditDeposit from './editDeposit'
import ImportMember from './importMember'
import ImportMemberError from './importMemberError'
import MemberDetail from './../../../faas/base/members/detail'
import UploadDirectDeposit from './upload_dd'
import DepositDialog from '../../common/deposit_info'
import MultiSelectionListMixin from '../../../../mixins/MultiSelectionListMixin'
import BatchMoveDialog from './../../members/batch/move'
import _ from 'lodash'

export default {
  name: 'mex-employers-employee',
  mixins: [
    MexPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    MultiSelectionListMixin,
    EventHandlerMixin('reload-mex-employers-employee', 'searchMember')
  ],
  components: {
    PayEmployee,
    HtmlListDialog,
    EditDeposit,
    ImportMember,
    ImportMemberError,
    MemberDetail,
    UploadDirectDeposit,
    DepositDialog,
    BatchMoveDialog
  },
  mounted () {
    this.initColumns()
    this.keyword = localStorage.getItem('quickSearch') ? localStorage.getItem('quickSearch') : ''
    this.dateRange = localStorage.getItem('quickSearch') ? 'all' : 'month'
    localStorage.setItem('quickSearch', '')
    this.title = this.textEmployees
    // console.log(this.$store.state.User)
    // console.log(!this.$store.state.User.isReadOnlyAdmin)
  },
  data () {
    return {
      title: 'Members',
      dateRange: 'month',
      requestUrl: `/admin/mex/employer/employees/list`,
      downloadUrl: `/admin/mex/employer/employees/export`,
      columns: [],
      balanceFilter: false,
      filterOptions: [
        {
          value: 'filter[u.id=]',
          label: 'User ID'
        }, {
          value: 'filter[u.firstName=]',
          label: 'First Name'
        }, {
          value: 'filter[u.lastName=]',
          label: 'Last Name'
        }, {
          value: 'filter[u.email=]',
          label: 'Email'
        }, {
          value: 'filter[u.mobilephone=]',
          label: 'Phone'
        }, {
          value: 'filter[u.title=]',
          label: 'External Employee ID'
        }, {
          value: 'is_botm',
          label: 'Migrated to new card',
          options: [
            { label: 'Yes', value: 'yes' },
            { label: 'No', value: 'no' }
          ]
        }, {
          value: 'filter[u.status=]',
          label: 'Account Status',
          options: [
            { label: 'Active', value: 'active' },
            { label: 'Inactive', value: 'inactive' }
          ]
        }
      ],
      autoLoad: true,
      freezeColumn: -2,
      freezeColumnRight: 3,
      keyword: '',
      lastImport: null,
      reason: '',
      selectRow: null,
      status: null,
      changeStatusDialogModel: false,
      multiSelIdKey: 'Employee ID'
    }
  },
  methods: {
    async request ({ pagination }) {
      this.beforeRequest({ pagination })

      this.loading = true
      const data = this.$refs.filtersDialog.getFilters()
      data.keyword = this.keyword
      if (this.balanceFilter) {
        data.balanceFilter = true
      }
      const resp = await request(`/admin/mex/employer/employees/list/${pagination.page}/${pagination.rowsPerPage}`, 'get', data)
      this.loading = false
      if (resp.success) {
        this.pagination = pagination
        this.data = resp.data.data
        this.pagination.rowsNumber = resp.data.total
        this.quick = resp.data.quick
      }
    },
    initColumns () {
      this.balanceFilter = false
      let columns = this.$store.state.User.group && this.$store.state.User.group.fundingType === 'ACH' ? [
        'multi_check', 'User ID', 'Email', 'First Name', 'Last Name', 'User Type',
        'Create Date', 'Phone', 'External Employee ID', 'Address',
        'Account Status', 'Card Proxy-Value number', 'Card Status', 'App Activation', 'Actions'
      ] : [
        'multi_check', 'User ID', 'Email', 'First Name', 'Last Name', 'User Type',
        'Create Date', 'Phone', 'External Employee ID', 'Migrated to new card', 'Address',
        'Last Payout Date', 'Last Payout Amount',
        'Account Status', 'Actions'
      ]
      const hidden = ['multi_check']
      if (!this.user.subEmployers.length) {
        columns = _.filter(columns, c => {
          return !hidden.includes(c)
        })
      }
      this.columns = generateColumns(columns, [], {
        'User ID': 'u.id',
        'Email': 'u.email',
        'First Name': 'u.firstName',
        'Last Name': 'u.lastName'
      })
    },
    searchMember (data) {
      this.keyword = data
      this.reload()
    },
    statusClass (status) {
      const cls = []
      cls.push({
        'Active': 'positive',
        'KYC Failed': 'kyc-failed',
        'Invited': 'blue',
        'Inactive': 'warning',
        'OFAC Failed': 'kyc-failed',
        'KYC Failed (Scan)': 'kyc-failed',
        'On Hold': 'on-hold',
        'KYC (Scan Pending)': 'purple',
        'Onboarded': 'blue'
      }[status] || status)
      return cls
    },
    changeEmployer (row) {
      this.$root.$emit('show-mex-member-batch-move-dialog', {
        employers: this.user.subEmployers,
        members: [row['User ID']],
        isEmployer: true
      })
    },
    moveToAnotherEmployer () {
      this.$root.$emit('show-mex-member-batch-move-dialog', {
        employers: this.user.subEmployers,
        members: this.getMultiSelKeys(),
        isEmployer: true
      })
    },
    createMembers () {
      this.$root.$emit('show-faas-import-member-detail-dialog')
    },
    async changeCardStatus (row, status) {
      this.$q.loading.show()
      const resp = await this.c.request(`/admin/mex/employee/${row['User ID']}/change-card-status`, 'post', {
        cardStatus: status
      })
      this.$q.loading.hide()
      if (resp.success) {
        await this.reload()
      }
    },
    payEmployee (row) {
      this.$root.$emit('show-mex-employee-pay-dialog')
    },
    pay (row) {
      if (!row['SkipCheckCardStatus'] && row['Card Status'] !== 'Active' && this.$store.state.User.cpKey !== 'cp_faas') {
        notifySuccess('Please assign and enroll/activate a Card first.', 'Message', 'negative')
        return
      }
      this.$root.$emit('show-mex-employee-pay-dialog', row)
    },
    view (row) {
      window.open(`/admin#/h/mex/employer/employee/${row['User ID']}`, '_blank')
    },
    viewHighBalanceReport () {
      window.open(`/admin#/h/mex/employer/reports/high_balance`, '_blank')
    },
    editDeposit (row) {
      this.$root.$emit('show-mex-employee-edit-last-deposit-dialog', row)
    },
    viewErrors (row, type = null) {
      row.type = type
      this.$root.$emit('show-faas-import-member-error-dialog', row)
    },
    edit (row) {
      this.$root.$emit('show-faas-member-detail-dialog', row)
    },
    onCancel () {
      this.changeStatusDialogModel = false
    },
    uploadDdZip (type = false) {
      this.$root.$emit('show-mex-employer-dd-dialog', {
        w2: type
      })
    },
    async onOk () {
      if (!this.reason) {
        return notify('The reason is required!', 'negative')
      }
      this.$q.loading.show()
      const resp = await this.c.request(`/admin/faas/base/members/${this.selectRow['User ID']}/change-status`, 'post', {
        status: this.status,
        reason: this.reason
      })
      this.$q.loading.hide()
      this.changeStatusDialogModel = false
      if (resp.success) {
        await this.reload()
      }
    },
    replaceCard (row) {
      this.$q.dialog({
        title: 'Replace Card',
        message: `Are you sure that you want to create a new card? The old card will be suspended. The balance on the old card will be transferred to the new card. The user address is: "${row['Address']}".`,
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        const resp = await this.c.request(`/admin/faas/base/members/${row['User ID']}/replace-card`, 'post')
        this.$q.loading.hide()
        if (resp.success) {
          notify(resp)
          await this.reload()
        }
      }).catch(() => {})
    },
    viewDeposit (row) {
      row.isEmployer = true
      this.$root.$emit('show-mex-common-deposit-dialog', row)
    },
    changeStatus (row, status) {
      this.changeStatusDialogModel = true
      this.selectRow = row
      this.status = status
      // let message = ''
      // if (status === 'Closed') {
      //   message = '. And do you want to set the card to inactive and sweep the funds back to the master funding account.'
      // }
      // this.$q.dialog({
      //   title: 'Change Status',
      //   message: `Please enter the reason why you want to change ${fullName(row)}'s the status to "${status}" ${message}:`,
      //   cancel: {
      //     color: 'negative',
      //     flat: true,
      //     noCaps: true
      //   },
      //   ok: {
      //     color: 'positive',
      //     label: 'Submit',
      //     noCaps: true
      //   },
      //   prompt: {
      //     model: ''
      //   }
      // }).then(async reason => {
      //   if (!reason) {
      //     return notify('The reason is required!', 'negative')
      //   }
      //   this.$q.loading.show()
      //   const resp = await this.c.request(`/admin/faas/base/members/${row['User ID']}/change-status`, 'post', {
      //     status: status,
      //     reason: reason
      //   })
      //   this.$q.loading.hide()
      //   if (resp.success) {
      //     await this.reload()
      //   }
      // }).catch(() => {})
    }
  }
}
</script>
<style lang="scss">
#mex__employees__index_page {
  .page-header,
  .main-table {
    margin: 0 10px;
  }
  .value {
    display: flex;
    justify-content: space-between;
    .view {
      font-weight: normal;
      font-size: 12px;
      color: var(--span-color-primary);
      cursor: pointer;
    }
  }
  .pay-employee-btn {
    background-color: var(--span-color-primary) !important;
    color: #fff;
    .q-btn-inner {
      font-size: 15px !important;
    }
    img {
      margin-right: 5px;
    }
  }
  .export-btn {
    background-color: #190644;
    color: #fff;
    .q-btn-inner {
      font-size: 15px !important;
    }
  }
  .import-member-btn {
    background-color: #4b00e4;
    color: #fff;
    .q-btn-inner {
      font-size: 15px !important;
    }
  }
  .gutter-sm {
    margin-left: -6px;
  }
  .icon-item {
    width: 50px;
    padding: 14px;
    display: flex;
    align-items: center;
    border-radius: 25px;
  }
  .total-employee,
  .total-balance {
    background: rgba($color: #4acc3d, $alpha: 0.1);
  }
  .ave-balance {
    background: rgba($color: #ff974a, $alpha: 0.1);
  }
  .q-if.dense {
    padding: 8px !important;
  }
  .positive {
    color: #4acc3d;
    background: rgba($color: #4acc3d, $alpha: 0.1) !important;
  }
  .blue {
    color: #005daa;
    background: rgba($color: #005daa, $alpha: 0.1) !important;
  }
  .warning {
    color: #f16501;
    background: rgba($color: #f16501, $alpha: 0.1) !important;
  }
  .on-hold {
    color: #9b71fc;
    background: rgba($color: #9b71fc, $alpha: 0.1) !important;
  }
  .kyc-failed {
    color: #f10013;
    background: rgba($color: #f10013, $alpha: 0.1) !important;
  }
}
</style>
