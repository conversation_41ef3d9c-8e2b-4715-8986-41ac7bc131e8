<template>
  <q-dialog class="mex-employer-admin-detail-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="font-16 mb-2">{{ edit ? 'Edit' : 'Create' }} New Admin</div>
      <div class="font-12 normal text-dark">Please fill in the information below about the admin.</div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5">
        <div class="col-sm-6">
          <q-input float-label="First Name"
                   autocomplete="no"
                   :error="$v.entity['First Name'].$error"
                   @blur="$v.entity['First Name'].$touch"
                   v-model="entity['First Name']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Last Name"
                   autocomplete="no"
                   :error="$v.entity['Last Name'].$error"
                   @blur="$v.entity['Last Name'].$touch"
                   v-model="entity['Last Name']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Email"
                   autocomplete="no"
                   :error="$v.entity['Email'].$error"
                   @blur="$v.entity['Email'].$touch"
                   v-model="entity['Email']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Mobile Phone Number"
                   autocomplete="no"
                   :error="$v.entity['Mobile Phone'].$error"
                   @blur="$v.entity['Mobile Phone'].$touch"
                   v-model="entity['Mobile Phone']"></q-input>
        </div>
        <div class="col-sm-6 pt-15 text-left">
          <q-checkbox label="Read Only Admins"
                      autocomplete="no"
                      v-model="entity['Read Only']"></q-checkbox>
        </div>
        <div v-if="!entity['Read Only']"
             class="col-sm-6 pt-15 text-left">
          <q-checkbox label="Manage Admins"
                      autocomplete="no"
                      v-model="entity['Manage Admins']"></q-checkbox>
        </div>
        <div v-if="!entity['Read Only']"
             class="col-sm-6 pt-15 text-left">
          <q-checkbox label="Receive System Message"
                      autocomplete="no"
                      v-model="entity['Receive System Message']"></q-checkbox>
        </div>
        <div class="col-sm-6 text-left pt-20"
             v-show="edit">
          <div class="bold">Status</div>
          <div class="mt-8">
            <q-radio v-model="entity['Status']"
                     color="blue"
                     :error="$v.entity['Status'].$error"
                     @change="$v.entity['Status'].$touch"
                     val="Active"
                     label="Active"></q-radio>
            <q-radio v-model="entity['Status']"
                     color="blue"
                     class="ml-15"
                     :error="$v.entity['Status'].$error"
                     @change="$v.entity['Status'].$touch"
                     val="Archived"
                     label="Archived"></q-radio>
          </div>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <div class="stacks"
           v-if="edit">
        <div class="row"
             v-if="origin">
          <q-btn :label="origin['Status'] === 'Active' ? 'Archive' : 'Activate'"
                 no-caps
                 :color="origin['Status'] === 'Active' ? 'orange' : 'blue'"
                 @click="toggleStatus" />
          <q-btn label="Save Changes"
                 no-caps
                 color="positive"
                 @click="save" />
        </div>
        <q-btn label="Cancel"
               no-caps
               color="grey-3"
               text-color="tertiary"
               @click="_hide" />
      </div>
      <template v-else>
        <q-btn label="Cancel"
               no-caps
               color="grey-3"
               text-color="tertiary"
               @click="_hide" />
        <q-btn label="Create Admin"
               no-caps
               color="positive"
               class="main"
               @click="save" />
      </template>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../../mixins/Singleton'
import {
  notifyForm,
  notify,
  request
} from '../../../../common'
import {
  required,
  email,
  helpers
} from 'vuelidate/lib/validators'
import _ from 'lodash'
const alpha = helpers.regex('alpha', /^[0-9+-\s]*$/)

export default {
  name: 'mex-employer-admin-detail-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      defaultEntity: {
        'Status': 'Active',
        'Manage Admins': false,
        'Read Only': false,
        'Receive System Message': false
      }
    }
  },
  computed: {
    edit () {
      return this.entity['User ID']
    }
  },
  validations: {
    entity: {
      'First Name': {
        required
      },
      'Last Name': {
        required
      },
      'Email': {
        required,
        email
      },
      'Mobile Phone': {
        required,
        alpha
      },
      'Status': {
        required
      }
    }
  },
  methods: {
    onSuccess (resp) {
      notify(resp.message)

      if (this.origin && this.origin['User ID']) {
        _.assignIn(this.origin, resp.data)
      }

      this.$root.$emit('reload-mex-employer-admins')
      this._hide()
    },
    async save () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }
      if (this.entity['Read Only']) {
        this.entity['Manage Admins'] = false
        this.entity['Receive System Message'] = false
      }
      this.$q.loading.show()
      const resp = await request(`/admin/mex/employer/admins/save`, 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        this.onSuccess(resp)
      }
    },
    async toggleStatus () {
      if (!this.origin) {
        return
      }
      this.$q.loading.show()
      const resp = await request(`/admin/mex/employer/admins/${this.entity['User ID']}/toggle-status`, 'post', {
        Status: this.origin['Status'] === 'Active' ? 'Archived' : 'Active'
      })
      this.$q.loading.hide()
      if (resp.success) {
        this.onSuccess(resp)
      }
    }
  }
}
</script>

<style lang="scss">
.mex-employer-admin-detail-dialog {
  .modal-content {
    width: 550px;
  }
}
</style>
