<template>
  <q-page id="mex__employer_admins__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-select v-model="selectedCp"
                  class="dense"
                  stack-label="Card Program"
                  :options="cardPrograms"></q-select>
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :default="dateRange"
                         :ranges="dateRanges"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm">
        <div class="col-sm-3 col-12">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-account-outline"></q-icon>
                <div class="column">
                  <div class="value">{{ quick.total || 0 }}</div>
                  <div class="description">Total Admins</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
               :columns="filteredVisibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong>{{ title }} Report</strong>
        </template>
        <template slot="top-right">
          <q-btn icon="mdi-account-plus-outline"
                 color="positive"
                 label="Create New Admin"
                 @click="add"
                 class="btn-sm mr-10"
                 no-caps></q-btn>
          <q-btn icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>

          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Status'">
              <q-chip class="font-12"
                      :class="props.row['Status'] === 'Active' ? 'positive' : 'negative'">
                {{ props.row['Status'] }}
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Actions'">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          @click.native="edit(props.row)">
                    <q-item-main>Edit</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="changeStatus(props.row, 'Active')"
                          v-if="props.row['Status'] === 'Archived'">
                    <q-item-main>Change to Active</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="changeStatus(props.row, 'Archived')"
                          v-else>
                    <q-item-main>Change to Archived</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="del(props.row)">
                    <q-item-main>Delete</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="resetFA(props.row)">
                    <q-item-main>Reset 2FA</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="masterAdmin"
                          @click.native="$c.loginAs(props.row['User ID'])">
                    <q-item-main>Login As</q-item-main>
                  </q-item>
                   <q-item v-close-overlay
                          v-if="employerAdmin && !user.currentEmployer"
                          @click.native="configSubCompany(props.row)">
                    <q-item-main>Config the Sub Company</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>

    <DetailDialog></DetailDialog>
    <SetSubCompanyDialog></SetSubCompanyDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns, request } from '../../../../common'
import MexPageMixin from '../../../../mixins/mex/MexPageMixin'
import DetailDialog from './detail'
import FreezeColumnMixin from '../../../../mixins/FreezeColumnMixin'
import SetSubCompanyDialog from './setSubCompany.vue'
export default {
  name: 'mex-employer-admins',
  mixins: [
    MexPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-mex-employer-admins')
  ],
  components: {
    DetailDialog,
    SetSubCompanyDialog
  },
  data () {
    return {
      title: 'Admins',
      requestUrl: `/admin/mex/employer/admins/list`,
      downloadUrl: `/admin/mex/employer/admins/export`,
      columns: generateColumns([
        'Create Date', 'User ID', 'First Name', 'Last Name',
        'Email', 'Mobile Phone', 'Last Login Time',
        'Status', 'Actions'
      ], [], {
        'Create Date': 'u.createdAt',
        'User ID': 'u.id',
        'First Name': 'u.firstName',
        'Last Name': 'u.lastName',
        'Email': 'u.email',
        'Mobile Phone': 'u.mobilephone',
        'Last Login Time': 'u.lastLogin'
      }),
      filterOptions: [
        {
          value: 'filter[u.id=]',
          label: 'User ID'
        }, {
          value: 'filter[u.firstName=]',
          label: 'First Name'
        }, {
          value: 'filter[u.lastName=]',
          label: 'Last Name'
        }, {
          value: 'filter[u.email=]',
          label: 'Email'
        }, {
          value: 'filter[u.mobilephone=]',
          label: 'Mobile Phone'
        }, {
          value: 'filter[u.status=]',
          label: 'Status',
          options: [
            { label: 'Active', value: 'active' },
            { label: 'Archived', value: 'closed' }
          ]
        }
      ],
      keyword: '',
      autoLoad: true,
      freezeColumn: -1,
      freezeColumnRight: 2
    }
  },
  methods: {
    add () {
      this.$root.$emit('show-mex-employer-admin-detail-dialog')
    },
    edit (row) {
      this.$root.$emit('show-mex-employer-admin-detail-dialog', row)
    },
    del (row) {
      this.$q.dialog({
        title: 'Confirm',
        message: 'Are you sure that you want to delete this admin?',
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.loading = true
        const resp = await request(`/admin/mex/employer/admins/${row['User ID']}/del`, 'post')
        this.loading = false
        if (resp.success) {
          this.reload()
        }
      }).catch(() => {})
    },
    async changeStatus (row, status) {
      this.$q.loading.show()
      const resp = await this.c.request(`/admin/mex/employer/admins/${row['User ID']}/toggle-status`, 'post', {
        Status: status
      })
      this.$q.loading.hide()
      if (resp.success) {
        this.reload()
      }
    },
    async resetFA (row) {
      this.$q.loading.show()
      const resp = await this.c.request(`/admin/mex/employer/admins/${row['User ID']}/resetFA`, 'post')
      this.$q.loading.hide()
      if (resp.success) {
        this.reload()
      }
    },
    configSubCompany (row) {
      this.$root.$emit('show-set-sub-company-dialog', row)
    }
  }
}
</script>
