<template>
  <q-page id="mex__payments__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :default="dateRange"
                         :ranges="dateRanges"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div class="row">
        <div class="col-12">
          <balanceChart v-model="chartDateRange"
                        :viewReportFlag='false'></balanceChart>
        </div>
      </div>
      <q-table :data="data"
               :columns="filteredVisibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <strong class="font-24">{{ title }} Report</strong>
        </template>
        <template slot="top-right">
          <q-btn icon="mdi-file-download-outline"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8 export-btn"
                 no-caps></q-btn>
          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10 font-18"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="font-18"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'Status'">
              <q-chip class="font-12"
                      :class="statusClass(props.row['Status'])">
                <span v-if="props.row['Status'] === 'init'">Pending</span>
                <span v-else-if="props.row['Status'] === 'Executed'">Completed</span>
                <span v-else-if="props.row['Status'] === 'Reversed'">Reversed</span>
                <span v-else-if="props.row['Status'] === 'canceled'">Canceled</span>
                <span v-else-if="props.row['Status'] === 'Duplicated'">Duplicated</span>
              </q-chip>
            </template>
            <template v-else-if="col.field === 'Transfer To'">
              <span v-if="(props.row['Role Type'] ==='Member' && props.row['Employee']) || (props.row['Role Type'] ==='Employee' && props.row['Employee'] ) || props.row['Status'] === 'init'"
                    class="font-12 view-btn"
                    @click="viewEmployee(props.row)">
                {{ props.row['Transfer To'] }}
              </span>
              <span v-else>{{props.row['Transfer To']}}</span>
            </template>
            <template v-else-if="col.field === 'Actions'">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          v-if="!$store.state.User.isReadOnlyAdmin && (props.row['Status'] === 'Duplicated' && (props.row['Payment Type'] === 'Employee Payout' || props.row['Payment Type'] === 'Member Payout'))"
                          @click.native="reExecuteOrComplete(props.row, 'repeat')">
                    <q-item-main>Execute</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="!$store.state.User.isReadOnlyAdmin && ((props.row['Status'] === 'init' || props.row['Status'] === 'Duplicated') && (props.row['Payment Type'] === 'Employee Payout' || props.row['Payment Type'] === 'Member Payout'))"
                          @click.native="reverse(props.row)">
                    <q-item-main>Cancel</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="$store.state.User.isMasterLogin && props.row['isExecuted'] && props.row['Status'] === 'init' && props.row['Payment Type'] === 'Employee Payout'"
                          @click.native="reExecuteOrComplete(props.row, 'execute')">
                    <q-item-main>Re-execute</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="$store.state.User.isMasterLogin && props.row['isExecuted'] && props.row['Status'] === 'init' && props.row['Payment Type'] === 'Employee Payout'"
                          @click.native="reExecuteOrComplete(props.row, 'complete')">
                    <q-item-main>Complete</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-else-if="!$store.state.User.isReadOnlyAdmin && (props.row['Status'] === 'Executed' && (props.row['Payment Type'] === 'Employee Payout' || props.row['Payment Type'] === 'Member Payout'))"
                          @click.native="reverse(props.row)">
                    <q-item-main>Reverse</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
    <Reversed></Reversed>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns, request, notify } from '../../../../common'
import MexPageMixin from '../../../../mixins/mex/MexPageMixin'
import FreezeColumnMixin from '../../../../mixins/FreezeColumnMixin'
import balanceChart from '../dashboard/balance'
import Reversed from './reversed'

export default {
  name: 'mex-employers-payment',
  mixins: [
    MexPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    EventHandlerMixin('reload-mex-employers-payment')
  ],
  components: {
    balanceChart,
    Reversed
  },
  mounted () {
    this.title = this.user.cpKey === 'cp_mex' ? 'Payments & Deposits' : 'Deposits & Payouts'
    if (this.user.cpKey === 'cp_faas') {
      this.dateRanges = [
        {
          label: 'Month',
          value: 'month'
        },
        {
          label: 'Week',
          value: 'week'
        },
        {
          label: 'All',
          value: 'all'
        },
        {
          label: 'Custom',
          value: 'custom_range'
        }
      ]
    }
    this.paymentType = localStorage.getItem('paymentType') ? localStorage.getItem('paymentType') : ''
    localStorage.setItem('paymentType', '')
    if (this.paymentType) {
      this.filters.push({
        'field': 'paymentType',
        'predicate': '=',
        'value': this.paymentType
      })
    }
  },
  data () {
    return {
      title: 'Payments & Deposits',
      chartDateRange: 'month',
      dateRange: 'month',
      paymentType: '',
      requestUrl: `/admin/mex/employer/payments/list`,
      downloadUrl: `/admin/mex/employer/payments/export`,
      columns: generateColumns([
        'Date & Time', 'Payment Type', 'Transfer To', 'Email', 'First Name', 'Last Name', 'External Employee ID', 'Role Type', 'Amount',
        'Status', 'Actions'
      ], [], {
        'Date & Time': 'uct.txnTime',
        'Amount': 'uct.txnAmount'
      }),
      filterOptions: [
        {
          value: 'paymentType',
          label: 'Payment Type',
          options: [
            { label: 'Employee Reversal', value: 'reversed' },
            { label: 'Employee Payment', value: 'payout' },
            { label: 'Employer Deposit', value: 'deposit' }
          ]
        },
        {
          value: 'transferTo',
          label: 'Transfer To'
        },
        {
          value: 'firstName',
          label: 'First Name'
        }, {
          value: 'lastName',
          label: 'Last Name'
        }, {
          value: 'email',
          label: 'Email'
        },
        {
          value: 'title',
          label: 'External Employee ID'
        },
        {
          value: 'roleType',
          label: 'Role Type',
          options: [
            { label: 'Employee', value: 'employee' },
            { label: 'Employer', value: 'employer' }
          ]
        },
        {
          value: 'accountStatus',
          label: 'Status',
          options: [
            { label: 'Canceled', value: 'canceled' },
            { label: 'Pending', value: 'init' },
            { label: 'Completed', value: 'Executed' },
            { label: 'Reversed', value: 'Reversed' },
            { label: 'Duplicated', value: 'Duplicated' }
          ]
        },
        {
          value: 'date_range',
          label: 'Date & Time',
          range: [{
            value: 'range[uct.txnTime][start]',
            type: 'date'
          }, {
            value: 'range[uct.txnTime][end]',
            type: 'date'
          }]
        }
      ],
      autoLoad: true,
      freezeColumn: -2,
      freezeColumnRight: 2,
      keyword: ''
    }
  },
  methods: {
    async reload () {
      let data = {}
      if (this.$refs && this.$refs.dateRangeFilter) {
        data = this.$refs.dateRangeFilter.params()
      }
      this.chartDateRange = data.period
      await this.request({
        pagination: this.pagination
      })
      this.everLoaded = true
    },
    statusClass (status) {
      const cls = []
      cls.push({
        'Reversed': 'negative',
        'init': 'pending',
        'Executed': 'positive',
        'Duplicated': 'negative'
      }[status] || status)
      return cls
    },
    reverse (row) {
      this.$root.$emit('show-mex-payment-reversed-dialog', row)
    },
    viewEmployee (row) {
      window.open(`/admin#/h/mex/employer/employee/${row['Transfer To']}`, '_blank')
    },
    async reExecuteOrComplete (row, type) {
      this.$q.dialog({
        title: 'Confirm',
        message: `Are you sure that you want to ${type} the payment (pay ${row.Amount} to ${row['First Name']} ${row['Last Name']})?`,
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.$q.loading.show()
        // console.log('sadfgrewsdf')
        const resp = await request(`/admin/mex/employer/payments/re-execute-complote`, 'post', {
          id: row['ID'],
          type: type
        })
        // console.log('sadfgrewsdf12345421')
        this.$q.loading.hide()
        if (resp) {
          notify()
          this.reload()
        }
      }).catch(() => {})
    }
  }
}
</script>
<style lang="scss">
#mex__payments__index_page {
  .page-header,
  .main-table {
    margin: 0 10px;
  }
  .export-btn {
    background-color: #190644;
    color: #fff;
    .q-btn-inner {
      font-size: 15px !important;
    }
  }
  .q-if.dense {
    padding: 8px !important;
  }
  .q-chip.pending {
    color: #ff9f00;
    background-color: rgba($color: #ff9f00, $alpha: 0.1);
  }
  .view-btn {
    color: #0062ff;
    cursor: pointer;
    text-decoration: underline;
  }
}
</style>
