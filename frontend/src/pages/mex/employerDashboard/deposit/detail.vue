<template>
  <q-dialog class="mex-employer-deposit-detail-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="font-16 mb-2">Add New Deposit</div>
      <div class="font-12 normal text-dark">Please fill in the information below about the deposit.</div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5">
        <div class="col-12">
          <q-input float-label="Amount"
                   autocomplete="no"
                   prefix="$"
                   min="0"
                   :error="$v.entity['Amount'].$error"
                   @blur="$v.entity['Amount'].$touch"
                   v-model="entity['Amount']"></q-input>
        </div>
        <div class="col-12">
          <q-input float-label="Trace #"
                   autocomplete="no"
                   :error="$v.entity['Trace ID'].$error"
                   @blur="$v.entity['Trace ID'].$touch"
                   v-model="entity['Trace ID']"></q-input>
        </div>
        <div class="pt-20 col-12">
          <q-datetime type="date"
                      format="MM/DD/YYYY"
                      :min="minDate"
                      placeholder='Select Posting Date'
                      :error="$v.entity['Post Date'].$error"
                      @blur="$v.entity['Post Date'].$touch"
                      v-model="entity['Post Date']"></q-datetime>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <template>
        <q-btn label="Cancel"
               no-caps
               color="grey-3"
               text-color="tertiary"
               @click="_hide" />
        <q-btn label="Save"
               no-caps
               color="positive"
               class="main"
               @click="save" />
      </template>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../../mixins/Singleton'
import {
  notifyForm,
  notify,
  request
} from '../../../../common'
import {
  required
} from 'vuelidate/lib/validators'
import { date } from 'quasar'
const { addToDate } = date

export default {
  name: 'mex-employer-deposit-detail-dialog',
  mixins: [
    Singleton
  ],
  data () {
    return {
      defaultEntity: {
        'Amount': 0,
        'Trace ID': null,
        'Post Date': null
      },
      minDate: null
    }
  },
  validations: {
    entity: {
      'Amount': {
        required
      },
      'Trace ID': {
        required
      },
      'Post Date': {
        required
      }
    }
  },
  methods: {
    init () {
      let today = new Date()
      this.minDate = addToDate(today, { days: 0 })
      this.entity['Amount'] = 0
      this.entity['Trace ID'] = ''
      this.entity['Post Date'] = null
    },
    show () {
      this.init()
    },
    onSuccess (resp) {
      this.init()
      notify(resp.message)
      this.$root.$emit('reload-mex-employer-deposit')
      this._hide()
    },
    async save () {
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }
      this.$q.loading.show()
      const resp = await request(`/admin/mex/employer/deposit/save`, 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        this.onSuccess(resp)
      }
    }
  }
}
</script>

<style lang="scss">
.mex-employer-deposit-detail-dialog {
  .modal-content {
    width: 550px;
  }
}
</style>
