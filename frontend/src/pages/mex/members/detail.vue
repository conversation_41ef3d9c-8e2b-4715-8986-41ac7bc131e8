<template>
  <q-dialog class="mex-member-detail-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="font-16 mb-2">{{ edit ? 'Edit Member' : 'Create New Member' }}</div>
      <div class="font-12 normal text-dark">Please fill in the information below about the member.</div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5">
        <div class="col-sm-12">
          <q-select :float-label="user.cpKey === 'cp_faas' ? 'Client' : 'Employer'"
                    autocomplete="no"
                    :options="employers"
                    :error="$v.entity['Employer'].$error"
                    @change="$v.entity['Employer'].$touch"
                    v-model="entity['Employer']"></q-select>
        </div>
        <div class="col-sm-6">
          <q-input float-label="First Name"
                   autocomplete="no"
                   :error="$v.entity['First Name'].$error"
                   @input="$v.entity['First Name'].$touch"
                   v-model="entity['First Name']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Last Name"
                   autocomplete="no"
                   :error="$v.entity['Last Name'].$error"
                   @input="$v.entity['Last Name'].$touch"
                   v-model="entity['Last Name']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Sec Last Name"
                   autocomplete="no"
                   v-model="entity['Sec Last Name']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Email"
                   autocomplete="no"
                   :error="$v.entity['Email'].$error"
                   @input="$v.entity['Email'].$touch"
                   v-model="entity['Email']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Phone Number"
                   autocomplete="no"
                   :error="$v.entity['Phone'].$error"
                   @input="$v.entity['Phone'].$touch"
                   v-model="entity['Phone']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Gov ID (SSN/CURP)"
                   autocomplete="no"
                   :error="$v.entity['CURP ID'].$error"
                   @input="$v.entity['CURP ID'].$touch"
                   :readonly="!$store.getters['User/masterAdmin'] && entity['isAllKycPassed'] && origin && !!origin['CURP ID']"
                   v-model="entity['CURP ID']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-datetime float-label="Date of Birth"
                      autocomplete="no"
                      type="date"
                      format="MM/DD/YYYY"
                      :error="$v.entity['Date of Birth'].$error"
                      :readonly="!$store.getters['User/masterAdmin'] && !$store.getters['User/transferMexAdmin'] && entity['isAllKycPassed']"
                      @change="$v.entity['Date of Birth'].$touch"
                      v-model="entity['Date of Birth']"></q-datetime>
        </div>
        <div class="col-sm-6">
          <q-input float-label="External Employee ID"
                   autocomplete="no"
                   v-model="entity['External Employee ID']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Last Four SSN"
                   autocomplete="no"
                   v-model="entity['Last Four SSN']"></q-input>
        </div>
        <div class="col-sm-6 flex">
          <q-checkbox label="Opt-in Disclaimer"
                      autocomplete="no"
                      toggle-indeterminate
                      v-model="entity['Opt-in Disclaimer']"></q-checkbox>
        </div>
        <div class="form-field-title">Residential Address</div>
        <div class="col-sm-12">
          <q-input float-label="Street Address"
                   autocomplete="no"
                   :error="$v.entity['Street Address'].$error"
                   @input="$v.entity['Street Address'].$touch"
                   v-model="entity['Street Address']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="City"
                   autocomplete="no"
                   :error="$v.entity['City'].$error"
                   @input="$v.entity['City'].$touch"
                   v-model="entity['City']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Postal Code"
                   autocomplete="no"
                   :error="$v.entity['Postal Code'].$error"
                   @input="$v.entity['Postal Code'].$touch"
                   v-model="entity['Postal Code']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-select float-label="Country"
                    autocomplete="no"
                    :options="countries"
                    filter
                    autofocus-filter
                    :error="$v.entity['CountryId'].$error"
                    @change="$v.entity['CountryId'].$touch"
                    v-model="entity['CountryId']"></q-select>
        </div>
        <div class="col-sm-6">
          <q-select float-label="State / Province"
                    autocomplete="no"
                    :options="states"
                    filter
                    autofocus-filter
                    :error="$v.entity['StateId'].$error"
                    :before="stateBefore"
                    @change="$v.entity['StateId'].$touch"
                    v-model="entity['StateId']"></q-select>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <div class="stacks">
        <div class="kyc-box"
             v-if="edit && entity.kycStep">
          <div class="form-field-title">KYC Status</div>
          <q-chip class="font-12 mv-5"
                  :class="kycStatusClass">{{ entity.kycStep }}</q-chip>
          <Statuses :statuses="entity.kycStatuses"></Statuses>
          <div class="mv-10 text-negative"
               v-if="kycFailType">
            <strong>{{ kycFailType }}</strong>: {{ kycFailMsg }}
          </div>
          <div v-if="entity.kycApprover"
               class="font-12 text-faded">
            <span>Manually approved by </span>
            <span notranslate="">{{ entity.kycApprover }} @ </span>
            <span>{{ entity.kycApprovedAt }}</span>
          </div>

          <q-btn label="Start KYC Verification"
                 color="blue"
                 no-caps
                 @click="startKyc('ofac')"
                 v-if="entity.kycStep === 'KYC Not Started'" />
          <div class="row"
               v-else-if="['KYC Failed (OFAC)', 'KYC Failed (OFAC & Scan)'].includes(entity.kycStep)">
            <q-btn label="Recheck OFAC"
                   color="orange"
                   no-caps
                   @click="startKyc('ofac')"></q-btn>
            <q-btn label="Manually Approve"
                   color="blue"
                   no-caps
                   class="main"
                   v-if="entity.kycStatuses && entity.kycStatuses['ID Scan'] !== null"
                   @click="manualKyc"></q-btn>
          </div>
          <q-btn label="Start ID Scan"
                 color="blue"
                 no-caps
                 @click="startIdScan"
                 class="ph-50"
                 v-else-if="entity.kycStep === 'KYC (Scan Pending)' && !isBotmUser" />
          <div class="row"
               v-else-if="entity.kycStep === 'KYC Failed (Scan)' && !isBotmUser">
            <q-btn label="Rescan ID"
                   color="orange"
                   no-caps
                   @click="startIdScan"></q-btn>
            <q-btn label="Manually Approve"
                   color="blue"
                   no-caps
                   class="main"
                   @click="manualKyc"></q-btn>
          </div>
        </div>
        <div class="row">
          <q-btn :label="edit ? 'Cancel' : 'Back'"
                 no-caps
                 color="grey-3"
                 text-color="tertiary"
                 @click="cancel" />
          <q-btn :label="edit ? 'Save Changes' : 'Next Step'"
                 no-caps
                 color="positive"
                 class="main"
                 @click="save" />
        </div>
      </div>
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { notifyForm, notify, request, EventHandlerMixin } from '../../../common'
import { required, email, helpers } from 'vuelidate/lib/validators'
import _ from 'lodash'
import MexStateListMixin from '../../../mixins/mex/MexStateListMixin'
import Statuses from './id_status'

const alpha = helpers.regex('alpha', /^[0-9+-\s]*$/)
const postalCodeUS = helpers.regex('Must be 5 digits', /^[0-9]{5}(?:-[0-9]{4})?$/)
const postalCode = helpers.regex('Must be digits or letters', /^[a-zA-Z0-9-]*$/)

const validations = {
  entity: {}
}
for (const field of [
  'First Name', 'Last Name', 'Email',
  'Employer', 'CURP ID', 'Date of Birth',
  'Street Address', 'CountryId', 'StateId',
  'City', 'Postal Code'
]) {
  validations.entity[field] = { required }
}
validations.entity['Email'] = { required, email }
validations.entity['Phone'] = { alpha }
export default {
  name: 'mex-member-detail-dialog',
  mixins: [
    Singleton,
    MexStateListMixin,
    EventHandlerMixin('save-mex-member-detail', 'doSave')
  ],
  components: {
    Statuses
  },
  data () {
    return {
      defaultEntity: {
        'Member ID': 0,
        'isCreatedUniTellerUser': false
      },
      employers: [],
      verifying: false,
      isBotmUser: false
    }
  },
  computed: {
    edit () {
      return this.entity['Member ID']
    },
    kycStatusClass () {
      const step = this.entity.kycStep
      if (!step || step === 'KYC Not Started') {
        return 'dark'
      }
      if (['KYC Passed', 'KYC Manually Approved'].includes(step)) {
        return 'positive'
      }
      return 'negative'
    },
    kycFailType () {
      let type = null
      _.forEach(this.entity.kycStatuses || {}, (v, k) => {
        if (v && v !== true) {
          type = k
          return false
        }
      })
      return type
    },
    kycFailMsg () {
      let msg = null
      _.forEach(this.entity.kycStatuses || {}, v => {
        if (v && v !== true) {
          msg = v
          return false
        }
      })
      return msg
    },
    user () {
      return this.$store.state.User
    }
  },
  validations,
  watch: {
    'entity.Employer': {
      immediate: true,
      handler () {
        _.forEach(this.employers, (v, k) => {
          if (v.value === this.entity.Employer) {
            this.isBotmUser = v.isBotm
          }
        })
        if (this.isBotmUser) {
          validations.entity['CURP ID'] = {}
        } else {
          validations.entity['CURP ID'] = { required }
        }
      }
    }
  },
  methods: {
    async show () {
      this.verifying = false
      this.$q.loading.show()
      const resp = await request(`/admin/mex/members/${this.entity['Member ID']}/detail`)
      this.$q.loading.hide()
      if (resp.success) {
        this.entity = _.assignIn({}, this.entity, resp.data.entity)
        this.employers = resp.data.employers || []

        const startKyc = this.entity.startKyc
        delete this.entity.startKyc
        if (startKyc) {
          this.startKyc()
        }
      }
    },
    onSuccess (resp) {
      if (this.verifying) {
        return
      }

      notify(resp.message)

      this.$root.$emit('reload-mex-members')
      this.$root.$emit('reload-mex-members-profile')
      this.$root.$emit('reload-mex-members-profile-notes')

      if (!this.edit) {
        this.visible = false

        setTimeout(() => {
          resp.data.startKyc = true
          this._show(resp.data)
        }, 200)
      } else {
        this._hide()
      }
    },
    async save () {
      if (this.entity['CountryId'] === 232) {
        validations.entity['Postal Code'] = { required, postalCodeUS }
      } else {
        validations.entity['Postal Code'] = { required, postalCode }
      }

      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }
      if (this.requireMfa()) {
        this.$root.$emit('show-prompt-dialog', {
          title: 'Reason',
          message: 'Please enter the reason for changing member details:',
          callback: text => {
            if (text) {
              this.entity.changeReason = text
              this.origin.smsType = 'editDetail'
              this.$root.$emit('show-mex-common-sms-dialog', this.origin)
            } else {
              notify('Operation canceled.', 'negative')
            }
          }
        })
      } else {
        await this.doSave()
      }
    },
    async doSave () {
      this.$q.loading.show({
        message: 'Saving changes...'
      })
      const resp = await request(`/admin/mex/members/save`, 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        this.onSuccess(resp)
      }
    },
    requireMfa () {
      if (this.$store.getters['User/masterAdmin']) {
        return false
      }
      const fields = [
        'Email',
        'Phone'
      ]
      let need = false
      for (const field of fields) {
        if (this.entity[field] !== this.origin[field]) {
          need = true
          break
        }
      }
      return need
    },
    cancel () {
      if (!this.edit) {
        setTimeout(() => {
          this.$root.$emit('show-mex-member-import-dialog')
        }, 200)
      }
      this._hide()
    },
    async startKyc (type, param) {
      this.$nextTick(() => {
        this.$el.querySelector('.modal-content').scrollTop = 10000
      })

      this.verifying = true
      if (this.$v.$anyDirty) {
        await this.save()
        if (this.$v.$invalid) {
          this.verifying = false
          return
        }
      }
      if (!type) {
        if (this.entity.kycStatuses['OFAC'] !== true) {
          type = 'ofac'
        } else {
          return
        }
      }
      const oldStep = this.entity.kycStep
      this.$q.loading.show({
        message: 'Verifying ' + type.toUpperCase()
      })
      const resp = await request(`/admin/mex/members/${this.entity['Member ID']}/id-${type}`, 'post', {
        param
      })
      this.$q.loading.hide()

      this.entity = _.assignIn({}, this.entity, resp.data)
      delete this.entity.startKyc
      this.verifying = false

      if (oldStep !== this.entity.kycStep) {
        this.$root.$emit('reload-mex-members')
        this.$root.$emit('reload-mex-members-profile')
      }

      if (resp.data.kycStep === 'KYC (Scan Pending)' && !this.isBotmUser) {
        this.startIdScan()
      }
    },
    async startIdScan () {
      if (this.$v.$anyDirty) {
        await this.save()
      }
      this._hideAndEmit('show-mex-member-id-upload-dialog', this.entity)
    },
    async manualKyc () {
      if (this.$v.$anyDirty) {
        await this.save()
      }
      this._hideAndEmit('show-mex-member-id-manual-dialog', this.entity)
    }
  }
}
</script>

<style lang="scss">
.mex-member-detail-dialog {
  .modal-content {
    width: 580px;
  }

  .modal-scroll {
    max-height: none;
  }

  .kyc-box {
    padding: 15px;
    margin-bottom: 15px;
    background-color: #f6f6f6;
    border-radius: 15px;
    text-align: center;
    color: #333;

    .form-field-title {
      margin: 0 auto;
      padding-top: 0 !important;
    }

    .mex-member-id-status {
      max-width: 320px;
      margin: 10px auto;
    }
  }
}
</style>
