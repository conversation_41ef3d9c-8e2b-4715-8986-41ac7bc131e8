<template>
  <q-card id="mex__members__profile_detail"
          class="high-card">
    <q-card-title>
      <div class="row flex-center">
        <div class="font-18 bold">Account Details</div>
        <q-btn icon="mdi-refresh"
               outline
               @click="$emit('reload')"
               class="btn-mini ml-auto"></q-btn>
        <q-btn icon="mdi-pencil-outline"
               outline
               @click="edit"
               class="btn-mini ml-5"></q-btn>
      </div>
    </q-card-title>
    <q-card-main>
      <table class="fields-table mt-10 mb-auto">
        <tr v-for="(r, i) in fields"
            :key="i">
          <th>{{ r }}</th>
          <td :class="['Account Status', 'Card Status'].includes(r) && entity[r] !== 'Active' ? 'text-negative' : ''">
            <div v-if="r === 'Legacy Account Numbers'">
              <span v-for="lan in entity[r]"
                    :key="lan"
                    class="ml-10">
                {{ lan }}
                <q-tooltip v-if="entity.ans && entity.ans[lan]">
                  Enrolled under "{{ entity.ans[lan] }}"
                </q-tooltip>
              </span>
            </div>
            <span v-else>
              {{ entity[r] }}
              <q-tooltip v-if="r === 'Barcode Number' && entity.ans && entity.ans[entity[r]]">
                Enrolled under "{{ entity.ans[entity[r]] }}"
              </q-tooltip>
            </span>
            <a href="javascript:"
               class="ml-5 decoration-none"
               @click="editField(r)"
               v-if="(['Active', 'On Hold'].includes(entity.Status) && ['Card Status', 'Account Status'].includes(r)) || r === 'Season Name'">
              <q-icon name="mdi-pencil-outline"></q-icon>
            </a>
            <a href="javascript:"
               class="ml-5 decoration-none"
               @click="syncBotmAccount(r)"
               v-if="r === 'BOTM Account ID' && !entity[r]">
              <q-icon name="mdi-refresh">
                <q-tooltip>Sync BOTM Account</q-tooltip>
              </q-icon>
            </a>
            <a href="javascript:"
               class="ml-5 decoration-none"
               @click="syncBotmAccount(r)"
               v-if="r === 'BOTM Account ID' && entity[r]">
              <q-icon name="mdi-auto-fix">
                <q-tooltip>Sync BOTM Account Status</q-tooltip>
              </q-icon>
            </a>
            <a href="javascript:"
               class="ml-5 decoration-none"
               @click="syncBotmKycStatus(r)"
               v-if="r === 'BOTM KYC Status'">
              <q-icon name="mdi-refresh">
                <q-tooltip>Update BOTM KYC Status</q-tooltip>
              </q-icon>
            </a>
          </td>
        </tr>
      </table>

      <div class="row flex-center mt-20 mb-auto">
        <q-btn icon="mdi-lock-open-outline"
               no-caps
               color="positive"
               class="btn-sm mb-10 mh-10"
               v-if="entity['Enrolled'] === 'Yes' && entity['Card Status'] === 'Inactive'"
               @click="activateCard"
               label="Activate Card"></q-btn>
        <q-btn icon="mdi-file-document-outline"
               no-caps
               color="blue"
               class="btn-sm mb-10"
               v-if="entity['Status'] && (['Initial'].includes(entity['Status']) || entity['Status'].startsWith('KYC Failed'))"
               @click="manual"
               label="Manually Verify KYC"></q-btn>
        <q-btn icon="mdi-file-download-outline"
               no-caps
               color="blue"
               class="btn-sm mb-10"
               v-else-if="entity['KYC Passed']"
               @click="download"
               label="Download KYC Documents"></q-btn>
      </div>
      <div v-if="masterAdmin || agentAdmin"
           class="row flex-center mt-20 mb-auto">
        <q-btn no-caps
               color="blue"
               class="btn-sm mb-10"
               @click="returnBalanace"
               label="Send Email to return balance"></q-btn>
      </div>
    </q-card-main>

    <HtmlListDialog></HtmlListDialog>
    <PromptDialog></PromptDialog>

    <DetailDialog></DetailDialog>

    <IdUploadDialog></IdUploadDialog>
    <IdInstructionDialog></IdInstructionDialog>
    <IdSuccessDialog></IdSuccessDialog>
    <IdManualDialog></IdManualDialog>

    <CardStatusDialog></CardStatusDialog>

    <SmsDialog :callback="verifiedSms"></SmsDialog>
    <ReturnDialog></ReturnDialog>
  </q-card>
</template>

<script>
import HtmlListDialog from '../../../../components/HtmlListDialog'
import PromptDialog from '../../../../components/PromptDialog'
import DetailDialog from '../detail'
import IdUploadDialog from '../id_upload'
import IdInstructionDialog from '../id_instruction'
import IdSuccessDialog from '../id_success'
import IdManualDialog from '../id_manual'
import CardStatusDialog from '../card/status'
import SmsDialog from '../../common/sms'
import { notify, notifyOptional, notifySuccess, request } from '../../../../common'
import _ from 'lodash'
import MexPageMixin from '../../../../mixins/mex/MexPageMixin'
import ReturnDialog from './returnBalance'

export default {
  name: 'mex-members-profile-detail',
  components: {
    HtmlListDialog,
    PromptDialog,
    DetailDialog,
    IdUploadDialog,
    IdInstructionDialog,
    IdSuccessDialog,
    IdManualDialog,
    CardStatusDialog,
    SmsDialog,
    ReturnDialog
  },
  mixins: [
    MexPageMixin
  ],
  props: {
    entity: {
      type: Object
    }
  },
  computed: {
    fields () {
      const all = [
        'First Name', 'Last Name', 'Date of Birth', 'Mailing Address',
        'City', 'Country', 'State / Province', 'Postal Code',
        'External Employee ID',
        'Barcode Number', 'Last Four SSN', 'Card Number',
        'Card Status',
        'Account Status',
        'Legacy Account Numbers', 'Season Name',
        'BOTM User ID', 'BOTM Account ID', 'BOTM KYC Status'
      ]
      if (this.entity && this.entity.currentProcessor === 'BOTM') {
        const remove = ['Card Status']
        if (!['Active', 'On Hold'].includes(this.entity.Status)) {
          remove.push('Account Status')
        }
        return _.difference(all, remove)
      }
      if (this.entity && this.entity.currentProcessor === 'Rapid') {
        return _.difference(all, ['Account Status'])
      }
      return all
    }
  },
  methods: {
    edit () {
      this.$root.$emit('show-mex-member-detail-dialog', this.entity)
    },
    manual () {
      this.$root.$emit('show-mex-member-id-manual-dialog', this.entity)
    },
    async download () {
      this.$q.loading.show()
      const resp = await request(`/admin/mex/members/${this.entity['Member ID']}/id-download`)
      this.$q.loading.hide()
      if (resp.success && resp.data) {
        if (resp.data.length > 1) {
          const links = []
          _.forEach(resp.data, (v, k) => {
            links.push(`<a href="${v}/download">File ${k + 1}</a>`)
          })
          this.$root.$emit('show-html-list-dialog', {
            title: 'Download KYC Documents',
            html: `<p>Click on the below links to download the files:</p><ul><li>${links.join('</li><li>')}</li></ul>`
          })
        } else if (resp.data.length) {
          location.href = `${resp.data[0]}/download`
        }
      }
    },
    async activateCard () {
      this.$q.loading.show()
      const resp = await request(`/admin/mex/members/${this.entity['Member ID']}/activate-card`, 'post')
      this.$q.loading.hide()
      if (resp.success) {
        notifySuccess(resp)
        this.$emit('reload')
      }
    },
    async editField (field) {
      if (field === 'Card Status') {
        this.$root.$emit('show-mex-member-card-status-dialog', this.entity)
      }
      if (field === 'Account Status') {
        return this.toggleAccountStatus()
      }
      if (field === 'Season Name') {
        this.$q.dialog({
          title: `Edit Member Season Name`,
          message: `Please enter the Season Name that the member belong to:`,
          prompt: {
            model: this.entity['Season Name'],
            type: String | null | undefined
          },
          cancel: true
        }).then(async seasonName => {
          this.$q.loading.show()
          const resp = await request(`/admin/mex/member/${this.entity['Member ID']}/set-season-name`, 'post', {
            seasonName
          })
          this.$q.loading.hide()
          if (resp.success) {
            notifySuccess(resp)
            this.$emit('reload')
          }
        }).catch(() => {})
      }
    },
    async syncBotmAccount () {
      this.$q.loading.show()
      const resp = await request(`/admin/mex/members/${this.entity['Member ID']}/botm/sync-account`, 'post')
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp)
        this.$emit('reload')
      }
    },
    async syncBotmKycStatus () {
      this.$q.loading.show()
      const resp = await request(`/admin/mex/members/${this.entity['Member ID']}/botm/sync-kyc-status`, 'post')
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp)
        this.$emit('reload')
      }
    },
    toggleAccountStatus () {
      if (!['Active', 'On Hold'].includes(this.entity.Status)) {
        return
      }

      const newStatus = this.entity.Status === 'Active' ? 'On Hold' : 'Active'
      let message = `<p>Please enter the reason why you want to change the account status to "${newStatus}".</p>`
      if (newStatus === 'On Hold') {
        message += `<ul class="text-orange mb-30"><li>The card will also be paused to block POS/ATM transactions. </li><li>The user cannot make transfers from our mobile app either.</li></ul>`
      } else {
        message += `<ul class="text-primary mb-30"><li>The card will be resumed to allow POS/ATM transactions. </li><li>The user will be able to make transfers from our mobile app too.</li></ul>`
      }
      this.$root.$emit('show-prompt-dialog', {
        title: 'Change Status',
        message: `<div class="text-left">${message}</div>`,
        callback: async reason => {
          if (reason === null) {
            return
          }
          if (!_.trim(reason)) {
            return notify('The reason is required!', 'negative')
          }
          this.$q.loading.show()
          const resp = await this.c.request(`/admin/mex/members/${this.entity['Member ID']}/change-status`, 'post', {
            reason,
            status: newStatus
          })
          this.$q.loading.hide()
          if (resp.success) {
            notifyOptional(resp.message)
            this.$emit('reload')
          }
        }
      })
    },
    verifiedSms (row, token) {
      row.token = token
      if (row.smsType === 'editDetail') {
        this.$root.$emit('save-mex-member-detail')
      }
    },
    returnBalanace () {
      this.$root.$emit('show-mex-member-return-dialog', this.entity)
    }
  }
}
</script>

<style lang="scss">
#mex__members__profile_detail {
  .q-card-main {
    display: flex;
    flex-direction: column;
    justify-content: start;
    overflow: auto;
  }
}
</style>
