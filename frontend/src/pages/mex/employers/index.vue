<template>
  <q-page id="mex__employers__index_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-select v-model="selectedCp"
                  class="dense"
                  stack-label="Card Program"
                  :options="cardPrograms"></q-select>
        <DateRangeFilter class="mr-20 dense"
                         :stack-label="true"
                         :default="dateRange"
                         :ranges="dateRanges"
                         @change="reload"
                         ref="dateRangeFilter"></DateRangeFilter>
      </div>
    </div>
    <div class="page-content">
      <div class="row gutter-sm">
        <div class="col-sm-3 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-briefcase-account-outline"></q-icon>
                <div class="column">
                  <div class="value font-mono">{{ quick.total || 0 }}</div>
                  <div class="description">Total Employers</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
        <div class="col-sm-3 col-6">
          <q-card class="top-statics">
            <q-card-main>
              <div class="row">
                <q-icon name="mdi-chart-bar"></q-icon>
                <div class="column">
                  <div class="value font-mono">{{ quick.average || 0 }}</div>
                  <div class="description">Average Employees</div>
                </div>
              </div>
            </q-card-main>
          </q-card>
        </div>
      </div>

      <q-table :data="data"
               :columns="filteredVisibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="main-table sticky-table mt-16"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <q-btn-dropdown class="btn-sm mr-10"
                          no-caps
                          v-if="multiSelActionLabel"
                          color="primary"
                          text-color="white"
                          :label="multiSelActionLabel">
            <q-list link>
              <q-item v-close-overlay
                      @click.native="resetMultiSel">
                <q-item-main>Unselect all</q-item-main>
              </q-item>
              <q-item v-close-overlay
                      @click.native="setSplashPage()">
                <q-item-main>Set Splash Page</q-item-main>
              </q-item>
              <q-item v-close-overlay
                      @click.native="clearSplashPage()">
                <q-item-main>Clear Splash Page</q-item-main>
              </q-item>
            </q-list>
          </q-btn-dropdown>

        </template>
        <template slot="top-right">
          <q-btn icon="mdi-account-plus-outline"
                 color="positive"
                 label="Create New Employer"
                 @click="add"
                 class="btn-sm mr-10"
                 no-caps></q-btn>
          <q-btn icon="mdi-file-download-outline"
                 color="blue"
                 label="Export as XLSX"
                 @click="download"
                 class="btn-sm mr-8"
                 no-caps></q-btn>
          <q-btn icon="mdi-cog-outline"
                 color="orange"
                 label="Global Config"
                 @click="$root.$emit('show-mex-employer-global-dialog')"
                 class="btn-sm mr-8"
                 no-caps></q-btn>
          <q-btn icon="mdi-message-processing-outline"
                 color="positive"
                 label="Send Deposit Inquiry"
                 @click="sendDeposit"
                 class="btn-sm mr-8"
                 no-caps></q-btn>

          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="mr-10 w-200 dense"
                    @keydown.13="delayedReload"
                    @clear="delayedReload"
                    clearable
                    placeholder="Search...">
          </q-search>

          <q-btn flat
                 round
                 dense
                 color="faded"
                 class="mr-10"
                 icon="mdi-filter-outline"
                 @click="filtersDialog = true">
            <q-chip floating
                    class="dot"
                    v-if="validFilters.length"></q-chip>
          </q-btn>
          <q-btn flat
                 round
                 dense
                 color="faded"
                 @click="reload"
                 icon="refresh" />
        </template>
        <StickyHead slot="header"
                    slot-scope="props"
                    :freeze-column.sync="freezeColumn"
                    :column-styles="columnStyles"
                    :column-classes="columnClasses"
                    :readonly="true"
                    :sort="true"
                    @change="reload"
                    @selectAll="onHeaderMultiSelectAll"
                    ref="stickyHead"
                    :props="props"></StickyHead>
        <q-tr slot="body"
              slot-scope="props"
              :props="props"
              :class="rowClass(props.row)">
          <q-td v-for="(col, i) in props.cols"
                :key="col.field"
                :align="col.align"
                :style="columnStyles[i]"
                :class="columnClasses[i]">
            <template v-if="col.field === 'multi_check'">
              <div class="checkbox-expander"
                   @click="toggleMultiSelRow(props.row)">
                <q-checkbox v-model="multiSel.ids[props.row['Employer ID']]"></q-checkbox>
              </div>
            </template>
            <template v-else-if="col.field === 'Status'">
              <q-chip class="font-12"
                      :class="props.row['Status'] === 'Active' ? 'positive' : 'negative'">
                {{ props.row['Status'] }}
              </q-chip>
            </template>
            <template v-else-if="['Refund Member ATM Fees', 'Refund Maintenance Fees'].includes(col.field)">
              {{ props.row[col.field] ? 'Yes' : 'No' }}
            </template>
            <template v-else-if="['Enable Transfer'].includes(col.field)">
              <q-toggle v-if="masterAgentAdmin"
                        :disable="quick.enableTransfer === false"
                        @input="onEnableTransferChanged($event, props.row)"
                        v-model="props.row[col.field]">
                <q-tooltip v-if="quick.enableTransfer === false">Disabled globally</q-tooltip>
              </q-toggle>
              <span v-else>{{ props.row[col.field] ? 'Yes' : 'No' }}</span>
            </template>
            <template v-else-if="['Allow Balance/PIN Viewing'].includes(col.field)">
              <q-toggle v-if="masterAgentAdmin"
                        :disable="quick.enableBalance === false"
                        @input="onEnableBalanceChanged($event, props.row)"
                        v-model="props.row[col.field]">
                <q-tooltip v-if="quick.enableBalance === false">Disabled globally</q-tooltip>
              </q-toggle>
              <span v-else>{{ props.row[col.field] ? 'Yes' : 'No' }}</span>
            </template>
            <template v-else-if="['Active Employees'].includes(col.field)">
              <span class="mex-amount">{{ props.row[col.field] }}</span>
            </template>
            <template v-else-if="['Rapid Agent Balance', 'BOTM Balance'].includes(col.field)">
              <span class="mex-amount">{{ _.get(props.row, col.field) }}</span>
              <a href="javascript:"
                 class="link ml-5 font-14"
                 v-if="_.get(props.row, col.field)"
                 @click="refreshAgentBalance(props.row, col.field)">
                <q-icon :name="props.row.loading ? 'mdi-loading' : 'mdi-refresh'"
                        :class="{'mdi-spin': props.row.loading}"></q-icon>
              </a>
            </template>
            <template v-else-if="col.field === 'Actions'">
              <q-btn-dropdown class="btn-sm"
                              no-caps
                              color="grey-2"
                              text-color="dark"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          v-if="props.row['Funding Type'] === 'Prefunded'"
                          @click.native="view(props.row)">
                    <q-item-main>View</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="$c.loginAs(props.row['Employer ID'])"
                          v-if="(props.row['Funding Type'] === 'Prefunded' && canLoginAsEmployers) || (props.row['Funding Type'] === 'ACH' && canLoginAsEmployers && masterAgentAdmin)">
                    <q-item-main>Login As</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="edit(props.row)">
                    <q-item-main>Edit</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="passcode(props.row)"
                          v-if="props.row['Status'] === 'Active'">
                    <q-item-main>Get Code</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="email(props.row)"
                          v-if="props.row['Status'] === 'Active'">
                    <q-item-main>Send Payroll Email</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="invite(props.row)"
                          v-if="props.row['Funding Type'] === 'Prefunded' && props.row['Status'] === 'Active' && !props.row['Last Login Time']">
                    <q-item-main>Resend Invitation Email</q-item-main>
                  </q-item>
                  <q-item v-if="masterAdmin"
                          v-close-overlay
                          @click.native="$root.$emit('show-mex-employer-rapid-dialog', props.row)">
                    <q-item-main>Config Rapid Account</q-item-main>
                  </q-item>
                  <q-item v-if="masterAdmin"
                          v-close-overlay
                          @click.native="$root.$emit('show-mex-employer-botm-dialog', props.row)">
                    <q-item-main>Config BOTM Account</q-item-main>
                  </q-item>
                  <q-item v-if="masterAdmin && props.row['Funding Type'] === 'Prefunded'"
                          v-close-overlay
                          @click.native="updateDepositRecords(props.row)">
                    <q-item-main>Sync Deposit Record</q-item-main>
                  </q-item>
                  <q-item v-if="masterAgentAdmin && props.row['Employees']">
                    <q-item-main label="Update All Cards"></q-item-main>
                    <q-item-side right>
                      <q-item-tile icon="mdi-menu-right"></q-item-tile>
                    </q-item-side>
                    <q-popover anchor="bottom left"
                               self="top right"
                               :offset="[0, -40]">
                      <q-list link>
                        <q-item v-close-overlay
                                @click.native="updateAllCards(props.row, 'disable')">
                          <q-item-main>Disable All Cards</q-item-main>
                        </q-item>
                        <q-item v-close-overlay
                                @click.native="updateAllCards(props.row, 'recover')">
                          <q-item-main>Recover All Cards</q-item-main>
                        </q-item>
                      </q-list>
                    </q-popover>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="settings(props.row)">
                    <q-item-main>Settings</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="del(props.row)"
                          v-if="props.row['Employees'] === 0">
                    <q-item-main>Delete</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="uploadDdZip(props.row)"
                          v-if="props.row['Funding Type'] === 'ACH'">
                    <q-item-main>Upload Direct Deposit PDFs</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="updateAgentTransaction(props.row)"
                          v-if="props.row['Funding Type'] === 'Prefunded'">
                    <q-item-main>Update Agent Transactions</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="masterAdmin || agentAdmin"
                          @click.native="resetFA(props.row)">
                    <q-item-main>Reset 2FA</q-item-main>
                  </q-item>
                  <q-item v-if="user.cpKey === 'cp_mex'"
                          v-close-overlay
                          @click.native="viewSplash(props.row)">
                    <q-item-main>View Splash</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="masterAdmin || agentAdmin"
                          @click.native="freeConfig(props.row)">
                    <q-item-main>Free Config</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="masterAgentAdmin && !props.row['enablePlatformUniTellerBank'] && !props.row['unitellerBankTransfer']"
                          @click.native="updateUnitellerBank(props.row)">
                    <q-item-main>Enable UniTeller Bank</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="masterAgentAdmin && !props.row['enablePlatformUniTellerBank'] && props.row['unitellerBankTransfer']"
                          @click.native="updateUnitellerBank(props.row, false)">
                    <q-item-main>Disable UniTeller Bank</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="masterAgentAdmin && !props.row['isPrimaryEmployer']"
                          @click.native="updatePrimaryEmployer(props.row)">
                    <q-item-main>Set as Primary Employer</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="masterAgentAdmin && props.row['isPrimaryEmployer']"
                          @click.native="updatePrimaryEmployer(props.row, false)">
                    <q-item-main>Cancel as Primary Employer</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="masterAgentAdmin && !props.row['balanceFilter']"
                          @click.native="updateBalanceFilter(props.row)">
                    <q-item-main>Enable High Balance Report</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="masterAgentAdmin && props.row['balanceFilter']"
                          @click.native="updateBalanceFilter(props.row, false)">
                    <q-item-main>Disable High Balance Report</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          v-if="props.row['isPrimaryEmployer']"
                          @click.native="configSubCompany(props.row)">
                    <q-item-main>Config the Sub Company</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              <div notranslate="">{{ _.get(props.row, col.field) }}</div>
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>

    <DetailDialog></DetailDialog>
    <PasscodeDialog></PasscodeDialog>
    <SmsDialog :callback="verifiedSms"></SmsDialog>
    <EmailDialog></EmailDialog>
    <RapidDialog></RapidDialog>
    <BotmDialog></BotmDialog>
    <GlobalDialog></GlobalDialog>

    <AssignDialog></AssignDialog>
    <AssignedDialog></AssignedDialog>
    <LoadDialog></LoadDialog>
    <SettingsDialog></SettingsDialog>
    <UploadDirectDeposit></UploadDirectDeposit>
    <BatchSetSplashDialog></BatchSetSplashDialog>
    <SentDepositInquiry></SentDepositInquiry>
    <SplashDialog></SplashDialog>
    <AddDepositRecordDialog></AddDepositRecordDialog>
    <FreeConfigDialog></FreeConfigDialog>
    <SetSubCompanyDialog></SetSubCompanyDialog>
  </q-page>
</template>

<script>
import ListPageMixin from '../../../mixins/ListPageMixin'
import { EventHandlerMixin, generateColumns, notify, request } from '../../../common'
import MexPageMixin from '../../../mixins/mex/MexPageMixin'
import DetailDialog from './detail'
import RapidDialog from './rapid'
import BotmDialog from './botm'
import AssignDialog from './assign'
import AssignedDialog from '../members/assigned'
import LoadDialog from '../members/card/load'
import SplashDialog from '../members/splash'
import PasscodeDialog from './passcode'
import SmsDialog from '../common/sms'
import EmailDialog from './email'
import GlobalDialog from './global'
import FreezeColumnMixin from '../../../mixins/FreezeColumnMixin'
import _ from 'lodash'
import SettingsDialog from './../../faas/base/clients/settings'
import UploadDirectDeposit from './../employerDashboard/employee/upload_dd'
import MultiSelectionListMixin from '../../../mixins/MultiSelectionListMixin'
import BatchSetSplashDialog from '../members/batch/setSplash'
import SentDepositInquiry from './sentDepositInquiry'
import AddDepositRecordDialog from './deposit'
import FreeConfigDialog from './free'
import SetSubCompanyDialog from './setSubCompany'

export default {
  name: 'mex-employers',
  mixins: [
    MexPageMixin,
    ListPageMixin,
    FreezeColumnMixin,
    MultiSelectionListMixin,
    EventHandlerMixin('reload-mex-employers')
  ],
  components: {
    DetailDialog,
    RapidDialog,
    BotmDialog,
    PasscodeDialog,
    SmsDialog,
    EmailDialog,
    AssignDialog,
    AssignedDialog,
    LoadDialog,
    GlobalDialog,
    SettingsDialog,
    UploadDirectDeposit,
    BatchSetSplashDialog,
    SentDepositInquiry,
    SplashDialog,
    AddDepositRecordDialog,
    FreeConfigDialog,
    SetSubCompanyDialog
  },
  data () {
    return {
      title: 'Employers',
      filtersUrl: `/admin/mex/employers/filters`,
      requestUrl: `/admin/mex/employers/list`,
      downloadUrl: `/admin/mex/employers/export`,
      columns: generateColumns([
        'multi_check',
        'Create Date', 'Employer Name', 'Employer ID', 'Employer Address',
        'Contact Name', 'Email', 'Phone', 'Funding Type', 'Card Provider',
        'Refund Member ATM Fees', 'Refund Maintenance Fees',
        'Active Employees', 'Migrated Employees', 'BOTM Accounts', 'BOTM Balance', 'Tern Balance', 'Enable Transfer', 'Allow Balance/PIN Viewing',
        'Last Login Time', 'Status', 'Actions'
      ], ['Rapid Agent Balance', 'BOTM Balance', 'Tern Balance', 'Active Employees', 'Migrated Employees', 'BOTM Accounts'], {
        'Create Date': 'u.createdAt',
        'Employer Name': 'g.name',
        'Employer ID': 'u.id',
        'Employer Address': 'CONCAT(u.address,u.addressline)',
        'Contact Name': 'CONCAT(u.firstName,u.lastName)',
        'Email': 'u.email',
        'Funding Type': 'g.fundingType',
        'Phone': 'u.mobilephone'
      }),
      filterOptions: [
        {
          value: 'filter[u.id=]',
          label: 'Employer ID'
        }, {
          value: 'filter[u.firstName=]',
          label: 'First Name'
        }, {
          value: 'filter[u.lastName=]',
          label: 'Last Name'
        }, {
          value: 'filter[u.email=]',
          label: 'Email'
        }, {
          value: 'filter[u.mobilephone=]',
          label: 'Phone'
        }, {
          value: 'filter[u.status=]',
          label: 'Status',
          options: [
            { label: 'Active', value: 'active' },
            { label: 'Inactive', value: 'inactive' }
          ]
        }, {
          value: 'filter[uc.accountNumber=]',
          label: 'Account Number'
        }, {
          value: 'filter[g.fundingType=]',
          label: 'Funding Type',
          options: [
            { label: 'ACH', value: 'ACH' },
            { label: 'Prefunded', value: 'Prefunded' }
          ]
        }, {
          value: 'filter[g.cardProvider=]',
          label: 'Card Provider',
          options: [
            { label: 'Rapid', value: 'Rapid' },
            { label: 'BOTM', value: 'BOTM' }
          ]
        }, {
          value: 'filter[g.name=]',
          label: 'Employer Name'
        }
      ],
      autoLoad: true,
      freezeColumn: -1,
      freezeColumnRight: 2,
      multiSelIdKey: 'Employer ID',
      splashList: [],
      employerList: []
    }
  },
  methods: {
    uploadDdZip (row) {
      this.$root.$emit('show-mex-employer-dd-dialog', row)
    },
    view (row) {
      window.open(`/admin#/j/mex/employers/${row['Employer ID']}`, '_blank')
    },
    add () {
      this.$root.$emit('show-mex-employer-detail-dialog')
    },
    edit (row) {
      this.$root.$emit('show-mex-employer-detail-dialog', row)
    },
    passcode (row) {
      if (this.masterAdmin) {
        this.$root.$emit('show-mex-employer-passcode-dialog', row)
      } else if (this.agentAdmin) {
        row.smsType = 'passcode'
        this.$root.$emit('show-mex-common-sms-dialog', row)
      }
    },
    verifiedSms (row, token) {
      row.token = token
      if (row.smsType === 'change') {
        row.change = true
        this.$root.$emit('show-mex-employer-assign-dialog', row)
      } else if (row.smsType === 'passcode') {
        this.$root.$emit('show-mex-employer-passcode-dialog', row)
      } else if (row.smsType === 'enroll') {
        this.doEnrollCard(row)
      } else if (row.smsType === 'loadUnload') {
        this.$root.$emit('show-mex-member-load-dialog', row)
      } else if (row.smsType && row.smsType.startsWith('updateAllCards_')) {
        const action = row.smsType.replace('updateAllCards_', '')
        this.doUpdateAllCards(row, action)
      }
    },
    email (row) {
      this.$root.$emit('show-mex-employer-email-dialog', row)
    },
    changeCard (row) {
      row.smsType = 'change'
      this.$root.$emit('show-mex-common-sms-dialog', row)
    },
    assignCard (row) {
      this.$root.$emit('show-mex-employer-assign-dialog', row)
    },
    freeConfig (row) {
      this.$root.$emit('show-mex-employer-free-config-dialog', row)
    },
    enrollCard (row) {
      if (this.masterAdmin) {
        this.doEnrollCard(row)
      } else if (this.agentAdmin) {
        row.smsType = 'enroll'
        this.$root.$emit('show-mex-common-sms-dialog', row)
      }
    },
    async doEnrollCard (row) {
      this.$q.loading.show()
      const resp = await this.c.request(`/admin/mex/employers/${row['Employer ID']}/enroll`, 'post')
      this.$q.loading.hide()
      if (resp.success) {
        this.reload()
      }
    },
    async updateUnitellerBank (row, enable = true) {
      this.$q.loading.show()
      const resp = await this.c.request(`/admin/mex/employers/${row['Employer ID']}/config-uniteller-bank`, 'post', {
        enable: enable
      })
      this.$q.loading.hide()
      if (resp.success) {
        this.reload()
      }
    },
    async updatePrimaryEmployer (row, enable = true) {
      this.$q.loading.show()
      const resp = await this.c.request(`/admin/mex/employers/${row['Employer ID']}/config-primary-employer`, 'post', {
        enable: enable
      })
      this.$q.loading.hide()
      if (resp.success) {
        this.reload()
      }
    },
    async updateBalanceFilter (row, enable = true) {
      this.$q.loading.show()
      const resp = await this.c.request(`/admin/mex/employers/${row['Employer ID']}/config-balance-filter`, 'post', {
        enable: enable
      })
      this.$q.loading.hide()
      if (resp.success) {
        this.reload()
      }
    },
    configSubCompany (row) {
      this.$root.$emit('show-set-sub-company-dialog', row)
    },
    loadUnload (row) {
      if (this.masterAdmin) {
        this.$root.$emit('show-mex-member-load-dialog', row)
      } else {
        row.smsType = 'loadUnload'
        this.$root.$emit('show-mex-common-sms-dialog', row)
      }
    },
    async invite (row) {
      this.$q.loading.show()
      const resp = await request(`/admin/mex/employers/${row['Employer ID']}/invite`, 'post')
      this.$q.loading.hide()
      if (resp.success) {
        notify(resp)
      }
    },
    del (row) {
      this.$q.dialog({
        title: 'Confirm',
        message: 'Are you sure that you want to delete this employer?',
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.loading = true
        const resp = await request(`/admin/mex/employers/${row['Employer ID']}/del`, 'post')
        this.loading = false
        if (resp.success) {
          this.reload()
        }
      }).catch(() => {})
    },
    async refreshAgentBalance (row, field) {
      row.loading = true
      const resp = await request(`/admin/mex/employers/${row['Employer ID']}/refresh-agent-balance`, 'post', {
        field
      })
      row.loading = false
      if (resp.success) {
        row[field] = resp.data
      }
    },
    onEnableTransferChanged (value, row) {
      this.$q.dialog({
        title: 'Confirm',
        message: `Are you sure that you want to ${value ? 'enable' : 'disable'} the transfer?`,
        color: value ? 'positive' : 'negative',
        cancel: true
      }).then(async () => {
        this.loading = true
        const resp = await request(`/admin/mex/employers/${row['Employer ID']}/update-toggle/transfer/${value ? 'on' : 'off'}`, 'post')
        this.loading = false
        if (resp.success) {
          this.reload()
          notify(resp)
        }
      }).catch(() => {
        row['Enable Transfer'] = !value
      })
    },
    onEnableBalanceChanged (value, row) {
      this.$q.dialog({
        title: 'Confirm',
        message: `Are you sure that you want to ${value ? 'enable' : 'disable'} the balance-viewing from the mobile app?`,
        color: value ? 'positive' : 'negative',
        cancel: true
      }).then(async () => {
        this.loading = true
        const resp = await request(`/admin/mex/employers/${row['Employer ID']}/update-toggle/balance/${value ? 'on' : 'off'}`, 'post')
        this.loading = false
        if (resp.success) {
          this.reload()
          notify(resp)
        }
      }).catch(() => {
        row['Allow Balance/PIN Viewing'] = !value
      })
    },
    updateAllCards (row, action) {
      this.$q.dialog({
        title: 'Confirm',
        message: `Are you sure that you want to ${action} all the cards under this employer? This may needs a long time(>= 30 minutes).`,
        color: 'negative',
        cancel: true
      }).then(() => {
        if (this.masterAdmin) {
          this.doUpdateAllCards(row, action)
        } else if (this.agentAdmin) {
          row.smsType = 'updateAllCards_' + action
          this.$root.$emit('show-mex-common-sms-dialog', row)
        }
      }).catch(() => {})
    },
    async doUpdateAllCards (row, action) {
      this.loading = true
      const resp = await request(`/admin/mex/employers/${row['Employer ID']}/update-cards/${action}`, 'post')
      this.loading = false
      if (resp.success) {
        notify(resp)
      }
    },
    settings (row) {
      this.$root.$emit('show-faas-client-settings-dialog', row)
    },
    updateAgentTransaction (row) {
      this.$q.dialog({
        title: 'Confirm',
        message: `Are you sure that you want to update the transactions under this employer? This may needs a long time(>= 10 minutes).`,
        color: 'negative',
        cancel: true
      }).then(async () => {
        const resp = await request(`/admin/mex/employers/${row['Employer ID']}/update-agent-transactions`, 'post')
        this.loading = false
        if (resp.success) {
          notify(resp)
        }
      }).catch(() => {})
    },
    updateDepositRecords (row) {
      this.$q.dialog({
        title: 'Confirm',
        message: `Are you sure that you want to update the deposit transactions under this employer? This may needs a long time(>= 10 minutes).`,
        color: 'negative',
        cancel: true
      }).then(async () => {
        const resp = await request(`/admin/mex/employers/${row['Employer ID']}/sync-deposit`, 'post')
        this.loading = false
        if (resp.success) {
          notify(resp)
        }
      }).catch(() => {})
    },
    viewSplash (row) {
      this.$root.$emit('show-mex-member-splash-dialog', {
        splashList: row.splashList,
        id: row['Employer ID'],
        type: 'employer'
      })
    },
    setSplashPage () {
      this.$root.$emit('show-mex-member-batch-set-splash-dialog', {
        splashList: this.splashList,
        members: this.getMultiSelKeys(),
        type: 'employer'
      })
    },
    clearSplashPage () {
      const members = this.getMultiSelKeys()
      this.$q.dialog({
        title: 'Confirm',
        message: `Are you sure that you want to clear the splash page for the selected ${members.length} members?`,
        color: 'negative',
        cancel: true
      }).then(async () => {
        this.$q.loading.show({ message: 'Batch clearring...' })
        const resp = await request(`/admin/mex/members/batch-clear-splash`, 'post', {
          members: members
        })
        this.$q.loading.hide()
        if (resp.success) {
          this.$root.$emit('reload-mex-members')
          this._hide()
          notify(resp.data)
        }
      }).catch(() => {})
    },
    postInitFilters (res) {
      if (res.success) {
        this.splashList = res.data.splashList ? res.data.splashList : []
        this.employerList = res.data.employerList ? res.data.employerList : []
      }
    },
    sendDeposit () {
      this.$root.$emit('show-sent-deposit-inquiry-dialog')
    },
    async resetFA (row) {
      this.$q.loading.show()
      const resp = await this.c.request(`/admin/mex/employer/${row['Employer ID']}/resetFA`, 'post')
      this.$q.loading.hide()
      if (resp.success) {
        this.reload()
      }
    }
  },
  mounted () {
    if (this.masterAdmin) {
      let index = _.findIndex(this.columns, c => {
        return c.field === 'BOTM Balance'
      })
      if (index >= 0) {
        for (const field of ['Rapid Agent Balance']) {
          this.columns.splice(++index, 0, {
            field,
            label: field,
            align: 'left',
            sortLabel: null
          })
        }
      }
    }
  }
}
</script>
