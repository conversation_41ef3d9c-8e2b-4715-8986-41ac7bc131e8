<template>
  <q-dialog class="mex-employer-detail-dialog"
            v-model="visible"
            prevent-close>
    <template slot="title">
      <div class="font-16 mb-2">{{ edit ? 'Edit Employer' : 'Create New Employer' }}</div>
      <div class="font-12 normal text-dark">Please fill in the information below about the employer.</div>
      <q-btn class="close"
             round
             flat
             @click="_hide"
             icon="close" />
    </template>
    <template slot="body">
      <div class="row gutter-form mt--10 mb-5">
        <div class="col-sm-6">
          <q-input float-label="First Name"
                   autocomplete="no"
                   :error="$v.entity['First Name'].$error"
                   @blur="$v.entity['First Name'].$touch"
                   v-model="entity['First Name']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Last Name"
                   autocomplete="no"
                   :error="$v.entity['Last Name'].$error"
                   @blur="$v.entity['Last Name'].$touch"
                   v-model="entity['Last Name']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Email"
                   autocomplete="no"
                   :error="$v.entity['Email'].$error"
                   @blur="$v.entity['Email'].$touch"
                   v-model="entity['Email']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Phone Number"
                   autocomplete="no"
                   :error="$v.entity['Phone'].$error"
                   @blur="$v.entity['Phone'].$touch"
                   v-model="entity['Phone']"></q-input>
        </div>
        <div class="col-sm-12">
          <q-input float-label="Employer Name"
                   autocomplete="no"
                   :error="$v.entity['Employer Name'].$error"
                   @blur="$v.entity['Employer Name'].$touch"
                   v-model="entity['Employer Name']"></q-input>
        </div>
        <!-- <div class="col-sm-12">
          <q-datetime float-label="Date of Birth"
                      autocomplete="no"
                      type="date"
                      format="MM/DD/YYYY"
                      :error="$v.entity['Date of Birth'].$error"
                      :readonly="!$store.getters['User/masterAdmin']"
                      @change="$v.entity['Date of Birth'].$touch"
                      v-model="entity['Date of Birth']"></q-datetime>
        </div> -->
        <div class="col-sm-12">
          <q-input float-label="Street Address"
                   autocomplete="no"
                   :error="$v.entity['Street Address'].$error"
                   @blur="$v.entity['Street Address'].$touch"
                   v-model="entity['Street Address']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="City"
                   autocomplete="no"
                   :error="$v.entity['City'].$error"
                   @blur="$v.entity['City'].$touch"
                   v-model="entity['City']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-input float-label="Postal Code"
                   autocomplete="no"
                   :error="$v.entity['Postal Code'].$error"
                   @blur="$v.entity['Postal Code'].$touch"
                   v-model="entity['Postal Code']"></q-input>
        </div>
        <div class="col-sm-6">
          <q-select float-label="Country"
                    autocomplete="no"
                    :options="countries"
                    filter
                    autofocus-filter
                    :error="$v.entity['CountryId'].$error"
                    @blur="$v.entity['CountryId'].$touch"
                    v-model="entity['CountryId']"></q-select>
        </div>
        <div class="col-sm-6">
          <q-select float-label="State / Province"
                    autocomplete="no"
                    :options="states"
                    filter
                    autofocus-filter
                    :error="$v.entity['State'].$error"
                    :before="stateBefore"
                    @blur="$v.entity['State'].$touch"
                    v-model="entity['State']"></q-select>
        </div>

        <div class="col-sm-6 pt-15 text-left">
          <div class="font-12">Refund Member ATM Fees</div>
          <div class="mt-8">
            <q-radio v-model="entity['Refund Member ATM Fees']"
                     color="blue"
                     :error="$v.entity['Refund Member ATM Fees'].$error"
                     @change="$v.entity['Refund Member ATM Fees'].$touch"
                     :val="true"
                     label="Yes"></q-radio>
            <q-radio v-model="entity['Refund Member ATM Fees']"
                     color="blue"
                     class="ml-15"
                     :error="$v.entity['Refund Member ATM Fees'].$error"
                     @change="$v.entity['Refund Member ATM Fees'].$touch"
                     :val="false"
                     label="No"></q-radio>
          </div>
        </div>
        <div class="col-sm-6 pt-15 text-left">
          <div class="font-12">Refund Maintenance Fees</div>
          <div class="mt-8">
            <q-radio v-model="entity['Refund Maintenance Fees']"
                     color="blue"
                     :error="$v.entity['Refund Maintenance Fees'].$error"
                     @change="$v.entity['Refund Maintenance Fees'].$touch"
                     :val="true"
                     label="Yes"></q-radio>
            <q-radio v-model="entity['Refund Maintenance Fees']"
                     color="blue"
                     class="ml-15"
                     :error="$v.entity['Refund Maintenance Fees'].$error"
                     @change="$v.entity['Refund Maintenance Fees'].$touch"
                     :val="false"
                     label="No"></q-radio>
          </div>
        </div>
        <div class="col-sm-6 pt-15 text-left">
          <div class="font-12">Funding Type</div>
          <div class="mt-8">
            <q-radio v-model="entity['Funding Type']"
                     color="blue"
                     :error="$v.entity['Funding Type'].$error"
                     @change="$v.entity['Funding Type'].$touch"
                     val="ACH"
                     label="ACH"></q-radio>
            <q-radio v-model="entity['Funding Type']"
                     color="blue"
                     class="ml-15"
                     :error="$v.entity['Funding Type'].$error"
                     @change="$v.entity['Funding Type'].$touch"
                     val="Prefunded"
                     label="Prefunded"></q-radio>
          </div>
        </div>
        <div class="col-sm-6 pt-15 text-left">
          <div class="font-12">Card Provider</div>
          <div class="mt-8">
            <q-radio v-model="entity['Card Provider']"
                     color="blue"
                     :error="$v.entity['Card Provider'].$error"
                     @change="$v.entity['Card Provider'].$touch"
                     val="Rapid"
                     label="Rapid"></q-radio>
            <q-radio v-model="entity['Card Provider']"
                     color="blue"
                     class="ml-15"
                     :error="$v.entity['Card Provider'].$error"
                     @change="$v.entity['Card Provider'].$touch"
                     val="BOTM"
                     label="BOTM"></q-radio>
            <q-radio v-model="entity['Card Provider']"
                     color="blue"
                     class="ml-15"
                     :error="$v.entity['Card Provider'].$error"
                     @change="$v.entity['Card Provider'].$touch"
                     val="TERN"
                     label="TERN"></q-radio>
          </div>
        </div>
        <div class="col-sm-6 pt-15 text-left"
             v-show="edit">
          <div class="font-12">Status</div>
          <div class="mt-8">
            <q-radio v-model="entity['Status']"
                     color="blue"
                     :error="$v.entity['Status'].$error"
                     @change="$v.entity['Status'].$touch"
                     val="Active"
                     label="Active"></q-radio>
            <q-radio v-model="entity['Status']"
                     color="blue"
                     class="ml-15"
                     :error="$v.entity['Status'].$error"
                     @change="$v.entity['Status'].$touch"
                     val="Inactive"
                     label="Inactive"></q-radio>
          </div>
        </div>
      </div>
    </template>
    <template slot="buttons">
      <q-btn label="Cancel"
             no-caps
             color="grey-3"
             text-color="tertiary"
             @click="_hide" />
      <q-btn :label="edit ? 'Save Changes' : 'Create Employer'"
             no-caps
             color="positive"
             class="main"
             @click="save" />
    </template>
  </q-dialog>
</template>

<script>
import Singleton from '../../../mixins/Singleton'
import { notifyForm, notify, request } from '../../../common'
import { required, email, helpers } from 'vuelidate/lib/validators'
import _ from 'lodash'
import MexStateListMixin from '../../../mixins/mex/MexStateListMixin'

const alpha = helpers.regex('alpha', /^[0-9+-\s]*$/)
const postalCodeUS = helpers.regex('Must be 5 digits', /^[0-9]{5}(?:-[0-9]{4})?$/)
const postalCode = helpers.regex('Must be digits or letters', /^[a-zA-Z0-9-]*$/)
const validations = {
  entity: {}
}
for (const field of [
  'First Name', 'Last Name', 'Email', 'Phone',
  'Street Address', 'Employer Name', 'CountryId', 'State',
  'City', 'Postal Code', 'Status', 'Funding Type', 'Card Provider',
  'Refund Member ATM Fees', 'Refund Maintenance Fees'
]) {
  validations.entity[field] = { required }
}
validations.entity['Email'] = { required, email }
validations.entity['Phone'] = { required, alpha }
export default {
  name: 'mex-employer-detail-dialog',
  mixins: [
    Singleton,
    MexStateListMixin
  ],
  data () {
    return {
      defaultEntity: {
        'Card Provider': 'Rapid',
        'Funding Type': 'ACH',
        'Refund Member ATM Fees': false,
        'Refund Maintenance Fees': true,
        'Status': 'Active'
      }
    }
  },
  computed: {
    edit () {
      return !!this.entity['Employer ID']
    },
    masterAdmin () {
      const user = this.$store.state.User
      return user && user.currentRole === 'MasterAdmin'
    }
  },
  validations,
  methods: {
    show () {
      let start = new Date('2024-04-01')
      let startTern = new Date('2025-11-01')
      let now = new Date()
      if (start < now && !this.edit) {
        this.entity['Card Provider'] = 'BOTM'
      }
      if (startTern < now && !this.edit) {
        this.entity['Card Provider'] = 'TERN'
      }
    },
    onSuccess (resp) {
      notify(resp.message)

      if (this.origin && this.origin['Employer ID']) {
        _.assignIn(this.origin, resp.data)
      }

      this.$root.$emit('reload-mex-employers')
      this._hide()
    },
    async save () {
      if (this.entity['CountryId'] === 232) {
        validations.entity['Postal Code'] = { required, postalCodeUS }
      } else {
        validations.entity['Postal Code'] = { required, postalCode }
      }
      if (this.$v.$invalid) {
        this.$v.$touch()
        return notifyForm()
      }
      this.$q.loading.show()
      const resp = await request(`/admin/mex/employers/save`, 'post', this.entity)
      this.$q.loading.hide()
      if (resp.success) {
        this.onSuccess(resp)
      }
    }
  }
}
</script>

<style lang="scss">
.mex-employer-detail-dialog {
  .modal-content {
    width: 580px;
  }
}
</style>
