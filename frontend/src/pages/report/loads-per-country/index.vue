<template>
  <q-page id="alerts_alert_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-btn icon="mdi-file-download-outline"
               color="blue"
               label="Export as XLSX"
               @click="download"
               class="btn-sm mr-8"
               no-caps></q-btn>
      </div>
    </div>
    <div class="page-content">
      <div class="quick-result">
        <div>
          <h5>{{ quick.total | number }}</h5>
          <div class="description">Countries</div>
        </div>
        <div>
          <h5>{{ quick.members | number }}</h5>
          <div class="description">Total Members</div>
        </div>
        <div>
          <h5>{{ quick.activeMembers | number }}</h5>
          <div class="description">Active Members</div>
        </div>
        <div>
          <h5>{{ quick.loadedMembers | number }}</h5>
          <div class="description">Loaded Members</div>
        </div>
        <div>
          <h5>{{ quick.avgBalance }}</h5>
          <div class="description">Average Balance</div>
        </div>
        <div>
          <h5>{{ quick.avgNetRevenue }}</h5>
          <div class="description">Average Revenue Per User</div>
        </div>
      </div>

      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               :rows-per-page-options="[5, 10, 20, 50]"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <q-btn flat
                 round
                 dense
                 icon="mdi-filter-variant"
                 @click="filtersDialog = true" />
          <FilterChips :filters.sync="filters"
                       :options="filterOptions"
                       v-show="!loadingFilters"
                       @reload="reload"></FilterChips>
        </template>
        <template slot="top-right">
          <q-btn flat
                 round
                 dense
                 class="q-mr-md"
                 @click="reload"
                 icon="refresh" />
          <ColumnFilter :columns="columns"></ColumnFilter>
        </template>

        <q-tr slot="body"
              slot-scope="props">
          <q-td v-for="col in props.cols"
                :key="col.field"
                :class="`text-${col.align || 'left'}`">
            <template v-if="col.field === 'Actions'">
              <q-btn-dropdown size="xs"
                              color="grey-3"
                              text-color="black"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          :to="`/a/alert_job_creation?target=${props.row['Alert ID']}`">
                    <q-item-main>New Job</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="archiveAlert(props.row['Alert ID'])">
                    <q-item-main>Archive</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              {{ col.value }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"
                       :max="999999"></BatchExportDialog>
  </q-page>
</template>

<script>
import { generateColumns, request } from '../../../common'
import ListPageMixin from '../../../mixins/ListPageMixin'
import _ from 'lodash'
// import BatchExportDialogEmail from '../../../components/BatchExportDialogEmail'

export default {
  // components: { BatchExportDialogEmail },
  mixins: [
    ListPageMixin
  ],
  data () {
    return {
      filtersUrl: '/admin/reports/loads_per_country/filters',
      downloadUrl: '/admin/reports/loads_per_country/export',
      title: 'Loads Per Country Report',
      timeField: 'time',
      // cardProgramField: 'filter[cp.id=]',
      autoReloadWhenUrlChanges: true,
      baseColumns: generateColumns([
        'Rank',
        'Country',
        'Total Members',
        'Pending Members',
        'Active Members',
        'Inactive Members',
        'Loaded Members',
        'Loaded Count',
        'Loaded Total',
        'Average Load',
        'Load Net Revenue',
        'Average Net Revenue',
        'Positive Balance Count',
        'Negative Balance Count',
        'Zero Balance Count',
        'Total Balance',
        'Average Balance'
      ], [
      ]),
      quick: {
        total: 0,
        members: 0,
        activeMembers: 0,
        loadedMembers: 0,
        avgBalance: 0,
        avgNetRevenue: 0
      },
      filterOptions: [
        {
          value: 'filter[cp.cardProgram=]',
          label: 'Card Program',
          options: [{
            label: 'US Unlocked', value: 'usunlocked'
          }]
        },
        // {
        //   value: 'initializedAt',
        //   label: 'Load Date',
        //   range: [
        //     {
        //       value: 'range[ucl.initializedAt][start]',
        //       type: 'date'
        //     }, {
        //       value: 'range[ucl.initializedAt][end]',
        //       type: 'date'
        //     }
        //   ]
        // },
        {
          value: 'filter[c.name=]',
          label: 'Country'
        }
      ]
    }
  },
  computed: {
    user () {
      return this.$store.state.User
    }
  },
  methods: {

    // async archiveAlert (alert) {
    //   console.log('run')
    //   const resp = await request(`/admin/alert/archive/${alert}`, 'GET')
    //   this.loading = true
    //   if (resp.success) {
    //     console.log(resp)
    //     this.reload()
    //   }
    // },

    async request ({ pagination }) {
      this.beforeRequest({ pagination })

      this.loading = true
      const data = this.$refs.filtersDialog.getFilters()
      data.keyword = this.keyword
      if (!_.isEmpty(this.$route.query)) {
        data.isFromDashboard = true
      }
      const resp = await request(`/admin/reports/loads_per_country/list/${pagination.page}/${pagination.rowsPerPage}`, 'form', data)
      this.loading = false
      if (resp.success) {
        this.pagination = pagination
        this.data = resp.data.data
        this.pagination.rowsNumber = resp.data.count
        this.quick = resp.data.quick
        console.log(resp)
      }
    },
    init () {
      this.data = []
      this.quick = {}
      this.filters = []
      this.keyword = ''

      this.columns = this.baseColumns
    }
  }
}
</script>
