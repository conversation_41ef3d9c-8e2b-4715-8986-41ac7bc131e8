<template>
  <q-page id="report_privacy_card_creation__list_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-btn class="mr-10"
               color="black"
               @click="download"
               label="Export"></q-btn>
      </div>
    </div>
    <div class="page-content">
      <div class="quick-result">
        <div>
          <h5>{{ quick.total | number }}</h5>
          <div class="description">Total creation</div>
        </div>
        <div>
          <h5>{{ quick.total * 25 | moneyFormat('USD', true) }}</h5>
          <div class="description">Total creation fee</div>
        </div>
        <div>
          <h5>{{ quick.average }}</h5>
          <div class="description">Avg monthly creation</div>
        </div>
        <div>
          <h5>{{ quick.average * 25 | moneyFormat('USD', true) }}</h5>
          <div class="description">Avg monthly creation fee</div>
        </div>
      </div>

      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="no-pagination"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <q-btn flat round dense
                 icon="mdi-filter-variant"
                 @click="filtersDialog = true"/>
          <FilterChips :filters.sync="filters"
                       :options="filterOptions"
                       v-show="!loadingFilters"
                       @reload="reload"></FilterChips>
        </template>
        <template slot="top-right">
          <q-btn flat round dense
                 class="q-mr-md"
                 @click="reload"
                 icon="refresh"/>
          <ColumnFilter :columns="columns"></ColumnFilter>
        </template>

        <q-tr slot="body"
              slot-scope="props" :class="`status_${props.row.status}`">
          <q-td v-for="col in props.cols" :key="col.field" :class="'text-' + col.align">
            <template v-if="col.field.endsWith('Fee')">
              {{ col.value | moneyFormat }}
            </template>
            <template v-else>
              {{ col.value }}
            </template>
          </q-td>
        </q-tr>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog" :max="999999"></BatchExportDialog>
  </q-page>
</template>

<script>
import { generateColumns, request } from '../../../common'
import ListPageMixin from '../../../mixins/ListPageMixin'

export default {
  mixins: [
    ListPageMixin
  ],
  data () {
    return {
      filtersUrl: '/admin/report/privacy-card-creation/filters',
      downloadUrl: '/admin/report/privacy-card-creation/export',
      title: 'Card Creation Report',
      cardProgramField: 'filter[cp.id=]',
      autoReloadWhenUrlChanges: true,
      columns: generateColumns([
        'Month',
        'One-Time Cards', 'Store Locked Cards', 'Unlocked Cards',
        'Total Cards Created', 'Card Creation Fee'
      ], [
        'One-Time Cards', 'Store Locked Cards', 'Unlocked Cards',
        'Total Cards Created', 'Card Creation Fee'
      ]),
      quick: {
        total: 0,
        average: 0
      },
      filterOptions: [
        {
          value: 'cardProvider',
          label: 'Card Provider',
          options: [],
          source: 'cardProvider'
        },
        {
          value: 'filter[cp.id=]',
          label: 'Card program',
          options: [],
          source: 'cardPrograms'
        }, {
          value: 'filter[u.id]',
          label: 'User ID'
        }, {
          value: 'filter[u.email]',
          label: 'User Email'
        }
      ]
    }
  },
  methods: {
    async request ({ pagination }) {
      this.beforeRequest({ pagination })

      this.loading = true
      const data = this.$refs.filtersDialog.getFilters()
      const resp = await request(`/admin/report/privacy-card-creation/search`, 'form', data)
      this.loading = false
      if (resp.success) {
        this.pagination = pagination
        this.data = resp.data.data
        this.pagination.rowsNumber = resp.data.count
        this.quick = resp.data.quick
      }
    },
    init () {
      this.data = []
      this.quick = {}
      this.filters = []
    }
  }
}
</script>
