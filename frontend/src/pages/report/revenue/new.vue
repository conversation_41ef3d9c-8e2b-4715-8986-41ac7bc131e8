<template>
  <q-page id="report_revenu__list_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-btn class="mr-10"
               color="black"
               @click="download"
               label="Export"></q-btn>
      </div>
    </div>
    <div class="page-content">
      <div class="quick-result">
        <div>
          <h5>{{ quick.fee | moneyFormat('USD', true) }}</h5>
          <div class="description">Total Fee Income</div>
        </div>
        <div>
          <h5>{{ quick.cost | moneyFormat('USD', true) }}</h5>
          <div class="description">Total Cost</div>
        </div>
        <div>
          <h5>{{ quick.revenue | moneyFormat('USD', true) }}</h5>
          <div class="description">Total Net Revenue</div>
        </div>
        <div>
          <h5>{{ quick.avgRevenue | moneyFormat('USD', true) }}</h5>
          <div class="description">Avg Monthly Net Revenue</div>
        </div>
      </div>

      <TopChart :chart-id="chartId"
                v-if="chartData">
      </TopChart>
      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               class="no-pagination"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <q-btn flat round dense
                 icon="mdi-filter-variant"
                 @click="filtersDialog = true"/>
          <FilterChips :filters.sync="filters"
                       :options="filterOptions"
                       v-show="!loadingFilters"
                       @reload="reload"></FilterChips>
        </template>
        <template slot="top-right">
          <q-btn flat round dense
                 class="q-mr-md"
                 @click="reload"
                 icon="refresh"/>
          <ColumnFilter :columns="columns"></ColumnFilter>
        </template>

        <q-tr slot="body"
              slot-scope="props" :class="`status_${props.row.status}`">
          <q-td v-for="col in props.cols" :key="col.field" :class="'text-' + col.align">
            <template v-if="col.field === 'Month'">
              {{ col.value }}
            </template>
            <template v-else>
              {{ col.value | moneyFormat }}
            </template>
          </q-td>
        </q-tr>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog" :max="999999"></BatchExportDialog>
  </q-page>
</template>

<script>

import { generateColumns, request } from '../../../common'
import ListPageMixin from '../../../mixins/ListPageMixin'
import { chartName } from '../../../const'
import eventBus from '../../../eventBus'
import _ from 'lodash'

export default {
  mixins: [
    ListPageMixin
  ],
  data () {
    return {
      filtersUrl: '/admin/report/revenue/filters',
      downloadUrl: '/admin/report/revenue/export',
      title: 'Net Revenue Report',
      timeField: 'time',
      cardProgramField: 'filter[cp.id=]',
      chartId: chartName.REVENUE_CHART,
      autoReloadWhenUrlChanges: true,
      baseColumns: generateColumns([
        'Month', 'Fee Income', 'Card Licensing Cost', 'Card Creation Cost', 'Load Cost', 'Affiliate Commission', 'System Load', 'KYC Cost', 'Postmark Cost', 'Twilio Cost', 'Net Revenue'
      ], [
        'Fee Income', 'Card Licensing Cost', 'Card Creation Cost', 'Load Cost', 'Affiliate Commission', 'System Load', 'KYC Cost', 'Postmark Cost', 'Twilio Cost', 'Net Revenue'
      ]),
      quick: {},
      filterOptions: [
        {
          value: 'filter[cp.id=]',
          label: 'Card program',
          options: [],
          source: 'cardPrograms'
        }
      ]
    }
  },
  methods: {
    async request ({ pagination }) {
      this.beforeRequest({ pagination })

      this.loading = true
      const data = this.$refs.filtersDialog.getFilters()
      data.keyword = this.keyword
      if (!_.isEmpty(this.$route.query)) {
        data.isFromDashboard = true
      }
      const resp = await request(`/admin/report/revenue/search`, 'form', data)
      this.loading = false
      if (resp.success) {
        this.pagination = pagination
        this.data = resp.data.data
        this.pagination.rowsNumber = resp.data.count
        this.quick = resp.data.quick
        this.chartData = resp.data.chartData
        if (this.chartData) {
          eventBus.$emit('reload-top-chart', this.chartData)
        }
      }
    },
    init () {
      this.data = []
      this.quick = {}
      this.filters = []
      this.keyword = ''

      this.columns = this.baseColumns
    }
  }
}
</script>
