<template>
  <q-page class="mt-20" id="dashboard_index">
    <div class="row ph-20">
      <div class="col-7 font-24">{{ title }}</div>
      <div class="col-5 text-right">
        <q-btn flat round dense icon="print"/>
        <q-btn flat round dense icon="mdi-publish"/>
        <q-btn flat round dense icon="save"/>
        <div class="v-border-right-line mv-3 mh-10 inline"></div>
        <q-btn flat round dense icon="mdi-arrow-left"/>
      </div>
    </div>
    <div class="flex ph-20">
      <span class="mr-15">Date range</span>
      <div class="bg-white dr-picker of-hidden" id="range_picker_dashboard">
        <span class="mr-25">{{ rangeName }}</span>
        <q-icon name="arrow_drop_down"></q-icon>
      </div>
      <span class="mr-25 ml-25">Group by</span>
      <q-select v-model="group" placeholder="Day" :options="groupBy"/>
    </div>
    <div class="chart" id="total-cards-graph">
      <div class="panel-heading">
        Total Cards
        <i class="fa fa-refresh mt-3 fa-spin pull-right"></i>
      </div>
      <div class="panel-body"></div>
    </div>
    <q-table
            v-if="items.length > 0"
            hide-bottom
            :data="items"
            :columns="columns"
            :pagination.sync="serverPagination"
            row-key="name">
      <template slot="top-left">
        <q-btn flat round dense icon="mdi-filter-variant" @click="addFilter"/>
      </template>
      <template slot="top-right">
        <q-btn flat round dense icon="edit" @click="edit">
          <q-popover class="popover-class">
            <q-list link separator class="scroll" style="min-width: 100px">
              <q-item
                      v-for="(item, index) in editList"
                      :key="`a-${index}`"
                      v-close-overlay
                      @click.native="item.isCheck = !item.isCheck"
                      :class="{'check-status': item.isCheck, 'not-check-status': !item.status}"
              >
                <q-item-side icon="check"  v-if="item.isCheck"/>
                <q-item-side icon="" v-if="!item.isCheck"/>
                <q-item-main :label="item.text" />
              </q-item>
            </q-list>
          </q-popover>
        </q-btn>
      </template>
      <q-tr slot="body" slot-scope="props" :props="props" :class="{'bg-white': props.row.__index % 2 == 0}">
        <q-td key="day" :props="props">
          {{ props.row.day }}
        </q-td>
        <q-td key="order_no" :props="props">
          {{ props.row.order_no }}
        </q-td>
        <q-td key="gross_sales" :props="props">
          {{ props.row.gross_sales }}
        </q-td>
        <q-td key="discounts" :props="props">
          {{ props.row.discounts }}
        </q-td>
        <q-td key="returns" :props="props">
          {{ props.row.returns }}
        </q-td>
        <q-td key="net_sales" :props="props">
          {{ props.row.net_sales }}
        </q-td>
        <q-td key="shipping" :props="props">
          {{ props.row.shipping }}
        </q-td>
        <q-td key="tax" :props="props">
          {{ props.row.tax }}
        </q-td>
        <q-td key="total_sales" :props="props">
          {{ props.row.total_sales }}
        </q-td>
      </q-tr>
    </q-table>
    <q-dialog
            class="filter-class"
            v-model="filterDia"
            stack-buttons
            @cancel="onCancel">
      <!-- This or use "title" prop on <q-dialog> -->
      <template slot="title">
        <span>Manage filters</span>
        <q-btn class="close" flat @click="onCancel" icon="close"/>
      </template>
      <div slot="body">
        <div v-for="(form, index) in filters" :key="index" class="mt-10">
          <div class="row">
            <div class="col-11">
              <div class="row">
                <q-select class="col-7" v-model="form.type" placeholder="This Month" :options="manageFitersOptions"/>
                <div class="col-1"></div>
                <q-select class="col-4" v-model="form.isOrNot" placeholder="This Month" :options="isOrNot"/>
              </div>
            </div>
            <q-btn flat round dense icon="delete" class="ml-10" @click="deleteFilter(index)"/>
          </div>
          <div class="row mt-10">
            <q-search class="col-11" v-model="date" placeholder="Search..."/>
          </div>
        </div>
        <q-btn class="mt-10 add-filter" flat icon="add" @click="addOneFilter">ADD FILTER</q-btn>
      </div>
      <template slot="buttons" slot-scope="props">
        <div class="flex-center text-right">
          <q-btn flat label="CLEAR ALL" class="clear-btn" icon="mdi-notification-clear-all" @click="props.cancel" />
          <q-btn label="ALLTY FILTERS" size="xm" class="filter-btn" @click="props.cancel" />
        </div>
      </template>
    </q-dialog>
  </q-page>
</template>

<script>
import echarts from 'echarts'
import { request } from '../../../common'
import _ from 'lodash'
import moment from 'moment'
import $ from 'jquery'
import 'daterangepicker/daterangepicker'
import 'daterangepicker/daterangepicker.scss'
import PageMixin from '../../../mixins/PageMixin'

export default {
  mixins: [
    PageMixin
  ],
  data () {
    return {
      title: 'Revenue',
      date: null,
      group: null,
      options: [],
      filterDia: false,
      graphStart: moment(),
      graphEnd: moment(),
      rangeName: null,
      groupBy: [
        {
          label: 'month',
          value: 1
        },
        {
          label: 'week',
          value: 2
        },
        {
          label: 'day',
          value: 3
        }
      ],
      filters: [{
        'type': 1,
        'isOrNot': 1
      }],
      manageFitersOptions: [
        {
          label: 'Adjustment',
          value: 1
        },
        {
          label: 'Canceled',
          value: 2
        },
        {
          label: 'Payment status',
          value: 3
        },
        {
          label: 'Fulfullment status',
          value: 4
        }
      ],
      isOrNot: [
        {
          label: 'Is not',
          value: 1
        },
        {
          label: 'Is',
          value: 2
        }
      ],
      editList: [
        {
          'text': 'Adjustment',
          'isCheck': false
        },
        {
          'text': 'Average order value',
          'isCheck': false
        },
        {
          'text': 'Cancelled',
          'isCheck': false
        },
        {
          'text': 'Discounts',
          'isCheck': false
        },
        {
          'text': 'Payment status',
          'isCheck': false
        },
        {
          'text': 'Fulfillment status',
          'isCheck': false
        }
      ],
      items: [
        {
          'day': 'Mon',
          'order_no': 'trn_20c710b422e24b',
          'gross_sales': '$ 762.89',
          'discounts': '$762.89',
          'returns': '$762.89',
          'net_sales': '$762.89',
          'shipping': '$762.89',
          'tax': '$762.89',
          'total_sales': '$762.89'
        },
        {
          'day': 'Mon',
          'order_no': 'trn_20c710b422e24b',
          'gross_sales': '$ 762.89',
          'discounts': '$762.89',
          'returns': '$762.89',
          'net_sales': '$762.89',
          'shipping': '$762.89',
          'tax': '$762.89',
          'total_sales': '$762.89'
        },
        {
          'day': 'Mon',
          'order_no': 'trn_20c710b422e24b',
          'gross_sales': '$ 762.89',
          'discounts': '$762.89',
          'returns': '$762.89',
          'net_sales': '$762.89',
          'shipping': '$762.89',
          'tax': '$762.89',
          'total_sales': '$762.89'
        },
        {
          'day': 'Mon',
          'order_no': 'trn_20c710b422e24b',
          'gross_sales': '$ 762.89',
          'discounts': '$762.89',
          'returns': '$762.89',
          'net_sales': '$762.89',
          'shipping': '$762.89',
          'tax': '$762.89',
          'total_sales': '$762.89'
        }

      ],
      serverPagination: {
        rowsNumber: 20
      },
      columns: [
        {
          name: 'day',
          label: 'Day',
          required: true,
          align: 'left',
          field: row => row.order_no
        },
        {
          name: 'order_no',
          label: 'Orders',
          required: true,
          align: 'left',
          field: row => row.order_no
        },
        {
          name: 'gross_sales',
          label: 'Gross Sales',
          align: 'left',
          field: row => row.gross_sales
        },
        {
          name: 'discounts',
          label: 'Discount',
          align: 'left',
          field: row => row.discounts
        },
        {
          name: 'returns',
          label: 'Returns',
          align: 'left',
          field: row => row.returns
        },
        {
          name: 'net_sales',
          label: 'Net Sales',
          align: 'left',
          field: row => row.net_sales
        },
        {
          name: 'shipping',
          label: 'Shipping',
          align: 'left',
          field: row => row.shipping
        },
        {
          name: 'tax',
          label: 'Tax',
          align: 'left',
          field: row => row.tax
        },
        {
          name: 'total_sales',
          label: 'Total Sales',
          align: 'left',
          field: row => row.total_sales
        }
      ]
    }
  },
  components: {
  },
  computed: {
  },
  methods: {
    async load () {
      this.$q.loading.show()
      // TODO: Adjust the url to use revenue query API
      const resp = await request('/admin/report/affiliate/search')
      this.$q.loading.hide()
      if (resp.success) {
        // this.items = resp.data.loads
      }
    },
    initChart () {
      let myChart = echarts.init(document.getElementById('total-cards-graph'), 'primary')
      let option = {
        xAxis: {
          type: 'category',
          data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          data: [820, 932, 901, 934, 1290, 1330, 1320],
          type: 'line'
        }]
      }
      myChart.setOption(option)
    },
    addFilter () {
      this.filterDia = true
    },
    edit () {
    },
    onCancel () {
      console.log('cancel')
      this.filterDia = false
    },
    addOneFilter () {
      this.filters.push([])
    },
    deleteFilter (index) {
      this.filters.splice(index, 1)
    },
    initDatePicker () {
      let types = ['This month', 'Last 30 days', 'This quarter', 'This year',
        'Last month', 'Last quarter', 'Last year']

      let presets = {
        'Today': [
          moment().startOf('date'),
          moment().endOf('date')
        ],
        'Last week': [
          moment().subtract(1, 'week').startOf('week'),
          moment().subtract(1, 'week').endOf('week')
        ],
        'This month': [
          moment().startOf('month'),
          moment().endOf('month')
        ],
        'Last 30 days': [
          moment().subtract(29, 'days'),
          moment()
        ],
        'This quarter': [
          moment().startOf('quarter'),
          moment().endOf('quarter')
        ],
        'Year to date': [
          moment().startOf('year'),
          moment().endOf('date')
        ],
        'This year': [
          moment().startOf('year'),
          moment().endOf('year')
        ],
        'Last month': [
          moment().subtract(1, 'month').startOf('month'),
          moment().subtract(1, 'month').endOf('month')
        ],
        'Last quarter': [
          moment().subtract(1, 'quarter').startOf('quarter'),
          moment().subtract(1, 'quarter').endOf('quarter')
        ],
        'Last year': [
          moment().subtract(1, 'year').startOf('year'),
          moment().subtract(1, 'year').endOf('year')
        ]
      }
      let ranges = {}, defaultKey = null
      _.forEach(types, function (v) {
        if (presets[v]) {
          ranges[v] = presets[v]

          if (defaultKey === null) {
            defaultKey = v
          }
        }
      })

      if (defaultKey) {
        this.graphStart = presets[defaultKey][0]
        this.graphEnd = presets[defaultKey][1]
        this.rangeName = defaultKey
      }
      $('#range_picker_dashboard').daterangepicker({
        startDate: this.graphStart,
        endDate: this.graphEnd,
        ranges: ranges
      }, (start, end, rangeName) => {
        this.cb(start, end, rangeName)
      })
      $('.daterangepicker').css('display', 'none')
    },
    cb (start, end, rangeName) {
      this.graphStart = start
      this.graphEnd = end
      if (rangeName === 'Custom Range') {
        rangeName = start.format('L') + ' ~ ' + end.format('L')
      }
      this.rangeName = rangeName
    }
  },
  mounted () {
    this.initChart()
    this.load()
    this.$nextTick(() => {
      this.initDatePicker()
    })
  }
}
</script>

<style lang="scss">
  @import '../../../css/variable';
  #dashboard_index {
    .flex {
      span {
        line-height: 34px;
      }
      .q-input-target {
        padding-right: 25px;
      }
    }
    .chart {
      background: white;
      margin: 20px;
      height: 326px;
    }
    .q-table thead, .q-table tr, .q-table th, .q-table td {
      border-color: transparent;
    }
    .q-table {
      tr {
        border-bottom: 1px solid $line-light;
        border-top: 1px solid $line-light;
      }
      color: $text;
      th {
        font-size: 14px;
        color: $text-highlight;
      }
    }
    .q-btn:hover {
      .q-icon {
        color: var(--q-color-primary);
      }
    }
  }
</style>
