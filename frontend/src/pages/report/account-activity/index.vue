<template>
  <q-page id="card_report__account_activity_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-field class="input-button-group inline-flex mr-20">
          <div class="row no-wrap">
            <q-input v-model="keyword"
                     clearable
                     @clear="reload"
                     placeholder="User ID, Account number..."></q-input>
            <q-btn label="Search"
                   color="grey-4"
                   class="no-shadow"
                   @click="reload"
                   text-color="black"></q-btn>
          </div>
        </q-field>

        <q-btn class="mr-10"
               color="black"
               @click="download"
               label="Export"></q-btn>
      </div>
    </div>
    <div class="page-content">
      <div class="quick-result">
        <div>
          <h5>{{ quick.totalSize | number }}</h5>
          <div class="description">Total # of accounts</div>
        </div>
        <div>
          <h5>{{ quick.balance | moneyFormat('USD', true) }}</h5>
          <div class="description">Total # of balance amount in USD</div>
        </div>
      </div>

      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               :rows-per-page-options="[5, 10, 20, 50]"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <q-btn flat round dense
                 icon="mdi-filter-variant"
                 @click="filtersDialog = true"/>
          <FilterChips :filters.sync="filters"
                       :options="filterOptions"
                       v-show="!loadingFilters"
                       @reload="reload"></FilterChips>
        </template>
        <template slot="top-right">
          <q-btn flat round dense
                 class="q-mr-md"
                 @click="reload"
                 icon="refresh"/>
          <ColumnFilter :columns="columns"></ColumnFilter>
        </template>

        <q-tr slot="body"
              slot-scope="props" :class="`status_${props.row.status}`">
          <q-td v-for="col in props.cols" :key="col.field">
            <template v-if="!col.field">
              <q-btn-dropdown size="xs" :label="col.label">
                <q-list link>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else-if="col.field === 'userId' && p('user')">
              <a :href="`/admin#/a/i/admin__user_modify__modify?user_id=${col.value}`"
                 target="_blank">{{ col.value }}</a>
            </template>
            <template v-else>
              {{ col.value }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination" slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>
import _ from 'lodash'
import { request } from '../../../common'
import PageMixin from '../../../mixins/PageMixin'
import ColumnFilter from '../../../components/ColumnFilter'
import ManageFiltersDialog from '../../../components/ManageFiltersDialog'
import BatchExportDialog from '../../../components/BatchExportDialog'
import Pagination from '../../../components/Pagination'
import FilterChips from '../../../components/FilterChips'

export default {
  mixins: [
    PageMixin
  ],
  components: {
    ColumnFilter,
    ManageFiltersDialog,
    BatchExportDialog,
    Pagination,
    FilterChips
  },
  data () {
    return {
      title: 'Card Account Activity Reporting',
      hideAjaxBarWhenLoading: true,
      keyword: null,
      pagination: {
        rowsNumber: 0,
        rowsPerPage: 10,
        page: 1
      },
      columns: [],
      baseColumns: [{
        field: 'id',
        label: 'Id',
        align: 'left'
      }, {
        field: 'userId',
        label: 'User ID',
        align: 'left'
      }, {
        field: 'firstName',
        label: 'First name',
        align: 'left'
      }, {
        field: 'lastName',
        label: 'Last name',
        align: 'left'
      }, {
        field: 'email',
        label: 'Email',
        align: 'left'
      }, {
        field: 'accountNumber',
        label: 'Account Number',
        align: 'left'
      }, {
        field: 'cardProgramName',
        label: 'Program',
        align: 'left'
      }, {
        field: 'cardCustName',
        label: 'Card name',
        align: 'left'
      }, {
        field: 'balance',
        label: 'Balance',
        align: 'left'
      }, {
        field: 'activityTypes',
        label: 'Activity type',
        align: 'left'
      }, {
        field: 'balanceChangedAt',
        label: 'Balance changed at',
        align: 'left'
      }, {
        field: 'billingAddressType',
        label: 'Billing Address Type',
        align: 'left'
      }, {
        field: 'address',
        label: 'Addr1',
        align: 'left'
      }, {
        field: 'addressLine',
        label: 'Addr2',
        align: 'left'
      }, {
        field: 'city',
        label: 'City',
        align: 'left'
      }, {
        field: 'stateName',
        label: 'State',
        align: 'left'
      }, {
        field: 'region',
        label: 'Region',
        align: 'left'
      }, {
        field: 'countryName',
        label: 'Country',
        align: 'left'
      }, {
        field: 'affiliateName',
        label: 'Affiliate',
        align: 'left'
      }],
      data: [],
      quick: {
        totalCount: 0,
        balance: 0
      },
      filtersDialog: false,
      filters: [],
      filterOptions: [
        {
          value: 'time-point',
          label: 'Time point',
          options: [],
          source: 'months'
        }, {
          value: 'type',
          label: 'Account Activity Type',
          options: [],
          source: 'types'
        }, {
          value: 'filter[cp.id=]',
          label: 'Card program',
          options: [],
          source: 'cardPrograms'
        }, {
          value: 'filter[ct.id=]',
          label: 'Card Type',
          options: [],
          source: 'cardTypes'
        }, {
          value: 'filter[c.id=]',
          label: 'Card nick name',
          options: [],
          source: 'cpCardTypes'
        }, {
          value: 'filter[pm.id=]',
          label: 'Program manager',
          options: [],
          source: 'programManagers'
        }, {
          value: 'filter[bp.id=]',
          label: 'Brand Partner',
          options: [],
          source: 'brandPartners'
        }, {
          value: 'filter[mp.id=]',
          label: 'Marketing partner',
          options: [],
          source: 'marketingPartners'
        }, {
          value: 'filter[p.id=]',
          label: 'Processor',
          options: [],
          source: 'processors'
        }, {
          value: 'filter[ib.id=]',
          label: 'Issuing bank',
          options: [],
          source: 'issuingBanks'
        }, {
          value: 'filter[country.region=]',
          label: 'Region',
          options: [],
          source: 'regions'
        }, {
          value: 'filter[country.id=]',
          label: 'Country',
          options: [],
          search: true,
          source: 'countries'
        }, {
          value: 'filter[state.id=]',
          label: 'State',
          options: [],
          search: true,
          source: 'states',
          map: {
            country: 'filter[country.id=]'
          }
        }, {
          value: 'filter[u.city]',
          label: 'City'
        }, {
          value: 'filter[aff.affType=]',
          label: 'Affiliate Type',
          options: [],
          source: 'affiliateTypes'
        }, {
          value: 'filter[u.affiliate=]',
          label: 'Affiliate',
          options: [],
          search: true,
          source: 'affiliates',
          map: {
            type: 'filter[aff.affType=]'
          }
        }
      ],
      loadingFilters: false
    }
  },
  watch: {
    '$route.fullPath' () {
      this.init()
      this.initFilters()
    }
  },
  computed: {
    visibleColumns () {
      return _.filter(this.columns, c => {
        return !c.hidden
      })
    }
  },
  methods: {
    reload () {
      this.request({
        pagination: this.pagination
      })
    },
    async request ({ pagination }) {
      this.loading = true
      const data = this.$refs.filtersDialog.getFilters()
      data.keyword = this.keyword
      const resp = await request(`/admin/report/card-account-activity/search/${pagination.page}/${pagination.rowsPerPage}`, 'form', data)
      this.loading = false
      if (resp.success) {
        this.pagination = pagination
        this.data = resp.data.data
        console.log(this.data)
        this.pagination.rowsNumber = resp.data.total
        this.quick = resp.data.quick
      }
    },
    async initFilters () {
      this.loadingFilters = true
      const resp = await request(`/admin/report/card-account-activity/filters`)
      this.loadingFilters = false
      if (resp.success) {
        _.forEach(this.filterOptions, option => {
          if (option.options && option.options.length <= 0 && option.source) {
            option.options = resp.data[option.source] || []
          }
        })
      }
    },
    download () {
      const data = this.$refs.filtersDialog.getFilters()
      data.keyword = this.keyword
      this.$refs.exportDialog.show('/admin/report/card-account-activity/export', data)
    },
    init () {
      this.data = []
      this.quick = {}
      this.filters = []
      this.keyword = ''

      this.columns = this.baseColumns
    }
  },
  mounted () {
    this.init()
    this.initFilters()
  }
}
</script>

<style lang="scss">
  @import '../../../css/variable';

  #card_report__account_activity_page {
  }
</style>
