<template>
  <q-page id="report__affiliate_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-field class="input-button-group inline-flex mr-20">
          <div class="row no-wrap">
            <q-input v-model="keyword"
                     clearable
                     @clear="reload"
                     placeholder="Affiliate ID"></q-input>
            <q-btn label="Search"
                   color="grey-4"
                   class="no-shadow"
                   @click="reload"
                   text-color="black"></q-btn>
          </div>
        </q-field>

        <q-btn class="mr-10"
               color="black"
               @click="download"
               label="Export"></q-btn>
      </div>
    </div>
    <div class="page-content">
      <div class="quick-result">
        <div>
          <h5>{{ quick.hits | number }}</h5>
          <div class="description">Link Clicks</div>
        </div>
        <div>
          <h5>{{ quick.signUps | number }}</h5>
          <div class="description">
            Pending Members
            <q-tooltip anchor="bottom left" self="top left">Users who signed up by the link but have not loaded</q-tooltip>
          </div>
        </div>
        <div>
          <h5>{{ quick.signUpsLoaded | number }}</h5>
          <div class="description">
            Linked Members
            <q-tooltip anchor="bottom left" self="top left"># of signups who have loaded</q-tooltip>
          </div>
        </div>
        <div>
          <h5>{{ quick.commission | moneyFormat('USD', true) }}</h5>
          <div class="description">Total Commission</div>
        </div>
      </div>

      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               :rows-per-page-options="[5, 10, 20, 50]"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <q-btn flat round dense
                 icon="mdi-filter-variant"
                 @click="filtersDialog = true"/>
          <FilterChips :filters.sync="filters"
                       :options="filterOptions"
                       v-show="!loadingFilters"
                       @reload="reload"></FilterChips>
        </template>
        <template slot="top-right">
          <q-btn flat round dense
                 class="q-mr-md"
                 @click="reload"
                 icon="refresh"/>
          <ColumnFilter :columns="columns"></ColumnFilter>
        </template>

        <q-tr slot="body"
              slot-scope="props" :class="`status_${props.row.status}`">
          <q-td v-for="col in props.cols" :key="col.field">
            <template v-if="!col.field">
              <q-btn-dropdown size="xs" :label="col.label">
                <q-list link>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else-if="col.field === 'userId' && p('user')">
              <a :href="`/admin#/a/i/admin__user_modify__modify?user_id=${col.value}`"
                 target="_blank">{{ col.value }}</a>
            </template>
            <template v-else>
              {{ col.value }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination" slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"></BatchExportDialog>
  </q-page>
</template>

<script>
import _ from 'lodash'
import { request } from '../../../common'
import PageMixin from '../../../mixins/PageMixin'
import ColumnFilter from '../../../components/ColumnFilter'
import ManageFiltersDialog from '../../../components/ManageFiltersDialog'
import BatchExportDialog from '../../../components/BatchExportDialog'
import Pagination from '../../../components/Pagination'
import FilterChips from '../../../components/FilterChips'

export default {
  mixins: [
    PageMixin
  ],
  components: {
    ColumnFilter,
    ManageFiltersDialog,
    BatchExportDialog,
    Pagination,
    FilterChips
  },
  data () {
    return {
      title: 'Affiliate Reporting',
      hideAjaxBarWhenLoading: true,
      keyword: null,
      pagination: {
        rowsNumber: 0,
        rowsPerPage: 10,
        page: 1
      },
      columns: [],
      baseColumns: [{
        field: 'receivedAt',
        label: 'Date time',
        align: 'left'
      }, {
        field: 'affType',
        label: 'Affiliate Type',
        align: 'left'
      }, {
        field: 'affName',
        label: 'Affiliate Name',
        align: 'left'
      }, {
        field: 'transactionNo',
        label: 'Transaction ID',
        align: 'left'
      }, {
        field: 'type',
        label: 'Type',
        align: 'left'
      }, {
        field: 'userId',
        label: 'User ID',
        align: 'left'
      }, {
        field: 'methodName',
        label: 'Load Method',
        align: 'left'
      }, {
        field: 'countryName',
        label: 'Country',
        align: 'left'
      }, {
        field: 'membershipFeeUSD',
        label: 'Membership Fee',
        align: 'left'
      }, {
        field: 'loadAmountUSD',
        label: 'Load Amount',
        align: 'left'
      }, {
        field: 'cardType',
        label: 'Card Type',
        align: 'left'
      }, {
        field: 'membershipCommissionUSD',
        label: 'New Consumer Pay out',
        align: 'left'
      }, {
        field: 'loadCommissionUSD',
        label: 'Net Load Pay out',
        align: 'left'
      }, {
        field: 'commissionUSD',
        label: 'Total commission',
        align: 'left'
      }],
      data: [],
      quick: {
        hits: 0,
        signUps: 0,
        signUpsLoaded: 0,
        commission: 0
      },
      filtersDialog: false,
      filters: [],
      filterOptions: [
        {
          value: 'receivedData',
          label: 'Received date',
          range: [
            {
              value: 'range[ucl.receivedAt][start]',
              type: 'date'
            }, {
              value: 'range[ucl.receivedAt][end]',
              type: 'date'
            }
          ]
        }, {
          value: 'filter[aff.affType=]',
          label: 'Affiliate Type',
          options: [],
          source: 'affiliateTypes'
        }, {
          value: 'filter[aff.id=]',
          label: 'Affiliate',
          options: [],
          search: true,
          source: 'affiliates',
          map: {
            type: 'filter[aff.affType=]'
          }
        }, {
          value: 'filter[aff.affId=]',
          label: 'Affiliate ID',
          search: true
        }
      ],
      loadingFilters: false
    }
  },
  watch: {
    '$route.fullPath' () {
      this.init()
      this.initFilters()
    }
  },
  computed: {
    visibleColumns () {
      return _.filter(this.columns, c => {
        return !c.hidden
      })
    }
  },
  methods: {
    reload () {
      this.request({
        pagination: this.pagination
      })
    },
    async request ({ pagination }) {
      this.loading = true
      const data = this.$refs.filtersDialog.getFilters()
      data.keyword = this.keyword
      const resp = await request(`/admin/report/affiliate/search/${pagination.page}/${pagination.rowsPerPage}`, 'form', data)
      this.loading = false
      if (resp.success) {
        this.pagination = pagination
        this.data = resp.data.data
        this.pagination.rowsNumber = resp.data.total
        this.quick = resp.data.quick
      }
    },
    async initFilters () {
      this.loadingFilters = true
      const resp = await request(`/admin/report/affiliate/filters`)
      this.loadingFilters = false
      if (resp.success) {
        _.forEach(this.filterOptions, option => {
          if (option.options && option.options.length <= 0 && option.source) {
            option.options = resp.data[option.source] || []
          }
        })
      }
    },
    download () {
      const data = this.$refs.filtersDialog.getFilters()
      data.keyword = this.keyword
      this.$refs.exportDialog.show('/admin/report/affiliate/export', data)
    },
    init () {
      this.data = []
      this.quick = {}
      this.filters = []
      this.keyword = ''

      this.columns = this.baseColumns
      this.reload()
    }
  },
  mounted () {
    this.init()
    this.initFilters()
  }
}
</script>
