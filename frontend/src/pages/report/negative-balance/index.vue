<template>
  <q-page id="report_negative_balance__list_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-btn class="mr-10"
               color="black"
               @click="download"
               label="Export"></q-btn>
      </div>
    </div>
    <div class="page-content">
      <div class="quick-result">
        <div>
          <h5>{{ quick.totalSize | number }}</h5>
          <div class="description"># of Negative Accounts</div>
        </div>
        <div>
          <h5>{{ quick.totalAmountUSD | moneyFormat('USD', true) }}</h5>
          <div class="description">$ of Negative Accounts</div>
        </div>
        <div>
          <h5>{{ quick.totalAmountUSD/pagination.rowsNumber | moneyFormat('USD', true) }}</h5>
          <div class="description">Avg Balance</div>
        </div>
      </div>

      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               :rows-per-page-options="[5, 10, 20, 50]"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <q-btn flat round dense
                 icon="mdi-filter-variant"
                 @click="filtersDialog = true"/>
          <FilterChips :filters.sync="filters"
                       :options="filterOptions"
                       v-show="!loadingFilters"
                       @reload="reload"></FilterChips>
        </template>
        <template slot="top-right">
          <q-btn flat round dense
                 class="q-mr-md"
                 @click="reload"
                 icon="refresh"/>
          <ColumnFilter :columns="columns"></ColumnFilter>
        </template>

        <q-tr slot="body" slot-scope="props">
          <q-td v-for="col in props.cols" :key="col.field" :class="'text-' + col.align">
            <template v-if="col.field === 'User ID' && e('user')">
              <a :href="`/admin#/a/i/admin__user_modify__modify?user_id=${col.value}`"
                 target="_blank">{{ col.value }}</a>
            </template>
            <template v-else-if="col.field.endsWith('Balance') || col.field === 'Sum'">
              {{ col.value | moneyFormat }}
            </template>
            <template v-else-if="col.field === 'Related Transactions'">
              <a :href="`/admin#/a/transactions/list?ids=${props.row['Related Transactions Id'].join(',')}`"
                 v-if="col.value > 0 && p('card_transaction_list')" target="_blank">{{ col.value }}</a>
              <span v-else>{{ col.value }}</span>
            </template>
            <template v-else-if="col.field === 'Actions'">
              <q-btn-dropdown size="xs" :label="col.label">
                <q-list link>
                  <q-item  v-if="e('user')"
                           v-close-overlay
                           @click.native="editUser(props.row)">
                    <q-item-main>View User</q-item-main>
                  </q-item>
                  <q-item v-if="p('user_managment__user__login_at') && !$store.state.User.isGranted"
                          v-close-overlay
                          @click.native="$c.loginAs(props.row['User ID'])">
                    <q-item-main>Login As</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              {{ col.value }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination" slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog" :max="999999"></BatchExportDialog>
  </q-page>
</template>

<script>

import { generateColumns, request } from '../../../common'
import ListPageMixin from '../../../mixins/ListPageMixin'
import _ from 'lodash'

export default {
  mixins: [
    ListPageMixin
  ],
  data () {
    return {
      filtersUrl: '/admin/report/negative-balance/filters',
      downloadUrl: '/admin/report/negative-balance/export',
      title: 'Negative Balance Report',
      timeField: 'time',
      cardProgramField: 'filter[cp.id=]',
      autoReloadWhenUrlChanges: true,
      baseColumns: generateColumns([
        'User ID', 'First Name', 'Last Name', 'Email Address', 'Country', 'Account Status',
        'Legacy Balance', 'Account Balance', 'Days Negative', 'Related Transactions',
        'Actions'
      ], [
        'Legacy Balance', 'Account Balance', 'Days Negative', 'Related Transactions'
      ]),
      quick: {
        totalSize: 0,
        totalAmountUSD: 0
      },
      filterOptions: [
        {
          value: 'filter[cp.id=]',
          label: 'Card program',
          options: [],
          source: 'cardPrograms'
        }, {
          value: 'filter[u.id=]',
          label: 'User ID'
        }, {
          value: 'filter[u.firstName]',
          label: 'First Name'
        }, {
          value: 'filter[u.lastName]',
          label: 'Last Name'
        }, {
          value: 'filter[u.email]',
          label: 'Email Address'
        }, {
          value: 'balance',
          label: 'Account Balance',
          range: [{
            value: 'range[uc.balance][min]',
            type: 'amount'
          }, {
            value: 'range[uc.balance][max]',
            type: 'amount'
          }]
        }
      ]
    }
  },
  methods: {
    async request ({ pagination }) {
      this.beforeRequest({ pagination })

      this.loading = true
      const data = this.$refs.filtersDialog.getFilters()
      data.keyword = this.keyword
      if (!_.isEmpty(this.$route.query)) {
        data.isFromDashboard = true
      }
      const resp = await request(`/admin/report/negative-balance/search/${pagination.page}/${pagination.rowsPerPage}`, 'form', data)
      this.loading = false
      if (resp.success) {
        this.pagination = pagination
        this.data = resp.data.data
        this.pagination.rowsNumber = resp.data.count
        this.quick = resp.data.quick
      }
    },
    init () {
      this.data = []
      this.quick = {}
      this.filters = [{
        field: 'balance',
        predicate: '=',
        value_1: -50
      }]
      this.keyword = ''

      this.columns = this.baseColumns
    },

    editUser (row) {
      window.open(`/admin#/a/i/admin__user_modify__modify?user_id=${row['User ID']}`, '_blank')
    }
  }
}
</script>
