<template>
  <q-page id="partner_page">
    <div class="page-header">
      <div class="title">{{ title }}</div>
      <div class="fun-group">
        <q-btn flat
               round
               class="ml-5"
               @click="download"
               icon="mdi-content-save-all">
          <q-tooltip>Export all the fields</q-tooltip>
        </q-btn>
        <div class="divider"></div>
        <q-btn flat
               v-if="c.isSuperAdmin(user)"
               @click="createPartner"
               icon="mdi-plus"
               label="Create New Partner"></q-btn>
      </div>
    </div>
    <div class="page-content">
      <div class="quick-result">
        <div class="max-w-300">
          <h5>{{ quick.count | number }}</h5>
          <div class="description">Number of Partners</div>
        </div>
      </div>

      <q-table :data="data"
               :columns="visibleColumns"
               :pagination.sync="pagination"
               :loading="loading"
               :rows-per-page-options="[5, 10, 20, 50]"
               @request="request"
               separator="none"
               row-key="id">
        <template slot="top-left">
          <q-btn flat
                 round
                 dense
                 icon="mdi-filter-variant"
                 @click="filtersDialog = true" />
          <FilterChips :filters.sync="filters"
                       :options="filterOptions"
                       v-show="!loadingFilters"
                       @reload="reload"></FilterChips>
        </template>
        <template slot="top-right">
          <q-btn flat
                 round
                 dense
                 class="q-mr-md"
                 @click="reload"
                 icon="refresh" />
          <ColumnFilter :columns="columns"></ColumnFilter>
        </template>

        <q-tr slot="body"
              slot-scope="props"
              :class="`status_${props.row.status}`">
          <q-td v-for="col in props.cols"
                :key="col.field"
                :class="`text-${col.align || 'left'}`">
            <template v-if="col.field === 'status'">
              <q-chip class="font-13"
                      :class="statusClass(col.value)">
                {{ col.value }}
              </q-chip>
            </template>
            <template v-else-if="col.field === 'country'">
              <p style="margin:0; max-width: 400px;overflow: hidden;text-overflow: ellipsis ;">{{col.value}}</p>
            </template>
            <template v-else-if="!col.field">
              <q-btn-dropdown size="xs"
                              color="grey-3"
                              text-color="black"
                              :label="col.label">
                <q-list link>
                  <q-item v-close-overlay
                          @click.native="view(props.row)">
                    <q-item-main>View</q-item-main>
                  </q-item>
                  <q-item v-close-overlay
                          @click.native="edit(props.row)">
                    <q-item-main>Edit</q-item-main>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
            <template v-else>
              {{ col.value }}
            </template>
          </q-td>
        </q-tr>

        <div slot="pagination"
             slot-scope="props">
          <Pagination :props="props"
                      :pagination="pagination"
                      @reload="reload"></Pagination>
        </div>
      </q-table>
    </div>

    <ManageFiltersDialog ref="filtersDialog"
                         :visible.sync="filtersDialog"
                         :filters.sync="filters"
                         :options="filterOptions"
                         @submit="reload"></ManageFiltersDialog>

    <BatchExportDialog ref="exportDialog"
                       :max="downloadMax"></BatchExportDialog>

  </q-page>
</template>

<script>
import { request, EventHandlerMixin } from '../../common'
import ListPageMixin from '../../mixins/ListPageMixin'

export default {
  mixins: [
    ListPageMixin,
    EventHandlerMixin('admin_user_quick_search', 'search')
  ],
  data () {
    return {
      title: 'Partners',
      url: '/admin/partners-search',
      // filtersUrl: '/admin/load_method_search/filters',
      // downloadUrl: '/admin/load_method_search/export',
      downloadMax: 1000,
      autoReloadWhenUrlChanges: true,
      columns: [
        {
          field: 'name',
          label: 'Partner Name',
          align: 'left'
        }, {
          field: 'forwarding',
          label: 'Freight Forwarding',
          align: 'left'
        }, {
          field: 'locator',
          label: 'Store locator',
          align: 'left'
        }, {
          field: 'memberShipFeeStr',
          label: 'Membership Fee',
          align: 'left'
        }, {
          field: 'monthlyFeeStr',
          label: 'Monthly Fee',
          align: 'left'
        }, {
          field: 'transactionFeeStr',
          label: 'Transaction Fee',
          align: 'left'
        }, {
          field: 'unloadFeeStr',
          label: 'Unload Fee',
          align: 'left'
        }, {
          label: 'Actions',
          align: 'center'
        }
      ],
      quick: {
        count: 0
      },
      filterOptions: [
        {
          value: 'type',
          label: 'Type',
          options: [],
          source: 'type'
        }, {
          value: 'country',
          label: 'Country',
          search: true,
          options: [],
          source: 'countrys'
        },
        {
          value: 'enabled',
          label: 'Status',
          options: [
            {
              label: 'Active',
              value: 'active'
            },
            {
              label: 'Inactive',
              value: 'inactive'
            }
          ]
        }
      ],
      autoLoad: true
    }
  },
  computed: {
    user () {
      return this.$store.state.User
    }
  },
  methods: {
    edit (row) {
      this.$router.push(`/a/partners/edit/${row['id']}`)
    },
    view (row) {
      this.$router.push(`/a/partners/detail/${row['id']}`)
    },
    statusClass (status) {
      const cls = []
      cls.push({
        'Active': 'positive',
        'Inactive': 'inactive'
      }[status] || status)

      return cls
    },
    download (type = 'all') {
      if (typeof type !== 'string') {
        type = 'all'
      }
      this.downloadMax = type === 'min' ? 99999999 : 1000

      const data = this.mergeQuerySortParams(this.pagination)
      data.query_type = type
      this.$refs.exportDialog.show(this.downloadUrl, data)
    },

    async request ({ pagination }) {
      this.beforeRequest({ pagination })

      this.loading = true
      const data = this.getQueryParams()
      const resp = await request(`/admin/partners-search/${pagination.page}/${pagination.rowsPerPage}`, 'form', data)
      this.loading = false
      if (resp.success) {
        this.pagination = pagination
        this.data = resp.data.data
        this.quick.count = resp.data.total
        this.pagination.rowsNumber = resp.data.total
      }
    },
    createPartner () {
      this.$router.push(`/a/partners/create`)
    }
  },
  mounted () {}
}
</script>

<style lang="scss">
@import "../../css/variable";

#partner_page {
}

.q-chip.inactive {
  color: #ff4852;
  background: rgba(#ff4852, 0.1);
  border-radius: 2.5px;
  min-width: 65px;
  font-size: 7px !important;
  padding: 0 10px;
  text-align: center;
  width: auto;
}
.q-chip.positive {
  color: #00d993;
  background: rgba(#00d993, 0.1);
  border-radius: 2.5px;
  min-width: 65px;
  font-size: 7px !important;
  padding: 0 10px;
  text-align: center;
  width: auto;
}
</style>
