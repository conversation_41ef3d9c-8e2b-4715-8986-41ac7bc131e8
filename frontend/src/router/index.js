import Vue from 'vue'
import VueRouter from 'vue-router'
import store from '../store'
import routes from './routes'

import queryString from 'query-string'
import { isLocal, request, clf, isSuperAdmin } from './../common'
import { deviceMode } from './../const'

Vue.use(VueRouter)

const router = new VueRouter({
  scrollBehavior: () => ({ y: 0 }),
  routes,

  /*
   * NOTE! Change Vue Router mode from quasar.conf.js -> build -> vueRouterMode
   *
   * If you decide to go with "history" mode, please also set "build.publicPath"
   * to something other than an empty string.
   * Example: '/' instead of ''
   */

  // Leave these as is and change from quasar.conf.js instead!
  // quasar.conf.js -> build -> vueRouterMode
  // quasar.conf.js -> build -> publicPath
  mode: process.env.VUE_ROUTER_MODE,
  base: process.env.VUE_ROUTER_BASE
})

let initialized = false

/**
 * for mobile app
 */
router.beforeEach(async (to, from, next) => {
  if (deviceMode !== 'clf') {
    return next()
  }

  if (!store.state.Config.token) {
    if (!to.path.startsWith('/clf/login')) {
      return next('/clf/login')
    }
    return next()
  }

  if (!initialized) {
    store.dispatch('User/init')
    store.dispatch('Config/init')
    initialized = true
  }

  if (!to.path.startsWith('/clf')) {
    return next('/clf')
  }
  return next()
})

/**
 * for admin portal
 */
router.beforeEach(async (to, from, next) => {
  if (deviceMode === 'clf') {
    return next()
  }

  if (!initialized) {
    let token = window['x-token']
    const search = queryString.parse(window.location.search)
    if (!token) {
      token = search['x-token']
    }
    if (!token && isLocal()) {
      const resp = await request('/user/api/token')
      if (resp.success) {
        token = resp.data
      }
    }
    if (token) {
      store.commit('Config/update', {
        token: token
      })
      await store.dispatch('User/init')
      if (!to.fullPath || to.fullPath === '/') {
        return next(`/${store.state.User.adminLayout || 'a'}`)
      }
    }
    store.dispatch('Config/init')

    initialized = true
  }

  // Check permission for CLF super admin
  if (to.path.startsWith('/a/clf/sa')) {
    if (!clf.is() || !isSuperAdmin()) {
      return next('/a')
    }
  }

  next()
})

router.beforeEach((to, from, next) => {
  if (window._sift) {
    window._sift.push(['_trackPageview'])
  }
  let path = to.path
  if (path === '/a/i/admin') {
    return
  }

  next()
})

/**
 * for FIS
 */
router.beforeEach((to, from, next) => {
  if (to.fullPath.startsWith('/a/fis/')) {
    return next(to.fullPath.replace('/a/fis/', '/j/fis/'))
  }

  next()
})

/**
 * for FaaS
 */
router.beforeEach((to, from, next) => {
  if (location.pathname.startsWith('/faas/widget/') && !to.fullPath.startsWith('/faas/widget')) {
    const all = location.pathname.split('/')
    return next('/faas/widget/' + all[all.length - 1])
  }

  next()
})

/**
 * for Spendr
 */
router.beforeEach((to, from, next) => {
  if (location.pathname.startsWith('/spendr/merchant/onboard') && !to.fullPath.startsWith('/spendr/merchant/onboard')) {
    return next('/spendr/merchant/onboard')
  }

  next()
})

// /**
//  * for debugging
//  */
// router.beforeEach((to, from, next) => {
//   if (window.location.hostname === 'localhost') {
//     console.log('--- fullPath: ' + to.fullPath)
//   }
//   next()
// })

router.afterEach((to, from, next) => {
  if (to.params.userCardId) {
    store.dispatch('UserCard/init', {
      userCardId: to.params.userCardId
    })
  }
})

export default router
