export default [
  {
    path: 'card-program/owners',
    component: () => import('pages/card-program/owners'),
    meta: {
      search: {
        tip: 'User ID, First Name, Last Name, Full Name, Email',
        event: 'admin_card_program_owners_search'
      }
    }
  },
  {
    path: 'card-program/owners/add',
    component: () => import('pages/card-program/owners/detail')
  },
  {
    path: 'card-program/owners/edit/:id',
    component: () => import('pages/card-program/owners/detail')
  },
  {
    path: 'card-program/load-methods',
    component: () => import('pages/card-program/load-methods')
  }
]
