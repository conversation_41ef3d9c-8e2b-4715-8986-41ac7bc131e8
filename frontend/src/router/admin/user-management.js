export default [
  {
    path: 'user-management/user',
    component: () => import('pages/user-management/user')
  },
  {
    path: 'user-management/kyc-exceptions',
    component: () => import('pages/user-management/kyc-exceptions'),
    meta: {
      search: {
        tip: 'Registration Request ID, First Name, Last Name, Email, Phone Numbers, Country, City, State/Province',
        event: 'admin_user_kyc_exceptions_search'
      }
    }
  },
  {
    path: 'user-management/instant-registration',
    component: () => import('pages/user-management/instant-registration')
  }
]
