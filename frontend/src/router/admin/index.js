export default {
  path: '/a',
  component: () => import('layouts/admin'),
  children: [
    {
      path: '',
      component: () => import('pages/common/welcome')
    },
    {
      path: 'dummy',
      redirect: '/a'
    },
    {
      path: 'dashboard',
      component: () => import('pages/dashboard/index')
    }, {
      path: 'i/:url',
      component: () => import('pages/common/frame')
    },
    {
      path: 'debug/error/:id',
      component: () => import('pages/common/error_detail')
    },
    {
      path: 'debug/invoke/:id',
      component: () => import('pages/common/invoke_detail')
    },
    ...require('./user-management').default,
    ...require('./transactions').default,
    ...require('./fee').default,
    ...require('./report').default,
    ...require('./marketing').default,
    ...require('./card-management').default,
    ...require('./card-program').default,
    ...require('./monitor').default,
    ...require('./system').default,
    {
      path: 'analytics-report',
      component: () => import('pages/dashboard/analytics/index')
    },

    ...require('./clf/index').default,
    ...require('./partner').default,
    ...require('../jake/leaflink/index').default,
    ...require('../jake/wilen/index').default
  ]
}
