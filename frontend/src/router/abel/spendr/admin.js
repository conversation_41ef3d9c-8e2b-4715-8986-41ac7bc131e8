export default [
  {
    path: 'spendr/admins',
    component: () => import('pages/spendr/admins/index.vue')
  },
  {
    path: 'spendr/merchants',
    component: () => import('pages/spendr/merchants/index.vue')
  },
  {
    path: 'spendr/merchant/:id/onboard',
    component: () => import('pages/spendr/merchants/onboard/index.vue')
  },
  {
    path: 'spendr/merchant/:id/profile/:adminId',
    component: () => import('pages/spendr/merchants/profile/index.vue')
  },
  {
    path: 'spendr/members',
    component: () => import('pages/spendr/members/index.vue')
  },
  {
    path: 'spendr/transactions',
    component: () => import('pages/spendr/transactions/index.vue')
  },
  {
    path: 'spendr/merchant-transactions',
    component: () => import('pages/spendr/merchants/transactions/index.vue')
  },
  {
    path: 'spendr/loads',
    component: () => import('pages/spendr/merchants/loads/index.vue')
  },
  {
    path: 'spendr/balance-history',
    component: () => import('pages/spendr/merchants/balance-history/index.vue')
  },
  {
    path: 'spendr/ach-files',
    component: () => import('pages/spendr/AchTransactions/index.vue')
  },
  {
    path: 'spendr/ach-returns',
    component: () => import('pages/spendr/AchReturns/index.vue')
  },
  {
    path: 'spendr/ach-file/:id',
    component: () => import('pages/spendr/AchTransactions/transactions.vue')
  },
  {
    path: 'spendr/load-report',
    component: () => import('pages/spendr/loadReport/index.vue')
  },
  {
    path: 'spendr/transaction-report',
    component: () => import('pages/spendr/transactionReport/index.vue')
  },
  {
    path: 'spendr/running-balance-report',
    component: () => import('pages/spendr/runningBalanceReport/index.vue')
  },
  {
    path: 'spendr/fee-report/tern',
    component: () => import('pages/spendr/ternFeeReport/index.vue')
  },
  {
    path: 'spendr/fee-report/bank',
    component: () => import('pages/spendr/bankFeeReport/index.vue')
  },
  {
    path: 'spendr/fee-report/estimated-bank',
    component: () => import('pages/spendr/estimatedBankFeeReport/index.vue')
  },
  {
    path: 'spendr/rewards',
    component: () => import('pages/spendr/rewards/index.vue')
  },
  {
    path: 'spendr/tipping',
    component: () => import('pages/spendr/tipping/index.vue')
  },
  {
    path: 'spendr/promotion-report',
    component: () => import('pages/spendr/promotionReport/index.vue')
  },
  {
    path: 'spendr/earn-report',
    component: () => import('pages/spendr/promotionReport/earn.vue')
  },
  {
    path: 'spendr/rewards-report',
    component: () => import('pages/spendr/rewardsReport/index.vue')
  },
  {
    path: 'spendr/referral-report',
    component: () => import('pages/spendr/referralReport/index.vue')
  },
  {
    path: 'spendr/tip-report',
    component: () => import('pages/spendr/tipping/report.vue')
  },
  {
    path: 'spendr/recon-report/activity',
    component: () => import('pages/spendr/reconReport/activity/index.vue')
  },
  {
    path: 'spendr/recon-report/ledger',
    component: () => import('pages/spendr/reconReport/ledger/index.vue')
  },
  {
    path: 'spendr/plaid-setting',
    component: () => import('pages/spendr/setting/plaid.vue')
  },
  {
    path: 'spendr/fuzzy-match-setting',
    component: () => import('pages/spendr/setting/fuzzyMatch.vue')
  },
  {
    path: 'spendr/app-version-setting',
    component: () => import('pages/spendr/setting/appVersion.vue')
  },
  {
    path: 'spendr/rewards-setting',
    component: () => import('pages/spendr/setting/rewards.vue')
  },
  {
    path: 'spendr/referral-setting',
    component: () => import('pages/spendr/setting/referral.vue')
  },
  {
    path: 'spendr/restrict',
    component: () => import('pages/spendr/setting/restrict.vue')
  },
  {
    path: 'spendr/username-blacklist',
    component: () => import('pages/spendr/setting/usernameBlacklist.vue')
  },
  {
    path: 'spendr/bankcard-blacklist',
    component: () => import('pages/spendr/setting/bankCardBlacklist.vue')
  },
  {
    path: 'spendr/recon-report/activity/pending-balance',
    component: () => import('pages/spendr/reconReport/activity/report/pendingBalance.vue')
  },
  {
    path: 'spendr/kyc_report',
    component: () => import('pages/spendr/kyc_exceptions/index.vue')
  },
  {
    path: 'spendr/groups',
    component: () => import('pages/spendr/group/index.vue')
  }
]
