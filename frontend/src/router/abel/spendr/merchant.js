export default [
  {
    path: 'spendr/merchant/admins',
    component: () => import('pages/spendr/merchants/admins/index.vue')
  },
  {
    path: 'spendr/merchant/onboard',
    component: () => import('pages/spendr/merchants/onboard/index.vue')
  },
  {
    path: 'spendr/consumers',
    component: () => import('pages/spendr/consumers/index.vue')
  },
  {
    path: 'spendr/consumers/:id',
    component: () => import('pages/spendr/consumers/profile')
  },
  {
    path: 'spendr/locations',
    component: () => import('pages/spendr/locations/index.vue')
  },
  {
    path: 'spendr/employees',
    component: () => import('pages/spendr/employees/index.vue')
  },
  {
    path: 'spendr/merchant/transactions',
    component: () => import('pages/spendr/merchants/transactions/index.vue')
  },
  {
    path: 'spendr/merchant/loads',
    component: () => import('pages/spendr/merchants/loads/index.vue')
  },
  {
    path: 'spendr/merchant/tipping',
    component: () => import('pages/spendr/tipping/index.vue')
  },
  {
    path: 'spendr/merchant/tip-report',
    component: () => import('pages/spendr/tipping/report.vue')
  },
  {
    path: 'spendr/merchant/balance-history',
    component: () => import('pages/spendr/merchants/balance-history/index.vue')
  },
  {
    path: 'spendr/merchant/setting',
    component: () => import('pages/spendr/merchants/setting/index.vue')
  },
  {
    path: 'spendr/merchant/setting/auto-withdrawal',
    component: () => import('pages/spendr/merchants/setting/autoWithdrawal.vue')
  }
]
