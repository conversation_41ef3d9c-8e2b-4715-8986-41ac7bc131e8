<template>
  <q-btn flat :label="user.currentRole"
         v-if="roles && roles.length > 1"
         no-caps
         no-wrap
         size="xs"
         anchor="bottom right"
         self="top right"
         icon-right="mdi-chevron-down"
         class="role-menu-btn">
    <q-popover>
      <q-list separator link>
        <q-list-header>Switch to role:</q-list-header>
        <q-item v-for="r in roles" :key="r"
                @click.native="switchTo(r)">
          <q-item-side :icon="r === user.currentRole ? 'mdi-check' : 'mdi-blank'"></q-item-side>
          <q-item-main :label="r"></q-item-main>
        </q-item>
      </q-list>
    </q-popover>
  </q-btn>
</template>

<script>
import _ from 'lodash'

export default {
  name: 'AdminLayoutRoleMenu',
  computed: {
    user () {
      return this.$store.state.User
    },
    roles () {
      return _.filter(this.$store.state.User.teams, r => r !== 'Consumer')
    }
  },
  methods: {
    async switchTo (role) {
      this.$q.loading.show()
      window.location.href = `/admin/user/switch-role?role=${role}`
    }
  }
}
</script>

<style lang="scss">
  .role-menu-btn {
    padding: 0 0 0 6px !important;
    margin-left: 10px;
    min-height: 18px;
    margin-bottom: 0;
    font-size: 12px !important;
    color: #333;

    .q-icon.on-right {
      padding-top: 3px;
    }
  }
</style>
