<template>
  <q-btn flat round class="ml-8 p-0">
    <div class="avatar" :style="{backgroundImage: `url(${user.avatar || '/static/img/avatar2.png'})`}"></div>
    <q-popover class="user-menu-popover">
      <q-list separator link>
        <q-item v-if="user.impersonating" @click.native="c.redirectRoot('/login_as/exit?url=/admin')">
          <q-item-side icon="mdi-arrow-left"></q-item-side>
          <q-item-main>Login back</q-item-main>
        </q-item>
        <q-item v-close-overlay @click.native="c.redirectRoot('/dashboard')" v-if="user.consumer">
          <q-item-side icon="mdi-cart"></q-item-side>
          <q-item-main>Consumer portal</q-item-main>
        </q-item>
        <q-item v-close-overlay :to="'/a'" v-if="c.clf.isAdminRoute($route)">
          <q-item-side icon="mdi-account-tie"></q-item-side>
          <q-item-main>Super admin portal</q-item-main>
        </q-item>
        <q-item v-close-overlay :to="'/a/clf/sa'" v-if="c.clf.isSaRoute($route)">
          <q-item-side icon="mdi-buddhism"></q-item-side>
          <q-item-main>UTC admin portal</q-item-main>
        </q-item>
        <q-item v-close-overlay
                v-if="user.switchedCp"
                @click.native="c.redirectRoot('/admin/card-program/switch/back')">
          <q-item-side icon="mdi-sitemap"></q-item-side>
          <q-item-main>Switch to main system</q-item-main>
        </q-item>
        <q-item v-close-overlay :to="'/a/i/admin__user_modify__profile'">
          <q-item-side icon="mdi-account"></q-item-side>
          <q-item-main>Profile</q-item-main>
        </q-item>
        <q-item v-close-overlay @click.native="logout">
          <q-item-side icon="mdi-logout-variant"></q-item-side>
          <q-item-main>Logout</q-item-main>
        </q-item>
      </q-list>
    </q-popover>
  </q-btn>
</template>

<script>
export default {
  name: 'AdminLayoutUserMenu',
  computed: {
    user () {
      return this.$store.state.User
    }
  },
  methods: {
    logout () {
      window.location.href = '/logout'
    }
  }
}
</script>

<style lang="scss">
  .user-menu-popover {
    max-width: 250px !important;
  }
</style>
