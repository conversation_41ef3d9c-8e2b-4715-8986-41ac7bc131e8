<template>
  <q-layout id="admin"
            class="admin_layout"
            view="lHh Lpr lFf"
            :class="layoutClass">
    <q-layout-drawer side="left"
                     v-model="left"
                     :width="240">
      <q-scroll-area class="fit">
        <q-list class="main-list">
          <div class="drawer-header">
            <img class="logo"
                 src="../statics/logo.png"
                 alt="">
          </div>
          <template v-for="m in menus">
            <q-item v-if="m.children.length <= 0"
                    :key="m.name"
                    v-ripple
                    :class="m.cls"
                    @click.native="ensureNav(m.route, m.mdRoute)"
                    :to="convertRoute(m.route, m.mdRoute)">
              <q-item-side>
                <q-icon :name="m.mdIcon"
                        class="q-item-icon"
                        v-if="m.mdIcon.startsWith('mdi-')"></q-icon>
                <i class="q-item-icon"
                   :class="m.mdIcon"
                   v-else-if="m.mdIcon.startsWith('flaticon-')"></i>
              </q-item-side>
              <q-item-main>
                <a :href="'/admin#' + convertRoute(m.route, m.mdRoute)">{{ m.name }}</a>
              </q-item-main>
              <q-item-side right
                           class="open-in-new"
                           v-if="m.route && m.route !== '/noop' && m.mdRoute">
                <a :href="'/admin#' + convertRoute(m.route)"
                   target="_blank"
                   class="btn-wrapper">
                  <q-icon name="mdi-open-in-new"
                          color="white"></q-icon>
                </a>
              </q-item-side>
            </q-item>
            <q-item v-else
                    v-ripple
                    :class="{
                      'router-link-active': collapsible[m.id],
                      'hover': hovers[m.id]
                    }"
                    @mouseenter.native="showSubNav(m)"
                    :key="m.name">
              <q-item-side>
                <q-icon :name="m.mdIcon"
                        class="q-item-icon"
                        v-if="m.mdIcon.startsWith('mdi-')"></q-icon>
                <i class="q-item-icon"
                   :class="m.mdIcon"
                   v-else-if="m.mdIcon.startsWith('flaticon-')"></i>
              </q-item-side>
              <q-item-main>{{ m.name }}</q-item-main>
              <q-item-side icon="mdi-chevron-right"></q-item-side>

              <q-popover anchor="top right"
                         self="top left"
                         :ref="`subNav_${m.id}`"
                         :offset="[0, 8]"
                         @hide="subNavHide(m)"
                         class="sidebar-popover"
                         :class="{wide: m.children.length > 4}">
                <q-list>
                  <q-item v-for="c in m.children"
                          :key="c.name"
                          v-ripple
                          @click.native="ensureNav(c.route, c.mdRoute)"
                          :class="{active: isSameRoute($route.fullPath, convertRoute(c.route, c.mdRoute))}"
                          :to="convertRoute(c.route, c.mdRoute)">
                    <q-item-side>
                      <q-icon name="mdi-circle-outline"></q-icon>
                      <q-icon name="mdi-circle"></q-icon>
                    </q-item-side>
                    <q-item-main>
                      <a :href="'/admin#' + convertRoute(c.route, c.mdRoute)">{{ c.name }}</a>
                    </q-item-main>
                    <q-item-side right
                                 class="open-in-new"
                                 v-if="c.route && m.route !== '/noop' && c.mdRoute && isSuperAdmin(user)">
                      <a :href="'/admin#' + convertRoute(c.route)"
                         onclick="event.stopPropagation();"
                         target="_blank"
                         class="btn-wrapper">
                        <q-icon name="mdi-open-in-new"
                                color="white"></q-icon>
                      </a>
                    </q-item-side>
                  </q-item>
                </q-list>
              </q-popover>
            </q-item>
          </template>
        </q-list>
      </q-scroll-area>
    </q-layout-drawer>
    <q-layout-header v-if="user && user.id">
      <q-toolbar class="admin-top-toolbar">
        <q-btn flat
               round
               icon="mdi-menu"
               color="primary"
               class="btn-top ml-5 font-18"
               @click="toggleDrawer"></q-btn>

        <q-btn flat
               round
               color="primary"
               class="btn-top ml-5"
               @click="$router.go(-1)"
               icon="mdi-chevron-left"></q-btn>

        <q-search icon="search"
                  v-model="keyword"
                  type="text"
                  v-if="hasQuickSearch"
                  class="m-10"
                  id="global-quick-search"
                  @keydown.13="search"
                  @clear="search"
                  clearable
                  placeholder="Quick Search">
          <q-tooltip anchor="bottom left"
                     self="top left">{{ quickSearchTooltip }}</q-tooltip>
        </q-search>

        <q-toolbar-title>
        </q-toolbar-title>
        <div class="column text-right">
          <div class="text-weight-bolder">
            {{ user.fullName }}
          </div>
          <div>
            <role-menu></role-menu>
          </div>
          <div>{{ user.email }}</div>
        </div>
        <user-menu></user-menu>
      </q-toolbar>
    </q-layout-header>
    <q-page-container v-if="user && user.id">
      <transition enter-active-class="animated fadeIn"
                  leave-active-class="animated fadeOut"
                  mode="out-in">
        <router-view />
      </transition>

      <SessionDetectDialog></SessionDetectDialog>
    </q-page-container>
    <q-layout-footer>
      <div class="bottom-power">
        <a href="https://www.ternitup.com"
           target="_blank">
          Powered by
          <img src="/static/img/tern_logo_new.svg"
               alt="Tern - FinTech Banking and Payment Leaders" />
        </a>
      </div>
    </q-layout-footer>
  </q-layout>
</template>

<script>
import 'fontawesome'
import eventBus from '../eventBus'
import UserMenu from './admin/user-menu'
import RoleMenu from './admin/role-menu'
import SessionDetectDialog from '../components/SessionDetectDialog'
import _ from 'lodash'
import $ from 'jquery'
import { request, isSuperAdmin, EventHandlerMixin, clf } from '../common'

export default {
  name: 'AdminLayout',
  mixins: [
    EventHandlerMixin('admin-hide-all-nav', 'hideAllNav')
  ],
  components: {
    UserMenu,
    RoleMenu,
    SessionDetectDialog
  },
  data () {
    return {
      left: true,
      keyword: '',
      collapsible: {},
      hovers: {}
    }
  },
  computed: {
    user () {
      return this.$store.state.User
    },
    menus () {
      let all = this.user.menus || []
      if (all.length <= 0) {
        all = [
          {
            children: [],
            icon: 'fa fa-fw fa-user',
            id: 'profile',
            mdIcon: 'mdi-account',
            name: 'Profile',
            route: '/admin/user_modify/profile'
          },
          {
            children: [],
            icon: 'fa fa-fw fa-sign-out',
            id: 'logout',
            mdIcon: 'mdi-logout',
            name: 'Logout',
            route: '/admin/logout'
          }
        ]
      }
      if (this.user.menus.length && clf.isAdminRoute(this.$route)) {
        all = [
          {
            children: [],
            icon: 'fa fa-fw fa-user',
            id: 'clf_dashboard',
            mdIcon: 'mdi-view-dashboard',
            name: 'Dashboard',
            route: '',
            mdRoute: 'clf/sa/dashboard'
          },
          {
            children: [],
            icon: 'fa fa-fw fa-user',
            id: 'clf_banks',
            mdIcon: 'mdi-bank',
            name: 'Banks',
            route: '',
            mdRoute: 'clf/sa/banks'
          },
          {
            children: [],
            icon: 'fa fa-fw fa-user',
            id: 'clf_dispensaries',
            mdIcon: 'mdi-store',
            name: 'Dispensaries',
            route: '',
            mdRoute: 'clf/sa/merchants/dispensary'
          },
          {
            children: [],
            icon: 'fa fa-fw fa-user',
            id: 'clf_vendors',
            mdIcon: 'mdi-gate',
            name: 'Vendors',
            route: '',
            mdRoute: 'clf/sa/merchants/vendor'
          },
          {
            children: [],
            icon: 'fa fa-fw fa-user',
            id: 'clf_patients',
            mdIcon: 'mdi-account-search',
            name: 'Patients',
            route: '',
            mdRoute: 'clf/sa/patients'
          },
          {
            children: [],
            icon: 'fa fa-fw fa-user',
            id: 'clf_transactions',
            mdIcon: 'mdi-format-list-bulleted-type',
            name: 'Transactions',
            route: '',
            mdRoute: 'clf/sa/report/transactions'
          },
          {
            children: [],
            icon: 'fa fa-fw fa-user',
            id: 'clf_fees',
            mdIcon: 'mdi-file-document-edit-outline',
            name: 'Taxes & Fees',
            route: '',
            mdRoute: 'clf/sa/report/fees'
          },
          {
            children: [
              {
                id: 'clf_testing_loads',
                name: 'Pending Loads / Unloads',
                route: '',
                mdRoute: 'clf/sa/testing/pending-loads'
              },
              {
                id: 'clf_testing_transactions',
                name: 'In-Store Purchase/Refund Transactions',
                route: '',
                mdRoute: 'clf/sa/testing/make-transaction'
              },
              {
                id: 'clf_testing_reset_balance',
                name: 'Reset Account Balance and Activity',
                route: '',
                mdRoute: 'clf/sa/testing/reset-balance'
              }
            ],
            icon: '',
            id: 'clf_testing',
            mdIcon: 'mdi-toolbox',
            name: 'Testing Tools',
            route: ''
          },
          {
            children: [],
            icon: 'fa fa-fw fa-user',
            id: 'clf_role',
            mdIcon: 'mdi-account-multiple-outline',
            name: 'Roles',
            route: '',
            mdRoute: 'clf/sa/roles/admin__role?prefix=UTC%20'
          },
          {
            children: [],
            icon: 'fa fa-fw fa-user',
            id: 'clf_setting',
            mdIcon: 'mdi-settings',
            name: 'Settings',
            route: '',
            mdRoute: 'clf/sa/settings'
          },
          {
            children: [],
            icon: 'fa fa-fw fa-user',
            id: 'clf_sa',
            mdIcon: 'mdi-account-tie',
            name: 'VirtualCards Admin Portal',
            route: '',
            mdRoute: 'dummy',
            cls: 'menu-upper-line'
          }
        ]
      }

      if (clf.isSaRoute(this.$route)) {
        if (!_.find(all, { id: 'original_sa' })) {
          all = _.cloneDeep(all)
          all.push({
            children: [],
            icon: 'fa fa-fw fa-user',
            id: 'original_sa',
            mdIcon: 'mdi-buddhism',
            name: 'UTC Admin Portal',
            route: '',
            mdRoute: 'clf/sa',
            cls: 'menu-upper-line'
          })
        }
      }

      return all
    },
    title () {
      return this.$route.meta.title || 'Load Card'
    },
    layoutClass () {
      const all = []
      if (this.user && this.user.cpKey) {
        all.push(this.user.cpKey)
      }
      if (this.left) {
        all.push('drawer-opened')
      }
      return all
    },
    hasQuickSearch () {
      if (this.$route.meta.search) {
        return true
      }
      if (this.user.cpKey === 'cp_fis') {
        return false
      }
      if (this.user.cpKey === 'cp_clf') {
        if (clf.isAdminRoute(this.$route)) {
          return false
        }
      }
      return this.user.permissions.includes('user')
    },
    quickSearchTooltip () {
      if (this.$route.meta.search) {
        return this.$route.meta.search.tip
      }
      return 'User ID, Email, First name, Last name, Full name'
    }
  },
  watch: {
    menus () {
      this.updateActiveMenu()
    },
    left () {
      this.$store.commit('Config/update', {
        sidebar: this.left
      })
      setTimeout(() => {
        $(window).resize()
      }, 300)
    },
    '$route': {
      deep: true,
      handler () {
        this.updateActiveMenu()
      }
    }
  },
  methods: {
    isSameRoute (a, b) {
      let p = a.indexOf('?')
      if (p >= 0) {
        a = a.substring(0, p)
      }
      p = b.indexOf('?')
      if (p >= 0) {
        b = b.substring(0, p)
      }
      return a === b
    },
    updateActiveMenu () {
      const route = this.$route.fullPath
      let found = false
      for (let m of this.menus) {
        this.$set(this.collapsible, m.id, false)
        if (!found) {
          for (let c of m.children) {
            const r = this.convertRoute(c.route, c.mdRoute)
            if (this.isSameRoute(r, route)) {
              this.$set(this.collapsible, m.id, true)
              found = true
              break
            }
          }
        }
      }
    },
    showSubNav (menu) {
      for (let m of this.menus) {
        this.$set(this.hovers, m.id, false)
        const ref = this.$refs[`subNav_${m.id}`]
        if (ref && ref[0]) {
          ref[0].hide().catch(() => {})
        }
      }
      this.$set(this.hovers, menu.id, true)
      const ref = this.$refs[`subNav_${menu.id}`]
      if (ref && ref[0]) {
        ref[0].show().catch(() => {})
      }
    },
    subNavHide (menu) {
      this.$set(this.hovers, menu.id, false)
    },
    hideAllNav () {
      _.forEach(this.hovers, (h, k) => {
        this.$set(this.hovers, k, false)

        const ref = this.$refs[`subNav_${k}`]
        if (ref && ref[0]) {
          ref[0].hide().catch(() => {})
        }
      })
    },
    toggleDrawer () {
      this.left = !this.left
    },
    convertRoute (r, newRoute) {
      if (newRoute) {
        if (newRoute === true) {
          return '/a'
        }
        return `/a/${newRoute}`
      }

      if (!r) {
        return null
      }
      r = r.toLowerCase()
      if (r.startsWith(window.location.origin)) {
        r = r.replace(window.location.origin, '')
      }
      if (r.startsWith('http://') || r.startsWith('https://')) {
        window.location.href = r
        return r
      }
      if (r.startsWith('/')) {
        r = r.substr(1)
      }
      const p = r.indexOf('#/')
      if (p >= 0) {
        return r.substr(p + 1)
      }
      return `/a/i/${r.replace(/\//g, '__')}`
    },
    ensureNav (r, newRoute) {
      const route = this.convertRoute(r, newRoute)
      if (this.isSameRoute(this.$route.fullPath, route)) {
        this.$root.$emit('frame-navigate-reload')
      }
    },
    search () {
      this.$nextTick(() => {
        setTimeout(() => {
          const route = this.$route.path
          if (this.$route.meta.search) {
            this.$root.$emit(this.$route.meta.search.event, this.keyword)
            return
          }

          if (route === '/a/user-management/user') {
            this.$root.$emit('admin_user_quick_search', this.keyword)
          } else {
            localStorage.setItem('quickSearch', this.keyword)
            this.$router.push(`/a/user-management/user`)

            // setTimeout(() => {
            //   this.$root.$emit('admin_user_quick_search', this.keyword)
            // }, 1500)
          }
        }, 300)
      })
    },
    goDefaultUrl () {
      if (this.$route.fullPath === '/a' && this.user.defaultUrl) {
        if (this.user.defaultUrl === '/admin') {
          let m = _.first(this.menus)
          if (m.hasOwnProperty('children')) {
            let children = m.children
            if (children.length > 0) {
              m = _.first(children)
            }
          }
          this.$router.replace(this.convertRoute(m.route, m.mdRoute))
        } else {
          this.$router.replace(this.convertRoute(this.user.defaultUrl))
        }
      }
    },
    async storageHandler () {
      const resp = await request('/admin/user/profile')
      if (resp.success) {
        this.$store.commit('User/update', resp.data)
        this.$forceUpdate()
        localStorage.setItem('updatePic', false)
      }
    },
    isSuperAdmin (user) {
      return isSuperAdmin(user)
    }
  },
  mounted () {
    if (window.adminLayoutMountedCb) {
      window.adminLayoutMountedCb()
    }

    this.left = this.$store.state.Config.sidebar

    if (this.$route.path === '/a/i/admin__user_index' && this.$route.query) {
      this.keyword = this.$route.query.keyword
    }

    for (let m of this.menus) {
      this.$set(this.collapsible, m.id, false)
      this.$set(this.hovers, m.id, false)
    }
    this.updateActiveMenu()

    eventBus.$on('loaded-user-profile', this.goDefaultUrl)

    window.document.addEventListener('scroll', () => {
      const header = window.document.querySelector('.q-layout-header')
      if (window.document.documentElement.scrollTop > 10) {
        header.classList.add('shadow')
      } else {
        header.classList.remove('shadow')
      }
    })

    window.addEventListener('storage', function () {
      if (localStorage.getItem('updatePic') === 'true') {
        setTimeout(() => {
          this.storageHandler()
        }, 2000)
      }
    }.bind(this), false)
  },
  beforeDestroy () {
    eventBus.$off('loaded-user-profile', this.goDefaultUrl)
  }
}
</script>

<style lang="scss">
@import "../css/font/Lato.css";
@import "../css/font/Flaticon/Flaticon.css";
@import "../css/admin_common";
@import "../css/admin";

.admin-top-toolbar {
  .btn-top {
    font-size: 20px;
    width: 40px;
    height: 40px;
  }
}

.clf_page,
.clf-dialog {
  .replaced-icon.mdi-alert-outline {
    @extend %q-svg-icon-size;
    background-image: url("../statics/mobile/clf/tran-pending-load.svg");
  }

  .replaced-icon.mdi-arrow-right-circle-outline {
    @extend %q-svg-icon-size;
    background-image: url("../statics/mobile/clf/tran-credit.svg");
  }

  .replaced-icon.mdi-arrow-left-circle-outline {
    @extend %q-svg-icon-size;
    background-image: url("../statics/mobile/clf/tran-debit.svg");
  }
}
</style>
