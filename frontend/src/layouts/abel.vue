<template>
  <q-layout id="abel_admin"
            class="admin_layout"
            view="lHh Lpr lFf"
            :class="layoutClass">
    <q-layout-drawer side="left"
                     v-model="left"
                     :width="240">
      <q-scroll-area class="fit">
        <q-list class="main-list"
                :class="menuClass()"
                no-border>
          <div v-if="this.user.cpKey === 'cp_mex' "
               class="mex-drawer-header drawer-header row">
            <div class="col flex flex-center">
              <img class="logo"
                   src="/static/mex/img/logo_header_white2.png"
                   alt="">
              <img class="mex-logo"
                   src="/static/mex/img/logo_header_white2.png"
                   alt="">
            </div>
          </div>
          <div v-else-if="this.user.cpKey === 'cp_faas' "
               class="mex-drawer-header drawer-header row">
            <div class="col flex flex-center">
              <img class="mex-logo"
                   v-if="this.user.clientLogo"
                   :src="this.user.clientLogo"
                   alt="">
              <img class="logo"
                   v-else
                   src="/static/faas/img/logo_header_white.png"
                   alt="">
            </div>
          </div>
          <div v-else
               class="drawer-header row">
            <div class="col flex flex-center">
              <img class="logo"
                   src="../statics/logo.png"
                   alt="">
            </div>
          </div>
          <template v-for="m in menus">
            <q-item v-if="m.children.length <= 0"
                    :key="m.name"
                    v-ripple
                    :class="m.cls"
                    @click.native="ensureNav(m.route, m.mdRoute)"
                    :to="convertRoute(m.route, m.mdRoute)">
              <q-item-side v-if="m.svgIcon === false">
                <q-icon :name="m.mdIcon"
                        class="q-item-icon"
                        v-if="m.mdIcon.startsWith('mdi-')"></q-icon>
                <i class="q-item-icon"
                   :class="m.mdIcon"
                   v-else-if="m.mdIcon.startsWith('flaticon-')"></i>
              </q-item-side>
              <q-item-side v-else
                           class="flex flex-center">
                <img class="menu-icon"
                     :src="`/static/abel/icons/${m.id}.svg`"
                     alt="">
              </q-item-side>
              <q-item-main>
                <a :href="'/admin#' + convertRoute(m.route, m.mdRoute)">{{ m.name }}</a>
              </q-item-main>
              <q-item-side right
                           class="open-in-new"
                           v-if="m.route && m.route !== '/noop' && m.mdRoute">
                <a :href="'/admin#' + convertRoute(m.route)"
                   target="_blank"
                   class="btn-wrapper">
                  <q-icon name="mdi-open-in-new"
                          color="white"></q-icon>
                </a>
              </q-item-side>
            </q-item>
            <q-item v-else
                    v-ripple
                    :class="{
                      'router-link-active': collapsible[m.id],
                      'hover': hovers[m.id]
                    }"
                    @mouseenter.native="showSubNav(m)"
                    :key="m.name">
              <q-item-side v-if="m.id == 'cow_members' || m.id == 'cow_settings' || m.id == 'cow_reports' || m.id == 'developer'"
                           class="flex flex-center">
                <img class="menu-icon"
                     :src="`/static/abel/icons/${m.id}.svg`"
                     alt="">
              </q-item-side>
              <q-item-side v-else-if="m.svgIcon"
                           class="flex flex-center">
                <img class="menu-icon"
                     :src="`/static/abel/icons/${m.svgIcon}.svg`"
                     alt="">
              </q-item-side>
              <q-item-side v-else>
                <q-icon :name="m.mdIcon"
                        class="q-item-icon"
                        v-if="m.mdIcon.startsWith('mdi-')"></q-icon>
                <i class="q-item-icon"
                   :class="m.mdIcon"
                   v-else-if="m.mdIcon.startsWith('flaticon-')"></i>
              </q-item-side>
              <q-item-main notranslate="">{{ m.name }}</q-item-main>
              <q-item-side icon="mdi-chevron-right"></q-item-side>

              <q-popover anchor="top right"
                         self="top left"
                         :ref="`subNav_${m.id}`"
                         :offset="[0, 8]"
                         @hide="subNavHide(m)"
                         class="sidebar-popover"
                         :class="{wide: m.children.length > 4}">
                <q-list>
                  <q-item v-for="c in m.children"
                          :key="c.name"
                          v-ripple
                          @click.native="ensureNav(c.route, c.mdRoute)"
                          :class="{active: isSameRoute($route.fullPath, convertRoute(c.route, c.mdRoute))}"
                          :to="convertRoute(c.route, c.mdRoute)">
                    <q-item-side>
                      <q-icon name="mdi-circle-outline"></q-icon>
                      <q-icon name="mdi-circle"></q-icon>
                    </q-item-side>
                    <q-item-main>
                      <a :href="'/admin#' + convertRoute(c.route, c.mdRoute)">{{ c.name }}</a>
                    </q-item-main>
                    <q-item-side right
                                 class="open-in-new"
                                 v-if="c.route && m.route !== '/noop' && c.mdRoute && isSuperAdmin(user)">
                      <a :href="'/admin#' + convertRoute(c.route)"
                         onclick="event.stopPropagation();"
                         target="_blank"
                         class="btn-wrapper">
                        <q-icon name="mdi-open-in-new"
                                color="white"></q-icon>
                      </a>
                    </q-item-side>
                  </q-item>
                </q-list>
              </q-popover>
            </q-item>
          </template>
        </q-list>
      </q-scroll-area>
    </q-layout-drawer>
    <q-layout-header v-if="user && user.id">
      <q-toolbar class="admin-top-toolbar"
                 :class="this.user.cpKey === 'cp_mex' && this.user.teams.indexOf('TransferMex Employer') !== -1 ? 'employer-header-margin' : ''"
                 color="white"
                 text-color="dark">
        <q-btn flat
               round
               icon="mdi-menu"
               color="primary"
               class="btn-top font-16 ml-5"
               @click="toggleDrawer"></q-btn>

        <!-- <q-btn flat
               round
               color="primary"
               class="btn-top font-16 ml-5"
               @click="$router.go(-1)"
               icon="mdi-chevron-left"></q-btn> -->

        <div class="account-box">
          <q-btn flat
                 round
                 class="p-0">
            <div class="avatar"
                 :style="{backgroundImage: `url(${user.avatar || '/static/img/avatar2.png'})`}"></div>
          </q-btn>
          <div class="column ml-7 mr-10 font-24">
            <div class="text-weight-bold name-desc"
                 notranslate="">
              {{ user.fullName }}
            </div>
            <h5 v-if="user.group"
                class="group-name">
              {{ user.group.name }}
            </h5>
            <div class="role-desc font-12">{{ user.currentRole }}</div>
          </div>
          <i class="q-icon mdi mdi-menu-down font-24 text-grey-7"></i>

          <q-popover>
            <q-list separator
                    link
                    class="user-menu-popover">
              <q-list-header>{{ user.email }}</q-list-header>
              <q-item v-if="user.impersonating"
                      @click.native="c.redirectRoot('/login_as/exit?url=/admin')">
                <q-item-side icon="mdi-arrow-left"></q-item-side>
                <q-item-main>Login back</q-item-main>
              </q-item>
              <q-item v-close-overlay
                      v-if="user.switchedCp"
                      @click.native="c.redirectRoot('/admin/card-program/switch/back')">
                <q-item-side icon="mdi-sitemap"></q-item-side>
                <q-item-main>Switch to main system</q-item-main>
              </q-item>
              <!-- <q-item v-close-overlay
                      :to="'/h/i/admin__user_modify__profile'">
                <q-item-side icon="mdi-account"></q-item-side>
                <q-item-main>Profile</q-item-main>
              </q-item> -->
              <q-item v-close-overlay
                      @click.native="logout">
                <q-item-side icon="mdi-logout-variant"></q-item-side>
                <q-item-main>Logout</q-item-main>
              </q-item>
            </q-list>
          </q-popover>
        </div>
        <div v-if="(this.user.cpKey === 'cp_mex' && this.user.teams.indexOf('TransferMex Employer') !== -1)
        || (this.user.cpKey === 'cp_faas' && this.user.currentRole !== 'Faas Member')"
             class="ml-10 col-4 global-quick-search-item">
          <q-search icon="search"
                    v-model="keyword"
                    type="text"
                    class="m-10"
                    id="global-quick-search"
                    @keydown.13="search"
                    @clear="search"
                    clearable
                    hide-underline
                    placeholder="Search first name, last name, email, mobile phone & user ID...">
          </q-search>
        </div>

        <!-- <h5 v-if="user.group"
            class="group-name">
          {{ user.group.name }}
        </h5> -->
        <q-btn flat
               round
               icon="mdi-bell-outline"
               color="grey-7"
               class="ml-auto font-16 notice-btn">
          <q-popover>
            <q-list separator
                    link>
              <q-item v-close-overlay>No notifications</q-item>
            </q-list>
          </q-popover>
        </q-btn>
      </q-toolbar>
      <div class="mt-5"
           v-if="(this.user.cpKey === 'cp_mex' && this.user.subEmployers.length > 0)">
        <div class="switch-employer">
          <q-select v-model="currentEmployer"
                    class="dense"
                    :clearable="true"
                    stack-label="Switch to Sub Employer"
                    @input=" val => switchEmployer(val)"
                    :options="this.user.subEmployers"></q-select>
        </div>
      </div>
    </q-layout-header>
    <q-page-container v-if="user && user.id">
      <template v-if="user.cpKey === 'cp_spendr' && user.merchantStatus && !user.merchantApproved && !$route.fullPath.startsWith('/h/spendr/merchant/onboard')">
        <q-alert v-if="user.merchantStatus === 'Initial'"
                 icon="mdi-information-outline"
                 color="warning"
                 @click.native="$router.push('/h/spendr/merchant/onboard')"
                 class="mh-30 mv-15 pointer">
          <span class="font-14">Complete your profile and get approved to continue.</span>
          <q-icon name="mdi-arrow-right"
                  class="ml-10"></q-icon>
        </q-alert>
        <q-alert v-else-if="user.merchantStatus === 'Pending'"
                 icon="mdi-information-outline"
                 color="primary"
                 @click.native="$router.push('/h/spendr/merchant/onboard')"
                 class="mh-30 mv-15 pointer">
          <span class="font-14">Your application is in review and being processed.</span>
          <q-icon name="mdi-arrow-right"
                  class="ml-10"></q-icon>
        </q-alert>
        <q-alert v-else-if="user.merchantStatus === 'Denied'"
                 icon="mdi-information-outline"
                 color="negative"
                 @click.native="$router.push('/h/spendr/merchant/onboard')"
                 class="mh-30 mv-15 pointer">
          <span class="font-14">Your application is denied. View details and resubmit the application</span>
          <q-icon name="mdi-arrow-right"
                  class="ml-10"></q-icon>
        </q-alert>
      </template>
      <template v-if="user.cpKey === 'cp_mex' && user.payrollInfo && !$route.fullPath.startsWith('/h/mex/employer/reports/batchSummary')">
        <q-alert icon="mdi-information-outline"
                 colr="warning"
                 @click.native="$router.push('/h/mex/employer/reports/batchSummary')"
                 class="mh-30 mv-15 pointer">
          <span class="font-14">{{ user.payrollInfo}}</span>
          <q-icon name="mdi-arrow-right"
                  class="ml-10"></q-icon>
        </q-alert>
      </template>
      <transition enter-active-class="animated fadeIn"
                  leave-active-class="animated fadeOut"
                  mode="out-in">
        <router-view />
      </transition>

      <SessionDetectDialog></SessionDetectDialog>
    </q-page-container>
    <q-layout-footer v-if="!$route.fullPath.startsWith('/h/spendr/') && !window.location.host.includes('spendr')">
      <div class="bottom-power">
        <a href="https://www.ternitup.com"
           target="_blank">
          Powered by
          <img src="/static/img/tern_logo_new.svg"
               alt="Tern - FinTech Banking and Payment Leaders" />
        </a>
      </div>
    </q-layout-footer>
  </q-layout>
</template>

<script>
import 'fontawesome'
import AdminLayoutMixin from '../mixins/AdminLayoutMixin'
import { request } from '../common'
import { Loading } from 'quasar'

export default {
  name: 'JakeAdminLayout',
  mixins: [
    AdminLayoutMixin
  ],
  data () {
    return {
      root: '/h',
      currentEmployer: this.user ? this.user.currentEmployer : null
    }
  },
  methods: {
    async switchEmployer (val) {
      Loading.show()
      const resp = await request('/admin/user/set-current-employer', 'post', {
        'currentEmployer': val
      })
      if (resp.success) {
        window.location.href = '/'
        Loading.hide()
      }
      Loading.hide()
    },
    search () {
      this.$nextTick(() => {
        setTimeout(() => {
          const route = this.$route.path
          if (this.user && this.user.cpKey === 'cp_mex') {
            if (route === '/h/mex/employer/employee') {
              this.$root.$emit('reload-mex-employers-employee', this.keyword)
            } else {
              localStorage.setItem('quickSearch', this.keyword)
              this.$router.push(`/h/mex/employer/employee`)
            }
          } else if (this.user && this.user.cpKey === 'cp_faas') {
            if (this.user.currentRole === 'Faas Client') {
              if (route === '/h/faas/base/client/member') {
                this.$root.$emit('reload-mex-employers-employee', this.keyword)
              } else {
                localStorage.setItem('quickSearch', this.keyword)
                this.$router.push(`/h/faas/base/client/member`)
              }
            } else {
              if (route === '/h/faas/base/members') {
                this.$root.$emit('reload-faas-members', this.keyword)
              } else {
                localStorage.setItem('quickSearch', this.keyword)
                this.$router.push(`/h/faas/base/members`)
              }
            }
          }
        }, 300)
      })
    },
    logout () {
      window.location.href = '/logout'
    },
    menuClass () {
      let programMenuClass = ''
      if (this.user.cpKey === 'cp_mex' && this.user.teams.indexOf('TransferMex Employer') !== -1) {
        programMenuClass = 'employer-menu-list'
      }
      if (this.user.cpKey === 'cp_faas') {
        programMenuClass = 'america-voice-menu-list'
      }
      this.currentEmployer = this.user.currentEmployer ? this.user.currentEmployer : null
      return programMenuClass
    }
  }
}
</script>

<style lang="scss">
@import url("https://fonts.googleapis.com/css?family=Poppins:100,100i,300,300i,400,500,500i,600,700&display=swap");
@import "../css/admin_common";
@import "../css/admin/dashboard";
@import "../css/abel/fis";
@import "../css/abel/index";
@import "../css/abel/upload";
@import "../css/abel/signaturePad";
@import "../css/abel/table";
@import "../css/abel/faas";
@import "../css/abel/spendr";
@import "../css/abel/custom";
.switch-employer {
  width: 50%;
  margin-left: 30px;
}
</style>
