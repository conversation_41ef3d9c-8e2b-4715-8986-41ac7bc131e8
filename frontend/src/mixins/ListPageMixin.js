import PageMixin from './PageMixin'
import ColumnFilter from '../components/ColumnFilter'
import ManageFiltersDialog from '../components/ManageFiltersDialog'
import BatchExportDialog from '../components/BatchExportDialog'
import Pagination from '../components/Pagination'
import FilterChips from '../components/FilterChips'
import TopChart from '../components/TopChart'
import StickyHead from '../components/StickyHead'
import _ from 'lodash'
import { isEmpty, request, EventHandlerMixin } from '../common'

export default {
  mixins: [
    PageMixin,
    EventHandlerMixin('reload-list-filters', 'initFilters')
  ],
  components: {
    TopChart,
    ColumnFilter,
    ManageFiltersDialog,
    BatchExportDialog,
    Pagination,
    FilterChips,
    StickyHead
  },
  data () {
    return {
      keyword: null,
      data: [],
      quick: {},
      filtersUrl: '',
      downloadUrl: '',
      requestUrl: null,
      requestCb: null,
      timeField: '',
      cardProgramField: '',
      chartData: {},
      loading: false,
      hideAjaxBarWhenLoading: true,
      pagination: {
        rowsNumber: 0,
        rowsPerPage: 10,
        page: 1,
        sortBy: null,
        descending: false
      },
      columns: [],
      filtersDialog: false,
      filters: [],
      loadingFilters: false,
      autoLoad: false,
      autoInitFiltersDialog: true,
      autoReloadWhenUrlChanges: false,
      everLoaded: false
    }
  },
  computed: {
    visibleColumns () {
      return _.filter(this.columns, c => {
        return !c.hidden
      })
    },
    validFilters () {
      return this.filters.filter(f => this.isFilterValid(f))
    }
  },
  watch: {
    '$route.fullPath' () {
      if (this.autoReloadWhenUrlChanges) {
        this.reload()
      }
    }
  },
  methods: {
    isFilterValid (filter) {
      return filter.field && (!isEmpty(filter.value) || !isEmpty(filter.value_0) || !isEmpty(filter.value_1))
    },
    async reload (mode) {
      const params = {
        pagination: this.pagination
      }
      if (mode === 'force') {
        params._mode_ = 'force'
      }
      await this.request(params)
      this.everLoaded = true
    },

    beforeRequest ({ pagination } = {}) {
      if (!pagination) {
        return
      }
      const filtersDialog = this.$refs.filtersDialog
      if (filtersDialog) {
        if (!this.everLoaded) {
          const options = filtersDialog.savedOptions()
          if (options && options.page && options.page >= 1) {
            pagination.page = options.page
          }
        } else {
          filtersDialog.updateOptions(pagination)
        }
      }
    },

    async request ({ pagination, _mode_ }) {
      this.beforeRequest({ pagination })
      if (pagination.page <= 0) {
        return
      }
      if (this.requestUrl) {
        const params = this.mergeQuerySortParams(pagination)
        if (_mode_) {
          params._mode_ = _mode_
        }
        this.loading = true
        const resp = await request(`${this.requestUrl}/${pagination.page}/${pagination.rowsPerPage}`, 'get', params)
        this.loading = false
        if (resp.success) {
          this.pagination = pagination
          this.data = resp.data.data
          this.pagination.rowsNumber = resp.data.count
          this.quick = resp.data.quick || {}
          if (this.requestCb) {
            this.requestCb(resp.data, params, pagination)
          }
        }
      }
    },

    async initFilters () {
      if (!this.filtersUrl) {
        return
      }
      const filtersDialog = this.$refs.filtersDialog
      if (this.autoInitFiltersDialog && filtersDialog) {
        filtersDialog.init(this.filters)
      }

      this.loadingFilters = true
      const resp = await request(this.filtersUrl)
      this.loadingFilters = false
      if (resp.success) {
        _.forEach(this.filterOptions, option => {
          if (option.options && option.options.length <= 0 && option.source) {
            option.options = resp.data[option.source] || []
          }
        })

        if (this.postInitFilters) {
          this.postInitFilters(resp)
        }
      }
    },

    postInitFilters () { },

    fromDashboard () {
      if (!_.isEmpty(this.$route.query) && _.isArray(this.filters)) {
        let query = this.$route.query
        let cardProgram = query.cardProgram
        let start = query.start
        let end = query.end
        this.filters.splice(0)
        if (cardProgram && cardProgram !== 'null') {
          let filter = {}
          filter.field = this.cardProgramField
          filter.value = parseInt(cardProgram)
          filter.predicate = '='
          this.filters.push(filter)
        }
        if ((start && start !== 'null') || (end && end !== 'null')) {
          let filter = {}
          filter.field = this.timeField
          filter.predicate = '='
          filter.value_0 = start && start !== 'null' ? (new Date(start)).toISOString() : ''
          filter.value_1 = (end && end !== 'null') ? (new Date(end)).toISOString() : ''
          this.filters.push(filter)
        }
        if (query.cardNumberProxy) {
          let filter = {}
          filter.field = 'filter[a.cardNumberProxy]'
          filter.value = query.cardNumberProxy
          filter.predicate = '='
          this.filters.push(filter)
        }
        if (this.fromDashboardEx) {
          this.fromDashboardEx(query)
        }

        /**
         * None of this needs to happen
         *
        this.filtersDialog = true
        setTimeout(() => {
          this.filtersDialog = false
          this.reload()
        }, 200)
        */
      }
    },

    download (type = 'all') {
      if (typeof type !== 'string') {
        type = 'all'
      }
      const data = this.mergeQuerySortParams(this.pagination)
      data.query_type = type
      this.$refs.exportDialog.show(this.downloadUrl, data)
    },

    getQueryParams () {
      const data = {
        keyword: this.keyword
      }

      if (!_.isEmpty(this.$route.query)) {
        data.isFromDashboard = true
      }

      if (this.$refs && this.$refs.filtersDialog) {
        _.assignIn(data, this.$refs.filtersDialog.getFilters())
      }

      if (this.$refs && this.$refs.dateRangeFilter) {
        _.assignIn(data, this.$refs.dateRangeFilter.params())
      }

      if (this.$refs && this.$refs.stickyHead) {
        _.assignIn(data, this.$refs.stickyHead.params())
      }

      _.assignIn(data, this.getOtherQueryParams())

      return data
    },

    getOtherQueryParams () {
      return {}
    },

    mergeQuerySortParams (pagination, params = null) {
      if (!params) {
        params = this.getQueryParams()
      }
      if (!pagination.sortBy) {
        return params
      }
      params.orderBy = `${pagination.descending ? '-' : '+'}${pagination.sortBy}`
      return params
    },

    init () { },

    updateColumnsFromPreference () {
      const key = `${this.$el.id}_visible_columns`
      const items = this.$store.state.User.preference[key] || []
      if (items.length > 0) {
        this.columns.forEach(c => {
          if (items.indexOf(c.field) >= 0 || !c.field) {
            this.$set(c, 'hidden', false)
          } else {
            this.$set(c, 'hidden', true)
          }
        })
      }
    },

    search (keyword) {
      this.keyword = keyword
      this.reload()
    },

    delayedReload () {
      this.$nextTick(() => {
        setTimeout(() => {
          this.reload()
        }, 400)
      })
    }
  },
  mounted () {
    this.init()
    this.updateColumnsFromPreference()
    this.initFilters()
    this.fromDashboard()

    setTimeout(() => {
      if (this.autoLoad) {
        this.reload()
      }
    }, 200)
  }
}
