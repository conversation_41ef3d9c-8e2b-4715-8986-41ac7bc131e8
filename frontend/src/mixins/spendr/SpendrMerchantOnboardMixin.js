import SpendrPageMixin from './SpendrPageMixin'
export default {
  mixins: [
    SpendrPageMixin
  ],
  computed: {
    currentMerchant () {
      return this.merchant || this.entity
    },
    verifyReadOnlyRule () {
      if (this.$store.getters['User/masterAdmin']) {
        return [true, false]
      }
      if (
        this.isSpendrEmployee() ||
        this.isSpendrEmployeeRoleLogin() ||
        this.isMerchantManagerAdmin() ||
        this.isMerchantAccountantAdmin() ||
        this.isMerchantOtherAdmin()
      ) {
        return [true, true]
      }
      if (this.reviewing) {
        return [true, true]
      }
      if (!this.currentMerchant) {
        return [true, true]
      }
      return [false]
    },
    readonly () {
      let [valid, result] = this.verifyReadOnlyRule
      if (valid) return result
      const merchant = this.currentMerchant
      return [
        'Pending',
        'Approved'
      ].includes(merchant['Status']) || merchant['Approved']
    },
    bankReadonly () {
      let [valid, result] = this.verifyReadOnlyRule
      if (valid) return result
      const merchant = this.currentMerchant
      return merchant['Status'] === 'Pending'
    }
  }
}
