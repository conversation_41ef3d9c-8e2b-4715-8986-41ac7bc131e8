import MexPageMixin from '../mex/MexPageMixin'

export default {
  mixins: [
    MexPageMixin
  ],
  data () {
    return {
      datesWithDaily: [
        {
          label: 'Daily',
          value: 'today'
        },
        {
          label: 'Weekly',
          value: 'week'
        },
        {
          label: 'Monthly',
          value: 'month'
        },
        {
          label: 'Quarterly',
          value: 'quarter'
        },
        {
          label: 'Yearly',
          value: 'year'
        },
        {
          label: 'All',
          value: 'all'
        },
        {
          label: 'Custom Range',
          value: 'custom_range'
        }
      ]
    }
  },
  computed: {
    merchantBalanceAdmin () {
      const user = this.user
      return user && user.currentRole === 'Spendr Merchant Admin'
    },
    merchantAdmin () {
      const user = this.user
      return user && (
        user.currentRole === 'Spendr Merchant Admin' ||
        user.currentRole === 'Spendr Merchant Master Admin' ||
        user.currentRole === 'Spendr Merchant Operator Admin' ||
        user.currentRole === 'Spendr Merchant Manager Admin' ||
        user.currentRole === 'Spendr Merchant Accountant Admin' ||
        user.currentRole === 'Spendr Merchant Other Admin'
      )
    },
    spendrAdmin () {
      const user = this.user
      return user && (
        user.currentRole === 'Spendr Program Owner' ||
        user.currentRole === 'Spendr Customer Support' ||
        user.currentRole === 'Spendr Compliance' ||
        user.currentRole === 'Spendr Accountant'
      )
    },
    agentAdmin () {
      const user = this.user
      return user && user.currentRole === 'Spendr Program Owner'
    },
    bankAdmin () {
      const user = this.user
      return user && user.currentRole === 'Spendr Bank'
    },
    spendrEmployee () {
      const user = this.user
      return user && (
        user.currentRole === 'Spendr Customer Support' ||
        user.currentRole === 'Spendr Compliance' ||
        user.currentRole === 'Spendr Accountant'
      )
    },
    spendrCustomerSupport () {
      const user = this.user
      return user && user.currentRole === 'Spendr Customer Support'
    },
    spendrCompliance () {
      const user = this.user
      return user && user.currentRole === 'Spendr Compliance'
    },
    spendrAccountant () {
      const user = this.user
      return user && user.currentRole === 'Spendr Accountant'
    },
    spendrLogin () {
      const user = this.user
      return user && user.isSpendrAdminLogin
    },
    spendrCustomerSupportLogin () {
      const user = this.user
      return user && user.isSpendrEmployeeCSLogin
    },
    spendrComplianceLogin () {
      const user = this.user
      return user && user.isSpendrEmployeeComplianceLogin
    },
    spendrAccountantLogin () {
      const user = this.user
      return user && user.isSpendrEmployeeAccountantLogin
    },
    spendrEmployeeLogin () {
      const user = this.user
      return user && user.isSpendrEmployeeLogin
    },
    merchantMasterAdmin () {
      const user = this.user
      return user && user.currentRole === 'Spendr Merchant Master Admin'
    },
    merchantOperatorAdmin () {
      const user = this.user
      return user && user.currentRole === 'Spendr Merchant Operator Admin'
    },
    merchantManagerAdmin () {
      const user = this.user
      return user && user.currentRole === 'Spendr Merchant Manager Admin'
    },
    merchantAccountantAdmin () {
      const user = this.user
      return user && user.currentRole === 'Spendr Merchant Accountant Admin'
    },
    merchantOtherAdmin () {
      const user = this.user
      return user && user.currentRole === 'Spendr Merchant Other Admin'
    }
  },
  methods: {
    isMasterAdmin () {
      const user = this.$store.state.User
      return user && user.currentRole === 'MasterAdmin'
    },
    isMerchantBalanceAdmin () {
      const user = this.$store.state.User
      return user && user.currentRole === 'Spendr Merchant Admin'
    },
    isMerchantAdmin () {
      const user = this.$store.state.User
      return user && (
        user.currentRole === 'Spendr Merchant Admin' ||
        user.currentRole === 'Spendr Merchant Master Admin' ||
        user.currentRole === 'Spendr Merchant Operator Admin' ||
        user.currentRole === 'Spendr Merchant Manager Admin' ||
        user.currentRole === 'Spendr Merchant Accountant Admin' ||
        user.currentRole === 'Spendr Merchant Other Admin'
      )
    },
    isSpendrAdmin () {
      const user = this.$store.state.User
      return user && (
        user.currentRole === 'Spendr Program Owner' ||
        user.currentRole === 'Spendr Customer Support' ||
        user.currentRole === 'Spendr Compliance' ||
        user.currentRole === 'Spendr Accountant'
      )
    },
    isAgentAdmin () {
      const user = this.$store.state.User
      return user && user.currentRole === 'Spendr Program Owner'
    },
    isBankAdmin () {
      const user = this.$store.state.User
      return user && user.currentRole === 'Spendr Bank'
    },
    isSpendrEmployee () {
      const user = this.$store.state.User
      return user && (
        user.currentRole === 'Spendr Customer Support' ||
        user.currentRole === 'Spendr Compliance' ||
        user.currentRole === 'Spendr Accountant'
      )
    },
    isSpendrCustomerSupport () {
      const user = this.$store.state.User
      return user && user.currentRole === 'Spendr Customer Support'
    },
    isSpendrCompliance () {
      const user = this.$store.state.User
      return user && user.currentRole === 'Spendr Compliance'
    },
    isSpendrAccountant () {
      const user = this.$store.state.User
      return user && user.currentRole === 'Spendr Accountant'
    },
    isSpendrCustomerSupportLogin () {
      const user = this.$store.state.User
      return user && user.isSpendrEmployeeCSLogin
    },
    isSpendrComplianceLogin () {
      const user = this.$store.state.User
      return user && user.isSpendrEmployeeComplianceLogin
    },
    isSpendrAccountantLogin () {
      const user = this.$store.state.User
      return user && user.isSpendrEmployeeAccountantLogin
    },
    isSpendrEmployeeRoleLogin () {
      const user = this.$store.state.User
      return user && user.isSpendrEmployeeLogin
    },
    isMerchantMasterAdmin () {
      const user = this.$store.state.User
      return user && user.currentRole === 'Spendr Merchant Master Admin'
    },
    isMerchantOperatorAdmin () {
      const user = this.$store.state.User
      return user && user.currentRole === 'Spendr Merchant Operator Admin'
    },
    isMerchantManagerAdmin () {
      const user = this.$store.state.User
      return user && user.currentRole === 'Spendr Merchant Manager Admin'
    },
    isMerchantAccountantAdmin () {
      const user = this.$store.state.User
      return user && user.currentRole === 'Spendr Merchant Accountant Admin'
    },
    isMerchantOtherAdmin () {
      const user = this.$store.state.User
      return user && user.currentRole === 'Spendr Merchant Other Admin'
    },
    statusClass (status) {
      return {
        'Initial': 'dark',
        'Onboarded': 'blue',
        'Pending': 'blue',
        'On Hold': 'warning',
        'Active': 'positive',
        'Approved': 'positive',
        'Inactive': 'purple',
        'Denied': 'negative',
        'Archived': 'gray',
        'Closed': 'negative',
        'KYC Failed (OFAC)': 'orange',
        'KYC (Scan Pending)': 'purple',
        'KYC Failed (Scan)': 'pansy',
        'KYC Failed (OFAC & Scan)': 'magenta',
        'Yes': 'purple',
        'No': 'blue'
      }[status] || status
    }
  }
}
