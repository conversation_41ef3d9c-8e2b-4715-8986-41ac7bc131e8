import echarts from 'echarts'
import { request, moneyFormat } from '../../common'

const FaasChartMixin = {
  props: [
    'chartId',
    'title',
    'value',
    'chartSetting'
  ],
  data () {
    return {
      chart: null,
      delta: 0,
      visible: false,
      series: [],
      option: null,
      positionStr: '',
      xAxis: [],
      graphSetting: [],
      totalField: ''
    }
  },
  methods: {
    async reloadData () {
      let resp = null
      let colorList = []
      let labelList = []
      let that = this
      if (this.chartId === 'partnerBalance') {
        this.totalField = 'Partner Balance'
        resp = await request(`/admin/faas/dashboard/balanceChart`, 'get', { 'period': this.value, 'type': 'partnerBalance', 'graphSetting': (this.chartSetting ? '' : 'get') })
      } else if (this.chartId === 'programBalance') {
        this.totalField = 'Program Balance'
        resp = await request(`/admin/faas/dashboard/balanceChart`, 'get', { 'period': this.value, 'type': 'programBalance', 'graphSetting': (this.chartSetting ? '' : 'get') })
      } else if (this.chartId === 'spendrPartner') {
        this.totalField = 'Partner Balance'
        resp = await request(`/admin/spendr/merchant/dashboard/balance-chart`, 'get', { ...this.value, 'type': 'partnerBalance', 'graphSetting': (this.chartSetting ? '' : 'get') })
        resp.data.chartCount = {
          bankDeposit: {
            '2021-06': 100,
            '2021-07': 200,
            '2021-08': 40,
            '2021-09': 60,
            '2021-10': 100,
            '2021-11': 110
          },
          partnerCommissions: {
            '2021-06': 10,
            '2021-07': 20,
            '2021-08': 4,
            '2021-09': 6,
            '2021-10': 10,
            '2021-11': 11
          },
          memberRewards: {
            '2021-06': 120,
            '2021-07': 120,
            '2021-08': 80,
            '2021-09': 100,
            '2021-10': 20,
            '2021-11': 90
          },
          memberDepostis: {
            '2021-06': 190,
            '2021-07': 280,
            '2021-08': 10,
            '2021-09': 30,
            '2021-10': 20,
            '2021-11': 40
          },
          memberPayout: {
            '2021-06': 110,
            '2021-07': 220,
            '2021-08': 80,
            '2021-09': 70,
            '2021-10': 120,
            '2021-11': 190
          },
          partnerBalance: {
            '2021-06': 110,
            '2021-07': 230,
            '2021-08': 40,
            '2021-09': 70,
            '2021-10': 190,
            '2021-11': 150
          }
        }
        resp.data.chartAvg = {
          bankDeposit: {
            '2021-06': 100,
            '2021-07': 200,
            '2021-08': 40,
            '2021-09': 60,
            '2021-10': 100,
            '2021-11': 110
          },
          partnerCommissions: {
            '2021-06': 10,
            '2021-07': 20,
            '2021-08': 4,
            '2021-09': 6,
            '2021-10': 10,
            '2021-11': 11
          },
          memberRewards: {
            '2021-06': 120,
            '2021-07': 120,
            '2021-08': 80,
            '2021-09': 100,
            '2021-10': 20,
            '2021-11': 90
          },
          memberDepostis: {
            '2021-06': 190,
            '2021-07': 280,
            '2021-08': 10,
            '2021-09': 30,
            '2021-10': 20,
            '2021-11': 40
          },
          memberPayout: {
            '2021-06': 110,
            '2021-07': 220,
            '2021-08': 80,
            '2021-09': 70,
            '2021-10': 120,
            '2021-11': 190
          },
          partnerBalance: {
            '2021-06': 110,
            '2021-07': 230,
            '2021-08': 40,
            '2021-09': 70,
            '2021-10': 190,
            '2021-11': 150
          }
        }
        resp.data.chartData = {
          bankDeposit: {
            '2021-06': 100,
            '2021-07': 200,
            '2021-08': 40,
            '2021-09': 60,
            '2021-10': 100,
            '2021-11': 110
          },
          partnerCommissions: {
            '2021-06': 10,
            '2021-07': 20,
            '2021-08': 4,
            '2021-09': 6,
            '2021-10': 10,
            '2021-11': 11
          },
          memberRewards: {
            '2021-06': 120,
            '2021-07': 120,
            '2021-08': 80,
            '2021-09': 100,
            '2021-10': 20,
            '2021-11': 90
          },
          memberDepostis: {
            '2021-06': 190,
            '2021-07': 280,
            '2021-08': 10,
            '2021-09': 30,
            '2021-10': 20,
            '2021-11': 40
          },
          memberPayout: {
            '2021-06': 110,
            '2021-07': 220,
            '2021-08': 80,
            '2021-09': 70,
            '2021-10': 120,
            '2021-11': 190
          },
          partnerBalance: {
            '2021-06': 110,
            '2021-07': 230,
            '2021-08': 40,
            '2021-09': 70,
            '2021-10': 190,
            '2021-11': 150
          }
        }
        resp.data.key = ['2021-06', '2021-07', '2021-08', '2021-09', '2021-10', '2021-11']
      } else if (this.chartId === 'spendrMerchant') {
        this.totalField = 'Merchant Balance'
        resp = await request(`/admin/spendr/merchant/dashboard/balance-chart`, 'get', { ...this.value, 'type': 'merchantBalance', 'graphSetting': (this.chartSetting ? '' : 'get') })
        resp.data.chartCount = {
          memberSpend: {
            '2021-06': 100,
            '2021-07': 200,
            '2021-08': 40,
            '2021-09': 60,
            '2021-10': 100,
            '2021-11': 110
          },
          merchantCommissions: {
            '2021-06': 10,
            '2021-07': 20,
            '2021-08': 4,
            '2021-09': 6,
            '2021-10': 10,
            '2021-11': 11
          },
          memberRewards: {
            '2021-06': 120,
            '2021-07': 120,
            '2021-08': 80,
            '2021-09': 100,
            '2021-10': 20,
            '2021-11': 90
          },
          merchantPayouts: {
            '2021-06': 190,
            '2021-07': 280,
            '2021-08': 10,
            '2021-09': 30,
            '2021-10': 20,
            '2021-11': 40
          },
          merchantFees: {
            '2021-06': 110,
            '2021-07': 220,
            '2021-08': 80,
            '2021-09': 70,
            '2021-10': 120,
            '2021-11': 190
          },
          merchantBalance: {
            '2021-06': 110,
            '2021-07': 230,
            '2021-08': 40,
            '2021-09': 70,
            '2021-10': 190,
            '2021-11': 150
          }
        }
        resp.data.chartAvg = {
          memberSpend: {
            '2021-06': 100,
            '2021-07': 200,
            '2021-08': 40,
            '2021-09': 60,
            '2021-10': 100,
            '2021-11': 110
          },
          merchantCommissions: {
            '2021-06': 10,
            '2021-07': 20,
            '2021-08': 4,
            '2021-09': 6,
            '2021-10': 10,
            '2021-11': 11
          },
          memberRewards: {
            '2021-06': 120,
            '2021-07': 120,
            '2021-08': 80,
            '2021-09': 100,
            '2021-10': 20,
            '2021-11': 90
          },
          merchantPayouts: {
            '2021-06': 190,
            '2021-07': 280,
            '2021-08': 10,
            '2021-09': 30,
            '2021-10': 20,
            '2021-11': 40
          },
          merchantFees: {
            '2021-06': 110,
            '2021-07': 220,
            '2021-08': 80,
            '2021-09': 70,
            '2021-10': 120,
            '2021-11': 190
          },
          memberBalance: {
            '2021-06': 110,
            '2021-07': 230,
            '2021-08': 40,
            '2021-09': 70,
            '2021-10': 190,
            '2021-11': 150
          }
        }
        resp.data.chartData = {
          memberSpend: {
            '2021-06': 100,
            '2021-07': 200,
            '2021-08': 40,
            '2021-09': 60,
            '2021-10': 100,
            '2021-11': 110
          },
          merchantCommissions: {
            '2021-06': 10,
            '2021-07': 20,
            '2021-08': 4,
            '2021-09': 6,
            '2021-10': 10,
            '2021-11': 11
          },
          memberRewards: {
            '2021-06': 120,
            '2021-07': 120,
            '2021-08': 80,
            '2021-09': 100,
            '2021-10': 20,
            '2021-11': 90
          },
          merchantPayouts: {
            '2021-06': 190,
            '2021-07': 280,
            '2021-08': 10,
            '2021-09': 30,
            '2021-10': 20,
            '2021-11': 40
          },
          merchantFees: {
            '2021-06': 110,
            '2021-07': 220,
            '2021-08': 80,
            '2021-09': 70,
            '2021-10': 120,
            '2021-11': 190
          },
          merchantBalance: {
            '2021-06': 110,
            '2021-07': 230,
            '2021-08': 40,
            '2021-09': 70,
            '2021-10': 190,
            '2021-11': 150
          }
        }
        resp.data.key = ['2021-06', '2021-07', '2021-08', '2021-09', '2021-10', '2021-11']
      } else if (this.chartId === 'spendrMember') {
        this.totalField = 'Member Balance'
        resp = {
          success: true,
          data: {
            chartCount: {
              memberDeposits: {
                '2021-06': 100,
                '2021-07': 200,
                '2021-08': 40,
                '2021-09': 60,
                '2021-10': 100,
                '2021-11': 110
              },
              memberRewards: {
                '2021-06': 10,
                '2021-07': 20,
                '2021-08': 4,
                '2021-09': 6,
                '2021-10': 10,
                '2021-11': 11
              },
              memberSpend: {
                '2021-06': 120,
                '2021-07': 120,
                '2021-08': 80,
                '2021-09': 100,
                '2021-10': 20,
                '2021-11': 90
              },
              memberWithdrawls: {
                '2021-06': 190,
                '2021-07': 280,
                '2021-08': 10,
                '2021-09': 30,
                '2021-10': 20,
                '2021-11': 40
              },
              memberFees: {
                '2021-06': 110,
                '2021-07': 220,
                '2021-08': 80,
                '2021-09': 70,
                '2021-10': 120,
                '2021-11': 190
              },
              memberBalance: {
                '2021-06': 110,
                '2021-07': 230,
                '2021-08': 40,
                '2021-09': 70,
                '2021-10': 190,
                '2021-11': 150
              }
            },
            chartAvg: {
              memberDeposits: {
                '2021-06': 100,
                '2021-07': 200,
                '2021-08': 40,
                '2021-09': 60,
                '2021-10': 100,
                '2021-11': 110
              },
              memberRewards: {
                '2021-06': 10,
                '2021-07': 20,
                '2021-08': 4,
                '2021-09': 6,
                '2021-10': 10,
                '2021-11': 11
              },
              memberSpend: {
                '2021-06': 120,
                '2021-07': 120,
                '2021-08': 80,
                '2021-09': 100,
                '2021-10': 20,
                '2021-11': 90
              },
              memberWithdrawls: {
                '2021-06': 190,
                '2021-07': 280,
                '2021-08': 10,
                '2021-09': 30,
                '2021-10': 20,
                '2021-11': 40
              },
              memberFees: {
                '2021-06': 110,
                '2021-07': 220,
                '2021-08': 80,
                '2021-09': 70,
                '2021-10': 120,
                '2021-11': 190
              },
              memberBalance: {
                '2021-06': 110,
                '2021-07': 230,
                '2021-08': 40,
                '2021-09': 70,
                '2021-10': 190,
                '2021-11': 150
              }
            },
            chartData: {
              memberDeposits: {
                '2021-06': 100,
                '2021-07': 200,
                '2021-08': 40,
                '2021-09': 60,
                '2021-10': 100,
                '2021-11': 110
              },
              memberRewards: {
                '2021-06': 10,
                '2021-07': 20,
                '2021-08': 4,
                '2021-09': 6,
                '2021-10': 10,
                '2021-11': 11
              },
              memberSpend: {
                '2021-06': 120,
                '2021-07': 120,
                '2021-08': 80,
                '2021-09': 100,
                '2021-10': 20,
                '2021-11': 90
              },
              memberWithdrawls: {
                '2021-06': 190,
                '2021-07': 280,
                '2021-08': 10,
                '2021-09': 30,
                '2021-10': 20,
                '2021-11': 40
              },
              memberFees: {
                '2021-06': 110,
                '2021-07': 220,
                '2021-08': 80,
                '2021-09': 70,
                '2021-10': 120,
                '2021-11': 190
              },
              memberBalance: {
                '2021-06': 110,
                '2021-07': 230,
                '2021-08': 40,
                '2021-09': 70,
                '2021-10': 190,
                '2021-11': 150
              }
            },
            key: ['2021-06', '2021-07', '2021-08', '2021-09', '2021-10', '2021-11']
          }
        }
      }
      if (this.$store.state.User.cpKey === 'cp_spendr') {
        if (resp && resp.success) {
          this.delta = resp.data.balance
        }
        return
      }
      if (resp && resp.success) {
        if (!this.chartSetting) {
          this.graphSetting = resp.data.chartSetting
        } else {
          this.graphSetting = this.chartSetting
        }
        colorList = this.graphSetting.colorList
        labelList = this.graphSetting.itemList
        labelList.forEach((element, index) => {
          if (index === (labelList.length - 1)) {
            this.series.push(
              {
                name: element,
                type: 'line',
                color: 'rgb(' + colorList[index] + ')',
                lineStyle: {
                  type: 'dotted'
                },
                data: []
              }
            )
          } else {
            this.series.push(
              {
                name: element,
                type: 'line',
                color: 'rgb(' + colorList[index] + ')',
                areaStyle: {
                  color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [
                      {
                        offset: 0, color: 'rgba(' + colorList[index] + ', 1)'
                      },
                      {
                        offset: 0.7, color: 'rgba(' + colorList[index] + ', 0.1)'
                      },
                      {
                        offset: 1, color: 'rgba(' + colorList[index] + ', 0)'
                      }
                    ]
                  }
                },
                data: []
              }
            )
          }
        })
        this.graphSetting.fieldList.forEach((e, index) => {
          this.series[index].data = Object.values(resp.data.chartData[e.name])
        })
        this.series[this.graphSetting.fieldList.length].data = Object.values(resp.data.chartData[this.graphSetting.totalField])
        this.xAxis = resp.data.key
        this.option.tooltip.formatter = function (params) {
          let endValue = that.chart.getOption().dataZoom[0].endValue
          let startValue = that.chart.getOption().dataZoom[0].startValue
          let totalCount = 0
          let totalValue = 0
          let str = '<div class="tooltip-area-' + that.positionStr + '" style="box-shadow: 0 5px 15px 0 rgba(28, 20, 70, 0.1);border-radius:20px;padding:30px;padding-top:16px;font-family: Poppins;font-size: 14px;"><p style="text-align:center;margin:0px;color:#231f20;font-weight: 600;">' + that.titleStr(params[0]['name']) + '</p>' +
            '<p style="text-align:center;margin:0px;color:#231f20;font-weight: 600;">Date ranges: ' + that.titleStr(that.xAxis[startValue], true) + ' ~ ' + that.titleStr(that.xAxis[endValue], true) + '</p>' +
            '<table style="min-width:500px;">' +
            '<tr style="color:#231f20;width:100%;">' +
            '<th style="text-align:left;font-weight: 600;">Transaction Type</th><th style="text-align:left;"># of TXNs</th><th style="text-align:left;">Total ($)</th><th style="text-align:left;">Average ($)</th>' +
            '</tr>'
          that.graphSetting.fieldList.forEach((e, index) => {
            str += '<tr style="color:#414141">' +
              '<td style="color: rgb(' + colorList[index] + ');font-weight: 600;">' + labelList[index] + '</td><td>' + resp.data.chartCount[e.name][params[0]['name']] + '</td><td>' + moneyFormat(resp.data.chartData[e.name][params[0]['name']] * 100) + '</td><td>' + moneyFormat(resp.data.chartAvg[e.name][params[0]['name']] * 100) + '</td>' +
              '</tr>'
            totalCount += resp.data.chartCount[e.name][params[0]['name']] * 1
            totalValue += resp.data.chartData[e.name][params[0]['name']] * 100
          })
          str += '<tr style="color:#231f20;font-weight: 600;">' +
            '<td>Total</td><td>' + totalCount + '</td>' +
            '<td>' + moneyFormat(totalValue) +
            '</td>' +
            '<td>' + '</td>' +
            '</tr>' +
            '<tr style="color:#414141;font-weight: 600;">' + '<td style="text-transform:capitalize;"><i style="background:#fcb72a;width:10px; height: 10px;border-radius:5px;display: inline-block;margin-right: 5px;"></i>' + that.totalField + '</td><td>' + moneyFormat(resp.data.chartData[that.graphSetting.totalField][params[0]['name']] * 100) + '</td>' +
            '<td>' + '</td>' +
            '<td>' + '</td>' +
            '</tr>' +
            '</table>' +
            '</div>'
          return str
        }
        this.delta = resp.data.balance
        this.option.xAxis[0].data = resp.data.key
        this.option.legend.data = labelList
        this.option.series = this.series
        this.chart.setOption(this.option)
      }
    },
    initChart () {
      this.chart = echarts.init(document.getElementById(this.chartId), 'primary')
      let that = this
      this.option = {
        legend: {
          bottom: '0px',
          icon: 'pin',
          data: []
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          },
          position: function (pos, params, dom, rect, size) {
            // 鼠标在左侧时 tooltip 显示到右侧，鼠标在右侧时 tooltip 显示到左侧。
            let obj = { top: pos[1] - 290 }
            if (pos[0] < size.viewSize[0] / 2) {
              obj['left'] = pos[0] - 50
              that.positionStr = 'left'
            } else {
              that.positionStr = 'right'
              obj['right'] = size.viewSize[0] - pos[0] - 50
            }
            return obj
          },
          // alwaysShowContent: true,
          backgroundColor: '#ffffff',
          padding: 0
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '60px',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            data: []
          }
        ],
        dataZoom: {
          bottom: '25px'
        },
        yAxis: [
          {
            type: 'value',
            splitLine: {
              show: false
            }
          }
        ],
        series: []
      }
    },
    titleStr (str = '', range = false) {
      let title = str.split('-')
      let month = {
        '01': 'Jan.',
        '02': 'Feb.',
        '03': 'Mar.',
        '04': 'Apr.',
        '05': 'May',
        '06': 'Jun.',
        '07': 'Jul.',
        '08': 'Aug.',
        '09': 'Sept.',
        '10': 'Oct.',
        '11': 'Nov.',
        '12': 'Dec.'
      }
      let bigMonth = ['01', '03', '05', '07', '08', '10', '12']
      let smallMonth = ['04', '06', '09', '11']
      // let days = ['01', '02', '03', '21', '22', '23', '31']
      if (title.length === 3) {
        if (title[2].slice(-1) === '1' && title[2] !== '11') {
          return month[title[1]] + ' ' + title[2] + 'st, ' + title[0]
        } else if (title[2].slice(-1) === '2' && title[2] !== '12') {
          return month[title[1]] + ' ' + title[2] + 'nd, ' + title[0]
        } else if (title[2].slice(-1) === '3' && title[2] !== '13') {
          return month[title[1]] + ' ' + title[2] + 'rd, ' + title[0]
        } else {
          return month[title[1]] + ' ' + title[2] + 'th, ' + title[0]
        }
      } else if (title.length === 2) {
        if (range) {
          return this.value === 'week' ? 'Week ' + title[1] + ', ' + title[0] : month[title[1]] + ', ' + title[0]
        } else {
          let end = '29th'
          if (bigMonth.indexOf(title[1]) !== -1) {
            end = '31st'
          } else if (smallMonth.indexOf(title[1]) !== -1) {
            end = '30th'
          } else if (title[0] % 4) {
            end = '28th'
          }
          return this.value === 'week' ? 'Week ' + title[1] + ' Sun., ' + title[0] + ' - ' + 'Week ' + title[1] + ' Sat., ' + title[0] : month[title[1]] + ' ' + '01st, ' + title[0] + ' - ' + month[title[1]] + ' ' + end + ', ' + title[0]
        }
      } else if (title.length === 1) {
        if (range) {
          return title[0]
        } else {
          return month['01'] + ' 01st, ' + title[0] + ' - ' + month['12'] + '31st, ' + title[0]
        }
      }
    }
  }
}
export default FaasChartMixin
