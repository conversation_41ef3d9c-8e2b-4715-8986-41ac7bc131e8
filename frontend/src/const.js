window.deviceMode = 'web'
window.$ = require('jquery')

// window.deviceMode = 'clf'
const deviceMode = window.deviceMode

// Production
const API_KEY_PRODUCTION = ''

// Staging
const API_KEY_STAGING = ''

let origin = window.location.origin
if (window.cordova) {
  origin = 'https://www.virtualcards.us'
  // origin = 'https://staging.virtualcards.us'

  if (deviceMode === 'clf') {
    origin = 'https://utc.virtualcards.us'
    // origin = 'https://clf-staging.virtualcards.us'
  }
}
if (window.location.host === 'localhost:8042') {
  origin = 'https://utc.virtualcards.us'
}

const utcServer = window.localStorage.getItem('utc_server')
if (utcServer) {
  origin = utcServer
}

// origin = 'https://utc.virtualcards.us'
// origin = window.location.origin
// origin = 'http://clf.span.hans'

window.origin = origin

let API_KEY = API_KEY_PRODUCTION
// let API_KEY = API_KEY_STAGING

if (origin === 'https://www.virtualcards.us') {
  API_KEY = API_KEY_PRODUCTION
} else if (origin === 'https://staging.virtualcards.us') {
  API_KEY = API_KEY_STAGING
}

const NATIVE_CALL = {
  CLOSE_LOAD_CARD_PAGE: 'closeLoadCardPage',
  OPEN_IN_SAFARI: 'openInSafari'
}

const predicates = [
  {
    label: 'Is',
    value: '='
  },
  {
    label: 'Is not',
    value: '!='
  }
]

const chartName = {
  USER_CHART: 'users_dashboard',
  LOAD_CHART: 'load_activity',
  USAGE_CHART: 'usage_activity',
  REVENUE_CHART: 'revenue',
  ACTIVE_CARDS_CHART: 'active_cards',
  CARD_FEES_CHART: 'card_fees_collected'
}

const chartType = {
  LINE_CHART: '1',
  BAR_CHART: '2',
  SUMMARY_CHART: '3'
}

const docTypes = [
  {
    value: 'DL',
    label: 'Driver License',
    requirement: 'BOTH'
  },
  {
    value: 'ID',
    label: 'ID Card',
    requirement: 'BOTH'
  },
  {
    value: 'PSP',
    label: 'Passport',
    requirement: 'FRONT'
  }
]

const format = {
  date: 'MM/DD/YYYY'
}

const clf = {
  billStatuses: ['Paid', 'Unpaid', 'Overdue', 'Draft', 'All'].map(v => ({
    label: v,
    value: v.toLowerCase()
  })),
  chart: {
    grid: {
      left: '20%',
      top: 20,
      bottom: 20
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      axisLabel: {
        interval: 0,
        showMinLabel: true,
        showMaxLabel: true
      },
      axisPointer: {
        show: true,
        lineStyle: {
          type: 'dash',
          opacity: 0.75
        },
        label: {
          show: false
        }
      }
    },
    yAxis: {
      type: 'value',
      scale: true,
      minInterval: 1,
      boundaryGap: ['5%', '5%'],
      axisPointer: {
        show: true,
        lineStyle: {
          type: 'dash',
          opacity: 0.75
        },
        label: {
          show: false
        }
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        axis: 'y'
      },
      backgroundColor: '#fff',
      borderColor: '#666',
      textStyle: {
        color: '#333'
      },
      extraCssText: 'box-shadow: 0 0 8px rgba(0, 0, 0, 0.8);',
      confine: true
    }
  }
}

const fis = {
  chart: {
    tooltip: {
      trigger: 'axis',
      backgroundColor: '#fff',
      borderColor: '#666',
      textStyle: {
        color: '#333'
      },
      extraCssText: 'box-shadow: 0 0 8px rgba(0, 0, 0, 0.8);',
      position: (point, params, dom, rect, size) => {
        const $d = window.$(dom)
        return [point[0] - $d.width() / 2, point[1] - $d.height() - 20]
      }
    }
  }
}

const pullConfig = {
  pullText: 'Pull to refresh', // The text is displayed when you pull down
  triggerText: 'Release to refresh', // The text that appears when the trigger distance is pulled down
  loadingText: 'Refreshing...', // The text in the load
  doneText: 'Refreshed', // Load the finished text
  failText: 'Failed to refresh' // Load failed text
}

function updateOrigin (o) {
  origin = o
}

const keys = {
  GOOGLE_WEBSITE: 'AIzaSyCtjFRds8BqTZtZjeULsaQkDKRaIW16aCw'
}

const esSolo = {
  cardShippingMethods: [
    {
      label: 'Standard',
      value: 1
    },
    {
      label: 'Priority',
      value: 2
    },
    {
      label: 'Rush Delivery',
      value: 3
    },
    {
      label: 'Registered Mail',
      value: 4
    }
  ],
  addressProofTypes: [
    {
      label: 'Utility Bill',
      value: 1
    },
    {
      label: 'Driver\'s License',
      value: 2
    },
    {
      label: 'Passport',
      value: 3
    },
    {
      label: 'National ID',
      value: 4
    },
    {
      label: 'Property Tax Receipt',
      value: 5
    },
    {
      label: 'Lease Agreement',
      value: 6
    },
    {
      label: 'Insurance Card',
      value: 7
    },
    {
      label: 'Landline Phone Statement',
      value: 8
    },
    {
      label: 'Bank Statement',
      value: 9
    },
    {
      label: 'Other',
      value: 10
    }
  ],
  idTypes: [
    {
      label: 'Driver\'s License',
      value: 1
    },
    {
      label: 'National ID',
      value: 2
    },
    {
      label: 'Passport',
      value: 3
    },
    {
      label: 'Social Security Card',
      value: 4
    },
    {
      label: 'Birth Certificate',
      value: 5
    }
  ]
}

const genders = [
  {
    label: 'Female',
    value: 'Female'
  },
  {
    label: 'Male',
    value: 'Male'
  }
]

const transferMex = {
  BOTM: {
    cardStatuses: [
      'Active', 'Paused', 'Inactive'
    ]
  },
  Rapid: {
    cardStatuses: [
      'Active', 'On Hold', 'Lost', 'Suspended',
      'Block', 'Stolen', 'SuspectedFraud', 'Account Under Review',
      'Damaged', 'Hot List', 'Terminated'
    ]
  }
}

export {
  API_KEY,
  NATIVE_CALL,
  chartName,
  chartType,
  docTypes,
  predicates,
  format,
  clf,
  esSolo,
  fis,
  deviceMode,
  origin,
  pullConfig,
  updateOrigin,
  keys,
  genders,
  transferMex
}
