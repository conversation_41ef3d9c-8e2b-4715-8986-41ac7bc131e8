{"name": "tc_span", "version": "1.9.1", "description": "Tern Commerce - SPAN platform", "productName": "UTC", "cordovaId": "us.virtualcards.app.clf", "author": "<PERSON> <<EMAIL>>", "private": true, "scripts": {"lint": "eslint --ext .js,.vue src", "test": "echo \"No test specified\" && exit 0", "dev": "quasar dev -t mat", "dev-ios": "quasar dev -t ios", "dev2": "quasar dev -t mat -m spa -p 8042", "dev3": "quasar dev -t ios -m spa -p 8042", "build": "quasar build -c -t mat && node ./bin/build.js", "ios": "quasar dev -t ios -m cordova -T ios", "android": "quasar dev -t mat -m cordova -T android", "build-ios": "quasar build -t ios -m cordova -T ios", "build-android": "quasar build -t mat -m cordova -T android", "build-post": "node ./bin/build.js"}, "dependencies": {"@babel/runtime": "7.0.0-beta.46", "@mdi/font": "^6.5.95", "@vue/composition-api": "^1.7.1", "accounting": "^0.4.1", "animate.css": "^3.6.1", "await-to-js": "^2.0.1", "axios": "^0.19.0", "babel-runtime": "^6.26.0", "chokidar": "^3.4.0", "color": "^3.1.2", "copy-to-clipboard": "^3.2.0", "country-data": "0.0.31", "currency-flags": "^1.2.1", "currency-formatter": "^1.4.2", "daterangepicker": "^2.1.25", "dexie": "^3.2.3", "echarts": "^4.6.0", "ejs": "^3.1.6", "fix-orientation": "^1.0.0", "fontawesome": "^4.7.2", "google-libphonenumber": "^3.2.2", "google-maps": "^3.3.0", "html2canvas": "^1.0.0-rc.7", "jquery": "^3.4.0", "js-md5": "^0.7.3", "lodash": "^4.17.14", "mathjs": "^9.5.0", "mobile-detect": "^1.4.2", "modernizr": "^3.7.1", "moment": "^2.29.4", "moment-range": "^4.0.1", "moment-timezone": "^0.5.14", "plaid": "^10.3.0", "qrcode": "^1.3.3", "qs": "^6.5.2", "quasar": "^0.0.18", "quasar-framework": "^0.17.20", "query-string": "^6.0.0", "sanitize-html": "2.7.3", "signature_pad": "^3.0.0-beta.4", "strip-ansi": "=3.0.1", "upath": "^1.1.2", "v-img-fallback": "^0.1.4", "vue": "^2.5.16", "vue-filter": "^0.2.5", "vue-i18n": "^7.3.3", "vue-json-viewer": "2", "vue-line-clamp": "^1.2.3", "vue-load-image": "^0.1.8", "vue-plaid-link": "^0.6.1", "vue-pull-to": "^0.1.6", "vue-recaptcha": "1.3.0", "vue-router": "^3.0.1", "vue-sweetalert2": "^1.6.4", "vue-template-compiler": "^2.5.17", "vue2-filters": "^0.3.0", "vuedraggable": "^2.23.2", "vuelidate": "^0.7.6", "vuex": "^3.0.1", "vuex-persistedstate": "^2.5.1"}, "devDependencies": {"@vue/eslint-config-standard": "^4.0.0", "babel-eslint": "^10.0.1", "eslint": "^5.10.0", "eslint-loader": "^2.1.1", "eslint-plugin-vue": "^5.0.0", "esm": "^3.2.25", "fstream": "^1.0.12", "lodash.mergewith": "^4.6.2", "lodash.template": "^4.5.0", "node-sass": "^4.12.0", "quasar-cli": "^0.17.0", "sass-loader": "^6.0.7", "strip-ansi": "=3.0.1", "webpack-bundle-analyzer": "^3.3.2"}, "optionalDependencies": {"fsevents": "^2.0.6"}, "engines": {"node": ">= 8.9.0", "npm": ">= 5.6.0", "yarn": ">= 1.6.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "resolutions": {"ajv": "6.8.1"}}