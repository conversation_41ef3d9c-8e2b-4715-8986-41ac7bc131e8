# Guide for PCI

## Upgrade

### nginx

see https://nginx.org/en/linux_packages.html#Ubuntu

### OpenSSH

```shell
sudo cp /etc/ssh/sshd_config /etc/ssh/sshd_config.bak
#sudo cp sshd_config /etc/ssh/
sudo apt install build-essential zlib1g-dev libssl-dev libpam0g-dev libselinux1-dev
wget https://cdn.openbsd.org/pub/OpenBSD/OpenSSH/portable/openssh-9.9p2.tar.gz
tar -zxvf openssh-9.9p2.tar.gz
cd openssh-9.9p2
./configure --with-pam --with-selinux --with-md5-passwords --sysconfdir=/etc/ssh
make
sudo make install
sudo systemctl restart sshd
ssh -V
```
Notes:
* Use `telnet server_ip ssh_port` to verify if it's updated.
* If not, ensure that the `sshd` command is the latest on one the server. Use `which sshd` to check.
* The possible paths to check:
  * `/usr/sbin/sshd`
  * `/usr/local/sbin/sshd`

## Installed patches

```shell
apt list --installed
apt list --manual-installed
apt list --upgradable
```

## Uninstalled patches

```shell
# run as root
unattended-upgrades
```

## Running services

```shell
systemctl --type=service --state=running > running_services_prod.txt
```

## Failed logins

### EC2

```shell
grep "failed" /var/log/auth.log # and maybe also /var/log/auth.log.1
grep "Disconnected from authenticating user" /var/log/auth.log
grep "Failed password" /var/log/auth.log
grep "Authentication failure" /var/log/auth.log
```

### RDS

In RDS log group, search for `Access denied for user`: `error/mysql-error-running.log`.

## Scan virus

Use https://docs.clamav.net/

```shell
# install
apt install clamav
apt install clamav-daemon

# start the service
systemctl restart clamav-daemon
systemctl restart clamav-freshclam

# refresh virus library
freshclam

# scan as root
cd /root
> ~/clamscan.log
clamscan . # to test
nohup clamscan --recursive --infected --suppress-ok-results --bell / --log=./clamscan.log &

# view scan status
tail ./clamscan.log
tail /var/log/clamav/clamscan.log

# stop the service
systemctl stop clamav-freshclam
systemctl stop clamav-daemon
```

### Install as cron jobs

```cron
0 2 * * *  freshclam
0 3 * * *  clamscan --recursive / --infected --suppress-ok-results --log=/var/log/clamav/clamscan.log
```

### Provide evidence

Log as root:

```shell
crontab -l
freshclam --version
clamscan --version
clamd --version
cat /etc/clamav/clamd.conf
cat /etc/clamav/freshclam.conf
systemctl status clamav-freshclam
systemctl status clamav-daemon
cat /var/log/clamav/freshclam.log
grep -E "SCAN SUMMARY| Infected files" ./clamscan.log
grep -E "SCAN SUMMARY| Infected files" /var/log/clamav/freshclam.log
```

## System time

```shell
systemctl status ntp
ntpq -p
cat /etc/ntp.conf
journalctl -u systemd-timesyncd --no-pager
journalctl | grep "Time has been changed"
journalctl | grep "time change"
journalctl | grep "Time Synchronization"
journalctl | grep "ntpd"
journalctl | grep "time slew"
journalctl | grep "synchronized"
cat /var/log/syslog | grep chronyd
cat /var/log/syslog | grep ntpd
cat /var/log/syslog | grep "time change"
cat /var/log/auth.log | grep "timedatectl"
```

### Audit time config change

```shell
apt install auditd
echo "-w /etc/ntp.conf -p wa -k time_config_changes" >> /etc/audit/rules.d/audit.rules
systemctl restart auditd
systemctl status auditd
auditctl -l
```

## DataDog

```shell
service datadog-agent restart
service datadog-agent status
datadog-agent status
datadog-agent flare
journalctl -u datadog-agent --no-pager --lines=50
```

## History

Search anything else in the Slack channel `#int-pci`.
