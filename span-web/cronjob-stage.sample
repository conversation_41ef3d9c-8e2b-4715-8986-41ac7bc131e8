PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
#Mins        Hours        Days        Months        Day of the week
*            *            *           *             *        chmod -R 777 /var/cache/span/
5            3            *           *             SUN      > /var/log/span/dev.log && > /var/log/span/test.log && > /var/log/span/prod.log
### General [{env_full}]
*            *            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/email-batch
21           *            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/system/check-redis
9            3            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/system/check-disk
20           1            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/update-google-fonts
30           1            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/delete-outdated-reports
20           2            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/webhook/github/notify
5            2            1           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/webhook/github/notify\?monthly\=true
30           0            *           *             *        php {project_directory}/bin/console span:db:remove-old-token
30           6            *           *             *        php {project_directory}/bin/console span:db:backup
18           0            *           *             *        php {project_directory}/bin/console span:dev:log:backup
22           2            *           *             *        php {project_directory}/bin/console span:dev:remove-outdated-backups
10           12           *           *             SUN      php {project_directory}/bin/console span:update-dev-cookie
3            5,6,7,8      *           3,11          SUN      php {project_directory}/bin/console span:dev:install-cron-jobs --install
10           1            *           *             *        php {project_directory}/bin/console span:app:update-exchange-rate
*            *            *           *             *        php {project_directory}/bin/console span:bg:instant
*            *            *           *             *        php {project_directory}/bin/console span:monitor:system
10           0            *           *             *        php {project_directory}/bin/console span:db:remove-old-email
15           0            *           *             *        php {project_directory}/bin/console span:db:remove-old-pin
45,15        *            *           *             *        php {project_directory}/bin/console span:dev:move-files-to-s3
#12          *            *           *             *        php {project_directory}/bin/console span:dev:monitor-error-log

### US Unlocked [{env_full}]
## wget
*            *            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/unlock-users
17           *            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/load-card-batch

## Command
25           1            *           *             *        php {project_directory}/bin/console span:usu:find-users-diff-running-balance
2            0            *           *             *        php {project_directory}/bin/console span:usu:report:daily --send
12           7            *           *             *        php {project_directory}/bin/console span:usu:remind-neg-users --remind
20           */4          *           *             *        php {project_directory}/bin/console span:usu:report:fee
33           20           1           *             *        php {project_directory}/bin/console span:usu:report:revenue
30           13           *           *             *        php {project_directory}/bin/console span:usu:remind-id-expired-users --remind
51           *            *           *             *        php {project_directory}/bin/console span:usu:coinflow:sync-payments
51           2            *           *             *        php {project_directory}/bin/console span:usu:rain:close-delayed-cards

### TransferMex [{env_full}]
## Wget
30           5,17         *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/mex/rapid/check-failed-refunds
18           *            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/mex/dashboard/update-cache
# */20       *            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/mex/rapid/execute-all-pay-employee
*/30         *            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/mex/rapyd/monitor-instant-transfer/alert
12           2            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/mex/check-canceled-transfers
24           */12         *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/mex/retry-settlement
10           */6          *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/mex/check-batch-payment

## Command
#15           */8          *           *             *        php {project_directory}/bin/console span:mex:store-program-chart-data
50           3            *           *             *        php {project_directory}/bin/console span:mex:employer:update-month-activity-report --type=day
18           0-5,7-23     *           *             *        php {project_directory}/bin/console span:mex:update-transactions --total --days=1
18           6            *           *             *        php {project_directory}/bin/console span:mex:update-transactions --total --days=15
13           *            *           *             *        php {project_directory}/bin/console span:mex:update-agent-balances
33           3            *           *             *        php {project_directory}/bin/console span:mex:rapid:find-unmatched-txn --fix
11           */4          *           *             *        php {project_directory}/bin/console span:mex:update-payout-status --days=31
12           3            *           *             *        php {project_directory}/bin/console span:mex:update-error-payout-status --days=30
12           17           3           *             *        php {project_directory}/bin/console span:mex:update-error-payout-status --days=400
*/20         *            *           *             *        php {project_directory}/bin/console span:mex:employer-payout-execute
*/10         *            *           *             *        php {project_directory}/bin/console span:mex:check-dup-refunds --alert
25           */2          *           *             *        php {project_directory}/bin/console span:mex:process-payout-queue
7            *            *           *             *        php {project_directory}/bin/console span:mex:check-legacy-cards --move
34           0            *           *             *        php {project_directory}/bin/console span:mex:update-rapid-agent-transactions --days=3 --notify
34           1-6          *           *             *        php {project_directory}/bin/console span:mex:update-rapid-agent-transactions --days=1 --notify
34           7-23         *           *             *        php {project_directory}/bin/console span:mex:update-rapid-agent-transactions --days=0 --notify
50           0            *           *             *        php {project_directory}/bin/console span:mex:update-botm-agent-transactions --days=3 --notify
50           1-6          *           *             *        php {project_directory}/bin/console span:mex:update-botm-agent-transactions --days=1 --notify
50           7-23         *           *             *        php {project_directory}/bin/console span:mex:update-botm-agent-transactions --days=0 --notify
50           2            *           *             *        php {project_directory}/bin/console span:dev:mex:deduct-dup-refunds
15           4            *           *             *        php {project_directory}/bin/console span:mex:update-rapyd-transactions --days=30 --detailDays=30
05           5            *           *             *        php {project_directory}/bin/console span:mex:inspect-rapyd-transactions --alert
45           */2          *           *             *        php {project_directory}/bin/console span:mex:update-rapyd-transactions --days=2 --detailDays=2
0            19           *           *             FRI      php {project_directory}/bin/console span:dev:mex:send-remind-msg
40           13           *           *             *        php {project_directory}/bin/console span:mex:check-all-cards-status
45           19           *           *             *        php {project_directory}/bin/console span:mex:remind-insecure-pin --notify
# 4          */8          *           *             *        php {project_directory}/bin/console span:mex:send-account-limit-error-email
6            */6          *           *             *        php {project_directory}/bin/console span:mex:member:find-duplicated-accounts
22,52        *            *           *             *        php {project_directory}/bin/console span:mex:process-operation-queue
4            *            *           *             *        php {project_directory}/bin/console span:mex:process-partner-error-pool --live
13           */3          *           *             *        php {project_directory}/bin/console span:mex:process-unhandled-rapyd-errors
37           11           */7         *             *        php {project_directory}/bin/console span:mex:update-card-balance-status --startDays=0 --endDays=3
35           4            *           *             *        php {project_directory}/bin/console span:mex:update-uniteller-transactions --days=30
*/5          *            *           *             *        php {project_directory}/bin/console span:mex:update-uniteller-transactions --days=2
0            */6          *           *             *        php {project_directory}/bin/console span:mex:update-uniteller-transfers-status
52           6            *           *             *        php {project_directory}/bin/console span:mex:remind-under-review-payout --notify
8            */12         *           *             *        php {project_directory}/bin/console span:mex:update-member-record
5            0            *           *             *        php {project_directory}/bin/console span:mex:update-program-list
*/15         *            *           *             *        php {project_directory}/bin/console span:mex:sent-message-batch
13           *            *           *             *        php {project_directory}/bin/console span:mex:check-rapyd-balance
21           *            *           *             *        php {project_directory}/bin/console span:mex:send-rapyd-inquiry --notify
37           3            *           *             *        php {project_directory}/bin/console span:mex:update-card-balance-status
27           1            *           *             *        php {project_directory}/bin/console span:mex:update-payout-methods
18           4,16         *           *             *        php {project_directory}/bin/console span:mex:member:sync-botm-user
0            3            *           *             *        php {project_directory}/bin/console span:mex:transfer-min-card-to-base
30           0,12,18      *           *             *        php {project_directory}/bin/console span:mex:update-botm-transactions --days=2 --env=prod
30           6            *           *             *        php {project_directory}/bin/console span:mex:update-botm-transactions --days=30 --env=prod
*            *            *           *             *        php {project_directory}/bin/console span:mex:remind-upload-payout
15           4,5,12,20    *           *             *        php {project_directory}/bin/console span:mex:update-monthly-snapshot-report --type=day --force
25           4,5,17       *           *             *        php {project_directory}/bin/console span:mex:update-monthly-snapshot-report --type=week --force
35           4,5,18       *           *             *        php {project_directory}/bin/console span:mex:update-monthly-snapshot-report --type=month --force

### Wilen [{env_full}]
## Wget
30           4            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/wilen/soap/get-wilen-data
*/30         *            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/wilen/soap/execute-campaign

### LeafLink [{env_full}]
### These jobs end up running in EST and are subject to DST
##  Wget
### 00       12           *           *             1        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/onboard/email
40           22^          *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ach/daily-counter-reset
40           22^          *           *             5        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ach/weekly-counter-reset
40           22^          1           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ach/monthly-counter-reset
0            *            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ach/return
40           *            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ach/post-fed
45           20^          *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ach/set-settled
5            21^          *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/report/daily-check

### LeafLink Batch - Week Days
30           21^          *           *             1        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ach/updated-batch
30           21^          *           *             2        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ach/updated-batch
30           21^          *           *             3        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ach/updated-batch
30           21^          *           *             4        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ach/updated-batch
30           21^          *           *             5        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ach/updated-batch

### LeafLink B2B
### 45       21           *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ach/b2b-batch
20           21^          *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ach/b2b-daily-counter-reset
20           21^          *           *             5        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ach/b2b-weekly-counter-reset
20           21^          1           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ach/b2b-monthly-counter-reset
15           *            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ach/b2b-return
55           *            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ach/b2b-post-fed
0            21^          *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ach/b2b-set-settled

### LeafLink Batch - Week Days
45           21^          *           *             1        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ach/b2b-batch
45           21^          *           *             2        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ach/b2b-batch
45           21^          *           *             3        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ach/b2b-batch
45           21^          *           *             4        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ach/b2b-batch
45           21^          *           *             5        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ach/b2b-batch

### LeafLink EWB
50           *            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/imap/ewb-prefed
10           *            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ewb-return
44           */8          *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/email/test

### LeafLink EWB Batch - Week Days
40           21^          *           *             1        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ewb-batch
40           21^          *           *             2        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ewb-batch
40           21^          *           *             3        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ewb-batch
40           21^          *           *             4        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ewb-batch
40           21^          *           *             5        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ewb-batch

### LeafLink upload CSV file
## Command
10           0            *           *             *        php {project_directory}/bin/console span:leaflink:upload-bank-info
