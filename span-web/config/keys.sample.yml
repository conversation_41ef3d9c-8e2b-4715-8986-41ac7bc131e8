parameters:
  # KMS
  kms_key:
  kms_secret:
  kms_data_key:

  # AWS S3
  s3_key:
  s3_secret:
      
  # AWS Market
  aws_silverlining_key:

  # AWS CloudTrail
  trail_key:
  trail_secret:

  # Privacy
  privacy_api_key:
  privacy_api_key_test:

  # Rapyd Collect/Disburse
  rapyd_access_key:
  rapyd_secret_key:
  rapyd_base_url:
  rapyd_webhook_url:
  rapyd_wallet_id:

  # Rapyd Collect/Disburse Test
  rapyd_access_key_test:
  rapyd_secret_key_test:
  rapyd_base_url_test:
  rapyd_webhook_url_test:
  rapyd_wallet_id_test:

  # Rapyd sFTP
  rapyd_ftp_host:
  rapyd_ftp_port:
  rapyd_ftp_user: # e
  rapyd_ftp_key: # e

  # TransferMex BOTM
  botm_url:
  botm_mex_program_id:
  botm_mex_program_account_id:
  botm_mex_user:
  botm_mex_pwd:
  botm_mex_contact:
  botm_mex_wire_user:
  botm_mex_wire_pwd:
  botm_mex_settle_rapyd:
  botm_mex_settle_uniteller:

  # GlobalCollect's account
  gc_api_key: # k
  gc_secret: # k
  gc_endpoint:
  gc_integrator:
  gc_merchant_id:

  gc_webhook_id: # k
  gc_webhook_secret: # k

  # GlobalCollect's sFTP
  gc_ftp_host:
  gc_ftp_username: # k
  gc_ftp_password: # k
  gc_ftp_ssh:
  gc_ftp_production:

  # AlternativePayment's account
  ap_api_key: # k
  ap_api_url:
      
  # Coinflow
  coinflow_api_key: # k
  coinflow_webhook_key: # k
  coinflow_destination: # e
  coinflow_destination_chain:
      
  # Mautic
  mautic_password: #k

  # Plaid Spendr
  plaid_spendr_client_id:
  plaid_spendr_dev_secret:
  plaid_spendr_sandbox_secret:
  plaid_spendr_prod_secret:
  # Spendr Google API Key
  spendr_google_api_key:

  email_url:
  email_token:

  twilio_sid:
  twilio_token: # k
  twilio_from:

  localizejs_api_key:
  localizejs_project_key:

  recaptcha_key: # k
  recaptcha_frontend:

  google_server_key:
  google_frontend_key:
  google_oauth_path:
  google_sheet_currency:

  rsa_key_path:

  backup_pwd:
  jwt_key:

  # AES keys
  aes_key:
  aes_iv:

  # Leaflink csv ftp info
  leaflink_bank_ftp_host:
  leaflink_bank_ftp_port:
  leaflink_bank_ftp_username:
  leaflink_bank_ftp_password:
    
  # UniTeller
  uniteller_base_url:
  uniteller_partner_code:
  uniteller_partner_secret:
  uniteller_partner_admin_secret:

  # Spendr 2.0
  spendr2_url:
  spendr_common_user:

  intermex_base_url:
  intermex_key:
  intermex_partner:
  intermex_ag_sender_code:
  intermex_webhook_secret:
