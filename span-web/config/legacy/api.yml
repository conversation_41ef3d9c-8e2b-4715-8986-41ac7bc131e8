#file: noinspection YAMLSchemaValidation,SwYamlUnresolvedReferencesInspection
nelmio_api_doc:
    models:
        use_jms: false
    areas:
        default:
            path_patterns:
                - ^/api/
    documentation:
        openapi: 3.1.0
        info:
            title: VirtualCards.us API documentation
            description: "<p>This is the VirtualCards.us API Developer-Knowledge Portal.
                    Here you will find what functionality is available to you to integrate your VirtualCards.us account
                    into your current and future business processes and to automate your business transactions.
                    There are APIs for most of what you can do via the consumer portal.</p>
                <p>To help you navigate, this page is split into 3 vertical sections:</p>
                <ul>
                  <li>the navigation menu on the left,</li>
                  <li>the content in the middle,</li>
                  <li>and usage examples on the right.</li>
                </ul>

                <h3>API Security</h3>
                <p>To get started, contact <a href='mailto:<EMAIL>'><EMAIL></a>
                    to get the credentials used to call the APIs. Two credentials will be provided,
                    one for sandbox and another one for production environment.
                    The sandbox environment is useful for development and allows to play with test data.</p>
                <p>When you send an HTTPS request, you must include specified header parameters, including a signature.
                    These headers are required for both the production platform and the sandbox.
                    When you send a request, you calculate the signature and insert the result into
                    the <code>x-signature</code> header. When the platform receives the request,
                    it performs the same signature calculation. If the resulting values do not match,
                    the request is rejected.</p>
                <p>This signing process helps secure requests in the following ways:</p>
                <ul>
                    <li>Verifies that the requester is an authorized user.</li>
                    <li>Protects data from tampering in transit.</li>
                    <li>Rejects requests from unauthorized persons.</li>
                </ul>
                <p>The signature is calculated as a hash of a concatenation of specific strings,
                    according to the following formula:</p>
                <p><strong>signature = BASE64 ( HASH ( url + salt + timestamp + access_key +
                    secret_key + body_string ), secret_key )</strong></p>
                <ul>
                    <li><strong>BASE64</strong> is a Base-64 encoding algorithm.</li>
                    <li><strong>HASH</strong> is the HMAC-SHA256 algorithm. The <code>secret_key</code>
                        we provided needs to be used here to generate the hash. 
                        See <a href='https://github.com/danharper/hmac-examples' target='_blank'>https://github.com/danharper/hmac-examples</a>
                        for the code samples in different programming languages. 
                        Note: we are using the lowercase hexits to encode BASE64 string.</li>
                    <li><strong>url</strong> is the full URL with schema and query string.</li>
                    <li><strong>salt</strong> is a random string with 8-20 digits, letters or special characters.
                        Put it in the HTTP header with name <code>x-salt</code>.</li>
                    <li><strong>timestamp</strong> is the current Unix timestamp of the request.
                        Put it in the HTTP header with name <code>x-timestamp</code>.</li>
                    <li><strong>access_key</strong> is the unique string we provided to you.
                        Put it in the HTTP header with name <code>x-access-key</code>.</li>
                    <li><strong>secret_key</strong> is the unique string we provided to you too.
                        The secret key is like a password, and is transmitted only as part of the calculated signature.
                        Do not share it with your customers or partners, and do not transmit it in plaintext.</li>
                    <li><strong>body_string</strong> is the request body string contains all the other parameters.
                        <ul>
                            <li>If the content type is <code>multipart/form-data</code>,
                                merge all the parameters with the format <code>key1=value1&key2=value2</code>.
                                They need to be merged in the same order as the request.
                                If (text or binary) files are being uploaded, append the <code>md5</code> string of
                                the file content to the end like this:
                                <code>key1=value1&key2=value2&file=md5_of_content</code>.
                                Don't URL-encode any keys and values.</li>
                            <li>Otherwise, just use the request body string which contains everything like URL-encoded
                                parameters, or a JSON string.</li>
                            <li>If the body is empty, this can just be omitted.</li>
                        </ul>
                    </li>
                </ul>

                <p>For legacy users, the <code>x-api-key</code> can still be used to call the APIs,
                    but it's recommended to upgrade to the above new signature way.</p>
                <p>Please submit the IP whitelist to us for your production environment.
                    There is no IP limitation on sandbox environment.</p>

                <h3>Login entrance to get/change API access/secret keys</h3>
                <ul>
                  <li>
                    <a href='https://staging.virtualcards.us/login' target='_blank'>Sandbox environment</a>
                    <div style='font-size:12px;'>By logging in, you agree to the
                      <a href='https://www.virtualcards.us/sandbox-terms-of-service'
                      target='_blank'>Terms & Conditions</a> of Sandbox environment.</div>
                  </li>
                  <li><a href='https://www.virtualcards.us/login' target='_blank'>Production environment</a></li>
                </ul>
                <h3>API Endpoint</h3>
                <ul>
                  <li>Sandbox: <code>https://staging.virtualcards.us/api</code></li>
                  <li>Production: <code>https://www.virtualcards.us/api</code></li>
                </ul>
                <h3>Other Notes</h3
                <p>All the currency amount(includes card balance, etc) is in
                    <code>minor unit</code>(the <code>exponent</code>).
                    See <a href='https://en.wikipedia.org/wiki/ISO_4217'
                    target='_blank'>https://en.wikipedia.org/wiki/ISO_4217</a>.
                    You may need to format it before displaying to your end users.</p>
                <p><strong>Please DO NOT use production environment until you have tested your
                integration sufficiently and are happy that it works as expected.</strong></p>"
            version: 1.0.0
            termsOfService: 'https://www.virtualcards.us/sandbox-terms-of-service'
            contact:
                name: 'Tern Commerce'
                url: 'https://www.virtualcards.us'
                email: <EMAIL>
        servers:
            -
                url: '%host_schema%/%host_path%'
        components:
            responses:
                Response:
                    response: 200
                    description: 'Success response'
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Response'
                ErrorParamResponse:
                    response: 400
                    description: 'Parameter error'
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Response'
                RequireAuthResponse:
                    response: 401
                    description: 'Authentication Required'
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Response'
                AuthFailedResponse:
                    response: 401
                    description: 'Failed to authenticate'
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Response'
                LLCreateResponse:
                    response: 200
                    description: 'Success response'
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/LLCreateResponse'
                LLCreateFailResponse:
                    response: 409
                    description: 'User was created before'
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/LLCreateFailResponse'
                LLBankInfoResponse:
                    response: 200
                    description: 'Success response'
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/LLBankInfoResponse'
                LLBankInfoFailure:
                    response: 409
                    description: 'Internal failure for Get Bank Info'
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/LLBankInfoFailure'
                LLDeleteAccountResponse:
                    response: 200
                    description: 'Success response'
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/LLDeleteAccountResponse'
                LLDeleteAccountFailure:
                    response: 409
                    description: 'Internal failure for Delete Account'
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/LLDeleteAccountFailure'
                LLOnboardingWebhookResponse:
                    response: 202
                    description: 'Onboarding Webhook'
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/LLOnboardingWebhookResponse'
                LLSendResponse:
                    response: 200
                    description: 'Success response'
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/LLSendResponse'
                LLSendFailureResponse:
                    response: 409
                    description: 'Internal failure for Send Payment'
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Response'
                LLPaymentDeleteResponse:
                    response: 200
                    description: 'Success response'
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/LLPaymentDeleteResponse'
                LLPaymentDeleteFailureResponse:
                    response: 409
                    description: 'Internal failure for Delete Payment'
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/LLPaymentDeleteFailureResponse'
                LLGetPaymentResponse:
                    response: 200
                    description: 'Success response'
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/LLGetPaymentResponse'
                LLGetPaymentFailureResponse:
                    response: 409
                    description: 'Internal failure for Get Payment Status'
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/LLGetPaymentFailureResponse'
                LLPaymentHistoryResponse:
                    response: 200
                    description: 'Success response'
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/LLPaymentHistoryResponse'
                LLPaymentHistoryFailureResponse:
                    response: 409
                    description: 'Internal failure for Get Payment History'
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/LLPaymentHistoryFailureResponse'
                CardProgramsResponse:
                    response: 200
                    description: 'Available card programs and cards information'
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CardProgramsResponse'
                ProvinceResponse:
                    response: 200
                    description: 'Available states in the country'
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ProvinceResponse'
                VerifyEmailResponse:
                    response: 200
                    description: 'Registered user information after verifying email'
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/VerifyEmailResponse'
                IdVerificationUploadResponse:
                    response: 200
                    description: 'ID verification ID and reference to track'
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/IdVerificationUploadResponse'
                LoginResponse:
                    response: 200
                    description: 'Login to get token'
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/LoginResponse'
                IdVerificationRetrieveResponse:
                    response: 200
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/IdVerificationRetrieveResponse'
                    description: 'Id Verification Retrieve Response'
                IdVerificationStatusResponse:
                    response: 200
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/IdVerificationStatusResponse'
                    description: 'Id Verification Status Response'
                CardDetailResponse:
                    response: 200
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CardDetailResponse'
                    description: 'Card Detail Response'
                CardProtectedDataResponse:
                    response: 200
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CardProtectedDataResponse'
                    description: 'Card Protected Data Response'
                StatementActivityResponse:
                    response: 200
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/StatementActivityResponse'
                    description: 'Statement Activity Response'
                CardTransactionHistoryResponse:
                    response: 200
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CardTransactionHistoryResponse'
                    description: 'Card Transaction History Response'
                LoadMethodIssuersResponse:
                    response: 200
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/LoadMethodIssuersResponse'
                    description: 'Load Method Issuers Response'
                UserProfileResponse:
                    response: 200
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/UserProfileResponse'
                    description: 'User Profile Response'
                UserSummaryResponse:
                    response: 200
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/UserSummaryResponse'
                    description: 'User Summary Response'
                QuotePaymentResponse:
                    response: 200
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/QuotePaymentResponse'
                    description: 'Quote Payment Response'
                QueryPaymentResponse:
                    response: 200
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/QueryPaymentResponse'
                    description: 'Payment summary like fee and discount details'
                CreatePaymentResponse:
                    response: 200
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CreatePaymentResponse'
                    description: 'Pay details like instruction or redirect URL.'
                PaymentStatusResponse:
                    response: 200
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/PaymentStatusResponse'
                    description: 'Payment Status Response'
                CardListResponse:
                    response: 200
                    description: "User's card list"
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CardListResponse'
                UserReshipperResponse:
                    response: 200
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/UserReshipperResponse'
                    description: 'User Reshipper Response'
                LoadHistoryResponse:
                    response: 200
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/LoadHistoryResponse'
                    description: 'Load History Response'
                EsSoloInstantRegistrationResponse:
                    response: 200
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/EsSoloInstantRegistrationResponse'
                    description: 'Es Solo Instant Registration Response'
            securitySchemes:
                x-access-key:
                    type: apiKey
                    description: 'API access key used to call the API.'
                    name: x-access-key
                    in: header
                x-salt:
                    type: apiKey
                    description: 'Random string used to calculate the `x-signature`. Length: 8-20 digits, letters or special characters.'
                    name: x-salt
                    in: header
                x-timestamp:
                    type: apiKey
                    description: "Unix timestamp. Used to verify the invoker's time. Also used to calculate the `x-signature`."
                    name: x-timestamp
                    in: header
                x-signature:
                    type: apiKey
                    description: "Used to verify the request parameters are not modified before receiving by our system. See this document's description above for the calculation method."
                    name: x-signature
                    in: header
                x-api-key:
                    type: apiKey
                    description: "Deprecated. Used to verify api invoker. Value can has prefix 'Bearer '. Please use the `x-signature` header instead."
                    name: x-api-key
                    in: header
                x-token:
                    type: apiKey
                    description: "Optional. User token. Get it by login API. Value can has prefix 'Bearer '"
                    name: x-token
                    in: header
                x-timezone:
                    type: apiKey
                    description: "Optional. Not security requirement. User's timezone. Optional but highly recommend to add to specify date's timezone in request parameters. Default to be current user's timezone configure or `America/New_York` if `x-token` is not provided or timezone is not configured for the user. Value should be one of the timezone names listed in `https://www.php.net/manual/en/timezones.php` or an offset value (like `+0200`)."
                    name: x-timezone
                    in: header
                x-i18n:
                    type: apiKey
                    description: "Optional. Not security requirement. User's language to translate response message. Optional. Default to `en`. Valid values: `en` for English, `zh` for Chinese(中文（简体）), `es` for Spanish(Español), `pt-BR` for Brazilian Portuguese(Português do Brasil), `ja` for Japanese(にほんご), or `ar` for Arabic(العَرَبِيَّة)."
                    name: x-i18n
                    in: header
                x-sub-program:
                    type: apiKey
                    description: "Optional. It's only relative to FIS related APIs. Specify this to overwrite the default `Subprogram ID` in our system to call partners (FIS, etc.)'s APIs."
                    name: x-sub-program
                    in: header
                x-card-program:
                    type: apiKey
                    description: 'Deprecated. Optional. Not security requirement. Only required in the staging environment. This is the card program id which you are requesting in the staging environment for testing. Refer to the `card programs` API for more details.'
                    name: x-card-program
                    in: header
            schemas:
                Response:
                    type: object
                    properties:
                        success:
                            type: boolean
                            description: 'Operation failed or not'
                        message:
                            type: string
                            description: 'Error or success message'
                        data:
                            type: object
                            description: 'Operation Result'
                    description: Response
                LLCreateResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                            description: 'Was the operation successful?'
                        message:
                            type: string
                            description: 'Error or success message, if one exists.'
                        data:
                            type: object
                            properties:
                                id:
                                    type: string
                                    description: 'Tern internal ID.'
                                bankAccountId:
                                    type: string
                                    description: 'Bank Account ID. Primary identifier for the newly created account.'
                                companyId:
                                    type: string
                                    description: 'ID for company with bank account ownership.'
                                companyName:
                                    type: string
                                    description: 'Name for company with bank account ownership.'
                                email:
                                    type: string
                                    description: 'Usable company email. Required for onboarding.'
                                phone:
                                    type: string
                                    description: 'Phone number for company. Optional.'
                                customerOnboardingStatus:
                                    type: string
                                    description: 'Onboarding status of the newly created user. Default is "Pending"'
                    description: 'LeafLink - Create Response'
                LLCreateFailResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                            description: 'Was the operation successful?'
                        message:
                            type: string
                            description: 'Error Message - This Bank Account ID has already been registered.'
                    description: 'LeafLink - Create Fail Response'
                LLBankInfoResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                            description: 'Was the operation successful?'
                        message:
                            type: string
                            description: 'Error or success message, if one exists.'
                        data:
                            type: object
                            properties:
                                id:
                                    type: string
                                    description: 'Tern internal ID.'
                                bankAccountId:
                                    type: string
                                    description: 'Bank Account ID. Primary identifier for the newly created account.'
                                companyId:
                                    type: string
                                    description: 'ID for company with bank account ownership.'
                                InstitutionName:
                                    type: string
                                    description: 'Name of institution that holds the bank account.'
                                lastFourOfAccountNum:
                                    type: string
                                    description: 'Last four digits of bank account number.'
                                status:
                                    type: string
                                    description: 'Account status. Active or closed.'
                    description: 'LeafLink - Bank Info Response'
                LLBankInfoFailure:
                    type: object
                    properties:
                        success:
                            type: boolean
                            description: 'Was the operation successful?'
                        message:
                            type: string
                            description: 'Error Message - This account does not exist. / No valid banking info on this account.'
                    description: 'LeafLink - Bank Info Failure'
                LLGetAccountsResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                            description: 'Was the operation successful?'
                        message:
                            type: string
                            description: 'Error or success message, if one exists.'
                        data:
                            type: object
                            properties:
                                companyId:
                                    type: string
                                    description: 'ID of company that owns corresponding bank accounts.'
                                bankAccountIds:
                                    type: array
                                    description: 'Array of strings containing Bank Account IDs for all bank accounts with the corresponding Company ID.'
                                    items:
                                        type: object
                                        properties:
                                            bankAccountId:
                                                type: string
                                                description: 'String containing bank account ID.'
                    description: 'LeafLink - Get Accounts Response'
                LLGetAccountsFailure:
                    type: object
                    properties:
                        success:
                            type: boolean
                            description: 'Was the operation successful?'
                        message:
                            type: string
                            description: 'Error Message - This user account contains no bank accounts.'
                    description: 'LeafLink - Get Accounts Failure'
                LLDeleteAccountResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                            description: 'Was the operation successful?'
                        message:
                            type: string
                            description: 'Error or success message, if one exists.'
                        data:
                            type: object
                            properties:
                                bankAccountId:
                                    type: string
                                    description: 'Bank Account ID. Primary identifier for the newly created account.'
                                companyId:
                                    type: string
                                    description: 'ID for company with bank account ownership.'
                    description: 'LeafLink - Delete Account Response'
                LLDeleteAccountFailure:
                    type: object
                    properties:
                        success:
                            type: boolean
                            description: 'Was the operation successful?'
                        message:
                            type: string
                            description: 'Error Message - This Bank Account ID does not exist in our database. / This account has already been closed.'
                    description: 'LeafLink - Delete Account Failure'
                LLOnboardingWebhookResponse:
                    type: object
                    properties:
                        event_id:
                            type: string
                            description: 'A UUID string noting a unique ID for your event.'
                        event_date:
                            type: string
                            description: 'Reflects date the event was created. Does not reflect the date in which the request was sent. Events cannot be posted with a date in the future.'
                        bank_account_id:
                            type: string
                            description: 'A UUID representing the unique LL bank account id.'
                        company_id:
                            type: string
                            description: 'A UUID representing the unique LL company id the bank account belongs to.'
                        institution_name:
                            type: string
                            description: 'The name of the linked financial institution.'
                        account_last_4:
                            type: string
                            description: 'The last 4 digits of the linked bank account.'
                        onboarding_status:
                            type: string
                            description: 'Linked or error.'
                        onboarding_status_message:
                            type: string
                            description: 'Context of onboarding_status. If onboarding_status is error, include any relevant error code or message.'
                    description: 'LeafLink - Onboarding Webhook Response'
                LLSendResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                            description: 'Was the operation successful?'
                        message:
                            type: string
                            description: 'Error or success message, if one exists.'
                        invoiceBatch:
                            type: array
                            items:
                                $ref: '#/components/schemas/LLSendResponseArray'
                    description: 'LeafLink - Send Response'
                LLSendResponseArray:
                    type: object
                    properties:
                        id:
                            type: integer
                            description: 'Tern unique transaction ID.'
                        bankAccountId:
                            type: string
                            description: 'Bank Account ID. Primary identifier for the newly created account.'
                        txnId:
                            type: string
                            description: 'ID for company with bank account ownership.'
                        tranType:
                            type: string
                            description: 'Transaction Type - Either credit or debit.'
                        tranStatus:
                            type: string
                            description: 'Transaction Status - Should be sent.'
                        txnAmount:
                            type: string
                            description: 'Transaction amount in cents.'
                    description: 'LeafLink - Send Response Array'
                LLSendResponseFailure:
                    type: object
                    properties:
                        success:
                            type: boolean
                            description: 'Was the operation successful?'
                        message:
                            type: string
                            description: 'Error Message - Unknown account / No valid banking info on this account.'
                    description: 'LeafLink - Send Response Failure'
                LLPaymentDeleteResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                            description: 'Was the operation successful?'
                    description: 'LeafLink - Payment Delete Response'
                LLPaymentDeleteFailureResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                            description: 'Was the operation successful?'
                        message:
                            type: string
                            description: 'Error Message - Unknown Account! / Unknown User! / Unknown Transaction!'
                    description: 'LeafLink - Payment Delete Failure Response'
                LLGetPaymentResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                            description: 'Was the operation successful?'
                        message:
                            type: string
                            description: 'Error or success message, if one exists.'
                        data:
                            type: object
                            properties:
                                id:
                                    type: integer
                                    description: 'Tern unique transaction ID.'
                                bankAccountId:
                                    type: string
                                    description: 'Bank Account ID. Primary identifier for the newly created account.'
                                txnId:
                                    type: string
                                    description: 'ID for company with bank account ownership.'
                                tranType:
                                    type: string
                                    description: 'Transaction Type - Either credit or debit.'
                                tranStatus:
                                    type: string
                                    description: 'Transaction Status - Sent, Deleted, Processing, Returned, and Settled.'
                                txnAmount:
                                    type: string
                                    description: 'Transaction amount in cents.'
                    description: 'LeafLink - Get Payment Response'
                LLGetPaymentFailureResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                            description: 'Was the operation successful?'
                        message:
                            type: string
                            description: 'Error Message - Unknown Account! / Unknown User! / Unknown Transaction!'
                    description: 'LeafLink - Get Payment Failure Response'
                LLPaymentHistoryResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                            description: 'Was the operation successful?'
                        message:
                            type: string
                            description: 'Error or success message, if one exists.'
                        data:
                            type: object
                            properties:
                                bankAccountId:
                                    type: string
                                    description: 'Bank Account ID. Primary identifier for the newly created account.'
                                transactions:
                                    type: array
                                    items:
                                        $ref: '#/components/schemas/LLPaymentHistoryArray'
                    description: 'LeafLink - Payment History Response'
                LLPaymentHistoryArray:
                    type: object
                    properties:
                        txnId:
                            type: string
                            description: 'ID for company with bank account ownership.'
                        tranStatus:
                            type: string
                            description: 'Transaction Status - Sent, Deleted, Processing, Returned, and Settled.'
                        createdAt:
                            type: string
                            description: 'Timestamp of transaction creation.'
                    description: 'LeafLink - Payment History Array'
                LLPaymentHistoryFailureResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                            description: 'Was the operation successful?'
                        message:
                            type: string
                            description: 'Error Message - Unknown Account!'
                    description: 'LeafLink - Payment History Failure Response'
                CardProgramsResponse:
                    type: object
                    description: 'Available card programs and related data. Save it so that you can call other APIs later.'
                    properties:
                        success:
                            type: boolean
                        message:
                            type: string
                        data:
                            type: array
                            items:
                                $ref: '#/components/schemas/CardProgram'
                ProvinceResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                        message:
                            type: string
                        data:
                            type: array
                            items:
                                $ref: '#/components/schemas/Province'
                    description: 'Province Response'
                VerifyEmailResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                        message:
                            type: string
                        data:
                            type: object
                            properties:
                                userId:
                                    type: integer
                                    description: 'User ID. Save it so that you can call other APIs later.'
                                token:
                                    type: string
                                    description: 'User token. Add it to HTTP header with name "x-token" for future API request.'
                                userCardId:
                                    type: integer
                                    description: 'User Card ID. Save it so that you can call other APIs later.'
                    description: 'Verify Email Response'
                IdVerificationUploadResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                        message:
                            type: string
                        data:
                            type: object
                            description: 'response detail'
                            properties:
                                id:
                                    type: integer
                                    description: 'Id verification ID. Save it so that you can call other APIs later.'
                                reference:
                                    type: string
                                    description: 'Reference used to track verify status and result. Save it so that you can call retrieve API later.'
                    description: 'Id Verification Upload Response'
                IdVerificationRetrieveResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                        message:
                            type: string
                        data:
                            type: object
                            properties:
                                processed:
                                    type: boolean
                                    description: "If verification is finished. Recall retrieve API later if it's false"
                                number:
                                    type: string
                                    description: 'Last 4 characters of the recognized ID number'
                                requireIssueDate:
                                    type: boolean
                                    description: 'Processed, but issue date is required to finish the id verification'
                                requireExpiryDate:
                                    type: boolean
                                    description: 'Processed, but expiry date is required to finish the id verification'
                    description: 'Id Verification Retrieve Response'
                IdVerificationStatusResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                        message:
                            type: string
                        data:
                            type: object
                            properties:
                                id:
                                    type: integer
                                type:
                                    type: string
                                    description: 'Id verification type, for instance, DL - Driver License'
                                issueAt:
                                    type: string
                                    description: 'The issue date (ISO 8601 format) recognized from uploaded image'
                                expireAt:
                                    type: string
                                    description: 'The expiry date (ISO 8601 format) recognized from uploaded image'
                                bornAt:
                                    type: string
                                    description: 'The born date (ISO 8601 format) recognized from uploaded image'
                                status:
                                    type: string
                                    description: "null, 'accepted', 'incomplete', 'invalid' or 'expired'"
                                number:
                                    type: string
                                    description: 'ID number'
                                reason:
                                    type: string
                                    description: "Error message if the status is 'invalid'"
                    description: 'Id Verification Status Response'
                LoginResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                        message:
                            type: string
                        data:
                            type: object
                            properties:
                                token:
                                    type: string
                                    description: 'User token. Add it to HTTP header with name "x-token" for future API request.'
                    description: 'Login Response'
                QueryPaymentResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                        message:
                            type: string
                        data:
                            type: object
                            properties:
                                amountText:
                                    type: string
                                pendingExceed:
                                    type: string
                                    nullable: true
                                    description: "If it's not null, the value is the max balance of card, which means `user's all amounts in pending loads` + `his card balance` + `this load amount` has exceed max card balance limit. Should alert user that the amount exceeding this limit won't be loaded."
                                methods:
                                    type: array
                                    items:
                                        $ref: '#/components/schemas/QueryPaymentMethod'
                    description: 'Query Payment Response'
                QueryPaymentMethod:
                    type: object
                    description: 'Load method, partner, and pay amount details'
                    properties:
                        id:
                            type: integer
                            description: 'Load method id'
                        name:
                            type: string
                            description: 'Load method name'
                        artwork:
                            type: string
                            description: 'Load method image URL'
                        partner:
                            type: object
                            properties:
                                id:
                                    type: integer
                                name:
                                    type: string
                        loadFee:
                            type: string
                            description: 'Formatted load fee detail'
                        payAmounts:
                            type: array
                            items:
                                $ref: '#/components/schemas/Coin'
                QuotePaymentResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                        message:
                            type: string
                        data:
                            type: object
                            properties:
                                id:
                                    type: integer
                                    description: 'Load Id. Save it so that you can call other APIs later.'
                                partner:
                                    type: string
                                    description: 'Load partner name'
                                method:
                                    type: string
                                    description: 'Load method name'
                                currency:
                                    type: string
                                    description: "User card's currency"
                                loadAmount:
                                    $ref: '#/components/schemas/Coin'
                                    description: 'Initial load amount'
                                loadFee:
                                    $ref: '#/components/schemas/Coin'
                                    description: 'Load fee that should charge'
                                membershipFee:
                                    $ref: '#/components/schemas/Coin'
                                    description: 'Membership fee that should charge'
                                replacementFee:
                                    $ref: '#/components/schemas/Coin'
                                    description: 'Replacement fee that should charge'
                                otherFees:
                                    type: array
                                    items:
                                        $ref: '#/components/schemas/QuotePaymentOtherFee'
                                totalAmount:
                                    $ref: '#/components/schemas/Coin'
                                    description: 'Total amount of the payment. `totalAmount` = `loadAmount` + `loadFee` + `membershipFee` + `replacementFee` + `otherFees`'
                                loadFeeDiscount:
                                    type: string
                                    description: 'Load fee discount'
                                membershipFeeDiscount:
                                    type: string
                                    description: 'Membership fee discount'
                                payAmount:
                                    $ref: '#/components/schemas/Coin'
                                    description: 'Total amount that user should pay. `payAmount` = `totalAmount` - `loadFeeDiscount` - `membershipFeeDiscount`'
                    description: 'Quote Payment Response'
                QuotePaymentOtherFee:
                    type: object
                    description: 'Other fees that charged directly in `payAmount`'
                    properties:
                        type:
                            type: string
                            description: 'Other fee type'
                        amount:
                            type: string
                            description: 'Other fee amount'
                        currency:
                            type: string
                            description: 'ISO currency code'
                        coin:
                            $ref: '#/components/schemas/Coin'
                CreatePaymentResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                        message:
                            type: string
                        data:
                            type: object
                            properties:
                                redirect:
                                    type: string
                                    description: 'Redirect url for some payment methods that user need to navigate to continue the payment'
                                instruction:
                                    type: array
                                    items:
                                        $ref: '#/components/schemas/CreatePaymentInstruction'
                    description: 'Create Payment Response'
                CreatePaymentInstruction:
                    type: object
                    description: 'Instruction of payment details for some load methods that user need to perform the payment'
                    properties:
                        key:
                            type: string
                        value:
                            type: string
                PaymentStatusResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                        message:
                            type: string
                        data:
                            type: object
                            properties:
                                status:
                                    type: string
                                transactionNo:
                                    type: string
                    description: 'Payment Status Response'
                CardDetailResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                        message:
                            type: string
                        data:
                            $ref: '#/components/schemas/UserCard'
                    description: 'Card Detail Response'
                CardProtectedDataResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                        message:
                            type: string
                        data:
                            $ref: '#/components/schemas/UserCardProtectedData'
                    description: 'Card Protected Data Response'
                UserSummaryResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                        message:
                            type: string
                        data:
                            type: object
                            properties:
                                coin:
                                    $ref: '#/components/schemas/Coin'
                                    description: 'Balance with currency details'
                                balance:
                                    type: integer
                                currency:
                                    type: string
                                activeCard:
                                    type: integer
                                    description: 'Active user card id. 0 means no active card.'
                                lastTransaction:
                                    nullable: true
                                    $ref: '#/components/schemas/UserCardTransaction'
                    description: 'User Summary Response'
                CardListResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                        message:
                            type: string
                        data:
                            type: array
                            items:
                                $ref: '#/components/schemas/UserCard'
                    description: 'Card List Response'
                UserCard:
                    type: object
                    properties:
                        id:
                            type: integer
                        card:
                            $ref: '#/components/schemas/CardProgramCardType'
                        hash:
                            type: string
                        pan:
                            type: string
                            nullable: true
                        holder:
                            type: string
                            nullable: true
                        balance:
                            type: integer
                            nullable: true
                        currency:
                            type: string
                        coin:
                            $ref: '#/components/schemas/Coin'
                        status:
                            type: string
                            description: '`inactive` status also means `closed`'
                            enum:
                                - active
                                - inactive
                        issued:
                            type: boolean
                        lastTransaction:
                            nullable: true
                            $ref: '#/components/schemas/UserCardTransaction'
                    description: 'User Card'
                UserCardProtectedData:
                    type: object
                    properties:
                        id:
                            type: integer
                        number:
                            type: string
                            description: 'Full card number'
                        expireAt:
                            type: string
                            format: date
                        cvc:
                            type: string
                    description: 'User Card Protected Data'
                StatementActivityResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                        message:
                            type: string
                        data:
                            type: object
                            properties:
                                date:
                                    type: string
                                    description: 'ISO-8601 date format'
                                description:
                                    type: string
                                amount:
                                    type: string
                                currency:
                                    type: string
                                merchant:
                                    type: string
                                mcc:
                                    type: string
                                type:
                                    type: string
                                balance:
                                    type: string
                    description: 'Statement Activity Response'
                CardTransactionHistoryResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                        message:
                            type: string
                        data:
                            type: array
                            items:
                                $ref: '#/components/schemas/UserCardTransaction'
                    description: 'Card Transaction History Response'
                LoadMethodIssuersResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                        message:
                            type: string
                        data:
                            type: array
                            items:
                                $ref: '#/components/schemas/LoadMethodIssuer'
                    description: 'Load Method Issuers Response'
                CardProgram:
                    type: object
                    properties:
                        id:
                            type: integer
                        name:
                            type: string
                        supportDDA:
                            type: boolean
                        supportExpense:
                            type: boolean
                        cardsAllowed:
                            type: integer
                            description: 'The max cards user can hold. `0` means no limit.'
                        bin:
                            type: string
                            description: '6-8 digit BIN of a card'
                        deliveryMethod:
                            type: string
                            description: 'hand = Hand to customer directly (instant issue), while mail = Mail to customer (requires shipping)'
                        kycProviderProgram:
                            type: string
                            description: 'Program Id of the KYC Provider if required'
                        cards:
                            type: array
                            items:
                                $ref: '#/components/schemas/CardProgramCardType'
                        countries:
                            type: array
                            items:
                                $ref: '#/components/schemas/CardProgramCountry'
                        reshippers:
                            type: array
                            items:
                                $ref: '#/components/schemas/CardProgramReshipper'
                    description: 'Card Program'
                CardProgramCardType:
                    type: object
                    properties:
                        id:
                            type: integer
                        name:
                            type: string
                        description:
                            type: string
                        benefit:
                            type: string
                        minLoadAmount:
                            type: number
                            format: float
                            description: 'Minimum load amount in USD cents'
                        maxLoadAmount:
                            type: number
                            format: float
                            description: 'Maximum load amount in USD cents'
                        maxBalance:
                            type: number
                            format: float
                            description: 'Minimum card balance in USD cents'
                        currencies:
                            type: array
                            items:
                                type: string
                        artwork:
                            type: string
                            description: "Card's image url"
                        network:
                            type: string
                            description: 'Visa or MasterCard'
                            enum:
                                - visa
                                - master
                    description: 'Card Program Card Type'
                CardProgramCountry:
                    type: object
                    properties:
                        country:
                            $ref: '#/components/schemas/Country'
                        idVerifications:
                            type: array
                            description: 'Id verification requirements'
                            items:
                                $ref: '#/components/schemas/IdVerificationRequirement'
                        loadPartners:
                            type: array
                            description: 'Available load partners in this card program'
                            items:
                                $ref: '#/components/schemas/LoadPartner'
                    description: 'Card Program Country'
                CardProgramReshipper:
                    type: object
                    properties:
                        reshipper:
                            $ref: '#/components/schemas/Reshipper'
                        addresses:
                            type: array
                            items:
                                $ref: '#/components/schemas/Address'
                    description: 'Card Program Reshipper'
                Country:
                    type: object
                    properties:
                        id:
                            type: integer
                        name:
                            type: string
                        iso:
                            type: string
                            description: 'ISO code (2 characters), for instance, US'
                        iso3:
                            type: string
                            description: 'ISO code (3 characters), for instance, USA'
                        phoneCode:
                            type: string
                        region:
                            type: string
                            enum:
                                - EMEA
                                - AMERICAS
                                - APAC
                                - ''
                    description: Country
                Reshipper:
                    type: object
                    properties:
                        id:
                            type: integer
                        name:
                            type: string
                            description: '`Custom` means this card program support custom address'
                        address:
                            type: string
                        dba:
                            type: string
                        autoSignup:
                            type: boolean
                            description: '`true` means this reshipper also support auto sign up'
                    description: Reshipper
                IdVerificationRequirement:
                    type: object
                    properties:
                        type:
                            type: string
                            enum:
                                - PSP
                                - ID
                                - DL
                        side:
                            type: string
                            enum:
                                - BOTH
                                - FRONT
                                - BACK
                    description: 'Id Verification Requirement'
                LoadPartner:
                    type: object
                    properties:
                        id:
                            type: integer
                        name:
                            type: string
                        methods:
                            type: array
                            items:
                                $ref: '#/components/schemas/LoadMethod'
                    description: 'Load Partner'
                LoadMethod:
                    type: object
                    properties:
                        id:
                            type: integer
                        name:
                            type: string
                        icon:
                            type: string
                            description: "Load method's logo image"
                        currencies:
                            type: array
                            description: 'The currency list this load method support. ISO currency code.'
                            items:
                                type: string
                    description: 'Load Method'
                Address:
                    type: object
                    properties:
                        id:
                            type: integer
                        name:
                            type: string
                        default:
                            type: string
                        primary:
                            type: string
                            description: 'Same as `default`'
                        second:
                            type: string
                            nullable: true
                        phone:
                            type: string
                        country:
                            type: string
                        state:
                            type: string
                        city:
                            type: string
                        zipCode:
                            type: string
                    description: Address
                State:
                    type: object
                    properties:
                        id:
                            type: integer
                        name:
                            type: string
                        abbr:
                            type: string
                    description: State
                Province:
                    type: object
                    properties:
                        id:
                            type: integer
                        name:
                            type: string
                        abbr:
                            type: string
                    description: Province
                UserCardTransaction:
                    type: object
                    properties:
                        id:
                            type: integer
                        tranId:
                            type: string
                            description: 'Internal transaction ID'
                        amount:
                            type: integer
                        currency:
                            type: string
                        coin:
                            $ref: '#/components/schemas/Coin'
                            description: 'Transaction amount with currency detail'
                        balance:
                            $ref: '#/components/schemas/Coin'
                            description: 'User card balance after the transaction'
                        time:
                            type: string
                            format: date
                        merchant:
                            $ref: '#/components/schemas/Merchant'
                            nullable: true
                        type:
                            type: string
                            enum:
                                - Credet
                                - Debit
                        description:
                            type: string
                        order:
                            type: integer
                            description: 'Transaction order of the user card. Start from 1. Only count for POS purchase(00040)'
                            nullable: true
                    description: 'User Card Transaction'
                Merchant:
                    type: object
                    properties:
                        id:
                            type: integer
                        name:
                            type: string
                        type:
                            type: string
                        category:
                            type: string
                            description: "merchant type's category"
                        mcc:
                            type: string
                        list:
                            type: string
                            nullable: true
                            enum:
                                - White
                                - Grey
                                - Restricted
                    description: Merchant
                LoadMethodIssuer:
                    type: object
                    properties:
                        id:
                            type: integer
                        name:
                            type: string
                    description: 'Load Method Issuer'
                User:
                    type: object
                    properties:
                        id:
                            type: integer
                        firstName:
                            type: string
                        lastName:
                            type: string
                        fullName:
                            type: string
                        username:
                            type: string
                        email:
                            type: string
                        gender:
                            type: string
                            enum:
                                - Male
                                - Female
                                - 'null'
                        locked:
                            type: string
                            enum:
                                - Unlock
                                - Lock
                        avatar:
                            type: string
                            description: 'URL to the avatar'
                            nullable: true
                        birthday:
                            type: string
                            format: date
                        address:
                            type: string
                        address2:
                            type: string
                            nullable: true
                        country:
                            $ref: '#/components/schemas/Country'
                            nullable: true
                        state:
                            $ref: '#/components/schemas/State'
                            nullable: true
                        status:
                            type: string
                            enum:
                                - active
                                - inactive
                                - closed
                                - banned
                                - under_review
                        city:
                            type: string
                            nullable: true
                        zip:
                            type: string
                            nullable: true
                        homePhone:
                            type: string
                            nullable: true
                        mobilePhone:
                            type: string
                            nullable: true
                        workPhone:
                            type: string
                            nullable: true
                        vip:
                            type: boolean
                        flags:
                            type: array
                            items:
                                type: string
                    description: User
                UserProfileResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                        message:
                            type: string
                        data:
                            $ref: '#/components/schemas/User'
                    description: 'User Profile Response'
                StringResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                        message:
                            type: string
                        data:
                            type: string
                    description: 'String Response'
                NumberResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                        message:
                            type: string
                        data:
                            type: integer
                    description: 'Number Response'
                CoinResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                        message:
                            type: string
                        data:
                            $ref: '#/components/schemas/Coin'
                    description: 'Coin Response'
                Coin:
                    type: object
                    properties:
                        amount:
                            type: integer
                            description: 'Minor unit of the currency'
                        currency:
                            type: string
                            description: 'ISO currency code'
                        formatted:
                            type: string
                            description: 'Formatted amount text in primary unit'
                        symbol:
                            type: 'string,'
                            description: 'Currency symbol'
                    description: Coin
                UserReshipperResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                        message:
                            type: string
                        data:
                            type: object
                            properties:
                                reshipper:
                                    $ref: '#/components/schemas/Reshipper'
                                address:
                                    $ref: '#/components/schemas/Address'
                                custom:
                                    type: string
                                    description: "User's (mailbox) id in the reshipper"
                    description: 'User Reshipper Response'
                LoadHistoryResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                        message:
                            type: string
                        data:
                            type: array
                            items:
                                $ref: '#/components/schemas/UserCardLoad'
                    description: 'Load History Response'
                UserCardLoad:
                    type: object
                    properties:
                        id:
                            type: integer
                        partner:
                            type: string
                            description: 'Load partner name'
                        method:
                            type: string
                            description: 'Load method name'
                        transactionId:
                            type: string
                            description: 'Transaction ID'
                        totalAmount:
                            $ref: '#/components/schemas/Coin'
                        totalCost:
                            $ref: '#/components/schemas/Coin'
                        totalReceived:
                            $ref: '#/components/schemas/Coin'
                        loadAmount:
                            $ref: '#/components/schemas/Coin'
                        loadFee:
                            $ref: '#/components/schemas/Coin'
                        membershipFee:
                            $ref: '#/components/schemas/Coin'
                        replacementFee:
                            $ref: '#/components/schemas/Coin'
                        discount:
                            $ref: '#/components/schemas/Coin'
                        createdAt:
                            type: string
                            format: date
                        loadAt:
                            type: string
                            format: date
                        order:
                            type: integer
                            description: 'Load order'
                    description: 'User Card Load'
                EsSoloInstantRegistrationResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                            description: 'True or False if Registration was successful. <br/>(see message field for human readable detailed response)'
                        message:
                            type: string
                            description: 'Human readable success or error message depending on if the success field (above at the top of this section) = true or false.'
                        data:
                            type: object
                            properties:
                                registrationRequestId:
                                    type: integer
                                    description: 'Unique ID for the request, from the Programt Owner/Brand Partner, used to enforce idempotency in the Registration API.'
                                submissonDateTime:
                                    type: date
                                    description: 'Original Date of Submission, unique to first presentation of requestID  for registration of a card, for card program.'
                                programOwnerId:
                                    type: string
                                    description: 'ID of associated Program Owner/Brand Partner'
                                programOwnerName:
                                    type: string
                                    description: 'Name of associated Program Owner/Brand Partner'
                                nestorProgramId:
                                    type: integer
                                    description: 'Nestor Card Program ID - Use this to lookup the actual Platform Program ID'
                                programName:
                                    type: string
                                    description: 'Program Name (derived by lookup from hash)'
                                issuingBankId:
                                    type: integer
                                    description: 'Issuing Bank ID associated with Card Program'
                                issuingBankName:
                                    type: string
                                    description: 'Issuing Bank Name associated with Card Program'
                                cardProcessor:
                                    type: string
                                    description: 'Processor Name associated with the Card Program'
                                isRegistered:
                                    type: boolean
                                    description: 'True or False<br/><br/>If True, you should expect kycPassed to also be True and the entire registration process to be complete.<br/><br/>If False, check to see if kycPassed = True. If so -  and the requestID is same from a prior call from API Invoker - Try to register the card.<br/><br/>If success = done.<br/><br/>If error = show the reason for error in message and ask them to try again.'
                                registrationDateTime:
                                    type: date
                                    nullable: true
                                    description: 'Date of successful registration (if applicable)<br/><br/>Will be NULL if isRegistered = False'
                                userCardId:
                                    type: integer
                                    nullable: true
                                    description: 'Platform Card ID (if registration was successful)'
                                cardRegistrationKey:
                                    type: string
                                    nullable: true
                                    description: 'Hash value derived from card import process.<br/><br/>Will be null if hash could not be found.'
                                cardCreationDate:
                                    type: date
                                    nullable: true
                                    description: 'Date/Time of card creation<br/><br/>Will be NULL if kycPassed or isRegisterd = False'
                                cardStatus:
                                    type: string
                                    nullable: true
                                    description: 'Card Status Name<br/><br/>Will be NULL if kycPassed or isRegisterd = False'
                                cardCurrency:
                                    type: string
                                    nullable: true
                                    description: 'Card Currency<br/><br/>Will be NULL if kycPassed or isRegisterd = False'
                                bin:
                                    type: integer
                                    nullable: true
                                    description: '6-8 digit BIN of a card'
                                cardProcessorUniqueId:
                                    type: integer
                                    nullable: true
                                    description: 'An example of the unique id’s used to correlate to a given card, for a registered cardholder, per individual processors supported (connectivity wise) today are:<br/><ul><li>GTP = Customer ID</li><li>NI = Client ID</li></ul><br/>Can be NULL in the event kycPassed = False and requiresShipping = True'
                                cardShippingMethod:
                                    type: integer
                                    nullable: true
                                    description: '1 = standard, 2 = priority, 3 = rush delivery, 4 = registered mail [default = 1/standard]'
                                shippingAddress1:
                                    type: string
                                    description: 'The place that the card will be shipped. This will only be required on shipped programs.'
                                shippingAddress2:
                                    type: string
                                    description: 'Shipping Address Line 2'
                                shippingCountryId:
                                    type: string
                                    description: 'ISO country code, 3 characters'
                                shippingCountry:
                                    type: string
                                    description: 'Shipping Country Name'
                                shippingStateProvinceId:
                                    type: string
                                    description: 'Shipping State/Province Id'
                                shippingCity:
                                    type: string
                                    description: 'Shipping City Name'
                                shippingPostalCode:
                                    type: string
                                    description: 'Shipping Postal Code'
                                userID:
                                    type: integer
                                    description: 'Platform User ID'
                                userToken:
                                    type: string
                                    description: 'Platform User Token which is required to call some other APIs'
                                kycPassed:
                                    type: boolean
                                    description: 'True or False<br/><br/>If False - you should expect to see the ‘success’ value (above) also to be False.<br/><br/>If True - you should expect to see the ‘success’ value above to also be True (if the card was successfully registered)'
                                failedKycAttempts:
                                    type: integer
                                    description: 'Count up all the individual failed attempts here for this particular requestID. Default value = 0 - unless events are recorded.'
                                kycApprovedDate:
                                    type: date
                                    nullable: true
                                    description: 'Date/Time of approval (if kycPassed = True)'
                                firstName:
                                    type: string
                                    description: 'First name'
                                lastName:
                                    type: string
                                    description: 'Last name'
                                birthday:
                                    type: 'string (date)'
                                    description: 'Date of birth. Format is YYYY-MM-DD'
                                gender:
                                    type: string
                                    description: 'Gender, Male or Female'
                                emailAddress:
                                    type: 'string (email)'
                                    description: 'Email address'
                                residenceAddress:
                                    type: string
                                    description: 'First address line'
                                residenceAddress2:
                                    type: string
                                    nullable: true
                                    description: 'Second address line'
                                residenceCountryId:
                                    type: string
                                    description: 'ISO country code, 3 characters'
                                residenceCountryName:
                                    type: string
                                    description: 'Name of Country'
                                residenceStateProvinceId:
                                    type: integer
                                    description: 'State Id'
                                residenceCity:
                                    type: string
                                    description: 'City name'
                                residencePostalCode:
                                    type: string
                                    description: 'Zip/Postal code'
                                homePhone:
                                    type: string
                                    description: 'Home phone'
                                mobilephone:
                                    type: string
                                    description: 'Mobile phone'
                                workphone:
                                    type: string
                                    nullable: true
                                    description: 'Work phone'
                                proofAddressType:
                                    type: string
                                    description: "Proof type of your address. Available values are 1 - Utility Bill, 2 - Driver's License, 3 - Passport, 4 - National ID, 5 - Property Tax Receipt, 6 - Lease Agreement, 7 - Insurance Card, 8 - Landline Phone Statement, 9 - Bank Statement, 10 - Other"
                                proofOfAddress:
                                    type: string
                                    description: 'VALUE  =  passed or failed'
                                idTypeId:
                                    type: string
                                    description: "Available values are 1 - Driver's License, 2 - National ID, 3 - Passport, 4 - Social Security Card, 5 - Birth Certificate"
                                idUpload:
                                    type: string
                                    description: 'VALUE  =  passed or failed'
                                ssn:
                                    type: string
                                    nullable: true
                                    description: '(Required if you are in the United States)<br/><br/>Mask all but the first character and last 4 digits in response'
                                driversLicenseNo:
                                    type: string
                                    nullable: true
                                    description: '(Required if IDTypeID is 1)<br/><br/>Mask all but the first character and last 4 digits in response'
                                passportNumber:
                                    type: string
                                    nullable: true
                                    description: '(Required if IDTypeID is 3)<br/><br/>Mask all but the first character and last 4 digits in response'
                                countryOfPassport:
                                    type: string
                                    nullable: true
                                    description: '(Required if IDTypeID is 3)'
                                nationalId:
                                    type: string
                                    nullable: true
                                    description: '(Required if IDTypeID is other than 1 and 3)<br/><br/>Mask all but the first character and last 4 digits in response'
                                lastErrorMessage:
                                    type: string
                                    nullable: true
                                    description: 'Show the last message presented if different than current (one below). <br/><br/>This will help the user troubleshoot any KYC and registration related process gaps.<br/><br/>Do not show this field on first submission attempt of a requestID'
                    description: 'Es Solo Instant Registration Response'
                FaasUserResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                        message:
                            type: string
                        data:
                            $ref: '#/components/schemas/FaasUser'
                    description: 'FaaS - User Response'
                FaasUser:
                    type: object
                    properties:
                        id:
                            type: integer
                        email:
                            type: string
                        firstName:
                            type: string
                        lastName:
                            type: string
                        phone:
                            type: string
                        country:
                            type: string
                        province:
                            type: string
                        city:
                            type: string
                        zip:
                            type: string
                        address:
                            type: string
                        address2:
                            type: string
                        status:
                            type: string
                        gender:
                            type: string
                        birthday:
                            type: string
                        registerStep:
                            type: string
                        accountNumber:
                            type: string
                        enrolled:
                            type: integer
                            description: '1 = enrolled, 0 = not enrolled'
                        currency:
                            type: string
                        kycStatus:
                            $ref: '#/components/schemas/FaasKycStatus'
                    description: 'FaaS - User'
                FaasKycStatus:
                    type: object
                    properties:
                        OFAC:
                            type: boolean
                            description: '`null` if pending to check, a boolean `true` if passed, and a string with the error details if failed'
                        'ID Scan':
                            type: boolean
                            description: '`null` if pending to check, a boolean `true` if passed, and a string with the error details if failed'
                    description: 'FaaS - Kyc Status'
                FaasUserWidgetTokenResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                        message:
                            type: string
                        data:
                            $ref: '#/components/schemas/FaasUserWidgetToken'
                    description: 'FaaS - User Widget Token Response'
                FaasUserWidgetToken:
                    type: object
                    properties:
                        token:
                            type: string
                        expireAt:
                            type: string
                            description: 'The token will expire after this time. Remember to call the get widget token API before this time and update your iframe with the new token.'
                        urls:
                            $ref: '#/components/schemas/FaasUserWidgetUrls'
                    description: 'FaaS - User Widget Token'
                FaasUserWidgetUrls:
                    type: object
                    properties:
                        card_issuing:
                            $ref: '#/components/schemas/FaasUserWidgetUrl'
                    description: 'FaaS - User Widget Urls'
                FaasUserWidgetUrl:
                    type: object
                    properties:
                        name:
                            type: string
                        description:
                            type: string
                        url:
                            type: string
                    description: 'FaaS - User Widget Url'
                FaasUserCardResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                        message:
                            type: string
                        data:
                            $ref: '#/components/schemas/FaasUserCard'
                    description: 'FaaS - User Card Response'
                FaasUserCard:
                    type: object
                    properties:
                        id:
                            type: string
                        accountNumber:
                            type: string
                        maskedCardNumber:
                            type: string
                        balance:
                            type: integer
                            description: 'Minor unit. For instance, cents in USD. It may be the local cached balance. Please call the `Account Balance` API to get the accurate realtime balance from the processor.'
                        status:
                            type: string
                            description: 'Available values: `pending_to_enroll`: not enrolled yet, `active`: active and enrolled, `inactive`, `closed`'
                        nativeStatus:
                            type: string
                            description: "Card's native status on the processor side. For instance, `Active`, `Stolen`, `Lost`, `SuspectedFraud`, `Suspended`, etc."
                    description: 'FaaS - User Card'
                FaasUserCardBalanceResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                        message:
                            type: string
                        data:
                            type: integer
                            description: 'Minor unit. For instance, cents in USD'
                    description: 'FaaS - User Card Balance Response'
                FaasUserCardConsumerDetailsResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                        message:
                            type: string
                        data:
                            $ref: '#/components/schemas/FaasUserCardConsumerDetails'
                    description: 'FaaS - User Card Consumer Details Response'
                FaasUserCardConsumerDetails:
                    type: object
                    properties:
                        accountNumber:
                            type: integer
                        availableBalance:
                            type: integer
                        currentBalance:
                            type: integer
                        creationDate:
                            type: string
                        status:
                            type: string
                        firstName:
                            type: string
                        lastName:
                            type: string
                        dateOfBirth:
                            type: string
                        maskedSSN:
                            type: string
                        email:
                            type: string
                        contactNumber:
                            type: string
                        address1:
                            type: string
                        address2:
                            type: string
                        city:
                            type: string
                        state:
                            type: string
                        zipCode:
                            type: string
                        country:
                            type: string
                    description: 'FaaS - User Card Consumer Details'
                FaasUserCardCardDetailsResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                        message:
                            type: string
                        data:
                            type: array
                            items:
                                $ref: '#/components/schemas/FaasUserCardCardDetails'
                    description: 'FaaS - User Card Card Details Response'
                FaasUserCardCardDetails:
                    type: object
                    properties:
                        accountNumber:
                            type: integer
                        availableBalance:
                            type: integer
                        currentBalance:
                            type: integer
                        creationDate:
                            type: string
                        status:
                            type: string
                        maskedCardNumber:
                            type: string
                    description: 'FaaS - User Card Card Details'
                FaasUserCardTransactionsResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                        message:
                            type: string
                        data:
                            type: array
                            items:
                                $ref: '#/components/schemas/FaasUserCardTransaction'
                    description: 'FaaS - User Card Transactions Response'
                FaasUserCardTransaction:
                    type: object
                    properties:
                        id:
                            type: integer
                        amount:
                            type: integer
                        time:
                            type: string
                        description:
                            type: string
                        code:
                            type: string
                            description: '`DEBIT` or `CREDIT`'
                        type:
                            type: string
                            description: "Processor's transaction type"
                        result:
                            type: string
                            description: 'Transaction result or decline reason'
                        merchant:
                            $ref: '#/components/schemas/Merchant'
                        status:
                            type: string
                    description: 'FaaS - User Card Transaction'
                FaasUserCardSpendingBreakdownResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                        message:
                            type: string
                        data:
                            type: array
                            items:
                                $ref: '#/components/schemas/FaasUserCardSpendingBreakdown'
                    description: 'FaaS - User Card Spending Breakdown Response'
                FaasUserCardSpendingBreakdown:
                    type: object
                    properties:
                        title:
                            type: string
                        icon:
                            type: string
                        color:
                            type: string
                        count:
                            type: integer
                        amount:
                            type: integer
                        percent:
                            type: number
                            format: float
                    description: 'FaaS - User Card Spending Breakdown'
                FaasKycIdScanResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                        message:
                            type: string
                        data:
                            $ref: '#/components/schemas/FaasKycIdScan'
                    description: 'FaaS - Kyc Id Scan Response'
                FaasKycIdScan:
                    type: object
                    properties:
                        type:
                            type: string
                            description: '`ID` = ID Card, `DL` = Driver License, `PSP` = Passport'
                            enum:
                                - ID
                                - DL
                                - PSP
                        status:
                            type: string
                            enum:
                                - accepted
                                - manual_accepted
                                - incomplete
                                - invalid
                                - ineligible
                                - expired
                        firtName:
                            type: string
                        middleName:
                            type: string
                        lastName:
                            type: string
                        lastName2:
                            type: string
                        lastName3:
                            type: string
                        streetAddress:
                            type: string
                        streetAddress2:
                            type: string
                        streetAddress3:
                            type: string
                        streetAddress4:
                            type: string
                        streetAddress5:
                            type: string
                        streetAddress6:
                            type: string
                        city:
                            type: string
                        state:
                            type: string
                        zip:
                            type: string
                        country:
                            type: string
                        monthOfBirth:
                            type: string
                        dayOfBirth:
                            type: string
                        yearOfBirth:
                            type: string
                        expirationDate:
                            type: string
                        issuanceDate:
                            type: string
                        documentNumber:
                            type: string
                    description: 'FaaS - Kyc Id Scan'
                FaasClientsResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                        message:
                            type: string
                        data:
                            type: array
                            items:
                                $ref: '#/components/schemas/FaasClients'
                    description: 'FaaS - Clients Response'
                FaasClients:
                    type: object
                    properties:
                        id:
                            type: integer
                        name:
                            type: string
                    description: 'FaaS - Clients'
                SkuxUserResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                        message:
                            type: string
                        data:
                            $ref: '#/components/schemas/SkuxUser'
                    description: 'SKUx - User Response'
                SkuxUser:
                    type: object
                    properties:
                        id:
                            type: string
                        firstName:
                            type: string
                        lastName:
                            type: string
                        birthday:
                            type: string
                        emailAddress:
                            type: string
                        gender:
                            type: string
                        phone:
                            type: string
                        address:
                            type: string
                        addressline:
                            type: string
                        city:
                            type: string
                        country:
                            type: string
                        zip:
                            type: string
                        status:
                            type: string
                    description: 'SKUx - User'
                SkuxUserCardResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                        message:
                            type: string
                        data:
                            $ref: '#/components/schemas/SkuxUserCard'
                    description: 'SKUx - User Card Response'
                SkuxUserCardsResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                        message:
                            type: string
                        data:
                            type: array
                            items:
                                $ref: '#/components/schemas/SkuxUserCard'
                    description: 'SKUx - User Cards Response'
                SkuxUserCard:
                    type: object
                    properties:
                        id:
                            type: string
                        accountNumber:
                            type: string
                        maskedCardNumber:
                            type: string
                        balance:
                            type: integer
                            description: 'Minor unit. For instance, cents in USD'
                        status:
                            type: string
                            description: 'Available values: `active`, `inactive`'
                        nativeStatus:
                            type: string
                            description: 'Available values: `ACTIVE`, `CLOSED`, `SUSPENDED`, `EXPIRED`'
                    description: 'SKUx - User Card'
                SkuxPurseDetailsResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                        message:
                            type: string
                        data:
                            $ref: '#/components/schemas/SkuxPurseDetails'
                    description: 'SKUx - Purse Details Response'
                SkuxPurseDetails:
                    type: object
                    properties:
                        CardStatus:
                            type: string
                        PurseEffectiveDate:
                            type: string
                        PurseExpirationDate:
                            type: string
                        PurseNumber:
                            type: string
                        PurseType:
                            type: string
                        PurseStatus:
                            type: string
                        PurseBalance:
                            type: int
                            description: 'Minor unit. For instance, cents in USD'
                    description: 'SKUx - Purse Details'
                SkuxPurseListResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                        message:
                            type: string
                        data:
                            type: array
                            items:
                                $ref: '#/components/schemas/SkuxPurseDetails'
                    description: 'SKUx - Purse List Response'
                SkuxUserCardTransactionsResponse:
                    type: object
                    properties:
                        success:
                            type: boolean
                        message:
                            type: string
                        data:
                            type: array
                            items:
                                $ref: '#/components/schemas/SkuxUserCardTransaction'
                    description: 'SKUx - User Card Transactions Response'
                SkuxUserCardTransaction:
                    type: object
                    properties:
                        id:
                            type: integer
                        time:
                            type: string
                        amount:
                            type: integer
                            description: 'Minor unit. For instance, cents in USD'
                        txnId:
                            type: string
                            description: 'Transaction Reference'
                        comment:
                            type: string
                        description:
                            type: string
                        type:
                            type: string
                        reason:
                            type: string
                        merchant:
                            type: string
                        result:
                            type: string
                    description: 'SKUx - User Card Transaction'
