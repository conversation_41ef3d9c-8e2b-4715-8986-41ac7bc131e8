# Learn more about services, parameters and containers at
# http://symfony.com/doc/current/book/service_container.html
services:
    load_partner.global_collect:
        class: CoreBundle\Services\APIServices\GlobalCollectService
        public: true

    load_partner.alternative_payment:
        class: CoreBundle\Services\APIServices\AlternativePaymentService
        public: true

    sha256salted_encoder:
        class: CoreBundle\Services\Security\Sha256SaltedUserPasswordEncoder
        public: true

    app.jwt_authenticator:
        class: ApiBundle\Security\JwtAuthenticator
        arguments: ['@doctrine.orm.entity_manager']
        public: false

    app.not_logged_in_exception_listener:
        class: CoreBundle\EventListener\NotLoggedInExceptionListener
        tags:
            - { name: kernel.event_listener, event: kernel.exception }

    app.message_exception_listener:
        class: CoreBundle\EventListener\MessageExceptionListener
        tags:
            - { name: kernel.event_listener, event: kernel.exception }

    app.redirect_exception_listener:
        class: CoreB<PERSON>le\EventListener\RedirectExceptionListener
        tags:
            - { name: kernel.event_listener, event: kernel.exception }

    app.api_method_not_allowed_exception_listener:
        class: ApiBundle\EventListener\MethodNotAllowedListener
        tags:
            - { name: kernel.event_listener, event: kernel.exception }

    app.global_exception_listener:
        class: CoreBundle\EventListener\ExceptionListener
        tags:
            - { name: kernel.event_listener, event: kernel.exception, priority: 999 }

    app.overwrite_bundle_exception_listener:
        class: CoreBundle\EventListener\OverwriteBundleListener
        tags:
            - { name: kernel.event_listener, event: kernel.exception }

    app.failed_exception_listener:
        class: CoreBundle\EventListener\FailedExceptionListener
        tags:
            - { name: kernel.event_listener, event: kernel.exception }

    app.render_view_exception_listener:
        class: CoreBundle\EventListener\RenderViewExceptionListener
        tags:
            - { name: kernel.event_listener, event: kernel.exception }

    vich_uploader.storage:
        class: Vich\UploaderBundle\Storage\FileSystemStorage
        arguments: [ "@vich_uploader.property_mapping_factory" ]
        public: true
        
    FOS\UserBundle\Util\TokenGenerator:
        alias: fos_user.util.token_generator
        
    FOS\UserBundle\Form\Factory\FormFactory: &#126;
        
    fos_user.resetting.form.factory:
        class: FOS\UserBundle\Form\Factory\FormFactory
        arguments:
            - "@form.factory"
            - "%fos_user.resetting.form.name%"
            - "%fos_user.resetting.form.type%"
            - "%fos_user.resetting.form.validation_groups%"
        public: true

    salex_user.logged_swift_mailer:
        class: SalexUserBundle\Mailer\SwiftMailer
        public: true

    fos_user.util.password_updater:
        class: SalexUserBundle\Security\HashingPasswordUpdater
        public: pubilc
        arguments:
            - "@security.password_hasher_factory"

