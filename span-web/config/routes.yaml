controllers:
    resource:
        path: ../src/Controller/
        namespace: App\Controller
    type: attribute

skux:
    resource: "@SkuxBundle/Controller/"
    type:     annotation
    prefix:   /

faas:
    resource: "@FaasBundle/Controller/"
    type:     annotation
    prefix:   /

wilen:
    resource: "@WilenBundle/Controller/"
    type:     annotation
    prefix:   /

cash_on_web:
    resource: "@CashOnWebBundle/Controller/"
    type:     annotation

pto:
    resource: "@PTOBundle/Controller/"
    type:     annotation
    prefix:   /

transfer_mex:
    resource: "@TransferMexBundle/Controller/"
    type:     annotation
    prefix:   /

leaf_link:
    resource: "@LeafLinkBundle/Controller/"
    type:     annotation
    prefix:   /

fis:
    resource: "@FisBundle/Controller/"
    type:     annotation
    prefix:   /

us_unlocked:
    resource: "@UsUnlockedBundle/Controller/"
    type:     annotation
    prefix:   /

es_solo:
    resource: "@EsSoloBundle/Controller/"
    type:     annotation
    prefix:   /

clf:
    resource: "@ClfBundle/Controller/"
    type:     annotation
    prefix:   /

mobile:
    resource: "@MobileBundle/Controller/"
    type:     annotation
    prefix:   /

dev:
    resource: "@DevBundle/Controller/"
    type:     annotation
    prefix:   /

api:
    resource: "@ApiBundle/Controller/"
    type:     annotation
    prefix:   /

core:
    resource: "@CoreBundle/Controller/"
    type:     annotation
    prefix:   /

admin:
    resource: "@AdminBundle/Controller/"
    type:     annotation
    prefix:   /

portal:
    resource: "@PortalBundle/Controller/"
    type:     annotation
    prefix:   /

salex_user:
    resource: "@SalexUserBundle/Controller/"
    type:     annotation

#FOSUserBundle routing file
fos_user_security:
    resource: "@FOSUserBundle/Resources/config/routing/security.xml"

fos_user_resetting:
    resource: "@FOSUserBundle/Resources/config/routing/resetting.xml"
    prefix:   /resetting

#app.swagger_ui:
#    resource: "@NelmioApiDocBundle/Resources/config/routing/swaggerui.xml"
#    prefix:   /api-doc

#app.swagger:
#    path: /api-doc.json
#    methods: GET
#    defaults: { _controller: nelmio_api_doc.controller.swagger }

spendr:
    resource: "@SpendrBundle/Controller/"
    type:     annotation
    prefix:   /
