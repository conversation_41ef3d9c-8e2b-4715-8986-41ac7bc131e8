# This file is a sample
parameters:
    database_host:
    database_port:
    database_name:
    database_user:
    database_password:

    # Parameters for the archive db
    database_host_archive:
    database_port_archive:
    database_name_archive:
    database_user_archive:
    database_password_archive:

    # Database parameters to migrate data from
    database_host_usunlocked:
    database_port_usunlocked:
    database_name_usunlocked:
    database_user_usunlocked:
    database_password_usunlocked:

    # Database parameters for Read Replica instance
    analytics_host:
    analytics_port:
    analytics_name:
    analytics_user:
    analytics_password:

    secure_session:

    #  mailer_transport:
    #  mailer_host:
    #  mailer_port:
    #  mailer_user:
    #  mailer_password:
    delivery_address:
    debug_recipient:

    redis_dsn:

    # use predis if you want
    redis_connector:

    host:
    host_path:
    host_schema:

    log_root:
    cache_root:
    bower_bin:
    upload_root:
    secure_root:

    # API runner
    api_runner_key:
    api_runner_key_test:

    # TransExpress
    trans_express_url:
    trans_express_username:
    trans_express_password:
    trans_express_agent_id:

    # ES Solo Compass
    compass_url:
    compass_username:
    compass_password:
    compass_access_code:

    # ES Solo GTP
    gtp_url:
    gtp_token_test:
    gtp_sub_company_test:

    # ES Solo Nestor
    nestor_key:
    nestor_key_test:

    # Slack webhooks
    slack_webhook_test:
    slack_webhook_system:
    slack_webhook_fis:
    slack_webhook_ll:
    slack_webhook_usu:
    slack_webhook_usu_compliance:
    slack_webhook_usu_events:
    slack_webhook_mex:
    slack_webhook_mex_client:
    slack_webhook_mex_init:
    slack_webhook_cow:
    slack_webhook_cow_test:
    slack_webhook_faas:
    slack_webhook_spendr:

    # Rapid
    rapid_url:
    rapid_auth_query_user:
    rapid_auth_query_pass:
    rapid_auth_header_user:
    rapid_auth_header_pass:
    rapid_client_id:
    rapid_agent_name:
    rapid_agent_account_number:
    rapid_agent_ani:
    rapid_rapyd_account_number:
    rapid_rapyd_rpn_account_number:
    rapid_rapyd_rpn_card_id:
    rapid_rapyd_rpn_user_id:
    rapid_rapyd_rpn_bank_account_id:
    rapid_rpn_wsdl:
    rapid_rpn_key:
    rapid_rpn_settle_account:
    rapid_rpn_revenue_account:

    # Rapid Test
    rapid_url_test:
    rapid_auth_query_user_test:
    rapid_auth_query_pass_test:
    rapid_auth_header_user_test:
    rapid_auth_header_pass_test:
    rapid_client_id_test:
    rapid_agent_name_test:
    rapid_agent_account_number_test:
    rapid_agent_ani_test:
    rapid_rapyd_account_number_test:
    rapid_rapyd_rpn_account_number_test:
    rapid_rapyd_rpn_card_id_test:
    rapid_rapyd_rpn_user_id_test:
    rapid_rapyd_rpn_bank_account_id_test:
    rapid_rpn_wsdl_test:
    rapid_rpn_key_test:
    rapid_rpn_settle_account_test:
    rapid_rpn_revenue_account_test:

    # Shipito
    shipito_user:
    shipito_user_test:
    shipito_key:
    shipito_key_test:

    # IDology
    idology_username:
    idology_username_dev:
    idology_password:
    idology_password_dev:

    # Citcon
    citcon_key:
    citcon_key_old:
    citcon_key_test:

    # FirstView's basic info
    fv_username:
    fv_password:
    fv_url:

    # FirstView's Visa reloadable card
    fv_visa_r_store_name:
    fv_visa_r_client_id:
    fv_visa_r_product_id:

    # FirstView's MasterCard reloadable card
    fv_mc_r_store_name:
    fv_mc_r_client_id:
    fv_mc_r_product_id:

    # FirstView's MasterCard non-reloadable card
    fv_mc_n_store_name:
    fv_mc_n_client_id:
    fv_mc_n_product_id:

    # FirstView's sFTP
    fv_ftp_host:
    fv_ftp_port:
    fv_ftp_username:
    fv_ftp_password:

    # Tern's sFTP
    tern_ftp_host:
    tern_ftp_port:
    tern_ftp_username:
    tern_ftp_password:

    # LeafLink's sFTP
    sl_ftp_host:
    sl_ftp_port:
    sl_ftp_username:
    sl_ftp_password:

    # LeafLink bank IDs
    ewb_id:
    ewb_id_cr:
    ewb_ids:
    ewb_ids_cr:

    salal_id:
    salal_id_cr:
    salal_ids:
    salal_ids_cr:

    # NetVerify(Jumio) account
    netverify_token:
    netverify_secret:

    # FIS
    fis_url:
    fis_user_id:
    fis_pwd:
    fis_source_id:
    fis_client_id:
    fis_cert_path:

    # FIS test
    fis_url_test:
    fis_user_id_test:
    fis_pwd_test:
    fis_source_id_test:
    fis_client_id_test:
    fis_cert_path_test:

    # OPAS
    opas_url:
    opas_email:
    opas_password:

    # IMAP Emails
    wilen_email_username:
    wilen_email_password:

    ewb_email_username:
    ewb_email_password:

    ffb_spendr_username:
    ffb_spendr_password:

    # Wilen
    wilen_wsdl:
    wilen_api_username:
    wilen_api_password:
    wilen_api_client_id:
    wilen_api_location_id:
    wilen_api_promocode_program:
    wilen_api_issuance_product_id:

    # Wilen-test
    wilen_wsdl_test:
    wilen_api_username_test:
    wilen_api_password_test:
    wilen_api_client_id_test:
    wilen_api_location_id_test:
    wilen_api_promocode_program_test:
    wilen_api_issuance_product_id_test:

    # PTO
    pto_api_key:
    pto_profile_group:
    pto_account_type_id:
    pto_wsdl:

    # PTO-Test
    pto_api_key_test:
    pto_profile_group_test:
    pto_account_type_id_test:
    pto_wsdl_test:

    # Google reCAPTCHA
    recaptcha_key:
    recaptcha_frontend:

    # MQTT
    mqtt_host:
    mqtt_port:
    mqtt_cert_path:

    # Leaflink csv ftp info
    leaflink_bank_ftp_host:
    leaflink_bank_ftp_port:
    leaflink_bank_ftp_username:
    leaflink_bank_ftp_password:

    # BOTM transfer recipient id
    botm_settle_recipient_uniteller:
    botm_settle_recipient_rapyd:
    botm_settle_recipient_tern:
    botm_settle_recipient_partner:
    botm_settle_recipient_intermex_NY:
    botm_settle_recipient_intermex_CA:
    botm_settle_recipient_intermex_Other:
    botm_settle_recipient_intermex_NY_business_id:
    botm_settle_recipient_intermex_CA_business_id:
    botm_settle_recipient_intermex_Other_business_id:
    botm_settle_recipient_intermex_NY_business_account_id:
    botm_settle_recipient_intermex_CA_business_account_id:
    botm_settle_recipient_intermex_Other_business_account_id:
