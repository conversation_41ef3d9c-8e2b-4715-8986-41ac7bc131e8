# Cache and logs (Symfony2)
/app/cache/*
/app/logs/*
!app/cache/.gitkeep
!app/logs/.gitkeep

# Email spool folder
/app/spool/*

# Cache, session files and logs (Symfony3)
/var/cache/*
/var/logs/*
/var/sessions/*
!var/cache/.gitkeep
!var/logs/.gitkeep
!var/sessions/.gitkeep

# Parameters
/app/config/parameters*.yml
/app/config/parameters*.yml.dist
!/app/config/parameters.sample.yml
/app/config/parameters.ini

# keys
/app/config/keys*.yml
/app/config/keys*.yml.dist
!/app/config/keys.sample.yml
/app/config/keys.ini

/config/parameters*.yml
/config/keys*.yml

# Managed by Composer
/app/bootstrap.php.cache
/var/bootstrap.php.cache
/bin/*
!/bin/console
!/bin/phpunit
!/bin/symfony_requirements
!/bin/fix_vendor
!/bin/sami_config.php
!/bin/generate_schema.sh
!/bin/api/conf/*
!/bin/api/tmp/.gitignore
!/bin/api/generate_docs.sh
!/bin/api/generate_sdk.sh
!/bin/docker_up.sh
!/bin/docker_down.sh
!/bin/read_file.php
!/bin/export_faild_kyc.php
!/bin/sitebackup.sh
!/bin/dbbackup.sh
!/bin/automated_test.php
!/bin/node
!/bin/cloudflare-whitelist.sh
/vendor/
/phpstan.local.neon
/phpstan-baseline-*.php

# Assets and user uploads
/web/uploads/
/web/.htaccess_dev
/web/.maintenance
/web/files
/web/chrome

/.env*
/.php-cs-fixer.cache
/vendor6
/vendor34
/docker/redis/data/*
/public/bundles

# PHPUnit
/app/phpunit.xml
/phpunit.xml

# Composer PHAR
/composer.phar

# Backup entities generated with doctrine:generate:entities command
**/Entity/*~

#/web/assetic/
/web/upload/
#/web/js/
#/web/css/
/web/images/
/web/build-temp
/web/build
/.parcel-cache
/web/mobile
/web/static/clf
/web/static/usun
/web/static/fisn
/web/static/mex/web
/web/static/spendr/web
/web/developer/*.html
/web/config.php
/web/app_dev.php
!/web/developer/*_origin.html

/cronjob
/vendor.zip
/mobile
/phpstan-baseline-*.php

######################### \/ Added for Symfony 6+ ##########################

###> symfony/framework-bundle ###
/.env.local
/.env.local.php
/.env.*.local
/config/secrets/prod/prod.decrypt.private.php
/config/parameters*.yml
!/config/parameters.sample.yml
/config/keys*.yml
!/config/keys.sample.yml

/public/bundles/
/var/
/vendor/
/vendor34/
/vendor6/
/vendor*
###< symfony/framework-bundle ###

###> phpunit/phpunit ###
/phpunit.xml
.phpunit.result.cache
###< phpunit/phpunit ###

###> symfony/phpunit-bridge ###
.phpunit.result.cache
/phpunit.xml
###< symfony/phpunit-bridge ###

###> IDE ###
/.idea
/.php-cs-fixer.cache
.DS_Store
###< IDE ###
