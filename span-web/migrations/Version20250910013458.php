<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250910013458 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE user_group ADD tern_business_id INT DEFAULT NULL, ADD tern_business_account_id INT DEFAULT NULL');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_8F02BF9DAC57C252 ON user_group (tern_business_account_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP INDEX UNIQ_8F02BF9DAC57C252 ON user_group');
        $this->addSql('ALTER TABLE user_group DROP tern_business_id, DROP tern_business_account_id');
    }
}
