<?php


namespace ApiBundle\Controller\LeafLink;


use CoreB<PERSON>le\Entity\AchTransactions;
use CoreBundle\Entity\Email;
use CoreBundle\Entity\UserCard;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\JsonResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Util;
use LeafLinkBundle\Entity\LeafLinkApiLog;
use PortalBundle\Util\RegisterStep;
use Ramsey\Uuid\Uuid;
use SalexUserBundle\Entity\User;
use SalexUserBundle\Entity\UserConfig;
use OpenApi\Annotations as SWG;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use CoreBundle\Entity\NachaEntity;
use CoreBundle\Services\SSLEncryptionService;
use LeafLinkBundle\Services\SlackService;

class UserController extends BaseController
{
    protected function successResponse(User $user)
    {
        $config = $user->ensureConfig();
        return new SuccessResponse([
            'id' => $user->getId(),
            'bankAccountId' => $config->getBankAccountId(),
            'email' => $user->getEmail(),
            'companyName' => $config->getCompanyName(),
            'customerOnboardingStatus' => Util::humanize($user->getRegisterStep()),
        ]);
    }

    protected function getUpdateResponse(User $user)
    {
        $config = $user->ensureConfig();
        return new SuccessResponse([
            'id' => $user->getId(),
            'bankAccountId' => $config->getBankAccountId(),
            'customerOnboardingStatus' => Util::humanize($user->getRegisterStep()),
            'userCreateDateTime' => $user->getMemberSince()
        ]);
    }

    protected function getBankInfoResponse(User $user, UserCard $uc)
    {
        $config = $user->ensureConfig();

        $log = new LeafLinkApiLog();
        $log->setRoute('/api/leaflink/get-bank-info')
            ->setResponse('200')
            ->setTime(new \DateTime())
            ->persist();

        return new SuccessResponse([
            'id' => $user->getId(),
            'bankAccountId' => $config->getBankAccountId(),
            'institutionName' => $uc->getBankName(),
            'lastFourOfAccountNum' => substr(SSLEncryptionService::decrypt($uc->getAccountNumber()), -4),
            'status' => $user->getStatus()
        ]);
    }

    /**
     * @Route("/api/leaflink/create-account", methods={"POST"}, name="api_leaflink_create_account")
     * @SWG\Post(
     *   tags={"Users"},
     *   summary="Create Bank Account",
     *   description="Create Bank account and prepare for onboarding.",
     *   @SWG\RequestBody(required=true, @SWG\MediaType(mediaType="application/x-www-form-urlencoded", schema=@SWG\Schema(required={"bankAccountId", "companyName", "email"}, properties={
     *     @SWG\Property(property="bankAccountId", description="LeafLink's User Bank ID.", type="string"),
     *     @SWG\Property(property="companyName", description="Name of company. Only used for onboarding email.", type="string"),
     *     @SWG\Property(property="email", description="Email address for institution. Necessary for onboarding.", type="string"),
     *     @SWG\Property(property="phone", description="Phone number of institution. Optional.", type="string"),
     *   }))),
     *   security={{"x-api-key":{}, "x-timezone":{}, "x-i18n":{}, "x-token":{}}},
     *   @SWG\Response(response=200, ref="#/components/responses/LLCreateResponse"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     *   @SWG\Response(response=409, ref="#/components/responses/LLCreateFailResponse"),
     * )
     *
     * @return JsonResponse
     */
    public function create()
    {
        $request = $this->validateParameters(__METHOD__);

        if (User::findByBankAccountId($request->get('bankAccountId'))) {

            $log = new LeafLinkApiLog();
            $log->setRoute('/api/leaflink/create-account')
                ->setResponse('409')
                ->setTime(new \DateTime())
                ->persist();

            return new FailedResponse('This bank account ID has already been registered.', [
                'Error Code' => 1
            ], 409);
        }

        $user = new User();
        $user->setEmail($request->get('email'))
            ->setUsername($request->get('companyName'))
            ->setMobilephone($request->get('phone'))
            ->setPlainPassword(Util::generatePassword('LeafLink*'))
            ->setRegisterStep(RegisterStep::PENDING)
            ->setStatus(User::STATUS_ACTIVE)
            ->setEnabled(true)
            ->setSource('leaflink_api')
            ->persist();

        $config = $user->ensureConfig();
        $config->setBankAccountId($request->get('bankAccountId'))
            ->setCompanyName($request->get('companyName'))
            ->setPlatform(UserConfig::PLATFORM_LEAFLINK)
            ->setReferCode(Uuid::uuid4())
            ->persist();

        $params['bankAccountId'] = $config->getBankAccountId();
        $params['companyName'] = $config->getCompanyName();
        $params['referCode'] = $config->getReferCode();

        // Send test email if in staging, otherwise send regular email.
        if (Util::isStaging())
        {
            Email::sendWithTemplateToUser($user, Email::TEMPLATE_LEAFLINK_ONBOARDING_LINK_TEST, $params);
        } elseif (Util::isStage())
        {
            Email::sendWithTemplateToUser($user, Email::TEMPLATE_LEAFLINK_ONBOARDING_LINK_STAGE, $params);
        } else {
        Email::sendWithTemplateToUser($user, Email::TEMPLATE_LEAFLINK_ONBOARDING_LINK, $params);
        }

        // Send slack alert.
        SlackService::wave('Bank Account ' . $config->getBankAccountId() . ' has been created for Company' . $config->getCompanyName() , [
            'bankAccountId' => $config->getBankAccountId()
        ]);

        $log = new LeafLinkApiLog();
        $log->setRoute('/api/leaflink/create-account')
            ->setResponse('200')
            ->setTime(new \DateTime())
            ->persist();

        return $this->successResponse($user);
    }

//    /**
//     * @Route("/api/leaflink/update-user", methods={"POST"}, name="api_leaflink_update_user")
//     * @SWG\Post(
//     *   tags={"Users"},
//     *   summary="Update user",
//     *   description="Update user profile",
//     *   @SWG\Parameter(name="userAccountId", description="LeafLink's User ID", in="formData", schema=@SWG\Schema(type="string"), required=true),
//     *   @SWG\Parameter(name="email", description="Email address", in="formData", schema=@SWG\Schema(type="string"), required=true),
//     *   @SWG\Parameter(name="mobile", description="Mobile phone number", in="formData", schema=@SWG\Schema(type="string"), required=true),
//     *   @SWG\Parameter(name="userFirstName", description="User' first name", in="formData", schema=@SWG\Schema(type="string"), required=true),
//     *   @SWG\Parameter(name="userLastName", description="User' last name", in="formData", schema=@SWG\Schema(type="string")),
//     *   @SWG\Parameter(name="companyName", description="Company name", in="formData", schema=@SWG\Schema(type="string"), required=true),
//     *   @SWG\Parameter(name="updateBanking", description="Update banking information for customer, boolean", in="formData", schema=@SWG\Schema(type="boolean"), required=false),
//     *   security={{"x-api-key":{}, "x-timezone":{}, "x-i18n":{}, "x-token":{}}},
//     *   @SWG\Response(response=200, ref="#/components/responses/Response"),
//     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
//     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
//     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
//     * )
//     *
//     * @return JsonResponse
//     */
//    public function update()
//    {
//        $request = $this->validateParameters(__METHOD__);
//
//        $user = User::findByCompanyId($request->get('companyId'));
//        if (!$user) {
//            return new FailedResponse('This user does not exist.');
//        }
//
//        /** @var User $user */
//        $user->setEmail($request->get('email'))
//            ->setUsername($request->get('email'))
//            ->setFirstName($request->get('userFirstName'))
//            ->setLastName($request->get('userLastName') ?: '')
//            ->setMobilephone($request->get('mobile'))
//            ->persist();
//
//        $config = $user->ensureConfig();
//        $config->setCompanyId($request->get('companyId'))
//            ->setUserAccountId($request->get('userAccountId'))
//            ->setCompanyName($request->get('companyName'))
//            ->persist();
//
//        if ($request->get('updateBanking'))
//        {
//            $params['companyId'] = $config->getCompanyId();
//            $params['userAccountId'] = $config->getUserAccountId();
//            $params['companyName'] = $config->getCompanyName();
//
//            // Send test email if in staging, otherwise send regular email.
//            if (Util::isStaging())
//            {
//                Email::sendWithTemplateToUser($user, Email::TEMPLATE_LEAFLINK_ONBOARDING_LINK_TEST, $params);
//            } else {
//                Email::sendWithTemplateToUser($user, Email::TEMPLATE_LEAFLINK_ONBOARDING_LINK, $params);
//            }
//            $user->setRegisterStep(RegisterStep::PENDING);
//        }
//
//        // Send slack alert.
//        SlackService::wave('Company ' . $config->getCompanyName() . ' has been updated!', [
//            'Company Id' => $config->getCompanyId(),
//            'User Account Id' => $config->getUserAccountId(),
//            'Update Banking?' => $request->get('updateBanking')
//        ]);
//
//        return $this->successResponse($user);
//    }

//    /**
//     * @Route("/api/leaflink/get-onboard-status", methods={"POST"}, name="api_leaflink_get_onboarding_status")
//     * @SWG\Post(
//     *   tags={"Users"},
//     *   summary="Get Onboarding Status",
//     *   description="Get User Onboarding Status",
//     *   @SWG\Parameter(name="bankAccountId", description="LeafLink's Bank Account ID", in="formData", schema=@SWG\Schema(type="string"), required=true),
//     *   security={{"x-api-key":{}, "x-timezone":{}, "x-i18n":{}, "x-token":{}}},
//     *   @SWG\Response(response=200, ref="#/components/responses/Response"),
//     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
//     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
//     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
//     * )
//     *
//     * @return JsonResponse
//     */
//    public function getOnboardStatus()
//    {
//        $request = $this->validateParameters(__METHOD__);
//
//        $user = User::findByCompanyId($request->get('companyId'));
//        if (!$user) {
//            return new FailedResponse('This user does not exist.', '', 409);
//        }
//
//        return $this->getUpdateResponse($user);
//    }

    /**
     * @Route("/api/leaflink/get-bank-info", methods={"POST"}, name="api_leaflink_get_bank_info")
     * @SWG\Post(
     *   tags={"Users"},
     *   summary="Get Bank Info",
     *   description="Get abbreviated saved banking info for a bank account.",
     *   @SWG\RequestBody(required=true, @SWG\MediaType(mediaType="application/x-www-form-urlencoded", schema=@SWG\Schema(required={"bankAccountId"}, properties={
     *     @SWG\Property(property="bankAccountId", description="LeafLink's User ID", type="string"),
     *   }))),
     *   security={{"x-api-key":{}, "x-timezone":{}, "x-i18n":{}, "x-token":{}}},
     *   @SWG\Response(response=200, ref="#/components/responses/LLBankInfoResponse"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     *
     * @return JsonResponse
     */
    public function getBankingInfo()
    {
        $request = $this->validateParameters(__METHOD__);

        $user = User::findByBankAccountId($request->get('bankAccountId'));
        if (!$user) {
            $log = new LeafLinkApiLog();
            $log->setRoute('/api/leaflink/get-bank-info')
                ->setResponse('409')
                ->setTime(new \DateTime())
                ->persist();

            return new FailedResponse('This account does not exist.', [
                'Error Code' => 1
            ], 409);
        }

        $ucs = $user->getActiveCardsInPlatform($this->platform);
        if ($ucs->isEmpty()) {
            $log = new LeafLinkApiLog();
            $log->setRoute('/api/leaflink/get-bank-info')
                ->setResponse('409')
                ->setTime(new \DateTime())
                ->persist();

            return new FailedResponse('No valid banking info on this account.', [
                'Error Code' => 2
            ], 409);
        }

        $uc = $ucs->last();
        return $this->getBankInfoResponse($user, $uc);
    }

//    /**
//     * @Route("/api/leaflink/get-accounts", methods={"POST"}, name="api_leaflink_get_accounts")
//     * @SWG\Post(
//     *   tags={"Users"},
//     *   summary="Get All Accounts",
//     *   description="Get all bank accounts under a specific Company ID.",
//     *   @SWG\Parameter(name="companyId", description="LeafLink's Institution ID", in="formData", schema=@SWG\Schema(type="string"), required=true),
//     *   security={{"x-api-key":{}, "x-timezone":{}, "x-i18n":{}, "x-token":{}}},
//     *   @SWG\Response(response=200, ref="#/components/schemas/LLGetAccountsResponse"),
//     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
//     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
//     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
//     *   @SWG\Response(response=409, ref="#/components/schemas/LLGetAccountsFailure"),
//     * )
//     *
//     * @return JsonResponse
//     */
//    public function getCompanyIds()
//    {
//        $request = $this->validateParameters(__METHOD__);
//
//        $companyId = $request->get('companyId');
//
//        $queryResults = Util::em()->getRepository(UserConfig::class)
//            ->createQueryBuilder('uc')
//            ->where('uc.companyId = :companyId')
//            ->setParameter('companyId', $companyId)
//            ->getQuery()
//            ->getResult();
//
//        if (!$queryResults) {
//            return new FailedResponse('This user account contains no bank accounts.', [], 409);
//        }
//
//        $bankAccountIdsList = [];
//        foreach ($queryResults as $config)
//        {
//            $bankAccountIdsList[] = $config->getBankAccountId();
//        }
//
//        return new JsonResponse(true, 'Success', [
//            'companyId' => $companyId,
//            'bankAccountIds' => $bankAccountIdsList
//        ]);
//    }

    /**
     * @Route("/api/leaflink/delete-bank-account", methods={"POST"}, name="api_leaflink_delete_bank_account")
     * @SWG\Post(
     *   tags={"Users"},
     *   summary="Delete Bank Account",
     *   description="Soft delete user's bank account. Hard delete any encrypted payment information.",
     *   @SWG\RequestBody(required=true, @SWG\MediaType(mediaType="application/x-www-form-urlencoded", schema=@SWG\Schema(required={"bankAccountId"}, properties={
     *     @SWG\Property(property="bankAccountId", description="LeafLink's Bank Account ID", type="string"),
     *   }))),
     *   security={{"x-api-key":{}, "x-timezone":{}, "x-i18n":{}, "x-token":{}}},
     *   @SWG\Response(response=200, ref="#/components/responses/LLDeleteAccountResponse"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     *   @SWG\Response(response=409, ref="#/components/responses/LLDeleteAccountFailure"),
     * )
     *
     * @return JsonResponse
     */
    public function deleteBankAccount()
    {
        $request = $this->validateParameters(__METHOD__);

        $user = User::findByBankAccountId($request->get('bankAccountId'));

        if (!$user)
        {
            $log = new LeafLinkApiLog();
            $log->setRoute('/api/leaflink/delete-bank-account')
                ->setResponse('409')
                ->setTime(new \DateTime())
                ->persist();

            return new FailedResponse('This Bank Account ID does not exist in our database.', [
                'Error Code' => 1
            ], 409);
        }

        if ($user->getStatus() === User::STATUS_CLOSED)
        {
            $log = new LeafLinkApiLog();
            $log->setRoute('/api/leaflink/delete-bank-account')
                ->setResponse('409')
                ->setTime(new \DateTime())
                ->persist();

            return new FailedResponse('This account has already been closed.', [
                'Error Code' => 2
            ], 409);
        }

        if ($user->getRegisterStep() === RegisterStep::PENDING) {
            $user->setStatus(User::STATUS_CLOSED);

            $log = new LeafLinkApiLog();
            $log->setRoute('/api/leaflink/delete-bank-account')
                ->setResponse('200')
                ->setTime(new \DateTime())
                ->persist();

            return new SuccessResponse([
                'bankAccountId' => $request->get('bankAccountId'),
            ], 'Account is still pending onboarding. No bank information to delete. Account has been closed.', 200);
        }



        $ucQuery = Util::em()->getRepository(UserCard::class)
            ->createQueryBuilder('uc')
            ->leftJoin('uc.user', 'u')
            ->where('u.id = :id')
            ->setParameter('id', $user->getId())
            ->getQuery()
            ->getResult();

        $uc = last($ucQuery);

        if (!$uc)
        {
            $user->setStatus(User::STATUS_CLOSED);

            $log = new LeafLinkApiLog();
            $log->setRoute('/api/leaflink/delete-bank-account')
                ->setResponse('200')
                ->setTime(new \DateTime())
                ->persist();

            return new SuccessResponse([
                'bankAccountId' => $request->get('bankAccountId'),
            ], 'No Bank data found for account. Account has been closed.', 200);
        }

        $uc->setAccountNumber('')
            ->setRoutingNumber('');

        $user->setStatus(User::STATUS_CLOSED);

        $log = new LeafLinkApiLog();
        $log->setRoute('/api/leaflink/delete-bank-account')
            ->setResponse('200')
            ->setTime(new \DateTime())
            ->persist();

        return new SuccessResponse([
            'bankAccountId' => $request->get('bankAccountId'),
        ], 'Bank account has been closed and sensitive information has been removed.', 200);
    }

    /**
     * @Route("/api/leaflink/test-onboarding-webhook", methods={"POST"}, name="api_leaflink_test_onboarding_webhook")
     * @SWG\Post(
     *   tags={"Users"},
     *   summary="Onboarding Webhook",
     *   description="Webhook triggered when a user completes the onboarding process.",
     *   security={{"x-api-key":{}, "x-timezone":{}, "x-i18n":{}, "x-token":{}}},
     *   @SWG\Response(response=202, ref="#/components/responses/LLOnboardingWebhookResponse"),
     * )
     *
     * @return JsonResponse
     */
    public function LLOnboardingWebhook()
    {
        return new JsonResponse();
    }

    /**
     * @Route("/t/cron/leaflink/onboard/email")
     * @param Request $request
     * @return Response
     * @throws \LogicException
     */
    public function sendReminderEmails(Request $request) {
        $this->authDevOrLocal();
        $userQuery = Util::em()->getRepository(User::class)
            ->createQueryBuilder('user')
            ->where('user.registerStep = :pending')
            ->setParameter('pending', RegisterStep::PENDING)
            ->getQuery()
            ->getResult();
        // Add joins instead of if in the future

        foreach($userQuery as $user) {
            $config = $user->ensureConfig();
            // Check if user platform is leaflink and user is not unsubscribed. (may need to change to include LeafLink later)
            if ($config->getPlatform() === UserConfig::PLATFORM_LEAFLINK && $config->getUnsubscribed() !== true)
            {
                $params['companyId'] = $config->getBankAccountId();
                Email::sendWithTemplateToUser($user, Email::TEMPLATE_LEAFLINK_ONBOARDING_LINK, $params);
            }
        }

        return new Response();
    }
}
