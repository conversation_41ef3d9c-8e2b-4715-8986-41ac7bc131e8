<?php


namespace ApiBundle\Services;


use CoreBundle\Services\SSLEncryptionService;
use CoreBundle\Utils\Util;
use \SoapClient;
use PTOBundle\Entity\PTOExternalAccount;
use PTOBundle\Entity\PTOInternalAccount;
use PTOBundle\Entity\PTOPaymentCard;
use PTOBundle\Entity\PTOTransaction;
use PTOBundle\Entity\PTOUser;
use PTOBundle\Entity\PTOUserType;
use PTOBundle\Services\SlackService;

class RPNService
{
    private static function getRPNVariables()
    {
        SlackService::info('Using non KYB info');
        if (Util::isDev() || Util::isStaging())
        {
            return [
                'apiKey' => Util::getParameter('pto_api_key_test'),
                'profileGroup' => Util::getParameter('pto_profile_group_test'),
                'accountTypeID' => Util::getParameter('pto_account_type_id_test'),
                'wsdl' => Util::getParameter('pto_wsdl_test'),
                'isDev' => true
            ];
        }

        return [
            'apiKey' => Util::getParameter('pto_api_key'),
            'profileGroup' => Util::getParameter('pto_profile_group'),
            'accountTypeID' => Util::getParameter('pto_account_type_id'),
            'wsdl' => Util::getParameter('pto_wsdl'),
            'isDev' => false
        ];
    }

    private static function getRPNVariablesKYB()
    {
        SlackService::info('Using KYB info');
        if (Util::isDev() || Util::isStaging())
        {
            return [
                'apiKey' => Util::getParameter('pto_api_key_test_kyb'),
                'profileGroup' => Util::getParameter('pto_profile_group_test_kyb'),
                'accountTypeID' => Util::getParameter('pto_account_type_id_test_kyb'),
                'wsdl' => Util::getParameter('pto_wsdl_test'),
                'isDev' => true
            ];
        }

        return [
            'apiKey' => Util::getParameter('pto_api_key_kyb'),
            'profileGroup' => Util::getParameter('pto_profile_group_kyb'),
            'accountTypeID' => Util::getParameter('pto_account_type_id_kyb'),
            'wsdl' => Util::getParameter('pto_wsdl'),
            'isDev' => false
        ];
    }

    public static function createUserProfile(PTOUser $user)
    {
        // Get variables to access RPN API. This determines if they are for live or test.
        $vars = self::getRPNVariables();
        $client = new SoapClient($vars['wsdl']);
        $result = $client->createUserProfile([
            'ApiKey' => $vars['apiKey'],
            'ProfileGroupId' => $vars['profileGroup'],
            'Username' => $user->getUuid(),
            'IdentificationType' => 'other',
            'EmailAddress' => $user->getEmail(),
            'FirstNames' => $user->getFirstName(),
            'LastNames' => $user->getLastName(),
            'CompanyName' => 'Tern', // What should this be?
            'SSN' => '',
            'VerifySSN' => '',
            'IdentificationNumber' => '',
            'VerifyIdentificationNumber' => '',
            'CountryResidence' => $user->getCountry(),
            'CountryCitizenship' => $user->getCountry(),
            'HomePhone' => $user->getPhone(),
            'OfficePhone' => $user->getPhone(),
            'MobilePhone' => $user->getPhone(),
            'Fax' => '',
            'MailingAddressPhone' => $user->getPhone(),
            'PhysicalAddressLine1' => $user->getCheckShipAddress1(),
            'PhysicalAddressLine2' => $user->getCheckShipAddress2(),
            'PhysicalAddressCity' => $user->getCity(),
            'PhysicalAddressState' => $user->getProvince(),
            'PhysicalAddressPostalCode' => $user->getCheckShipPostalCode(),
            'PhysicalAddressCountry' => $user->getCountry(),
            'MailingAddressName' => $user->getFirstName() . ' ' . $user->getLastName(),
            'MailingAddressLine1' => $user->getCheckShipAddress1(),
            'MailingAddressLine2' => $user->getCheckShipAddress2(),
            'MailingAddressCity' => $user->getCheckShipCity(),
            'MailingAddressState' => $user->getCheckShipProvince(),
            'MailingAddressPostalCode' => $user->getCheckShipPostalCode(),
            'MailingAddressCountry' => $user->getCheckShipCountry(),
            'AllowSystemEmails' => 'no',
            'AcceptTerms' => 'true'
        ]);

        if ($result->{'ResponseCode'} === '0')
        {
            SlackService::bell('RPN User Profile created successfully.', [
                'Response' => $result
            ]);
            return $result->{'UserId'};
        }

        SlackService::info('An error occurred when creating a new User Profile with RPN', [
            'Code' => $result->{'ResponseCode'},
            'Message' => $result->{'ResponseMessage'}
        ]);

        return 'Error';
    }

    public static function createUserProfileKyb(PTOUser $user)
    {
        // Get variables to access RPN API. This determines if they are for live or test.
        $vars = self::getRPNVariablesKYB();
        $client = new SoapClient($vars['wsdl']);
        $result = $client->createUserProfile([
            'ApiKey' => $vars['apiKey'],
            'ProfileGroupId' => $vars['profileGroup'],
            'Username' => $user->getUuid(),
            'IdentificationType' => 'other',
            'EmailAddress' => $user->getEmail(),
            'FirstNames' => $user->getFirstName(),
            'LastNames' => $user->getLastName(),
            'CompanyName' => 'Tern', // What should this be?
            'SSN' => '',
            'VerifySSN' => '',
            'IdentificationNumber' => '',
            'VerifyIdentificationNumber' => '',
            'CountryResidence' => $user->getCountry(),
            'CountryCitizenship' => $user->getCountry(),
            'HomePhone' => $user->getPhone(),
            'OfficePhone' => $user->getPhone(),
            'MobilePhone' => $user->getPhone(),
            'Fax' => '',
            'MailingAddressPhone' => $user->getPhone(),
            'PhysicalAddressLine1' => $user->getCheckShipAddress1(),
            'PhysicalAddressLine2' => $user->getCheckShipAddress2(),
            'PhysicalAddressCity' => $user->getCity(),
            'PhysicalAddressState' => $user->getProvince(),
            'PhysicalAddressPostalCode' => $user->getCheckShipPostalCode(),
            'PhysicalAddressCountry' => $user->getCountry(),
            'MailingAddressName' => $user->getFirstName() . ' ' . $user->getLastName(),
            'MailingAddressLine1' => $user->getCheckShipAddress1(),
            'MailingAddressLine2' => $user->getCheckShipAddress2(),
            'MailingAddressCity' => $user->getCheckShipCity(),
            'MailingAddressState' => $user->getCheckShipProvince(),
            'MailingAddressPostalCode' => $user->getCheckShipPostalCode(),
            'MailingAddressCountry' => $user->getCheckShipCountry(),
            'AllowSystemEmails' => 'no',
            'AcceptTerms' => 'true'
        ]);

        if ($result->{'ResponseCode'} === '0')
        {
            SlackService::bell('RPN User Profile created successfully.', [
                'Response' => $result
            ]);
            return $result->{'UserId'};
        }

        SlackService::info('An error occurred when creating a new User Profile with RPN', [
            'Code' => $result->{'ResponseCode'},
            'Message' => $result->{'ResponseMessage'}
        ]);

        return 'Error';
    }

    public static function createPaymentCard(PTOPaymentCard $card, PTOUser $user)
    {
        $userType = PTOUserType::findUserTypeByUuid($user->getUserTypeId());

        if ($userType->getIsKybRequired() === true)
        {
            $vars = self::getRPNVariablesKYB();
        } else {
            $vars = self::getRPNVariables();
        }

        $client = new SoapClient($vars['wsdl']);
        $result = $client->createInstantBankTransferTokenV2([
            'ApiKey' => $vars['apiKey'],
            'UserID' => $user->getExternalId(),
            'NickName' => $card->getCardName(),
            'DestinationCardNumber' => SSLEncryptionService::decrypt($card->getCardNumber()),
            'ExpirationYear' => $card->getExpirationYear(),
            'ExpirationMonth' => $card->getExpirationMonth(),
            'LastName' => $user->getLastName(),
            'FirstName' => $user->getFirstName(),
            'AddressLine1' => 'Address Line 1',
            'AddressLine2' => 'Address Line 2',
            'City' => $user->getCity(),
            'State' => 'GA',
            'ZipCode' => '30132',
            'PhoneNumber' => '**********',
        ]);

        if ($result->{'ResponseCode'} === '0')
        {
            SlackService::bell('RPN Payment Card created successfully', [
                'Response' => $result
            ]);

            return $result->{'InstantBankTransferAccountID'};
        }

        SlackService::alert('An error occurred when creating a new Payment Card with RPN', [
            'Code' => $result->{'ResponseCode'},
            'Message' => $result->{'ResponseMessage'}
        ]);

        return 'Error';
    }

    public static function createInternalAccount(PTOUser $user)
    {
        $userType = PTOUserType::findUserTypeByUuid($user->getUserTypeId());

        if ($userType->getIsKybRequired() === true)
        {
            $vars = self::getRPNVariablesKYB();
        } else {
            $vars = self::getRPNVariables();
        }

        $client = new SoapClient($vars['wsdl']);
        $result = $client->createAccountV2([
            'ApiKey' => $vars['apiKey'],
            'UserID' => $user->getExternalId(),
            'AccountTypeId' => $vars['accountTypeID'],
            'AccountNumber' => '',
            'AdministratorNotes' => ''
        ]);

        if ($result->{'ResponseCode'} === '0')
        {
            SlackService::bell('RPN Internal Account created successfully.', [
                'Response' => $result
            ]);

            return $result->{'AccountNumber'};
        }

        SlackService::alert('An error occurred when creating a new Internal Account with RPN', [
            'Code' => $result->{'ResponseCode'},
            'Message' => $result->{'ResponseMessage'}
        ]);

        return 'Error';
    }

    public static function createInternalAccountKyb(PTOUser $user)
    {
        $userType = PTOUserType::findUserTypeByUuid($user->getUserTypeId());

        if ($userType->getIsKybRequired() === true)
        {
            $vars = self::getRPNVariablesKYB();
        } else {
            $vars = self::getRPNVariables();
        }

        $client = new SoapClient($vars['wsdl']);
        $result = $client->createAccountV3([
            'ApiKey' => $vars['apiKey'],
            'UserID' => $user->getExternalId(),
            'AccountTypeId' => $vars['accountTypeID'],
            'AccountNumber' => '',
            'AdministratorNotes' => ''
        ]);

        if ($result->{'ResponseCode'} === '0')
        {
            SlackService::bell('RPN Internal Account created successfully.', [
                'Response' => $result
            ]);

            return $result->{'AccountNumber'};
        }

        SlackService::alert('An error occurred when creating a new Internal Account with RPN', [
            'Code' => $result->{'ResponseCode'},
            'Message' => $result->{'ResponseMessage'}
        ]);

        return 'Error';
    }

    public static function createP2CTransfer(PTOTransaction $txn, PTOUser $user)
    {
        $userType = PTOUserType::findUserTypeByUuid($user->getUserTypeId());

        if ($userType->getIsKybRequired() === true)
        {
            $vars = self::getRPNVariablesKYB();
        } else {
            $vars = self::getRPNVariables();
        }

        $client = new SoapClient($vars['wsdl']);
        $result = $client->createInstantBankTransferWithVaultedToken([
            'ApiKey' => $vars['apiKey'],
            'CustomerID' => $user->getExternalId(),
            'AccountNumber' => $user->getExternalAccountId(),
            'CardID' => '',
            'InstantBankTransferAccountID' => $txn->getToUserPayment(),
            'Amount' => $txn->getAmount(),
            'Memo' => $txn->getUuid(),
            'SoftDescriptorName' => '',
            'SoftDescriptorAddress' => '',
            'SoftDescriptorCity' => '',
            'SoftDescriptorState' => '',
            'SoftDescriptorCounty' => '',
            'SoftDescriptorZipCode' => '',
            'SoftDescriptorPhoneNumber' => ''
        ]);

        if ($result->{'ResponseCode'} === '0')
        {
            SlackService::bell('RPN Push to Card Transaction created successfully.', [
                'Response' => $result
            ]);

            return 'Success';
        }

        if ($result->{'ResponseCode'} === '42' || $result->{'ResponseCode'} === '41')
        {
            SlackService::alert('RPN Push to Card Transaction is now pending.', [
                'Code' => $result->{'ResponseCode'},
                'Message' => $result->{'ResponseMessage'}
            ]);

            return 'Pending';
        }

        SlackService::alert('An error occurred when creating a new transaction with RPN', [
            'Code' => $result->{'ResponseCode'},
            'Message' => $result->{'ResponseMessage'}
        ]);

        return 'Failure';
    }

    public static function createExternalAccount(PTOUser $user, PTOExternalAccount $externalAccount)
    {
        $userType = PTOUserType::findUserTypeByUuid($user->getUserTypeId());

        if ($userType->getIsKybRequired() === true)
        {
            $vars = self::getRPNVariablesKYB();
        } else {
            $vars = self::getRPNVariables();
        }

        $client = new SoapClient($vars['wsdl']);
        $result = $client->createUserProfileBankAccount([
            'ApiKey' => $vars['apiKey'],
            'UserId' => $user->getExternalId(),
            'PrimaryAccount' => 'yes',
            'AccountNickname' => $externalAccount->getName(),
            'BankName' => $externalAccount->getBankName(),
            'BankRoutingNumber' => SSLEncryptionService::decrypt($externalAccount->getRoutingNumber()),
            'BankAccountNumber' => SSLEncryptionService::decrypt($externalAccount->getAccountNumber()),
            'BankAccountType' => $externalAccount->getType(),
            'BankAccountName' => $externalAccount->getName()
        ]);

        if ($result->{'ResponseCode'} === '0')
        {
            SlackService::bell('New external account added successfully', [
                'Response' => $result
            ]);

            return $result->{'BankAccountID'};
        }

        SlackService::alert('An error occurred when adding a new bank account with RPN', [
            'Code' => $result->{'ResponseCode'},
            'Message' => $result->{'ResponseMessage'}
        ]);
        return 'Failure';
    }

    public static function transferFundsBetweenInternalAccounts(PTOUser $user, PTOInternalAccount $toAccount, PTOInternalAccount $fromAccount, $amount)
    {
        $userType = PTOUserType::findUserTypeByUuid($user->getUserTypeId());

//        if ($userType->getIsKybRequired() === true)
//        {
//            $vars = self::getRPNVariablesKYB();
//        } else {
//            $vars = self::getRPNVariables();
//        }

        $vars = self::getRPNVariablesKYB();

        $client = new SoapClient($vars['wsdl']);
        $result = $client->createTransferBetweenUsers([
            'ApiKey' => $vars['apiKey'],
            'DebitFromAccount' => $fromAccount->getExternalAccountNumber(),
            'CreditToAccount' => $toAccount->getExternalAccountNumber(),
            'Amount' => $amount,
            'Description' => 'User' . $user->getUuid() . ' is transferring ' . $amount . ' between their internal accounts: ' . $fromAccount->getUuid() . ' and ' . $toAccount->getUuid(),
            'TransactionStatus' => 'Executed'
        ]);

        if ($result->{'ResponseCode'} === '0')
        {
            SlackService::bell("Funds have been transferred between user's internal accounts.", [
                'Users' => $user->getUuid(),
                'From Account' => $fromAccount->getUuid(),
                'To Account' => $toAccount->getUuid(),
                'Amount' => $amount,
                'Response' => $result
            ]);
            return 'Success';
        }

        SlackService::alert('An error occurred when transferring funds.', [
            'Code' => $result->{'ResponseCode'},
            'Message' => $result->{'ResponseMessage'}
        ]);

        return 'Failure';
    }

    public static function ExternaltoInternalBankTransfer(PTOInternalAccount $ia, PTOExternalAccount $ea, PTOUser $user, $amount)
    {
        $userType = PTOUserType::findUserTypeByUuid($user->getUserTypeId());

        if ($userType->getIsKybRequired() === true)
        {
            $vars = self::getRPNVariablesKYB();
        } else {
            $vars = self::getRPNVariables();
        }

        $client = new SoapClient($vars['wsdl']);
        $result = $client->createIncomingBankTransfer([
            'ApiKey' => $vars['apiKey'],
            'AccountNumber' => $ia->getExternalAccountNumber(),
            'Amount' => $amount,
            'BankAccountID' => $ea->getExternalAccountNumber(),
            'Description' => 'Incoming Transfer'
        ]);

        if ($result->{'ResponseCode'} === '0')
        {
            SlackService::bell("A user has transferred funds from an external account to an internal account.", [
                'Users' => $user->getUuid(),
                'From Account' => $ea->getUuid(),
                'To Account' => $ia->getUuid(),
                'Amount' => $amount,
                'Response' => $result
            ]);
            return 'Success';
        }

        SlackService::alert('An error occurred when creating an incoming bank transfer.', [
            'Code' => $result->{'ResponseCode'},
            'Message' => $result->{'ResponseMessage'}
        ]);

        return 'Failure';
    }

    public static function InternalToExternalBankTransfer(PTOInternalAccount $ia, PTOExternalAccount $ea, PTOUser $user, $amount)
    {
        $userType = PTOUserType::findUserTypeByUuid($user->getUserTypeId());

        if ($userType->getIsKybRequired() === true)
        {
            $vars = self::getRPNVariablesKYB();
        } else {
            $vars = self::getRPNVariables();
        }

        $client = new SoapClient($vars['wsdl']);
        $result = $client->createOutgoingBankTransfer([
            'ApiKey' => $vars['apiKey'],
            'AccountNumber' => $ia->getExternalAccountNumber(),
            'Amount' => $amount,
            'BankAccountID' => $ea->getExternalAccountNumber(),
            'Description' => 'Outgoing Transfer'
        ]);

        if ($result->{'ResponseCode'} === '0')
        {
            SlackService::bell("A user has transferred funds from an internal account to an external account.", [
                'Users' => $user->getUuid(),
                'From Account' => $ea->getUuid(),
                'To Account' => $ia->getUuid(),
                'Amount' => $amount,
                'Response' => $result
            ]);
            return 'Success';
        }

        SlackService::alert('An error occurred when creating an outgoing bank transfer.', [
            'Code' => $result->{'ResponseCode'},
            'Message' => $result->{'ResponseMessage'}
        ]);

        return 'Failure';
    }

    public static function transferFundsBetweenUsers(PTOUser $fromUser, PTOUser $toUser, $amount)
    {
        $userType = PTOUserType::findUserTypeByUuid($fromUser->getUserTypeId());

//        if ($userType->getIsKybRequired() === true)
//        {
//            $vars = self::getRPNVariablesKYB();
//        } else {
//            $vars = self::getRPNVariables();
//        }

        $vars = self::getRPNVariablesKYB();

        $client = new SoapClient($vars['wsdl']);
        $result = $client->createTransferBetweenUsers([
            'ApiKey' => $vars['apiKey'],
            'DebitFromAccount' => $fromUser->getExternalAccountId(),
            'CreditToAccount' => $toUser->getExternalAccountId(),
            'Amount' => $amount,
            'Description' => $fromUser->getExternalId() . ' is transferring ' . $amount . ' to ' . $toUser->getExternalId(),
            'TransactionStatus' => 'Executed'
        ]);

        if ($result->{'ResponseCode'} === '0')
        {
            SlackService::bell('Funds have been transferred between users.', [
                'Users' => [
                    'fromUser' => $fromUser->getUuid(),
                    'toUser' => $toUser->getUuid()
                ],
                'Amount' => $amount,
                'Response' => $result
            ]);
            return 'Success';
        }

        SlackService::alert('An error occurred when transferring funds.', [
            'Code' => $result->{'ResponseCode'},
            'Message' => $result->{'ResponseMessage'}
        ]);

        return 'Failure';
    }

    public static function getAccountBalance(PTOInternalAccount $internalAccount)
    {
        $user = PTOUser::findUserByUuid($internalAccount->getUser());
        $userType = PTOUserType::findUserTypeByUuid($user->getUserTypeId());

        if ($userType->getIsKybRequired() === true)
        {
            $vars = self::getRPNVariablesKYB();
        } else {
            $vars = self::getRPNVariables();
        }

        $client = new SoapClient($vars['wsdl']);
        $result = $client->getAccountBalance([
           'ApiKey' => $vars['apiKey'],
           'AccountNumber' => $internalAccount->getExternalAccountNumber()
        ]);

        if ($result->{'ResponseCode'} === '0')
        {
            SlackService::info('Internal account balance has been retrieved.', [
                'Internal Account' => $internalAccount->getUuid()
            ]);

            return [
                'currentBalance' => $result->{'CurrentBalance'},
                'availableBalance' => $result->{'AvailableBalance'}
            ];
        }

        SlackService::alert('Internal account failed to retrieve balance.', [
            'Internal Account' => $internalAccount->getUuid(),
            'Response Message' => $result->{'ResponseMessage'}
        ]);

        return 'Failure';
    }

    public static function envTest()
    {
        $vars = self::getRPNVariables();
        return $vars['isDev'];
    }
}
