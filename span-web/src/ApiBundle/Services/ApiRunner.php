<?php


namespace ApiBundle\Services;


use CoreBundle\Entity\UserToken;
use CoreBundle\Exception\FailedException;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Log;
use CoreB<PERSON>le\Utils\Util;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Exception\RequestException;
use PortalBundle\Exception\PortalException;
use SalexUserBundle\Entity\User;
use Symfony\Component\HttpFoundation\Response;

class ApiRunner
{
    /**
     * Don't expose it to the frontend
     */
    public const INNER_INVOKER = '<EMAIL>';

    /**
     * Public accessible with no access
     */
    public const NONE_INVOKER = '<EMAIL>';

    public static function getInnerInvoker()
    {
        return Util::em()->getRepository(User::class)
            ->findOneBy([
                'email' => self::INNER_INVOKER,
            ]);
    }

    public static function getNoneInvoker()
    {
        return Util::em()->getRepository(User::class)
            ->findOneBy([
                'email' => self::NONE_INVOKER,
                'source' => 'system',
            ]);
    }

    public static function isInnerInvoker($user = null)
    {
        $user = $user ?? Util::invoker();
        return $user->getEmail() === self::INNER_INVOKER;
    }

    public static function isNoneInvoker(User $user)
    {
        if ($user->getEmail() !== self::NONE_INVOKER) {
            return false;
        }
        $none = self::getNoneInvoker();
        return Util::eq($user, $none);
    }

    public static function getNoteById(User $user)
    {
        $by = Util::invoker();
        if (self::isInnerInvoker($by)) {
            $by = $user;
        }
        return $by->getId();
    }

    /**
     * Call with API access key & signature
     *
     * @param        $url
     * @param string $method
     * @param array  $params
     * @param array  $headers
     * @param bool   $parse
     *
     * @return FailedResponse|SuccessResponse|mixed
     * @throws GuzzleException
     * @throws PortalException
     */
    public static function run($url, $method = 'GET', $params = [], $parse = false, $headers = [])
    {
        $ut = null;

//        if (Util::isDevDevice()) {
//            // api invoker on the staging server
//            $ut = new UserToken();
//            $ut->setToken('2879d250-1b6a-af88-8850-f7f7b973f354')
//                ->setSecret('d9606708-f8d3-4757-f87a-c3c99b494748');
//        }

        if (!$ut) {
            $invokerEmail = Util::getParameter('api_runner_user', true) ?? self::INNER_INVOKER;
            /** @var User $invoker */
            $invoker = Util::em()->getRepository(User::class)->findOneBy([
                'email' => $invokerEmail,
            ]);
            if (!$invoker) {
                throw PortalException::temp('Unknown API Invoker!');
            }
            $ut = UserToken::instance($invoker, null, UserToken::SCOPE_API);
            UserToken::updateUseTime($ut);
        }

        $uri = self::getHost() . Util::ensurePrefix($url, '/api');
        $builtins = self::createSignatureHeaders($ut, $uri, $method, $params);
        $response = static::request($url, $method, $params, array_merge($builtins, $headers));
        if (!$parse) {
            return $response;
        }
        $resp = json_decode($response->getContent(), true);
        if (!empty($resp['success'])) {
            return $resp['data'] ?? null;
        }
        throw new FailedException($resp['message'] ?? 'Unknown error!');
    }

    public static function createSignatureHeaders(UserToken $ut, $url, $method, array $params = [], &$debug = null)
    {
        if (strtoupper($method) === 'GET') {
            $separator = str_contains($url, '?') ? '&' : '?';
            $query = http_build_query($params);
            if ($query) {
                $url .= $separator . $query;
            }
        }

        $salt = Util::randString();
        $timestamp = time();
        $accessKey = $ut->getToken();
        $secretKey = $ut->getDecryptedSecret();
        $parts = [
            $url,
            $salt,
            $timestamp,
            $accessKey,
            $secretKey,
        ];

        if (strtoupper($method) === 'POST') {
            $body = [];
            foreach ($params as $key => $item) {
                if (is_string($item) && is_file($item)) {
                    $body[$key] = hash('md5', file_get_contents($item));
                } else {
                    $body[$key] = $item;
                }
            }
            $parts[] = http_build_query($body);
        }

        $raw = implode('', $parts);
        $signature = base64_encode(hash_hmac("sha256", $raw, $secretKey));

//        if (is_array($debug) && !Util::isLive()) {
//            $debug = compact('salt', 'timestamp', 'raw', 'parts', 'signature');
//        }

        return [
            'Accept' => 'application/json',
            'x-access-key' => $accessKey,
            'x-salt' => $salt,
            'x-timestamp' => $timestamp,
            'x-signature' => $signature,
        ];
    }

    /**
     * Call with API key
     *
     * @param        $url
     * @param string $method
     * @param array  $params
     * @param array  $headers
     *
     * @return Response
     */
    public static function call($url, $method = 'GET', $params = [], $headers = [])
    {
        // user: <EMAIL>
        $apiKey = Util::isLive()
            ? Util::getParameter('api_runner_key')
            : Util::getParameter('api_runner_key_test');

        $builtins = [
            'Accept' => 'application/json',
            'x-api-key' => $apiKey,
            'x-token' => $headers['x-token'] ?? null,
            'x-timezone' => $headers['x-timezone'] ?? null,
            'x-i18n' => $headers['x-i18n'] ?? null,
            'x-card-program' => $headers['x-card-program'] ?? null, // Needs to update to `x-platform`
        ];
        return static::request($url, $method, $params, array_merge($builtins, $headers));
    }

    /**
     * @param User   $user
     * @param        $url
     * @param string $method
     * @param array  $params
     * @param array  $headers
     *
     * @return Response
     */
    public static function callForUser(User $user, $url, $method = 'GET', $params = [], $headers = [])
    {
        $ut = UserToken::instance($user);
        $headers['x-token'] = $ut->getToken();
        UserToken::updateUseTime($ut);
        return static::call($url, $method, $params, $headers);
    }

    public static function raw(string $url, string $method = 'GET', array $params = [], array $headers = [])
    {
        if ( ! Util::isUrl($url)) {
            $url = self::getHost() . Util::ensurePrefix($url, '/');
        }
        return self::request($url, $method, $params, $headers);
    }

    /**
     * @param        $url
     * @param string $method
     * @param array  $params
     * @param array  $headers
     *
     * @return Response
     * @throws GuzzleException
     */
    protected static function request($url, $method = 'GET', $params = [], $headers = []) {
        $method = strtoupper($method);

        if ( ! Util::isUrl($url)) {
            $url = self::getHost() . Util::ensurePrefix($url, '/api');
        }

        $headers = array_filter($headers, static function ($v) {
            return $v;
        });

        $client = new Client();
        $content = null;
        try {
            $response = $client->request($method, $url, [
                'headers' => $headers,
                'query' => strtoupper($method) === 'GET' ? $params : [],
                'form_params' => strtoupper($method) !== 'GET' ? $params : [],
            ]);
            $content = $response->getBody()->getContents();
            $code = 200;
        } catch (RequestException $exception) {
            $response = $exception->getResponse();
            $code = $response ? $response->getStatusCode() : 503;
            $useful = false;
            if ($response) {
                $content = $response->getBody()->getContents();
                $cc = json_decode($content, true);
                if ($cc && array_key_exists('success', $cc)) {
                    $useful = true;
                } else if (in_array($code, [200, 400, 401, 402])) {
                    $useful = true;
                }
            }
            if (!$useful) {
                throw new FailedException($exception->getMessage());
            }
        }
        $response = new Response($content);
        $response->headers->set('Content-Type', 'application/json');
        $response->setStatusCode($code);
        return $response;
    }

    protected static function getHost()
    {
//        if (Util::isDevDevice()) {
//            return 'https://faas-test.virtualcards.us';
//        }
        return Util::host();
    }
}
