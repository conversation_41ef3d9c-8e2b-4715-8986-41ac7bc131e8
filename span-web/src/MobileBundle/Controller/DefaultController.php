<?php

namespace MobileBundle\Controller;

use Carbon\Carbon;
use CoreBundle\Entity\Email;
use CoreBundle\Entity\FAverifyType;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserPin;
use CoreBundle\Entity\UserToken;
use CoreBundle\Exception\InvalidAdminTokenException;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Services\SSLEncryptionService;
use CoreBundle\Services\UserPinService;
use CoreBundle\Services\UserService;
use CoreBundle\Utils\Bundle;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Util;
use PortalBundle\Exception\PortalException;
use PragmaRX\Google2FA\Google2FA;
use SalexUserBundle\Entity\User;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class DefaultController extends BaseController
{
    /**
     * DefaultController constructor.
     * @throws \CoreBundle\Exception\RedirectException
     * @throws \PortalBundle\Exception\PortalException
     * @throws \UnexpectedValueException
     */
    public function __construct()
    {
        parent::__construct();
        $user = $this->getUser();
        $except = $this->authExcept();
        if (!$except && !$user) {
            $msg = 'Unknown or expired token!';
            if (Util::request()->isXmlHttpRequest()) {
                Util::dj([
                    'success' => false,
                    'message' => $msg,
                    'data' => null,
                ]);
            }
            throw new InvalidAdminTokenException($msg);
        }
    }

    /**
     * @Route("/mobile")
     * @Route("/mobile/")
     * @Route("/mobile/ios")
     * @Route("/mobile/ios/")
     * @Route("/mobile/ios/index.html")
     * @Route("/mobile/mat")
     * @Route("/mobile/mat/")
     * @Route("/mobile/mat/index.html")
     * @return Response
     */
    public function matAction()
    {
        return new RedirectResponse('/admin');
    }

   /** @Route("/consumer-verify-resend",name="consumer-verify-resend")
    * @param Request $request
    *
    * @return Response
    * @throws \PortalBundle\Exception\PortalException
    */
    public function resendVerifyCodeAction (Request $request)
    {
         // Log::debug(Util::isCRSFAttack($request->headers->get('referer'), 'verify'));
        if (Util::isCRSFAttack($request->headers->get('referer'), 'verify')) {
          $msg = 'Meaning CSRF attack, referer: ' . $request->headers->get('referer');
          throw new InvalidAdminTokenException($msg);
        }
        $user = $this->getUser();
        $this->authActiveUser($user, false);
        // send verify code
        // Log::debug($user->getPhone());
        $debug = false;
        $verifyCode = '';
        $verifyType = trim($request->request->get('verifytype'));
        $setApp = $request->request->get('setApp');
        $session = $request->getSession();
        if (!$debug && Util::isDebug()) {
          $debug = true;
        }
        if ($setApp) {
          $verifyCode = Util::randNumber(6); // UserPinService::randomPin($debug);
          $session->set('set_app_verify_code_' . $user->getId(), $verifyCode);
          Email::sendWithTemplateToUser($user, Email::VERIFY_CODE, [
            'verifyCode' => $verifyCode,
          ]);
          $session->set('set_app_verify_code__time' . $user->getId(), Carbon::now()->timestamp);
          return new SuccessResponse();
        }
        if ($user->getMobilephone() && $verifyType ==='phone') {
          // $session->set('verify_code_send_type_' . $user->getId(), 'phone');
          UserPinService::create($this->getUser(), UserPin::PHONE_TYPE_MOBILE, UserPin::MSG_TYPE_SMS, false, UserPin::LOGIN, $this->platform);
        } else if ($user->getEmail() && $verifyType ==='email') {
          // $session->set('verify_code_send_type_' . $user->getId(), 'email');
          $verifyCode = Util::generate_key(32); // UserPinService::randomPin($debug);
          $session->set('email_verify_code_' . $user->getId(), $verifyCode);
          Email::sendWithTemplateToUser($user, Email::VERIFY_CODE, [
            'verifyCode' => $verifyCode,
          ]);
          $session->set('email_verify_code_time' . $user->getId(), Carbon::now()->timestamp);
        }
        return new SuccessResponse('');
    }
    /**
     * @Route("/verify",name="verify")
     * @param Request $request
     *
     * @return Response
     * @throws \PortalBundle\Exception\PortalException
     */
    public function check2FAAction (Request $request) {
        $user = $this->getUser();
        $this->authActiveUser($user, false);
        // send verify code
        // Log::debug($user->getPhone());
        $debug = false;
        $verifyCode = '';
        // $session = $request->getSession();
        if (!$debug && Util::isDebug()) {
          $debug = true;
        }
        // if (!$session->get('verify_code_send_' . $user->getId())) {
        //   if ($user->getMobilephone()) {
        //     $session->set('verify_code_send_type_' . $user->getId(), 'phone');
        //     UserPinService::create($this->getUser(), UserPin::PHONE_TYPE_MOBILE, UserPin::MSG_TYPE_SMS);
        //   } else if ($user->getEmail()) {
        //     $session->set('verify_code_send_type_' . $user->getId(), 'email');
        //     $verifyCode = Util::randNumber(6); // UserPinService::randomPin($debug);
        //     $session->set('email_verify_code_' . $user->getId(), $verifyCode);
        //     Email::sendWithTemplateToUser($user, Email::VERIFY_CODE, [
        //       'verifyCode' => $verifyCode,
        //     ]);
        //   }
        // }
        // $session->set('verify_code_send_' . $user->getId(), true);
        $platformName = $this->getPlatformName();
        return $this->render('@Portal/Default/verify.html.twig', [
          'phone' => $user->getPhone() ? '*** *** ***' . substr($user->getPhone(), -3) : null,
          'email' => $user->getEmail() ? '******' .  substr($user->getEmail(), strpos($user->getEmail(), '@')) : null,
          'verifyApp'  => true,
          'isSetupVerifyApp' => Util::meta($user, $platformName . '-twoFactorSecret') ? true : false,
          'verifytype' => Util::meta($user, $platformName . '-twoFactorType') ? Util::meta($user, $platformName . '-twoFactorType') : 'app'
        ]);
    }

    /**
     * get platform name
     */
    protected function getPlatformName() {
        $platformName = $this->platform ? $this->platform->getName() : 'Tern';
        if (Util::isStaging()) {
          $platformName = $platformName . '(Staging)';
        }
        if (Util::isDevDevice()) {
          $platformName = $platformName . '(Local)';
        }
        return $platformName;
    }
     /**
     * @Route("/setupApp", name="setup_app", methods={"POST"})
     * @param Request $request
     *
     * @return Response
     * @throws \PortalBundle\Exception\PortalException
     */
    public function setUpApp(Request $request) {
        $user = $this->getUser();
        $this->authActiveUser($user, false);
        $verifyCode = trim($request->request->get('verifycode'));
        $secret = trim($request->request->get('secret'));
        // $session = $request->getSession();
        $g = new Google2FA();
        if ($g->verify($verifyCode, $secret)) {
          $platformName = $this->getPlatformName();

          Util::updateMeta($user, [
            $platformName . '-twoFactorSecret' => SSLEncryptionService::encrypt($secret),
            'sslencryptionFlag' => true
          ]);
          $this->updateVerifyType($user, 'app');
          // $session->set('verify_code_send_' . $user->getId(), 'app');
          return new SuccessResponse();
        } else {
          throw PortalException::tempPage('Setup error, Invalid verify code!');
        }
    }
     /**
     * @Route("/consumerverify", name="consumer_verify", methods={"POST"})
     * @param Request $request
     *
     * @return Response
     * @throws \PortalBundle\Exception\PortalException
     */
    public function verifyUserAction(Request $request)
    {
        $session = $request->getSession();
        $verifyCode = trim($request->request->get('verifycode'));
        $verifyType = trim($request->request->get('verifytype'));
        $setupApp = $request->request->get('setupApp');
        $verified = false;
        /** @var User $user */
        $user = $this->getUser();
        $this->authActiveUser($user, false);
        $platformName = $this->getPlatformName();
        if ($setupApp) {
          $sessionCode = $session->get('set_app_verify_code_' . $user->getId());
          $sessionTime = $session->get('set_app_verify_code__time' . $user->getId());

          $verified = !strcmp($verifyCode, $sessionCode);
          if ($verified) {
              $g = new Google2FA();
              $secret = $g->generateSecretKey();
              $res = [
                'secret' => $secret,
                'QRL' => Util::getQrCodeUrl($g->getQRCodeUrl($this->getPlatformName(), $user->getName(), $secret)),
              ];
              return new SuccessResponse($res);
          } else {
            throw PortalException::tempPage('Invalid verify code!');
          }
        } else {
          if ($verifyType === 'phone') {
            $verified = UserPinService::verify($user, $verifyCode, 1, UserPin::LOGIN, $this->platform);
          } else if ($verifyType === 'email') {
            $sessionCode = $session->get('email_verify_code_' . $user->getId());
            $sessionTime = $session->get('email_verify_code_time' . $user->getId());
            // Log::debug($sessionTime);
            // Log::debug((Carbon::now()->timestamp - $sessionTime) <= 60 * 1);
            $verified = !strcmp($verifyCode, $sessionCode) && ((Carbon::now()->timestamp - $sessionTime) <= 60 * 5);
          } else if ($verifyType === 'app') {
            $secret = Util::meta($user, $platformName . '-twoFactorSecret');
            if (!$secret) {
                throw PortalException::tempPage('Please reset your 2FA configuration.');
            }
            $g = new Google2FA();
            if ( Util::meta($user, 'sslencryptionFlag')) {
                if (SSLEncryptionService::isEncrypted($secret)) {
                    $secret = SSLEncryptionService::decrypt($secret);
                } else {
                    Util::updateMeta($user, [
                        $platformName . '-twoFactorSecret' => SSLEncryptionService::encrypt($secret),
                    ]);
                }
            }
            $verified = $g->verify($verifyCode, $secret);
          }
          if (!$verified) {
            $verifiedFailedCount = Util::meta($user, '2FAVerifiedFailedCount') ?? 0;
            $verifiedFailedCount++;
            if ($verifiedFailedCount >= 5) {
                UserService::deactivateUser($user, 'Too many failed login attempts');
                throw PortalException::tempPage('Too many attempts with the invalid or outdated verification code. Your account has been locked!', '/logout');
            }
            Util::updateMeta($user, [
              '2FAVerifiedFailedCount' =>   $verifiedFailedCount
            ]);
            throw PortalException::tempPage('Invalid or outdated verification code!', '/verify');
          }
          $session->set('Need_2FA_' . $user->getId(), false);
          $session->set('email_verify_code_' . $user->getId(), null);
          Util::updateMeta($user, [
            '2FAVerifiedFailedCount' => 0
          ]);
          try {
            $headers = $request->headers;
            if ($headers->get('x-timezone')) {
              $user->setTimezone(new \DateTimeZone($headers->get('x-timezone')));
              Util::persist($user);
            }
          } catch (\Throwable $t) {
            Log::debug('Failed update the user timezone: ' . Util::exceptionBrief($t));
          }
          $this->updateVerifyType($user, $verifyType);
          if ($user->inTeam(Role::ROLE_TRANSFER_MEX_MEMBER)) {
            return new RedirectResponse('/mex');
          } else {
            return new RedirectResponse('/admin');
          }
        }
    }

    /**
     * updat verify type and store log
     */
    protected function updateVerifyType ($user, $verifyType) {
        $platformName = $this->getPlatformName();
        $defaultVerifyType = Util::meta($user, $platformName . '-twoFactorType');
        if (strcmp($verifyType, $defaultVerifyType)) {
          $FAverifyTypeRecord = new FAverifyType();
          $FAverifyTypeRecord->setUsers($user)
                            ->setFromType($defaultVerifyType)
                            ->setPlatform($this->platform ? $this->platform->getId() : null)
                            ->setToType($verifyType)
                            ->persist();
          Util::updateMeta($user, [
            $platformName . '-twoFactorType' => $verifyType
          ]);
        }
    }
    /**
     * @Route("/n")
     * @Route("/admin")
     * @Route("/admin/", name="homepage")
     * @param Request $request
     *
     * @return Response
     * @throws \PortalBundle\Exception\PortalException
     */
    public function newThemeAction(Request $request)
    {
        $session = $request->getSession();
        $session->migrate();
        if ($this->check2FA($request, $this->getUser())) {
          return new RedirectResponse('/verify');
        }
        if ($this->checkUpdatePassword($this->getUser())) {
          return new RedirectResponse('/account-service/reset-password');
        }
        if ($request->cookies->has('br_session')) {
            return new RedirectResponse('/');
        }

        $this->authAdmin();
        $content = self::themeContent(null, $this->platform);
        $response = new Response($content);

        Bundle::common('limitAdminDomain');

        return Util::pageResponseForPreview($response);
    }

    public static function themeContent(User $user = null, Platform $platform = null, $theme = 'mat')
    {
        $token = 'not_in_use';
        if ($user) {
            $userToken = UserToken::instance($user);
            if ($userToken) {
                UserToken::updateUseTime($userToken);
                $token = $userToken->getToken();
            }
        }

        $part = '/mobile/' . $theme . '/';
        $file = __DIR__ . '/../../../web' . $part . 'xTg8GNCBOVv4j7U.php';
        if (!file_exists($file)) {
            return 'Failed to load the admin template. Please contact support.';
        }

        $content = file_get_contents($file);
        $content = str_replace([
            'href=',
            'src=',
            '</title>',
        ], [
            'href=' . $part,
            'src=' . $part,
            '</title><script>window["x-token"]="' . $token . '";</script>'
        ], $content);

        /** @noinspection CascadeStringReplacementInspection */
        $content = str_replace([
            'href=' . $part . 'http',
            'src=' . $part . 'http',
            'href=' . $part . '"/static/',
            'src=' . $part . '"/static/',
            'href=' . $part . '"/files/',
            'src=' . $part . '"/files/',
        ], [
            'href=http',
            'src=http',
            'href="/static/',
            'src="/static/',
            'href="/files/',
            'src="/files/',
        ], $content);

        $version = Util::version();
        $content = str_replace('__VER__', $version, $content);

        if ($platform) {
            $content = str_replace([
                '__CUSTOMIZE_VER__',
                '__SUB_DOMAIN__',
            ], [
                $platform->getCustomizeVersion() . '_' . $version,
                $platform->getSubDomain(),
            ], $content);
        } else {
            $content = str_replace([
                '__CUSTOMIZE_VER__',
                '__SUB_DOMAIN__',
            ], [
                $version,
                'www',
            ], $content);
        }

        if (Util::isDemo()) {
            $content .= Util::demoLabel();
        }

        $request = Util::request();
        if ($request->get('preview') && $request->get('preview_page') === 'admin') {
            $content = preg_replace(
                '|<link rel=stylesheet href="/files/customize-.*?.css\?v=.*?">|',
                '<link rel="stylesheet" href="/static/customize-css?v=' . $version . '&' . $request->getQueryString() . '">',
                $content);
            $content = preg_replace(
                '|<script src="/files/customize-fis.js\?v=.*?"></script>|',
                '<script src="/static/customize-js?v=' . $version . '&' . $request->getQueryString() . '"></script>',
                $content);
        }

        $content .= Bundle::common('getAdminPortalHtmlSuffix', [], '');

        return $content;
    }


    /**
     * check 2FA
     */
    protected function check2FA(Request $request, $user) {
      // Log::debug('Need 2FA');
      $session = $request->getSession();
      return $session->get('Need_2FA_' . $user->getId());
    }

    /**
     * check 2FA
     */
    protected function checkUpdatePassword( $user) {
      // Log::debug('Need 2FA');
      return (Util::meta($user, 'forceUpdatePassword') || $user->isPasswordExpired())  && Util::platform()
              && Util::platform()->isTransferMex()
              && $user->inTeams([
                  Role::ROLE_TRANSFER_MEX_ADMIN,
                  Role::ROLE_TRANSFER_MEX_AGENT
              ]);
    }
}
