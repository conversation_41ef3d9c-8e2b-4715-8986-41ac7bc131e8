{% extends 'layout/consumer-login-layout.html.twig' %}
{% block page_content %}
    {% block page_content_start %}{% endblock %}

    <style>
        .login-page-inner {
            max-width: 90%;
        }

        @media (max-width: 434px) {
            .login-page-inner {
                padding-top: 20px;
            }

            .login-page-inner .cp_logo {
                max-width: 70vw !important;
                max-height: 30vw !important;
            }
        }
    </style>

 <div class="login-page-inner" style="padding-bottom: 40px">
     {% block form_logo %}
         <img src="{{ _cp_logo_tern | default('/static/img/tern_logo_new.svg') | url }}"
              alt="{{ _cp.name | default('Tern Commerce') }}" class="cp_logo"
              style="max-width: 350px; max-height: 200px; margin-bottom: 20px;">
     {% endblock %}

     <div class="box-login clearfix">
         <div class="intro">
             {% block form_title %}<h1>Login to your account</h1>{% endblock %}
             {% block form_subtitle %}
                 <p>Please enter your login and password to access your account. Remember, your password is case sensitive.</p>
                 {% if _cp_deplay_subtitle_2 %} <p>If you need to set up your cardholder account, click the "forgot password?" link and an email will be sent to the address used during program enrollment.</p>   {% endif %}
             {% endblock %}
         </div>
         {% if error %}
             <div class="alert alert-danger alert-dismissible">{{ error|replace({'[':'',']':'',',':'\n'})|nl2br }}</div>
         {% endif %}
         <form id="form-login" class="form-login" action="{{ path("consumer_check") }}" method="POST"
               onsubmit="return onSubmit()">
             <input type="hidden" name="_csrf_token"
                    value="{{ csrf_token('authenticate') }}"
             >
             <input type="hidden" name="g-recaptcha-response">
             {% if goto is defined %}
                <input type="hidden" name="goto" value="{{ goto | default }}">
             {% endif %}

             {% block page_form_username %}
                 <p>
                     <input type="text" id="username" name ="username" value="{{ last_username }}" style="margin-top: 15px;"
                            placeholder="Input your email{% if not otherPlatforms() %} or username{% endif %}"
                            required="required">
                 </p>
             {% endblock %}

             {% block page_form_password %}
                 <p>
                     <input type="password" id="password" name="password" placeholder="Input your password"
                            required="required" autocomplete="off" style="margin-top: 10px;">
                 </p>
             {% endblock %}

             {% block page_form_action %}
                 <p class="text-right font-14"><a href="/login/reset-password" class="fg-pass">Forgot password?</a></p>

                 {% if usu() %}
                     <div class="font-14">
                         <div class="mt-10">
                             {% if _cpp %}
                                 <label for="terms_conditions" class="normal">
                                     <input type="hidden" name="legal_fields[]"
                                            value="terms_conditions" />
                                     <input type="checkbox" id="terms_conditions"
                                            name="terms_conditions" class="required_terms mr-3" />
                                     I accept the {{ _cpp.name }}
                                 </label>
                                 <a href="https://www.usunlocked.com/terms-of-service/" class="underline"
                                    target="_blank" rel="noopener">Terms of Service</a> and the
                                 <a href="https://www.usunlocked.com/privacy-policy/" class="underline"
                                    target="_blank" rel="noopener">Privacy Policy</a>
                             {% else %}
                                 By logging in, you agree to the
                                 <a href="https://www.usunlocked.com/terms-of-service/" class="underline"
                                    target="_blank" rel="noopener">Terms & Conditions</a>
                             {% endif %}
                             .
                         </div>

                         <div>
                             <label class="normal">
                                 <input type="checkbox" id="remember_me" name="_remember_me" class="mr-3"> Remember me
                             </label>
                         </div>
                     </div>
                 {% else %}
                     <div class="mt-10 font-14">
                         <label class="normal">
                             <input type="checkbox" id="remember_me" name="_remember_me" class="mr-3"> Remember me
                         </label>
                     </div>
                 {% endif %}

                 <p class="for-submit mt-10">
                     <input type="submit" class="btn" value="LOG IN" />
                 </p>

                 {% if dev(true) or (usu() and staging(true)) %}
                     <div class="mv-10 text-center font-12">
                         By logging in, you agree to our
                         <a href="/sandbox-terms-of-service" target="_blank" class="underline">
                             Terms & Conditions of Sandbox environment
                         </a>.
                     </div>
                 {% endif %}

                 {% if platform() and not otherPlatforms() %}
                     <p class="extra text-center">
                        <span class="extra text-center">Don't have account?
                            <a href="/choose-card" style="font-weight:bold;">Sign up now</a></span>
                     </p>
                 {% endif %}
             {% endblock %}
             </form>
         </div>
    </div>

    {% if otherPlatforms() and not spendr() %}
        {% block form_under %}
            <p class="text-center" style="font-size: 13px">
                <a style="cursor: pointer; color: #666 !important;" href="https://www.ternitup.com" target="_blank">
                    Powered by
                    <img src="/static/img/tern_logo_new.svg" style="height: 15px; margin: -5px auto auto 2px;" alt=""/>
                </a>
            </p>

            <style>
                .box-login {
                    padding-bottom: 35px;
                }
            </style>
        {% endblock %}
    {% endif %}

 {% block page_content_end %}{% endblock %}
    {% set recaptchaKey = util('getConfigKey', ['recaptcha_frontend', true]) %}
    <script src="https://www.recaptcha.net/recaptcha/api.js?render={{ recaptchaKey }}&badge=bottomleft"></script>
    <script>
        function onSubmit() {
          const notChecked = $('#form-login').find('input.required_terms:not(:checked)').length
          if (notChecked) {
            swal('Error', 'You must accept the terms and conditions to continue.');
            return false;
          }
          grecaptcha.ready(function() {
            swal({
              title: '',
              text: 'Logging in...',
              showConfirmButton: false,
              allowEscapeKey: false,
              allowOutsideClick: false
            });
            grecaptcha.execute('{{ recaptchaKey }}', {action: 'login'}).then(function(token) {
              var form = $('#form-login');
              form.find('[name="g-recaptcha-response"]').val(token);
              form[0].submit();
            }).catch(function () {
              swal('Error', 'Failed to load reCAPTCHA. Please refresh the page and try again.');
            });
          });
          return false;
        }
    </script>

    <script defer>
      localStorage.removeItem('__u');
      localStorage.removeItem('__p');
      localStorage.removeItem('timezone_ts');
      localStorage.removeItem('balance_ts');
    </script>
{% endblock %}
