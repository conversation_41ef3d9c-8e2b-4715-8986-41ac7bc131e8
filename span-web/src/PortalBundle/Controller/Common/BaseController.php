<?php
/**
 * Created by PhpStorm.
 * User: hans
 * Date: 2017/4/28
 * Time: 上午11:17
 */

namespace PortalBundle\Controller\Common;


use Carbon\Carbon;
use CoreBundle\Constant\CardProgramStatus;
use CoreBundle\Entity\Affiliate;
use CoreB<PERSON>le\Entity\AffiliateMaterial;
use CoreBundle\Entity\Attachment;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\Config;
use CoreBundle\Entity\Email;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\ReportSchedule;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Entity\UserPinLog;
use CoreBundle\Entity\UserToken;
use CoreBundle\Exception\RedirectException;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Util;
use Detection\MobileDetect;
use Doctrine\ORM\QueryBuilder;
use FaasBundle\FaasBundle;
use PortalBundle\Exception\DeniedException;
use PortalBundle\Exception\PortalException;
use SalexUserBundle\Entity\User;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use UsUnlockedBundle\Exception\UnsupportedBrowserException;

class BaseController extends \CoreBundle\Controller\BaseController
{
    /**
     * BaseController constructor.
     * @throws \UnexpectedValueException
     * @throws \CoreBundle\Exception\RedirectException
     * @throws \PortalBundle\Exception\PortalException
     */
    public function __construct()
    {
        parent::__construct();

        $request = Util::request();
        $uri = $request->getRequestUri();
        if (!$this instanceof \ApiBundle\Controller\BaseController
            && !$this instanceof \AdminBundle\Controller\BaseController
            && !$this instanceof \CoreBundle\Controller\Cron\BaseController
            && !Util::user())
        {
            if (!Util::startsWith($uri, '/static/customize-css')) {
                $affId = Affiliate::getAffIdFromRequest($request);
                if ($affId) {
                    $affiliate = Util::em()->getRepository(\CoreBundle\Entity\Affiliate::class)->findOneBy([
                        'affId' => $affId,
                    ]);
                    if ($affiliate) {
                        $affiliate->hit();
                    }
                }
            }
        }

        if (Util::startsWith($uri, '/admin/')/* && !Util::isDev()*/) {
            Log::debug('Admin portal request', Util::jsonRequest($request, true));
        } else if (Util::hasPrefix($uri, ['/t/', '/dev/'])) {
            if (!Util::hasPrefix($uri, [
                '/t/service-log',
                '/t/filter-log',
                '/t/replace-log',
                '/t/line-log',
                '/t/write-log',
                '/t/cron/email-batch',
                '/t/cron/send-email/',
                '/t/cron/update-email-details',
                '/t/cron/unlock-users',
                '/t/cron/us/velocity-check-uct/',
                '/t/cron/us/velocity-check-ucd/',
            ])) {
                Log::debug('Dev/test/cron request', Util::jsonRequest($request, true));
            }
        } else if (Util::isAPI()) {
            if (!Util::hasPrefix($uri, [
                // TransferMex
                '/mex/m/user/home',
                '/mex/m/user/profile',
                '/mex/m/user/webview/urls',
                '/mex/m/user/storeToken',
                '/mex/m/transaction/list',

                // Spendr
                '/spendr/m/consumer/transaction/list',
                '/spendr/m/merchant/transactions/scan-status',
                '/spendr/m/merchant/transactions/status',
            ])) {
                Log::debug('Public or mobile app request', Util::jsonSimpleRequest($request));
            }
        }

        $ut = $this->getApiUserToken();
        if ($ut) {
            $ut->tryRenew();
        }
    }

    public function authExcept()
    {
        $path = $this->request->getPathInfo();
        foreach ([
            '/js/',
            '/attachments/',
            '/provinces',
            '/card-programs',
        ] as $prefix) {
            if (Util::startsWith($path, $prefix)) {
                return true;
            }
        }
        return false;
    }

    public function auth()
    {
        if ($this->authExcept()) {
            return null;
        }
        return $this->authForce();
    }

    public function authForce()
    {
        $user = $this->getUser();
        if (!$user) {
            header('Location: /login?goto=' . $this->request->getPathInfo());
            die;
        }
        return $user;
    }

    public function authActiveUser(User $user, bool $alert = true)
    {
        if (!$user->isActive() && !$user->isReservedAdmin()) {
            throw new PortalException('Your account is not active anymore. Please contact support or admin.',
                '/logout',
                persist: $alert);
        }
        return $user;
    }

    public function authSuperAdminOrAdmins(array $roles)
    {
        $user = $this->authForce();
        if ($user->isSuperAdmin()) {
            return $this->authActiveUser($user);
        }
        if (!$user->inTeams($roles)) {
            throw new DeniedException();
        }
        return $this->authActiveUser($user);
    }

    public function authAdmin()
    {
        if ($this->authExcept()) {
            return null;
        }
        $user = $this->auth();
        if (!$user->hasNonConsumerRole() && !FaasBundle::isConsumerRoleAllowed()) {
            throw new PortalException('Permission denied');
        }
        if (!$this->authIP($user)) {
            throw new PortalException('Access restricted.');
        }

        // Auth platform accessing according to sub domain first
        if ($user->needAuthPlatform()) {
            $platform = Util::platform();
            if ($platform) {
                if (!$user->authPlatform($platform)) {
                    throw new PortalException('No access to platform "' . $platform->getName() . '"!');
                }

                $cps = $user->getOpenCardPrograms(true, null, true);
                if ($cps->isEmpty()) {
                    throw new PortalException('No access to any card programs on platform "' . $platform->getName() . '"!');
                }

                $opened = $cps->filter(function (CardProgram $cp) {
                    return !$cp->isSuspended();
                });
                if ($opened->isEmpty()) {
                    throw new PortalException('Temporarily suspended account access!');
                }
            }
        }

        return $this->authActiveUser($user);
    }

    public function authIP(User $user)
    {
        if (!Util::isDev() && !Util::isCronJob() && !Util::isCommand() && $user->needIpRestriction()) {
            if (!$user->isInIpWhitelisted()) {
                return false;
            }
        }
        return true;
    }

    public function authSuperAdmin()
    {
        $user = $this->authAdmin();

        // trick when developing in frontend with vue
        if (gethostname() === 'hans-mac' &&
            in_array(Util::host(), ['http://span.hans', 'https://span.local']) &&
            Util::startsWith(static::class, 'DevBundle')) {
            return true;
        }

        if (!$user->isSuperAdmin()) {
            throw new PortalException('Permission denied...');
        }

        return $user;
    }

    public function authRoles($roleNames)
    {
        $user = $this->getUser();
        $this->authActiveUser($user);

        if ($user->isSuperAdmin()) {
            return;
        }

        if (!in_array($user->getTeamName(), $roleNames)) {
            throw new DeniedException();
        }
    }

    public function own($entity, $throw = true)
    {
        $user = $this->getUser();
        if ($user->isSuperAdmin()) {
            return true;
        }
        if ($entity instanceof User) {
            if (Util::eq($user, $entity)) {
                return true;
            }
            if ($entity->isSuperAdmin()) {
                if (!$throw) {
                    return false;
                }
                throw new PortalException('Access denied!');
            }
            if ($entity->hasNonConsumerRole()) {
                if ($user->isPlatformOwner() && $entity->isPlatformRelated()) {
                    return true;
                }
                if (!$throw) {
                    return false;
                }
                throw new PortalException('Access denied!');
            }
            // Check card program
            $accessible = $user->getOpenCardPrograms();
            $participates = $entity->getParticipatedCardPrograms();
            $intersect = Util::intersect($accessible, $participates);
            if (!$intersect) {
                if (!$participates && $user->isConsumerServiceAgent()) {
                    return true;
                }

                if (!$throw) {
                    return false;
                }
                throw new PortalException('Access denied!');
            }
        } else if ($entity instanceof UserCard) {
            if ($user->isConsumerServiceAgent()) {
                return true;
            }
            if ($user->isPlatformRelated()) {
                $cp = $entity->getCardProgram();
                $cps = $user->getOpenCardPrograms();
                if (Util::includes($cps, $cp)) {
                    return true;
                }
            }
            $entityUser = $entity->getUser();
            if (!Util::eq($user, $entityUser)) {
                if (!$throw) {
                    return false;
                }
                throw new PortalException('Access denied!');
            }
        } else if ($entity instanceof UserCardLoad) {
            if ($user->hasNonConsumerRole()) {
                return true;
            }
            $uc = $entity->getUserCard();
            if (!$uc) {
                return false;
            }
            return Util::eq($uc->getUser(), $user);
        } else if ($entity instanceof AffiliateMaterial) {
            $owner = $entity->getOwner();
            if (!$owner) {
                return true;
            }
            $accessible = $user->getOpenCardPrograms();
            if (!Util::includes($accessible, $owner)) {
                if (!$throw) {
                    return false;
                }
                throw new PortalException('Access denied!');
            }
        } else if ($entity instanceof ReportSchedule) {
            $owner = $entity->getOwner();
            if ($owner && !Util::eq($owner, $user)) {
                if (!$throw) {
                    return false;
                }
                throw new PortalException('Access denied!');
            }
        } else if ($entity instanceof Email) {
            $rs = $entity->getRecipients();
            if (strpos($user->getEmail(), $rs) === false) {
                if (!$throw) {
                    return false;
                }
                throw new PortalException('Access denied!');
            }
        } else if ($entity instanceof CardProgram) {
            $accessible = $user->getOpenCardPrograms();
            if (!Util::includes($accessible, $entity)) {
                if (!$throw) {
                    return false;
                }
                throw new PortalException('Access denied!');
            }
        } else if ($entity instanceof Platform) {
            $accessible = $user->getOpenPlatforms();
            if (!Util::includes($accessible, $entity)) {
                if (!$throw) {
                    return false;
                }
                throw new PortalException('Access denied!');
            }
        }
        return true;
    }

    public function authPin()
    {
        $user = $this->auth();
        if ($user->isSuperAdmin() || Util::isDev()) {
            return;
        }
        $q = Util::em()->getRepository(\CoreBundle\Entity\UserPinLog::class)
            ->createQueryBuilder('upl');
        $e = $q->expr();
        $rs = $q->where($e->andX(
            $e->eq('upl.user', $user->getId()),
            $e->eq('upl.type', ':type'),
            $e->eq('upl.status', ':status'),
            $e->gt('upl.createdAt', ':time')
        ))
            ->setParameters([
                'type'   => UserPinLog::TYPE_CHECK,
                'status' => UserPinLog::STATUS_SUCCESS,
                'time'   => Carbon::now()->subMinute(),
            ])
            ->orderBy('upl.createdAt', 'DESC')
            ->setMaxResults(1)
            ->getQuery()
            ->getResult();
        if (!$rs) {
            throw new PortalException('PIN session expired. Please re-validate!',
                '/card-management?validate=true');
        }
    }

    protected function render($view, array $parameters = array(), Response $response = null): Response
    {
        if (empty($parameters['_menu'])) {
            $menu = '';
            $matches = [];
            preg_match('/Default[:\/](.+?)[:\/].+\.html.twig/', $view, $matches);
            if (count($matches) >= 2) {
                $menu = '/' . $matches['1'];
            } else if (Util::endsWith($view, 'index.html.twig')) {
                $menu = '/';
            }
            $parameters['_menu'] = $menu;
        }

        return parent::render($view, $parameters, $response);
    }

    public function checkBrowser()
    {
        $md = new MobileDetect();
        $ieVer = $md->version('IE', MobileDetect::VERSION_TYPE_FLOAT);
        if ($ieVer) {
            throw new UnsupportedBrowserException();
        }
    }

    public function queryByDateRange(QueryBuilder $query, $timeField, Request $request = null)
    {
        $request = $request ?: $this->request;

        $startTime = $request->get('start');
        if ($startTime) {
            $query->andWhere($timeField . ' >= :__startTime')
                ->setParameter('__startTime', Util::timeUTC($startTime));
        }

        $endTime = $request->get('end');
        if ($endTime) {
            $query->andWhere($timeField . ' < :__endTime')
                ->setParameter('__endTime', Util::timeUTC($endTime)->addDay());
        }

        // Multiple support
        $starts = json_decode($request->get('start`', '[]'), true);
        $ends = json_decode($request->get('end`', '[]'), true);
        if ($starts && $ends) {
            $expr = Util::expr();
            $ors = [];
            foreach ($starts as $i => $start) {
                $ands = [];
                if ($start) {
                    $ands[] = $expr->gte($timeField, ':__startTime_' . $i);
                    $query->setParameter('__startTime_' . $i, Util::timeUTC($start));
                }
                $end = $ends[$i] ?? null;
                if ($end) {
                    $ands[] = $expr->lt($timeField, ':__endTime_' . $i);
                    $query->setParameter('__endTime_' . $i, Util::timeUTC($end)->addDay());
                }
                if ($ands) {
                    $ors[] = call_user_func_array([$expr, 'andX'], $ands);
                }
            }
            if ($ors) {
                $query->andWhere(call_user_func_array([$expr, 'orX'], $ors));
            }
        }

        return $query;
    }

    public function queryCowByDateRange(QueryBuilder $query, $timeField,  Request $request = null)
    {
        $period  = $request->get('period', 'all');
        if ($period != 'custom_range' && $period != 'today' && $period != 'week' && $period != 'month' && $period != 'quarter' && $period != 'year') {
          return $query;
        }

        $startTime = $request->get('start');

        if ($startTime) {
            switch($period) {
              case 'today':
                $start = Util::timeUTC($startTime)->subDay();
                break;
              case 'week':
                $start = Util::timeUTC($startTime)->subWeek();
                break;
              case 'month':
                $start = Util::timeUTC($startTime)->subMonthWithoutOverflow();
                break;
              case 'quarter':
                $start = Util::timeUTC($startTime)->subQuarterWithoutOverflow();
                break;
              case 'year':
                $start = Util::timeUTC($startTime)->subYearWithoutOverflow();
                break;
              default:
                $start =  Util::timeUTC($startTime);
                break;
            }
            $query->andWhere($timeField . ' >= :__startTime')
                ->setParameter('__startTime', $start);
        }

        $endTime = $request->get('end');
        if ($endTime) {
            switch($period) {
              case 'today':
                $end = Util::timeUTC($endTime)->subDay()->addDay();
                break;
              case 'week':
                $end = Util::timeUTC($endTime)->subWeek()->addDay();
                break;
              case 'month':
                $end = Util::timeUTC($endTime)->subMonthWithoutOverflow()->addDay();
                break;
              case 'quarter':
                $end = Util::timeUTC($endTime)->subQuarterWithoutOverflow();
                break;
              case 'year':
                $end = Util::timeUTC($endTime)->subYearWithoutOverflow();
                break;
              default:
                $end =  Util::timeUTC($endTime);
                break;
            }
            $query->andWhere($timeField . ' < :__endTime')
                ->setParameter('__endTime', $end);
        }
        return $query;
    }

    /**
     * Get clinet list
     */
    protected function listForSelection($role, $isPrefund = false)
    {
        $q = Util::em()->getRepository(User::class)
            ->createQueryBuilder('u')
            ->join('u.teams', 't')
            ->join('u.config', 'config')
            ->join('config.group', 'g')
            ->where(Util::expr()->in('t.name', ':roles'))
            ->setParameter('roles', [
                $role,
            ]);
        $query = $this->queryAdminsInCurrentPlatform($q);
        if ($isPrefund) {
          $query->andwhere(Util::expr()->eq('g.fundingType', ':prefund'))
                ->setParameter('prefund', 'Prefunded');
        }
        return $query->orderBy('g.name')
            ->distinct()
            ->select('u.id as value, g.name as label')
            ->getQuery()
            ->getArrayResult();
    }

    protected function logLine($msg, $detail = [], $type = 'debug')
    {
        $msg = $this->request->getRequestUri() . ' ---- ' . $msg;
        Log::$type($msg, $detail);
    }

    protected function logExceptionLine($msg, $exception, $detail = [])
    {
        $msg = $this->request->getRequestUri() . ' ---- ' . $msg;
        Log::exception($msg, $exception, $detail);
    }

    public function queryUserOtherInfo(QueryBuilder $q, Request $request = null)
    {
        $this->queryOtherInfoFilter($q, $request);
        $this->queryOtherInfoNotFilter($q, $request);
        return $q;
    }

    protected function queryOtherInfoFilter($q, $request)
    {
        $filterRanges = $request->get('otherInfo');
        if (is_array($filterRanges)) {
          $expr = $q->expr();
          foreach($filterRanges as $key => $item) {
            if ($key != 'start' && $key !='end') {
              if(isset($item['min'])) {
                $v = $item['min'];
                if (stripos($key, 'total') !== false || stripos($key, 'avg') !== false) {
                  $v *= 100;
                }

                if (stripos($key, 'last') !== false) {
                  $v = "'" . Util::timeUTC($v) . "'";
                }
                $q->andWhere($expr->gte($key, $v));
              }
              if (isset($item['max'])) {
                $v = $item['max'];
                if (stripos($key, 'total') !== false || stripos($key, 'avg') !== false) {
                  $v *= 100;
                }
                if (stripos($key, 'last') !== false) {
                  $v = "'" . Util::timeUTC($v) . "'";
                }
                $q->andWhere($expr->lte($key,  $v));
              }
            }
          }
        }
        return $q;
    }

    protected function queryOtherInfoNotFilter($q, $request)
    {
      $not = $request->get('__not');
        if ($not) {
            if (is_string($not)) {
              $not = (array)json_decode($not, TRUE);
            }
            $filters = [];
            foreach ($not as $i => $n) {
              if (!Util::startsWith($i, 'otherInfo')) {
                continue;
              }
              if (is_array($n)) {
                $name = mb_substr($i, 10);
                $filters[$name] = $n;
              }
            }

            $expr = $q->expr();
            foreach ($filters as $key => $value) {
                if ($value === '' || $value === NULL) {
                    continue;
                }
                if (Util::endsWith($key, '`')) {
                    $key = substr($key, 0, -1);
                    $value = json_decode($value, true);
                }
                $neq = false;
                if (Util::endsWith($key, '=')) {
                    $key = substr($key, 0, -1);
                    $neq = true;
                }
                if (is_array($value)) {
                    if ($neq) {
                        $q->andWhere($expr->notIn($key, $value));
                    } else {
                        $orx = [];
                        foreach ($value as $k => $v) {
                            if ($k == 'min') {
                              if (stripos($key, 'total') !== false || stripos($key, 'avg') !== false) {
                                $v *= 100;
                              }
                              if (stripos($key, 'last') !== false) {
                                $v = "'" . Util::timeUTC($v) . "'" ;
                              }
                              $orx[] = $expr->lt($key, $v);
                            } else if ($k == 'max') {
                              if (stripos($key, 'total') !== false || stripos($key, 'avg') !== false) {
                                $v *= 100;
                              }
                              if (stripos($key, 'last') !== false) {
                                $v = "'" . Util::timeUTC($v) . "'";
                              }
                              $orx[] = $expr->gt($key, $v);
                            } else {
                              $orx[] = $expr->notLike($key, "'%" . $v . "%'");
                            }
                        }
                        $q->andWhere(call_user_func_array([$expr, 'orX'], $orx));
                    }
                } else {
                    $q->andWhere($expr->neq($key, $value));
                }
            }
        }
        return $q;
    }
}
