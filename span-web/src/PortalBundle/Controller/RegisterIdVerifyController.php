<?php
/**
 * Created by PhpStorm.
 * User: hans
 * Date: 2017/4/11
 * Time: 下午1:26
 */

namespace PortalBundle\Controller;


use Carbon\Carbon;
use CoreBundle\Constant\IdentityType;
use CoreBundle\Entity\CardProgramCardType;
use CoreBundle\Entity\Country;
use CoreBundle\Entity\UserIdVerify;
use CoreBundle\Exception\RedirectException;
use CoreBundle\Response\ErrorResponse;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Services\APIServices\NetVerifyService;
use CoreBundle\Utils\Util;
use FOS\RestBundle\Controller\Annotations\Route;
use SalexUserBundle\Entity\User;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class RegisterIdVerifyController extends RegisterController
{
    /**
     * @Route("/consumer-register/id-verification",name="consumer_register_id_verification")
     * @param Request $request
     * @return ErrorResponse|Response
     * @throws \PortalBundle\Exception\PortalException
     */
    public function IDVerifyAction(Request $request)
    {
        /** @var User $user */
        $user = $this->getUser();
        if (!$user) {
            return $this->redirect('/choose-card');
        }
        if (!$user->getEmailVerified()) {
            return $this->redirect('/consumer-register/confirm-email');
        }
        $upgradeCard = $request->get('upgrade');
        if ($upgradeCard) {
            $uc = Util::em()->getRepository(\CoreBundle\Entity\UserCard::class)->find($upgradeCard);
            /** @var CardProgramCardType $cpCard */
            $cpCard = $uc->getCard();
            $card = $cpCard->getUpgradeTargetCard();
            if (!$card) {
                if ($uc->getUpgradeFrom()) {
                    return new ErrorResponse('Your card has been upgraded.');
                }
                return new ErrorResponse('Your card cannot be upgraded!');
            }
        } else {
            $uc = $user->getLoadingCard();
            if (!$uc) {
                return $this->redirect('/choose-card');
            }
            $card = $uc->getCard();
//            if ($uc->isUsUnlockedLegacy() && !$card->isReloadable()) {
//                return new ErrorResponse('Non-Reloadable card don\'t need ID verification!', '/consumer-register/card-load?startup');
//            }
        }
        if ($uc->isUsUnlocked()) {
            $uiv = $user->getIdVerify();
            $defaultCountryId = $user->getCountryid();
            if ($uiv && $uiv->getCountry()) {
                $defaultCountryId = $uiv->getCountry()->getId();
            }
            return $this->render('@Portal/Register/id-verify-new.html.twig', [
                'userCard' => $uc,
                'reloadable' => true,
                'uiv' => $uiv,
                'welcome' => $request->get('welcome'),
                'defaultCountryId' => $defaultCountryId,
                'countryList' => Util::em()->getRepository(\CoreBundle\Entity\Country::class)
                    ->getCountriesWithCardPrograms([
                        $uc->getCardProgram()->getId(),
                    ]),
            ]);
        }

        if ($uc->isCashOnWeb()) {
            $uiv = $user->getIdVerify();
            if ($user->isIdVerified()) {
                return $this->redirect('/p');
            }

            $defaultCountryId = $user->getCountryid();
            if ($uiv && $uiv->getCountry()) {
                $defaultCountryId = $uiv->getCountry()->getId();
            }
            // twice KYC failed
            // var_dump($user->getIdVerifies());die();
            if (count($user->getIdVerifies()) >= 2) {
              if (Util::meta($uiv, 'pending')) {
                return new ErrorResponse('Your Documents are Pending Review. You’ll receive an email notification once you eligibility has been determined.');
              }
              return $this->render('@Portal/Register/id-post-new.html.twig', [
                'userCard' => $uc,
                'reloadable' => true,
                'uiv' => $uiv,
                'welcome' => $request->get('welcome'),
                'defaultCountryId' => $defaultCountryId,
                'countryList' => Util::em()->getRepository(\CoreBundle\Entity\Country::class)
                    ->getCountriesWithCardPrograms([
                        $uc->getCardProgram()->getId(),
                    ]),
            ]);
            }

            return $this->render('@Portal/Register/id-verify-new.html.twig', [
                'userCard' => $uc,
                'reloadable' => true,
                'uiv' => $uiv,
                'welcome' => $request->get('welcome'),
                'defaultCountryId' => $defaultCountryId,
                'countryList' => Util::em()->getRepository(\CoreBundle\Entity\Country::class)
                    ->getCountriesWithCardPrograms([
                        $uc->getCardProgram()->getId(),
                    ]),
            ]);
        }

        $this->checkCountry($user, $card);

        $leftAttempts = $user->getIdVerifyLeftAttempts();
        $verified = $user->isIdVerified();
        if (!$verified && !Util::isDemo()) {
            $uiv = $user->getIdVerify();
            $status = $uiv->getStatus();
            if ($leftAttempts <= 0
                && !in_array($status, [
                    UserIdVerify::STATUS_INCOMPLETE,
                    UserIdVerify::STATUS_ACCEPTED
                ], true)
            ) {
                return new ErrorResponse('You have no attempts left to verify ID. Please contact us.');
            }
        } else {
            $uiv = $user->getIdVerify();
            $status = $user->getIdVerifyStatus();
        }

        if ($upgradeCard && $verified) {
            $uc->upgrade();
        }

        return $this->render('@Portal/Register/id-verify.html.twig', [
            'userCard'     => $uc,
            'upgradeCard'  => $upgradeCard,
            'cardName'     => $card->getFullName(),
            'reloadable'   => $card->isReloadable(),
            'verified'     => $verified,
            'leftAttempts' => $leftAttempts,
            'status'       => $status,
            'result'       => $uiv instanceof UserIdVerify ? json_decode($uiv->getResult()) : false,
            'uploadLimit'    => Util::uploadLimit(),
        ]);
    }

    /**
     * @Route("/consumer-register/clear-id-verification")
     * @throws \Doctrine\ORM\ORMInvalidArgumentException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function clearIdVerificationAction()
    {
        if (!Util::isDev()) {
            return $this->redirect('/');
        }
        /** @var User $user */
        $user = $this->getUser();
        $uiv = $user->getIdVerify();
        $em = Util::em();
        if ($uiv) {
            foreach ($uiv->getFiles() as $file) {
                $uiv->removeFile($file);
                $em->remove($file);
            }
            $em->remove($uiv);
            $em->flush();
        }
        return $this->redirect('/consumer-register/id-verification');
    }

    /**
     * @Route("/consumer-register/countries", methods={"GET"})
     * @return JsonResponse
     */
    public function getCountriesAction()
    {
        /** @var User $user */
        $user = $this->getUser();
        $uc = $user->getOneCardInPlatform();

        $countries = Util::em()->getRepository(\CoreBundle\Entity\Country::class)->findBy([], [
            'name' => 'asc',
        ]);

        if ($uc) {
            $cp = $uc->getCard()->getCardProgram();
            $setting = Util::json($cp, 'meta', 'idVerificationCountry');
            if (!empty($setting['inOnlySupported'])) {
                $countries = $cp->getCountries()->toArray();
            }
            if (!empty($setting['sameAsPersonal'])) {
                $countries = [
                    $user->getCountry()
                ];
            }
        }
        $countries = array_map(function (Country $country) {
            return $country->toApiArray();
        }, $countries);
        Util::usort($countries, ['name' => 'asc']);
        return new JsonResponse([
            'countries' => $countries,
            'country' => $user->getCountryid(),
        ]);
    }

    /**
     * @Route("/consumer-register/kyc-setting/{country}", methods={"GET"})
     * @param Country $country
     * @param Request $request
     * @return JsonResponse
     */
    public function getKycSettingAction(Country $country, Request $request)
    {
        $user = $this->getUser();
        $card = null;
        $upgradeCard = $request->get('upgrade');
        if ($upgradeCard) {
            $uc = Util::em()->getRepository(\CoreBundle\Entity\UserCard::class)->find($upgradeCard);
            /** @var CardProgramCardType $cpCard */
            $cpCard = $uc->getCard();
            $card = $cpCard->getUpgradeTargetCard();
        } else {
            $uc = $user->getLoadingCard();
            if ($uc) {
                $card = $uc->getCard();
            }
        }
        $settings = [];
        if ($card) {
            $settings = Util::em()->getRepository(\CoreBundle\Entity\CardProgram::class)
                ->getImageRequirementSettings($card->getCardProgram(), $country);
        }

        return new JsonResponse($settings);
    }

    /**
     * @Route("/consumer-register/upload-id-verify", methods={"POST"})
     * @param Request $request
     * @return JsonResponse
     */
    public function uploadAction(Request $request)
    {
        /** @var User $user */
        $user = $this->getUser();
        $em = Util::em();

        $uiv = $user->getIdVerify(true);

        foreach ($uiv->getFiles() as $file) {
            $uiv->removeFile($file);
        }

        $uiv->setCountry($em->getRepository(\CoreBundle\Entity\Country::class)->find($request->get('country')));
        $uiv->setProvider($em->getRepository(\CoreBundle\Entity\KycProvider::class)->find($request->get('provider')));

        $type = $request->get('type');
        $uiv->setType($type);

        $repo = $em->getRepository(\CoreBundle\Entity\Attachment::class);
        foreach ((array)$request->get('attachment') as $attachmentId) {
            $uiv->addFile($repo->find($attachmentId));
        }
        Util::persist($uiv);

        $resp = Util::e2a($uiv);

        if (stripos($uiv->getProvider()->getName(), NetVerifyService::NAME) !== false) {
            if (empty(IdentityType::NETVERIFY_MAPS[$type])) {
                return new FailedResponse('Type ' . $type . ' is not supported by NetVerify!');
            }
            $invoke = NetVerifyService::request($uiv, IdentityType::NETVERIFY_MAPS[$type]);
            if (is_string($invoke)) {
                return new FailedResponse($invoke);
            }

            $uiv->setInvoke($invoke);
            Util::persist($uiv);

            $invoke->setEntity(UserIdVerify::class);
            $invoke->setForeignKey($uiv->getId());
            $invoke->persist();

            $resp['scanReference'] = $invoke->getRequestKey();
        } else {
            return new FailedResponse('Unknown kyc provider: ' . $uiv->getProvider()->getName());
        }

        if (Util::isAPI()) {
            return new SuccessResponse([
                'id'        => $uiv->getId(),
                'reference' => $resp['scanReference'],
            ]);
        }

        $left = $user->getIdVerifyLeftAttempts() - 1;
        $user->setIdVerifyLeftAttempts($left >= 0 ? $left : 0);
        Util::persist($user);

        $user->logIP('id_verification_upload');

        return new JsonResponse($resp);
    }

    /**
     * @Route("/consumer-register/retrieve/{uiv}/{reference}")
     * @param UserIdVerify $uiv
     * @param $reference
     * @return FailedResponse|SuccessResponse
     */
    public function retrieveAction(UserIdVerify $uiv, $reference)
    {
        $resp = NetVerifyService::retrieve($reference, $uiv);
        if (!$resp) {
            if (Util::isDemo()) {
                return new SuccessResponse([
                    'document' => [
                        'issue' => Util::formatDate(new \DateTime('1980-01-01'), null, Util::DATE_FORMAT_SEARCH),
                        'expiry' => Util::formatDate(new \DateTime('2050-01-01'), null, Util::DATE_FORMAT_SEARCH),
                        'number' => '1234567890',
                    ],
                ]);
            }

            return new SuccessResponse();
        }
        $user = $uiv->getUser();

        $uiv->setResult(json_encode($resp));
        $errors = [
            'ERROR_NOT_READABLE_ID'         => 'ID is not readable from the uploaded image(s)',
            'DENIED_UNSUPPORTED_ID_TYPE'    => 'Type ' . $uiv->getType() . ' is not supported',
            'DENIED_UNSUPPORTED_ID_COUNTRY' => 'Country ' . $uiv->getCountry()->getIso3Code() . ' is not supported',
            'DENIED_NAME_MISMATCH'          => 'Name is mismatched',
            'NO_ID_UPLOADED'                => 'No ID was uploaded',
        ];
        $status = $resp['document']['status'];
        if ($status === 'DENIED_FRAUD') {
            $uiv->setStatus(UserIdVerify::STATUS_INVALID, true);
            Util::persist($uiv);

            $user->setStatus(User::STATUS_BANNED)
                ->addNote('Possible fraud detected when verifying ID. Account has been changed to banned.', false);
            Util::persist($user);

            return new FailedResponse('Possible fraud detected.', [
                'fraud' => true,
            ]);
        }

        $error = null;
        if (isset($errors[$status])) {
            $error = $errors[$status];
        } else if ($status !== 'APPROVED_VERIFIED') {
            $error = $status;
        }
        if ($error) {
            $uiv->setStatus(UserIdVerify::STATUS_INVALID, true);
            Util::persist($uiv);
            if (isset($resp['verification']['rejectReason']['rejectReasonDescription'])) {
                $error .= ' (' . $resp['verification']['rejectReason']['rejectReasonDescription'] . ')';
            }
            return new FailedResponse($error);
        }

        $testNumber = '410111198807145555';
        $doc = $resp['document'];
        if (isset($doc['expiry'])
            && (!isset($doc['number']) || $doc['number'] !== $testNumber))
        {
            $expiry = strtotime($doc['expiry']);
            if ($expiry <= time()) {
                $uiv->setStatus(UserIdVerify::STATUS_EXPIRED, true);
                Util::persist($uiv);

                return new FailedResponse('Your ID has been expired');
            }
        }

        if (isset($doc['number']) && $doc['number'] !== $testNumber) {
            $query = Util::em()->getRepository(\CoreBundle\Entity\UserIdVerify::class)
                ->createQueryBuilder('uiv')
                ->join('uiv.user', 'u');
            $expr = $query->expr();
            $others = $query->where($expr->andX(
                    $expr->eq('uiv.number', ':number'),
                    $expr->isNull('u.deletedAt')
                ))->setParameters([
                    'number' => $doc['number'],
                ])
                ->getQuery()
                ->getResult();
            if ($others) {
                /** @var UserIdVerify $other */
                $other = $others[0];
                $otherUser = $other->getUser();
                if ($otherUser && $otherUser->getId() !== $user->getId()) {
                    $uiv->setReason(UserIdVerify::REASON_ID_DUPLICATE)
                        ->setStatus(UserIdVerify::STATUS_NONE, true);
                    Util::persist($uiv);

                    return new FailedResponse('An account already exists, please log into your account!', [
                        'close_logout' => $otherUser->getId(),
                        'reason' => 'Same ID number: ' . $doc['number'],
                    ]);
                }
            }
        }

        // TCSPAN2-254: Override the issuing country (and type)
        /** @var Country $country */
        $country = $uiv->getCountry();
        $old = $country->getIso3Code();
        $new = $doc['issuingCountry'];
        if ($old !== $new) {
            $country = Country::findByCode($new);
            if ($country) {

                // https://shinetechchina.atlassian.net/browse/TCSPAN2-318
                $uc = $user->getOneCardInPlatform();
                if ($uc) {
                    $cp = $uc->getCard()->getCardProgram();
                    $setting = Util::json($cp, 'meta', 'idVerificationCountry');
                    if (!empty($setting['inOnlySupported'])) {
                        $countries = $cp->getCountries()->toArray();
                        if (!Util::includes($countries, $country)) {
                            return new FailedResponse('Your ID country is not supported by the card program!');
                        }
                    }
                    if (!empty($setting['sameAsPersonal'])) {
                        if (!Util::eq($country, $user->getCountry())) {
                            return new FailedResponse('Your ID country should be same as personal country!');
                        }
                    }
                }

                $uiv->setCountry($country);

                $user->addNote('Override id verification issuing country from ' . $old . ' to '
                    . $new . ' in ' . $uiv->getId());
            }
        }

        $old = $uiv->getType();
        $new = IdentityType::NETVERIFY_MAPS_REVERSE2[$doc['type']];
        if ($old !== $new) {
            $uiv->setType($new);

            $user->addNote('Override id verification type from ' . $old . ' to '
                . $new . ' in ' . $uiv->getId());
        }

        $disabledFields = $uiv->calculateDisabledFieldsAndFixUserName($resp);
        $uiv->setDisabledFields(json_encode($disabledFields))
            ->setStatus(UserIdVerify::STATUS_INCOMPLETE);
        Util::persist($uiv);

        return new SuccessResponse([
            'document' => [
                'issue' => Util::formatDate($uiv->getIssueAt(), null, Util::DATE_FORMAT_SEARCH),
                'expiry' => Util::formatDate($uiv->getExpireAt(), null, Util::DATE_FORMAT_SEARCH),
                'number' => $uiv->getNumber(),
            ],
        ]);
    }

    /**
     * @Route("/consumer-register/id-verify-finish", methods={"POST"})
     * @param Request $request
     * @return FailedResponse|SuccessResponse
     * @throws \PortalBundle\Exception\PortalException
     */
    public function finishAction(Request $request)
    {
        /** @var User $user */
        $user = $this->getUser();
        $uiv = $user->getIdVerify(true);
        if (Util::isDemo()) {
            $uiv->setStatus(UserIdVerify::STATUS_ACCEPTED);
            Util::persist($uiv);

            return new SuccessResponse([
                'upgradeCard' => null,
            ]);
        }

        $disabled = Util::s2j($uiv->getDisabledFields());

        if ($request->get('issue_date') && !in_array('issueAt', $disabled, true)) {
            $uiv->setIssueAt(new Carbon($request->get('issue_date')));
        }

        if ($request->get('expire_date') && !in_array('user.expireAt', $disabled, true)) {
            $uiv->setExpireAt(new Carbon($request->get('expire_date')));
        }

        if ($request->get('number') && !in_array('number', $disabled, true)) {
            $uiv->setNumber($request->get('number'));
        }

        $issueAt = $uiv->getIssueAt();
        if ($issueAt) {
            $issueAt = Carbon::instance($issueAt);
            if ($issueAt->lt(Carbon::instance($user->getBirthday()))) {
                return new FailedResponse('Issue date cannot be less than DOB!');
            }
        }

        $birthday = $user->getBirthday();
        $atLeast = (new Carbon())->subYearsWithoutOverflow(18)->startOfMonth();
        if (Carbon::instance($birthday)->gte($atLeast)) {
            return new FailedResponse('Consumer must be older than 18 years old!');
        }

        $uiv->setStatus(UserIdVerify::STATUS_ACCEPTED, true);
        Util::persist($uiv);
        $user->ensureConfig()->setIdVerification($uiv)->persist();

        $upgradeCard = $request->get('upgrade');
        if ($upgradeCard) {
            $uc = Util::em()->getRepository(\CoreBundle\Entity\UserCard::class)->find($upgradeCard);
            $uc->upgrade();
        }

        $user->logIP('id_verification_finish');

        return new SuccessResponse([
            'upgradeCard' => $upgradeCard,
        ]);
    }
}
