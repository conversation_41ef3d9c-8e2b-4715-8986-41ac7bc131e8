<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2017/3/22 0022
 * Time: 下午 8:57
 */

namespace PortalBundle\Controller;

use AdminBundle\Form\Type\ReshipperAddressType;
use Carbon\Carbon;
use CoreBundle\Entities\ResultEntity;
use CoreBundle\Entity\Affiliate;
use CoreBundle\Entity\AffiliateReshipper;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\CardProgramCardType;
use CoreBundle\Entity\Config;
use CoreBundle\Entity\Country;
use CoreBundle\Entity\Email;
use CoreBundle\Entity\ExternalInvoke;
use CoreBundle\Entity\IpUsage;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\Promotion;
use CoreBundle\Entity\Reshipper;
use CoreBundle\Entity\ReshipperAddress;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserBillingAddress;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserIdVerify;
use CoreBundle\Entity\UserPin;
use CoreBundle\Entity\UserToken;
use CoreBundle\Exception\RedirectException;
use CoreBundle\Response\ErrorResponse;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Services\User\LegalService;
use CoreBundle\Utils\Bundle;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Security;
use CoreBundle\Utils\Service;
use CoreBundle\Utils\Session;
use CoreBundle\Utils\Util;
use EsSoloBundle\Services\EsSoloService;
use GuzzleHttp\Client;
use hisorange\BrowserDetect\Parser;
use libphonenumber\NumberParseException;
use PortalBundle\Controller\Common\BaseController;
use PortalBundle\Exception\PortalException;
use PortalBundle\Util\RegisterStep;
use SalexUserBundle\Entity\User;
use SalexUserBundle\Entity\UserConfig;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Core\Authentication\Token\UsernamePasswordToken;
use Symfony\Component\Validator\ConstraintViolation;
use UsUnlockedBundle\Services\IDologyService;
use UsUnlockedBundle\Services\ReferService;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use UsUnlockedBundle\Services\SlackService;
use UsUnlockedBundle\Services\UserRefererService;

class RegisterController extends BaseController
{
    const FAIL_ERROR = '_consumer_register_error';
    const USER = '_consumer_register_user';
    const RESHIPPER_INVALID = '_consumer_register_reshipper_error';

    protected function ensurePlatformRequest()
    {
        if (!Util::platform()) {
            throw new RedirectException(Platform::ternCommerce()->host());
        }
    }

    // TCSPAN2-33: Prevent user from registering with not added countries in card program
    protected function checkCountry(User $user, CardProgramCardType $card)
    {
        if (!$user || !$card) {
            return;
        }
        $country = $user->getCountry();
        if ($country) {
            $cp = $card->getCardProgram();
            $found = false;
            /** @var Country $_country */
            foreach ($cp->getCountries() as $_country) {
                if ($_country->getId() === $country->getId()) {
                    $found= true;
                    break;
                }
            }
            if (!$found) {
                throw PortalException::temp('Your country ' . $country->getName()
                    . ' is not supported in card program ' . $cp->getName() . '. Please contact support.');
            }
        }
    }

    /**
     * @Route("/consumer/register/")
     * @Route("/consumer/register/{card}",name="portal_register")
     * @Route("/consumer-register")
     * @Route("/consumer/register/{card}/{affiliateId}", name="portal_register_with_affiliate")
     * @param Request $request
     * @param CardProgramCardType $card
     * @param $affiliateId
     * @return \Symfony\Component\HttpFoundation\Response
     * @throws \Doctrine\ORM\ORMInvalidArgumentException
     * @throws \Doctrine\ORM\OptimisticLockException
     * @throws \PortalBundle\Exception\PortalException
     */
    public function RegisterAction(Request $request, CardProgramCardType $card = null, $affiliateId = null)
    {
        $this->ensurePlatformRequest();

        if (Bundle::isRootOrUsu() && !Util::isDev() && !Config::isNewUsuEnabled()) {
            return $this->redirect('/choose-card');
        }

        $platform = Util::platform();
        if ($platform && !Util::isAPI() && !$platform->canConsumerRegister()) {
            return $this->redirect('/');
        }

        /** @var User $user */
        $user = $this->getUser();

        // Delete old uninitialized card
        if ($card && $user) {
            /** @var UserCard $_uc */
            foreach ($user->getCards(false) as $_uc) {
                if (
                    $_uc->isUsUnlockedLegacy() &&
                    !$_uc->getAccountNumber() &&
                    !$_uc->getIssuedAt() &&
                    !$_uc->getInitializedAt() &&
                    !Util::eq($_uc->getCard(), $card)
                ) {
                    $this->em->remove($_uc);
                    $this->em->flush();
                }
            }
        }

        $uc = null;
        if (!$card) {
            if ($user) {
                $uc = $user->getLoadingCard();
                if ($uc) {
                    $card = $uc->getCard();
                }
            }
        }
        if (!$card) {
            return $this->redirect('/choose-card');
        }
        Session::set(User::REGISTERING_CARD, $card->getId());

        $failKey = RegisterController::FAIL_ERROR;
        $failInfo = null;
        $session = $request->getSession();
        if (null != $session && $session->has($failKey)) {
            $failInfo = $session->get($failKey);
            $session->remove($failKey);
        } else {
            $failInfo = null;
        }

        $reloadable = $card->isReloadable();
        if ($user) {
            $uc = $user->ensureActiveCard($card);
            if ($user->isIdVerified() && $uc->getBillingAddress() && $uc->getInitializedAt() && $uc->everLoaded()) {
                return $this->redirect('/');
            }

            $allCards = $user->getCards();
            /** @var UserCard $_uc */
            foreach ($allCards as $_uc) {
                // NOTE: TCSPAN-621 impossible to have 2 active cards
                if (!$_uc->isActive()) {
                    continue;
                }
                if ($_uc->everPaid()) {
                    return $this->redirect('/');
                }
            }

            $this->checkCountry($user, $card);

            if (!filter_var($user->getEmail(), FILTER_VALIDATE_EMAIL)) {
                return $this->redirect('/consumer-register/member-info');
            }

            if (!$user->getMobilephone()) {
                $birthday = $user->getBirthday();
                $validBirthday = (bool)$birthday;
                if ($validBirthday) {
                    $birthday = Carbon::instance($birthday);
                    $min = Carbon::createFromTimestamp(-2208992400); // 1900-01-01
                    $max = (new Carbon())->subYearsWithoutOverflow(18)->startOfMonth();
                    if ($birthday->gte($max) || $birthday->lte($min)) {
                        $validBirthday = false;
                    }
                }
                if (!$validBirthday) {
                    return $this->redirect('/consumer-register/member-info');
                }
            }

            if (!$user->getEmailVerified()) {
                return $this->redirect('/consumer-register/confirm-email');
            }

            if (!$user->getMobilephone()) {
                return $this->redirect('/consumer-register/personal-info');
            }

//            if ($reloadable && !$user->isIdVerified()) {
//                return $this->redirect('/consumer-register/id-verification');
//            }

            if (!$uc->getBillingAddress()) {
                return $this->redirect('/consumer-register/billing-address');
            }

            return $this->redirect('/consumer-register/card-load');
        }

        $year = date('Y');
        $affiliateId = $affiliateId ?: Affiliate::getAffIdFromRequest($request);
        $data = [
            'failInfo'   => $failInfo,
            'reloadable' => $reloadable,
            'cardId'     => $card->getId(),
            'cardName'   => $card->getFullName(),
            'year'       => $year,
            'month'      => date('m'),
            'day'        => date('d'),
            'user'       => $user,
            'userCard'   => $uc,
            'emailAddress' => $user ? $user->getEmail() : '',
            'mobilePhone' => $user ? $user->getMobilephone() : '',
            //Set affiliate info if any
            'affiliateId' => $affiliateId ?: '',
            'inviteCode' => UserRefererService::getInviteCodeFromRequest($request),
            'phoneCode'   => '',
            'phoneCountryList' => Util::em()->getRepository(\CoreBundle\Entity\Country::class)->phoneCountries()
        ];
        if ($failInfo) {
            if (!$failInfo['success']) {
                $data = array_merge($data, [
                    'error'        => $failInfo['error'],
                    'firstName'    => $failInfo['firstName'],
                    'lastName'     => $failInfo['lastName'],
                    'emailAddress' => $failInfo['emailAddress'],
                ]);
            }
        } else {
            $data = array_merge($data, [
                'firstName'    => null,
                'lastName'     => null,
            ]);
        }

        $response = $this->render('@Portal/Register/member-info.html.twig', $data);
        return Affiliate::ensureAffIdCookie($request, $response, $affiliateId);
    }

    /**
     * @Route("/consumer-register/member-info")
     * @Route("/consumer-register")
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function memberInfoAction(Request $request)
    {
        $this->ensurePlatformRequest();

        /** @var User $user */
        $user = $this->getUser();
        if (!$user) {
            return $this->redirect('/choose-card');
        }
        if ($user->getEmailVerified()) {
            return $this->redirect('/');
        }
        $uc = $user->getLoadingCard();
        $card = $uc->getCard();
        $this->checkCountry($user, $card);
        $tz = Util::tzUTC();
        $data = [
            'failInfo'   => null,
            'reloadable' => $card->isReloadable(),
            'cardId'     => $card->getId(),
            'cardName'   => $card->getFullName(),
            'year'       => $user->getBirthday() ? Util::formatDateTime($user->getBirthday(), 'Y', $tz) : date('Y'),
            'month'      => $user->getBirthday() ? Util::formatDateTime($user->getBirthday(), 'm', $tz) : date('m'),
            'day'        => $user->getBirthday() ? Util::formatDateTime($user->getBirthday(), 'd', $tz) : date('d'),
            'user'       => $user,
            'userCard'   => $uc,
            'firstName'    => $user->getFirstName(),
            'lastName'     => $user->getLastName(),
            'emailAddress' => $user->getEmail(),
            'mobilePhone' => $user->getMobilephone(),
            'phoneCode' => '',
            'inviteCode' => UserRefererService::getInviteCodeFromRequest($request),

            //Set affiliate info if any
            'affiliateId' => Affiliate::getAffIdFromRequest($request),
            'phoneCountryList' => Util::em()->getRepository(\CoreBundle\Entity\Country::class)->phoneCountries()
        ];

        if (Bundle::isCashOnWeb()) {
            $phoneList = explode(' ', $user->getMobilephone());
            $data = array_merge($data, [
                'mobilePhone' =>  count($phoneList) > 1 ? $phoneList[1] : $user->getMobilephone(),
                'phoneCode'   => count($phoneList) > 1 ? $phoneList[0] : '',
            ]);
        }

        return $this->render('@Portal/Register/member-info.html.twig', $data);
    }

    /**
     * @Route("/consumer-register/confirm-email")
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function confirmEmailAction(Request $request)
    {
        $this->ensurePlatformRequest();

        /** @var User $user */
        $user = $this->getUser();
        if (!$user) {
            return $this->redirect('/choose-card');
        }
        if ($user->getEmailVerified()) {
            return $this->redirect('/');
        }

        if (!$user->getToken()) {
            $token = md5($user->getUsername() . $user->getPassword() . time());
            $user->setToken($token)
                ->persist();
        }

        $uc = $user->getLoadingCard();
        $card = $uc->getCard();
        $data = [
            'failInfo'   => [
                'success' => true,
                'error' => false,
            ],
            'reloadable' => $card->isReloadable(),
            'cardId'     => $card->getId(),
            'cardName'   => $card->getFullName(),
            'year'       => date('Y'),
            'user'       => $user,
            'userCard'   => $uc,
            'firstName'    => null,
            'lastName'     => null,
            'emailAddress' => $user->getEmail(),
            'mobilePhone' => $user->getMobilephone(),
        ];
        return $this->render('@Portal/Register/member-info.html.twig', $data);
    }

    /**
     * @Route("/consumer-register/send-confirm-email")
     */
    public function sendConfirmEmailAction()
    {
        $user = $this->getUser();
        if (!$user) {
            return new FailedResponse();
        }
        if ($user->getEmailVerified()) {
            return new FailedResponse('Your email address has been verified.',  [
                'url' => '/'
            ]);
        }

        $uc = $user->getLoadingCard();
        $card = $uc->getCard();

        $token = $user->getToken();
        $url = '/consumer/verification?verify=' . $token;
        if (Util::isAPI()) {
            $url = '/consumer/verification/page?verify=' . $token;
        }

        Email::sendWithTemplateToUser($user, $card->getCardProgram()->isCashOnWeb() ? Email::COW_TEMPLATE_EMAIL_ADDRESS_VERIFICATION : Email::TEMPLATE_EMAIL_ADDRESS_VERIFICATION, array(
            'name' => $user->getName(),
            'action_url' => Util::host() . $url,
            'username' => $card->getCardProgram()->isCashOnWeb() ? $user->getEmail() : $user->getUsername(),
        ), $card->getCardProgram());

        return new SuccessResponse();
    }

    public function passwordverify($password)
    {
        $ver=false;
        if(preg_match('/^[0-9a-zA-Z]+$/', $password)) {
            if (preg_match('/^[A-Z]+/', $password)) {
                $ver = true;
            }
        }
        return $ver;
    }

    /**
     * @Route("/consumer/add",name="add_consumer", methods={"POST"})
     * @throws \InvalidArgumentException
     * @throws \Symfony\Component\DependencyInjection\Exception\ServiceCircularReferenceException
     */
    public function AddConsumerAction(Request $request)
    {
        $this->ensurePlatformRequest();

        LegalService::validateLegalFields($request);
        \CoreBundle\Utils\Security::recaptchaCheck($request, 'register');

        $emailAddress = strtolower(trim($request->request->get('emailAddress')));
        $firstName = trim($request->request->get('firstName'));
        $lastName = trim($request->request->get('lastName'));
        Log::debug('Registration request', compact('emailAddress', 'firstName', 'lastName'));

        if (Config::isEmailInactive($emailAddress)) {
            $msg = 'There is a problem with delivering to this email address, please use an alternate email address.';
            if (Util::hasSuffix($emailAddress, [
                '@filmla.org',
                '@filmla.com',
            ])) {
                $msg = 'The registration is blocked. Please contact support.';
            }
            return new ErrorResponse($msg);
        }
        $emailConstraint = new \Symfony\Component\Validator\Constraints\Email(
            message: 'Please input valid email address!',
            mode: \Symfony\Component\Validator\Constraints\Email::VALIDATION_MODE_HTML5
        );
        $errors = Util::$validator->validate(
            $emailAddress,
            $emailConstraint
        );
        if ($errors && $errors->count()) {
            /** @var ConstraintViolation $error */
            $error = $errors[0];
            return new ErrorResponse($error->getMessage());
        }

        if (str_contains($emailAddress, '*')) {
            return new ErrorResponse('The email address must not contain the character "*"');
        }

        $failKey = RegisterController::FAIL_ERROR;
        $failInfo = null;

        if (!Util::testLatinName($firstName) || !Util::testLatinName($lastName)) {
            return new ErrorResponse('First name and last name can only include latin characters!');
        }

        $cardProgramcardTypeId = trim($request->request->get('cardId', ''));
        $card = Util::em()->getRepository(\CoreBundle\Entity\CardProgramCardType::class)->find($cardProgramcardTypeId);
        if (!$card) {
            return new ErrorResponse('Unknown card program card type!');
        }
        $cp = $card->getCardProgram();
        if ($cp->isUsUnlockedLegacy()) {
            return new ErrorResponse('The legacy USU platform is not available anymore!');
        }

        $userName = $emailAddress;
        $oldEmail = null;
        if ($card->getCardProgram()->isCashOnWeb()) {
          $user = User::findPlatformUserByEmail($emailAddress);
          $userName = $emailAddress.'_cow';
        } else if ($card->getCardProgram()->isSpendr()) {
          $user = User::findPlatformUserByEmail($emailAddress);
          $userName = $emailAddress.'_spendr';
        } else {
          $user = Util::em()->getRepository(\SalexUserBundle\Entity\User::class)->findOneBy(['email' => $emailAddress]);
        }
        if ($user) {
            $currentUser = $this->getUser();
            if (!$currentUser || !Util::eq($currentUser, $user)) {
                return new ErrorResponse('The email address has been registered before. '
                    . ' Please try to login or use another email address to register.');
            }
            $oldEmail = $user->getEmail();
        }

        $usu = $cp->isUsUnlocked();
        if ($usu) {
            if (Util::endsWith($emailAddress, '@web.de')) {
                Log::warn('Disable signing up from @web.de', [
                    'email' => $emailAddress,
                ]);
                return new ErrorResponse('Sorry that your email address is not supported in our system!');
            }
        }

        $singleKey = 'registering_' . $emailAddress;
        if (Data::lockedHas($singleKey, 3)) {
            return new ErrorResponse('Your registration is being processed, please wait and try again later!');
        }

        $password = trim($request->request->get('password'));
        $gender = trim($request->request->get('gender'));

        if ($request->request->has('birthDay')) {
            $birthDay = (int)trim($request->request->get('birthDay'));
            $birthMonth = (int)trim($request->request->get('birthMonth'));
            $birthYear = (int)trim($request->request->get('birthYear'));
            $bithAt = new \DateTime();
            $bithAt->setDate($birthYear,$birthMonth,$birthDay);

            $atLeast = (new Carbon())->subYearsWithoutOverflow(18)->startOfMonth();
            if (Carbon::instance($bithAt)->gte($atLeast)) {
                return new ErrorResponse('Consumer must be older than 18 years old!');
            }
        } else {
            $bithAt = null;
        }

        $em = Util::em();
        $repository = $this->getDoctrine()->getRepository(\SalexUserBundle\Entity\User::class);

        $newUser = $this->getUser();
        $creatingNewUser = false;
        if (!$newUser) {
            $newUser = new User();
            $newUser->setEnabled(true);
            $creatingNewUser = true;
        } else {
            if ($newUser->getEmailVerified()) {
                return new ErrorResponse('Your email address had been verified!');
            }
            $step = $newUser->getRegisterStep();
            if ($step && !in_array($step, [
                RegisterStep::CONSUMER_CREATED,
                RegisterStep::EMAIL,
            ])) {
                return new ErrorResponse('Invalid request since your account had been created!');
            }
        }

        $mobilePhone = trim($request->get('mobilePhone'));
        $rawMobilePhone = $mobilePhone;
        $mobilePhone = str_replace([' ', '-', '(', ')', '.'], '', $mobilePhone);
        if (!$mobilePhone && !Util::isAPI()) {
            return new ErrorResponse('The mobile phone is required!');
        }
        $mobilePhone = $mobilePhone ?: '+';
        if ($mobilePhone === '+' && !Util::isAPI()) {
            return new ErrorResponse('Invalid mobile phone!');
        }
        if ($card->getCardProgram()->isCashOnWeb()) {
          $mobilePhone = Util::inputPhone($mobilePhone, $request->get('phoneCountryCode') ?  $request->get('phoneCountryCode') : 'CW');
        } else if (!Util::startsWith($mobilePhone, '+')) {
            $mobilePhone = '+' . $mobilePhone;
        }

        $formattedPhone = Util::formatFullPhone($mobilePhone);

        $phones = Config::array(Config::CONFIG_WILD_PHONE_NUMBERS);
        if ($mobilePhone !== '+' && !in_array($mobilePhone, $phones)) {
          if ($card->getCardProgram()->isCashOnWeb()) {
            $roles = [
              Role::ROLE_CASH_ON_WEB_ADMIN,
              Role::ROLE_CASH_ON_WEB_AGENT,
              Role::ROLE_CASH_ON_WEB_PARTNER,
              Role::ROLE_CASH_ON_WEB_MEMBER
            ];
            $users = $em->getRepository(User::class)
            ->createQueryBuilder('u')
            ->join('u.teams', 't')
            ->where(Util::expr()->in('t.name', ':roles'))
            ->Andwhere(Util::expr()->orX(Util::expr()->eq('u.phone',':phone'),
            Util::expr()->eq('u.mobilephone', ':mobilephone'),
            Util::expr()->eq('u.workphone', ':workphone'),
            Util::expr()->eq('u.phone', ':raw_phone'),
            Util::expr()->eq('u.mobilephone', ':raw_mobilephone'),
            Util::expr()->eq('u.workphone', ':raw_workphone'),
            Util::expr()->eq('u.phone', ':format_phone'),
            Util::expr()->eq('u.mobilephone', ':format_mobilephone'),
            Util::expr()->eq('u.workphone', ':format_workphone')
            ))
            ->setParameter('roles', $roles)
            ->setParameter('phone', $mobilePhone)
            ->setParameter('mobilephone', $mobilePhone)
            ->setParameter('workphone', $mobilePhone)
            ->setParameter('raw_phone', $rawMobilePhone)
            ->setParameter('raw_mobilephone', $rawMobilePhone)
            ->setParameter('raw_workphone', $rawMobilePhone)
            ->setParameter('format_phone', $formattedPhone)
            ->setParameter('format_mobilephone', $formattedPhone)
            ->setParameter('format_workphone', $formattedPhone)
            ->getQuery()
            ->getResult()
        ;
          } else {
            $users = $em->getRepository(User::class)
                ->createQueryBuilder('u')
                ->where('u.phone = :phone')
                ->orWhere('u.mobilephone = :mobilephone')
                ->orWhere('u.workphone = :workphone')
                ->orWhere('u.phone = :raw_phone')
                ->orWhere('u.mobilephone = :raw_mobilephone')
                ->orWhere('u.workphone = :raw_workphone')
                ->orWhere('u.phone = :format_phone')
                ->orWhere('u.mobilephone = :format_mobilephone')
                ->orWhere('u.workphone = :format_workphone')
                ->setParameter('phone', $mobilePhone)
                ->setParameter('mobilephone', $mobilePhone)
                ->setParameter('workphone', $mobilePhone)
                ->setParameter('raw_phone', $rawMobilePhone)
                ->setParameter('raw_mobilephone', $rawMobilePhone)
                ->setParameter('raw_workphone', $rawMobilePhone)
                ->setParameter('format_phone', $formattedPhone)
                ->setParameter('format_mobilephone', $formattedPhone)
                ->setParameter('format_workphone', $formattedPhone)
                ->getQuery()
                ->getResult()
            ;
          }
            foreach ($users as $u) {
                if (Util::neq($newUser, $u)) {
                    return new ErrorResponse('The phone number has been registered before. Please login to your account or ask support for help.');
                }
            }
        }

        if ($usu && !Util::isInternalTester($emailAddress)) {
            $country = Util::parseCountryFromPhoneNumber($formattedPhone);
            if ($country && $country->isUSA()) {
                Log::info('Disable signing up from USA', [
                    'email' => $emailAddress,
                ]);
                return new ErrorResponse('Sorry that your region is not supported in our system at present!');
            }
        }

        $users = $repository->findBy(['status' => User::STATUS_BANNED]);
        $currentIp = Security::getClientIp();
        foreach ($users as $bannedUser){
            $bannedIps = Util::em()->getRepository(\CoreBundle\Entity\IpUsage::class)->findBy(['users' => $bannedUser]);
            $ipsArr = [];
            /** @var IpUsage $bannedIp */
            foreach ($bannedIps as $bannedIp){
                if ($bannedIp->getLoginIp()){
                    $ipsArr[] = $bannedIp->getLoginIp();
                }
            }
            if (in_array($currentIp,$ipsArr)){
                $newUser->addFlag(User::FLAG_BAD_AP_ASSOCIATION);
                break;
            }
        }
        $newUser->setFirstName($firstName);
        $newUser->setLastName($lastName);
        $newUser->setEmail($emailAddress);
        $newUser->setEmailVerified(false);
        $newUser->setUsername($userName);
        $newUser->setBirthday($bithAt);
        $newUser->setGender($gender);
        $newUser->setMobilephone($formattedPhone);

        $token = md5($newUser->getUsername() . $newUser->getPassword() . time());
        $matches = [];
        preg_match('/tcspan\.at\.(\d{13})@yopmail\.com/', $emailAddress, $matches);
        if (isset($matches[1])) {
            $token = 'tcspan_at_' . $matches[1];
        }

        $newUser->setToken($token);
        $newUser->setRegisterStep(RegisterStep::CONSUMER_CREATED);

        $encodedPassword = Util::encodePassword($newUser, $password);
        $newUser->setPassword($encodedPassword);
        $newUser->setEnabled(true);
        $newUser->setLockedStatus(User::LOCK_STATUS_UNLOCK);
        $newUser->setStatus(User::STATUS_ACTIVE);
        $newUser->setSource(User::SOURCE_SELF);
        $em->persist($newUser);
        $em->flush();

        //Set affiliate if any
        $affiliateId = trim($request->request->get('affiliateId')) ?: Affiliate::getAffIdFromRequest($request);
        if ($affiliateId) {
            /** @var Affiliate $affiliate */
            $affiliate = $em->getRepository(\CoreBundle\Entity\Affiliate::class)->findOneBy([
                'affId' => $affiliateId,
            ]);
            if ($affiliate) {
                $newUser->setAffiliate($affiliate)
                    ->persist();
            } else {
                ReferService::refer($newUser, $affiliateId);
            }
            Promotion::applyUsuThanksLoadFee($newUser, $affiliateId);
        }

        $newUser->ensureRole(Role::ROLE_CONSUMER);
        $newUser->resetPasswordDone();
        $newUser->updateAffiliateDiscount();

        if ($card->getCardProgram()->isCashOnWeb()) {
          $newUser->ensureRole(Role::ROLE_CASH_ON_WEB_MEMBER);
        }

        if ($creatingNewUser) {
            $userToken = new UsernamePasswordToken($newUser, $newUser->getPassword(), ['ROLE_CONSUMER']);
            $this->container->get("security.token_storage")->setToken($userToken);
        }

        $userCard = $newUser->ensureActiveCard($card);

        if ($card->getCardProgram()->isUsUnlocked()) {
          $userCard->setIsWarning(true)->setWarningValue(1500)->persist();
          SlackService::sendUsuEvents($newUser, 'register');
        }

        if ($request->get('registrationMode') !== 'instant') {
            $url = '/consumer/verification?verify=' . $token;
            if (Util::isAPI()) {
                $url = '/consumer/verification/page?verify=' . $token;
            }

            Email::sendWithTemplateToUser($newUser, $card->getCardProgram()->isCashOnWeb() ? Email::COW_TEMPLATE_EMAIL_ADDRESS_VERIFICATION : Email::TEMPLATE_EMAIL_ADDRESS_VERIFICATION, array(
                'name' => $newUser->getName(),
                'action_url' => Util::host() . $url,
                'username' => $card->getCardProgram()->isCashOnWeb() ? $newUser->getEmail() : $newUser->getUsername(),
            ), $card->getCardProgram());
        }

        Log::debug('Registration request accepted', [
            'id' => $newUser->getId(),
            'email' => $emailAddress,
            'old_email' => ($oldEmail && $oldEmail !== $emailAddress) ? $oldEmail : '',
        ]);

        if (Util::isAPI()) {
            $newUser->setSource(User::SOURCE_API);
            Util::persist($newUser);

            $data = $newUser->toFinalApiArray();
            if ($userCard) {
                $data['cards'] = [
                    $userCard->toApiArray(),
                ];
            } else {
                $data['cards'] = [];
            }
            return new SuccessResponse($data, 'Member added successfully, proceed with step 2.');
        }

        $failInfo = array(
            'success' => true,
            'error'   => null
        );
        Session::set($failKey, $failInfo);

        return $this->redirect('/consumer/register/' . $cardProgramcardTypeId);

    }

    /**
     * @Route("/consumer/save-registration-meta", methods={"POST", "GET"})
     * @param Request $request
     * @return Response
     * @throws \InvalidArgumentException
     */
    public function saveRegistrationMeta(Request $request) {
        $user = $this->getUser();
        if (!$user) {
            return new SuccessResponse();
        }

        $ip = Security::getClientIp();
        $userAgent = $request->headers->get('User-Agent', '');
        $clientType = $request->get('client', User::CLIENT_WEB);
        $meta = [
            'ip' => $ip,
            'userAgent' => $userAgent,
            'client' => $clientType,
        ];

        $countryCode = '';
        try {
            $client = new Client();
            $resp = $client->get('https://api.hostip.info/get_json.php?ip=' . $ip);
            $resp = json_decode($resp->getBody()->getContents(), true);
            $meta['geo'] = $resp;
            $countryCode = $resp['country_code'];
        } catch (\Exception $exception) {
            Service::log('Failed to get country of IP ' . $ip . ': ' . $exception->getMessage());
        }

        $parser = new Parser();
        $detector = $parser->detect();

        $osName = $detector->platformFamily();
        $meta['os'] = [
            'name' => $osName,
            'version' => $detector->platformVersion(),
            'isMobile' => $detector->isMobile(),
        ];

        $meta['browser'] = [
            'name' => $detector->browserFamily(),
            'version' => $detector->browserVersion(),
        ];

        $meta['language'] = [
            'name' => Util::acceptLanguage(),
        ];

        $user->setRegistrationClient($clientType)
            ->setRegistrationIp($ip)
            ->setRegistrationCountry(Country::findByCode($countryCode))
            ->setRegistrationOS($osName);
        Util::updateJson($user, 'meta', $meta);

        Service::identify($user);

        return new SuccessResponse();
    }

    /**
     * @Route("/consumer/verification",name="consumer_email_verification", methods={"GET"})
     * @param Request $request
     * @return FailedResponse|\Symfony\Component\HttpFoundation\RedirectResponse|SuccessResponse
     * @throws \Doctrine\ORM\ORMInvalidArgumentException
     * @throws \Doctrine\ORM\OptimisticLockException
     * @throws \InvalidArgumentException
     */
    public function VerifyEmail(Request $request){
        $em = Util::em();
        $repository = $em->getRepository(\SalexUserBundle\Entity\User::class);

        /** @var User $user */
        $verify = $request->get('verify');
        $user = $repository->findOneBy(array(
            'token' => $verify,
        ));

        if ($user && $user->getEmailVerified()) {
            if (Util::isAPI()) {
                return new FailedResponse('Invalid or used verification code.');
            }
            return $this->redirect('/');
        }

        if ($this->getUser() && $this->getUser()->isDemoUser()) {
            $verify = true;
            $user = $this->getUser();
        }

        if($verify && $user) {
            $user->setRegisterStep(RegisterStep::CONSUMER_EMAIL_CONFIRMED)
                ->setEmailVerified(true);
            $em->persist($user);
            $em->flush();

            $user->logIP('verify_email');

            if (Util::isAPI()) {
                $ut = UserToken::instance($user);
                UserToken::updateUseTime($ut);
                $uc = $user->getOneCardInPlatform();

                return new SuccessResponse([
                    'userId'     => $user->getId(),
                    'token'      => $ut->getToken(),
                    'userCardId' => $uc ? $uc->getId() : null,
                ]);
            }

            Util::$security->login($user, 'form_login');

            $uc = $user->getOneCardInPlatform();
            if ($uc && $uc->isUsUnlockedLegacy()) {
                return $this->redirect('/consumer-register/personal-info');
            }

            // if ($uc && $uc->isCashOnWeb()) {
            //     return $this->redirect('/consumer-register/id-verification');
            // }
            if ($uc && $uc->isUsUnlocked()) {
               SlackService::sendUsuEvents($user, 'Confirm Email');
            }
            return $this->redirect('/consumer-register/billing-address');
        }

        if (Util::isAPI()) {
            return new FailedResponse('Invalid verify code.');
        }

        return $this->redirect('/choose-card');
    }

    /**
     * @Route("/consumer-register/personal-info",name="consumer_register_personalInfo", methods={"GET"})
     * @return \Symfony\Component\HttpFoundation\RedirectResponse|Response
     */
    public function PersonInfo() {
        /** @var User $user */
        $user = $this->getUser();
        if($user) {
            $uc = $user->getLoadingCard();
            if(!$uc) {
                return $this->redirect('/choose-card');
            }

            $card = $uc->getCard();
            //TCSPAN2-33 country list with card program
            /*$items = Util::em()->getRepository(\CoreBundle\Entity\Country::class)->getItemsForPortal();*/
            $cardProgramIds = array($card->getCardProgram()->getId());
            $items = Util::em()->getRepository(\CoreBundle\Entity\Country::class)->getCountriesWithCardPrograms($cardProgramIds);

            return $this->render('@Portal/Register/personal-info.html.twig', [
                'countryList' => $items,
                'reloadable'  => $card->isReloadable(),
                'cardName'    => $card->getFullName(),
                'currencies'  => $card->getCurrencyArray(),
                'userCard'    => $uc,
                'user'        => $user,
            ]);
        }
        return $this->redirect('/login');
    }

    /**
     * @Route("/consumer-register/personinfo",name="consumer_register_personinfo_add", methods={"POST"})
     * @param Request $request
     * @return Response
     * @throws \PortalBundle\Exception\PortalException
     */
    public function AddPersonalInfo(Request $request) {
        /** @var User $user */
        $user = $this->getUser();
        if($user) {
            $address = trim($request->request->get('address'));
            $addressline = trim($request->request->get('addressline', ''));
            $city = trim($request->get('city'));
            if (!Util::testLatinName($address) || !Util::testLatinName($addressline) || !Util::testLatinName($city)) {
                return new ErrorResponse('Address and city name can only include latin characters!');
            }

            $countryCode = trim($request->request->get('country'));
            $stateid = trim($request->request->get('state'));
            $phone = trim($request->request->get('phone'));
            $mobilephone = trim($request->request->get('mobilephone'));
            $workphone = trim($request->request->get('workphone'));
            $zip = trim($request->request->get('zip'));
            $knownAddress = trim($request->request->get('knownAddress'));

            $em = Util::em();
            $repository = $em->getRepository(\SalexUserBundle\Entity\User::class);
            $user = $repository->findOneBy(array('email' => $user->getEmail()));

            $countryRepository = $em->getRepository(\CoreBundle\Entity\Country::class);
            $country = $countryRepository->findOneBy(array('iso3Code'=>$countryCode));

            $userId = $user->getId();
            $phoneUtil = \libphonenumber\PhoneNumberUtil::getInstance();

            if ($user->isTestData() || Bundle::isEsSolo()) {
                $isValidp = $isValidmp = $isValidwp = true;
                $phones = [$phone, $mobilephone, $workphone];
                $swissNumberProtop = $phone;
                $swissNumberProtowp = $workphone;
                $swissNumberProtomp = $mobilephone;
            } else {
                try {
                    $swissNumberProtop = $phoneUtil->parse($phone, $country->getIsoCode());
                    $isValidp = $phoneUtil->isValidNumber($swissNumberProtop);

                    $swissNumberProtomp = $phoneUtil->parse($mobilephone, $country->getIsoCode());
                    $isValidmp = $phoneUtil->isValidNumber($swissNumberProtomp);

                    $swissNumberProtowp = $phoneUtil->parse($workphone, $country->getIsoCode());
                    $isValidwp = $phoneUtil->isValidNumber($swissNumberProtowp);
                } catch (NumberParseException $ex) {
                    throw new PortalException('Invalid phone number: ' . $ex->getMessage() . ' (' . $ex->getErrorType() . ')');
                }

                $phones = [
                    $phoneUtil->format($swissNumberProtop, \libphonenumber\PhoneNumberFormat::INTERNATIONAL),
                    $phoneUtil->format($swissNumberProtomp, \libphonenumber\PhoneNumberFormat::INTERNATIONAL),
                    $phoneUtil->format($swissNumberProtowp, \libphonenumber\PhoneNumberFormat::INTERNATIONAL),
                ];
            }
            $query = $repository->createQueryBuilder('user');
            $expr = $query->expr();
            $users = $query->where($expr->andX(
                $expr->neq('user.id', $userId),
                $expr->orX(
                    $expr->in('user.phone', $phones),
                    $expr->in('user.mobilephone', $phones),
                    $expr->in('user.workphone', $phones)
                )
            ))->getQuery()->getResult();

            // Allow the same phone numbers for different card programs in Es Solo
            if (Bundle::isEsSolo()) {
                $nestorProgramId = $request->get('nestorProgramId');
                if ($nestorProgramId) {
                    $cp = CardProgram::findByKycProviderProgramId($nestorProgramId);
                    if ($cp) {
                        $users = array_filter($users, function (User $user) use ($cp) {
                            return $user->getParticipatedCardPrograms($cp->getId());
                        });
                    }
                }
            }

            if(count($users)) {
                /** @var User $other */
                $other = $users[0];
                $msg = 'An account already exists, please log into your account!';
                if (Util::isAPI()) {
                    return new FailedResponse($msg, [
                        'close_logout' => $other->getId(),
                        'reason' => 'Same phone number: ' . implode(', ', $phones),
                    ]);
                }
                return new ErrorResponse($msg,
                    '/user/close_logout?type=duplicated&other=' . $other->getId()
                        . '&reason=Same phone number: ' . implode(', ', $phones),
                    'OK');
            }
            if(!$isValidp or !$isValidmp or !$isValidwp) {
                return new ErrorResponse('The phone number format is invalid in the selected country!');
            }

            $user->setAddress($address);
            $user->setAddressline($addressline);
            $user->setCountryid($country->getId());
            $user->setStateid($stateid);
            $user->setCity($city);

            if ($user->isTestData() || Bundle::isEsSolo()) {
                $user->setPhone($phone)
                    ->setMobilephone($mobilephone)
                    ->setWorkphone($workphone);
            } else {
                $user->setPhone($phoneUtil->format($swissNumberProtop, \libphonenumber\PhoneNumberFormat::INTERNATIONAL));
                $user->setMobilephone($phoneUtil->format($swissNumberProtomp, \libphonenumber\PhoneNumberFormat::INTERNATIONAL));
                $user->setWorkphone($phoneUtil->format($swissNumberProtowp, \libphonenumber\PhoneNumberFormat::INTERNATIONAL));
            }

            $user->setZip($zip);
            $user->setKnownAddress($knownAddress=="1");

            $user->setRegisterStep(RegisterStep::CONSUMER_PERSONAL_INFO_ADDED);
            $em->persist($user);

            if ($user->isTestData()) {
                $pin = new UserPin();
                $pin->setUser($user)
                    ->setPhoneType(UserPin::PHONE_TYPE_MOBILE)
                    ->setPhone($mobilephone)
                    ->setPin('999')
                    ->setStatus(UserPin::STATUS_NEW);
                $em->persist($pin);
            }

            $uc = $user->getLoadingCard();
            $uc->setCurrency($request->get('currency'));
            $em->persist($uc);

            $em->flush();

            $userToken = new UsernamePasswordToken($user, $user->getPassword(), ['ROLE_ADMIN']);
            $this->container->get("security.token_storage")->setToken($userToken);

            $user->logIP('personal_info');

            if (Bundle::isEsSolo()) {
                $response = EsSoloService::fillUserBillingAddress($user);
                if (!($response instanceof SuccessResponse)) {
                    return $response;
                }
            }

            if (Util::isAPI()) {
                return new SuccessResponse($user->toFinalApiArray(), 'Personal Info Update Successfully, you may now proceed with the ID verification process for this user.');
            }

            return $this->redirect('/consumer-register/billing-address');
        }

        if (Util::isAPI()) {
            return new FailedResponse('Unknown user.');
        }

        return $this->redirect('/choose-card');
    }

    /**
     * @Route("/consumer-register/billing-address",name="consumer_register_billing_address", methods={"GET"})
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\RedirectResponse|Response
     */
    public function BillingAddress(Request $request) {
        Util::longRequest();

        /** @var User $user */
        $user = $this->getUser();
        if ($user) {
            if (!$user->getMobilephone()) {
                return $this->redirect('/consumer-register/personal-info');
            }

            $failKey = $this::RESHIPPER_INVALID;
            $failInfo = null;
            $session = $request->getSession();

            $uc = $user->getLoadingCard();
            if (!$uc) {
                return $this->redirect('/choose-card');
            }

            if ($session && $session->has($failKey)) {
                $failInfo = $session->get($failKey);
                $session->remove($failKey);
            }

            if ( $uc && $uc->isCashOnWeb() && !$user->getEmailVerified()) {
              return $this->redirect('/consumer-register/confirm-email');
            }

            if (($uc && $uc->isUsUnlocked()) || ($uc && $uc->isCashOnWeb())) {
                $ba = $user->getUsuShippingAddress($uc);
                if ($ba) {
                    $sa = $ba->getReshipperAddress();
                } else {
                    $sa = new ReshipperAddress();
                    $sa->setPhone($user->getMobilephone())
                        ->setDefaultAddress($user->getAddress())
                        ->setSecondAddress($user->getAddressline())
                        ->setCountry($user->getCountry())
                        ->setState($user->getState())
                        ->setCity($user->getCity())
                        ->setZipCode($user->getZip())
                        ->setUser($user)
                        ->setUserCard($uc);

                    try {
                        $config = $user->ensureConfig();
                        $config->setPlatform(UserConfig::PLATFORM_USUNLOCKED)
                            ->persist();
                    } catch (\Throwable $t) {
                        Log::exception('Failed to save the USU platform to user', $t, [
                            'user' => $user->getId(),
                        ]);
                    }
                }
                $countries = Util::em()->getRepository(\CoreBundle\Entity\Country::class)
                    ->getCountriesWithCardPrograms([
                        $uc->getCardProgram()->getId(),
                    ]);

                if ($uc->isUsUnlocked() && !$user->isInternalTester()) {
                    $temp = [];
                    foreach ($countries as $item) {
                        if ($item->getIsoCode() !== 'US') {
                            $temp[] = $item;
                        }
                    }
                    $countries = $temp;
                }

                return $this->render('@Portal/Register/billing-address-new.html.twig', [
                    'sa' => $sa,
                    'countryList' => $countries,
                ]);
            }

            $shippers = Util::em()->getRepository(\CoreBundle\Entity\Reshipper::class)
                ->getItems();
            $autoSignUpShippers = Util::em()->getRepository(\CoreBundle\Entity\Reshipper::class)
                ->getItemsByAutoSignUp(1);

            $affiliate = $user->getAffiliate();
            if ($affiliate) {
                $ars = $affiliate->getReshippers();
                if ($ars && $ars->count()) {
                    $shippers = [];
                    $autoSignUpShippers = [];
                    /** @var AffiliateReshipper $ar */
                    foreach ($ars as $ar) {
                        $reshipper = $ar->getReshipper();
                        if ($ar->getAutoSignup()) {
                            $autoSignUpShippers[] = $reshipper;
                        } else {
                            $shippers[] = $reshipper;
                        }
                    }
                }
            }

            $card = $uc->getCard();
            $this->checkCountry($user, $card);
            $data = [
                'shippers'           => $shippers,
                'autoSignUpShippers' => $autoSignUpShippers,
                'reloadable'         => $card->isReloadable(),
                'userCard'           => $uc,
                'cardName'           => $card->getFullName(),
                'failInfo'           => $failInfo,
                'error'              => $failInfo ? $failInfo['error'] : '',
                'user'               => $user,
            ];
            if ($card->getCardProgram()->getHasCustomerAddress()) {
                $address = new ReshipperAddress();
                $address->setIsSuite(false);
                $address->setAddressTypeName('Custom');
                $form = $this->createForm(ReshipperAddressType::class, $address);
                $data['reshipperAddressForm'] = $form->createView();
            }
            return $this->render('@Portal/Register/billing-address.html.twig', $data);
        }
        return $this->redirect('/choose-card');
    }

    protected function responseError(SessionInterface $session, $api, $failKey,
                                     ResultEntity $result, $redirectUrl = '/consumer-register/billing-address')
    {
        if ($api) {
            return new FailedResponse($result->getError());
        }
        $failInfo = array(
            'error' => $result->getError()
        );
        $session->set($failKey, $failInfo);
        return $this->redirect($redirectUrl);
    }

    /**
     * @Route("/consumer-register/billing-address",name="consumer_register_billing_address_add", methods={"POST"})
     */
    public function AddBillingAddress(Request $request, User $user = null) {
        $failKey = RegisterController::RESHIPPER_INVALID;
        $failInfo = null;
        /** @var User $user */
        $user = $user ?: $this->getUser();
        $session = $request->getSession();
        $em = Util::em();
        $api = Util::isAPI();
        if($user) {
            $uc = $user->getLoadingCard();
            $shipperId = trim($request->request->get('shipper'));
            //auto sign up
            if($shipperId == 0)
            {
                $shipperId = trim($request->request->get('autoSignUpShipper'));
                $shipperRepository = $em->getRepository(\CoreBundle\Entity\Reshipper::class);
                $shipper = $shipperRepository->findOneBy(array('id' => $shipperId));

                /** @var Reshipper $shipper */
                if($shipper->is(Reshipper::OPAS) || $shipper->is(Reshipper::TRANS_EXPRESS)) {
                    $shipperService = $shipper->getService();

                    /** @var ResultEntity $result */
                    $result = $shipperService->autoSignUp($user);
                    if (!$result) {
                        return new ErrorResponse('This auto sign up feature is not implemented for reshippper' . $shipper->getName()
                            . '. Please contact administrator!');
                    }
                    if ($result->getResult()) {
                        $shipperService->updateBillingAddress($uc, null, null, $result->getData());
                    } else {
                        return $this->responseError($session, $api, $failKey, $result);
                    }
                }
            } else if ($shipperId === '-2') {
                $raMeta = $request->get('reshipper_address');
                if (!empty($raMeta['country']) && Bundle::isUsUnlocked()) {
                    $country = Country::findByCode($raMeta['country']);
                    if ($country && !$country->canSignupInUsu()) {
                        $created = $user->getCreatedAt();
                        if ($created && Carbon::create(2022, 11, 18, 0, 0, 0)->isBefore($created)) {
                            $context = [
                                'user' => $user->getId(),
                                'email' => $user->getEmail(),
                                'phone' => $user->getMobilephone(),
                                'country' => $country->getName(),
                                'continent' => $country->getContinent(),
                            ];
                            Log::warn('Disable signing up from Europe country', $context);

                            $blocked = Data::getArray('usu_blocked_eu_users');
                            $blocked[$user->getEmail()] = $context;
                            Data::setArray('usu_blocked_eu_users', $blocked);

                            return new ErrorResponse('Your region is not available temporarily. Please try again later. We will also update you via email once it is resumed. Sorry for the inconvenience!');
                        }
                    }
                }

                $usa = $user->getBillingAddress() ?: new UserBillingAddress();
                $usa->setReshipper(Reshipper::find(Reshipper::CUSTOM));
                $usa->setUserCard($uc);
                $usa->addReshipperAddress($raMeta, $uc);
                Util::persist($usa);

                $uc->setBillingAddress($usa);
                Util::persist($uc);

                // Fill user's country/state info if it's not filled before, for instance, in the new Privacy
                // registration process.
                $ra = $usa->getReshipperAddress();
                if ($ra) {
                    if (Bundle::isUsUnlocked()) {
                        $user->setCountry($ra->getCountry())
                            ->setState($ra->getState())
                            ->setCity($ra->getCity())
                            ->setZip($ra->getZipCode())
                            ->setAddress($ra->getDefaultAddress())
                            ->setAddressline($ra->getSecondAddress())
                        ;

                        $config = $user->ensureConfig();
                        $config->setPlatform(UserConfig::PLATFORM_USUNLOCKED);
                    } if (Bundle::isCashOnWeb()) {
                      $user->setCountry($ra->getCountry())
                          ->setState($ra->getState())
                          ->setCity($ra->getCity())
                          ->setZip($ra->getZipCode())
                          ->setAddress($ra->getDefaultAddress())
                          ->setAddressline($ra->getSecondAddress())
                      ;

                      $config = $user->ensureConfig();
                      $config->setPlatform(UserConfig::PLATFORM_CASHONWEB);
                  } else {
                        $user->setCountry($user->getCountry() ?: $ra->getCountry())
                            ->setState($user->getState() ?: $ra->getState())
                            ->setCity($user->getCity() ?: $ra->getCity())
                            ->setZip($user->getZip() ?: $ra->getZipCode())
                            ->setAddress($user->getAddress() ?: $ra->getDefaultAddress())
                            ->setAddressline($user->getAddressline() ?: $ra->getSecondAddress())
                        ;
                    }
                }
            } else {
                //not auto sign up
                $em = Util::em();
                $shipperRepository = $em->getRepository(\CoreBundle\Entity\Reshipper::class);
                /** @var Reshipper $shipper */
                $shipper = $shipperRepository->findOneBy(array('id' => $shipperId));
                $useRepository = $em->getRepository(\SalexUserBundle\Entity\User::class);
                /** @var User $user */
                $user = $useRepository->findOneBy(array('email' => $user->getEmail()));

                $shipperService = $shipper->getService();
                if ($shipper->getAccountValidation() and !$shipper->getIdValidation()) {
                    $email = trim($request->request->get('email_' . $shipperId));
                    $password = trim($request->request->get('password_' . $shipperId));
                    $data['email'] = $email;
                    $data['password'] = $password;

                    $result = $shipperService->validateAccount($data);
                    if ($result->getResult()) {
                        $_d = $result->getData();
                        if ($_d) {
                            $data = array_merge($data, $_d);
                        }
                        $shipperService->updateBillingAddress($uc, null, null, $data);
                    } else {
                        return $this->responseError($session, $api, $failKey, $result);
                    }
                } else {
                    $address = null;
                    $content = null;
                    $addressId = null;
                    if (count($shipper->getAddresses()) == 1) {
                        $address = $shipper->getAddresses()->get(0);
                        $content = trim($request->request->get('suite_' . $shipperId));
                    } else {
                        $addressId = trim($request->request->get('address_index_' . $shipperId));
                        $content = trim($request->request->get('suite_' . $addressId));
                        $addressRepository = $em->getRepository(\CoreBundle\Entity\ReshipperAddress::class);
                        $address = $addressRepository->findOneBy(array('id' => $addressId));
                    }

                    $data['address'] = $address;
                    if ($shipper->getAccountValidation() and $shipper->getIdValidation()) {
                        $data['userID'] = $user->getId();
                        $data['suitID'] = $content;

                        if ($addressId != -2) {
                            $result = $shipperService->validateAccount($data);
                            if ($result->getResult()) {
                                $_d = $result->getData();
                                if ($_d) {
                                    $data = array_merge($data, $_d);
                                }
                                $shipperService->updateBillingAddress($uc, null, null, $data);
                            } else {
                                return $this->responseError($session, $api, $failKey, $result);
                            }
                        } else if ($shipper->getAutoSignUp()) {
                            /** @var ResultEntity $result */
                            $result = $shipperService->autoSignUp($user);
                            if (!$result) {
                                return new ErrorResponse('This auto sign up feature is not implemented for reshippper' . $shipper->getName()
                                    . '. Please contact administrator!');
                            }
                            if ($result->getResult()) {
                                $data['suitID'] = $result->getData()['suiteID'];
                                $shipperService->updateBillingAddress($uc, null, null, $data);
                            } else {
                                return $this->responseError($session, $api, $failKey, $result);
                            }
                        }
                    } else {
                        $data['custom_address'] = $content;
                        $shipperService->updateBillingAddress($uc, null, null, $data);
                    }
                }
            }

            $user->persist();
            RegisterStep::updateUser($user->refresh());

            $user->logIP('billing_address');

            if ($api) {
                return new SuccessResponse();
            }

            $card = $uc->getCard();

            Email::sendWithTemplateToUser($user, $card->getCardProgram()->isCashOnWeb() ? Email::COW_TEMPLATE_REGISTERED : Email::TEMPLATE_REGISTERED, [
                'action_url' => Util::host() . '/consumer-register/card-load?startup',
            ], $card->getCardProgram());

            if ($uc->isUsUnlocked()) {
                // if (Util::isServer()) {
                //     $uiv = $user->getIdVerify(true);
                //     foreach ($uiv->getFiles() as $file) {
                //         $uiv->removeFile($file);
                //     }

                //     /** @var ExternalInvoke $ei */
                //     list($ei) = IDologyService::ofac($user);
                //     if ($ei && $ei->isFailed()) {
                //         $error = $ei->getError();
                //         $logout = false;
                //         $msg = $error;
                //         if ($error === IDologyService::OFAC_ALERT_MESSAGE) {
                //             $user->setStatus(User::STATUS_CLOSED)
                //                 ->setClosureReason('["OFAC"]')
                //                 ->persist();
                //             $user->addNote('Account closed because OFAC alert.');
                //             $logout = true;
                //             $msg = 'We are sorry but we cannot offer you our Virtual Payment Cards.';
                //         }

                //         $uiv->setStatus(UserIdVerify::STATUS_INELIGIBLE)
                //             ->setReason($error)
                //             ->setResult($ei->getResponse())
                //             ->persist();

                //         $response = new ErrorResponse($msg, $logout ? '/logout' : null, 'OK');
                //         if ($logout) {
                //             return Util::clearCookie($request, $response);
                //         }
                //         return $response;
                //     }
                // }
                SlackService::sendUsuEvents($user, 'Update Home Address');
                return $this->redirect('/consumer-register/all-set');
            }
            if ($uc->isCashOnWeb()) {
              // Log::debug('Cow Ofac start');
              // Log::debug('is Server'.Util::isServer());
              if (Util::isServer()) {
                $uiv = $user->getIdVerify(true);
                foreach ($uiv->getFiles() as $file) {
                    $uiv->removeFile($file);
                }

                /** @var ExternalInvoke $ei */
                list($ei) = IDologyService::ofac($user);
                if ($ei && $ei->isFailed()) {
                    $error = $ei->getError();
                    $logout = true;
                    $msg = $error;
                    // if ($error === IDologyService::OFAC_ALERT_MESSAGE) {
                    $user->setStatus(User::STATUS_CLOSED)
                        ->setClosureReason('["OFAC"]')
                        ->persist();
                    $user->addNote('Account closed because OFAC alert.');
                    // $logout = true;
                    $msg = 'We’re sorry, according to the information provided and our identity verification provider, we cannot issue you an account at this time. Should this status change you will be notified via email. Thank You.';
                   // }
                    $uiv->setStatus(UserIdVerify::STATUS_INELIGIBLE)
                        ->setReason($error)
                        ->setResult($ei->getResponse())
                        ->persist();

                    $response = new ErrorResponse($msg, $logout ? '/logout' : null, 'OK');
                    if ($logout) {
                        return Util::clearCookie($request, $response);
                    }
                    return $response;
                }
              }
              return $this->redirect('/consumer-register/all-set');
            }
            $reloadable =  $card->isReloadable();
            if(!$reloadable) {
                if($user->getRegisterStep() ===RegisterStep::CONSUMER_BILLING_ADDRESS_BACK) {
                    return $this->redirect('/');
                }
                return $this->redirect('/consumer-register/card-load?startup');
            }
            if ($user->getRegisterStep() ===RegisterStep::CONSUMER_BILLING_ADDRESS_BACK) {
                return $this->redirect('/');
            }
            return $this->redirect('/consumer-register/id-verification');
        }
        return $this->redirect('/choose-card');
    }

    /**
     * @Route("/consumer-register/all-set")
     * @param Request $request
     *
     * @return Response
     */
    public function allSetAction(Request $request)
    {
        $user = $this->getUser();
        if (!$user) {
            return $this->redirect('/login');
        }

        $uc = $user->getOneCardInPlatform();

        $platform = Util::platform();
        if ($platform && $platform->isTernCommerce()) {
            Email::sendWithTemplateToUser($user, Email::TEMPLATE_ACCOUNT_CREATION_FOLLOWUP);
        }

        return $this->render('@Portal/Register/all-set.html.twig', [
            'cpName' => $uc ? $uc->getCardProgram()->getName() : '',
        ]);
    }

    /**
     * @Route("/consumer-register/change-card", methods={"GET"})
     *
     * @param Request $request
     * @return ErrorResponse|\Symfony\Component\HttpFoundation\RedirectResponse
     * @throws \Doctrine\ORM\ORMInvalidArgumentException
     */
    public function changeCardAction(Request $request)
    {
        $userCardId = $request->get('user_card');
        if ($userCardId) {
            $em = Util::em();
            $userCard = $em->getRepository(\CoreBundle\Entity\UserCard::class)->find($userCardId);
            if ($userCard) {
                if ($userCard->getInitializedAt()) {
                    return new ErrorResponse('Upgrading your card while a card load payment is in progress
                        can be done on <a href="/card-management">Card Management</a> page. ');
                }
            }
        }
        return $this->redirect('/choose-card');
    }
}
