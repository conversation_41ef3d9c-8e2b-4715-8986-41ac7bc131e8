<?php

namespace SkuxBundle\Controller\Mobile;

use CoreBundle\Response\SuccessResponse;
use SkuxBundle\Services\FisPurseService;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class CardController extends BaseController
{
    /**
     * @Route("/skux/m/card", methods={"GET"})
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function getAction(Request $request)
    {
        $uc = FisPurseService::aggregatedCard($this->user);
        $data = $uc->toApiArrayForSkux();
        $data = $this->skux()->fixCardDetails($data);
        return new SuccessResponse($data);
    }

    /**
     * @Route("/skux/m/cards", methods={"GET"})
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function getCards(Request $request)
    {
        $result = $this->skux()->getUserCards();
        return new SuccessResponse($result);
    }
}
