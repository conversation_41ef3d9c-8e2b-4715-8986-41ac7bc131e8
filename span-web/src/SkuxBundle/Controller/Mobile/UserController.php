<?php

namespace SkuxBundle\Controller\Mobile;

use CoreBundle\Entity\UserToken;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Util;
use SalexUserBundle\Entity\User;
use Symfony\Component\HttpFoundation\File\File;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class UserController extends BaseController
{
    /**
     * @Route("/skux/m/user/social-url", methods={"GET"})
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function getSocialLoginUrl(Request $request)
    {
        $skux = $this->skux();
        $result = $skux->getLoginEndpoint();
        return new SuccessResponse($result);
    }

    /**
     * @Route("/skux/m/user", methods={"GET"})
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function getAction(Request $request)
    {
        $user = $this->user;
        $this->skux()->updateUserProfile($user);
        $data = $this->user->toSkuxApiArray();

        $data['everClaimedOffer'] = $user->everClaimedOffer();

        return new SuccessResponse($data);
    }

    /**
     * @Route("/skux/m/user", methods={"POST"})
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function saveAction(Request $request)
    {
        $name = $this->validateRequired('name');
        $names = explode(' ', $name);

        $first = $names[0] ?? '';
        $last = implode(' ', array_slice($names, 1));

        $this->user->setFirstName($first)
            ->setLastName($last);

        /** @var File $avatar */
        $avatar = $request->files->get('avatar');
        if ($avatar) {
            $this->user->setProfilePictureFile($avatar);
        }

        $this->user->persist();

        return new SuccessResponse($this->user->toSkuxApiArray());
    }

    /**
     * @Route("/skux/m/user/terms", methods={"POST"})
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function termsAction(Request $request)
    {
        $accept = $request->get('accept');
        $accept = $accept === true || $accept === 'true';

        Util::updateMeta($this->user, [
            'acceptApplePayTerms' => $accept,
        ]);

        if ($accept) {
            Util::updateMeta($this->user, [
                'acceptApplePayTermsAt' => date('c'),
            ]);
        } else {
            Util::updateMeta($this->user, 'acceptApplePayTermsAt');
        }

        return new SuccessResponse($this->user->toSkuxApiArray());
    }

    /**
     * @Route("/skux/m/user/refresh-token", methods={"POST"})
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function refreshTokenAction(Request $request)
    {
        $this->skux()->refreshToken();

        $token = null;
        if ($request->get('force')) {
            $uts = $this->em->getRepository(UserToken::class)
                ->createQueryBuilder('ut')
                ->where('ut.user = :user')
                ->setParameter('user', $this->user)
                ->getQuery()
                ->getResult();
            $now = new \DateTime();
            /** @var UserToken $ut */
            foreach ($uts as $ut) {
                $ut->setExpireAt($now);
            }
            $this->em->flush();

            $ut = UserToken::create($this->user);
            $token = $ut->getToken();
        }

        return new SuccessResponse($token);
    }
}
