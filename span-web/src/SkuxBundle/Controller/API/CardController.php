<?php


namespace SkuxBundle\Controller\API;

use ApiBundle\Entity\Coin;
use Carbon\Carbon;
use CoreBundle\Entity\ExternalInvoke;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Entity\UserCardPurse;
use CoreBundle\Entity\UserCardTransaction;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Util;
use OpenApi\Annotations as SWG;
use SkuxBundle\Services\FisPurseService;
use Symfony\Component\Routing\Annotation\Route;

class CardController extends BaseController
{
    /**
     * @Route("/api/skux/card/assign", methods={"POST"}, name="api_skux_card_assign")
     * @SWG\Post(
     *   tags={"Cards"},
     *   summary="Assign card",
     *   description="Assign a card for the user. The system will also try to load 0 to this card to initialize it. Call this API again to assign a new card.",
     *   security={{"x-signature":{}, "x-salt":{}, "x-timestamp":{}, "x-access-key":{}}},
     *   @SWG\RequestBody(required=true,@SWG\MediaType(mediaType="application/x-www-form-urlencoded",schema=@SWG\Schema(required={"userId"}, properties={
     *     @SWG\Property(property="userId", description="The user ID returned from the user register API", type="integer"),
     *     @SWG\Property(property="packageId", description="Package ID which describes card fulfillment attributes. Provided by FIS. Omit to use the default value in our system", type="string"),
     *   }))),
     *   @SWG\Response(response=200, ref="#/components/schemas/SkuxUserCardResponse"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     */
    public function assign()
    {
        $request = $this->validateParameters(__METHOD__);
        $user = $this->validateUser($request->get('userId'), Role::ROLE_SKUX_CONSUMER, false);

        $uc = FisPurseService::assignCard($user, $request->get('packageId'));

        $message = '';
        try {
            $this->request->request->set('userCardId', $uc->getId());
            $this->request->request->set('amount', 0);
            $this->load();
        } catch (\Exception $e) {
            $message = 'The card is assigned but failed to load it: ' . $e->getMessage();
        }

        return new SuccessResponse($uc->toApiArrayForSkux(), $message);
    }

    /**
     * @Route("/api/skux/card/list", methods={"GET"}, name="api_skux_card_list")
     * @SWG\Get(
     *   tags={"Cards"},
     *   summary="List cards",
     *   description="List all cards assigned to the user.",
     *   security={{"x-signature":{}, "x-salt":{}, "x-timestamp":{}, "x-access-key":{}}},
     *   @SWG\Parameter(name="userId", description="The user ID returned from the user register API", in="query", schema=@SWG\Schema(type="integer"), required=true),
     *   @SWG\Response(response=200, ref="#/components/schemas/SkuxUserCardsResponse"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     */
    public function list()
    {
        $request = $this->validateParameters(__METHOD__);
        $user = $this->validateUser($request->get('userId'), Role::ROLE_SKUX_CONSUMER, false);

        $ucs = $user->getCardsInPlatforms();
        $result = [];
        /** @var UserCard $uc */
        foreach ($ucs as $uc) {
            $result[] = $uc->toApiArrayForSkux();
        }

        return new SuccessResponse($result);
    }

    /**
     * @Route("/api/skux/card/load", methods={"POST"}, name="api_skux_card_load")
     * @SWG\Post(
     *   tags={"Cards"},
     *   summary="Load value",
     *   description="Add value to the card/purse. This is required to query the balance or make a transfer. You can load 0 to intitlize it if it's not.",
     *   security={{"x-signature":{}, "x-salt":{}, "x-timestamp":{}, "x-access-key":{}}},
     *   @SWG\RequestBody(required=true, @SWG\MediaType(mediaType="application/x-www-form-urlencoded", schema=@SWG\Schema(required={}, properties={
     *     @SWG\Property(property="userCardId", description="User Card Id returned from the assign API", type="integer"),
     *     @SWG\Property(property="purseNo", description="The purse number returned from the previous calls or other valid values you know. Omit to load to the default purse.", type="integer"),
     *     @SWG\Property(property="amount", description="Load amount. Cents in USD. 0 to $1000. Contact us for higher values.", type="integer"),
     *     @SWG\Property(property="comment", description="Comment", type="string"),
     *     @SWG\Property(property="refNo", description="Custom reference to this load", type="string"),
     *   }))),
     *   @SWG\Response(response=200, ref="#/components/schemas/SkuxPurseDetailsResponse"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     */
    public function load()
    {
        $request = $this->validateParameters(__METHOD__);
        $uc = $this->validateUserCard();

        $amount = $request->get('amount') ?? 0;
        if ($amount < 0) {
            return new FailedResponse('Invalid amount!');
        }

        $max = 100000;
        if ($amount > $max) {
            return new FailedResponse('Amount cannot be greater than ' . Money::formatUSD($max) . '!');
        }

        $purseNo = $this->validatePurseNo() ?: $uc->getDbaNo();

        $now = Carbon::now();
        $currency = 'USD';

        $ucl = new UserCardLoad();
        $ucl->setUserCard($uc)
            ->setInitialCurrency($currency)
            ->setInitialAmount($amount)
            ->setStatus(UserCardLoad::STATUS_COMPLETED)
            ->setPayCurrency($currency)
            ->setPayAmount($amount)
            ->updateFeeStructure([
                'discount'      => 0,
                'origin'        => [
                    'loadFee'       => 0,
                    'membershipFee' => 0,
                ],
                'loadFee'       => 0,
                'membershipFee' => 0,
                'replacementFee' => 0,
                'otherFees'     => [],
                'totalAmount'   => $amount,
            ])
            ->setCompletedAt($now)
            ->setType(UserCardLoad::TYPE_LOAD_CARD)
            ->setServerKey(Util::getServerKey())
            ->setFvNo($purseNo)
            ->setReload(true)
            ->setPaymentReference($request->get('refNo'))
            ->setInitializedAt($now)
            ->setMeta(Util::j2s([
                'fisPurseLoad' => true,
            ]))
            ->persist()
        ;
        $ucl->getTransactionNo(true);

        $ucl->setLoadAmount($amount)
            ->setReceivedCurrency($currency)
            ->setReceivedAmount($amount)
            ->setMembershipFeeUSD(0)
            ->setLoadFeeUSD(0)
            ->setLoadStatus(UserCardLoad::LOAD_STATUS_PENDING, false)
            ->persist();

        $api = FisPurseService::getAPI();

        /** @var ExternalInvoke $ei */
        [$ei] = $api->loadValue(
            $uc->getAccountNumber(),
            null,
            $amount,
            $request->get('comment'),
            $purseNo,
            0,
            $ucl->getTransactionNo(),
        );

        FisPurseService::syncBalanceStatusSilently($uc);

        if (!$purseNo) {
            $purseNo = $uc->getDbaNo();
        }

        if ($ei && $ei->isFailed()) {
            $ucl->setLoadStatus(UserCardLoad::LOAD_STATUS_ERROR, false)
                ->setError($ei->getError())
                ->persist();
            return new FailedResponse($uc->toApiArrayForSkux());
        }

        $ucl->setLoadStatus(UserCardLoad::LOAD_STATUS_LOADED, false)
            ->setLoadAt(Carbon::now())
            ->setFvNo($purseNo)
            ->persist();

        $purse = $uc->findPurseByNumberOrDummy($purseNo);
        return new SuccessResponse($purse->toApiArrayForSkux());
    }

    /**
     * @Route("/api/skux/card/balance", methods={"GET"}, name="api_skux_card_balance")
     * @SWG\Get(
     *   tags={"Cards"},
     *   summary="Card/purse balance",
     *   description="Get card/purse balance",
     *   security={{"x-signature":{}, "x-salt":{}, "x-timestamp":{}, "x-access-key":{}}},
     *   @SWG\Parameter(name="userCardId", description="User Card Id returned from the assign API", in="query", schema=@SWG\Schema(type="string"), required=true),
     *   @SWG\Parameter(name="purseNo", description="The purse number returned from the previous calls. Omit to get the card total balance.", in="query", schema=@SWG\Schema(type="integer")),
     *   @SWG\Response(response=200, ref="#/components/schemas/CoinResponse"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     */
    public function balance()
    {
        $this->validateParameters(__METHOD__);
        $uc = $this->validateUserCard();
        $purseNo = $this->validatePurseNo();

        $api = FisPurseService::getAPI();
        $data = ExternalInvoke::host(
            $api->openToBuy(
                $uc->getAccountNumber(),
                null,
                $purseNo
            )
        );

        $balance = Money::normalizeAmount($data[0] ?? 0, 'USD');

        if ($purseNo) {
            $purse = $uc->findPurseByNumber($purseNo);
            if ($purse) {
                $purse->setBalance($balance);
                Util::persist($purse);
            }
        } else {
            $uc->setBalance($balance)
                ->persist();
        }

        return new SuccessResponse(Coin::create($balance, 'USD')->toApiArray());
    }

    /**
     * @Route("/api/skux/card/purses", methods={"GET"}, name="api_skux_card_purses")
     * @SWG\Get(
     *   tags={"Cards"},
     *   summary="Purse list",
     *   description="Get all purses of the card",
     *   security={{"x-signature":{}, "x-salt":{}, "x-timestamp":{}, "x-access-key":{}}},
     *   @SWG\Parameter(name="userCardId", description="User Card Id returned from the assign API", in="query", schema=@SWG\Schema(type="string"), required=true),
     *   @SWG\Response(response=200, ref="#/components/schemas/SkuxPurseListResponse"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     */
    public function purses()
    {
        $this->validateParameters(__METHOD__);
        $uc = $this->validateUserCard();

        FisPurseService::syncBalanceStatus($uc);
        $ps = $uc->getPurses();
        $result = [];
        /** @var UserCardPurse $p */
        foreach ($ps as $p) {
            $result[] = $p->toApiArrayForSkux();
        }

        return new SuccessResponse($result);
    }

    /**
     * @Route("/api/skux/card/purse", methods={"GET"}, name="api_skux_card_purse")
     * @SWG\Get(
     *   tags={"Cards"},
     *   summary="Purse details",
     *   description="Get purse details like balance, status, number, etc.",
     *   security={{"x-signature":{}, "x-salt":{}, "x-timestamp":{}, "x-access-key":{}}},
     *   @SWG\Parameter(name="userCardId", description="User Card Id returned from the assign API", in="query", schema=@SWG\Schema(type="string"), required=true),
     *   @SWG\Parameter(name="purseNo", description="The purse number returned from the previous calls. Omit to return the default purse.", in="query", schema=@SWG\Schema(type="integer")),
     *   @SWG\Response(response=200, ref="#/components/schemas/SkuxPurseDetailsResponse"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     */
    public function purse()
    {
        $this->validateParameters(__METHOD__);
        $uc = $this->validateUserCard();
        $purseNo = $this->validatePurseNo() ?? $uc->getDbaNo();

        FisPurseService::syncBalanceStatus($uc);

        $purse = $uc->findPurseByNumberOrDummy($purseNo);
        return new SuccessResponse($purse->toApiArrayForSkux());
    }

    /**
     * @Route("/api/skux/card/comment", methods={"POST"}, name="api_skux_card_comment")
     * @SWG\Post(
     *   tags={"Cards"},
     *   summary="Add comment",
     *   description="Add comment to the card",
     *   security={{"x-signature":{}, "x-salt":{}, "x-timestamp":{}, "x-access-key":{}}},
     *   @SWG\RequestBody(required=true, @SWG\MediaType(mediaType="application/x-www-form-urlencoded", schema=@SWG\Schema(required={"userCardId", "comment"}, properties={
     *     @SWG\Property(property="userCardId", description="User Card Id returned from the assign API", type="integer"),
     *     @SWG\Property(property="comment", description="Comment to be added", type="string"),
     *     @SWG\Property(property="reasonCode", description="Reason code. Defaults to 0 (Undefined). 1318 for Acceptance. Refer to the <a href='/content/protected?key=fis_reason_codes' target='blank'>doc</a> for more codes.", type="string", default="0"),
     *   }))),
     *   @SWG\Response(response=200, ref="#/components/responses/Response"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     */
    public function comment()
    {
        $request = $this->validateParameters(__METHOD__);
        $uc = $this->validateUserCard();

        $api = FisPurseService::getAPI();
        ExternalInvoke::host($api->createAccountComment(
            $uc->getAccountNumber(),
            $request->get('comment'),
            $request->get('reasonCode', 0)
        ));

        return new SuccessResponse();
    }

    /**
     * @Route("/api/skux/card/change-status", methods={"POST"}, name="api_skux_card_change_status")
     * @SWG\Post(
     *   tags={"Cards"},
     *   summary="Change status",
     *   description="Change card status",
     *   security={{"x-signature":{}, "x-salt":{}, "x-timestamp":{}, "x-access-key":{}}},
     *   @SWG\RequestBody(required=true, @SWG\MediaType(mediaType="application/x-www-form-urlencoded", schema=@SWG\Schema(required={"userCardId", "status"}, properties={
     *     @SWG\Property(property="userCardId", description="User Card Id returned from the assign API", type="integer"),
     *     @SWG\Property(property="purseNo", description="The purse number returned from the previous calls. Omit to use the default purse.", type="integer"),
     *     @SWG\Property(property="status", description="The new card status. 2: Active, 3: Closed, 6: Suspended, 7: Expired", type="string", enum={"2", "3", "6", "7"}),
     *   }))),
     *   @SWG\Response(response=200, ref="#/components/schemas/SkuxPurseDetailsResponse"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     */
    public function changeStatus()
    {
        $request = $this->validateParameters(__METHOD__);
        $uc = $this->validateUserCard();
        $purseNo = $this->validatePurseNo() ?? $uc->getDbaNo();

        $api = FisPurseService::getAPI();
        ExternalInvoke::host($api->postPurseStatus(
            $uc->getAccountNumber(),
            $purseNo,
            $request->get('status')
        ));

        FisPurseService::syncBalanceStatus($uc);

        $purse = $uc->findPurseByNumberOrError($purseNo);
        return new SuccessResponse($purse->toApiArrayForSkux());
    }

    /**
     * @Route("/api/skux/card/transfer", methods={"POST"}, name="api_skux_card_transfer")
     * @SWG\Post(
     *   tags={"Cards"},
     *   summary="Account to account transfer",
     *   description="Transfer funds from one account to another",
     *   security={{"x-signature":{}, "x-salt":{}, "x-timestamp":{}, "x-access-key":{}}},
     *   @SWG\RequestBody(required=true, @SWG\MediaType(mediaType="application/x-www-form-urlencoded", schema=@SWG\Schema(required={"fromUserCardId", "toUserCardId", "amount", "comment"}, properties={
     *     @SWG\Property(property="fromUserCardId", description="The user card to transfer funds from.", type="integer"),
     *     @SWG\Property(property="fromPurseNo", description="The purse number returned from the previous calls. Omit to use the default purse of the fromUserCard.", type="integer"),
     *     @SWG\Property(property="toUserCardId", description="The user card to transfer funds to.", type="integer"),
     *     @SWG\Property(property="toPurseNo", description="The purse number returned from the previous calls. Omit to use the default purse of the toUserCard.", type="integer"),
     *     @SWG\Property(property="amount", description="Transfer amount. Cents in USD.", type="integer"),
     *     @SWG\Property(property="comment", description="Comment", type="string"),
     *   }))),
     *   @SWG\Response(response=200, ref="#/components/responses/Response"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     */
    public function transfer()
    {
        $request = $this->validateParameters(__METHOD__);
        $fromUc = $this->validateUserCard('fromUserCardId');
        $fromPurseNo = $this->validatePurseNo($fromUc, 'fromPurseNo') ?? $fromUc->getDbaNo();
        $toUc = $this->validateUserCard('toUserCardId');
        $toPurseNo = $this->validatePurseNo($toUc, 'toPurseNo') ?? $toUc->getDbaNo();
        $amount = (int)$request->get('amount');
        if ($amount <= 0) {
            return new FailedResponse('The transfer amount needs to be positive!');
        }

        $api = FisPurseService::getAPI();
        ExternalInvoke::host($api->accountToAccountTransfer(
            $fromUc->getAccountNumber(),
            $toUc->getAccountNumber(),
            null,
            $amount,
            $request->get('comment'),
            $fromPurseNo,
            $toPurseNo
        ));

        return new SuccessResponse();
    }

    /**
     * @Route("/api/skux/card/transactions", methods={"GET"}, name="api_skux_card_transactions")
     * @SWG\Get(
     *   tags={"Cards"},
     *   summary="Get transactions",
     *   description="Get card transactions",
     *   security={{"x-signature":{}, "x-salt":{}, "x-timestamp":{}, "x-access-key":{}}},
     *   @SWG\Parameter(name="userCardId", description="User Card Id returned from the assign API", in="query", schema=@SWG\Schema(type="string"), required=true),
     *   @SWG\Parameter(name="startDate", description="Format: YYYY-mm-dd. Default to today.", in="query", schema=@SWG\Schema(type="string", format="date")),
     *   @SWG\Parameter(name="days", description="From how many days ago before the `startDate`.", in="query", schema=@SWG\Schema(type="integer")),
     *   @SWG\Response(response=200, ref="#/components/schemas/SkuxUserCardTransactionsResponse"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     */
    public function transactions()
    {
        $request = $this->validateParameters(__METHOD__);
        $uc = $this->validateUserCard();

        $api = FisPurseService::getAPI();
        $data = ExternalInvoke::host(
            $api->getCardTransactions(
                $uc->getAccountNumber(),
                $request->get('startDate'),
                $request->get('days', 30)
            )
        ) ?? [];

        $q = $this->em->getRepository(UserCardTransaction::class)
            ->createQueryBuilder('uct')
            ->where('uct.userCard = :uc')
            ->andWhere('uct.tranId = :tranId')
            ->setParameter('uc', $uc)
            ->setMaxResults(1);
        $tz = Util::tzUTC();

        $result = [];
        foreach ($data as $item) {
            if (empty($item['Reference'])) {
                continue;
            }
            $rs = (clone $q)->setParameter('tranId', $item['Reference'])
                ->getQuery()
                ->getResult();
            if ($rs) {
                $uct = $rs[0];
            } else {
                $uct = new UserCardTransaction();
            }
            $amount = $item['SettleAmount'] ?? null;
            if ($amount !== null) {
                $amount = Money::normalizeAmount($amount, 'USD');
            }
            $uct->setUserCard($uc)
                ->setTranId($item['Reference'])
                ->setTxnTime(Carbon::parse($item['UTCPostDate'], $tz))
                ->setTxnAmount($amount)
                ->setTranDesc($item['Comment'])
                ->setTranCode($item['TrxType'])
                ->setActualTranCode($item['RequestCode'])
                ->setProductName($item['Description'])
                ->setOverdraftProtected($item['ReasonDescription'])
                ->setAccountStatus($item['TranResult'])
                ->setUserField1($item['Merchant'])
                ->setUserField2($item['MerchantNo'])
            ;
            Util::updateMeta($uct, [
                'details' => $item,
            ], false);
            $this->em->persist($uct);
            $result[] = $uct;
        }
        $this->em->flush();

        foreach ($result as $i => $uct) {
            $result[$i] = $uct->toApiArrayForSKUx();
        }

        return new SuccessResponse($result);
    }
}
