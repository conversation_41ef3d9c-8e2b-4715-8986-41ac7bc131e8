<?php

namespace SkuxBundle\Entity;

use CoreBundle\Entity\UserCardPurse;
use CoreBundle\Utils\Util;
use PortalBundle\Exception\PortalException;

trait UserCardSkuxTrait
{
    /**
     * @param $number
     *
     * @return UserCardPurse|null
     */
    public function findPurseByNumber($number)
    {
        $rs = Util::em()->getRepository(UserCardPurse::class)
            ->createQueryBuilder('ucp')
            ->where('ucp.userCard = :uc')
            ->andWhere('ucp.number = :number')
            ->setParameter('uc', $this)
            ->setParameter('number', $number)
            ->setMaxResults(1)
            ->getQuery()
            ->getResult();
        return $rs ? $rs[0] : null;
    }

    public function findPurseByNumberOrError($number)
    {
        $purse = $this->findPurseByNumber($number);
        if (!$purse) {
            throw PortalException::temp('Failed to find/sync the purse details of ' . $number);
        }
        return $purse;
    }

    public function findPurseByNumberOrDummy($number)
    {
        $purse = $this->findPurseByNumber($number);
        if (!$purse) {
            $purse = new UserCardPurse();
            $purse->setUserCard($this)
                ->setNumber($number)
                ->setType('Unknown Purse');
        }
        return $purse;
    }

    public function toApiArrayForSkux()
    {
        $pan = $this->getPan();
        if (Util::isStaging()) {
            $pan = str_replace('*', '', $pan);
            if (strlen($pan) <= 2) {
                $pan = substr($this->getAccountNumber(), -4, 2) . $pan;
            }
        }
        return [
            'id' => $this->getId(),
            'accountNumber' => $this->getAccountNumber(),
            'maskedCardNumber' => Util::maskPan($pan),
            'balance' => $this->getBalance(),
            'status' => $this->getStatus(),
            'nativeStatus' => $this->getNativeStatus(),
        ];
    }
}
