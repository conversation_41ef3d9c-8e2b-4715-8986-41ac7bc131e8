<?php

namespace SkuxBundle\Services;

use Carbon\Carbon;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\CardProgramCardType;
use CoreBundle\Entity\Country;
use CoreBundle\Entity\ExternalInvoke;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserCardPurse;
use CoreBundle\Exception\FailedException;
use CoreBundle\Services\Common\CardService;
use CoreBundle\Services\SSLEncryptionService;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Util;
use FisBundle\Services\Reward\PrepaidAPI;
use SalexUserBundle\Entity\User;

class FisPurseService
{
    public static function getAPI()
    {
        return new PrepaidAPI(null, 'skux');
    }

    /**
     * @param User $user
     * @param null $packageId
     *
     * @return UserCard
     * @throws FailedException
     */
    public static function assignCard(User $user, $packageId = null)
    {
        $api = self::getAPI();
        $personId = Util::meta($user, 'fisPersonId');
        if ($personId) {
            $params = [
                'Comment' => 'Create extra card for ' . $user->getId(),
            ];

            /** @var ExternalInvoke $ei */
            [$ei, $data] = $api->assignCardToExistingPerson($personId, $params, $packageId);
            if ($ei && $ei->isFailed()) {
                throw FailedException::fromEi($ei);
            }
            if (empty($data[4])) {
                throw new FailedException('Invalid proxy number from the API response.');
            }
            [$cardNum, $personId, $cvx2, $expDate, $proxyKey] = $data;
        } else {
            $country = $user->getCountry() ?? Country::usa();
            $ssn = Util::meta($user, 'ssn');
            if ($ssn) {
                $ssn = SSLEncryptionService::tryToDecrypt($ssn);
            }

            $params = [
                'First' => $user->getFirstName(),
                'Last' => $user->getLastName(),
                'Addr1' => $user->getAddress(),
                'Addr2' => $user->getAddressline(),
                'City' => $user->getCity(),
                'Email' => $user->getEmail(),
                'Country' => $country->getNumericCode(),
                'ZipCode' => $user->getZip(),
                'Phone' => null,
                'SSN' => $ssn ?: $user->getId(),
                'ClientUniqueID' => $user->getId(),
                'Comment' => 'Create initial card for ' . $user->getId(),
            ];

            if ($user->getBirthday()) {
                $params['DOB'] = $user->getBirthday()->format('m/d/Y');
            }

            if ($user->getGender()) {
                $params['Gender'] = $user->getGenderAbbr();
            }

            /** @var ExternalInvoke $ei */
            [$ei, $data] = $api->assignCard($params, $packageId);
            if ($ei && $ei->isFailed()) {
                throw FailedException::fromEi($ei);
            }
            if (empty($data[7])) {
                throw new FailedException('Invalid proxy number from the API response.');
            }
            [$cardNum, $personId, $cvx2, $expDate, , , , $proxyKey] = $data;

            Util::updateMeta($user, [
                'fisPersonId' => $personId,
            ]);
        }

        $uc = CardService::create($user, CardProgramCardType::getForCardProgram(CardProgram::skux()));
        $uc->setAccountNumber($proxyKey)
            ->setExpireAt(Carbon::parse($expDate))
            ->setStatus(UserCard::STATUS_ACTIVE)
            ->setIssued(true)
            ->setIssuedAt(Carbon::now())
            ->setAccountId($personId)
            ->setBalance(0)
            ->persist();

        $uc->saveMaskedCardNumber($cardNum);
        $uc->setUpdatedPIN($cvx2);

        return $uc;
    }

    public static function aggregatedCard(User $user)
    {
        $uc = new UserCard();
        $uc->setAccountNumber($user->getId())
            ->setPan($user->getId())
            ->setStatus(UserCard::STATUS_ACTIVE);

        $balance = Util::em()->getRepository(UserCard::class)
            ->createQueryBuilder('uc')
            ->where('uc.user = :user')
            ->andWhere('uc.card = :card')
            ->setParameter('user', $user)
            ->setParameter('card', CardProgramCardType::getForCardProgram(CardProgram::skux()))
            ->select('sum(uc.balance)')
            ->getQuery()
            ->getSingleScalarResult();
        $uc->setBalanceOnly($balance ?: 0);

        return $uc;
    }

    public static function syncBalanceStatusSilently(UserCard $uc, UserCardPurse &$purse = null)
    {
        try {
            self::syncBalanceStatus($uc, $purse);
        } catch (\Exception $e) {
            Log::warn('Failed to sync FIS: purse status for ' . $uc->getId()
                      . ': ' . $e->getMessage());
        }
    }

    /**
     * @param UserCard           $uc
     * @param UserCardPurse|NULL $purse
     *
     * @return void
     * @throws FailedException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public static function syncBalanceStatus(UserCard $uc, UserCardPurse &$purse = null)
    {
        $api = self::getAPI();

        // Fetch card balance first
        $data = ExternalInvoke::host($api->openToBuy($uc->getAccountNumber()));
        $balance = Money::normalizeAmount($data[0] ?? 0, 'USD');
        $uc->setBalance($balance)
            ->persist();

        // Update purses
        $result = ExternalInvoke::host(
            $api->getPurseBalance(
                $uc->getAccountNumber(),
                $purse ? $purse->getNumber() : null
            )
        );

        $first = null;
        if ($purse) {
            foreach ($result as $item) {
                $it = $item['PurseNumber'] ?? '';
                if ((string)$it === $purse->getNumber()) {
                    $first = $item;
                    break;
                }
            }
        }
        if (!$first) {
            $first = $result[0] ?? [];
        }

        // Update cards first
        if (empty($uc->getDbaNo())) {
            $uc->setDbaNo($first['PurseNumber']);
        }
        $active = in_array($first['CardStatus'], ['READY', 'ACTIVE']);
        $uc->setStatus($active ? UserCard::STATUS_ACTIVE : UserCard::STATUS_INACTIVE, false, false)
            ->setNativeStatus($first['CardStatus'])
            ->persist();

        // Create or update purses
        $em = Util::em();
        foreach ($result as $it) {
            if (empty($it['PurseNumber'])) {
                continue;
            }
            $pu = $uc->findPurseByNumber($it['PurseNumber']);
            if (!$pu) {
                $pu = new UserCardPurse();
                $pu->setUserCard($uc)
                    ->setNumber($it['PurseNumber']);
            }
            $pu->setCardStatus($it['CardStatus'])
                ->setStatus($it['PurseStatus'])
                ->setType($it['PurseType'])
                ->setEffectiveDate(Carbon::parse($it['PurseEffectiveDate']))
                ->setExpirationDate(Carbon::parse($it['PurseExpirationDate']))
                ->setBalance(Money::normalizeAmount($it['PurseBalance'], 'USD'));

            Util::updateMeta($pu, [
                'fisDetail' => $it,
            ], false);

            $em->persist($pu);

            if ($purse && (string)$purse->getNumber() === (string)$pu->getNumber()) {
                $purse = $pu;
            }
        }
        $em->flush();
    }
}
