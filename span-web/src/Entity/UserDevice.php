<?php

namespace App\Entity;

use App\Repository\UserDeviceRepository;
use CoreBundle\Entity\UserToken;
use CoreBundle\Utils\Traits\EntityNewMetaTrait;
use CoreBundle\Utils\Traits\EntityTrait;
use CoreBundle\Utils\Util;
use DateTime;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use SalexUserBundle\Entity\User;

#[ORM\Entity(repositoryClass: UserDeviceRepository::class)]
#[ORM\Table(name: '`user_devices`')]
#[ORM\Index(columns: ['user_id', 'device_id'])]
class UserDevice
{
    use EntityTrait;
    use EntityNewMetaTrait;

    public function touch(): static
    {
        return $this->setActiveAt(new DateTime())
            ->persist();
    }

    public function isPinTokenTrusted(?string $pinToken): bool
    {
        if ( ! $pinToken) {
            return false;
        }
        if ($pinToken !== $this->getPinToken()) {
            return false;
        }
        if ($this->getTrustedFingerprint() !== null) {
            return true;
        }
        if ($this->isWeb() && Util::isUUID($pinToken)) {
            return true;
        }
        return false;
    }

    public function getTrustedFingerprint(): ?string
    {
        $deviceId = $this->getDeviceId();
        if (!$deviceId || $deviceId === 'xxxx' || $deviceId === 'unknown_device') {
            return null;
        }
        $pinToken = $this->getPinToken();
        if (!$pinToken || $pinToken === 'yyyy') {
            return null;
        }
        return implode('_', [$deviceId, $pinToken]);
    }

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: User::class)]
    private User $user;

    #[ORM\ManyToOne(targetEntity: UserToken::class)]
    #[ORM\JoinColumn(name: "user_token_id", referencedColumnName: "id", nullable: true, onDelete: "SET NULL")]
    private ?UserToken $userToken;

    #[ORM\Column]
    private DateTime $createdAt;

    #[ORM\Column]
    private DateTime $activeAt;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $name = null;

    #[ORM\Column(length: 63, nullable: true)]
    private ?string $ip = null;

    #[ORM\Column(length: 63, nullable: true)]
    private ?string $os = null;

    #[ORM\Column(length: 63, nullable: true)]
    private ?string $osVersion = null;

    #[ORM\Column(length: 31, nullable: true)]
    private ?string $locale = null;

    #[ORM\Column(length: 31, nullable: true)]
    private ?string $appVersion = null;

    #[ORM\Column(length: 31, nullable: true)]
    private ?string $appBuild = null;

    #[ORM\Column(length: 31, nullable: true)]
    private ?string $appInnerVer = null;

    #[ORM\Column(nullable: true)]
    private ?bool $web = null;

    #[ORM\Column(length: 1023, nullable: true)]
    private ?string $userAgent = null;

    #[ORM\Column(length: 255)]
    private ?string $deviceId = null;

    #[ORM\Column(length: 63, nullable: true)]
    private ?string $pinToken = null;

    #[ORM\Column(nullable: true)]
    private ?float $latitude = null;

    #[ORM\Column(nullable: true)]
    private ?float $longitude = null;

    /**
     * @var int|null Timezone offset in minutes
     */
    #[ORM\Column(nullable: true)]
    private ?int $timezone = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): static
    {
        $this->name = $name;

        return $this;
    }

    public function getIp(): ?string
    {
        return $this->ip;
    }

    public function setIp(?string $ip): static
    {
        $this->ip = $ip;

        return $this;
    }

    public function getOs(): ?string
    {
        return $this->os;
    }

    public function setOs(?string $os): static
    {
        $this->os = $os;

        return $this;
    }

    public function getOsVersion(): ?string
    {
        return $this->osVersion;
    }

    public function setOsVersion(?string $osVersion): static
    {
        $this->osVersion = $osVersion;

        return $this;
    }

    public function getLocale(): ?string
    {
        return $this->locale;
    }

    public function setLocale(?string $locale): static
    {
        $this->locale = $locale;

        return $this;
    }

    public function getAppVersion(): ?string
    {
        return $this->appVersion;
    }

    public function setAppVersion(?string $appVersion): static
    {
        $this->appVersion = $appVersion;

        return $this;
    }

    public function getAppBuild(): ?string
    {
        return $this->appBuild;
    }

    public function setAppBuild(?string $appBuild): static
    {
        $this->appBuild = $appBuild;

        return $this;
    }

    public function getAppInnerVer(): ?string
    {
        return $this->appInnerVer;
    }

    public function setAppInnerVer(?string $appInnerVer): static
    {
        $this->appInnerVer = $appInnerVer;

        return $this;
    }

    public function isWeb(): ?bool
    {
        return $this->web;
    }

    public function setWeb(?bool $web): static
    {
        $this->web = $web;

        return $this;
    }

    public function getDeviceId(): ?string
    {
        return $this->deviceId;
    }

    public function setDeviceId(string $deviceId): static
    {
        $this->deviceId = $deviceId;

        return $this;
    }

    public function getPinToken(): ?string
    {
        return $this->pinToken;
    }

    public function setPinToken(?string $pinToken): static
    {
        $this->pinToken = $pinToken;

        return $this;
    }

    public function getLatitude(): ?float
    {
        return $this->latitude;
    }

    public function setLatitude(?float $latitude): static
    {
        $this->latitude = $latitude;

        return $this;
    }

    public function getLongitude(): ?float
    {
        return $this->longitude;
    }

    public function setLongitude(?float $longitude): static
    {
        $this->longitude = $longitude;

        return $this;
    }

    public function getTimezone(): ?int
    {
        return $this->timezone;
    }

    public function setTimezone(?int $timezone): static
    {
        $this->timezone = $timezone;

        return $this;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): static
    {
        $this->user = $user;

        return $this;
    }

    public function getCreatedAt(): ?\DateTime
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTime $createdAt): static
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    public function getActiveAt(): ?\DateTime
    {
        return $this->activeAt;
    }

    public function setActiveAt(\DateTime $activeAt): static
    {
        $this->activeAt = $activeAt;

        return $this;
    }

    public function getUserAgent(): ?string
    {
        return $this->userAgent;
    }

    public function setUserAgent(?string $userAgent): static
    {
        $this->userAgent = $userAgent;

        return $this;
    }

    public function getUserToken(): ?UserToken
    {
        return $this->userToken;
    }

    public function setUserToken(?UserToken $userToken): static
    {
        $this->userToken = $userToken;

        return $this;
    }
}
