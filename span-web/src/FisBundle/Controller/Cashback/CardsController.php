<?php


namespace FisB<PERSON>le\Controller\Cashback;


use CoreBundle\Controller\ListControllerTrait;
use CoreBundle\Exception\FailedException;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\QueryListParams;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Util;
use Doctrine\ORM\QueryBuilder;
use FisBundle\Controller\BaseController;
use FisBundle\Entity\AccountBalanceBase;
use FisBundle\Entity\Monetary;
use FisBundle\Entity\MonetaryNew;
use FisBundle\Services\ProgramTypeService;
use FisBundle\Services\Reward\AccumulateService;
use FisBundle\Services\Reward\PrepaidService;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class CardsController extends BaseController
{
    use ListControllerTrait;

    /**
     * @Route("/admin/fis/cashback/cards/list/{page}/{limit}", methods={"GET"}, defaults={"page" = 1,"limit" = 10})
     * @param Request $request
     * @param int     $page
     * @param int     $limit
     *
     * @return SuccessResponse
     */
    public function search(Request $request, $page = 1, $limit = 10)
    {

        $resp = $this->traitSearch($request, ['id'], $page, $limit);
        $result = $resp->getResult();
        $this->fillStatisticValues($result['data'], $request);

        $result['quick'] = [];
        $q = $this->query($request);


        foreach ([
            'active'    => AccountBalanceBase::CASHBACK_STATUS_ACTIVE,
            'inactive'  => AccountBalanceBase::CASHBACK_STATUS_INACTIVE,
            'onHold'    => AccountBalanceBase::CASHBACK_STATUS_ON_HOLD,
            'cancelled' => AccountBalanceBase::CASHBACK_STATUS_CANCELLED,
        ] as $key => $status) {
            $result['quick'][$key] = (int)((clone $q)->andWhere('a.cashbackStatus = :status')
                ->setParameter('status', $status)
                ->select('count(distinct a)')
                ->getQuery()
                ->getSingleScalarResult());
        }

        // Restructure Data
        if ($result['data'] && gettype($result['data']) !== "array") {
            $result['data'] = array_values($result['data']);
        }

        // fix error the key is '' task ****************
        $result['data'] = array_filter($result['data'], function($item) {
          return isset($item['ID']);
        });
        return new SuccessResponse($result);
    }

    protected function query(Request $request)
    {
        $q = Util::em(false, 'analytics')->getRepository(AccountBalanceBase::class)
            ->createQueryBuilder('a')
            ->where(Util::expr()->isNotNull('a.optInAt'));
        ProgramTypeService::queryAccessibleClients($q);
        return $q;
    }

    protected function queryListParams(QueryBuilder $query, Request $request)
    {
        $params = new QueryListParams($query, $request, 'a', 'count(distinct a)');
        $params->distinct = true;
        return $params;
    }

    protected function fillStatisticValues(array &$items, Request $request)
    {
        $force = false; // Util::isLocal();
        $rs = null;
        $timeout = 14400;
        $cacheKey = $this->createCacheKey($request, 'FillStatisticsValues', 'FIS');
        if (!$rs && !$force) {
            $rs = Data::getArray($cacheKey);
        }
        $indexes = [];
        if (!$rs) {
            foreach ($items as $index => $item) {
                $pan = $item['PAN Proxy'];
                $indexes[$pan] = $index;
            }
            $pans = array_keys($indexes);
            $expr = Util::em(false, 'analytics')->getExpressionBuilder();
            $rs = Util::em(false, 'analytics')->getRepository(MonetaryNew::class)
                ->createQueryBuilder('a')
                ->where($expr->in('a.panProxyNumber', ':pans'))
                ->andWhere($expr->isNotNull('a.rewardStatus'))
                ->setParameter('pans', $pans)
                ->select('a.panProxyNumber, a.rewardAmount, a.rewardedAmount, a.rewardStatus')
                ->getQuery()
                ->getArrayResult();

            Data::setArray($cacheKey, $rs, $timeout);
        }

        foreach ($rs as $r) {
            $pan = $r['panProxyNumber'];
            $status = $r['rewardStatus'];
            $amount = $r['rewardAmount'] ?: 0;
            $rewarded = $r['rewardedAmount'] ?: 0;
            $index = $indexes[$pan];
            if (in_array($status, [
                MonetaryNew::REWARD_STATUS_PENDING,
                MonetaryNew::REWARD_STATUS_REVERSED,
            ])) {
                $items[$index]['Pending Transaction Count']++;
                $items[$index]['Pending Dollars Total'] += $amount;
            } else if ($status === MonetaryNew::REWARD_STATUS_ISSUED) {
                $items[$index]['Rewarded Transaction Count']++;
                $items[$index]['Rewarded Dollars Total'] += $amount;
            } else if ($status === MonetaryNew::REWARD_STATUS_PARTIALLY_ISSUED) {
                $items[$index]['Rewarded Transaction Count']++;
                $items[$index]['Pending Dollars Total'] += $amount - $rewarded;
                $items[$index]['Rewarded Dollars Total'] += $rewarded;
            } else {
                $items[$index]['Cancelled Transaction Count']++;
                $items[$index]['Cancelled Dollars Total'] += $amount;
            }
        }
        foreach ($items as $index => $item) {
            foreach (['Pending', 'Rewarded', 'Cancelled'] as $status) {
                $items[$index]['Avg ' . $status . ' $'] = Util::percent($item[$status . ' Dollars Total'], $item[$status . ' Transaction Count']);
                $items[$index][$status . ' Dollars Total'] = Money::format($items[$index][$status . ' Dollars Total'], 'USD');
                $items[$index]['Avg ' . $status . ' $'] = Money::format($items[$index]['Avg ' . $status . ' $'], 'USD');
            }
        }
    }

    /**
     * @param AccountBalanceBase $entity
     *
     * @return array
     */
    protected function getRowData($entity)
    {
        return [
            'ID'                          => $entity->getId(),
            'PAN Proxy'                   => $entity->getPanProxyNumber(),
            'Opt-In Date'                 => Util::formatDateTime($entity->getOptInAt()),
            'Cashback Program ID'         => $entity->getIssuerClientId(),
            'Cashback Program Name'       => $entity->getClientName(),
            'Pending Transaction Count'   => 0,
            'Pending Dollars Total'       => 0,
            'Avg Pending $'               => 0,
            'Rewarded Transaction Count'  => 0,
            'Rewarded Dollars Total'      => 0,
            'Avg Rewarded $'              => 0,
            'Cancelled Transaction Count' => 0,
            'Cancelled Dollars Total'     => 0,
            'Avg Cancelled $'             => 0,
            'Reward Purse Balance'        => Money::format($entity->getPurseBalance(), 'USD', false),
            'Cashback Status'             => $entity->getCashbackStatus(),
            'loading'                     => FALSE,
        ];
    }

    /**
     * @Route("/admin/fis/cashback/cards/filters")
     * @return SuccessResponse
     */
    public function filtersAction()
    {
        return new SuccessResponse([]);
    }

    /**
     * @Route("/admin/fis/cashback/cards/export", methods={"POST"})
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function export(Request $request)
    {
        return $this->commonExport($request, 'FIS Cashback Cards', [
            'PAN Proxy'                   => 18,
            'Opt-In Date'                 => 18,
            'Cashback Program ID'         => 12,
            'Cashback Program Name'       => 25,
            'Pending Transaction Count'   => 22,
            'Pending Dollars Total'       => 20,
            'Avg Pending $'               => 18,
            'Rewarded Transaction Count'  => 22,
            'Rewarded Dollars Total'      => 20,
            'Avg Rewarded $'              => 18,
            'Cancelled Transaction Count' => 22,
            'Cancelled Dollars Total'     => 20,
            'Avg Cancelled $'             => 18,
            'Cashback Status'             => 20,
        ], [], function (&$items, $request) {
            $this->fillStatisticValues($items, $request);
        });
    }

    protected function validatePan(Request $request)
    {
        $pan = $request->get('Card Proxy Number')
            ?? $request->get('PAN Proxy')
            ?? $request->get('PAN Proxy Number');
        if (!$pan) {
            throw new FailedException('Invalid PAN Proxy Number');
        }

        $abb = AccountBalanceBase::findByPanProxyNumber($pan);
        if (!$abb) {
            if (Util::isLive()) {
                throw new FailedException('Unknown PAN Proxy Number: ' . $pan);
            }

            $ms = Util::em(false, 'analytics')->getRepository(MonetaryNew::class)
                ->findBy([
                    'panProxyNumber' => $pan,
                ], null, 1);
            if ($ms) {
                /** @var Monetary $m */
                $m = $ms[0];
                $abb = new AccountBalanceBase();
                $abb->setPanProxyNumber($pan)
                    ->setFileProcessorName($m->getFileProcessorName())
                    ->setIssuerClientId($m->getIssuerClientId())
                    ->setClientName($m->getClientName())
                    ->setBin($m->getBin())
                    ->setBankName($m->getBankName())
                    ->setPan($m->getPan())
                    ->setBinCurrencyAlpha($m->getBinCurrencyAlpha())
                    ->setSubProgramId($m->getSubProgramId())
                    ->setSubProgramName($m->getSubProgramName())
                    ->setClosingBalance(0)
                    ->setUpdateDate($m->getWorkOfDate());
                Util::em(false, 'analytics')->persist($abb);
                Util::em(false, 'analytics')->flush();

                (new AccumulateService())->autoOptInCardForProgram($abb);
            } else {
                throw new FailedException('Unknown PAN Proxy Number: ' . $pan);
            }
        }
        return $abb;
    }

    /**
     * @Route("/admin/fis/cashback/cards/opt-in", methods={"POST"})
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function optIn(Request $request)
    {
        $abb = $this->validatePan($request);

        $as = new AccumulateService();
        $error = $as->optInCard($abb);
        if ($error) {
            return new FailedResponse($error);
        }

        return new SuccessResponse();
    }

    /**
     * @Route("/admin/fis/cashback/cards/change-status/{action}", methods={"POST"})
     * @param Request $request
     * @param string  $action opt-out/hold/deactivate/resume/activate
     *
     * @return FailedResponse|SuccessResponse
     */
    public function changeStatus(Request $request, $action)
    {
        $abb = $this->validatePan($request);
        $map = [
            'opt-out' => AccountBalanceBase::CASHBACK_STATUS_CANCELLED,
            'opt-in' => AccountBalanceBase::CASHBACK_STATUS_ACTIVE,
            'hold' => AccountBalanceBase::CASHBACK_STATUS_ON_HOLD,
            'deactivate' => AccountBalanceBase::CASHBACK_STATUS_INACTIVE,
            'resume' => AccountBalanceBase::CASHBACK_STATUS_ACTIVE,
            'activate' => AccountBalanceBase::CASHBACK_STATUS_ACTIVE,
        ];
        if (!isset($map[$action])) {
            return new FailedResponse('Unknown action: ' . $action);
        }
        $status = $map[$action];
        $abb->setCashbackStatus($status);
        Util::persist($abb);

        // Update transactions' status
        $txnStatus = null;
        $txnOldStatuses = [];
        if (in_array($status, [
            AccountBalanceBase::CASHBACK_STATUS_INACTIVE,
            AccountBalanceBase::CASHBACK_STATUS_CANCELLED,
        ])) {
            $txnOldStatuses = [
                MonetaryNew::REWARD_STATUS_PENDING,
                MonetaryNew::REWARD_STATUS_PARTIALLY_ISSUED,
                MonetaryNew::REWARD_STATUS_REVERSED,
            ];
            $txnStatus = MonetaryNew::REWARD_STATUS_PAUSED;
        } else if (in_array($status, [
            AccountBalanceBase::CASHBACK_STATUS_ACTIVE,
            AccountBalanceBase::CASHBACK_STATUS_ON_HOLD,
        ])) {
            $txnOldStatuses = [
                MonetaryNew::REWARD_STATUS_PAUSED,
            ];
            $txnStatus = MonetaryNew::REWARD_STATUS_PENDING;
        }

        $as = new AccumulateService();
        $as->updateTransactionStatusAndAccumulateForCard($abb, $txnStatus, $txnOldStatuses);

        return new SuccessResponse();
    }

    /**
     * @Route("/admin/fis/cashback/cards/refresh-balance/{abb}", methods={"POST"})
     * @param Request            $request
     * @param AccountBalanceBase $abb
     *
     * @return FailedResponse|SuccessResponse
     */
    public function refreshBalance(Request $request, AccountBalanceBase $abb)
    {
        $balance = PrepaidService::getBalance($abb);
        $abb->setPurseBalance($balance);
        Util::persist($abb);

        return new SuccessResponse(Money::format($balance, 'USD', false));
    }

    /**
     * @Route("/admin/fis/cashback/cards/login-micro-site/{abb}")
     * @param Request            $request
     * @param AccountBalanceBase $abb
     *
     * @return \Symfony\Component\HttpFoundation\RedirectResponse
     */
    public function loginMicroSite(Request $request, AccountBalanceBase $abb)
    {
        return $this->redirect('/fis/cashback/sso/test/uGylMbll0M8uX1R?ppn=' . $abb->getPanProxyNumber());
    }
}
