<?php


namespace FisB<PERSON>le\Controller\Cashback;


use CoreBundle\Controller\ListControllerTrait;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\QueryListParams;
use CoreBundle\Utils\Util;
use Doctrine\ORM\QueryBuilder;
use FisBundle\Controller\BaseController;
use FisBundle\Entity\AccountBalanceBase;
use FisBundle\Entity\CashbackProgram;
use FisBundle\Entity\MonetaryNew;
use FisBundle\Entity\ProgramType;
use FisBundle\Services\ProgramTypeService;
use FisBundle\Services\Reward\AccumulateService;
use FisBundle\Services\Reward\PrepaidAPI;
use FisBundle\Services\Reward\PrepaidService;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class TransactionsController extends BaseController
{
    use ListControllerTrait;

    /**
     * @var CashbackProgram
     */
    protected $cashbackProgram;

    /**
     * @Route("/admin/fis/cashback/transactions/list/{page}/{limit}", methods={"GET"}, defaults={"page" = 1,"limit" = 10})
     * @param Request $request
     * @param int     $page
     * @param int     $limit
     *
     * @return SuccessResponse
     */
    public function search(Request $request, $page = 1, $limit = 10)
    {
        $resp = $this->traitSearch($request, ['id'], $page, $limit);
        $result = $resp->getResult();
        $result['quick'] = [];

        $q = $this->query($request);
        $expr = Util::expr();
        $rs = (clone $q)->andWhere($expr->in('a.rewardStatus', ':statuses'))
            ->setParameter('statuses', [
                MonetaryNew::REWARD_STATUS_PENDING,
                MonetaryNew::REWARD_STATUS_REVERSED,
            ])
            ->select('sum(a.rewardAmount) amt, count(a) cnt')
            ->getQuery()
            ->getArrayResult();
        $result['quick']['pendingAmount'] = (int)($rs[0]['amt'] ?? 0);
        $result['quick']['pendingCount'] = (int)($rs[0]['cnt']);

        $rs = (clone $q)->andWhere('a.rewardStatus = :status')
            ->setParameter('status', MonetaryNew::REWARD_STATUS_ISSUED)
            ->select('sum(a.rewardAmount) amt, count(a) cnt')
            ->getQuery()
            ->getArrayResult();
        $result['quick']['issuedAmount'] = (int)($rs[0]['amt'] ?? 0);
        $result['quick']['issuedCount'] = (int)($rs[0]['cnt']);

        $rs = (clone $q)->andWhere('a.rewardStatus = :status')
            ->setParameter('status', MonetaryNew::REWARD_STATUS_PARTIALLY_ISSUED)
            ->select('sum(a.rewardAmount) amt, sum(a.rewardedAmount) rewarded, count(a) cnt')
            ->getQuery()
            ->getArrayResult();
        $result['quick']['pendingAmount'] += (int)($rs[0]['amt'] ?? 0) - (int)($rs[0]['rewarded'] ?? 0);
        $result['quick']['issuedAmount'] += (int)($rs[0]['rewarded'] ?? 0);
        $result['quick']['issuedCount'] += (int)($rs[0]['cnt']);

        return new SuccessResponse($result);
    }

    protected function query(Request $request)
    {
        $this->cashbackProgram = CashbackProgram::current();

        $subQuery = $this->em->getRepository(AccountBalanceBase::class)
            ->createQueryBuilder('b')
            ->where(Util::expr()->isNotNull('b.optInAt'))
            ->select('b.panProxyNumber');

        $expr = Util::expr();
        $q = $this->em->getRepository(MonetaryNew::class)
            ->createQueryBuilder('a')
            ->where($expr->eq('a.panProxyNumber', $expr->any($subQuery)))
            ->andWhere($expr->isNotNull('a.rewardStatus'));
        ProgramTypeService::queryAccessibleClients($q);
        return $q;
    }

    protected function queryListParams(QueryBuilder $query, Request $request)
    {
        $params = new QueryListParams($query, $request, 'a', 'count(distinct a)');
        $params->distinct = true;
        $params->orderBy = [
            'a.workOfDate' => 'desc',
        ];
        $params->searchFields = [
            'a.panProxyNumber',
            'a.merchantName',
            'a.merchantNumber',
            'a.mccDescription',
            'a.txnTypeName',
        ];
        return $params;
    }

    /**
     * @param MonetaryNew $entity
     *
     * @return array
     */
    protected function getRowData($entity)
    {
        $program = $this->cashbackProgram;
        $percent = $program->getCashbackPercentage();
        $amount = $entity->getRewardAmount() ?? 0;
        return [
            'ID'                        => $entity->getId(),
            'Date'                      => $entity->getWorkOfDate()->format(Util::DATE_FORMAT_ISO_DATE_TIME),
            'Issuing Bank'              => $entity->getBankName(),
            'Issuing Client'            => $entity->getIssuerClientId(),
            'Date Time'                 => $entity->getWorkOfDate()->format(Util::DATE_TIME_FORMAT),
            'Tran ID'                   => $entity->getTxnUid(),
            'Tran Type'                 => $entity->getTxnTypeName(),
            'Reason Code'               => $entity->getReasonCodeDescription(),
            'Derived Response'          => $entity->getDerivedRequestCodeDescription(),
            'Response'                  => $entity->getResponseDescription(),
            'Program'                   => $entity->getClientName(),
            'Type'                      => ProgramType::getNameForBin($entity->getBin()),
            'BIN'                       => $entity->getBin(),
            'BIN Currency'              => $entity->getBinCurrencyAlpha(),
            'PAN'                       => $entity->getPan(),
            'Card Proxy Number'         => $entity->getCardNumberProxy(),
            'Debit/Credit'              => $entity->getTxnSignName(),
            'Auth Amount'               => $entity->getAuthorizationAmount(),
            'Auth Code'                 => $entity->getAuthorizationCode(),
            'Txn Local Amount'          => $entity->getTxnLocalAmount(),
            'Transaction Currency'      => $entity->getTransactionCurrencyAlpha(),
            'Settle Amount'             => $entity->getSettleAmount(),
            'MCC'                       => $entity->getMcc(),
            'MCC Description'           => $entity->getMccDescription(),
            'Merchant'                  => $entity->getMerchantName(),
            'Merchant Number'           => $entity->getMerchantNumber(),
            'Merchant City'             => $entity->getMerchantCity(),
            'Merchant Country'          => $entity->getMerchantCountryCode(),
            'Request Code Description'  => $entity->getActualRequestCodeDescription(),
            'Acquirer Reference Number' => $entity->getAcquirerReferenceNumber(),
            'Acquirer Id'               => $entity->getAcquirerId(),
            'Reward $'                  => Money::format($amount, 'USD', false),
            'Reward %'                  => Util::formatPercent($percent),
            'Rewarded $'                => Money::formatWhen($entity->getRewardedAmount()),
            'Cashback Program Name'     => $program->getName(),
            'Cashback Program ID'       => $program->getId(),
            'Reward Status'             => ucfirst($entity->getRewardStatus() ?? 'pending'),
        ];
    }

    /**
     * @Route("/admin/fis/cashback/transactions/filters")
     * @return SuccessResponse
     */
    public function filtersAction() {
        return new SuccessResponse([]);
    }

    /**
     * @Route("/admin/fis/cashback/transactions/{monetary}/action/{action}")
     * @param MonetaryNew $monetary
     * @param             $action
     *
     * @return FailedResponse|SuccessResponse
     */
    public function commonAction(MonetaryNew $monetary, $action) {
        if (in_array($action, [
            'reward', 'pending',
        ])) {
            $as = new AccumulateService();
            $as->accumulateForMonetary($monetary);
        }

        $status = $monetary->getRewardStatus();
        if ($action === 'reward') {
            if (!in_array($status, [
                MonetaryNew::REWARD_STATUS_PENDING,
                MonetaryNew::REWARD_STATUS_PARTIALLY_ISSUED,
                MonetaryNew::REWARD_STATUS_REVERSED,
                MonetaryNew::REWARD_STATUS_CANCELLED,
            ])) {
                return new FailedResponse('Invalid status!');
            }
            PrepaidService::reward($monetary);
        } else if ($action === 'cancel') {
            if (!in_array($status, [
                MonetaryNew::REWARD_STATUS_PENDING,
                MonetaryNew::REWARD_STATUS_PAUSED,
            ])) {
                return new FailedResponse('Invalid status!');
            }
            $monetary->setRewardStatus(MonetaryNew::REWARD_STATUS_CANCELLED);
        } else if ($action === 'pending') {
            if (!in_array($status, [
                MonetaryNew::REWARD_STATUS_CANCELLED,
            ])) {
                return new FailedResponse('Invalid status!');
            }
            $monetary->setRewardStatus(MonetaryNew::REWARD_STATUS_PENDING);
        } else if ($action === 'reverse') {
            if (!in_array($status, [
                MonetaryNew::REWARD_STATUS_ISSUED,
                MonetaryNew::REWARD_STATUS_PARTIALLY_ISSUED,
            ])) {
                return new FailedResponse('Invalid status!');
            }
            PrepaidService::reverse($monetary);
        }
        Util::persist($monetary);
        return new SuccessResponse();
    }

    /**
     * @Route("/admin/fis/cashback/transactions/export", methods={"POST"})
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function export(Request $request)
    {
        return $this->commonExport($request, 'FIS Cashback Transactions', [
            'ID'                        => 12,
            'Date'                      => 18,
            'Issuing Bank'              => 20,
            'Issuing Client'            => 22,
            'Date Time'                 => 18,
            'Tran ID'                   => 22,
            'Tran Type'                 => 24,
            'Reason Code'               => 20,
            'Derived Response'          => 20,
            'Response'                  => 20,
            'Type'                      => 20,
            'BIN'                       => 20,
            'BIN Currency'              => 20,
            'PAN'                       => 20,
            'Card Proxy Number'         => 20,
            'Debit/Credit'              => 20,
            'Auth Amount'               => 20,
            'Auth Code'                 => 20,
            'Txn Local Amount'          => 20,
            'Settle Amount'             => 20,
            'MCC'                       => 20,
            'MCC Description'           => 20,
            'Merchant'                  => 20,
            'Merchant City'             => 20,
            'Merchant Country'          => 20,
            'Request Code Description'  => 20,
            'Acquirer Reference Number' => 24,
            'Acquirer Id'               => 20,
            'Reward $'                  => 18,
            'Reward %'                  => 18,
            'Cashback Program Name'     => 22,
            'Cashback Program ID'       => 18,
            'Reward Status'             => 22,
        ]);
    }
}
