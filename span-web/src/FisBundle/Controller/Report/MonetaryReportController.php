<?php


namespace FisBundle\Controller\Report;


use CoreBundle\Controller\ListControllerTrait;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\QueryListParams;
use CoreBundle\Utils\Util;

use Doctrine\ORM\QueryBuilder;

use FisBundle\Controller\BaseController;
use FisBundle\Controller\Traits\PortfolioControllerTrait;
use FisBundle\Entity\MonetaryNew;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class MonetaryReportController extends BaseController
{
    use ListControllerTrait;
    use PortfolioControllerTrait;

    /**
     * @Route("/admin/fis/report/monetary/list/{page}/{limit}", methods={"GET"}, defaults={"page" = 1,"limit" = 10})
     * @param Request $request
     * @param int     $page
     * @param int     $limit
     *
     * @return SuccessResponse
     */
    public function search(Request $request, $page = 1, $limit = 10)
    {
        Util::longerRequest();
        $cache = $this->traitSearchCached($request, ['id'], $page, $limit);
        return new SuccessResponse($cache);
    }

    protected function getRepository($ab = FALSE)
    {
        return Util::em(false, 'analytics')->getRepository(MonetaryNew::class);
    }

    protected function query(Request $request, $export = false)
    {
        $q = $this->commonQuery($request, $export, true, MonetaryNew::class);

        $achType = $request->get('ach_type');
        $expr = Util::expr();
        if ($achType && in_array($achType, ['in', 'out'])) {
            $q->andWhere($expr->orX(
                $expr->in('a.actualRequestCode', ':requestCodesFixed'),
                $expr->andX(
                    $expr->in('a.actualRequestCode', ':requestCodesRound'),
                    $expr->eq('a.txnSign', ':txnSign')
                )
            ));
            if ($achType === 'in') {
                $q->setParameter('requestCodesFixed', ['1921'])
                    ->setParameter('requestCodesRound', ['1922', '1923'])
                    ->setParameter('txnSign', MonetaryNew::TXN_SIGN_CREDIT);
            } else if ($achType === 'out') {
                $q->setParameter('requestCodesFixed', ['1401', '1920'])
                    ->setParameter('requestCodesRound', ['1922', '1923'])
                    ->setParameter('txnSign', MonetaryNew::TXN_SIGN_DEBIT);
            }
        }

        $this->queryByDateRange($q, 'a.workOfDate');

        return $q;
    }

    protected function queryListParams(QueryBuilder $query, Request $request)
    {
        $params = new QueryListParams($query, $request, 'a', 'count(distinct a)');
        $params->distinct = true;
        $params->orderBy = [
            'a.workOfDate' => 'desc',
        ];
        return $params;
    }

    /**
     * @param MonetaryNew $entity
     * @param string $format
     * @param array  $otherColumns
     *
     * @return array
     */
    protected function getRowData($entity, $format = 'Detail', $otherColumns = [])
    {
        $all = [
            'Date' => $entity->getWorkOfDate()->format(Util::DATE_FORMAT_ISO_DATE_TIME),
            'Issuing Bank' => $entity->getBankName(),
            'Issuing Client' => $entity->getIssuerClientId(),
        ];

        if ($format === 'Detail') {
            /** @var MonetaryNew $entity */
            $all = array_merge($all, [
                'Date Time' => $entity->getWorkOfDate()->format(Util::DATE_TIME_FORMAT),
                'Tran ID' => $entity->getTxnUid(),
                'Tran Type' => $entity->getTxnTypeName(),
                'Reason Code' => $entity->getReasonCodeDescription(),
                'Derived Response' => $entity->getDerivedRequestCodeDescription(),
                'Response' => $entity->getResponseDescription(),
                'Issuing Bank' => $entity->getBankName(),
                'Issuing Client' => $entity->getClientName(),
                'BIN' => $entity->getBin(),
                'BIN Currency' => $entity->getBinCurrencyAlpha(),
                'PAN' => $entity->getPan(),
                'Card Proxy Number' => $entity->getCardNumberProxy(),
                'Debit/Credit' => $entity->getTxnSignName(),
                'Auth Amount' => $entity->getAuthorizationAmount(),
                'Auth Code' => $entity->getAuthorizationCode(),
                'Txn Local Amount' => $entity->getTxnLocalAmount(),
                'Transaction Currency' => $entity->getTransactionCurrencyAlpha(),
                'Settle Amount' => $entity->getSettleAmount(),
                'MCC' => $entity->getMcc(),
                'MCC Description' => $entity->getMccDescription(),
                'Merchant' => $entity->getMerchantName(),
                'Merchant Number' => $entity->getMerchantNumber(),
                'Merchant City' => $entity->getMerchantCity(),
                'Merchant Country' => $entity->getMerchantCountryCode(),
                'Request Code Description' => $entity->getActualRequestCodeDescription(),
                'Request Code' => $entity->getActualRequestCode(),
                'Acquirer Reference Number' => $entity->getAcquirerReferenceNumber(),
                'Acquirer Id' => $entity->getAcquirerId(),
            ]);

            foreach ($otherColumns as $title => $express) {
                if (Util::startsWith($express, '$')) {
                    $express = Util::method($express);
                    $all[$title] = $entity->$express();
                    continue;
                }
            }
        }
        return $all;
    }

    /**
     * @Route("/admin/fis/report/monetary/filters", methods={"GET"})
     * @return SuccessResponse
     */
    public function filters()
    {
        $data = $this->queryTopFilters(false);
        /*
        $repo = $this->getRepository();
        $data = array_merge($data, Util::allToFilterOptions([
            'transactionCurrencies' => Util::queryArray($repo->createQueryBuilder('a')
                ->select('a.transactionCurrencyAlpha name')),
            'tranTypes' => Util::queryArray($repo->createQueryBuilder('a')
                ->select('a.txnTypeName name')),
            'reasonCodes' => Util::queryArray($repo->createQueryBuilder('a')
                ->select('a.reasonCodeDescription name')),
            'derivedResponses' => Util::queryArray($repo->createQueryBuilder('a')
                ->select('a.derivedRequestCodeDescription name')),
            'responses' => Util::queryArray($repo->createQueryBuilder('a')
                ->select('a.responseDescription name')),
            'mccDescriptions' => Util::queryArray($repo->createQueryBuilder('a')
                ->select('a.mccDescription name')),
            'merchantCountries' => Util::queryArray($repo->createQueryBuilder('a')
                ->select('a.merchantCountryName name')),
        ]));
        */
        return new SuccessResponse($data);
    }

    /**
     * @param $format
     *
     * @return array
     */
    protected function getOtherColumnsForReport($format)
    {
        if ($format !== 'Detail') {
            return [];
        }
        return [
            'Sub Program ID' => '$subProgramId',
            'Sub Program Name' => '$subProgramName',
            'BIN Currency Code' => '$binCurrencyCode',
            'Card Number' => '$cardNumber',
            'Transaction Loc Date Time' => '$txnLocDateTime',
            'Transaction Currency Code' => '$transactionCurrencyCode',
            'Transaction Currency Alpha' => '$transactionCurrencyAlpha',
            'Transaction Type Code' => '$txnTypeCode',
            'Derived Request Code' => '$derivedRequestCode',
            'Response Code' => '$responseCode',
            'Match Status Code' => '$matchStatusCode',
            'Match Type Code' => '$matchTypeCode',
            'Initial Load Date Flag' => '$initialLoadDateFlag',
            'Merchant Currency Alpha' => '$merchantCurrencyAlpha',
            'Merchant Currency Code' => '$merchantCurrencyCode',
            'Merchant Number' => '$merchantNumber',
            'Reference Number' => '$referenceNumber',
            'Payment Method ID' => '$paymentMethodId',
            'WCS UTC Post Date' => '$wcsUtcPostDate',
            'Source Code' => '$sourceCode',
            'Acquirer Reference Number' => '$acquirerReferenceNumber',
            'Acquirer ID' => '$acquirerId',
            'Address Verification Response' => '$addressVerificationResponse',
            'Adjust Amount' => '$adjustAmount',
            'Authorization Response' => '$authorizationResponse',
            'AVS Information' => '$avsInformation',
            'Denomination' => '$denomination',
            'Direct Access Number' => '$directAccessNumber',
            'Fudge Amt' => '$fudgeAmt',
            'Match Status Description' => '$matchStatusDescription',
            'Match Type Description' => '$matchTypeDescription',
            'Merchant Zip' => '$merchantZip',
            'Merchant Country Name' => '$merchantCountryName',
            'Merchant Province' => '$merchantProvince',
            'Merchant State' => '$merchantState',
            'Merchant Street' => '$merchantStreet',
            'PIN' => '$pin',
            'POS Data' => '$posData',
            'POS Entry Code' => '$posEntryCode',
            'POS Entry Description' => '$posEntryDescription',
            'Purse No' => '$purseNo',
            'Reason Code Description' => '$reasonCodeDescription',
            'Derived Request Code Description' => '$derivedRequestCodeDescription',
            'Retrieval Ref No' => '$retrievalRefNo',
            'Reversed' => '$reversed',
            'Source Description' => '$sourceDescription',
            'Terminal Number' => '$terminalNumber',
            'User ID' => '$userId',
            'User First Name' => '$userFirstName',
            'User Last Name' => '$userLastName',
            'WCS Local Post Date' => '$wcsLocalPostDate',
            'Comment' => '$comment',
            'Client Reference Number' => '$clientReferenceNumber',
            'Client Specific ID' => '$clientSpecificId',
            'Actual Request Code' => '$actualRequestCode',
            'Actual Request Code Description' => '$actualRequestCodeDescription',
            'Card Holder Client Unique ID' => '$cardholderClientUniqueId',
            'PAN Proxy Number' => '$panProxyNumber',
            'Purse Name' => '$purseName',
            'Purse Status' => '$purseStatus',
            'Purse Creation Date' => '$purseCreationDate',
            'Purse Effective Date' => '$purseEffectiveDate',
            'Purse Expiration Date' => '$purseExpirationDate',
            'Purse Status Date' => '$purseStatusDate',
            'Association Source' => '$associationSource',
            'Reason Id' => '$reasonId',
            'Reason Description' => '$reasonDescription',
            'Variance' => '$variance',
            'Process Code' => '$processCode',
            'Token Unique Reference ID' => '$tokenUniqueReferenceId',
            'PAN Unique Reference ID' => '$panUniqueReferenceId',
            'Token Transaction ID' => '$tokenTransactionId',
            'Token Status' => '$tokenStatus',
            'Token Status Description' => '$tokenStatusDescription',
            'Network Reference ID' => '$networkReferenceId',
            'Multi-clearing Indication' => '$multiClearingIndication',
            'Authorization Balance' => '$authorizationBalance',
            'Settle Balance' => '$settleBalance',
            'WCS Local Inserted' => '$wcsLocalInserted',
            'WCS UTC Inserted' => '$wcsUtcInserted',
            'WCS UTC Updated' => '$wcsUtcUpdated',
            'Discount Amount' => '$discountAmount',
        ];
    }

    /**
     * @Route("/admin/fis/report/monetary/export", methods={"POST"})
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function export(Request $request)
    {
        $request->request->set('format', 'Detail');

        $ret = $this->commonFisExport($request, false);
        if ($ret instanceof SuccessResponse) {
            return $ret;
        }
        list($data, $from, $limit, $format) = $ret;

        $headers = [
            'Date Time' => 20,
            'Tran ID' => 20,
            'Tran Type' => 20,
            'Reason Code' => 20,
            'Derived Response' => 20,
            'Response' => 20,
            'Issuing Bank' => 20,
            'Issuing Client' => 20,
            'Type' => 20,
            'BIN' => 20,
            'BIN Currency' => 20,
            'PAN' => 20,
            'Card Proxy Number' => 20,
            'Debit/Credit' => 20,
            'Auth Amount' => 20,
            'Auth Code' => 20,
            'Txn Local Amount' => 20,
            'Settle Amount' => 20,
            'MCC' => 20,
            'MCC Description' => 20,
            'Merchant' => 20,
            'Merchant City' => 20,
            'Merchant Country' => 20,
            'Request Code' => 20,
            'Request Code Description' => 20,
        ];

        $others = $this->getOtherColumnsForReport($format);
        foreach ($others as $title => $field) {
            $headers[$title] = 20;
        }

        $filename = 'Monetary Activity ' . $this->getReportFileSuffix($request);
        $destination = Util::uploadDir('report') . $filename . '.csv';
        $excel = $this->loadOrCreateExcel($destination, $filename);

        $excel = $this->generateSheetAppendToExcel($excel, $from . ' ~ ' . ($from + $limit), $headers, $data,
            function ($col, $row, $excel, $title) use ($format) {
                $cache = $format === 'Detail' ? $this->rowCache : $row;
                return $cache[$title] ?? '';
            });
        $this->saveCsvTo($excel, $destination, count($headers));

        return new SuccessResponse('report/' . $filename . '.csv');
    }
}
