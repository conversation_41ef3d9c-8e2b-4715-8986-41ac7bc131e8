<?php


namespace FisBundle\Controller\Report;


use Carbon\Carbon;
use CoreBundle\Controller\ListControllerTrait;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\QueryListParams;
use CoreBundle\Utils\Util;
use Doctrine\ORM\QueryBuilder;
use FisBundle\Controller\BaseController;
use FisBundle\Controller\Traits\PortfolioControllerTrait;
use FisBundle\Entity\StatisticsBalance;
use FisBundle\Entity\ProgramType;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use CoreBundle\Utils\Log;

class PortfolioReportController extends BaseController
{
    use ListControllerTrait;
    use PortfolioControllerTrait;

    /**
     * @Route("/admin/fis/report/portfolio/list/{page}/{limit}", methods={"GET"}, defaults={"page" = 1,"limit" = 10})
     * @param Request $request
     * @param int     $page
     * @param int     $limit
     *
     * @return SuccessResponse
     */
    public function search(Request $request, $page = 1, $limit = 10)
    {
        Util::longerRequest();

        $format = $request->get('format');
        if ($format === 'Summary') {
            $this->page = $page;
            $this->limit = $limit;

            list($count, $start, $end, $banks, $clients) = $this->queryCountForSummary($request);
            // Log::debug('debug date');
            // Log::debug($start);
            // Log::debug($end);
            $data = $this->queryDataForSummary($request, $start, $end, $banks, $clients);

            return new SuccessResponse(compact('count', 'data'));
        }
        $cache = $this->traitSearchCached($request, ['id'], $page, $limit);
        return new SuccessResponse($cache);
    }

    public function getRowDataForSummary($date, $bank, $client, $group, $step, $unit)
    {
        return [
            'Date' => $date,
            'Issuing Bank' => $bank,
            'Issuing Client' => $client,
            'Currency' => $this->request->get('currency'),
        ];
    }

    protected function getSumFieldsForSummary($otherColumns = [])
    {
        $all = [];
        if ($otherColumns) {
            $keys = array_keys($otherColumns);
            $keys = array_filter($keys, function ($key) {
                return $key !== 'Currency' && !Util::startsWith($key, 'Avg ');
            });
            $all = array_merge($all, $keys);
        }
        return $all;
    }

    protected function getAggregationFieldsForSummary($otherColumns = [])
    {
        $all = [
            'Starting Balance' => ['sumOfDate'],
            'Ending Balance' => ['sumOfDate'],
            'Starting Cards #' => ['sumOfDate'],
            'Ending Cards #' => ['sumOfDate'],
            'Avg Starting Balance' => ['divide', 'Starting Balance', 'Starting Cards #'],
            'Avg Ending Balance' => ['divide', 'Ending Balance',  'Ending Cards #'],
            'Balance % Change' => ['delta', 'Ending Balance', 'Starting Balance'],
            '% Card Growth' => ['delta', 'Ending Cards #', 'Starting Cards #'],
        ];
        if ($otherColumns) {
            $reserved = array_flip($otherColumns);
            foreach ($otherColumns as $key => $agg) {
                if ($key === 'Currency' || !Util::startsWith($agg, '=')) {
                    continue;
                }
                $agg = substr($agg, 1);
                $parts = explode('/', $agg);
                $params = ['divide'];
                foreach ($parts as $part) {
                    $params[] = $reserved[$part];
                }
                $all[$key] = $params;
            }
        }
        return $all;
    }

    protected function query(Request $request, $export = false)
    {
        $query = $this->commonQuery($request, $export, false, StatisticsBalance::class, null, 'a');

        if ($request->get('format') === 'Summary') {
            $this->queryFilter($query, $request);
        }

        return $query;
    }

    protected function queryListParams(QueryBuilder $query, Request $request)
    {
        $params = new QueryListParams($query, $request, 'a', 'count(distinct a)');
        $params->distinct = true;
        $params->orderBy = [
            'a.workOfDate' => 'desc',
        ];
        return $params;
    }

    /**
     * @param StatisticsBalance $entity
     * @param string $format
     * @param array  $otherColumns
     *
     * @return array
     */
    protected function getRowData($entity, $format = 'Detail', $otherColumns = [])
    {
        $all = [
            'Date' => $entity->getWorkOfDate()->format(Util::DATE_FORMAT_ISO_DATE_TIME),
            'Issuing Bank' => $entity->getBankName(),
            'Issuing Client' => $entity->getIssuerClientId(),
            'Starting Balance' => $entity->getOpeningBalanceTotal(),
            'Ending Balance' => $entity->getClosingBalanceTotal(),
        ];

        if ($format === 'Detail') {
            $startingCards = Util::isZero($entity->getOpeningBalanceTotal()) ? 0 : 1;
            $endingCards = Util::isZero($entity->getClosingBalanceTotal()) ? 0 : 1;

            /** @var StatisticsBalance $entity */
            $all = array_merge($all, [
                'Starting Cards #' => $startingCards,
                'Ending Cards #' => $endingCards,
                'PAN Proxy Number' => $entity->getPanProxyNumberCount(),
                'BIN' => $entity->getBin(),
                'Group Type' => ProgramType::getNameForBin($entity->getBin()),
                'Avg Starting Balance' => $entity->getOpeningBalanceAverage(),
                'Avg Ending Balance' => $entity->getClosingBalanceAverage(),
                'Balance % Change' => Util::delta($entity->getClosingBalanceAverage(), $entity->getOpeningBalanceAverage()),
                '% Card Growth' => Util::delta($endingCards, $startingCards),
            ]);
        } else {
            $all = array_merge($all, [
                'Starting Cards #' => $entity->getOpeningCardCount(),
                'Ending Cards #' => $entity->getClosingCardCount(),
            ]);
        }

        foreach ($otherColumns as $title => $express) {
            if (Util::startsWith($express, '$')) {
                $express = Util::method($express);
                $all[$title] = $entity->$express();
                continue;
            }

            if ($format === 'Detail') {
                if (Util::startsWith($express, '=')) {
                    $express = str_replace('=', '', $express);
                    $methods = explode('/', $express);
                    $method1 = Util::method($methods[0]);
                    $method2 = Util::method($methods[1]);
                    $percent = Util::percent($entity->$method1(), $entity->$method2());
                    $all[$title] = Util::formatPercent($percent);
                } else {
                    $all[$title] = $express;
                }
            }
        }

        return $all;
    }

    /**
     * @Route("/admin/fis/report/portfolio/filters", methods={"GET"})
     * @return SuccessResponse
     */
    public function filters()
    {
        $data = $this->queryTopFilters();
        return new SuccessResponse($data);
    }

    protected function getOtherColumnsForReport($format)
    {
        $map = [
            'Issuing Client ID' => '$issuerClientId',
            'Currency' => '$binCurrencyAlpha',
            'Total Valueload Amount' => '$totalValueLoadAmount',
            'Total Valueload Count' => '$totalValueLoadCount',
            'Avg Valueload' => '=$totalValueLoadAmount/$totalValueLoadCount',
            'Total Purchase Amount' => '$totalPurchaseAmount',
            'Total Purchase Count' => '$totalPurchaseCount',
            'Avg Purchase' => '=$totalPurchaseAmount/$totalPurchaseCount',
            'Total OCT Amount' => '$totalOtcAmount',
            'Total OCT Count' => '$totalOtcCount',
            'Avg OCT' => '=$totalOtcAmount/$totalOtcCount',
            'Total ATM Withdrawal Amount' => '$totalAtmWithdrawalAmount',
            'Total ATM Withdrawal Count' => '$totalAtmWithdrawalCount',
            'Avg ATM Withdrawal' => '=$totalAtmWithdrawalAmount/$totalAtmWithdrawalCount',
            'Total Return Amount' => '$totalReturnAmount',
            'Total Return Count' => '$totalReturnCount',
            'Avg Return' => '=$totalReturnAmount/$totalReturnCount',
            'Total Adjustment Amount' => '$totalAdjustmentAmount',
            'Total Adjustment Count' => '$totalAdjustmentCount',
            'Avg Adjustment' => '=$totalAdjustmentAmount/$totalAdjustmentCount',
            'Total Fees' => '$totalFees',
            'Total Fees Count' => '$totalFeesCount',
            'Avg Fees' => '=$totalFees/$totalFeesCount',
            'Other Credit Amount' => '$otherCreditAmount',
            'Other Credit Count' => '$otherCreditCount',
            'Avg Credit' => '=$otherCreditAmount/$otherCreditCount',
            'Other Debit Amount' => '$otherDebitAmount',
            'Other Debit Count' => '$otherDebitCount',
            'Avg Debit' => '=$otherDebitAmount/$otherDebitCount',
            'Total Credit Amount' => '$totalCreditAmount',
            'Total Credit Count' => '$totalCreditCount',
            'Avg Total Credit' => '=$totalCreditAmount/$totalCreditCount',
            'Total Debit Amount' => '$totalDebitAmount',
            'Total Debit Count' => '$totalDebitCount',
            'Avg Total Debit' => '=$totalDebitAmount/$totalDebitCount',
            'Total Transaction Amount' => '$totalTransactionAmount',
            'Total Transaction Count' => '$totalTransactionCount',
            'Avg Transaction' => '=$totalTransactionAmount/$totalTransactionCount',
            'PAN Proxy Number' => '$panProxyNumber',
        ];
        if ($format === 'Summary') {
            foreach ([
                'Issuing Client ID', 'BIN', 'PAN Proxy Number',
            ] as $field) {
                unset($map[$field]);
            }
        }
        return $map;
    }

    /**
     * @Route("/admin/fis/report/portfolio/export", methods={"POST"})
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function export(Request $request)
    {
        $ret = $this->commonFisExport($request);
        if ($ret instanceof SuccessResponse) {
            return $ret;
        }
        list($data, $from, $limit, $format) = $ret;

        $columns = [
            'Date', 'Issuing Bank', 'Issuing Client ID', 'Issuing Client', 'BIN',
            'Currency', 'Starting Balance', 'Avg Starting Balance', 'Ending Balance',
            'Avg Ending Balance', 'Balance % Change', 'Starting Cards #', 'Ending Cards #',
            '% Card Growth', 'Total Valueload Amount', 'Total Valueload Count', 'Avg Valueload',
            'Total Purchase Amount', 'Total Purchase Count', 'Avg Purchase',
            'Total OCT Amount', 'Total OCT Count', 'Avg OCT',
            'Total ATM Withdrawal Amount', 'Total ATM Withdrawal Count', 'Avg ATM Withdrawal',
            'Total Return Amount', 'Total Return Count', 'Avg Return',
            'Total Adjustment Amount', 'Total Adjustment Count', 'Avg Adjustment',
            'Total Fees', 'Total Fees Count', 'Avg Fees',
            'Other Credit Amount', 'Other Credit Count', 'Avg Credit',
            'Other Debit Amount', 'Other Debit Count', 'Avg Debit',
            'Total Credit Amount', 'Total Credit Count', 'Avg Total Credit',
            'Total Debit Amount', 'Total Debit Count', 'Avg Total Debit',
            'Total Transaction Amount', 'Total Transaction Count', 'Avg Transaction',
            'PAN Proxy Number',
        ];
        if ($format === 'Summary') {
            $columns = array_diff($columns, [
                'Issuing Client ID', 'BIN', 'PAN Proxy Number',
            ]);
        }
        $headers = [];
        foreach ($columns as $column) {
            $headers[$column] = 10;
        }

        $middle = '(' . implode(' ', [
            $request->get('range'),
            $request->get('format'),
        ]) . ')';
        $filename = 'Portfolio Report ' . $middle . ' ' . $this->getReportFileSuffix($request);
        $destination = Util::uploadDir('report') . $filename . '.csv';
        $excel = $this->loadOrCreateExcel($destination, $filename);

        $excel = $this->generateSheetAppendToExcel($excel, $from . ' ~ ' . ($from + $limit), $headers, $data,
            function ($col, $row, $excel, $title) use ($format) {
                $cache = $format === 'Detail' ? $this->rowCache : $row;
                if ($title === 'Date') {
                    return Carbon::parse($cache[$title])->format(Util::DATE_FORMAT_LONG);
                }
                if (in_array($title, ['Balance % Change', '% Card Growth'])) {
                    return Util::formatPercent($cache[$title] ?? 0);
                }
                if (isset($cache[$title])) {
                    if ($title === 'Total Fees'
                        || Util::endsWith($title, ' Amount')
                        || Util::startsWith($title, 'Avg '))
                    {
                        return round($cache[$title], 2);
                    }
                    return $cache[$title];
                }
                return '';
            });
        $this->saveCsvTo($excel, $destination, count($columns));

        return new SuccessResponse('report/' . $filename . '.csv');
    }
}
