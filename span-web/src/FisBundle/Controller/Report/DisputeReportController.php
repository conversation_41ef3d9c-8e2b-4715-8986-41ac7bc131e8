<?php


namespace FisBundle\Controller\Report;


use CoreBundle\Controller\ListControllerTrait;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\QueryListParams;
use CoreBundle\Utils\Util;
use Doctrine\ORM\QueryBuilder;
use FisBundle\Controller\BaseController;
use FisBundle\Controller\Traits\PortfolioControllerTrait;
use FisBundle\Entity\Dispute;
use FisBundle\Entity\ProgramType;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class DisputeReportController extends BaseController
{
    use ListControllerTrait;
    use PortfolioControllerTrait;

    /**
     * @Route("/admin/fis/report/dispute/list/{page}/{limit}", methods={"GET"}, defaults={"page" = 1,"limit" = 10})
     * @param Request $request
     * @param int     $page
     * @param int     $limit
     *
     * @return SuccessResponse
     */
    public function search(Request $request, $page = 1, $limit = 10)
    {
        Util::longerRequest();
        $cache = $this->traitSearchCached($request, ['id'], $page, $limit);
        return new SuccessResponse($cache);
    }

    protected function getRepository($ab = FALSE)
    {
        return Util::em(false, 'analytics')->getRepository(Dispute::class);
    }

    protected function query(Request $request, $export = false)
    {
        $q = $this->commonQuery($request, $export, false, Dispute::class);
        $this->queryByDateRange($q, 'a.workOfDate');

        return $q;
    }

    protected function queryListParams(QueryBuilder $query, Request $request)
    {
        $params = new QueryListParams($query, $request, 'a', 'count(distinct a)');
        $params->distinct = true;
        $params->orderBy = [
            'a.workOfDate' => 'desc',
        ];
        return $params;
    }

    /**
     * @param Dispute $entity
     * @param string $format
     * @param array  $otherColumns
     *
     * @return array
     */
    protected function getRowData($entity, $format = 'Detail', $otherColumns = [])
    {
        $all = [
            'Date' => $entity->getWorkOfDate()->format(Util::DATE_FORMAT),
            'Issuing Bank' => $entity->getBankName(),
            'Issuing Client' => $entity->getIssuerClientId(),
        ];

        if ($format === 'Detail') {
            /** @var Dispute $entity */
            $all = array_merge($all, [
                'Tran ID' => $entity->getTxnUid(),
                'Original Transaction Date' => Util::formatDateTime($entity->getOriginalTransactionDate()),
                'Issuing Client' => $entity->getClientName(),
                'Group Type' => ProgramType::getNameForBin($entity->getBin()),
                'BIN' => $entity->getBin(),
                'BIN Currency' => $entity->getBinCurrencyAlpha(),
                'PAN' => $entity->getPan(),
                'Card Proxy Number' => $entity->getCardNumberProxy(),
                'Cardholder Name' => $entity->getCardHolderName(),
                'Open Date' => Util::formatDate($entity->getClaimOpenDate()),
                'Claim Requested' => Util::formatDate($entity->getDateWrittenConfirmationWasRequested()),
                'Aging (days)' => $entity->getAgeingBusDays(),
                'Tran Description' => $entity->getOriginalRequestDescription(),
                'Original Tran Type Name' => $entity->getOriginalTxnTypeName(),
                'Merchant' => $entity->getMerchantName(),
                'State' => $entity->getMerchantState(),
                'Country' => $entity->getMerchantCountryAlpha(),
                'Disputed Amount' => $entity->getDisputedAmount(),
                'Dispute Type' => $entity->getDisputeType(),
                'Dispute State' => $entity->getDisputeState(),
                'Dispute Status' => $entity->getDisputeStatus(),
                'Dispute Reason' => $entity->getDisputeReason(),
                'Claim ID' => $entity->getClaimIdentifier(),
            ]);

            foreach ($otherColumns as $title => $express) {
                if (Util::startsWith($express, '$')) {
                    $express = Util::method($express);
                    $all[$title] = $entity->$express();
                    continue;
                }
            }
        }
        return $all;
    }

    /**
     * @Route("/admin/fis/report/dispute/filters", methods={"GET"})
     * @return SuccessResponse
     */
    public function filters()
    {
        $data = $this->queryTopFilters(false);

        /*
        $repo = $this->getRepository();
        $data = array_merge($data, Util::allToFilterOptions([
            'tranTypes' => Util::queryArray($repo->createQueryBuilder('a')
                ->select('a.originalTxnTypeName name')),
            'disputeTypes' => Util::queryArray($repo->createQueryBuilder('a')
                ->select('a.disputeType name')),
            'disputeStates' => Util::queryArray($repo->createQueryBuilder('a')
                ->select('a.disputeState name')),
            'disputeStatuses' => Util::queryArray($repo->createQueryBuilder('a')
                ->select('a.disputeStatus name')),
            'disputeReasons' => Util::queryArray($repo->createQueryBuilder('a')
                ->select('a.disputeReason name')),
            'liabilities' => Util::queryArray($repo->createQueryBuilder('a')
                ->select('a.liability name')),
            'merchantCountries' => Util::queryArray($repo->createQueryBuilder('a')
                ->select('a.merchantCountryAlpha name')),
        ]));
        */
        
        return new SuccessResponse($data);
    }

    /**
     * @param $format
     *
     * @return array
     */
    protected function getOtherColumnsForReport($format)
    {
        if ($format !== 'Detail') {
            return [];
        }
        return [
            'Sub Program ID' => '$subProgramId',
            'Sub Program Name' => '$subProgramName',
            'BIN Currency Code' => '$binCurrencyCode',
            'Card Number' => '$cardNumber',
            'PAN Proxy Number' => '$panProxyNumber',
            'SSN' => '$ssn',
            'Cardholder Client Unique ID' => '$cardholderClientUniqueId',
            'Direct Access Number' => '$directAccessNumber',
            'Reg E' => '$regE',
            'Date Dispute Form Received' => '$dateDisputeFormReceived',
            'Original Request Code' => '$originalRequestCode',
            'Original Transaction Amount' => '$originalTransactionAmount',
            'Original Txn Type Code' => '$originalTxnTypeCode',
            'Provisional Credit' => '$provisionalCredit',
            'Provisional Credit Date' => '$provisionalCreditDate',
            'Claim Close Date' => '$claimCloseDate',
            'Final Resolution Date' => '$finalResolutionDate',
            'Final Credit' => '$finalCredit',
            'Final Credit Date' => '$finalCreditDate',
            'Letters And Dates' => '$lettersAndDates',
            'Auto Charge Back Indicator' => '$autoChargeBackIndicator',
            'Negative Date' => '$negativeDate',
            'Negative Balance Amount' => '$negativeBalanceAmount',
            'Change Back File Date' => '$chargeBackFileDate',
            'Charge Back Reason' => '$chargeBackReason',
            'Charge Back Amount' => '$chargeBackAmount',
            'Representment Amount' => '$representmentAmount',
            'Representment Date' => '$representmentDate',
            'Provisional Credit Reversal Amount' => '$provisionalCreditReversalAmount',
            'Provisional Credit Reversal Date' => '$provisionalCreditReversalDate',
            'Liability' => '$liability',
            'Write Off Amount' => '$writeOffAmount',
            'Write Off Date' => '$writeOffDate',
            'Write Off Request Code' => '$writeOffRequestCode',
            'ATM' => '$atm',
            'Denial Date' => '$denialDate',
            'Denial Reason' => '$denialReason',
            'Claim Identifier' => '$claimIdentifier',
        ];
    }

    /**
     * @Route("/admin/fis/report/dispute/export", methods={"POST"})
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function export(Request $request)
    {
        $request->request->set('format', 'Detail');

        $ret = $this->commonFisExport($request, false);
        if ($ret instanceof SuccessResponse) {
            return $ret;
        }
        list($data, $from, $limit, $format) = $ret;

        $all = [
            'Tran ID', 'Original Transaction Date', 'Issuing Bank',
            'Program', 'Type', 'BIN', 'BIN Currency',
            'PAN', 'Card Proxy Number', 'Cardholder Name',
            'Open Date', 'Claim Requested', 'Aging (days)', 'Tran Description',
            'Original Tran Type Name', 'Merchant', 'State', 'Country',
            'Disputed Amount', 'Dispute Type', 'Dispute State',
            'Dispute Status', 'Dispute Reason', 'Claim ID',
        ];
        $headers = [];
        $all = array_merge($all, array_keys($this->getOtherColumnsForReport($format)));
        foreach ($all as $title) {
            $headers[$title] = 20;
        }

        $filename = 'Dispute Activity ' . $this->getReportFileSuffix($request);
        $destination = Util::uploadDir('report') . $filename . '.csv';
        $excel = $this->loadOrCreateExcel($destination, $filename);

        $excel = $this->generateSheetAppendToExcel($excel, $from . ' ~ ' . ($from + $limit), $headers, $data,
            function ($col, $row, $excel, $title) use ($format) {
                $cache = $format === 'Detail' ? $this->rowCache : $row;
                return $cache[$title] ?? '';
            });
        $this->saveCsvTo($excel, $destination, count($headers));

        return new SuccessResponse('report/' . $filename . '.csv');
    }
}
