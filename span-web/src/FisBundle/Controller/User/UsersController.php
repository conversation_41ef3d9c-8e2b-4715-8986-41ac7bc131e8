<?php


namespace FisBundle\Controller\User;


use CoreBundle\Controller\ListControllerTrait;
use CoreBundle\Entity\Attachment;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\Country;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\Role;
use CoreBundle\Exception\FailedException;
use CoreBundle\Response\DeniedResponse;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Services\UserService;
use CoreBundle\Utils\QueryListParams;
use CoreBundle\Utils\Util;
use Doctrine\ORM\QueryBuilder;
use FisBundle\Controller\BaseController;
use FisBundle\FisBundle;
use FisBundle\Services\AccountBalanceService;
use FisBundle\Services\Velocity\SettingService;
use PhpOffice\PhpSpreadsheet\Exception;
use SalexUserBundle\Entity\User;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class UsersController extends BaseController
{
    public $protected = true;

    use ListControllerTrait;

    /**
     * @Route("/admin/fis/users/list/{page}/{limit}", methods={"GET"}, defaults={"page" = 1,"limit" = 10})
     * @param Request $request
     * @param int     $page
     * @param int     $limit
     *
     * @return SuccessResponse
     */
    public function search(Request $request, $page = 1, $limit = 10)
    {
        return $this->traitSearch($request, ['id'], $page, $limit);
    }

    protected function query(Request $request)
    {
        return \FisBundle\Services\UserService::queryUsers();
    }

    protected function queryListParams(QueryBuilder $query, Request $request)
    {
        $params = new QueryListParams($query, $request, 'u', 'count(distinct u)');
        $params->distinct = true;
        $params->orderBy = [
            'u.createdAt' => 'desc',
        ];
        return $params;
    }

    /**
     * @param User $entity
     *
     * @return array
     */
    protected function getRowData(User $entity)
    {
        $inviting = !$entity->isOnboard();
        return [
            'ID' => $entity->getId(),
            'First Name' => $entity->getFirstName(),
            'Last Name' => $entity->getLastName(),
            'Email' => $entity->getEmail(),
            'Status' => Util::humanize($entity->getStatus()) . ($inviting ? ' (Inviting)' : ''),
            'User Type' => $entity->getCurrentRoleCustomName(),
            'Account Create Date' => Util::formatDate($entity->getCreatedAt()),
            'Last Login Date' => Util::formatDate($entity->getLastLogin()),
            'Platforms' => strtoupper(implode(', ', $entity->getFisPlatforms())),
            'Company' => $entity->getFisCompany(),
        ];
    }

    /**
     * @Route("/admin/fis/users/filters", methods={"GET"})
     * @return SuccessResponse
     */
    public function filters()
    {
        $all = $this->em->getRepository(Role::class)
            ->createQueryBuilder('r')
            ->where(Util::expr()->in('r.name', ':names'))
            ->setParameter('names', FisBundle::getInternalRoleNames())
            ->select('r.id, r.name')
            ->orderBy('r.name')
            ->getQuery()
            ->getArrayResult();
        $all = Util::keyArrayBy($all);
        $map = FisBundle::getInternalRoleNamesMap();
        $roles = [];
        foreach ($map as $name => $newName) {
            $item = $all[$name];
            $item['name'] = $newName;
            $roles[] = $item;
        }
        $data = Util::allToFilterOptions([
            'roles' => $roles,
        ]);
        return new SuccessResponse($data);
    }

    /**
     * @Route("/admin/fis/users/export", methods={"POST"})
     * @param Request $request
     *
     * @return SuccessResponse
     * @throws Exception
     */
    public function export(Request $request)
    {
        Util::longRequest();

        $query = $this->query($request);
        $params = $this->queryListParams($query, $request);
        if ($request->get('query_count')) {
            $count = $this->queryCountForExport($params);
            return new SuccessResponse($count);
        }

        $from  = (int)$request->get('query_from', 0);
        $limit = (int)$request->get('query_limit', 5000);

        $data = $this->queryListForExport($params, $from, $limit, function (User $entity) {
            return $this->getRowData($entity);
        });

        $headers = [
            'ID' => 10,
            'First Name' => 20,
            'Last Name' => 20,
            'Email' => 40,
            'Status' => 20,
            'User Type' => 20,
            'Account Create Date' => 20,
            'Last Login Date' => 20,
            'Platforms' => 20,
        ];

        $filename = 'FIS Users ' . $this->getReportFileSuffix($request);
        $destination = Util::uploadDir('report') . $filename . '.xlsx';
        $excel = $this->loadOrCreateExcel($destination, $filename);

        $excel = $this->generateSheetAppendToExcel($excel, $from . ' ~ ' . ($from + $limit), $headers, $data,
            function ($col, $row, $excel, $title) {
                return $row[$title] ?? '';
            });
        $excel->setActiveSheetIndex(0);
        $this->saveExcelTo($excel, $destination);

        return new SuccessResponse('report/' . $filename . '.xlsx');
    }

    /**
     * @param Request $request
     *
     * @return User|object|null
     * @throws FailedException
     */
    protected function getEntity(Request $request)
    {
        $id = $request->get('id');
        /** @var User|null $entity */
        $entity = null;
        if ($id) {
            $entity = $this->em->getRepository(User::class)->find($id);
        }
        if ($entity) {
            $ps = $entity->getOpenPlatforms();
            if (!Util::includes($ps, Platform::fis())) {
                throw new FailedException('Access denied');
            }
        }
        return $entity;
    }

    /**
     * @Route("/admin/fis/users/detail", methods={"GET"})
     * @param Request  $request
     *
     * @return DeniedResponse|SuccessResponse
     */
    public function detail(Request $request)
    {
        $data = $this->filters()->getResult();
        $data['statuses'] = Util::toFilterOptions([
            User::STATUS_ACTIVE,
            User::STATUS_INACTIVE,
        ]);
        $data['platforms'] = FisBundle::getBuiltInPlatforms();
        $data['programs'] = FisBundle::getAllProgramsKeyedByPlatforms();

        $entity = $this->getEntity($request) ?: new User();

        return new SuccessResponse([
            'source' => $data,
            'form' => $entity->toFisApiArray(),
        ]);
    }

    /**
     * @Route("/admin/fis/users/save", methods={"POST"})
     * @param Request  $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function save(Request $request)
    {
        $this->authRoles([
            Role::ROLE_PLATFORM_OWNER,
            Role::ROLE_PROGRAM_OWNER,
        ]);

        $newUser = false;
        $entity = $this->getEntity($request);
        if (!$entity) {
            $entity = new User();
            $entity->setEnabled(true)
                ->setPlainPassword(Util::randTimeNumber())
                ->setSource('fis_admin');
            $newUser = true;
        }
        $email = $request->get('email');
        $others = $this->em->getRepository(User::class)
            ->findBy([
                'email' => $email,
            ], null, 1);
        if ($others) {
            $other = $others[0];
            if (!Util::eq($other, $entity)) {
                return new FailedResponse('Duplicated email address! Please use another one!');
            }
        }

        $fn = $request->get('firstName');
        $ln = $request->get('lastName');
        if (!$fn || !$ln) {
            return new FailedResponse('Invalid user name!');
        }

        $entity->setUsername($email)
            ->setEmail($email)
            ->setFirstName($fn)
            ->setLastName($ln)
            ->setStatus($request->get('status', User::STATUS_INACTIVE))
            ->setCountry(Country::usa());

        // Set the avatar
        if ($request->get('avatar_delete')) {
            $entity->setProfilePicture(null);
        }
        if ($request->get('avatar_id')) {
            $file = Attachment::find($request->get('avatar_id'));
            $entity->setProfilePicture($file->getAssetUrl(true));
        }
        $entity->persist();

        // Set the role
        foreach ($entity->getTeams() as $role) {
            $entity->removeTeam($role);
        }
        $role = null;
        $roleId = $request->get('role');
        if ($roleId) {
            $role = Role::find($roleId);
        }
        if (!$role) {
            $role = Role::find(Role::ROLE_CORPORATE_USER);
        } else if (!in_array($role->getName(), FisBundle::getInternalRoleNames())) {
            $role = Role::find(Role::ROLE_CORPORATE_USER);
        }
        $entity->ensureRole($role->getName());
        $entity->setCurrentRole($role->getName());

        // Set the platform
        foreach ($entity->getAccessiblePlatforms() as $platform) {
            $entity->removeAccessiblePlatform($platform);
        }
        $entity->addAccessiblePlatform(Platform::fis());

        // Set the card program
        foreach ($entity->getAccessibleCardPrograms() as $cp) {
            $entity->removeAccessibleCardProgram($cp);
        }
        $entity->addAccessibleCardProgram(CardProgram::fis());

        $entity->setFisPlatforms($request->get('platforms', []))
            ->setFisDefaultPlatform($request->get('defaultPlatform'))
            ->setFisPrograms($request->get('programs', []))
            ->setFisCompany($request->get('company', 'FIS'))
            ->persist();

        SettingService::initForUser($entity);

        if ($newUser) {
            UserService::sendResetPasswordEmail($entity, CardProgram::fis());
        }

        // Clear cache
        Util::clearRequestSignatureCache($entity);
        AccountBalanceService::getClientsMap(true, $entity);

        return new SuccessResponse();
    }

    /**
     * @Route("/admin/fis/users/{user}/invite", methods={"POST"})
     * @param Request $request
     * @param User    $user
     *
     * @return SuccessResponse
     */
    public function invite(Request $request, User $user)
    {
        if ($user->getConfirmationToken()) {
            UserService::sendResetPasswordEmail($user, CardProgram::fis());
        }
        return new SuccessResponse();
    }

    /**
     * @Route("/admin/fis/users/{user}/delete", methods={"DELETE"})
     * @param Request $request
     * @param User    $user
     *
     * @return SuccessResponse
     */
    public function remove(Request $request, User $user)
    {
        $user->setStatus(User::STATUS_INACTIVE)
            ->persist();
        return new SuccessResponse();
    }

    /**
     * @Route("/admin/fis/users/{user}/recover", methods={"POST"})
     * @param Request $request
     * @param User    $user
     *
     * @return SuccessResponse
     */
    public function recover(Request $request, User $user)
    {
        $user->setStatus(User::STATUS_ACTIVE)
            ->persist();
        return new SuccessResponse();
    }
}
