#admin parts
##################################################
#Common
#Button
btn.search : Search
btn.modify : Edit
btn.view : View
btn.save : Save
btn.delete : Delete
btn.confirm : Confirm
btn.cancel : Cancel
btn.close : Close
btn.actions : Actions
btn.feeitem : Fee Item Setting
btn.setaffiliate : Set As Affiliate

#Column:
column.action : Actions

##################################################
#Reshipper
#Button
reshipper.btn.create : Create Reshipper
reshipperAddress.btn.create : Create Billing Address
reshipperAddress.btn.goback : Reshipper(s) Management

#Title
reshipper.title.view: Reshipper(s) View
reshipper.title.modify : Reshipper Management
reshipper.title.new : Reshipper Management
reshipperAddress.title.view: Reshipper Billing Address(es) View
reshipperAddress.title.modify : Reshipper Billing Address Management
reshipperAddress.title.new : Reshipper Billing Address Management

#Header
reshipper.header.modify : Reshipper / Edit
reshipper.header.new : Reshipper / Create
reshipperAddress.header.modify : Reshipper Billing Address / Edit
reshipperAddress.header.new : Reshipper Billing Address / Create

#From
reshipper.search.title : Company Name
reshipperAddress.search.title : Default Address

#Column
reshipper.column.name : Company Name
reshipper.column.dba : DBA
reshipper.column.address : Company Address
reshipper.column.apiIntegrationId : API Integration Id
reshipper.column.autoSignUp : Auto Create
reshipper.column.accountValidation : Account Validation
reshipper.column.contact: Contact Person
reshipper.column.email: Email
reshipper.column.phone: Phone
reshipperAddress.column.addressTypeName : Address Type Name
reshipperAddress.column.defaultAddress : Default Address
reshipperAddress.column.secondAddress : Second Address
reshipperAddress.column.phone : Phone

#Label
reshipper.label.delete : Delete Reshipper
reshipperAddress.label.delete : Delete Billing Address

#Messages
reshipper.msg.delete : Do you want to delete this Reshipper?
reshipperAddress.msg.delete : Do you want to delete this Reshipper Billing Address?

##################################################
#Processor
#Button
processor.btn.create : Create Processor

#Title
processor.title.view : Processor(s) View
processor.title.modify : Processor Management
processor.title.new : Processor Management

#Header
processor.header.modify : Processor / Edit
processor.header.new : Processor / Create

#From
processor.search.title : Processor Name

#Column
processor.column.name : Name
processor.column.address : Address
processor.column.contact : Contact Person
processor.column.email : Email

#Label
processor.label.delete : Delete Processor

#Messages
processor.msg.delete : Do you want to delete this Processor?

##################################################
#Program
#Button
program.btn.create : Create Program

#Title
program.title.view : Program View
program.title.modify : Program Management
program.title.new : Program Management

#Header
program.header.modify : Program / Edit
program.header.new : Program / Create

#From
program.search.title : Program Name

#Column
program.column.name : Name
program.column.address : Address
program.column.contact : Contact Person
program.column.email : Email

#Label
program.label.delete : Delete Program

#Messages
program.msg.delete : Do you want to delete this Program?

##################################################
#Issuing Bank
#Button
issuingbank.btn.create : Create Issuing Bank

#Title
issuingbank.title.view : Issuing Bank View
issuingbank.title.modify : Issuing Bank Management
issuingbank.title.new : Issuing Bank Management

#Header
issuingbank.header.modify : Issuing Bank / Edit
issuingbank.header.new : Issuing Bank / Create

#From

#Column
issuingbank.column.name : Name
issuingbank.column.address : Address
issuingbank.column.contact : Contact Person
issuingbank.column.email : Email

#Label
issuingbank.label.delete : Delete Issuing Bank

#Messages
issuingbank.msg.delete : Do you want to delete this Issuing Bank?

##################################################
#Fee Category
#Button
feecategory.btn.create : Create Category

#Title
feecategory.title.view : Category View
feecategory.title.modify : Category Management
feecategory.title.new : Category Management

#Header
feecategory.header.modify : Category / Edit
feecategory.header.new : Category / Create

#From
feecategory.search.title : Category Name

#Column
feecategory.column.name : Name

#Label
feecategory.label.delete : Delete Category

#Messages
feecategory.msg.delete : Do you want to delete this Category?

##################################################
#Fee Global Name
#Button
feeglobalname.btn.create : Create Global Name

#Title
feeglobalname.title.view : Global Name View
feeglobalname.title.modify : Global Name Management
feeglobalname.title.new : Global Name Management

#Header
feeglobalname.header.modify : Global Name / Edit
feeglobalname.header.new : Global Name / Create

#From

#Column
feeglobalname.column.name : Name
feeglobalname.column.category : Category

#Label
feeglobalname.label.delete : Delete Global Name

#Messages
feeglobalname.msg.delete : Do you want to delete this Global Name?

##################################################
#Fee Item
#Button
feeitem.btn.create : Create Fee Item
feeitem.btn.add : Add
feeitem.btn.remove : Remove

#Title
feeitem.title.view : Fee Item View
feeitem.title.modify : Fee Item Management
feeitem.title.new : Fee Item Management

#Header
feeitem.header.modify : Fee Item / Edit
feeitem.header.new : Fee Item / Create

#From

#Table
#Comment on 2017-03-24 remove add&remove functions from fee item
#feeitem.table.label.selected : Selected Fee Item
#feeitem.table.label.unselected : Unselected Fee Item

#Column
feeitem.column.category : Fee Category
feeitem.column.globalname : Fee Type
feeitem.column.entitytype : Entity Type
feeitem.column.entityid : Entity Id
feeitem.column.name : Specific Name
feeitem.column.appliedby : Applied By
feeitem.column.measure : Measure
#Comment on 2017-03-29 remove max fee(fixed/ratio) and fee(fixed/ratio)
#feeitem.column.maxfeefixed : Max Fee Fixed
#feeitem.column.maxfeeratio : Max Fee Ration
#feeitem.column.feeFixed : Fee Fixed
#feeitem.column.feeratio : Fee Ratio
#Add on 2017-03-29 add cost tern(fixed/ratio)
feeitem.column.costternfixed : Cost To Tern Fixed
feeitem.column.costternratio : Cost To Tern Ratio

#Label
feeitem.label.delete : Delete Fee Item
feeitem.label.add : Add Fee Item
feeitem.label.remove : Remove Fee Item

#Messages
feeitem.msg.delete : Do you want to delete this Fee Item?
feeitem.msg.add : Do you want to add this Fee Item to %entityType% %entityId%?
feeitem.msg.remove : Do you want to remove this Fee Item from %entityType% %entityId%?

##################################################
#Card Type
#Button
cardtype.btn.create : Create Card Type

#Title
cardtype.title.view : Card Type View
cardtype.title.modify : Card Type Management
cardtype.title.new : Card Type Management

#Header
cardtype.header.modify : Card Type / Edit
cardtype.header.new : Card Type / Create

#From

#Column
cardtype.column.name : Name
cardtype.column.issuingbank : Issuing Bank
cardtype.column.processor : Processor
cardtype.column.programmanager : Program Manager

#Label
cardtype.label.delete : Delete Card Type

#Messages
cardtype.msg.delete : Do you want to delete this Card Type?

##################################################
#Card Program Set Up Wizard
#Title
cardprogramwizard.title : Card Program Set Up Wizard

#Step Title
cardprogramwizard.step.1.title : Step 1
cardprogramwizard.step.2.title : Step 2
cardprogramwizard.step.3.title : Step 3
cardprogramwizard.step.4.title : Step 4
cardprogramwizard.step.5.title : Step 5
cardprogramwizard.step.6.title : Step 6
cardprogramwizard.step.7.title : Step 7
cardprogramwizard.step.8.title : Step 8

#Step Name
cardprogramwizard.step.1.name : Set Program Name
cardprogramwizard.step.2.name : Brand Tenant
cardprogramwizard.step.3.name : Card Type
#remove Kyc provider process and move related functions into country process
#cardprogramwizard.step.4.name : KycProvider
cardprogramwizard.step.4.name : Reshipper
cardprogramwizard.step.5.name : Country
cardprogramwizard.step.6.name : Fee Schedule
cardprogramwizard.step.7.name : Add-ons
cardprogramwizard.step.8.name : Email

#Card Type Detail Dialog
cardprogramwizard.cardtype.btn.add : Add Card Type
cardprogramwizard.cardtype.column.name : Card Type Name
cardprogramwizard.cardtype.column.cusname : Customized Name
cardprogramwizard.cardtype.column.artwork : Artwork
cardprogramwizard.cardtype.column.description : Description
cardprogramwizard.cardtype.column.benefit : Benefit
cardprogramwizard.cardtype.column.maxBalance : Max Balance
cardprogramwizard.cardtype.column.maxLoad : Max Load
cardprogramwizard.cardtype.column.minLoad : Min Load
cardprogramwizard.cardtype.column.cardsallowed : Numbers of Cards Allowed
cardprogramwizard.cardtype.column.isautocreated : Need Auto Created
cardprogramwizard.cardtype.detail.label : Card Type Details Configuration
cardprogramwizard.cardtype.detail.title : Card Type Details Configuration

#Fee Item Step Table
cardprogramwizard.feeitem.column.name : Name
#Comment on 2017-03-29 remove max fee(fixed/ratio) and fee(fixed/ratio)
#cardprogramwizard.feeitem.column.maxFeeFixed : Max Fee Fixed
#cardprogramwizard.feeitem.column.maxFeeRatio : Max Fee Ratio
#cardprogramwizard.feeitem.column.costFixed : Cost Fixed
#cardprogramwizard.feeitem.column.costRatio : Cost Ratio
#Add on 2017-03-29 add cost tern(fixed/ratio)
cardprogramwizard.feeitem.column.costternfixed : Cost To Tern Fixed
cardprogramwizard.feeitem.column.costternratio : Cost To Tern Ratio

#Fee Item Detail Dialog
cardprogramwizard.feeitem.detail.btn : Details Config
cardprogramwizard.feeitem.detail.label : Fee Item Details Configuration
cardprogramwizard.feeitem.common.fixed : Fixed
cardprogramwizard.feeitem.common.Ratio : Ratio
cardprogramwizard.feeitem.consumer : Fee to Consumer
cardprogramwizard.feeitem.brandpartner : Fee to Brand Partner
cardprogramwizard.feeitem.brandpartner.name : Brand Partner Name

#Fee Item Revenue Dialog
cardprogramwizard.feeitem.revenue.btn : Revenue Config
cardprogramwizard.feeitem.revenue.label : Fee Item Revenue Configuration
cardprogramwizard.feeitem.revenue.btn.add : Add Tenant
cardprogramwizard.feeitem.revenue.column.type : Tenant Type
cardprogramwizard.feeitem.revenue.column.id : Tenant ID
cardprogramwizard.feeitem.revenue.column.name : Tenant Name
cardprogramwizard.feeitem.revenue.column.fixed : Fixed
cardprogramwizard.feeitem.revenue.column.ratio : Ratio

##################################################
#Card Program Management
#Title
cardprogram.title.view : Card Programs

#Column
cardprogram.column.name : Name
cardprogram.column.brandpartner : Brand Partner
cardprogram.column.cobrand : Co-Brand
cardprogram.column.marketingpartner : Marketing Partner
cardprogram.column.status : Status

#Label
cardprogram.label.status : Set Status

#button
cardprogram.btn.setstatus : Set Status

##################################################
#Service Management
#Title
service.title.view : Service Management View

#Column
service.column.name : Name

##################################################
#Platform Management
#Title
platform.title.view : Platform View

#Column
platform.column.name : Name

##################################################
#Email History
#Title
emailhistory.title.view : Email History

#Column
emailhistory.column.subject : Subject
emailhistory.column.contenttype : Content Type
emailhistory.column.sender : Sender
emailhistory.column.recipient : Recipient
emailhistory.column.sendtime : Send Time
emailhistory.column.error : Error
emailhistory.column.body : Body
emailhistory.column.purpose : Purpose

#Label
emailhistory.label.detail : Email Deatil Information

##################################################
#IP White List
#Title
ipwhitelist.title.view : IP White List
userStatusList.title.view : User Status List

#Column
ipwhitelist.column.ip : IP Address
ipwhitelist.column.user: User
userstatuslist.column.status : User Status

##################################################
#Page Content
#Title
pagecontent.title.view : Page Content

#Column
pagecontent.column.title : Title
pagecontent.column.usefullinks : Useful Links

##################################################
#Card
#Button
card.btn.balance : View Balance History

#Title
card.title.view : Cards View

#Header
#From

#Column
card.column.number : Number
card.column.username : Username
card.column.balance : Balance
card.column.issued : Issued
card.column.status : Status
card.column.currency : Currency
card.column.create : Create Date

#Label
#Messages

##################################################
#Card Balance
#Button
#Title
cardbalance.title.view : Card Balance History

#Header
#From

#Column
cardbalance.column.type : Type
cardbalance.column.prebalance : Previous Balance
cardbalance.column.curbalance : Current Balance
cardbalance.column.update : Update Date

#Label
#Messages

##################################################
#User Discount
#Button
#Title
userdiscount.title.view : User Discount Setting

#Header
#Form

#Column
userdiscount.column.feeglobalname : Fee Global Name
userdiscount.column.feename : Fee Name
userdiscount.column.previousfeefixed : Fee Fixed
userdiscount.column.discountfeefixed : Discount Fee Fixed
userdiscount.column.previousfeeratio : Fee Ratio
userdiscount.column.discountfeeratio : Discount Fee Ratio
userdiscount.column.expiredate : Expire Date
userdiscount.column.minload : Min Load
userdiscount.column.update : Update Date

#Label
#Messages

##################################################
#Merchant
#Title
merchant.title.modify : Merchant Management
merchant.title.view : Merchant Details

#Column
merchant.column.merchantname : Merchant Name
merchant.column.merchantid : MID
merchant.column.merchantcustname : Merchant Cust Name
merchant.column.mcc : MCC
merchant.column.merchanttype : Merchant Type


##################################################
#Affiliate
#Title
affiliate.title.revenueshare : Affiliate Revenue Share
affiliate.title.delete : Delete Affiliate

#Column
affiliate.column.revenue.type : Type
affiliate.column.revenue.fixed : Fixed
affiliate.column.revenue.ratio : Ratio
affiliate.column.revenue.currency : Currency

#button
affiliate.btn.revenueshare : Set Revenue Share

#Message
affiliate.msg.delete : Do you want to delete this Affiliate?