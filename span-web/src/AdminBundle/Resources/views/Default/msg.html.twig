<link rel="stylesheet" href="/node_modules/bootstrap/dist/css/bootstrap.min.css">
<link rel="stylesheet" href="/node_modules/font-awesome/css/font-awesome.min.css">
<script src="/static/npm/jquery.min.js"></script>
<script src="/node_modules/bootstrap/dist/js/bootstrap.min.js"></script>

<div class="panel panel-default">
    <div class="panel-body">
        <div class="icon">
            <i class="fa fa-{{ success ? 'check' : 'times' }}-circle"></i>
        </div>

        <div>{{ message | raw }}</div>

        <a href='{{ link }}' class="btn btn-primary" id="link-btn">
            {{ button }}
            {% if success %} ( 2 ){% endif %}
        </a>
    </div>
</div>

<script>
  $(function () {
      {% if success %}
        var count = 2;
        var $btn = $('#link-btn');
        var interval = setInterval(function () {
          count--;
          $btn.text('{{ button }} ( ' + count + ' )');
          if (count <= 1) {
            clearInterval(interval);
            location.href = '{{ link }}';
          }
        }, 1000)
      {% endif %}
  })
</script>

{% include 'layout/embed-in-md.html.twig' %}

<style>
    body {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        font-size: 16px;
        background: #F5F5F5;
        padding: 0;
        margin: 0;
    }

    .panel {
        text-align: center;
        min-width: 400px;
        max-width: 95% !important;
        margin-top: 30px;
    }

    .panel-body {
        padding: 25px;
    }

    .panel-body > .icon {
        color: {{ success ? 'green' : 'red' }};
        font-size: 50px;
        margin-bottom: 20px;
    }

    .panel-body > .btn {
        margin-top: 20px;
    }
</style>