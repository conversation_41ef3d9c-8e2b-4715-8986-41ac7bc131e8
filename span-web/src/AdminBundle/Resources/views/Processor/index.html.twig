{% extends 'layout/base-layout.html.twig' %}
{% block page_title %} {{ 'processor.title.view'|trans }} {% endblock %}


{% block page_content %}
    <!--/row-->
    <div class="row-fluid opera-box">
        {% for flashMessage in app.session.flashbag.get('success') %}
            <div class="alert alert-success">
                <button type="button" class="close" data-dismiss="alert">&times;</button>
                {{ flashMessage }}
            </div>
        {% endfor %}
        {% for flashMessage in app.session.flashbag.get('failure') %}
            {% if flashMessage %}
                <div class="alert alert-error">
                    <button type="button" class="close" data-dismiss="alert">&times;</button>
                    {{ flashMessage }}
                </div>
            {% endif %}
        {% endfor %}

        {% if editable(Module.ID_PROCESSOR_MANAGEMENT) %}
            <div class="opera-right span3 pull-right">
                <button type="button" class="btn btn-success" id="new_processor"
                        onclick="window.location.href='{{ path('admin_processor_new') }}';">{{ 'processor.btn.create'|trans }}</button>
            </div>
        {% endif %}
        {#Comment on 2017-02-23 replace with jquery data table
        <div class="opera-right span3 pull-right">
            <form action="{{ path('admin_processor_search')}}" method="get">
                <input type="text" name="search_info" id="search_info" value="{{ search_info }}" title="{{ 'processor.search.title'|trans }}" />
                <button type="submit" class="btn btn-success" id="search_processor" >{{ 'btn.search'|trans }}</button>
            </form>
        </div>#}
    </div>

    <div class="row-fluid">
        <table class="table table-striped table-bordered table-hover" id="processorTable">
            <thead>
            <tr>
                <th>{{ 'processor.column.name'|trans }}</th>
                <th>{{ 'processor.column.address'|trans }}</th>
                <th>{{ 'processor.column.contact'|trans }}</th>
                <th>{{ 'processor.column.email'|trans }}</th>
                <th>{{ 'column.action'|trans }}</th>
            </tr>
            </thead>
            <tbody>
            {% for item in processors %}
                <tr>
                    <td>{{ item.getName() }}</td>
                    <td>{{ item.getAddress() }}</td>
                    <td>{{ item.getContact().getFirstName()~' '~ item.getContact().getLastName()}}</td>
                    <td>{{ item.getContact().getEmail() }}</td>
                    <td>
                        {#Changed on 2017-02-27 with button group#}
                        <div class="btn-group">
                            <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">
                                {{ 'btn.actions'|trans }} <span class="caret"></span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-right">
                                {% if editable(Module.ID_PROCESSOR_MANAGEMENT) %}
                                    <li><a href="{{ path('admin_processor_modify', {'processor_id':item.getId()}) }}">{{ 'btn.modify'|trans }}</a></li>
                                    <li>
                                        <a href="#" data-toggle="modal"  onclick="def({{ item.getId() }})">{{ 'btn.delete'|trans }}</a>
                                        <form action="{{ path('admin_processor_delete',{'processor_id':item.getId() }) }}" method="post"></form>
                                    </li>
                                {% endif %}
                                <li><a href="/admin/tenants/contacts/{{ item.id }}" target="_blank">Contacts</a></li>
                                <li><a href="/admin/tenants/{{ item.id }}/fee-options" target="_blank">Fee options</a></li>
                                <li><a href="{{ path('admin_fee_item', {'entity_id':item.getId(), 'entity_type':'Processor'}) }}">{{ 'btn.feeitem'|trans }}</a></li>
                            </ul>
                        </div>
                    </td>
                </tr>
            {% endfor %}
            </tbody>
            Show
            <div class="btn-group">
                <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    Page <span class="caret"></span>
                </button>
                <ul class="dropdown-menu">
                    <li><a href="{{ path('admin_processor',{'limit':5}) }}">default</a></li>
                    <li><a href="{{ path('admin_processor',{'limit':10}) }}">10</a></li>
                    <li><a href="{{ path('admin_processor',{'limit':20 }) }}">20</a></li>
                    <li><a href="{{ path('admin_processor',{'limit':50 }) }}">50</a></li>
                </ul>
            </div>
            entries
        </table>
<div class="opera-right span3 pull-right">
            {{ knp_pagination_render(processors) }}
</div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteProcessorConfirmModal" tabindex="-1" role="dialog" aria-labelledby="deleteProcessorConfirmModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <input type="hidden" id="deid" value="">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="deleteProcessorConfirmModalLabel">{{ 'processor.label.delete'|trans }}</h4>
                </div>
                <div class="modal-body">
                    {{ 'processor.msg.delete'|trans }}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">{{ 'btn.cancel'|trans }}</button>
                    <button type="button" class="btn btn-danger confirm-button" data-dismiss="modal" onclick="defy()">{{ 'btn.confirm'|trans }}</button>
                </div>
            </div>
        </div>
    </div>

    <!--/row-->
{% endblock %}

{% block javascripts_inline %}
    <script>
        // triggered when delete confirm modal is about to be shown
        $("#deleteProcessorConfirmModal").on("show.bs.modal", function(e) {
            // Pass form reference to modal
            var form = $(e.relatedTarget).siblings('form');
            $(this).find(".confirm-button").data('form', form);
        });

        // action on confirm
        $("#deleteProcessorConfirmModal .confirm-button").on("click", function(){
            $(this).data('form').submit();
        });

        function def(id) {
            $.ajax({
                type: "get",
                cache: false,
                async:true,
                url: "{{ path('admin_processor_ajaxdelete')}}",
                data: {'pid': id},
                success: function(data) {
                    if(data=='no'){
                        alert('Can not delete');
                    }else
                    {
                        $('#deid').val(id);
                        $('#deleteProcessorConfirmModal').modal('show');
                    }
                }
            });
        }
        function defy() {
            var pidy=$('#deid').val();
            $.ajax({
                type: "get",
                cache: false,
                async:true,
                url: "{{ path('admin_processor_ajaxydelete')}}",
                data: {'pid': pidy},
                success: function(data) {
                  alert('success!');
                    window.location.reload();
                }
            });
        }

        //Add on 2017-02-23 replace with jquery data table
        $("#processorTable").DataTable({
            "paging": false,
            "lengthChange": false,
            "searching": false,
            "ordering": true,
            "info": true,
            "autoWidth": false
//            "lengthMenu": [[20, 50, 100, -1], [20, 50, 100, "All"]],
//            "columnDefs": [ {
//                "targets": [4],
//                "orderable": false,
//                "searchable": false
//            }]
        });
    </script>
{% endblock %}