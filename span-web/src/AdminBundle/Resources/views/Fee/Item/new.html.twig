{% extends 'layout/base-layout.html.twig' %}
{% block page_title %} {{ 'feeitem.title.new'|trans }} {% endblock %}


{% block page_content %}
    <div class="row-fluid">
        <div>
            <h2>{{ 'feeitem.header.new'|trans }}</h2>
        </div>
    </div>
    <!--/row-->
    <div class="row-fluid">
        <div>
            <form action="{{ path('admin_fee_item_new',{'entity_id':form.vars.value.feeEntityId , 'entity_type':form.vars.value.feeEntityType}) }}" method="post"  >
                {#{{ form_widget(form) }}#}
                <input type="hidden" value="{{ entity_id }}" name="entityid">
                <input type="hidden" value="{{ entity_type }}" name="entitytype">
                <h5 style="font-weight: 700">Fee Global Name</h5>
                <select id="FeeGlobalName" name="FeeGlobalName" STYLE="width: 100%;height: 33px">
                    {#<option value=""></option>#}
                    {% for item in feeGlobalNames %}
                        <option value="{{ item.getId() }}">{{ item.getName() }}</option>
                    {% endfor %}
                </select><br>
                <h5 style="font-weight: 700">Fee Name</h5>
                <input type="text" name="FeeName" STYLE="width: 100%;height: 33px">
                <br><br>
                <h5 style="font-weight: 700">Transaction Code</h5>
                <input type="text" name="TransactionCode" STYLE="width: 100%;height: 33px">
                <br><br>
                {#<h5 style="font-weight: 700">Applied By</h5>#}
                {#<input type="text" name="AppliedBy" STYLE="width: 100%;height: 33px">#}
                {#<br><br>#}
                <h5 style="font-weight: 700">Measure</h5>
                <input type="number" name="Measure" STYLE="width: 100%;height: 33px">
                <br><br>
                {#<h5 style="font-weight: 700">Measure measure str</h5>#}
                {#<select id="Measuremeasurestr" name="Measuremeasurestr" STYLE="width: 100%;height: 33px">#}
                    {#<option value=""></option>#}
                    {#{% for item in measureChoices %}#}
                        {#<option value="{{item}}">{{ item}}</option>#}
                    {#{% endfor %}#}
                {#</select><br>#}
                <h5 style="font-weight: 700">Measure unit</h5>
                <select id="Measureunit" name="Measureunit" STYLE="width: 100%;height: 33px">
                    {#<option value=""></option>#}
                    {% for item in frequencyUnit %}
                        <option value="{{item}}">{{ item}}</option>
                    {% endfor %}
                </select><br>
                <h5 style="font-weight: 700">Cost tern fixed currency</h5>
                <select id="Costternfixedcurrency" name="Costternfixedcurrency" STYLE="width: 100%;height: 33px">
                    <option value=""></option>
                    {% for item in currencyChoices %}
                        <option value="{{item}}">{{ item}}</option>
                    {% endfor %}
                </select><br>
                <h5 style="font-weight: 700">Cost To Tern Ratio</h5>
                <input type="text" name="CostToTernRatio" STYLE="width: 100%;height: 33px">
                <br><br>
                <h5 style="font-weight: 700">Cost To Tern Fixed</h5>
                <input type="text" name="CostTernFixed" STYLE="width: 100%;height: 33px">
                <br><br>
                <button type="submit" class="btn">{{ 'btn.save'|trans }}</button>
                <button type="button" id="btnCancel" class="btn" onclick="this.form.action='{{ path('admin_fee_item',{'entity_id':form.vars.value.feeEntityId , 'entity_type':form.vars.value.feeEntityType}) }}';this.form.submit();">{{ 'btn.cancel'|trans }}</button>

            </form>
        </div>
    </div>
    <!--/row-->
{% endblock %}

{% block javascripts_inline %}
    <script>
        $('#btnConfirm').on('click', function () {
            var fixed = $('#form_costTernFixed').val();
            var currency = $('#form_costTernFixedCurrency').val();
            if(fixed == '' && currency != ''){
                alert('Please enter the fixed with selected currency!');
                return false;
            }
            if(fixed !='' && currency == ''){
                alert('Please select currency with the fixed value!');
                return false;
            }

        });
    </script>
{% endblock %}