{% extends 'layout/base-layout.html.twig' %}
{% block page_title %} {{ 'feeitem.title.modify'|trans }} {% endblock %}

{% block page_content %}
    <div class="row-fluid">
        <div>
            <h2>{{ 'feeitem.header.modify'|trans }}</h2>
        </div>
    </div>

    <div class="row-fluid">
        <div>
            <form action="{{ path('admin_fee_item_modify', {'item_id': item_id,'entity_id':form.vars.value.feeEntityId , 'entity_type':form.vars.value.feeEntityType, 'target':target}) }}" method="post"  >
                {#{{ form_widget(form) }}#}

                <h5 style="font-weight: 700">Fee Global Name</h5>
                <select id="FeeGlobalName" name="FeeGlobalName" STYLE="width: 100%;height: 33px">
                    {#<option value=""></option>#}
                    {% for item in feeGlobalNames %}
                        <option value="{{ item.getId() }}" {% if feeGlobalName== item.getId() %} selected="selected"{% endif %}>{{ item.getName() }}</option>
                    {% endfor %}
                </select><br>
                <h5 style="font-weight: 700">Fee Name</h5>
                <input type="text" name="FeeName" STYLE="width: 100%;height: 33px" {% if feeName %} value="{{ feeName }}"{% endif %}>
                <br><br>
                <h5 style="font-weight: 700">Transaction Code</h5>
                <input type="text" name="TransactionCode" STYLE="width: 100%;height: 33px" {% if transactionCode %} value="{{ transactionCode }}"{% endif %}>
                <br><br>
                {#<h5 style="font-weight: 700">Applied By</h5>#}
                {#<input type="text" name="AppliedBy" STYLE="width: 100%;height: 33px">#}
                {#<br><br>#}

                {% if entity_type != null and entity_id != null %}
                    <input type="hidden" value="{{ entity_type }}" name="entitytype">
                    <input type="hidden" value="{{ entity_id }}" name="entityid">
                {% else %}
                    <h5 style="font-weight: 700">Tenant Type</h5>
                    <select id="entitytype" name="entitytype" STYLE="width: 100%;height: 33px">
                        <option value=""> - Select - </option>
                        <option value="Processor">Processor</option>
                        <option value="Program">Program Manager</option>
                        <option value="LoadPartner">Load Partner</option>
                        <option value="KycProvider">Kyc Provider</option>
                        <option value="IssuingBank">Issuing Bank</option>
                        <option value="Service">Service Manager</option>
                        <option value="Platform">Platform</option>
                    </select>
                    <br><br>
                    <h5 style="font-weight: 700">Tenant</h5>
                    <select id="entityid" name="entityid" STYLE="width: 100%;height: 33px">
                        <option value=""> - Select - </option>
                    </select>
                    <br><br>
                {% endif %}

                <h5 style="font-weight: 700">Measure</h5>
                <input id="measure" type="number" name="Measure" STYLE="width: 100%;height: 33px" {% if measure %} value="{{ measure }}"{% endif %}>
                <br><br>
                {#<h5 style="font-weight: 700">Measure measure str</h5>#}
                {#<select id="Measuremeasurestr" name="Measuremeasurestr" STYLE="width: 100%;height: 33px">#}
                {#<option value=""></option>#}
                {#{% for item in measureChoices %}#}
                {#<option value="{{item}}">{{ item}}</option>#}
                {#{% endfor %}#}
                {#</select><br>#}
                <h5 style="font-weight: 700">Measure unit</h5>
                <select id="Measureunit" name="Measureunit" STYLE="width: 100%;height: 33px">
                    {#<option value=""></option>#}
                    {% for item in frequencyUnit %}
                        <option value="{{item}}" {% if measureunit== item%} selected="selected"{% endif %}>{{ item}}</option>
                    {% endfor %}
                </select><br>
                <h5 style="font-weight: 700">Cost tern fixed currency</h5>
                <select id="Costternfixedcurrency" name="Costternfixedcurrency" STYLE="width: 100%;height: 33px">
                    <option value=""></option>
                    {% for item in currencyChoices %}
                        <option value="{{item}}" {% if currency== item%} selected="selected"{% endif %}>{{ item}}</option>
                    {% endfor %}
                </select><br>
                <h5 style="font-weight: 700">Cost To Tern Ratio</h5>
                <input type="text" name="CostToTernRatio" STYLE="width: 100%;height: 33px" {% if costToTernRatio %} value="{{ costToTernRatio }}"{% endif %}>
                <br><br>
                <h5 style="font-weight: 700">Cost To Tern Fixed</h5>
                <input type="text" id="form_costTernFixed" name="CostTernFixed" STYLE="width: 100%;height: 33px" {% if Fixed and Cou %} value="{{ Fixed|  money_format_amount(Cou)}}" {% endif %}>
                <br><br>
                <button type="submit" id="btnConfirm" class="btn btn-primary">{{ 'btn.save'|trans }}</button>
                <button type="button" id="btnCancel" class="btn" onclick="this.form.action='{{ path('admin_fee_item',{'entity_id':form.vars.value.feeEntityId , 'entity_type':form.vars.value.feeEntityType, 'target':target}) }}';this.form.submit();">{{ 'btn.cancel'|trans }}</button>
            </form>
        </div>
    </div>
{% endblock %}

{% block javascripts_inline %}
    <script>
        $('#btnConfirm').on('click', function () {
            //Check fixed and currency
            var fixed = $('#form_costTernFixed').val();
            var currency = $('#Costternfixedcurrency').val();
            if(fixed == '' && currency != ''){
                alert('Please enter the fixed with selected currency!');
                return false;
            }
            if(fixed !='' && currency == ''){
                alert('Please select currency with the fixed value!');
                return false;
            }

            //Check measure and unit
            var measure = $('#measure').val();
            var unit = $('#measureunit').val();
            if(measure == '' && unit != ''){
                alert('Please enter the measure with selected measure unit!');
                return false;
            }
            if(measure !='' && unit == ''){
                alert('Please select measure unit with the measure value!');
                return false;
            }

            //Check
            var tenantType = $('#entitytype').find("option:selected").val();
            var tenant = $('#entityid').find("option:selected").val();
            if(tenantType == '' && tenant != ''){
                alert('Please select tenant type with selected tenant!');
                return false;
            }
            if(tenantType !='' && tenant == ''){
                alert('Please select tenant with the selected tenant type!');
                return false;
            }

        });

        $('#entitytype').on('change', function () {
            var target = $('#entitytype').find("option:selected").val();
            if(target == ""){
                $('#entityid').empty()
                    .prepend('<option value=""> - Select - </option>');
                return false;
            }
            $.post("{{ path('admin_tenant_type_fee') }}", {tenantType: target}, function (data) {
                $('#entityid').empty()
                    .prepend('<option value=""> - Select - </option>');
                for (var tenant in data) {
                    if (data.hasOwnProperty(tenant)) {
                        var option = document.createElement("option");
                        option.name = data[tenant];
                        option.innerHTML = data[tenant];
                        option.value = tenant;
                        $('#entityid').append(option);
                    }
                }
                $('#entityid').val('');

                if ($('#entityid').selectpicker) {
                    $('#entityid').selectpicker('refresh');
                }
            }, "json")
        });
    </script>
{% endblock %}