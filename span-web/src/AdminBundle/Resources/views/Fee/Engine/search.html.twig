<form action="/admin/fee-engine/list" method="post" class="mt-10 form-inline form-advanced-search form-advanced-search-inline">
    <div class="form-group">
        <label>Global Name:</label>
        <select name="filter[fi.feeGlobalName=]" class="form-control">
            <option value="">All</option>
            {% for g in globals %}
                <option value="{{ g.id }}">{{ g.feeCategory.name | default('Unset') }} - {{ g.name }}</option>
            {% endfor %}
        </select>
    </div>

    <div class="form-group">
        <label> </label>
        <a href="javascript:" class="btn btn-primary" onclick="reload()">
            <i class="fa fa-search mr-3"></i> Search
        </a>
        <a href="javascript:" class="btn btn-default ml-3" onclick="clearParameters(false)">
            <i class="fa fa-eraser"></i> Clear
        </a>
    </div>

    {% if editable(Module.ID_FEE_SCHEDULE) %}
    <div class="pull-right">
        <a href="/admin/fee-engine/new" class="btn btn-primary"><i class="fa fa-plus mr-5"></i>Add</a>
    </div>
    {% endif %}
</form>

<script>
  function reload(url) {
    commonSimpleAdvancedLoad(url || $('.form-advanced-search').attr('action'));
  }
</script>

