{% extends 'layout/base-layout.html.twig' %}
{% block page_title %}
    {% if entity %}
        Edit fee item "{{ entity.name }}"
    {% else %}
        Add fee item
    {% endif %}
{% endblock %}


{% block page_content %}
    <form action="/admin/fee-engine/new" method="post" id="main-form">
        {% if entity %}
            <input type="hidden" name="id" value="{{ entity.id }}">
        {% endif %}

        <div class="row">
            <div class="col-sm-6">
                <div class="form-group">
                    <label class="control-label">Tenant Type:</label>
                    <select name="tenant_type" class="form-control" v-model="tenantType">
                        <option v-for="(t, k) in tenantTypes" :key="k" :value="k" v-text="t.name"></option>
                    </select>
                </div>
            </div>
            <div class="col-sm-6">
                <div class="form-group">
                    <label class="control-label">Tenant Name:</label>
                    <select name="tenant" class="form-control" v-model="tenant">
                        <option v-for="t in tenants" :key="t.id" :value="t.id" v-text="t.name"></option>
                    </select>
                </div>
            </div>
        </div>

        <hr class="dark">

        <div class="row">
            <div class="col-sm-6">
                <div class="form-group">
                    <label>Category:</label>
                    <select name="fee_category" class="form-control" v-model="category" required>
                        <option v-for="g in categories" :key="g.id" :value="g.id" v-text="g.name"></option>
                    </select>
                </div>
            </div>
            <div class="col-sm-6">
                <div class="form-group">
                    <label>Global Name:</label>
                    <select name="fee_global_name" class="form-control" v-model="global" required>
                        <option v-for="g in globals" :key="g.id" :value="g.id" v-text="g.name"></option>
                        <option value="new" v-if="category">- Create new -</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="row" v-if="global === 'new'">
            <div class="col-sm-12">
                <div class="form-group">
                    <label>New Global Name:</label>
                    <input type="text" name="fee_global_name_custom" class="form-control"/>
                </div>
            </div>
        </div>

        <hr class="dark">

        <div class="row">
            <div class="col-sm-6">
                <div class="form-group">
                    <label>Fee Original Name:</label>
                    <select name="original_name" class="form-control" v-model="originalName" required>
                        <option v-for="o in filterField(options, 'originName')" :key="o" :value="o"
                                v-text="o"></option>
                        <option value="N/A">N/A</option>
                        <option value="new">- Create new -</option>
                    </select>
                </div>
            </div>
            <div class="col-sm-6" v-if="originalName === 'new'">
                <div class="form-group">
                    <label>New Fee Original Name:</label>
                    <input type="text" name="original_name_custom" v-model="global_name_custom" class="form-control"/>
                </div>
            </div>
        </div>

        <hr class="dark">

        <div class="row">
            <div class="col-sm-6">
                <div class="form-group">
                    <label>Fee TranCode:</label>
                    <select name="tran_code" class="form-control" v-model="tranCode">
                        <option v-for="o in filterField(options, 'tranCode')" :key="o" :value="o"
                                v-text="o"></option>
                        <option value="N/A">N/A</option>
                        <option value="new">- Create new -</option>
                    </select>
                </div>
            </div>
            <div class="col-sm-6">
                <div class="form-group" v-if="tranCode === 'new'">
                    <label>New TranCode:</label>
                    <input type="number" name="tran_code_custom" v-model="tran_code_custom" class="form-control"/>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-sm-6">
                <div class="form-group" v-if="tranCode === 'N/A'">
                    <label>Transaction type:</label>
                    <select name="tran_type" class="form-control" v-model="tranType">
                        <option v-for="o in filterField(options, 'transactionType')" :key="o" :value="o"
                                v-text="o"></option>
                        <option value="N/A">N/A</option>
                    </select>
                </div>
            </div>
            <div class="col-sm-6">
                <div class="form-group" v-if="tranCode === 'N/A' && tranType && tranType !== 'N/A'">
                    <label>Transaction status:</label>
                    <select name="status" class="form-control" v-model="status">
                        <option v-for="o in filterField(options, 'transactionStatus')" :key="o" :value="o"
                                v-text="o"></option>
                        <option value="N/A">N/A</option>
                    </select>
                </div>

                <div class="form-group" v-if="tranCode === 'N/A' && tranType === 'N/A'">
                    <label>Function (API):</label>
                    <select name="api" class="form-control" v-model="api">
                        <option v-for="o in filterField(options, 'function')" :key="o" :value="o"
                                v-text="o"></option>
                        <option value="N/A">N/A</option>
                    </select>
                </div>
            </div>
        </div>

        <hr class="dark">

        <div class="row">
            <div class="col-sm-6">
                <div class="form-group">
                    <label>Way of entry/mapping</label>
                    <select name="way" class="form-control" data-vv="{{ entity ? entity.appliedBy : '' }}">
                        {% for w, name in ways %}
                            <option value="{{ w }}">{{ name }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="col-sm-6">
                <div class="form-group">
                    <label>Hard cost currency</label>
                    <select name="currency" class="form-control" data-vv="{{ entity ? entity.costTernFixedCurrency : 'USD' }}">
                        {% for c in currencies %}
                            <option value="{{ c }}">{{ c }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-sm-6">
                <div class="form-group">
                    <label>Hard cost amount fixed</label>
                    <input type="number" name="fixed" class="form-control" min="0" step="0.01"
                           value="{{ entity ? entity.costTernFixed | money_format_amount(entity.costTernFixedCurrency) : '' }}">
                </div>
            </div>
            <div class="col-sm-6">
                <div class="form-group">
                    <label>Hard cost %</label>
                    <input type="number" name="percent" class="form-control" min="0" step="0.01"
                           value="{{ entity ? entity.costTernRatio : '' }}">
                </div>
            </div>
        </div>

        <div class="mt-5">
            <a href="javascript:" @click="submit" class="btn btn-primary mr-15">Submit</a>
            <a href="/admin/fee-engine" class="btn btn-default">Cancel</a>
        </div>
    </form>
{% endblock %}

{% block javascripts_inline %}
    <script>
      var app = new Vue({
        el: '#main-form',
        data: function () {
          return {
            tenantTypes: {{ tenantTypes | json_encode | raw }},
            allCategories: {{ categories | json_encode | raw }},
            allGlobals: {{ globals | json_encode | raw }},
            tenantType: '{{ entity.tenantType | default }}',
            tenant: {{ entity.tenant.id | default('null') }},
            category: {{ entity.feeGlobalName.feeCategory.id | default('null') }},
            global: {{ globals ? (entity ? entity.feeGlobalName.id : globals[0].id) : 'null' }},
            options: [],
            originalName: {{ entity.name | default(null) | json_encode | raw }},
            tranCode: {{ entity.tranCode | default(null) | json_encode | raw }},
            tranType: {{ entity.transactionType | default(null) | json_encode | raw }},
            api: {{ entity.function | default(null) | json_encode | raw }},
            global_name_custom: '',
            tran_code_custom: ''
          }
        },
        watch: {
          tenants: function () {
            const f = _.find(this.tenants, {id: this.tenant})
            if (!f) {
              this.tenant = this.tenants.length ? this.tenants[0].id : null
            }
          },
          tenant: {
            handler: function () {
              this.loadOptions()
            },
            immediate: true
          },
          tenantType: function () {
            const f = _.find(this.allCategories, {id: this.category})
            if (f) {
              if (_.includes(f.tenantTypes, this.tenantType)) {
                return
              }
            }
            this.category = null
          }
        },
        computed: {
          tenants: function () {
            if (!this.tenantType) {
              return [];
            }
            var type = this.tenantTypes[this.tenantType];
            if (!type || !type.items) {
              return [];
            }
            return type.items;
          },
          categories: function () {
            if (!this.tenantType) {
              return [];
            }
            return _.filter(this.allCategories, function (category) {
              return _.includes(category.tenantTypes, this.tenantType);
            }.bind(this));
          },
          globals: function () {
            if (!this.category) {
              return [];
            }
            return _.filter(this.allGlobals, {category: this.category});
          }
        },
        methods: {
          filterField: function (data, field) {
            return _.uniq(_.filter(_.map(data, function (d) {
              return d[field];
            }), function (d) {
              return d && d !== '' && d !== 'N/A';
            }));
          },
          loadOptions () {
            $.get('/admin/tenants/' + this.tenant + '/fee-options/data', function (resp) {
              if (resp.success) {
                this.options = resp.data
              }
            }.bind(this));
          },
          submit () {
            if (!this.category || !this.global || !this.originalName) {
              return ts.error('Please input fee category, global name and original name!');
            }

            if (this.global === 'new' && !this.global_name_custom) {
              return ts.error('Please input fee global name!');
            }
            if (this.tranCode === 'new' && !this.tran_code_custom) {
              return ts.error('Please input tran code!');
            }

            ts.modal();
            $('#main-form')[0].submit();
          }
        },
        mounted: function () {
          if (!this.tenantType) {
            this.tenantType = Object.keys(this.tenantTypes)[0] || null;
          }
        }
      });
    </script>
{% endblock %}