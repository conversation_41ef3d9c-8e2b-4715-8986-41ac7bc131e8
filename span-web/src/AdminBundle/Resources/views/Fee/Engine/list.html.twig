<table class="table table-striped table-bordered table-hover mt-5" id="main-table">
    <thead>
    <tr>
        <th>Tenant Type</th>
        <th>Tenant Name</th>
        <th>Global Name</th>
        <th>Fee Original Name</th>
        <th>Fee TranCode</th>
        <th>Transaction Type</th>
        <th>Way of entry/mapping</th>
        <th>Hard cost currency</th>
        <th>Hard cost amount fixed</th>
        <th>Hard cost %</th>
        <th>{{ 'column.action'|trans }}</th>
    </tr>
    </thead>
    <tbody>
    {% for item in items %}
        <tr>
            <td>{{ item.getFeeEntityType()}}</td>
            <td>{{ item.entity.name | default }}</td>
            <td>{{ item.getFeeGlobalName().getName() | default }}</td>
            <td>{{ item.getFeeName()}}</td>
            <td>{{ item.tranCode }}</td>
            <td>
                {% if item.transactionType and item.transactionType != 'N/A' %}
                    {{ item.transactionType }}
                {% else %}
                    {{ item.function }}
                {% endif %}
            </td>
            <td>{{ item.appliedBy | humanize }}</td>
            <td>{{ item.costTernFixedCurrency }}</td>
            <td>{{ item.getCostTernFixed() | money_format_amount(item.getCostTernFixedCurrency())}}</td>
            <td>{{ item.getCostTernRatio()}}</td>
            <td>
                {% if editable(Module.ID_FEE_SCHEDULE) %}
                <div class="btn-group">
                    <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">
                        {{ 'btn.actions'|trans }} <span class="caret"></span>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-right">
                        <li><a href="/admin/fee-engine/{{ item.id }}/edit">Edit</a></li>
                        <li><a href="javascript:" onclick="deleteItem({{ item.id }})">Delete</a></li>
                    </ul>
                </div>
                {% endif %}
            </td>
        </tr>
    {% endfor %}
    </tbody>
</table>
<div class="opera-right span3 pull-right" id="pageNumber">
    {{ knp_pagination_render(items) }}
</div>
