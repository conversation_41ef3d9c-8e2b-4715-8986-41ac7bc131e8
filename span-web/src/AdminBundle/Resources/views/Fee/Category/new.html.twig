{% extends 'layout/base-layout.html.twig' %}
{% block page_title %} {{ 'feecategory.title.new'|trans }} {% endblock %}


{% block page_content %}
    <div class="row-fluid">
        <div>
            <h2>{{ 'feecategory.header.new'|trans }}</h2>
        </div>
    </div>
    <!--/row-->
    <div class="row-fluid">
        <div>
            <form action="{{ path('admin_fee_category_new') }}" method="post"  >
                {{ form_widget(form) }}

                <div class="form-group">
                    <label>Tenant types</label>
                    <select name="tenantTypes[]" class="select2" multiple>
                        {% for t in tenantTypes %}
                            <option value="{{ t }}">{{ t | humanize }}</option>
                        {% endfor %}
                    </select>
                </div>

                <button type="submit" class="btn btn-primary">{{ 'btn.save'|trans }}</button>
                <button type="button" id="btnCancel" class="btn ml-10"
                        onclick="this.form.action='{{ path('admin_fee_category') }}'this.form.submit()">
                    {{ 'btn.cancel'|trans }}</button>
            </form>
        </div>
    </div>
    <!--/row-->
{% endblock %}