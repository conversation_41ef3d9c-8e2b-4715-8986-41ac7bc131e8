{% extends 'layout/base-layout.html.twig' %}
{% block page_title %} {{ 'feecategory.title.view'|trans }} {% endblock %}


{% block page_content %}
    <!--/row-->
    <div class="row-fluid opera-box">
        {% for flashMessage in app.session.flashbag.get('success') %}
            <div class="alert alert-success">
                <button type="button" class="close" data-dismiss="alert">&times;</button>
                {{ flashMessage }}
            </div>
        {% endfor %}
        {% for flashMessage in app.session.flashbag.get('failure') %}
            {% if flashMessage %}
                <div class="alert alert-error">
                    <button type="button" class="close" data-dismiss="alert">&times;</button>
                    {{ flashMessage }}
                </div>
            {% endif %}
        {% endfor %}

        {% if editable(Module.ID_FEE_CATEGORY) %}
            <div class="opera-right span3 pull-right">
                <button type="button" class="btn btn-success" id="new_fee_category"
                        onclick="window.location.href='{{ path('admin_fee_category_new') }}'">
                    {{ 'feecategory.btn.create'|trans }}</button>
            </div>
        {% endif %}
    </div>

    <div class="row-fluid">
        <table class="table table-striped table-bordered table-hover" id="feeCategoryTable">
            <thead>
            <tr>
                <th>{{ 'feecategory.column.name'|trans }}</th>
                <th>Tenant Types</th>
                <th width="100">{{ 'column.action'|trans }}</th>
            </tr>
            </thead>
            <tbody>
            {% for item in feeCategories %}
                <tr>
                    <td>{{ item.getName() }}</td>
                    <td>{{ item.tenantTypesArray | join(', ') | humanize }}</td>
                    <td>
                        {% if editable(Module.ID_FEE_CATEGORY) %}
                            <button class="btn btn-sm btn-default"
                                    onclick="window.location.href='{{ path('admin_fee_category_modify', {'fee_category_id':item.getId ()}) }}';">
                                {{ 'btn.modify'|trans }}
                            </button>
                            <a class="btn btn-sm btn-danger ml-5" href="#" data-toggle="modal" data-target="#deleteFeeCategoryConfirmModal">{{ 'btn.delete'|trans }}</a>
                            <form action="{{ path('admin_fee_category_delete',{'fee_category_id':item.getId() }) }}" method="post"></form>
                        {% endif %}
                    </td>
                </tr>
            {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteFeeCategoryConfirmModal" tabindex="-1" role="dialog" aria-labelledby="deleteFeeCategoryConfirmModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="deleteFeeCategoryConfirmModalLabel">{{ 'feecategory.label.delete'|trans }}</h4>
                </div>
                <div class="modal-body">
                    {{ 'feecategory.msg.delete'|trans }}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">{{ 'btn.cancel'|trans }}</button>
                    <button type="button" class="btn btn-danger confirm-button">{{ 'btn.confirm'|trans }}</button>
                </div>
            </div>
        </div>
    </div>

    <!--/row-->
{% endblock %}

{% block javascripts_inline %}
    <script>
        // triggered when delete confirm modal is about to be shown
        $("#deleteFeeCategoryConfirmModal").on("show.bs.modal", function(e) {
            // Pass form reference to modal
            var form = $(e.relatedTarget).siblings('form');
            $(this).find(".confirm-button").data('form', form);
        });

        // action on confirm
        $("#deleteFeeCategoryConfirmModal .confirm-button").on("click", function(){
            $(this).data('form').submit();
        });

        //Add on 2017-02-23 replace with jquery data table
        $("#feeCategoryTable").DataTable({
            "lengthMenu": [[20, 50, 100, -1], [20, 50, 100, "All"]],
            searching: false,
            "columnDefs": [ {
                "targets": [1],
                "orderable": false,
                "searchable": false
            }]
        });
    </script>
{% endblock %}