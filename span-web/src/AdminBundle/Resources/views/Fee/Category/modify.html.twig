{% extends 'layout/base-layout.html.twig' %}
{% block page_title %} {{ 'feecategory.title.modify'|trans }} {% endblock %}

{% block page_content %}
    <div class="row-fluid">
        <div>
            <h2>{{ 'feecategory.header.modify'|trans }}</h2>
        </div>
    </div>

    <div class="row-fluid">
        <div>
            <form action="{{ path('admin_fee_category_modify', {'fee_category_id': fee_category_id}) }}" method="post"  >
                {{ form_widget(form) }}

                {% set saved = entity.tenantTypesArray %}
                <div class="form-group">
                    <label>Tenant types</label>
                    <select name="tenantTypes[]" class="select2" multiple>
                        {% for t in tenantTypes %}
                            <option value="{{ t }}"
                                {% if t in saved %}
                                    selected
                                {% endif %}
                            >{{ t | humanize }}</option>
                        {% endfor %}
                    </select>
                </div>

                <button type="submit" class="btn btn-primary">{{ 'btn.save'|trans }}</button>
            </form>
        </div>
    </div>
{% endblock %}