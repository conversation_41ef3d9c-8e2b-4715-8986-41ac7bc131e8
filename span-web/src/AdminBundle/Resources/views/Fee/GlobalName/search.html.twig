<form action="/admin/fee-global-name/list" method="post" class="mt-10 form-inline form-advanced-search form-advanced-search-inline">
    <div class="form-group">
        <label>Global Name:</label>
        <input type="text" class="form-control" name="filter[g.name]" placeholder="fuzzy match" data-default="">
    </div>

    <div class="form-group">
        <label>Category</label>
        <select name="filter[g.feeCategory=]" class="form-control">
            <option value="">All</option>
            {% for g in categories %}
                <option value="{{ g.id }}">{{ g.name | humanize }}</option>
            {% endfor %}
        </select>
    </div>

    <div class="form-group">
        <label>Type</label>
        <select name="filter[g.type]" class="form-control">
            <option value="">All</option>
            {% for g in types %}
                <option value="{{ g }}">{{ g | humanize }}</option>
            {% endfor %}
        </select>
    </div>

    <div class="form-group">
        <label> </label>
        <a href="javascript:" class="btn btn-primary" onclick="reload()">
            <i class="fa fa-search mr-3"></i> Search
        </a>
        <a href="javascript:" class="btn btn-default ml-3" onclick="clearParameters(false)">
            <i class="fa fa-eraser"></i> Clear
        </a>
    </div>

    {% if editable(Module.ID_FEE_GLOBAL_NAME) %}
        <div class="opera-right span3 pull-right">
            <button type="button" class="btn btn-success" id="new_fee_global_name"
                    onclick="window.location.href='{{ path('admin_fee_global_name_new') }}';">
                {{ 'feeglobalname.btn.create'|trans }}</button>
        </div>
    {% endif %}
</form>

<script>
  function reload(url) {
    commonSimpleAdvancedLoad(url || $('.form-advanced-search').attr('action'));
  }
</script>

