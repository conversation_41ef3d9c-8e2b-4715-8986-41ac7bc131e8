{% extends 'layout/base-layout.html.twig' %}
{% block page_title %} KYC Provider Management {% endblock %}

{% block page_content %}
    <div class="row-fluid opera-box">
        {% include "@Admin/Affiliate/message.html.twig" %}

        {% if editable(Module.ID_KYC_PROVIDER_MANAGEMENT) %}
        <div class="opera-right span3 pull-right">
            <button type="button" class="btn btn-success" id="new_kyc_provider"
                    onclick="window.location.href='{{ path('admin_kyc_provider_new') }}';">Create KYC Provider</button>
        </div>
        {% endif %}
    </div>

    <div class="row-fluid">
        <table class="table table-striped table-bordered table-hover" id="kycProvider">
            <thead>
            <tr>
                <th class="col-md-2">Name</th>
                <th class="col-md-2">Integration Provider Id</th>
                <th class="col-md-2">Contact Person</th>
                <th class="col-md-2">Email</th>
                <th class="col-md-2">Phone</th>
                <th class="col-md-2">Actions</th>
            </tr>
            </thead>
            <tbody>
            {% for item in kycProviders %}
                <tr>
                    <td>{{ item.getName() }}</td>
                    <td>{{ item.getIntegrationProviderId() }}</td>
                    <td>{{ item.getContact().getTitle()~ ' '~item.getContact().getFirstName()~' '~ item.getContact().getLastName() }}</td>
                    <td>{{ item.getContact().getEmail() }}</td>
                    <td>{{ item.getContact().getPhone() }}</td>
                    <td>
                        <div class="btn-group">
                            <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">
                                Actions <span class="caret"></span>
                            </button>
                            <ul class="dropdown-menu">
                                {% if editable(Module.ID_KYC_PROVIDER_MANAGEMENT) %}
                                    <li><a href="{{ path('admin_kyc_provider_modify', {'id':item.getId()}) }}">Edit</a></li>
                                    <li>
                                        <a href="#" data-toggle="modal" data-target="#deleteKycProviderConfirmModal">Delete</a>
                                        <form action="{{ path('admin_kyc_provider_delete', {'id':item.getId()}) }}" method="post"></form>
                                    </li>
                                    <li><a href="{{ path('admin_affiliate_set', {'tenant':item.getId(),'return_url':'admin_kyc_provider', 'token':date()}) }}">{{ 'btn.setaffiliate'|trans }}</a></li>
                                {% endif %}
                                <li><a href="/admin/tenants/contacts/{{ item.id }}" target="_blank">Contacts</a></li>
                                <li><a href="/admin/tenants/{{ item.id }}/fee-options" target="_blank">Fee options</a></li>
                                <li><a href="{{ path('admin_fee_item', {'entity_id':item.getId(), 'entity_type':'KycProvider'}) }}">{{ 'btn.feeitem'|trans }}</a></li>
                            </ul>
                        </div>
                    </td>
                </tr>
            {% endfor %}
            </tbody>
            Show
            <div class="btn-group">
                <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    Page <span class="caret"></span>
                </button>
                <ul class="dropdown-menu">
                    <li><a href="{{ path('admin_kyc_provider',{'limit':5}) }}">default</a></li>
                    <li><a href="{{ path('admin_kyc_provider',{'limit':10}) }}">10</a></li>
                    <li><a href="{{ path('admin_kyc_provider',{'limit':20 }) }}">20</a></li>
                    <li><a href="{{ path('admin_kyc_provider',{'limit':50 }) }}">50</a></li>
                </ul>
            </div>
            entries
        </table>
        <div class="opera-right span3 pull-right">
            {{ knp_pagination_render(kycProviders) }}
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteKycProviderConfirmModal" tabindex="-1" role="dialog" aria-labelledby="deleteKycProviderConfirmModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="deleteKycProviderConfirmModalLabel">Delete KYC Provider</h4>
                </div>
                <div class="modal-body">
                    Do you want to delete this KYC Provider?
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger confirm-button">Confirm</button>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts_inline %}
    <script>
        // triggered when delete confirm modal is about to be shown
        $("#deleteKycProviderConfirmModal").on("show.bs.modal", function(e) {
            // Pass form reference to modal
            var form = $(e.relatedTarget).siblings('form');
            $(this).find(".confirm-button").data('form', form);
        });

        // action on confirm
        $("#deleteKycProviderConfirmModal .confirm-button").on("click", function(){
            $(this).data('form').submit();
        });

        $("#kycProvider").DataTable({
            "paging": false,
            "lengthChange": false,
            "searching": false,
            "ordering": true,
            "info": true,
            "autoWidth": false
//            "lengthMenu": [[20, 50, 100, -1], [20, 50, 100, "All"]],
//            "columnDefs": [ {
//                "targets": [4],
//                "orderable": false,
//                "searchable": false
//            }]
        });
    </script>
{% endblock %}