{% extends 'layout/base-layout.html.twig' %}
{% block page_title %} {{ 'service.title.view'|trans }} {% endblock %}


{% block page_content %}
    <!--/row-->
    <div class="row-fluid opera-box">
        {% for flashMessage in app.session.flashbag.get('success') %}
            <div class="alert alert-success">
                <button type="button" class="close" data-dismiss="alert">&times;</button>
                {{ flashMessage }}
            </div>
        {% endfor %}
        {% for flashMessage in app.session.flashbag.get('failure') %}
            {% if flashMessage %}
                <div class="alert alert-error">
                    <button type="button" class="close" data-dismiss="alert">&times;</button>
                    {{ flashMessage }}
                </div>
            {% endif %}
        {% endfor %}
    </div>

    <div class="row-fluid">
        <table class="table table-striped table-bordered table-hover" id="serviceTable">
            <thead>
            <tr>
                <th>{{ 'service.column.name'|trans }}</th>
                <th width="100">{{ 'column.action'|trans }}</th>
            </tr>
            </thead>
            <tbody>
            {% for item in services %}
                <tr>
                    <td>{{ item.getName() }}</td>
                    <td>
                        {#Changed on 2017-02-28 with button group#}
                        <div class="btn-group">
                            <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">
                                {{ 'btn.actions'|trans }} <span class="caret"></span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-right">
                                {% if editable(Module.ID_SERVICE_MANAGEMENT) %}
                                    <li><a href="{{ path('admin_fee_item', {'entity_id':item.getId(), 'entity_type':'Service'}) }}">{{ 'btn.feeitem'|trans }}</a></li>
                                {% endif %}
                                <li><a href="/admin/tenants/contacts/{{ item.id }}" target="_blank">Contacts</a></li>
                                <li><a href="/admin/tenants/{{ item.id }}/fee-options" target="_blank">Fee options</a></li>
                            </ul>
                        </div>
                    </td>
                </tr>
            {% endfor %}
            </tbody>
            Show
            <div class="btn-group">
                <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    Page <span class="caret"></span>
                </button>
                <ul class="dropdown-menu">
                    <li><a href="{{ path('admin_service',{'limit':5}) }}">default</a></li>
                    <li><a href="{{ path('admin_service',{'limit':10}) }}">10</a></li>
                    <li><a href="{{ path('admin_service',{'limit':20 }) }}">20</a></li>
                    <li><a href="{{ path('admin_service',{'limit':50 }) }}">50</a></li>
                </ul>
            </div>
            entries
        </table>
        <div class="opera-right span3 pull-right">
            {{ knp_pagination_render(services) }}
        </div>
    </div>
    <!--/row-->
{% endblock %}

{% block javascripts_inline %}
    <script>

        $("#serviceTable").DataTable({
            "paging": false,
            "lengthChange": false,
            "searching": false,
            "ordering": true,
            "info": true,
            "autoWidth": false
//            "lengthMenu": [[20, 50, 100, -1], [20, 50, 100, "All"]],
//            "columnDefs": [ {
//                "targets": [1],
//                "orderable": false,
//                "searchable": false
//            }]
        });
    </script>
{% endblock %}