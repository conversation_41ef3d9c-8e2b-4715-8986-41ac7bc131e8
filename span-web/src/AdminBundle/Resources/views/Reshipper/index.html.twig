{% extends 'layout/base-layout.html.twig' %}
{% block page_title %} Reshipper Management {% endblock %}

{% block page_content %}
    <div class="row-fluid opera-box">
        {% include "@Admin/Affiliate/message.html.twig" %}

        {% if editable(Module.ID_RESHIPPER_MANAGEMENT) %}
        <div class="opera-right span3 pull-right" style="margin-bottom:1rem">
            <button type="button" class="btn btn-primary" id="new_reshipper"
                    onclick="window.location.href='{{ path('admin_reshipper_new') }}';">{{ 'reshipper.btn.create'|trans }}</button>
        </div>
        {% endif %}
    </div>

    <div class="row-fluid">
        <table class="table table-striped table-bordered table-hover" id="loadPartner">
            <thead>
            <tr>
                <th>Name</th>
                <th>Dba</th>
                <th>Address</th>
                <th>Auto Sign Up</th>
                <th>Account Validation</th>
                <th>Contact</th>

                {% if usu() %}
                    <th>Enabled in USU</th>
                {% endif %}

                <th>Action</th>
            </tr>
            </thead>
            <tbody>
            {% for item in reshippers %}
                <tr>
                    <td>{{ item.getName() }}</td>
                    <td>{{ item.getDba() }}</td>
                    <td>{{ item.getAddress() }}</td>
                    <td>{{ item.getAutoSignUp() | bool }}</td>
                    <td>{{ item.getAccountValidation() | bool }}</td>
                    <td>
                        {% set contact = item.contact %}
                        {% if contact %}
                            {{ contact.getTitle() ~ ' '~contact.getFirstName()~' '~ contact.getLastName() }}<br>
                            {{ contact.getEmail() | default }}<br>
                            {{ contact.getPhone() | default }}
                        {% endif %}
                    </td>

                    {% if usu() %}
                        <td>
                            {% if editable(Module.ID_RESHIPPER_MANAGEMENT) %}
                                <input type="checkbox" class="usu_switches" data-id="{{ item.id }}" {% if item.isEnabledInUSU %}checked{% endif %}>
                            {% else %}
                                {{ item.isEnabledInUSU ? 'Yes' : 'No' }}
                            {% endif %}
                        </td>
                    {% endif %}

                    <td>
                        <div class="btn-group">
                            <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">
                                Actions <span class="caret"></span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-right">
                                {% if editable(Module.ID_RESHIPPER_MANAGEMENT) %}
                                    <li><a href="{{ path('admin_reshipper_modify', {'reshipper_id':item.getId()}) }}">Edit</a></li>
                                    <li>
                                        <a href="#" data-toggle="modal" data-target="#deleteLoadPartnerConfirmModal">Delete</a>
                                        <form action="{{ path('admin_reshipper_delete', {'reshipper_id':item.getId()}) }}" method="post"></form>
                                    </li>
                                    <li><a href="{{ path('admin_affiliate_set', {'tenant':item.getId(),'return_url':'admin_reshipper', 'token':date()}) }}">{{ 'btn.setaffiliate'|trans }}</a></li>
                                {% endif %}
                                <li><a href="/admin/tenants/contacts/{{ item.id }}" target="_blank">Contacts</a></li>
                                <li><a href="/admin/tenants/{{ item.id }}/fee-options" target="_blank">Fee options</a></li>
                                <li><a href="{{ path('admin_reshipper_address', {'reshipper_id':item.getId()}) }}">Billing Address</a></li>
                            </ul>
                        </div>
                    </td>
                </tr>
            {% endfor %}
            </tbody>
            Show
            <div class="btn-group">
                <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    Page <span class="caret"></span>
                </button>
                <ul class="dropdown-menu">
                    <li><a href="{{ path('admin_reshipper',{'limit':5}) }}">default</a></li>
                    <li><a href="{{ path('admin_reshipper',{'limit':10}) }}">10</a></li>
                    <li><a href="{{ path('admin_reshipper',{'limit':20 }) }}">20</a></li>
                    <li><a href="{{ path('admin_reshipper',{'limit':50 }) }}">50</a></li>
                </ul>
            </div>
            entries
        </table>
        <div class="opera-right span3 pull-right">
            {{ knp_pagination_render(reshippers) }}
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteLoadPartnerConfirmModal" tabindex="-1" role="dialog" aria-labelledby="deleteLoadPartnerConfirmModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="deleteLoadPartnerConfirmModalLabel">Delete Reshipper</h4>
                </div>
                <div class="modal-body">
                    Do you want to delete this Reshipper?
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger confirm-button">Confirm</button>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts_inline %}
    <script>
        // triggered when delete confirm modal is about to be shown
        $("#deleteLoadPartnerConfirmModal").on("show.bs.modal", function(e) {
            // Pass form reference to modal
            var form = $(e.relatedTarget).siblings('form');
            $(this).find(".confirm-button").data('form', form);
        });

        // action on confirm
        $("#deleteLoadPartnerConfirmModal .confirm-button").on("click", function(){
            $(this).data('form').submit();
        });

        $("#loadPartner").DataTable({
            "paging": false,
            "lengthChange": false,
            "searching": false,
            "ordering": true,
            "info": true,
            "autoWidth": false
//            "lengthMenu": [[20, 50, 100, -1], [20, 50, 100, "All"]],
//            "columnDefs": [ {
//                "targets": [4],
//                "orderable": false,
//                "searchable": false
//            }]
        });

        {% if usu() %}
            $('.usu_switches').bootstrapSwitch({
              size: 'mini',
              onSwitchChange: function (event, state) {
                ts.modal();
                $.post('/admin/reshipper/toggle-in-usu', {
                  state: state,
                  id: $(this).data('id')
                }, function () {
                  ts.msg('Saved!');
                });
              }
            });
        {% endif %}
    </script>
{% endblock %}
