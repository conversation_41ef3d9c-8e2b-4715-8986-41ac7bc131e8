<table class="table table-striped table-bordered table-hover" id="main-table" style="margin-top: 4px" >
    <thead>
    <tr>
        <th>ID</th>
        {% if not tenant %}
            <th>Tenant type</th>
            <th>Tenant name</th>
        {% endif %}
        <th>Fee origin name</th>
        <th>Tran code</th>
        <th>Function</th>
        <th>Transaction type</th>
        <th>Transaction status</th>
        <th>Action</th>
    </tr>
    </thead>
    {% for item in list %}
        <tr>
            <td>{{ item.id }}</td>
            {% if not tenant %}
                <td>{{ item.tenant.type | humanize }}</td>
                <td>{{ item.tenant.name }}</td>
            {% endif %}
            <td>{{ item.originName }}</td>
            <td>{{ item.tranCode }}</td>
            <td>{{ item.function }}</td>
            <td>{{ item.transactionType }}</td>
            <td>{{ item.transactionStatus }}</td>
            <td>
                {% if editable(Module.ID_TENANT_MANAGEMENT) %}
                <div class="btn-group">
                    <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">
                        {{ 'btn.actions'|trans }} <span class="caret"></span>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-right">
                        <li>
                            <a href="/admin/tenants/{{ item.tenant.id }}/fee-options/{{ item.id }}/edit">Edit</a>
                        </li>
                        <li>
                            <a href="javascript:"
                               onclick="commonDeleteItem('/admin/tenants/{{ item.tenant.id }}/fee-options/{{ item.id }}/delete')">
                                Delete</a>
                        </li>
                    </ul>
                </div>
                {% endif %}
            </td>
        </tr>
    {% endfor %}
</table>
<div class="opera-right span3 pull-right" id="pageNumber">
    {{ knp_pagination_render(list) }}
</div>