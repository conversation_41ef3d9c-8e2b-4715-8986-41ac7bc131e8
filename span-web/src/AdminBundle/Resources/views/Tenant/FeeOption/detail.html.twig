{% extends 'layout/base-layout.html.twig' %}
{% block page_title %}
    {% if entity %}
        Edit Fee Option of Tenant "{{ tenant.type | humanize }} - {{ tenant.name }}"
    {% else %}
        Add Fee Option for Tenant "{{ tenant.type | humanize }} - {{ tenant.name }}"
    {% endif %}
{% endblock %}


{% block page_content %}

    <form action="/admin/tenants/{{ tenant.id }}/fee-options/save" method="post" id="main-form">
        {% if entity %}
            <input type="hidden" name="id" value="{{ entity.id }}">
        {% endif %}

        <div class="form-group">
            <label class="control-label">Fee origin name:</label>
            <input type="text" name="originName" class="form-control" value="{{ entity.originName | default }}"/>
        </div>

        <div class="form-group">
            <label class="control-label">Tran code:</label>
            <input type="text" name="tranCode" class="form-control" value="{{ entity.tranCode | default }}"/>
        </div>

        <div class="form-group">
            <label class="control-label">Function:</label>
            <input type="text" name="function" class="form-control" value="{{ entity.function | default }}"/>
        </div>

        <div class="form-group">
            <label class="control-label">Transaction type:</label>
            <input type="text" name="transactionType" class="form-control" value="{{ entity.transactionType | default }}"/>
        </div>

        <div class="form-group">
            <label class="control-label">Transaction status:</label>
            <input type="text" name="transactionStatus" class="form-control" value="{{ entity.transactionStatus | default }}"/>
        </div>

        <div class="mt-5">
            <input type="submit" class="btn btn-primary mr-15" value="Submit">
            <a href="javascript:" onclick="history.go(-1);" class="btn btn-default">Cancel</a>
        </div>

    </form>

{% endblock %}

{% block javascripts_inline %}
    <script>
    </script>
{% endblock %}