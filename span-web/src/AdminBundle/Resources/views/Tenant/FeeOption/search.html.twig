<form action="" class="form-inline mt-10 form-advanced-search form-advanced-search-inline" method="post">
    <div class="form-group">
        <label>Keyword: </label>
        <input type="text" class="form-control" name="keyword">
    </div>

    <div class="form-group">
        <label></label>
        <a href="javascript:" class="btn btn-primary" onclick="reload()">
            <i class="fa fa-search mr-3"></i> Search
        </a>
        <a href="javascript:" class="ml-3" onclick="clearParameters(false)">
            <i class="fa fa-eraser"></i>
        </a>
    </div>

    {% if editable(Module.ID_TENANT_MANAGEMENT) %}
        <div class="pull-right">
            {% if tenant %}
                <a href="/admin/tenants/{{ tenant.id }}/fee-options/new" class="btn btn-primary">
                    <i class="fa fa-plus mr-5"></i>Add</a>
            {% else %}
                <select class="form-control mr-5" id="tenant_item">
                    {% for type, ts in tenants %}
                        <optgroup label="{{ type | humanize }}">
                            {% for t in ts %}
                                <option value="{{ t.id }}">{{ t.name }}</option>
                            {% endfor %}
                        </optgroup>
                    {% endfor %}
                </select>
                <a href="javascript:" class="btn btn-primary" onclick="add()">
                    <i class="fa fa-plus mr-5"></i>Add</a>
            {% endif %}
        </div>
    {% endif %}
</form>

<script>
    function reload (url) {
        commonAdvancedLoad(url, '/admin/tenants/{{ tenant ? tenant.id : '0' }}/fee-options/list');
    }

    function add () {
      location.href = '/admin/tenants/' + $('#tenant_item').val() + '/fee-options/new';
    }
</script>