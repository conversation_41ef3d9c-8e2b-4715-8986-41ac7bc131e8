{% extends 'layout/base-layout.html.twig' %}
{% block page_title %} {{ 'cardprogram.title.view'|trans }} {% endblock %}


{% block page_content %}
    <!--/row-->
    <div class="row-fluid opera-box">
        {% for flashMessage in app.session.flashbag.get('success') %}
            <div class="alert alert-success">
                <button type="button" class="close" data-dismiss="alert">&times;</button>
                {{ flashMessage }}
            </div>
        {% endfor %}
        {% for flashMessage in app.session.flashbag.get('failure') %}
            {% if flashMessage %}
                <div class="alert alert-error">
                    <button type="button" class="close" data-dismiss="alert">&times;</button>
                    {{ flashMessage }}
                </div>
            {% endif %}
        {% endfor %}

        {% if accessible(Module.ID_SET_UP_WIZARD) %}
            <div class="opera-right span3 pull-right mb-10">
                <button type="button" class="btn btn-success"
                        onclick="window.location.href='{{ path('admin_card_program_wizard') }}';">Create New Program</button>
            </div>
        {% endif %}
    </div>

    <div class="row-fluid">
        <table class="table table-striped table-bordered table-hover" id="cardProgramTable">
            <thead>
            <tr>
                <th>ID</th>
                <th>{{ 'cardprogram.column.name'|trans }}</th>
                <th>Platform</th>
                <th>Program Owner</th>
                <th>Processor</th>
                <th>Issuing Bank</th>
                <th>KYC Provider (Program ID)</th>
                <th>BIN</th>
                <th>Package Id</th>
                <th>Delivery Method</th>
                <th>{{ 'cardprogram.column.status'|trans }}</th>
                <th>{{ 'column.action'|trans }}</th>
            </tr>
            </thead>
            <tbody>
            {% for item in cardPrograms %}
                <tr>
                    <td>{{ item.id }}</td>
                    <td>{{ item.getName() }}</td>
                    <td>{{ item.platform.name | default }}</td>
                    <td>{{ item.brandPartner.name | default }}</td>
                    <td>{{ item.processor.name | default }}</td>
                    <td>{{ item.issuingBank.name | default }}</td>
                    <td>
                        {{ item.kycProvider.name | default }}
                        {% if item.kycProvider is not null and item.kycProviderProgram is not null %}
                            ({{ item.kycProviderProgram }})
                        {% endif %}
                    </td>
                    <td>{{ item.bin }}</td>
                    <td>{{ item.packageId }}</td>
                    <td>{{ item.deliveryMethodName }}</td>
                    <td>
                        {% if item.getStatus() == 'Test Mode' %}
                            <p style="color: #4a41ff">{{ item.getStatus }}</p>
                        {% elseif item.getStatus() == 'Paused' %}
                            <p style="color: orange">{{ item.getStatus }}</p>
                        {% elseif item.getStatus() == 'Suspended' %}
                            <p style="color: red" data-toggle="tooltip" title="API/GUI access is disabled">{{ item.getStatus }}</p>
                        {% elseif item.getStatus() == 'Live' %}
                            <p style="color: green; font-weight: bold">{{ item.getStatus }}</p>
                        {% endif %}
                    </td>
                    <td>
                        <div class="btn-group">
                            <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">
                                {{ 'btn.actions'|trans }} <span class="caret"></span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-right">
                                {% if editable(Module.ID_CARD_PROGRAM_LIST) %}
                                    <li><a href="{{ path('admin_card_program_modify',{'cardprogram_id':item.getId() }) }}">Edit</a></li>
                                    <li>
                                        <a href="javascript:"
                                           onclick="postMdFrameMessage('route', '/a/card-program/load-methods?cp={{ item.getId() }}')">
                                            Load Methods
                                        </a>
                                    </li>

                                    {% if not esSolo() %}
                                        <li><a href="/admin/card-program/{{ item.id }}/fee-items">Fee items</a></li>
                                    {% endif %}

                                    {% if item.status != 'Suspended' or user.isSuperAdmin() %}
                                        <li>
                                            <a href="#" data-id="{{ item.getId() }}" data-toggle="modal"
                                               data-target="#statusConfirmModal">{{ 'cardprogram.btn.setstatus'|trans }}</a>
                                        </li>
                                    {% endif %}
                                {% else %}
                                    <li><a href="{{ path('admin_card_program_modify',{'cardprogram_id':item.getId() }) }}">View</a></li>

                                    {% if not esSolo() %}
                                        <li><a href="/admin/card-program/{{ item.id }}/fee-items">Fee items</a></li>
                                    {% endif %}
                                {% endif %}

                                {% if util('isStaging', [true]) %}
                                    <div class="divider"></div>
                                    <li>
                                        <a href="javascript:"
                                           onclick="openInRootWindow('/admin/card-program/{{ item.id }}/switch-to')">Switch to</a>
                                    </li>
                                {% endif %}
                            </ul>
                        </div>
                    </td>
                </tr>
            {% endfor %}
            </tbody>
        </table>
    </div>

    <!--/row-->

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteCPConfirmModal" tabindex="-1" role="dialog" aria-labelledby="deleteCPConfirmModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="deleteCPConfirmModalLabel">Delete Card Program</h4>
                </div>
                <div class="modal-body">
                    Do you want to delete this Card Program?
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger confirm-button">Confirm</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Confirmation Modal -->
    <div class="modal fade" id="statusConfirmModal" tabindex="-1" role="dialog" aria-labelledby="statusConfirmModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="statusConfirmModalLabel">{{ 'cardprogram.label.status'|trans }}</h4>
                </div>
                <div class="modal-body">
                    <form action="{{ path('admin_card_program_status') }}" method="post" id="card_program_status">
                        <input type="hidden" name="targetId" id="targetId"/>
                        <select name="status" id="status" class="form-control">
                            <option value="Test Mode">Test Mode</option>
                            <option value="Paused">Paused</option>

                            {% if user.isSuperAdmin %}
                                <option value="Suspended">Suspended (Disable API/GUI access)</option>
                            {% endif %}

                            <option value="Live">Live</option>
                        </select>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">{{ 'btn.cancel'|trans }}</button>
                    <button id="statusConfirm" type="button" class="btn btn-danger confirm-button">{{ 'btn.confirm'|trans }}</button>
                </div>
            </div>
        </div>
    </div>

{% endblock %}

{% block javascripts_inline %}
    <script>
        // triggered when delete confirm modal is about to be shown
        $("#deleteCPConfirmModal").on("show.bs.modal", function(e) {
            // Pass form reference to modal
            var form = $(e.relatedTarget).siblings('form');
            $(this).find(".confirm-button").data('form', form);
        });

        // action on confirm
        $("#deleteCPConfirmModal .confirm-button").on("click", function(){
            $(this).data('form').submit();
        });

        $("#statusConfirmModal").on("show.bs.modal", function(e) {
            var btn = $(e.relatedTarget);
            $("#statusConfirmModal #targetId").val(btn.data("id"));
        });

        $('#statusConfirmModal #statusConfirm').on('click', function () {
           $('#statusConfirmModal #card_program_status').submit();
        });

        $("#cardProgramTable").DataTable({
            "lengthMenu": [[20, 50, 100, -1], [20, 50, 100, "All"]],
            "order": [[1, 'asc']],
            "columnDefs": [ {
                "targets": [4],
                "orderable": false,
                "searchable": false
            }]
        });
    </script>

    <style>
        #cardProgramTable_filter {
            display: none;
        }
    </style>
{% endblock %}
