{% extends 'layout/base-layout.html.twig' %}
{% block page_title %}Fee items setting of card program "{{ cp.name }}"{% endblock %}


{% block page_content %}
    <div class="row-fluid">
        <table class="table table-striped table-bordered table-hover mt-5">
            <thead>
            <tr>
                <th>Fee item ID</th>
                <th>Tenant Type</th>
                <th>Tenant Name</th>
                <th>Fee Global Name</th>
                <th>Fee Original Name</th>
                <th>Apply to</th>
                <th>Measure</th>
                <th>Price currency</th>
                <th>Statement display name</th>
                <th>{{ 'column.action'|trans }}</th>
            </tr>
            </thead>
            <tbody>
            {% for item in items %}
                <tr>
                    <td>
                        {% if editable(Module.ID_FEE_SCHEDULE) %}
                            <a href="/admin/fee-engine/{{ item.id }}/edit" target="_blank">{{ item.id }}</a>
                        {% else %}
                            {{ item.id }}
                        {% endif %}
                    </td>
                    <td>{{ item.entityType }}</td>
                    <td>{{ item.entity.name | default }}</td>
                    <td>{{ item.getFeeGlobalName().getName() | default }}</td>
                    <td>{{ item.getFeeName()}}</td>
                    <td>{{ item.cp.applyTo | default }}</td>
                    <td>{{ item.cp.measureText | default }}</td>
                    <td>{{ item.cp.priceCurrencies | default }}</td>
                    <td>{{ item.cp.statementDisplayName | default }}</td>
                    <td>
                        {% if editable(Module.ID_CARD_PROGRAM_LIST) %}
                            <a href="/admin/card-program/{{ cp.id }}/fee-item/{{ item.id }}"
                               class="btn btn-primary btn-xs ph-10">Edit</a>
                        {% endif %}
                    </td>
                </tr>
            {% endfor %}
            </tbody>
        </table>
    </div>

    <!--/row-->
{% endblock %}

{% block javascripts_inline %}
    <script>
        $(function () {
        });
    </script>
{% endblock %}