{% extends 'layout/base-layout.html.twig' %}
{% block page_title %}
    Edit fee item "{{ feeItem.fullName }}" for card program "{{ cp.name }}"
{% endblock %}


{% block page_content %}

    <form action="/admin/card-program/{{ cp.id }}/fee-item/{{ feeItem.id }}" method="post" id="main-form">
        {% if entity %}
            <input type="hidden" name="id" value="{{ feeItem.id }}">
        {% endif %}

        <div class="form-group">
            <label class="control-label">Apply to:</label>
            <select class="form-control">
                {% for s in applyTos %}
                    <option value="{{ s }}">{{ s | humanize }}</option>
                {% endfor %}
            </select>
        </div>

        <div class="form-group">
            <label class="control-label">Measure:</label>
            <input type="number" class="form-control">
        </div>

        <div class="form-group">
            <label class="control-label">Measure Unit:</label>
            <select class="form-control">
                {% for s in measureUnits %}
                    <option value="{{ s }}">{{ s | humanize }}</option>
                {% endfor %}
            </select>
        </div>

        <div class="form-group">
            <label class="control-label">Price currency:</label>
            <select class="form-control" data-vv="USD">
                {% for s in currencies %}
                    <option value="{{ s }}">{{ s }}</option>
                {% endfor %}
            </select>
        </div>

        <div class="form-group">
            <label class="control-label">Display name on account holder statement:</label>
            <div class="input-group">
                <input type="text" class="form-control">
                <span class="input-group-btn">
                    <a href="javascript:" class="btn btn-default">Clear</a>
                </span>
                <span class="input-group-btn">
                    <a href="javascript:" class="btn btn-default">Fill with global name</a>
                </span>
            </div>
            <div class="help-block">Leave empty if you don't want it show in account holder statement</div>
        </div>

        <div class="mt-5">
            <input type="submit" class="btn btn-primary mr-15" value="Submit">
            <a href="javascript:" onclick="history.go(-1);" class="btn btn-default">Cancel</a>
        </div>

    </form>

{% endblock %}

{% block javascripts_inline %}
    <script>
    </script>
{% endblock %}