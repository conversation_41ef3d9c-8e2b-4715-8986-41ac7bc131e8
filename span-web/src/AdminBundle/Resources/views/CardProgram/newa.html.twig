<h4>Edit Kyc Provider and Load Partner:</h4>
<form id="cardTypecuntryadform">
<div class="row-fluid" id="dadsadsadasd">
        <table class="table table-striped table-bordered table-hover" id="countryTable">
            <thead>
            <tr>
                {#<th><input type="checkbox" id="countryTableCheckAll" /></th>#}
                <th>Name</th>
                <th>ISO3</th>
                <th>Region</th>
                <th>Currency</th>
                <th>Actions</th>
            </tr>
            </thead>
            <tbody>
            {% for item in countries %}
                <tr>
                    {#<td><input type="checkbox" name="card_program[countries][]" value="{{ item.getId() }}" class="country-checkbox"></td>#}
                    <td>{{ item.getName() }}</td>
                    <td>{{ item.getIso3Code() }}</td>
                    <td>{{ item.getRegion() }}</td>
                    <td>{{ item.getCurrency() }}</td>
                    <td>
                        <div class="btn-group">
                            <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                Actions <span class="caret"></span>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a href="#" data-toggle="modal" data-target="#loadPartnerSettingsModel_{{ item.getId() }}">Load Partner Settings</a></li>
                                <li><a href="#" data-toggle="modal" data-target="#kycProviderSettingsModel_{{ item.getId() }}">Kyc Provider Settings</a></li>
                            </ul>
                        </div>
                        <input type="hidden" name="load_partner_settings_{{ item.getId() }}" value=""/>
                        <input type="hidden" name="kyc_provider_settings_{{ item.getId() }}" value=""/>
                    </td>
                </tr>
            {% endfor %}
            </tbody>
        </table>
    <span class="btn btn-danger confirm-button"  onclick="finshall()">Return List</span>
    </div>
    {#{% do form.countries.setRendered %}#}
</form>


<!-- Load partner currency settings Models (TODO: consider ajax)-->
{% for country in countries %}
    <div class="modal fade" id="loadPartnerSettingsModel_{{ country.getId() }}" tabindex="-1" role="dialog" aria-labelledby="loadPartnerSettingsModelLabel_{{ country.getId() }}">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="loadPartnerSettingsModelLabel_{{ country.getId() }}">Load Partner Settings</h4>
                </div>
                <div class="modal-body">
                    <form id="loadPartnesform">
                        <input type="hidden" name="cardprogram_id" value="{{ cardprogram_id }}">
                        {% for partner in loadPartners %}
                            {% if partner.getLoadMethods() | length > 0 %}
                                <div class="row-fluid">
                                    <h4>{{ partner.getName() }}</h4>
                                    <table class="table table-striped table-bordered table-hover currency-table">
                                        <thead>
                                        <tr>
                                            <th>Load Method</th>
                                            <th>Currencies</th>
                                            <th style="display: none">Enabled</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        {% for method in partner.getLoadMethods() %}
                                            {% set settings = currencySettings[country.getId()][partner.getId()][method.getId()] %}
                                            {#{% if settings["enabled"] %}#}
                                            {% if settings['currencies']%}
                                                <tr>
                                                    <td>
                                                        {{ method.getName() }}
                                                        <input type="hidden" name="countryid[]" value="{{ country.getId() }}">
                                                        <input type="hidden" name="partnerid[]" value="{{ partner.getId() }}">
                                                        <input type="hidden" name="methodid[]" value="{{ method.getId() }}">
                                                    </td>
                                                    <td>
                                                        {% set currencyArray = settings["currencies"] | split(";") %}
                                                        {% set currencyxArray = settings["currenciesx"] | split(";") %}
                                                        {% for currency in currencyArray %}
                                                             {#twig split will an array containning one empty element if the string is empty#}
                                                            {% if currency %}
                                                                <div class="checkbox">
                                                                    <label>
                                                                        <input type="checkbox"
                                                                               name="{{ country.getId() ~ '_' ~ partner.getId() ~ '_' ~ method.getId() ~ '_currencies[]' }}"
                                                                               value="{{ currency }}"
                                                                               {% if settings["enabled"] and (currency in currencyxArray)%}checked{% endif %}
                                                                               class="currency-checkbox"
                                                                        />
                                                                        {{ currency }}
                                                                    </label>
                                                                </div>
                                                            {% endif %}
                                                        {% endfor %}
                                                        {#{% set moreCurrencies = settings["moreCurrencies"] %}#}
                                                        {#{% for more in moreCurrencies %}#}
                                                            {#<div class="checkbox">#}
                                                                {#<label>#}
                                                                    {#<input type="checkbox"#}
                                                                           {#name="{{ country.getId() ~ '_' ~ partner.getId() ~ '_' ~ method.getId() ~ '_currencies[]' }}"#}
                                                                           {#value="{{ more }}"#}
                                                                            {#{% if settings["enabled"] %}checked{% endif %}#}
                                                                           {#class="currency-checkbox"#}
                                                                    {#/>#}
                                                                    {#{{ more }}#}
                                                                {#</label>#}
                                                            {#</div>#}
                                                        {#{% endfor %}#}
                                                    </td>
                                                    {#<td style="display: none">#}
                                                        {#<input type="checkbox" name="{{ country.getId() ~ '_' ~ partner.getId() ~ '_' ~ method.getId() ~ '_enabled' }}"#}
                                                               {#class="enabled-checkbox"#}
                                                                {#{{ settings["enabled"] ? 'checked' : '' }}#}
                                                        {#/>#}
                                                    {#</td>#}
                                                </tr>
                                            {% endif %}
                                        {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                                <br/>
                            {% endif %}
                        {% endfor %}
                    </form>
                </div>
                <div class="modal-footer">
                    {#<button type="button" class="btn btn-primary load-partner-settings-confirm" data-dismiss="modal">Confirm</button>#}
                    <span class="btn btn-danger confirm-button"  onclick="cardTypecuntryads({{ country.getId() }})">Save</span>
                </div>
            </div>
        </div>
    </div>
{% endfor %}

<!-- Kyc provider image requirement settings Models (TODO: consider ajax)-->
{% for country in countries %}
    <div class="modal fade" id="kycProviderSettingsModel_{{ country.getId() }}" tabindex="-1" role="dialog" aria-labelledby="kycProviderSettingsModelLabel_{{ country.getId() }}">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="kycproviderSettingsModelLabel_{{ country.getId() }}">Program kyc country settings</h4>
                </div>
                <div class="modal-body">
                    <form id="kycProvidersform">
                        <input type="hidden" name="cardprogram_id" value="{{ cardprogram_id }}">
                        {% for kyc in kycProviders %}
                            <h4>{{ kyc.getName() }}</h4>
                            <table class="table table-striped table-bordered table-hover kyc-settings-table">
                                <thead>
                                <tr>
                                    <th>Identity Type</th>
                                    <th>Image Requirement</th>
                                    <th>Enabled</th>
                                </tr>
                                </thead>
                                <tbody>
                                {% for idType in identityTypeAll  %}
                                    {% set settings = imageRequirementSettings[country.getId()][kyc.getId()][idType] %}
                                {% if settings['requirement']%}
                                    <tr>
                                        <td>
                                            {{ idType }}
                                            <input type="hidden" name="countryid[]" value="{{ country.getId() }}">
                                            <input type="hidden" name="kycid[]" value="{{ kyc.getId() }}">
                                            <input type="hidden" name="typeid[]" value="{{ idType }}">
                                        </td>
                                        <td>
                                            <select name="{{ country.getId() ~ '_' ~ kyc.getId() ~ '_' ~ idType ~ '_requirement_disabled' }}"
                                                    class="form-control requirement-select" disabled>
                                                {% for imageReq in imageRequirementAll  %}
                                                    <option value="{{ imageReq }}" {{ settings["requirement"] == imageReq ? 'selected' : '' }}>{{ imageReq }}</option>
                                                {% endfor %}
                                            </select>
                                            <div style="display: none">
                                                <select name="{{ country.getId() ~ '_' ~ kyc.getId() ~ '_' ~ idType ~ '_requirement' }}"
                                                        class="form-control requirement-select">
                                                    {% for imageReq in imageRequirementAll  %}
                                                        <option value="{{ imageReq }}" {{ settings["requirement"] == imageReq ? 'selected' : '' }}>{{ imageReq }}</option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                        </td>
                                        <td>
                                            <input type="checkbox" name="{{ country.getId() ~ '_' ~ kyc.getId() ~ '_' ~ idType ~ '_enabled' }}"
                                                   class="enabled-checkbox" {{ settings["enabled"] ? 'checked' : '' }}
                                            />
                                        </td>
                                    </tr>
                                {% endif %}
                                {% endfor %}
                                </tbody>
                            </table>
                        {% endfor %}
                    </form>
                </div>
                <div class="modal-footer">
                    {#<button type="button" class="btn btn-primary kyc-provider-settings-confirm" data-dismiss="modal">Confirm</button>#}
                    <span class="btn btn-danger confirm-button"  onclick="cardTypecuntrykyc({{ country.getId() }})">Save</span>
                </div>
            </div>
        </div>
    </div>
{% endfor %}

<script>
    fillLoadPartnerSettings();
    fillKycProviderSettings();
    function cardTypecuntryads(id) {
        fillLoadPartnerSettings();
        $.ajax({
            type: "get",
            cache: false,
            async:true,
//            contentType: "application/json; charset=utf-8",
            url: "{{ path('admin_card_program_loadPartnesformajax')}}",
            data: $('#loadPartnesform').serialize(),
//            data:{'dasdasd':'dadad'},
            success: function(data) {
                $("#loadPartnerSettingsModel_"+id).modal("hide");
            }
        });
    }
    function cardTypecuntrykyc(id) {
        fillKycProviderSettings();
        $.ajax({
            type: "get",
            cache: false,
            async:true,
//            contentType: "application/json; charset=utf-8",
            url: "{{ path('admin_card_program_kycProvidersformajax')}}",
            data: $('#kycProvidersform').serialize(),
            success: function(data) {
                $("#kycProviderSettingsModel_"+id).modal("hide");
            }
        });
    }

    function finshall() {
        var progromids=$('#function_cardprogram_id').val();
        $.ajax({
            type: "get",
            cache: false,
            contentType: "application/json; charset=utf-8",
            url: "{{ path('admin_card_program_cuntryfinshajax')}}",
            data: {'cardprogram_id':progromids},
            success: function(data) {
                $("#cuntry").html("");
                $("#cuntry").append(data);
            }
        });
    }
    // fill load partner settings fields
    function fillLoadPartnerSettings() {
        $("input[name^='load_partner_settings_']").each(function(){
            var country = $(this).attr("name").replace("load_partner_settings_", "");
            var $form = $("#loadPartnerSettingsModel_" + country).find("form");
            //var $disabledTextBoxes = $form.find(":input:disabled").prop('disabled', false); // also get the form data from disabled textbox
            var formData = $form.serialize();
            $(this).val(formData);
            //$disabledTextBoxes.prop('disabled', true);
        });
    }
    // fill kyc provider settings fields
    function fillKycProviderSettings() {
        $("input[name^='kyc_provider_settings_']").each(function(){
            var country = $(this).attr("name").replace("kyc_provider_settings_", "");
            var $form = $("#kycProviderSettingsModel_" + country).find("form");
            //var $disabledTextBoxes = $form.find(":input:disabled").prop('disabled', false); // also get the form data from disabled textbox
            var formData = $form.serialize();
            $(this).val(formData);
            //$disabledTextBoxes.prop('disabled', true);
        });
    }


    $(".country-checkbox, #countryTableCheckAll, .load-partner-settings-confirm, .kyc-provider-settings-confirm").on("click", function(){
        fillLoadPartnerSettings();
        fillKycProviderSettings();
    });

    // toggle select all
    $("#countryTableCheckAll").on("click", function(){
        var checked = $(this).prop("checked");
        $(".country-checkbox").each(function(){
            $(this).prop("checked", checked);
        });
    });

    //        // toggle enabled/disable for load partner
    //        $(".currency-table .enabled-checkbox").on("click", function() {
    //            var checked = $(this).prop("checked");
    //            $(this).closest("tr").find(".currency-checkbox").prop("disabled", !checked);
    //        });

    // toggle enabled/disable for kyc provider
//    $(".kyc-settings-table .enabled-checkbox").on("click", function() {
//        var checked = $(this).prop("checked");
//        $(this).closest("tr").find(".requirement-select").prop("disabled", !checked);
//    });
//    $(function () {
//        $(this).closest("tr").find(".requirement-select").prop("readonly");
//    })
</script>