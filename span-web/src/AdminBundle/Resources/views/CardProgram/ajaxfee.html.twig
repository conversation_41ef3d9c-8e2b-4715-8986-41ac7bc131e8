<form id="cardTypecuntryadform">
<div class="col-md-12">
    <table class="table table-striped table-bordered table-hover">
        <thead>
        <tr>
            <th><input type="checkbox" id="feeItemTableCheckAll" /></th>
            <th>FeeName</th>
            <th>Cost To Tern Fixed</th>
            <th>Cost To Cus Fixed</th>
            <th>Cost To Tern Ratio</th>
            <th>Cost To Cus Ratio</th>
        </tr>
        </thead>

        <tbody>

        {% for item in feeItems %}
            {% if item is not null %}
            {% if item.getFeeGlobalName() %}{% if item.getFeeGlobalName().getName()!='Unknown'%}
                <tr>
                    <td><input type="checkbox" name="card_program[feeItem][]" value="{{ item.getId() }}" class="feeItem-checkbox" {% if item.getFeeGlobalName() %}{% if item.getFeeGlobalName().getName()=='Unknown'%} disabled="disabled"{% endif %}{% endif %}></td>
                    <td>{{ item.getFeeName() }}</td>
                    {#<td>{{ item.getCostTernFixed()~' '~item.getCostTernFixedCurrency() }}</td>#}
                    <td>{% if item.getCostTernFixedCurrency() %}
                            {{ item.getCostTernFixed()| money_format(item.getCostTernFixedCurrency())}}
                        {% else %}
                            {{ item.getCostTernFixed()| money_format_amount(item.getCostTernFixedCurrency())}}
                        {% endif %}
                    </td>
                    <td>{% if item.getCostTernFixedCurrency() %}
                            {{ item.getCardProgramFeeItemByCardProgramId(cardprogram_id).getFeeToCusFixed()| money_format(item.getCostTernFixedCurrency())}}
                        {% else %}
                            {{ item.getCardProgramFeeItemByCardProgramId(cardprogram_id).getFeeToCusFixed()| money_format_amount(item.getCostTernFixedCurrency())}}
                        {% endif %}
                    </td>
                    <td>{{ item.getCostTernRatio() }}</td>
                    <td>{{ item.getCardProgramFeeItemByCardProgramId(cardprogram_id).getFeeToCusRatio() }}</td>
                </tr>
            {% endif %}{% endif %}
            {% endif %}
        {% endfor %}
        </tbody>
    </table>
    <input type="hidden" name="cardprogram_id" value="{{ cardprogram_id }}">
    <span class="btn btn-danger confirm-button"  onclick="finshall()">Return List</span>
    <span class="btn btn-danger confirm-button " id="cardTypecuntryad" onclick="cardTypecuntryads()">Next Step</span>
</div>
</form>
<script>
    function cardTypecuntryads() {
        $.ajax({
            type: "get",
            cache: false,
            async:true,
            url: "{{ path('admin_card_program_feeadajax')}}",
            data: $('#cardTypecuntryadform').serialize(),
            success: function(data) {
                $("#feeschedule").html("");
                $("#feeschedule").append(data);
            }
        });
    }
    $("#feeItemTableCheckAll").on("click", function(){
        var checked = $(this).prop("checked");
        $(".feeItem-checkbox").each(function(){
            $(this).prop("checked", checked);
        });
    });

    function finshall() {
        var progromids=$('#function_cardprogram_id').val();
        $.ajax({
            type: "get",
            cache: false,
            contentType: "application/json; charset=utf-8",
            url: "{{ path('admin_card_program_feefinshajax')}}",
            data: {'cardprogram_id':progromids},
            success: function(data) {
                $("#feeschedule").html("");
                $("#feeschedule").append(data);
            }
        });
    }
</script>