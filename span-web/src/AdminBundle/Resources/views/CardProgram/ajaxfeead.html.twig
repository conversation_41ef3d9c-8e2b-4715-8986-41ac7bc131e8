<div class="col-md-12">
    <form id="cardTypecuntryadformx">
       {% for item in feeitemids %}
           <input type="hidden" name="feeitemids[]" value="{{ item }}">
        {% endfor %}
        <input type="hidden" name="cardprogram_id" value="{{ cardprogram_id }}">
    </form>
    <table class="table table-striped table-bordered table-hover">
        <thead>
        <tr>

            <th>FeeName</th>
            <th>Cost To Tern Fixed</th>
            <th>Cost To Cus Fixed</th>
            <th>Cost To Tern Ratio</th>
            <th>Cost To Cus Ratio</th>
            <th>Action</th>
        </tr>
        </thead>

        <tbody>

        {% for item in feeItems %}
            {% if item is not null %}
                <tr>
                    {#<td><input type="checkbox" name="card_program[feeItem][]" value="{{ item.getId() }}"></td>#}
                    <td>{{ item.getFeeName() }}</td>
                    {#<td>{{ item.getCostTernFixed()~' '~item.getCostTernFixedCurrency() }}</td>#}

                    {#<td>#}
                        {#{% if item.getCostTernFixedCurrency() %}#}
                            {#{{ item.getCostTernFixed()| money_format(item.getCostTernFixedCurrency())}}#}
                        {#{% else %}#}
                            {#{{ item.getCostTernFixed()| money_format_amount(item.getCostTernFixedCurrency())}}#}
                        {#{% endif %}#}
                    {#</td>#}
                    {#<td>#}
                        {#{% if item.getCostTernFixedCurrency() %}#}
                            {#{{ item.getCardProgramFeeItemByCardProgramId(cardprogram_id).getFeeToCusFixed()| money_format(item.getCostTernFixedCurrency())}}#}
                        {#{% else %}#}
                            {#{{ item.getCardProgramFeeItemByCardProgramId(cardprogram_id).getFeeToCusFixed()| money_format_amount(item.getCostTernFixedCurrency())}}#}
                        {#{% endif %}#}
                    {#</td>#}
                    {#<td>{{ item.getCostTernRatio() }}</td>#}
                    {#<td>{{ item.getCardProgramFeeItemByCardProgramId(cardprogram_id).getFeeToCusRatio() }}</td>#}

                    <td>
                        {{ item.getCostTernFixed()| money_format(item.getCostTernFixedCurrency())}}
                    </td>
                    <td>
                        {% if cpfeeitem and cpfeeitem[cardprogram_id][item.getId()] is defined %}
                            {{ cpfeeitem[cardprogram_id][item.getId()]['fee_to_customer_fixed']| money_format(item.getCostTernFixedCurrency())}}
                        {% endif %}
                    </td>
                    <td>{{ item.getCostTernRatio() }}</td>
                    <td>
                        {% if cpfeeitem and cpfeeitem[cardprogram_id][item.getId()] is defined %}
                            {{ cpfeeitem[cardprogram_id][item.getId()]['fee_to_customer_ratio'] }}
                        {% endif %}
                    </td>
                    <td>
                        <div class="btn-group">
                            <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                Actions <span class="caret"></span>
                            </button>
                            <ul class="dropdown-menu" id="fee_item_config_btns_{{ item.getId() }}">
                                <div id = "fee_item_detail_action_{{ item.getId() }}">
                                    <a href="#" class="btn btn-success" data-id="{{ item.getId() }}" data-toggle="modal" data-target="#feeItemDetailModal">{{ "cardprogramwizard.feeitem.detail.btn"|trans }}</a>
                                    <a href="#" class="btn btn-success" data-id="{{ item.getId() }}'" data-toggle="modal" data-target="#feeItemRevenueModal">{{ "cardprogramwizard.feeitem.revenue.btn"|trans }}</a>
                                </div>
                            </ul>
                        </div>
                    </td>
                </tr>
            {% endif %}
        {% endfor %}
        </tbody>
    </table>
    <span class="btn btn-danger confirm-button"  onclick="finshall()">Return List</span>
</div>

<!-- Fee Item Detail Modal -->
<div class="modal fade" id="feeItemDetailModal" tabindex="-1" role="dialog" aria-labelledby="feeItemDetailModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="feeItemDetailModalLabel">{{ 'cardprogramwizard.feeitem.detail.label'|trans }}</h4>
            </div>
            <form  method="post" id="feeItemDetailfrom">
                <input type="hidden" name="cardprogram_id" value="{{ cardprogram_id }}" id="cardprogram_id">
                <input type="hidden" id="fee_item_detail_info_" name="fee_item_detail_info[]">
            <div class="modal-body">
                {{ form(feeItemForm) }}
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">{{ 'btn.cancel'|trans }}</button>
                <button type="button" id="feeItemConfirm" class="btn btn-danger confirm-button">{{ 'btn.confirm'|trans }}</button>
            </div>
            </form>
        </div>
    </div>
</div>

<!-- Fee Item Revenue Modal -->
<div class="modal fade" id="feeItemRevenueModal" tabindex="-1" role="dialog" aria-labelledby="feeItemRevenueModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="feeItemRevenueModalLabel">{{ 'cardprogramwizard.feeitem.revenue.label'|trans }}</h4>
            </div>
            <form  method="post" id="feeItemRevenuefrom">
                <input type="hidden" name="cardprogram_id" value="{{ cardprogram_id }}" id="cardprogram_id">
                <input type="hidden" id="fee_item_revenue_info_" name="fee_item_revenue_info[]">
            <div class="modal-body">
                {{ form(feeItemRevenueForm) }}
                <div id="feeItemRevenueTenant" class="form-group">

                </div>
                <button type="button" id="feeItemRevenueAdd" class="btn btn-success confirm-button" disabled="disabled">{{ 'cardprogramwizard.feeitem.revenue.btn.add'|trans }}</button>
                <div class="panel panel-default">
                    <div class="panel-heading">{{ 'cardprogramwizard.cardtype.detail.title'|trans }}</div>
                    <table class="table">
                        <thead>
                        <tr>
                            <th>{{ 'cardprogramwizard.feeitem.revenue.column.type'|trans }}</th>
                            <th>{{ 'cardprogramwizard.feeitem.revenue.column.id'|trans }}</th>
                            <th>{{ 'cardprogramwizard.feeitem.revenue.column.name'|trans }}</th>
                            <th>{{ 'cardprogramwizard.feeitem.revenue.column.fixed'|trans }}</th>
                            <th>{{ 'cardprogramwizard.feeitem.revenue.column.ratio'|trans }}</th>
                            <th>{{ 'column.action'|trans }}</th>
                        </tr>
                        </thead>
                        <tbody id="revenueTenantList">
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">{{ 'btn.cancel'|trans }}</button>
                <button type="button" id="feeItemRevenueConfirm" class="btn btn-danger confirm-button">{{ 'btn.confirm'|trans }}</button>
            </div>
            </form>
        </div>
    </div>
</div>
<script type="text/javascript" src="{{ asset('bundles/admin/js/jquery.smartWizard.js') }}"></script>
<script>
    $("#feeItemDetailModal").on("show.bs.modal", function(e) {
        var btn = $(e.relatedTarget);
        $("#feeItemDetailModal #form_targetId").val(btn.data("id"));
        $.ajax({
            type: "get",
            cache: false,
            async:true,
            url: "{{ path('admin_card_program_feeitemlistajax')}}",
            data: {'feeid': btn.data("id"),'cardprogramid':$("#cardprogram_id").val()},
            dataType: "json",
            success: function(data) {
                $("#feeItemDetailModal #form_feeToCusFixed").val(data.feetocusfixed/100);
                $("#feeItemDetailModal #form_feeToCusRatio").val(data.feetocusratio);
                $("#feeItemDetailModal #form_feeToBPFixed").val(data.feetobpfixed/100);
                $("#feeItemDetailModal #form_feeToBPRatio").val(data.feetobpratio);
            }
        });

    })

    $("#feeItemDetailModal #feeItemConfirm").on("click", function () {
        var targetId = $("#feeItemDetailModal #form_targetId").val();
//        targetId=targetId.substr(0,targetId.length-1);
        var feeToCusFixed = $("#feeItemDetailModal #form_feeToCusFixed").val();
        if(feeToCusFixed == ""){
            feeToCusFixed = 0;
        }
        var feeToCusRatio = $("#feeItemDetailModal #form_feeToCusRatio").val();
        if(feeToCusRatio == ""){
            feeToCusRatio = 0;
        }
        var feeToBPFixed = $("#feeItemDetailModal #form_feeToBPFixed").val();
        if(feeToBPFixed == ""){
            feeToBPFixed = 0;
        }
        var feeToBPRatio = $("#feeItemDetailModal #form_feeToBPRatio").val();
        if(feeToBPRatio == ""){
            feeToBPRatio = 0;
        }
        var brandPartnerId = $("#feeItemDetailModal #form_brandPartnerName").val();
//        if($('#fee_item_detail_info_'+targetId)){
//            $('#fee_item_detail_info_'+targetId).remove();
//        }
        var tmp = '{"id":' + targetId + ', "feeToCusFixed":' + feeToCusFixed + ',"feeToCusRatio":'
                + feeToCusRatio + ',"feeToBPFixed":' + feeToBPFixed + ',"feeToBPRatio":' + feeToBPRatio + '}';
//        $('#fee_item_detail_action_'+targetId).after(
//                '<input type="hidden" id="fee_item_detail_info_' + targetId + '" name="fee_item_detail_info[]"/>'
//        );
        $('#fee_item_detail_info_').val(tmp);
//        $("#feeItemDetailModal").modal("hide");
        carprogramfeeItemDetail();
    });

    function carprogramfeeItemDetail() {
        $.ajax({
            type: "get",
            cache: false,
//                      contentType: "application/json; charset=utf-8",
            url: "{{ path('admin_card_program_ajaxfeeitem')}}",
            data: $('#feeItemDetailfrom').serialize(),
            success: function(data) {
                cardTyperefresh();
                $("#feeItemDetailModal").modal("hide");
                $('#feeItemDetailfrom')[0].reset();
            }
        });
    }

    $("#feeItemRevenueModal").on("show.bs.modal", function(e) {
        var btn = $(e.relatedTarget);
        $("#feeItemRevenueModal #form_targetId").val(btn.data("id"));
        $("#feeItemRevenueModal #form_targetTenant option:first").prop("selected", "selected");
        $("#feeItemRevenueModal #revenueTenantList").empty();
        $("#feeItemRevenueModal #feeItemRevenueTenant").empty();
        $.ajax({
            type: "get",
            cache: false,
            async:true,
            url: "{{ path('admin_card_program_feeitemsharelistajax')}}",
            data: {'feeid': btn.data("id"),'cardprogramid':$("#cardprogram_id").val()},
            dataType: "json",
            success: function(data) {
//                alert(data.tenantname);
                $.each(data, function(i, item) {
                $('#feeItemRevenueModal #revenueTenantList').append(
                        '<tr>'
                        + '<td>' + item.tenanttype + '</td>'
                        + '<td>' + item.tenantid + '</td>'
                        + '<td>' + item.tenantname + '</td>'
                        + "<td><input type='text' value='"+item.fixeds+"' /></td>"
                        + '<td><input type="text" value="'+item.ratios+'"/></td>'
                        + '</tr>'
                ); });
//                $("#feeItemDetailModal #form_feeToCusFixed").val(data.feetocusfixed);
//                $("#feeItemDetailModal #form_feeToCusRatio").val(data.feetocusratio);
//                $("#feeItemDetailModal #form_feeToBPFixed").val(data.feetobpfixed);
//                $("#feeItemDetailModal #form_feeToBPRatio").val(data.feetobpratio);
            }
        });
    });

    $("#feeItemRevenueModal #form_targetTenant").on('change',function () {
        var target = $('#feeItemRevenueModal #form_targetTenant').find("option:selected").text();
        if(target.indexOf('Choose') >= 0 ){
            $('#feeItemRevenueModal #feeItemRevenueAdd').prop("disabled","disabled");
            alert(target);
            return false;
        }
        $('#feeItemRevenueModal #feeItemRevenueAdd').prop("disabled",false);
        var selectName;
        var selectId;
        if('Brand Partner' == target){
            selectName = $('#form_brandPartner').find("option:selected").text();
            selectId = $('#form_brandPartner').find("option:selected").val();
        }else if('Marketing Partner' == target){
            if(!$('#form_isCoBrand').is(':checked')){
                alert("You do not have a related Marketing Partner");
                $('#feeItemRevenueModal #feeItemRevenueTenant').empty();
                $('#feeItemRevenueModal #feeItemRevenueAdd').prop("disabled","disabled");
                return false;
            }
            selectName = $('#form_marketingPartner').find("option:selected").text();
            selectId = $('#form_marketingPartner').find("option:selected").val();
        }
        var select = document.createElement("select");
        select.id = 'fee_item_revenue_tenant_detail';
        var option = document.createElement("option");
        option.name = selectName;
        option.innerHTML = selectName;
        option.value = selectId;
        select.appendChild(option)
        $('#feeItemRevenueModal #feeItemRevenueTenant').empty();
        $('#feeItemRevenueModal #feeItemRevenueTenant').append(
                '<label class="control-label required" >' + target + '</label>'
        ).append(select);
    });

    $('#feeItemRevenueModal #feeItemRevenueAdd').on('click', function () {
        var targetType = $('#feeItemRevenueModal #form_targetTenant').find("option:selected").text();
        var targetId = $('#feeItemRevenueModal #fee_item_revenue_tenant_detail').val();
        var targetName = $('#feeItemRevenueModal #fee_item_revenue_tenant_detail').find("option:selected").text();
        $('#feeItemRevenueModal #revenueTenantList').append(
                '<tr>'
                + '<td>' + targetType + '</td>'
                + '<td>' + targetId + '</td>'
                + '<td>' + targetName + '</td>'
                + '<td><input type="text" /></td>'
                + '<td><input type="text" /></td>'
                + '</tr>'
        );
    });

    $("#feeItemRevenueModal #feeItemRevenueConfirm").on("click", function () {
        var index = $('#feeItemRevenueModal #revenueTenantList').children("tr").length;

        var tmp = "{";
        var feeItemId = $("#feeItemRevenueModal #form_targetId").val();
        feeItemId=feeItemId.substr(0,feeItemId.length-1);
        $('#feeItemRevenueModal #revenueTenantList').find('tr').each(function () {
            var tdArr = $(this).children();
            var tdType= tdArr.eq(0).text();//Type
            var tdId = tdArr.eq(1).text();//Id
            var tdName = tdArr.eq(2).text();//Name
            var tdFixed = tdArr.eq(3).find('input').val();//Fixed
            if(tdFixed == ""){
                tdFixed = 0;
            }
            var tdRatio = tdArr.eq(4).find('input').val();//Ratio
            if(tdRatio == ""){
                tdRatio = 0;
            }
            if(tdFixed == "" && tdRatio == ""){
                alert("Please enter either Fixed or Ratio value for " + tdType + " : " +tdName);
            }
            tmp = tmp + '"' + tdType + tdId + '":'
                    + '{"feeItemId":'+ feeItemId + ', "tenantId":' + tdId
                    + ', "tenantType":"' + tdType + '", "tenantName":"' + tdName
                    + '", "fixed":' + tdFixed + ', "ratio":' + tdRatio + '},';
        });
        tmp = tmp.substring(0, tmp.length-1);
        tmp = tmp + "}";

//        if($('#fee_item_revenue_info_')){
//            $('#fee_item_revenue_info_').remove();
//        }
//        $('#fee_item_detail_action_').after(
//                '<input type="hidden" id="fee_item_revenue_info_" name="fee_item_revenue_info[]"/>'
//        );
////
        $('#fee_item_revenue_info_').val(tmp);
//        $("#feeItemRevenueModal").modal("hide");
        carprogramfeeItemRevenueModal();
    });
    function carprogramfeeItemRevenueModal() {
        $.ajax({
            type: "get",
            cache: false,
//                      contentType: "application/json; charset=utf-8",
            url: "{{ path('admin_card_program_ajaxfeeshare')}}",
            data: $('#feeItemRevenuefrom').serialize(),
            success: function(data) {
                cardTyperefresh();
                $("#feeItemRevenueModal").modal("hide");
                $('#feeItemRevenuefrom')[0].reset();
            }
        });
    }

    function cardTyperefresh() {
        $.ajax({
            type: "get",
            cache: false,
            async:true,
            url: "{{ path('admin_card_program_feeadfreshajax')}}",
            data: $('#cardTypecuntryadformx').serialize(),
            success: function(data) {
                $("#feeschedule").html("");
                $("#feeschedule").append(data);
            }
        });
    }

    function finshall() {
        var progromids=$('#function_cardprogram_id').val();
        $.ajax({
            type: "get",
            cache: false,
            contentType: "application/json; charset=utf-8",
            url: "{{ path('admin_card_program_feefinshajax')}}",
            data: {'cardprogram_id':progromids},
            success: function(data) {
                $("#feeschedule").html("");
                $("#feeschedule").append(data);
            }
        });
    }
</script>