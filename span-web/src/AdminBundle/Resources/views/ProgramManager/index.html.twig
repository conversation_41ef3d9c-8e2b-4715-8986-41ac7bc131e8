{% extends 'layout/base-layout.html.twig' %}
{% block page_title %} {{ 'program.title.view'|trans }} {% endblock %}


{% block page_content %}
    <!--/row-->
    <div class="row-fluid opera-box">
        {% for flashMessage in app.session.flashbag.get('success') %}
            <div class="alert alert-success">
                <button type="button" class="close" data-dismiss="alert">&times;</button>
                {{ flashMessage }}
            </div>
        {% endfor %}
        {% for flashMessage in app.session.flashbag.get('failure') %}
            {% if flashMessage %}
                <div class="alert alert-error">
                    <button type="button" class="close" data-dismiss="alert">&times;</button>
                    {{ flashMessage }}
                </div>
            {% endif %}
        {% endfor %}

        {% if editable(Module.ID_PROGRAM_MANAGEMENT) %}
        <div class="opera-right span3 pull-right">
            <button type="button" class="btn btn-success" id="new_program"
                    onclick="window.location.href='{{ path('admin_program_new') }}';">{{ 'program.btn.create'|trans }}</button>
        </div>
        {% endif %}
    </div>

    <div class="row-fluid">
        <table class="table table-striped table-bordered table-hover" id="programTable">
            <thead>
            <tr>
                <th>{{ 'program.column.name'|trans }}</th>
                <th>{{ 'program.column.address'|trans }}</th>
                <th>{{ 'program.column.contact'|trans }}</th>
                <th>{{ 'program.column.email'|trans }}</th>
                <th>{{ 'column.action'|trans }}</th>
            </tr>
            </thead>
            <tbody>
            {% for item in programs %}
                <tr>
                    <td>{{ item.getName() }}</td>
                    <td>{{ item.getAddress() }}</td>
                    <td>{{ item.getContact().getFirstName()~' '~ item.getContact().getLastName()}}</td>
                    <td>{{ item.getContact().getEmail() }}</td>
                    <td>
                        {#Changed on 2017-02-28 with button group#}
                        <div class="btn-group">
                            <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">
                                {{ 'btn.actions'|trans }} <span class="caret"></span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-right">
                                {% if editable(Module.ID_PROGRAM_MANAGEMENT) %}
                                    <li><a href="{{ path('admin_program_modify', {'program_id':item.getId()}) }}">{{ 'btn.modify'|trans }}</a></li>
                                    <li>
                                        <a href="#" data-toggle="modal" data-target="#deleteProgramConfirmModal">{{ 'btn.delete'|trans }}</a>
                                        <form action="{{ path('admin_program_delete',{'program_id':item.getId() }) }}" method="post"></form>
                                    </li>
                                {% endif %}
                                <li><a href="/admin/tenants/contacts/{{ item.id }}" target="_blank">Contacts</a></li>
                                <li><a href="/admin/tenants/{{ item.id }}/fee-options" target="_blank">Fee options</a></li>
                                <li><a href="{{ path('admin_fee_item', {'entity_id':item.getId(), 'entity_type':'Program'}) }}">{{ 'btn.feeitem'|trans }}</a></li>
                            </ul>
                        </div>
                    </td>
                </tr>
            {% endfor %}
            </tbody>
            Show
            <div class="btn-group">
                <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    Page <span class="caret"></span>
                </button>
                <ul class="dropdown-menu">
                    <li><a href="{{ path('admin_program',{'limit':5}) }}">default</a></li>
                    <li><a href="{{ path('admin_program',{'limit':10}) }}">10</a></li>
                    <li><a href="{{ path('admin_program',{'limit':20 }) }}">20</a></li>
                    <li><a href="{{ path('admin_program',{'limit':50 }) }}">50</a></li>
                </ul>
            </div>
            entries
        </table>
        <div class="opera-right span3 pull-right">
            {{ knp_pagination_render(programs) }}
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteProgramConfirmModal" tabindex="-1" role="dialog" aria-labelledby="deleteProgramConfirmModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="deleteProgramConfirmModalLabel">{{ 'program.label.delete'|trans }}</h4>
                </div>
                <div class="modal-body">
                    {{ 'program.msg.delete'|trans }}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">{{ 'btn.cancel'|trans }}</button>
                    <button type="button" class="btn btn-danger confirm-button">{{ 'btn.confirm'|trans }}</button>
                </div>
            </div>
        </div>
    </div>

    <!--/row-->
{% endblock %}

{% block javascripts_inline %}
    <script>
        // triggered when delete confirm modal is about to be shown
        $("#deleteProgramConfirmModal").on("show.bs.modal", function(e) {
            // Pass form reference to modal
            var form = $(e.relatedTarget).siblings('form');
            $(this).find(".confirm-button").data('form', form);
        });

        // action on confirm
        $("#deleteProgramConfirmModal .confirm-button").on("click", function(){
            $(this).data('form').submit();
        });

        //Add on 2017-02-23 replace with jquery data table
        $("#programTable").DataTable({
            "paging": false,
            "lengthChange": false,
            "searching": false,
            "ordering": true,
            "info": true,
            "autoWidth": false
//            "lengthMenu": [[20, 50, 100, -1], [20, 50, 100, "All"]],
//            "columnDefs": [ {
//                "targets": [4],
//                "orderable": false,
//                "searchable": false
//            }]
        });
    </script>
{% endblock %}