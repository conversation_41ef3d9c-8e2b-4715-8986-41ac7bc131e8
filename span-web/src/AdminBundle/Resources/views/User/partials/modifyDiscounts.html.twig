<div class="tab-pane fade " id="applied_discounts">
    <div class="row-fluid">
        <div>
            <h2>
                Discounts

                <i v-if="loading"
                   class="fa fa-spin fa-refresh font-18 ml-20"></i>

                <a href="javascript:" class="btn btn-default pull-right ml-10" @click="reload">Refresh</a>

                {% if editable(Module.ID_PROMOTION) %}
                    <a href="/admin/promotion/new?user_id={{ user.id }}"
                    class="btn btn-success pull-right">Add Promotion</a>
                {% endif %}
            </h2>
        </div>
    </div>
    <div class="row-fluid" id="discounts_list">
        <table class="table table-striped table-bordered table-hover">
            <thead>
            <tr>
                <th>Promotion</th>
                <th>Fee category</th>
                <th>Discount fixed</th>
                <th>Discount ratio</th>
                <th>Time range</th>
                <th>Min load</th>
                <th>Left redemption</th>
                <th>Action</th>
            </tr>
            <tbody>
            <tr v-for="a in items" :key="a.type + '_' + a.id"
                :data-type="a.type" :data-id="a.id"
                :class="{silver:a.invalid}">
                <td>
                    <span v-if="a.promotion" v-text="a.promotion"></span>
                </td>
                <td v-text="a.fee"></td>
                <td v-text="a.fixed"></td>
                <td v-text="a.ratio"></td>
                <td v-text="a.range"></td>
                <td v-text="a.minLoad"></td>
                <td v-text="a.leftRedemption"></td>
                <td>
                    <a href="javascript:"
                       @click="remove(a)"
                       v-if="!a.promotion" class="btn btn-xs btn-danger">Delete</a>
                    {% if editable(Module.ID_PROMOTION) %}
                        <a :href="'/admin/promotion/' + a.id + '/edit'"
                           v-if="a.promotion" target="_blank"
                           class="btn btn-primary btn-xs">Edit</a>
                    {% endif %}
                </td>
            </tr>
            </tbody>
            </thead>
        </table>
    </div>
</div>

<script>
    $(function () {
      new Vue({
        el: '#applied_discounts',
        data: {
          items: [],
          loading: false
        },
        methods: {
          reload () {
            this.loading = true;
            $.get('/admin/user/{{ user.id }}/promotion/discounts', function (resp) {
              this.loading = false;
              if (resp.success) {
                this.items = resp.data
              }
            }.bind(this));
          },
          remove (ud) {
            ts.confirm('Are you sure that you want to remove this discount from this user?', function () {
              ts.modal();
              $.post('/admin/user/promotion/remove/' + ud.id + '/discount', function (resp) {
                ts.close();
                ts.notify(resp, true);
                this.reload();
              }.bind(this));
            }.bind(this));
          }
        },
        mounted: function () {
          this.reload();
        }
      })
    });
</script>