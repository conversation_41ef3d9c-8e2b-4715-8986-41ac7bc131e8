{% extends 'layout/base-layout.html.twig' %}
{% block page_title %} User Profile {% endblock %}

{% block style_inline%}
    <style>
        #home img {
            width: 100px;
        }
    </style>
{% endblock %}

{% block page_content %}
    <ul id="myTab" class="nav nav-tabs">
        <li class="{% if not errorInfo %}active{% endif %}">
            <a href="#home" data-toggle="tab">
                Basic info
            </a>
        </li>
        <li class="{% if  errorInfo %}active{% endif %}"><a href="#security" data-toggle="tab">Security</a></li>
        {% if ipusage is defined %}
            <li><a href="#ipUsage" data-toggle="tab">IP Usage</a></li>
        {% endif %}
         {% if verifyTypeRecord is defined %}
            <li><a href="#verifyTypeRecord" data-toggle="tab">2FA Verify Type</a></li>
        {% endif %}
    </ul>
    <div id="myTabContent" class="tab-content">
    <div class="tab-pane fade {% if not errorInfo %} in active {% endif %}" id="home">
        <p>
        <div class="row-fluid">
            <div>
                <h2> Basic info</h2>
            </div>
        </div>
        <div class="row-fluid">
            <div>
                <form action="{{ path('admin_user_modify_profile') }}" method="post"
                      enctype="multipart/form-data" class="profile-form">
                    {#{{ form_widget(form) }}#}
                    <div class="box-body">
                        <div class="form-group">
                            {{ form_label(form.username) }}
                            {{ form_errors(form.username) }}
                            {{ form_widget(form.username, { 'attr': { 'style': 'color:#E00E40', disabled: true } }) }}
                        </div>
                        <div class="form-group">
                            {{ form_label(form.firstName) }}
                            {{ form_errors(form.firstName) }}
                            {{ form_widget(form.firstName) }}
                        </div>
                        <div class="form-group">
                            {{ form_label(form.lastName) }}
                            {{ form_errors(form.lastName) }}
                            {{ form_widget(form.lastName) }}
                        </div>

                        <div class="form-group">
                            {{ form_label(form.email) }}
                            {{ form_errors(form.email) }}
                            {{ form_widget(form.email, { attr: { 'style': 'color:#E00E40', disabled: true } }) }}
                        </div>
                        <div class="form-group">
                            {{ form_label(form.gender) }}
                            {{ form_errors(form.gender) }}
                            {{ form_widget(form.gender,{'attr':{'style': 'width:150px'}}) }}
                        </div>
                        <div class="form-group">
                            {{ form_label(form.birthday) }}
                            {{ form_errors(form.birthday) }}
                            {{ form_widget(form.birthday) }}
                        </div>
                        <div class="form-group">
                            {{ form_label(form.phone) }}
                            {{ form_errors(form.phone) }}
                            {{ form_widget(form.phone) }}
                        </div>
                        <div class="form-group">
                            {{ form_label(form.mobilephone) }}
                            {{ form_errors(form.mobilephone) }}
                            {{ form_widget(form.mobilephone) }}
                        </div>
                        <div class="form-group">
                            {{ form_label(form.zip) }}
                            {{ form_errors(form.zip) }}
                            {{ form_widget(form.zip) }}
                        </div>
                        <div class="form-group">
                            <h5><strong>Address：</strong></h5>
                            <input type="text" name="addressn" STYLE="width: 100%;height: 33px"
                                   value="{% if addressn is not null %} {{ addressn }}{% endif %}">
                            <br/>
                            <h5><strong>Address Line 2：</strong></h5>
                            <input type="text" name="addressLine" STYLE="width: 100%;height: 33px"
                                   value="{% if addressLine is not null %} {{ addressLine }}{% endif %}">
                            <br/><br/>
                            <div class="from-group">
                                {{ form_row(form.countryid,{'attr':{'style': 'width:150px'}}) }}
                            </div>
                            <div class="from-group">
                               <h5><strong>State:</strong></h5>
                               <select id="stateSelect" name="stateSelectName" style="width: 150px;background-color: white;height: 33px;">
                               {% if state  %}
                                <option value="stateid">{{ state.stateName }}</option>
                               {% endif %}
                                {% for key,value in states %}
                                 <option value="{{ key }}">{{ value }}</option>
                                {% endfor %}
                               </select>
                            </div>
                        </div>
                        <div class="form-group">
                            {{ form_label(form.profile_picture_file) }}
                            {{ form_errors(form.profile_picture_file) }}
                            {{ form_widget(form.profile_picture_file) }}
                        </div>

                        {% if user.isAdmin or user.isPlatformOwner %}
                            <div class="form-group">
                                <label for="form_demo_mode">Demo mode</label>
                                <div class="mv-3">
                                    <label for="form_demo_mode">
                                        <input type="checkbox" id="form_demo_mode"
                                            name="demo_mode" {% if user.demoAccount %} checked {% endif %} />
                                        <span class="normal">Demo mode</span>
                                    </label>
                                </div>
                            </div>
                        {% endif %}
                        <br/>
                        <div>
                            <button class="btn">Save</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <!--/row-->

        </p>
    </div>
    <!--/row-->
    <div class="tab-pane fade {% if errorInfo %} in active {% endif %}" id="security">
        <p>
        <div class="row-fluid">
            <div>
                <h2>Security</h2>
            </div>
        </div>
        <div class="row-fluid">
                  <div  id="failInfo"></div>
            <div>
                <form action="{{ path('admin_profile_modify_security', {'user_id':user_id}) }}" id="securityForm" method="post">
                    {#{{ form_widget(form) }}#}
                    <div class="box-body">
                        <div class="form-group">
                            {{ form_label(formsecurity.password) }}
                            {{ form_errors(formsecurity.password) }}
                            {{ form_widget(formsecurity.password) }}
                        </div>
                        <div class="form-group">
                            {{ form_row(formsecurity.plainPassword.first) }}
                            {{ form_row(formsecurity.plainPassword.second) }}
                        </div>

                        <div class="form-group">
                            <label for="form_restrict_ip">
                                <input type="checkbox" id="form_restrict_ip" name="restrict_ip" {% if user.needIpRestriction %} checked {% endif %} />
                                <span class="normal">Restrict my IP when logging in. (Please contact the system administrators to add your IP to the whitelist.)</span>
                            </label>
                        </div>

                        <button type="button" onclick="pwdResult()" class="btn">Save</button>
                    </div>
                </form>
            </div>
        </div>
        <!--/row-->
        </p>
    </div>
    {% if ipusage is defined %}
        <div class="tab-pane fade" id="ipUsage">
            <div class="row-fluid">
                <div>
                    <h2>IP Usage</h2>
                </div>
            </div>
            <div class="row-fluid">
                {% include '@Admin/User/partials/ipusage_content.html.twig' %}
            </div>
        </div>
    {% endif %}
    {% if FAVerifyType is defined %}
     <div class="tab-pane fade" id="verifyTypeRecord">
            <div class="row-fluid">
                <div>
                    <h2>Verify Type Info</h2>
                </div>
            </div>
            <div class="row-fluid">
             <div class="col-md-12">
                    Current Verify Type {{FAVerifyType}}
                    {% if FAVerifyType == 'app' %}
                     <form action="{{ path('admin_profile_reset_fa_verify_type', {'user_id':user_id}) }}" id="securityForm" method="post">
                    <div class="box-body">
                        <button type="submit" class="btn">Reset</button>
                    </div>
                </form>
                    {% endif %}
                </div>
            </div>
            <div class="row-fluid">
                {% include '@Admin/User/partials/verify_type_content.html.twig' %}
            </div>
        </div>
    {% endif %}
{% endblock %}
   {% block javascripts_inline %}
      <script>
        $(document).ready(function () {
          if ('{{ reload | default(false) | json_encode }}' === 'true' && window.parent && window.parent.location) {
            window.parent.location.reload()
            return;
          }

          if ($('#salex_user_profile_stateid').val()) {
            return;
          }
          var $country = $('#salex_user_profile_countryid');
          var $form = $(this).closest('form');
          var data = {};
//          console.log($form.attr('action'));
          data[$country.attr('name')] = $country.val();
          $.ajax({
            url: $form.attr('action'),
            type: 'post',
            data: data,
            success: function (html) {
//              console.log($(html).find('#salex_user_profile_stateid'));
              $('#stateSelect').replaceWith(
                $(html).find('#stateSelect')
              );
            }
          });
        })
        var $country = $('#salex_user_profile_countryid');
        $country.change(function () {
          var $form = $(this).closest('form');
          var data = {};
          data[$country.attr('name')] = $country.val();
//          console.log(data);
          $.ajax({
            url: $form.attr('action'),
            type: $form.attr('method'),
            data: data,
            success: function (html) {
              // Replace current position field ...
              $('#stateSelect').replaceWith(
                // ... with the returned one from the AJAX response.
                $(html).find('#stateSelect')
              );
              // Position field now displays the appropriate positions.
            }
          });
        });

      function checkOldPsd() {
        var oldPsd = $("#form_password").val();
        var newPsd = $("#form_plainPassword_first").val();
        var newPsd2 = $('#form_plainPassword_second').val();
        if (newPsd !== newPsd2) {
           $('#failInfo').html('<div class="alert alert-danger alert-dismissible">'
                    + 'New password must be equal!' +
                    '</div>');
            return;
           }
          $.ajax({
                type: "get",
                url: "{{ path('admin_check_oldPsd')}}",
                data: {"user_id":{{ user.id }},"oldPsd":oldPsd},
                success: function(data) {
                  if (data === 'true'){
                    $('#securityForm').submit();
                 } else {
                    $('#failInfo').html('<div class="alert alert-danger alert-dismissible">'
                    + 'Wrong original password!' +
                    '</div>');
                 }
                }
            });
      }
      function pwdResult() {
        var validateResult = checkPSD();
        if(!validateResult.isValid)
            {
                   $('#failInfo').html('<div class="alert alert-danger alert-dismissible">'
                    + validateResult.errorMesage +
                    '</div>');
            }else {
                $('#failInfo').html('');
                checkOldPsd();
            }
      }
        function checkPSD() {
            var newPSD1 = $('#form_plainPassword_first').val();
        var result ={
             isValid:true,
             errorMesage :null
         } ;
         if(newPSD1 && !ts.checkPassword(newPSD1)){
                     result.isValid = false;
                     result.errorMesage = "Password does not meet the requirements:<br\>" +
                             "<i class='fa fa-angle-right mr-10'></i>" + ts.invalidPwdMsg[0] + "<br\>" +
                             "<i class='fa fa-angle-right mr-10'></i>" + ts.invalidPwdMsg[1] + "<br\>";
                     return result;
         }
         return result;
        }
      </script>
      <!--/row-->
  {% endblock %}