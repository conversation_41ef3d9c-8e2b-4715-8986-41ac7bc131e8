{% extends 'layout/base-layout.html.twig' %}
{% block page_title %} {{ 'platform.title.view'|trans }} {% endblock %}


{% block page_content %}
    <!--/row-->
    <div class="row-fluid opera-box">
        {% for flashMessage in app.session.flashbag.get('success') %}
            <div class="alert alert-success">
                <button type="button" class="close" data-dismiss="alert">&times;</button>
                {{ flashMessage }}
            </div>
        {% endfor %}
        {% for flashMessage in app.session.flashbag.get('failure') %}
            {% if flashMessage %}
                <div class="alert alert-error">
                    <button type="button" class="close" data-dismiss="alert">&times;</button>
                    {{ flashMessage }}
                </div>
            {% endif %}
        {% endfor %}

        {% if editable(Module.ID_PLATFORM_MANAGEMENT) %}
            <div class="opera-right span3 pull-right">
                <button type="button" class="btn btn-success" id="new_kyc_provider"
                        onclick="window.location.href='{{ path('admin_platform_new') }}';">Create Platform</button>
            </div>
        {% endif %}
    </div>

    <div class="row-fluid">
        <table class="table table-striped table-bordered table-hover" id="platformTable">
            <thead>
            <tr>
                <th>{{ 'platform.column.name'|trans }}</th>
                <th width="100">{{ 'column.action'|trans }}</th>
            </tr>
            </thead>
            <tbody>
            {% for item in platforms %}
                <tr>
                    <td>{{ item.getName() }}</td>
                    <td>
                        <div class="btn-group">
                            <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">
                                {{ 'btn.actions'|trans }} <span class="caret"></span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-right">
                                {% if editable(Module.ID_PLATFORM_MANAGEMENT) %}
                                    <li><a href="{{ path('admin_platform_modify', {'id':item.getId()}) }}">Edit</a></li>
                                    <li>
                                        <a href="#" data-toggle="modal" data-target="#deletePlatformConfirmModal">Delete</a>
                                        <form action="{{ path('admin_platform_delete', {'id':item.getId()}) }}" method="post"></form>
                                    </li>
                                    <li><a href="{{ path('admin_affiliate_set', {'tenant':item.getId(),'return_url':'admin_kyc_provider', 'token':date()}) }}">{{ 'btn.setaffiliate'|trans }}</a></li>
                                    <li><a href="{{ path('admin_fee_item', {'entity_id':item.getId(), 'entity_type':'Platform'}) }}">{{ 'btn.feeitem'|trans }}</a></li>
                                {% endif %}
                                <li><a href="/admin/tenants/contacts/{{ item.id }}" target="_blank">Contacts</a></li>
                                <li><a href="/admin/tenants/{{ item.id }}/fee-options" target="_blank">Fee options</a></li>
                            </ul>
                        </div>
                    </td>
                </tr>
            {% endfor %}
            </tbody>
            Show
            <div class="btn-group">
                <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    Page <span class="caret"></span>
                </button>
                <ul class="dropdown-menu">
                    <li><a href="{{ path('admin_platform',{'limit':5}) }}">default</a></li>
                    <li><a href="{{ path('admin_platform',{'limit':10}) }}">10</a></li>
                    <li><a href="{{ path('admin_platform',{'limit':20 }) }}">20</a></li>
                    <li><a href="{{ path('admin_platform',{'limit':50 }) }}">50</a></li>
                </ul>
            </div>
            entries
        </table>
        <div class="opera-right span3 pull-right">
            {{ knp_pagination_render(platforms) }}
        </div>
    </div>
    <!--/row-->

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deletePlatformConfirmModal" tabindex="-1" role="dialog" aria-labelledby="deletePlatformConfirmModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="deletePlatformConfirmModalLabel">Delete Platform</h4>
                </div>
                <div class="modal-body">
                    Do you want to delete this Platform?
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger confirm-button">Confirm</button>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts_inline %}
    <script>
      // triggered when delete confirm modal is about to be shown
      $("#deletePlatformConfirmModal").on("show.bs.modal", function(e) {
        // Pass form reference to modal
        var form = $(e.relatedTarget).siblings('form');
        $(this).find(".confirm-button").data('form', form);
      });

      // action on confirm
      $("#deletePlatformConfirmModal .confirm-button").on("click", function(){
        $(this).data('form').submit();
      });

        $("#platformTable").DataTable({
            "paging": false,
            "lengthChange": false,
            "searching": false,
            "ordering": true,
            "info": true,
            "autoWidth": false
        });
    </script>
{% endblock %}