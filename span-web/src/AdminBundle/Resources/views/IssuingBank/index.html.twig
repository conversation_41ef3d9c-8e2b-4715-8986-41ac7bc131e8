{% extends 'layout/base-layout.html.twig' %}
{% block page_title %} {{ 'issuingbank.title.view'|trans }} {% endblock %}


{% block page_content %}
    <!--/row-->
    <div class="row-fluid opera-box">
        {% for flashMessage in app.session.flashbag.get('success') %}
            <div class="alert alert-success">
                <button type="button" class="close" data-dismiss="alert">&times;</button>
                {{ flashMessage }}
            </div>
        {% endfor %}
        {% for flashMessage in app.session.flashbag.get('failure') %}
            {% if flashMessage %}
                <div class="alert alert-error">
                    <button type="button" class="close" data-dismiss="alert">&times;</button>
                    {{ flashMessage }}
                </div>
            {% endif %}
        {% endfor %}

        {% if editable(Module.ID_ISSUING_BANK_MANAGEMENT) %}
        <div class="opera-right span3 pull-right">
            <button type="button" class="btn btn-success" id="new_issuingbank"
                    onclick="window.location.href='{{ path('admin_issuing_bank_new') }}';">
                {{ 'issuingbank.btn.create'|trans }}</button>
        </div>
        {% endif %}
    </div>

    <div class="row-fluid">
        <table class="table table-striped table-bordered table-hover" id="issuingBankTable">
            <thead>
            <tr>
                <th>{{ 'issuingbank.column.name'|trans }}</th>
                <th>{{ 'issuingbank.column.address'|trans }}</th>
                <th>{{ 'issuingbank.column.contact'|trans }}</th>
                <th>{{ 'issuingbank.column.email'|trans }}</th>
                <th>{{ 'column.action'|trans }}</th>
            </tr>
            </thead>
            <tbody>
            {% for item in issuingBanks %}
                <tr>
                    <td>{{ item.getName() }}</td>
                    <td>{{ item.getAddress() }}</td>
                    <td>{{ item.getContact().getFirstName()~' '~ item.getContact().getLastName()}}</td>
                    <td>{{ item.getContact().getEmail() }}</td>
                    <td>
                        {#Changed on 2017-02-27 with button group#}
                        <div class="btn-group">
                            <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">
                                {{ 'btn.actions'|trans }} <span class="caret"></span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-right">
                                {% if editable(Module.ID_ISSUING_BANK_MANAGEMENT) %}
                                    <li><a href="{{ path('admin_issuing_bank_modify', {'issuing_bank_id':item.getId()}) }}">{{ 'btn.modify'|trans }}</a></li>
                                    <li>
                                        <a href="#" data-toggle="modal" data-target="#deleteIssuingBankConfirmModal">{{ 'btn.delete'|trans }}</a>
                                        <form action="{{ path('admin_issuing_bank_delete',{'issuing_bank_id':item.getId() }) }}" method="post"></form>
                                    </li>
                                {% endif %}
                                <li><a href="/admin/tenants/contacts/{{ item.id }}" target="_blank">Contacts</a></li>
                                <li><a href="/admin/tenants/{{ item.id }}/fee-options" target="_blank">Fee options</a></li>
                                <li><a href="{{ path('admin_fee_item', {'entity_id':item.getId(), 'entity_type':'IssuingBank'}) }}">{{ 'btn.feeitem'|trans }}</a></li>
                            </ul>
                        </div>
                    </td>
                </tr>
            {% endfor %}
            </tbody>
            Show
            <div class="btn-group">
                <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    Page <span class="caret"></span>
                </button>
                <ul class="dropdown-menu">
                    <li><a href="{{ path('admin_issuing_bank',{'limit':5}) }}">default</a></li>
                    <li><a href="{{ path('admin_issuing_bank',{'limit':10}) }}">10</a></li>
                    <li><a href="{{ path('admin_issuing_bank',{'limit':20 }) }}">20</a></li>
                    <li><a href="{{ path('admin_issuing_bank',{'limit':50 }) }}">50</a></li>
                </ul>
            </div>
            entries
        </table>
        <div class="opera-right span3 pull-right">
            {{ knp_pagination_render(issuingBanks) }}
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteIssuingBankConfirmModal" tabindex="-1" role="dialog" aria-labelledby="deleteIssuingBankConfirmModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="deleteIssuingBankConfirmModalLabel">{{ 'issuingbank.label.delete'|trans }}</h4>
                </div>
                <div class="modal-body">
                    {{ 'issuingbank.msg.delete'|trans }}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">{{ 'btn.cancel'|trans }}</button>
                    <button type="button" class="btn btn-danger confirm-button">{{ 'btn.confirm'|trans }}</button>
                </div>
            </div>
        </div>
    </div>

    <!--/row-->
{% endblock %}

{% block javascripts_inline %}
    <script>
        // triggered when delete confirm modal is about to be shown
        $("#deleteIssuingBankConfirmModal").on("show.bs.modal", function(e) {
            // Pass form reference to modal
            var form = $(e.relatedTarget).siblings('form');
            $(this).find(".confirm-button").data('form', form);
        });

        // action on confirm
        $("#deleteIssuingBankConfirmModal .confirm-button").on("click", function(){
            $(this).data('form').submit();
        });

        //Add on 2017-02-23 replace with jquery data table
        $("#issuingBankTable").DataTable({
            "paging": false,
            "lengthChange": false,
            "searching": false,
            "ordering": true,
            "info": true,
            "autoWidth": false
//            "lengthMenu": [[20, 50, 100, -1], [20, 50, 100, "All"]],
//            "columnDefs": [ {
//                "targets": [4],
//                "orderable": false,
//                "searchable": false
//            }]
        });
    </script>
{% endblock %}