<?php
/**
 * User: Bob
 * Date: 2017/3/7
 * Time: 17:07
 */

namespace AdminBundle\Controller;


use AdminBundle\Form\Type\CardProgramType;
use AdminBundle\Response\FailedMsgResponse;
use CoreBundle\Constant\IdentityType;
use CoreBundle\Constant\ImageRequirement;
use CoreBundle\Constant\TenantType;
use CoreBundle\Entity\BrandPartner;
use CoreBundle\Entity\CardPrinter;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\CardProgramCardType;
use CoreBundle\Entity\CardProgramCountryKycIds;
use CoreBundle\Entity\CardProgramCountryLoadPartnerMethodCurrency;
use CoreBundle\Entity\CardProgramFeeItem;
use CoreBundle\Entity\CardProgramFeeRevenueShare;
use CoreBundle\Entity\CardType;
use CoreBundle\Entity\Config;
use CoreBundle\Entity\IssuingBank;
use CoreBundle\Entity\KycProvider;
use CoreBundle\Entity\Module;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\Processor;
use CoreBundle\Entity\Role;
use CoreBundle\Utils\Bundle;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Traits\ExcelTrait;
use CoreBundle\Utils\Util;
use Doctrine\ORM\EntityRepository;
use SalexUserBundle\Entity\User;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use CoreBundle\Attribute\Template;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Card Program Management
 * Class CardProgramController
 * @package AdminBundle\Controller
 */
class CardProgramController extends BaseController
{
    use ExcelTrait;

    /**
     * DefaultController constructor.
     */
    public function __construct()
    {
        parent::__construct();

        $this->authPermission(Module::ID_CARD_PROGRAM_LIST);
    }

    /**
     * @Route("/admin/card-program",name="admin_card_program")
     */
    #[Template()]
    public function indexAction()
    {
        $user = Util::user();
        $cardPrograms = $user->getOpenCardPrograms();
        return Array('cardPrograms'=> $cardPrograms,);
    }

    /**
     * @Route("/admin/card-program/feeitemlistajax",name="admin_card_program_feeitemlistajax")
     */
    #[Template()]
    public function feeitemlistajaxAction(Request $request){

        $cfeeitem=array();
        $em = $this->getDoctrine()->getManager();
        $cfeeitems=$em->getRepository(\CoreBundle\Entity\CardProgramFeeItem::class)->findAll();
        foreach ($cfeeitems as $item){
            if($item->getFeeItem()->getId()==$_GET['feeid'] and $item->getCardProgram()->getId()==$_GET['cardprogramid']){
                $cfeeitem=array('feetocusfixed'=>$item->getFeeToCusFixed(),
                    'feetocusratio'=>$item->getFeeToCusRatio(),
                    'feetobpfixed'=>$item->getFeeToBPFixed(),
                    'feetobpratio'=>$item->getFeeToBPRatio(),
                    );
            }
        }
        $provinces_json = json_encode($cfeeitem);
        exit($provinces_json);

    }

    /**
     * @Route("/admin/card-program/cardprogramcheckajax",name="admin_card_program_checkajax")
     */
    #[Template()]
    public function checkajaxAction(Request $request){

//        $cfeeitem=array();
        $em = $this->getDoctrine()->getManager();
        $cpt=$em->getRepository(\CoreBundle\Entity\CardProgramCardType::class)->find($_GET['cpt_id']);
                $cpts=array('is_auto_created'=>$cpt->getIsAutoCreated(),
                    'registrable' => $cpt->getRegistrable(),
                    'funding_account_numberx'=>$cpt->getFundingAccountNumber(),
                );
        $provinces_json = json_encode($cpts);
        exit($provinces_json);

    }

    /**
     * @Route("/admin/card-program/feeitemsharelistajax",name="admin_card_program_feeitemsharelistajax")
     */
    #[Template()]
    public function feeitemsharelistajaxAction(Request $request){

        $cfeeitem=array();
        $em = $this->getDoctrine()->getManager();
        $cfeeitemshares=$em->getRepository(\CoreBundle\Entity\CardProgramFeeRevenueShare::class)->findAll();
        foreach ($cfeeitemshares as $item){
            if($item->getFeeItem()->getId()==$_GET['feeid'] and $item->getCardProgram()->getId()==$_GET['cardprogramid']){
                $TenantName='';
                if($item->getTenantType()=='Brand Partner')
                {
                    $TenantName=$em->getRepository(\CoreBundle\Entity\BrandPartner::class)->find($item->getTenantId())->getName();
                }
                if($item->getTenantType()=='Marketing Partner')
                {
                    $TenantName=$em->getRepository(\CoreBundle\Entity\MarketingPartner::class)->find($item->getTenantId())->getName();
                }
                $cfeeitem[]=array('tenanttype'=>$item->getTenantType(),
                    'tenantid'=>$item->getTenantId(),
                    'tenantname'=>$TenantName,
                    'fixeds'=>$item->getShareFixed()/100,
                    'ratios'=>$item->getShareRatio(),
                );
            }
        }
        $provinces_json = json_encode($cfeeitem);
        exit($provinces_json);

    }

    /**
     * @Route("/admin/card-program/status",name="admin_card_program_status")
     */
    public function statusAction(Request $request)
    {
        $targetId = $request->get('targetId');
        $status = $request->get('status');
        $em = $this->getDoctrine()->getManager();
        $cardProgram = $em
            ->getRepository(\CoreBundle\Entity\CardProgram::class)
            ->find($targetId);
        $cardProgram->setStatus($status);
        $em->flush();
        return $this->redirect($this->generateUrl('admin_card_program'));
    }

    /**
     * @Route("/admin/card-program/cpajaxfeeshare",name="admin_card_program_ajaxfeeshare")
     */
    #[Template()]
    public function cpajaxfeeshareAction(Request $request)
    {
        $em = $this->getDoctrine()->getManager();
        $cpId = $request->get('cardprogram_id');
        $feeItemRevenueStrs = $request->get("fee_item_revenue_info");
        $targetcardProgram = $em->find(\CoreBundle\Entity\CardProgram::class, $cpId);
        $cardProgramFeeShare=$em->getRepository(\CoreBundle\Entity\CardProgramFeeRevenueShare::class)->findBy([
            'cardProgram' => $targetcardProgram,
        ]);
        foreach ($feeItemRevenueStrs as $feeItemRevenueStr) {
            $feeItemRevenues = json_decode($feeItemRevenueStr, true);
            foreach ($feeItemRevenues as $feeItemRevenue)
            {
                $record = $this->fieldonefeeshareclitem(
                    $cardProgramFeeShare,
                    $cpId,
                    $feeItemRevenue['feeItemId'],
                    $feeItemRevenue['tenantType'],
                    $feeItemRevenue['tenantId']
                );
                $tmpFeeItem = $em->find(\CoreBundle\Entity\FeeItem::class, $feeItemRevenue['feeItemId']);
                $feecur = $tmpFeeItem->getCostTernFixedCurrency();
                $Fixeds=Money::normalizeAmount($feeItemRevenue['fixed'],$feecur);
                if ($record) {
                    $record ->setTenantId($feeItemRevenue['tenantId']);
                    $record  ->setTenantType($feeItemRevenue['tenantType']);
                    $record ->setShareFixed($Fixeds);
                    $record  ->setShareRatio($feeItemRevenue['ratio']);
                } else {
                    $record = new CardProgramFeeRevenueShare();
                    $tmpFeeItem = $em->find(\CoreBundle\Entity\FeeItem::class, $feeItemRevenue['feeItemId']);
                    $record->setFeeItem($tmpFeeItem)
                        ->setCardProgram($targetcardProgram)
                        ->setTenantId($feeItemRevenue['tenantId'])
                        ->setTenantType($feeItemRevenue['tenantType'])
                        ->setShareFixed($Fixeds)
                        ->setShareRatio($feeItemRevenue['ratio']);
                    $em->persist($record);

                    $cardProgramFeeShare[] = $record;
                }
                $em->flush();
            }
        }
        exit('ok');
    }

    /**
     * @Route("/admin/card-program/cpajaxfeeitem",name="admin_card_program_ajaxfeeitem")
     */
    #[Template()]
    public function cpajaxfeeitemAction(Request $request)
    {
        $em = $this->getDoctrine()->getManager();
        $feeItems = $request->get("fee_item_detail_info");
        $targetcardProgram = $em->find(\CoreBundle\Entity\CardProgram::class, $_GET['cardprogram_id']);
        $cardProgramFeeShare=$em->getRepository(\CoreBundle\Entity\CardProgramFeeItem::class)->findBy([
            'cardProgram' => $targetcardProgram,
        ]);
        foreach ($feeItems as $feeStr)
        {
            $feeItem = json_decode($feeStr);
            $record = $this->fieldonefeeclitem($cardProgramFeeShare, $_GET['cardprogram_id'], $feeItem->{'id'});
            $tmpFeeItem = $em->find(\CoreBundle\Entity\FeeItem::class, $feeItem->{'id'});
            $feecur = $tmpFeeItem->getCostTernFixedCurrency();
            $Fixedcus=Money::normalizeAmount($feeItem->{'feeToCusFixed'},$feecur);
            $Fixedbp=Money::normalizeAmount($feeItem->{'feeToBPFixed'},$feecur);
            if ($record) {
                $record->setFeeToCusFixed($Fixedcus);
                $record->setFeeToCusRatio($feeItem->{'feeToCusRatio'});
                $record->setFeeToBPFixed($Fixedbp);
                $record->setFeeToBPRatio($feeItem->{'feeToBPRatio'});
                $record->setCardProgram($targetcardProgram);
            } else {
                $record = new CardProgramFeeItem();
                $tmpFeeItem = $em->find(\CoreBundle\Entity\FeeItem::class, $feeItem->{'id'});
                $record->setFeeItem($tmpFeeItem)
                    ->setFeeToCusFixed($Fixedcus)
                    ->setFeeToCusRatio($feeItem->{'feeToCusRatio'})
                    ->setFeeToBPFixed($Fixedbp)
                    ->setFeeToBPRatio($feeItem->{'feeToBPRatio'})
                    ->setCardProgram($targetcardProgram);
                $em->persist($record);
            }
            $em->flush();
        }
        exit('ok');
    }

    /**
     * @Route("/admin/card-program/cpajax",name="admin_card_program_ajax")
     */
    #[Template()]
    public function cpajaxAction(Request $request)
    {
        $country = $this->getDoctrine()->getRepository(\CoreBundle\Entity\Country::class)->find($_GET['county_id']);
        $cpId = $request->get('cardprogram_id');

        $cardProgramCountryLoad = $this->getDoctrine()->getRepository(\CoreBundle\Entity\CardProgramCountryLoadPartnerMethodCurrency::class)->findOneBy([
            'cardProgram' => $cpId,
        ]);
        $cardProgramCountryLoadx=$this->fieldcuntryitem($cardProgramCountryLoad, $cpId, $_GET['county_id']);

        $cardProgramCountryKyc = $this->getDoctrine()->getRepository(\CoreBundle\Entity\CardProgramCountryKycIds::class)->findBy([
            'cardProgram' => $cpId,
        ]);
        $cardProgramCountryKycx=$this->fieldcuntryitem($cardProgramCountryKyc, $cpId, $_GET['county_id']);
        return $this->render('@Admin/CardProgram/ajaxcardprogram.html.twig', ['cardProgramCountryLoad' => $cardProgramCountryLoadx,'cardProgramCountryKyc'=>$cardProgramCountryKycx,'cardprogram_id'=>$_GET['cardprogram_id'],'county_id'=>$_GET['county_id'],'country'=>$country]);
    }

    /**
     * @Route("/admin/card-program/feemoreajax",name="admin_card_program_feemoreajax")
     */
    #[Template()]
    public function feemoreajaxAction(Request $request)
    {
        $fees=array();
        $feeshares=array();
        $cpId = $request->get('cardprogram_id');
        $cardProgramfee = $this->getDoctrine()->getRepository(\CoreBundle\Entity\CardProgramFeeItem::class)->findBy([
            'cardProgram' => $cpId,
        ]);
        $cardProgramfeeshare = $this->getDoctrine()->getRepository(\CoreBundle\Entity\CardProgramFeeRevenueShare::class)->findBy([
            'cardProgram' => $cpId,
        ]);
        foreach ($cardProgramfee as $item) {
            if ($item->getFeeItem()->getId() == $_GET['fee_id'] && $item->getCardProgram()->getId() == $_GET['cardprogram_id']) {
                $fees[] = $item;
            }
        }
        foreach ($cardProgramfeeshare as $sitem) {
            if ($sitem->getFeeItem()->getId() == $_GET['fee_id'] && $sitem->getCardProgram()->getId() == $_GET['cardprogram_id']) {
                $feeshares[] = $sitem;
            }
        }

        return $this->render('@Admin/CardProgram/ajaxfeemore.html.twig', ['cardProgramFeemore' => $fees,'cardProgramFeesharemore'=>$feeshares]);
    }

    /**
     * @Route("/admin/card-program/cpcuntryajax",name="admin_card_program_cuntryajax")
     */
    #[Template()]
    public function cpcuntryajaxAction(Request $request)
    {
        $countryids=array();
        $countries = $this->getDoctrine()->getRepository(\CoreBundle\Entity\Country::class)->findBy([], [
            'name' => 'asc',
        ]);
//        $cardProgram = $this->getDoctrine()->getRepository(\CoreBundle\Entity\CardProgram::class)->findOneBy(array("id" =>$_GET['cardprogram_id']));
//        $cpcpm = $this->getDoctrine()->getRepository(\CoreBundle\Entity\CardProgramCountryLoadPartnerMethodCurrency::class)->findAll();
//        $cpcki = $this->getDoctrine()->getRepository(\CoreBundle\Entity\CardProgramCountryKycIds::class)->findAll();
//        foreach ($cpcpm as $item)
//        {
//            if($item->getCardProgram()){
//               if($item->getCardProgram()->getId()==$_GET['cardprogram_id']){
//                   if(!in_array($item->getCountry()->getId(),$countryids)){
//                      $countryids[]=$item->getCountry()->getId();
//                  }
//               }
//            }
//        }
//        foreach ($cpcki as $item)
//        {
//            if($item->getCardProgram()){
//                if($item->getCardProgram()->getId()==$_GET['cardprogram_id']){
//                    if(!in_array($item->getCountry()->getId(),$countryids)){
//                        $countryids[]=$item->getCountry()->getId();
//                    }
//                }
//            }
//        }
//        foreach ($countryids as $item)
//        {
//            if($item->getCardProgram()){
//                if($item->getCardProgram()->getId()==$_GET['cardprogram_id']){
//                    if(!in_array($item->getCountry()->getId(),$countryids)){
//                        $countryids[]=$item->getCountry()->getId();
//                    }
//                }
//            }
//        }

        return $this->render('@Admin/CardProgram/new.html.twig', [
            'countries' =>$countries,
            'cardprogram_id'=>$_GET['cardprogram_id'],
        ]);
    }

    /**
     * @Route("/admin/card-program/cpfeeajax",name="admin_card_program_feeajax")
     */
    #[Template()]
    public function cpfeeajaxAction(Request $request)
    {
        $feeItems = $this->getDoctrine()->getRepository(\CoreBundle\Entity\FeeItem::class)->findAll();
        return $this->render('@Admin/CardProgram/ajaxfee.html.twig', [
            'feeItems' => $feeItems,
            'cardprogram_id'=>$_GET['cardprogram_id'],
        ]);
    }
    /**
     * @Route("/admin/card-program/cuntryfinshajax",name="admin_card_program_cuntryfinshajax")
     */
    #[Template()]
    public function cuntryfinshajaxAction(Request $request)
    {
        $em = $this->getDoctrine()->getManager();
        $cardProgram = $em->getRepository(\CoreBundle\Entity\CardProgram::class)->findOneBy(array("id" => $_GET['cardprogram_id']));
        return $this->render('@Admin/CardProgram/cuntryfinsh.html.twig', [
            'countries' => empty($cardProgram->getCountries())?null:$cardProgram->getCountries(),
            'cardprogram_id'=>$_GET['cardprogram_id'],
        ]);
    }
    /**
     * @Route("/admin/card-program/feefinshajax",name="admin_card_program_feefinshajax")
     */
    #[Template()]
    public function feefinshajaxAction(Request $request)
    {
        $em = $this->getDoctrine()->getManager();
        $cpId = $request->get('cardprogram_id');
        $cardProgramFeeI = $this->getDoctrine()->getRepository(\CoreBundle\Entity\CardProgramFeeItem::class)->findBy([
            'cardProgram' => $cpId,
        ]);
        $cardProgramFeeIx=$this->fielditem($cardProgramFeeI,$_GET['cardprogram_id']);
        if(!empty($cardProgramFeeIx)) {
            foreach ($cardProgramFeeIx as $feeitem) {
                $cardProgramFeeItem[] = $em->getRepository(\CoreBundle\Entity\FeeItem::class)->findOneBy(array("id" => $feeitem->getFeeItem()->getId()));
            }
        }
        else
            $cardProgramFeeItem[]=$em->getRepository(\CoreBundle\Entity\FeeItem::class)->findOneBy(array("id" =>'-1'));
        return $this->render('@Admin/CardProgram/feefinsh.html.twig', [
            'feeItems' => $cardProgramFeeIx,
            'cardprogram_id'=>$_GET['cardprogram_id'],
        ]);
    }

    /**
     * @Route("/admin/card-program/cardtypefinshajax",name="admin_card_program_cardtypefinshajax")
     */
    #[Template()]
    public function cardtypefinshajaxAction(Request $request)
    {
        $em = $this->getDoctrine()->getManager();
        $cardProgram = $em->getRepository(\CoreBundle\Entity\CardProgram::class)->find($_GET['cardprogram_id']);
        $cardTypeProgramsx = $em->getRepository(\CoreBundle\Entity\CardProgramCardType::class)->findBy(['cardProgram'=>$cardProgram]);
        return $this->render('@Admin/CardProgram/cardtypefinsh.html.twig', [
            'cardTypePrograms'=> $cardTypeProgramsx,
            'cardprogram_id'=>$_GET['cardprogram_id'],
        ]);
    }

    /**
     * @Route("/admin/card-program/cardTypeProgram",name="admin_modify_cardTypeProgram")
     */
    public function cardTypeProgramAction(Request $request)
    {
//
        $field=$_POST['id'];  //获取前端提交的字段名
        $val=$_POST['value']; //获取前端提交的字段对应的内容
        $val = htmlspecialchars($val, ENT_QUOTES);
        $em = $this->getDoctrine()->getManager();
        $cpId = $request->get('cardprogram_id');
        //$cardTypePrograms = $em->getRepository(\CoreBundle\Entity\CardProgramCardType::class)->findOneBy(array("cardProgramId" => $request->get("cardprogram_id")));
        $cardTypeProgram = $em->getRepository(\CoreBundle\Entity\CardProgramCardType::class)->findBy([
            'cardProgram' => $cpId,
        ]);
        $cardTypeProgramsx=$this->fieldoneitem($cardTypeProgram,$request->get("cardprogram_id"));
        if ($request->isMethod('POST')) {
            $cardTypeProgramsx->$field($val);
            $em->persist($cardTypeProgramsx);
            $em->flush();
            if('setIsAutoCreated'==$field)
            {
                if (0==$val)
                    exit('NO');
                else
                    exit('YES');
            }else {
                exit($val);
            }
        }
    }

    /**
     * @Route("/admin/card-program/cardProgramKycI",name="admin_modify_cardProgramKycI")
     */
    public function cardProgramKycIAction(Request $request)
    {
        $field=$_POST['id'];  //获取前端提交的字段名
        $val=$_POST['value']; //获取前端提交的字段对应的内容
        $fields=explode('@',$field);
        $fieldx=$fields[0];
        $val = htmlspecialchars($val, ENT_QUOTES);
        $em = $this->getDoctrine()->getManager();
        //$cardProgramKycI = $em->getRepository(\CoreBundle\Entity\CardProgramCountryKycIds::class)->findOneBy(array("cardProgramId" => $request->get("cardprogram_id"),"identityType" =>$fields[1]));
        $cardProgramKycI = $em->getRepository(\CoreBundle\Entity\CardProgramCountryKycIds::class)->findBy(array("identityType" =>$fields[1]));
        $cardProgramKycIx=$this->fieldcuntryoneitem($cardProgramKycI,$request->get("cardprogram_id"),$request->get("amp;county_id"));
        if ($request->isMethod('POST')) {
            $cardProgramKycIx->$fieldx($val);
            $em->persist($cardProgramKycIx);
            $em->flush();
            if('setEnabled'==$fieldx)
            {
                if (0==$val)
                    exit('NO');
                else
                    exit('YES');
            }else {
                exit($val);
            }
        }
    }

    /**
     * @Route("/admin/card-program/cardProgramLoad",name="admin_modify_cardProgramLoad")
     */
    public function cardProgramLoadAction(Request $request)
    {
        $field=$_POST['id'];  //获取前端提交的字段名
        $val=$_POST['value']; //获取前端提交的字段对应的内容
        $fields=explode('@',$field);
        $fieldx=$fields[0];
        $val = htmlspecialchars($val, ENT_QUOTES);
        $em = $this->getDoctrine()->getManager();
        $cpId = $request->get('cardprogram_id');
       // $cardProgramLoad = $em->getRepository(\CoreBundle\Entity\CardProgramCountryLoadPartnerMethodCurrency::class)->findOneBy(array("cardProgramId" => $request->get("cardprogram_id"),"loadPartnerId" =>$fields[1],"loadMethodId" =>$fields[2]));
        $cardProgramLoad=$em->getRepository(\CoreBundle\Entity\CardProgramCountryLoadPartnerMethodCurrency::class)->findBy([
            'cardProgram' => $cpId,
        ]);
        $cardProgramLoadx=$this->fieldonecpclitem($cardProgramLoad,$request->get("cardprogram_id"),$fields[1],$fields[2],$request->get("amp;county_id"));
        if ($request->isMethod('POST')) {
            $cardProgramLoadx->$fieldx($val);
            $em->persist($cardProgramLoadx);
            $em->flush();
            if('setEnabled'==$fieldx)
            {
                if (0==$val)
                    exit('NO');
                else
                    exit('YES');
            }else {
                exit($val);
            }
        }
    }

    /**
     * @Route("/admin/card-program/cardProgramFeeI",name="admin_modify_cardProgramFeeI")
     */
    public function cardProgramFeeIAction(Request $request)
    {
        $field=$_POST['id'];  //获取前端提交的字段名
        $val=$_POST['value']; //获取前端提交的字段对应的内容
        $fields=explode('@',$field);
        $fieldx=$fields[0];
        $val = htmlspecialchars($val, ENT_QUOTES);
        $em = $this->getDoctrine()->getManager();
        $cardProgramLoad=$em->getRepository(\CoreBundle\Entity\CardProgramFeeItem::class)->findOneBy(array("id" =>$fields[1]));
        if ($request->isMethod('POST')) {
            $cardProgramLoad->$fieldx($val);
            $em->persist($cardProgramLoad);
            $em->flush();
            if('setEnabled'==$fieldx)
            {
                if (0==$val)
                    exit('NO');
                else
                    exit('YES');
            }else {
                exit($val);
            }
        }
    }

    /**
     * @Route("/admin/card-program/cardProgramFeeR",name="admin_modify_cardProgramFeeR")
     */
    public function cardProgramFeeRAction(Request $request)
    {
        $field=$_POST['id'];  //获取前端提交的字段名
        $val=$_POST['value']; //获取前端提交的字段对应的内容
        $fields=explode('@',$field);
        $fieldx=$fields[0];
        $val = htmlspecialchars($val, ENT_QUOTES);
        $em = $this->getDoctrine()->getManager();
        $cardProgramLoad=$em->getRepository(\CoreBundle\Entity\CardProgramFeeRevenueShare::class)->findOneBy(array("id" =>$fields[1]));
        if ($request->isMethod('POST')) {
            $cardProgramLoad->$fieldx($val);
            $em->persist($cardProgramLoad);
            $em->flush();
            if('setEnabled'==$fieldx)
            {
                if (0==$val)
                    exit('NO');
                else
                    exit('YES');
            }else {
                exit($val);
            }
        }
    }

    /**
     * @Route("/admin/card-program/modify",name="admin_card_program_modify")
     */
    #[Template()]
    public function modifyAction(Request $request)
    {
        $em = $this->getDoctrine()->getManager();
        $cpId = $request->get('cardprogram_id');
        $cardProgram = $em->getRepository(\CoreBundle\Entity\CardProgram::class)->findOneBy(array("id" => $request->get("cardprogram_id")));

        $cardProgramFeeI = $this->getDoctrine()->getRepository(\CoreBundle\Entity\CardProgramFeeItem::class)->findBy([
            'cardProgram' => $cpId,
        ]);
        $cardProgramFeeItem = [];
        $cardProgramFeeIx=$this->fielditem($cardProgramFeeI,$request->get("cardprogram_id"));
        if(!empty($cardProgramFeeIx)) {
            foreach ($cardProgramFeeIx as $feeitem) {
                $cardProgramFeeItem[] = $em->getRepository(\CoreBundle\Entity\FeeItem::class)->findOneBy(array("id" => $feeitem->getFeeItem()->getId()));
            }
        }
       else
           $cardProgramFeeItem[]=$em->getRepository(\CoreBundle\Entity\FeeItem::class)->findOneBy(array("id" =>'-1'));
        $cardProgramFeeIxs=$this->fielditem($cardProgramFeeI,$request->get("cardprogram_id"));

        $cardProgramFeeR = $this->getDoctrine()->getRepository(\CoreBundle\Entity\CardProgramFeeRevenueShare::class)->findBy([
            'cardProgram' => $cpId,
        ]);
        $cardProgramFeeRx=$this->fielditem($cardProgramFeeR,$request->get("cardprogram_id"));

        if (!$cardProgram) {
            throw $this->createNotFoundException('No cardPrograms found for id ' . $request->get("cardprogram_id"));
        }
        if ($cardProgram) {
            $disabled = !Util::user()->authEditable(Module::ID_CARD_PROGRAM_LIST);
            $form = $this->createForm(CardProgramType::class, $cardProgram);
            $formname = $this->createFormBuilder($cardProgram)
                ->add('name',TextType::class, array(
                    'label' => 'Card Program Name',
                    'required' => true,
                    'disabled' => $disabled,
                    )
                )
                ->getForm();
            $formbt = $this->createFormBuilder($cardProgram)
                ->add('platform', EntityType::class, array(
                    'label'              => 'Platform',
                    'translation_domain' => 'forms',
                    "class" => \CoreBundle\Entity\Platform::class,
                    'choice_label'       => 'name',
                    'multiple'           => false,
                    'expanded'           => false,
                    'required'           => true,
                    'disabled'           => $disabled,
                    'query_builder'      => [CardProgramType::class, 'platformQueryBuilder'],
                    'attr' => array(
                        'readonly' => (bool)Util::platform(),
                    ),
                ))
                ->add('brandPartner', EntityType::class, array(
                    'label'              => 'Brand Partner',
                    'translation_domain' => 'forms',
                    "class" => \CoreBundle\Entity\BrandPartner::class,
                    'choice_label'       => 'name',
                    'multiple'           => false,
                    'expanded'           => false,
                    'required'           => true,
                    'disabled'           => $disabled,
                    'query_builder'      => [CardProgramType::class, 'brandPartnerQueryBuilder'],
                ))
                ->add('programOwner', EntityType::class, array(
                    'label'              => Role::ROLE_PROGRAM_OWNER,
                    'translation_domain' => 'forms',
                    'class'              => User::class,
                    'choice_label'       => 'firstName',
                    'multiple'           => false,
                    'expanded'           => false,
                    'required'           => true,
                    'disabled'           => $disabled,
                    'query_builder'      => [CardProgramType::class, 'programOwnerQueryBuilder'],
                ))
                ->add('isCoBrand', CheckboxType::class,array('label' => 'Need Co-Brand', 'required'=>false, 'disabled' => $disabled,))
                ->add('marketingPartner', EntityType::class, array(
                    'label'              => 'Marketing Partner',
                    'translation_domain' => 'forms',
                    "class" => \CoreBundle\Entity\MarketingPartner::class,
                    'choice_label'       => 'name',
                    'multiple'           => false,
                    'expanded'           => false,
                    'empty_data'         => null,
                    'required'           => false,
                    'disabled'           => $disabled,
                ))
                ->add('kycProvider', EntityType::class, array(
                    'label'              => 'KYC Provider',
                    'translation_domain' => 'forms',
                    "class" => \CoreBundle\Entity\KycProvider::class,
                    'choice_label'       => 'name',
                    'multiple'           => false,
                    'expanded'           => false,
                    'empty_data'         => null,
                    'required'           => false,
                    'disabled'           => $disabled,
                    'query_builder'      => [CardProgramType::class, 'kycProviderQueryBuilder'],
                ))
                ->add('kycProviderProgram', TextType::class, array(
                    'label'    => 'KYC Provider Program Id',
                    'required' => false,
                    'disabled' => $disabled,
                ))
                ->add('wallet', TextType::class, array(
                    'label'    => 'Sub Wallet/Company',
                    'required' => false,
                    'disabled' => $disabled,
                ))
                ->add('walletApiKey', TextType::class, array(
                    'label'    => 'Wallet/Company API Key',
                    'required' => false,
                    'disabled' => $disabled,
                ))
                ->add('onDemandPrinting', CheckboxType::class, array(
                    'label'    => 'Enable OnDemand Printing',
                    'required' => false,
                    'disabled' => $disabled,
                ))
                ->add('cardPrinter', EntityType::class, array(
                    'label'              => 'Card Printer',
                    'class'              => CardPrinter::class,
                    'choice_label'       => 'name',
                    'required'           => false,
                    'disabled'           => $disabled,
                ))
                ->add('packageId', TextType::class, array(
                    'label'    => 'Package Id',
                    'required' => false,
                    'disabled' => $disabled,
                ))
                ->add('subBin', TextType::class, array(
                    'label'    => 'Sub BIN',
                    'required' => false,
                    'disabled' => $disabled,
                ))
                ->add('currency', ChoiceType::class, array(
                    'label'    => 'Currency',
                    'choices'  => [
                        'USD' => 'USD',
                        'EUR' => 'EUR',
                        'GBP' => 'GBP',
                    ],
                    'required' => false,
                    'disabled' => $disabled,
                ))
                ->add('issuingBank', EntityType::class, array(
                    'label'              => 'Issuing Bank',
                    'translation_domain' => 'forms',
                    'class'              => IssuingBank::class,
                    'choice_label'       => 'name',
                    'multiple'           => false,
                    'expanded'           => false,
                    'empty_data'         => null,
                    'required'           => true,
                    'disabled'           => $disabled,
                    'query_builder'      => [CardProgramType::class, 'issuingBankQueryBuilder'],
                ))
                ->add('processor', EntityType::class, array(
                    'label'              => 'Processor',
                    'translation_domain' => 'forms',
                    'class'              => Processor::class,
                    'choice_label'       => 'name',
                    'multiple'           => false,
                    'expanded'           => false,
                    'empty_data'         => null,
                    'required'           => true,
                    'disabled'           => $disabled,
                    'query_builder'      => [CardProgramType::class, 'processorQueryBuilder'],
                ))
                ->add('isPortalUse', CheckboxType::class,array('label' => 'Use proprietary portal', 'required'=>false, 'disabled' => $disabled,))
                //Begin Add by Bob Wen Bao on 2017-04-17 to move cards allowed from card type level to card program level
                ->add('cardsAllowed', IntegerType::class, array('label' => 'Numbers of Cards Allowed', 'disabled' => $disabled,))
                //End
                ->getForm();

            $formreshipper = $this->createFormBuilder($cardProgram)
                ->add('hasCustomerAddress', CheckboxType::class,array('label' => 'Support custom address', 'required'=>false, 'disabled' => $disabled,))
                ->add('reshipper', EntityType::class, array(
                    'label'              => 'Reshipper',
                    'translation_domain' => 'forms',
                    "class" => \CoreBundle\Entity\Reshipper::class,
                    'query_builder'      => function (EntityRepository $er) {
                        return $er->createQueryBuilder('s')->where('s.dba <> \'Custom\'');
                    },
                    'choice_label'       => 'name',
                    'multiple'           => true,
                    'expanded'           => true,
                    'required'           => true,
                    'disabled'           => $disabled,
                ))
                ->getForm();

            $formaddons = $this->createFormBuilder($cardProgram)
                ->add('service', EntityType::class, array(
                    'label'              => 'Add-ons',
                    'translation_domain' => 'forms',
                    "class" => \CoreBundle\Entity\ServiceManager::class,
                    'choice_label'       => 'name',
                    'multiple'           => true,
                    'expanded'           => true,
                    'required'           => true,
                    'disabled'           => $disabled,
                ))
                ->getForm();
            $formemail = $this->createFormBuilder($cardProgram)
                ->add('support_email_value',TextType::class,array('label' => 'Support Email Value', 'disabled' => $disabled,))
                ->add('live_chat_url_value',TextType::class,array('label' => 'Live Chat Url Value', 'disabled' => $disabled,))
                ->add('sender_name',TextType::class,array('label' => 'Sender Name', 'disabled' => $disabled,))
                ->add('product_name',TextType::class,array('label' => 'Product Name', 'disabled' => $disabled,))
                ->add('company_name',TextType::class,array('label' => 'Company Name', 'disabled' => $disabled,))
                ->add('htmlLogo',TextareaType::class,array('label' => 'Logo HTML', 'disabled' => $disabled,))
                ->add('html_address',TextareaType::class,array('label' => 'Address HTML', 'disabled' => $disabled,))
                ->add('text_address',TextareaType::class,array('label' => 'Address Text', 'disabled' => $disabled,))
                ->getForm();

            $cardTypeForm = $this->buildCardTypeDetailForm();

            if ($request->isMethod('POST')) {
                $form->handleRequest($request);
                $formname->handleRequest($request);
                $em->persist($cardProgram);
                $em->flush();

                return $this->redirect($this->generateUrl('admin_card_program_modify',array('cardprogram_id'=>$request->get("cardprogram_id"))));
            }
        }

        return Array(
            'cardProgram'=> $cardProgram,
            'cardTypePrograms'=> $cardProgram->getCardProgramCardType(),
            'cardprogram_id' =>  $request->get("cardprogram_id"),
            'form' => $form->createView(),
            'formname' => $formname->createView(),
            'formbt' => $formbt->createView(),
            'formreshipper' => $formreshipper->createView(),
            'formaddons' => $formaddons->createView(),
            'cardTypeForm' => $cardTypeForm->createView(),
            'formemail' => $formemail->createView(),
            'countries' => empty($cardProgram->getCountries())?null:$cardProgram->getCountries(),
//            'cardProgramCountryLoad' => $cardProgramCountryLoadx,
//            'cardProgramCountryKyc' => $cardProgramCountryKycx,
            'feeItems' => $cardProgramFeeItem,
            'cardProgramFeeIxs' => $cardProgramFeeIxs,
            'brandpartner'=>$cardProgram->getBrandPartner(),
//            'county_id'=>$cuntryid,
            'cardProgramFeeRx'=>$cardProgramFeeRx,
            'languages' => Config::array(Config::CONFIG_LANGUAGES),
            'cpLanguages' => $cardProgram->getLanguages(),
            );
    }

    /**
     * @Route("/admin/card-program/modifyado",name="admin_card_program_modifyado")
     */
    #[Template()]
    public function modifyadoAction(Request $request){
        $em = $this->getDoctrine()->getManager();
        $cpId = $request->get('cardprogram_id');
        $cardProgram = $em->getRepository(\CoreBundle\Entity\CardProgram::class)->findOneBy(array("id" => $request->get("cardprogram_id")));
        $cardProgramService = $em->getRepository(\CoreBundle\Entity\CardProgramService::class)->findBy([
            'cardProgram' => $cpId,
        ]);
        $cardProgramServicex=$this->fielditem($cardProgramService,$request->get("cardprogram_id"));
        foreach ($cardProgramServicex as $serveritem)
        {
            $em->remove($serveritem);
            $em->flush();
        }

        $formaddons = $this->createFormBuilder($cardProgram)
            ->add('service', EntityType::class, array(
                'label'              => 'Add-ons',
                'translation_domain' => 'forms',
                "class" => \CoreBundle\Entity\ServiceManager::class,
                'choice_label'       => 'name',
                'multiple'           => true,
                'expanded'           => true,
                'required'           => true,
            ))
            ->getForm();
        if ($request->isMethod('POST')) {
            $formaddons->handleRequest($request);
            $em->persist($cardProgram);
            $em->flush();
            return $this->redirect($this->generateUrl('admin_card_program_modify',array('cardprogram_id'=>$request->get("cardprogram_id"))));
        }
    }

    /**
     * @Route("/admin/card-program/modifyrs",name="admin_card_program_modifyrs")
     */
    #[Template()]
    public function modifyrsAction(Request $request){
        $em = $this->getDoctrine()->getManager();
        $cardProgram = $em->getRepository(\CoreBundle\Entity\CardProgram::class)->findOneBy(array("id" => $request->get("cardprogram_id")));
        $formreshipper = $this->createFormBuilder($cardProgram)
            ->add('hasCustomerAddress', CheckboxType::class,array('label' => 'Support custom address', 'required'=>false))
            ->add('reshipper', EntityType::class, array(
                'label'              => 'Reshipper',
                'translation_domain' => 'forms',
                "class" => \CoreBundle\Entity\Reshipper::class,
                'query_builder'      => function (EntityRepository $er) {
                    return $er->createQueryBuilder('s')->where('s.dba <> \'Custom\'');
                },
                'choice_label'       => 'name',
                'multiple'           => true,
                'expanded'           => true,
                'required'           => true,
            ))
            ->getForm();
        if ($request->isMethod('POST')) {
            $formreshipper->handleRequest($request);

            $em->persist($cardProgram);
            $em->flush();
            return $this->redirect($this->generateUrl('admin_card_program_modify',array('cardprogram_id'=>$request->get("cardprogram_id"))));
        }
    }

    /**
     * @Route("/admin/card-program/modifybt",name="admin_card_program_modifybt")
     */
    #[Template()]
    public function modifybtAction(Request $request){
        $em = $this->getDoctrine()->getManager();
        $cardProgram = $em->getRepository(\CoreBundle\Entity\CardProgram::class)->findOneBy(array("id" => $request->get("cardprogram_id")));
        $formbt = $this->createFormBuilder($cardProgram)
            ->add('platform', EntityType::class, array(
                'label'              => 'Platform',
                'translation_domain' => 'forms',
                "class" => \CoreBundle\Entity\Platform::class,
                'choice_label'       => 'name',
                'multiple'           => false,
                'expanded'           => false,
                'required'           => true,
                'query_builder'      => [CardProgramType::class, 'platformQueryBuilder'],
                'attr' => array(
                    'readonly' => (bool)Util::platform(),
                ),
            ))
            ->add('brandPartner', EntityType::class, array(
                'label'              => 'Brand Partner',
                'translation_domain' => 'forms',
                "class" => \CoreBundle\Entity\BrandPartner::class,
                'choice_label'       => 'name',
                'multiple'           => false,
                'expanded'           => false,
                'required'           => true,
                'query_builder'      => [CardProgramType::class, 'brandPartnerQueryBuilder'],
            ))
            ->add('programOwner', EntityType::class, array(
                'label'              => Role::ROLE_PROGRAM_OWNER,
                'translation_domain' => 'forms',
                'class'              => User::class,
                'choice_label'       => 'firstName',
                'multiple'           => false,
                'expanded'           => false,
                'required'           => true,
                'query_builder'      => [CardProgramType::class, 'programOwnerQueryBuilder'],
            ))
            ->add('isCoBrand', CheckboxType::class,array('label' => 'Need Co-Brand', 'required'=>false))
            ->add('marketingPartner', EntityType::class, array(
                'label'              => 'Marketing Partner',
                'translation_domain' => 'forms',
                "class" => \CoreBundle\Entity\MarketingPartner::class,
                'choice_label'       => 'name',
                'multiple'           => false,
                'expanded'           => false,
                'empty_data'         => null,
                'required'           => false,
            ))
            ->add('kycProvider', EntityType::class, array(
                'label'              => 'KYC Provider',
                'translation_domain' => 'forms',
                "class" => \CoreBundle\Entity\KycProvider::class,
                'choice_label'       => 'name',
                'multiple'           => false,
                'expanded'           => false,
                'empty_data'         => null,
                'required'           => false,
                'query_builder'      => [CardProgramType::class, 'kycProviderQueryBuilder'],
            ))
            ->add('kycProviderProgram', TextType::class, array(
                'label'    => 'KYC Provider Program Id',
                'required' => false,
            ))
            ->add('wallet', TextType::class, array(
                'label'    => 'Sub Wallet/Company',
                'required' => false,
            ))
            ->add('walletApiKey', TextType::class, array(
                'label'    => 'Wallet/Company API Key',
                'required' => false,
            ))
            ->add('onDemandPrinting', CheckboxType::class, array(
                'label'    => 'Enable OnDemand Printing',
                'required' => false,
            ))
            ->add('cardPrinter', EntityType::class, array(
                'label'              => 'Card Printer',
                'class'              => CardPrinter::class,
                'choice_label'       => 'name',
                'required'           => false,
            ))
            ->add('packageId', TextType::class, array(
                'label'    => 'Package Id',
                'required' => false,
            ))
            ->add('subBin', TextType::class, array(
                'label'    => 'Sub BIN',
                'required' => false,
            ))
            ->add('currency', ChoiceType::class, array(
                'label'    => 'Currency',
                'choices'  => [
                    'USD' => 'USD',
                    'EUR' => 'EUR',
                    'GBP' => 'GBP',
                ],
                'required' => false,
            ))
            ->add('issuingBank', EntityType::class, array(
                'label'              => 'Issuing Bank',
                'translation_domain' => 'forms',
                'class'              => IssuingBank::class,
                'choice_label'       => 'name',
                'multiple'           => false,
                'expanded'           => false,
                'empty_data'         => null,
                'required'           => true,
                'query_builder'      => [CardProgramType::class, 'issuingBankQueryBuilder'],
            ))
            ->add('processor', EntityType::class, array(
                'label'              => 'Processor',
                'translation_domain' => 'forms',
                'class'              => Processor::class,
                'choice_label'       => 'name',
                'multiple'           => false,
                'expanded'           => false,
                'empty_data'         => null,
                'required'           => true,
                'query_builder'      => [CardProgramType::class, 'processorQueryBuilder'],
            ))
            ->add('isPortalUse', CheckboxType::class,array('label' => 'Use proprietary portal', 'required'=>false))
            //Begin Add by Bob Wen Bao on 2017-04-17 to move cards allowed from card type level to card program level
            ->add('cardsAllowed', IntegerType::class, array('label' => 'Numbers of Cards Allowed'))
            //End
            ->getForm();
        if ($request->isMethod('POST')) {
            $formbt->handleRequest($request);

            $cardProgram->setBin($request->get('bin'));
            $cardProgram->setDeliveryMethod($request->get('deliveryMethod'));
            $em->persist($cardProgram);
            $em->flush();
            return $this->redirect($this->generateUrl('admin_card_program_modify',array('cardprogram_id'=>$request->get("cardprogram_id"))));
                }
    }

    /**
     * @Route("/admin/card-program/modifyemail",name="admin_card_program_modifyemail")
     */
    #[Template()]
    public function modifyemailAction(Request $request){
        $em = $this->getDoctrine()->getManager();
        $cardProgram = $em->getRepository(\CoreBundle\Entity\CardProgram::class)->findOneBy(array("id" => $request->get("cardprogram_id")));
        $formemail = $this->createFormBuilder($cardProgram)
            ->add('support_email_value',TextType::class,array('label' => 'Support Email Value'))
            ->add('live_chat_url_value',TextType::class,array('label' => 'Live Chat Url Value'))
            ->add('sender_name',TextType::class,array('label' => 'Sender Name'))
            ->add('product_name',TextType::class,array('label' => 'Product Name'))
            ->add('company_name',TextType::class,array('label' => 'Company Name'))
            ->add('htmlLogo',TextareaType::class,array('label' => 'Logo HTML'))
            ->add('html_address',TextareaType::class,array('label' => 'Address HTML'))
            ->add('text_address',TextareaType::class,array('label' => 'Address Text'))
            ->getForm();
        if ($request->isMethod('POST')) {
            $formemail->handleRequest($request);

            $em->persist($cardProgram);
            $em->flush();
            return $this->redirect($this->generateUrl('admin_card_program_modify',array('cardprogram_id'=>$request->get("cardprogram_id"))));
        }
    }

    /**
     * @Route("/admin/card-program/tenantajax",name="admin_card_program_tenantajax")
     */
    #[Template()]
    public function tenantajaxAction(Request $request){
        $em = Util::em();
        $query = $em->createQuery('SELECT p.id,p.name FROM CoreBundle:MarketingPartner p');
//        $provinces = $em->getRepository(\CoreBundle\Entity\Address::class)->findAll();
        $provinces=$query->getResult();
        $provincems=array();
        for ($i=0;$i<count($provinces);$i++) {
            $provincems[$provinces[$i]['id']]=$provinces[$i]['name'];
        }
        $provinces_json = json_encode($provincems);
        exit($provinces_json);
    }

    /**
     * @Route("/admin/card-program/cardprojramajax",name="admin_card_program_cardprojramajax")
     */
    #[Template()]
    public function cardprojramajaxAction(Request $request){
        $currencies='';
        if (!empty($_GET['currencies'] )) {
            foreach ($_GET['currencies'] as $item) {
                $currencies=$item.';'.$currencies;
            }
        }
        $em = $this->getDoctrine()->getManager();
//        $cardProgram = new CardProgram();card_type_cardprogram_id
        if($_GET['card_program_card_type_id']){
            $tmp = $em->find(\CoreBundle\Entity\CardProgramCardType::class,$_GET['card_program_card_type_id']);
        }else{
            $tmp = new CardProgramCardType();
            $targetcardProgram= $em->find(\CoreBundle\Entity\CardProgram::class,$_GET['card_type_cardprogram_id']);
            $tmp->setCardProgram($targetcardProgram);
        }
        $targetCardType = $em->find(\CoreBundle\Entity\CardType::class, $_GET['form']['cardType']);
        $tmp->setCustName($_GET['card_type_custName'])->setMaxBalance($_GET['card_type_maxBalance'])
            ->setMaxLoad($_GET['card_type_maxLoad'])->setMinLoad($_GET['card_type_minLoad'])
            ->setIsAutoCreated(empty($_GET['card_type_isAutoCreateds'])?0:($_GET['card_type_isAutoCreateds'] == 'Yes'? 1: 0))
//            ->setCardsAllowed($_GET['card_type_cardsAllowed'])
            ->setFundingAccountNumber($_GET['funding_account_number'])
            ->setCardsAllowed(0)
            //Begin Add By Bob Wen Bao on 2017-03-26 to handle card information in sign up process
            ->setArtwork($_GET['card_type_artwork_path'])
            ->setDescription($_GET['form']['description'])
            ->setBenefit($_GET['form']['benefit'])
            //End
            //Need to handle additional logic if any later
            ->setIsAdditional(false)
            ->setCardType($targetCardType)
            ->setRegistrable(isset($_GET['registrable']))
            ->setCurrencies($currencies);
             $em->persist($tmp);
             $em->flush();

        exit($request->get("cardprogram_id"));
    }

    /**
     * @Route("/admin/card-program/cardprojramcuntryadajax",name="admin_card_program_cuntryadajax")
     */
    #[Template()]
    public function cardprojramcuntryadajaxAction(Request $request){
        Util::longRequest();

        $all = $request->request->all();
        $cuntryitem=array();
        $em = $this->getDoctrine()->getManager();
        $cuntryitems=$em->getRepository(\CoreBundle\Entity\CardProgram::class)->findOneBy(array("id" => $all['cardprogram_id']));
        $cuntryarray=array();
        foreach ($cuntryitems->getCountries() as $ca)
        {
            $cuntryarray[]=$ca->getId();
        }
        foreach ($all['card_program']['countries'] as $item){
            $cuntryitem[]=$em->getRepository(\CoreBundle\Entity\Country::class)->findOneBy(array("id" => $item));
            if (!in_array($item,$cuntryarray)) {
                $targetcardCountry = $em->find(\CoreBundle\Entity\Country::class, $item);
                $cuntryitems->addCountry($targetcardCountry);
                $em->persist($cuntryitems);
                $em->flush();
            }
        }

        $loadPartners = $this->getDoctrine()->getRepository(\CoreBundle\Entity\LoadPartner::class)->findAll();
        //Load partner settings
        $currencySettings = $this->getCardProgramCountryLoadPartnerMethodCurrencySettings($all['cardprogram_id'], $cuntryitem, $loadPartners);
        //Kyc providers
        $kycProviders = $this->getDoctrine()->getRepository(\CoreBundle\Entity\KycProvider::class)->findAll();
        //Kyc provider settings
        $imageRequirementSettings = $this->getCardProgramKycProviderImageRequirementSettings($all['cardprogram_id'], $cuntryitem, $kycProviders);
        return $this->render('@Admin/CardProgram/newa.html.twig', [
            'countries' => $cuntryitem,
            'loadPartners' => $loadPartners,
            'currencySettings' => $currencySettings,
            'kycProviders' => $kycProviders,
            'imageRequirementSettings' => $imageRequirementSettings,
            "identityTypeAll" => IdentityType::All,
            "imageRequirementAll" => ImageRequirement::All,
            'cardprogram_id'=>$all['cardprogram_id'],
        ]);
    }

    /**
     * @Route("/admin/card-program/cardprojramfeeadajax",name="admin_card_program_feeadajax")
     */
    #[Template()]
    public function cardprojramfeeadajaxAction(Request $request){

        $feeitem=array();
        $feeitemids=array();
        $cpfeeitem=array();
        $em = $this->getDoctrine()->getManager();
        $cpId = $request->get('cardprogram_id');
        $feeitemx=$em->getRepository(\CoreBundle\Entity\CardProgramFeeItem::class)->findBy([
            'cardProgram' => $cpId,
        ]);
        foreach ($_GET['card_program']['feeItem'] as $item){
            $feeitem[]=$em->getRepository(\CoreBundle\Entity\FeeItem::class)->findOneBy(array("id" => $item));
            $feeitemids[]=$item;
            foreach ($feeitemx as $itemx)
            {
                if($itemx->getFeeItem()->getId()==$item&&$itemx->getCardProgram()->getId()==$_GET['cardprogram_id']){
                    $cpfeeitem[$_GET['cardprogram_id']][$item]['fee_to_customer_fixed']=$itemx->getFeeToCusFixed();
                    $cpfeeitem[$_GET['cardprogram_id']][$item]['fee_to_customer_ratio']=$itemx->getFeeToCusRatio();
                }
            }
        }

        //Detail form to handle fee item details
        $feeItemForm = $this->buildFeeItemDetailForm();
        //Revenue Form to handle fee item revenue
        $feeItemRevenueForm = $this->buildFeeItemRevenueForm();
        return $this->render('@Admin/CardProgram/ajaxfeead.html.twig', [
            "feeItems" =>$feeitem,
            "feeitemids" =>$feeitemids,
            "cpfeeitem" =>$cpfeeitem,
            'feeItemForm'=> $feeItemForm->createView(),
            'feeItemRevenueForm'=>$feeItemRevenueForm->createView(),
            'cardprogram_id'=>$_GET['cardprogram_id'],
        ]);
    }

    /**
     * @Route("/admin/card-program/cardprojramfeeadrefreshajax",name="admin_card_program_feeadfreshajax")
     */
    #[Template()]
    public function cardprojramfeeadrefreshajaxAction(Request $request){

        $feeitem=array();
        $feeitemids=array();
        $cpfeeitem=array();
        $em = $this->getDoctrine()->getManager();
        $cpId = $request->get('cardprogram_id');
        $feeitemx=$em->getRepository(\CoreBundle\Entity\CardProgramFeeItem::class)->findBy([
            'cardProgram' => $cpId,
        ]);
        foreach ($_GET['feeitemids'] as $item){
            $feeitem[]=$em->getRepository(\CoreBundle\Entity\FeeItem::class)->findOneBy(array("id" => $item));
            $feeitemids[]=$item;
            foreach ($feeitemx as $itemx)
            {
                if($itemx->getFeeItem()->getId()==$item&&$itemx->getCardProgram()->getId()==$_GET['cardprogram_id']){
                    $cpfeeitem[$_GET['cardprogram_id']][$item]['fee_to_customer_fixed']=$itemx->getFeeToCusFixed();
                    $cpfeeitem[$_GET['cardprogram_id']][$item]['fee_to_customer_ratio']=$itemx->getFeeToCusRatio();
                }
            }
        }

        //Detail form to handle fee item details
        $feeItemForm = $this->buildFeeItemDetailForm();
        //Revenue Form to handle fee item revenue
        $feeItemRevenueForm = $this->buildFeeItemRevenueForm();
        return $this->render('@Admin/CardProgram/ajaxfeead.html.twig', [
            "feeItems" =>$feeitem,
            "feeitemids" =>$feeitemids,
            "cpfeeitem" =>$cpfeeitem,
            'feeItemForm'=> $feeItemForm->createView(),
            'feeItemRevenueForm'=>$feeItemRevenueForm->createView(),
            'cardprogram_id'=>$_GET['cardprogram_id'],
        ]);
    }
    /**
     * @Route("/admin/card-program/loadPartnesformajax",name="admin_card_program_loadPartnesformajax")
     */
    #[Template()]
    public function loadPartnesformajaxAction(Request $request){

        $em = $this->getDoctrine()->getManager();
        foreach($_GET['countryid'] as $country ) {
            foreach($_GET['partnerid'] as $loadPartner) {
                foreach($_GET['methodid'] as $loadMethod) {

            $currenciesKey = $country . '_' . $loadPartner . '_' .$loadMethod . '_currencies';
                    $currencies = "";
            if (array_key_exists($currenciesKey, $_GET)) {
                $currencies = join(";", $_GET[$currenciesKey]);
            }

            $enabledKey = $country . '_' . $loadPartner . '_' .$loadMethod .'_enabled';
                    $enabled = false;

//            if (array_key_exists($enabledKey, $_GET)) {
                if($currencies) {
                    $enabled =true;
                }
//            }
            $rs = $em->getRepository(\CoreBundle\Entity\CardProgramCountryLoadPartnerMethodCurrency::class)->findBy([
                'cardProgram' => $_GET['cardprogram_id'],
                'loadPartner' => $loadPartner,
                'loadMethod' => $loadMethod,
                'country' => $country,
            ]);
            /** @var CardProgramCountryLoadPartnerMethodCurrency $record */
            $record = $rs ? $rs[0] : null;
            if ($record) {
                $record->setCurrencies($currencies);
                $record->setEnabled($enabled);
            } else {
                $targetcardProgram = $em->find(\CoreBundle\Entity\CardProgram::class, $_GET['cardprogram_id']);
                $targetCountry = $em->find(\CoreBundle\Entity\Country::class, $country);
                $targetLoadPartner = $em->find(\CoreBundle\Entity\LoadPartner::class, $loadPartner);
                $targetLoadMethod = $em->find(\CoreBundle\Entity\LoadMethod::class, $loadMethod);
                $record = new CardProgramCountryLoadPartnerMethodCurrency();
                $record->setCardProgram($targetcardProgram)
                    ->setCountry($targetCountry)
                    ->setLoadPartner($targetLoadPartner)
                    ->setLoadMethod($targetLoadMethod)
                    ->setCurrencies($currencies)
                    ->setEnabled($enabled);
                $em->persist($record);
            }

                  $em->flush();

                }
            }
        }
        exit('ok');
    }
    /**
     * @Route("/admin/card-program/kycProvidersformajax",name="admin_card_program_kycProvidersformajax")
     */
    #[Template()]
    public function kycProvidersformajaxAction(Request $request){
        $em = $this->getDoctrine()->getManager();
        foreach($_GET['countryid'] as $country ) {
            foreach ($_GET['kycid'] as $kycProvider) {
                foreach ($_GET['typeid'] as $idType) {
                    $requirementKey = $country . '_' . $kycProvider . '_' . $idType . '_requirement';
                    $requirement = "";
                    if (array_key_exists($requirementKey, $_GET)) {
                        $requirement = $_GET[$requirementKey];
                    }

                    $enabledKey = $country . '_' . $kycProvider . '_' . $idType . '_enabled';
                    $enabled = false;

                    if (array_key_exists($enabledKey, $_GET)) {
                        $enabledx = $_GET[$enabledKey];
                        if($enabledx=='on') {
                            $enabled = true;
                        }
                    }
                    $cardProgramKycI = $em->getRepository(\CoreBundle\Entity\CardProgramCountryKycIds::class)->findBy(array("identityType" =>$idType));
                    $record=$this->fieldcuntryoneitemxx($cardProgramKycI, $_GET['cardprogram_id'],$country,$kycProvider);
                    if ($record) {
                        $record->setImageRequirement($requirement);
                        $record->setEnabled($enabled);
                    } else {
                        $record = new CardProgramCountryKycIds();
                        $targetcardProgram = $em->find(\CoreBundle\Entity\CardProgram::class, $_GET['cardprogram_id']);
                        $targetCountry = $em->find(\CoreBundle\Entity\Country::class, $country);
                        $targetKycProvider = $em->find(\CoreBundle\Entity\KycProvider::class, $kycProvider);
                        $record->setCardProgram($targetcardProgram)
                            ->setCountry($targetCountry)
                            ->setKycProvider($targetKycProvider)
                            ->setIdentityType($idType)
                            ->setImageRequirement($requirement)
                            ->setEnabled($enabled);
                        $em->persist($record);
                    }

                    $em->flush();
                }
            }
        }
        exit('ok');
    }

    /**
     * @Route("/admin/card-program/tenantbajax",name="admin_card_program_tenantbajax")
     */
    #[Template()]
    public function tenantbajaxAction(Request $request){
        $em = Util::em();
        $query = $em->createQuery('SELECT p.id,p.name FROM CoreBundle:BrandPartner p');
//        $provinces = $em->getRepository(\CoreBundle\Entity\Address::class)->findAll();
        $provinces=$query->getResult();
        $provincems=array();
        for ($i=0;$i<count($provinces);$i++) {
            $provincems[$provinces[$i]['id']]=$provinces[$i]['name'];
        }
        $provinces_json = json_encode($provincems);
        exit($provinces_json);
    }


    /**
     * @Route("/admin/card-program/delete",name="admin_card_program_delete")
     */
    #[Template()]
    public function deleteAction(Request $request)
    {
        $em   = $this->getDoctrine()->getManager();
        $cardprogram = $em->getRepository(\CoreBundle\Entity\CardProgram::class)->findOneBy(array( "id" => $request->get("cardprogram_id") ));
        if ( ! $cardprogram) {
            throw $this->createNotFoundException('No cardprogram found for id '.$request->get("cardprogram_id"));
        }
        $em->remove($cardprogram);
        $em->flush();

        return $this->redirect($this->generateUrl('admin_card_program'));
    }

    /**
     * @Route("/admin/card-program/deletetypeajax",name="admin_card_program_deletetypeajax")
     */
    public function deletetypeajaxAction(Request $request)
    {
        $em   = $this->getDoctrine()->getManager();
        $cardprogram = $em->getRepository(\CoreBundle\Entity\CardProgramCardType::class)->findOneBy(array( "id" => $_GET['ct_id']));
        if ( ! $cardprogram) {
            throw $this->createNotFoundException('No CardprogramCardType found for id '.$_GET['ct_id']);
        }
        $em->remove($cardprogram);
        $em->flush();
      exit('ok');
    }

    /**
     * @Route("/admin/card-program/deletecountryajax",name="admin_card_program_deletecountryajax")
     * @throws \Doctrine\ORM\ORMInvalidArgumentException
     */
    public function deletecountryajaxAction(Request $request)
    {
        Util::longRequest();

        $countryid=$_POST['ct_id'];
        $progromids=$_POST['progromids'];
        $em   = Util::em();
        $cardprogram = $em->getRepository(\CoreBundle\Entity\CardProgram::class)->findOneBy(array( "id" => $progromids));
        $country = $em->getRepository(\CoreBundle\Entity\Country::class)->findOneBy(array( "id" => $countryid));
        $cardprogram->removeCountry($country);
        $em->persist($cardprogram);
        $countrykys=$em->getRepository(\CoreBundle\Entity\CardProgramCountryKycIds::class)->findBy([
            'cardProgram' => $progromids,
        ]);
        $countrylpmc=$em->getRepository(\CoreBundle\Entity\CardProgramCountryLoadPartnerMethodCurrency::class)->findBy([
            'cardProgram' => $progromids,
        ]);
        foreach ($countrykys as $item){
            if($item->getCardProgram()->getId()==$progromids && $item->getCountry()->getId()==$countryid){
                $em->remove($item);
            }
        }
        foreach ($countrylpmc as $xitem){
            if($xitem->getCardProgram()->getId()==$progromids && $xitem->getCountry()->getId()==$countryid){
                $em->remove($xitem);
            }
        }
        $em->flush();

//        $q = $em->getRepository(\SalexUserBundle\Entity\User::class)
//            ->createQueryBuilder('u')
//            ->join('u.teams', 't')
//            ->join('u.cards', 'uc')
//            ->join('uc.card', 'c');
//        $e = $q->expr();
//        $users = $q->where($e->andX(
//            $e->eq('c.cardProgram', $progromids),
//            $e->eq('u.country', $countryid)
//        ))->andWhere('t.name = :team')
//            ->setParameter('team', Role::ROLE_CONSUMER)
//            ->getQuery()->getResult();
//        /** @var User $user */
//        foreach ($users as $user) {
//            $note = 'Account closed because your country ' . $country->getName()
//                . ' is no longer supported in card program ' . $cardprogram->getName();
//            $user->setStatus(User::STATUS_CLOSED, $note);
//            $em->persist($user);
//        }
//        $em->flush();

        exit('ok');
    }

    /**
     * @Route("/admin/card-program/deletefeeitemajax",name="admin_card_program_deletefeeitemajax")
     */
    public function deletefeeitemajaxAction(Request $request)
    {
        $feeitemid=$_POST['ct_id'];
        $progromids=$_POST['progromids'];
        $em   = $this->getDoctrine()->getManager();
        $cpf=$em->getRepository(\CoreBundle\Entity\CardProgramFeeItem::class)->findBy([
            'cardProgram' => $progromids,
        ]);
        $cpfs=$em->getRepository(\CoreBundle\Entity\CardProgramFeeRevenueShare::class)->findBy([
            'cardProgram' => $progromids,
        ]);
        foreach ($cpf as $item){
            if($item->getCardProgram()->getId()==$progromids && $item->getFeeItem()->getId()==$feeitemid){
                $em->remove($item);
            }
        }
        foreach ($cpfs as $xitem){
            if($xitem->getCardProgram()->getId()==$progromids && $xitem->getFeeItem()->getId()==$feeitemid){
                $em->remove($xitem);
            }
        }

        $em->flush();
        exit('ok');
    }

    /**
     * @Route("/admin/card-program/wizard",name="admin_card_program_wizard")
     */
    #[Template()]
    public function wizardAction(Request $request)
    {
        $this->authPermission(Module::ID_SET_UP_WIZARD);

        Util::longRequest();

        $repo = $this->getDoctrine()->getRepository(\CoreBundle\Entity\CardProgram::class);

        $cp = $request->get('card_program');
        if (isset($cp['name'])) {
            $rs = $repo->findBy([
                'name' => $cp['name'],
            ]);
            if ($rs) {
                return new FailedMsgResponse('Card Program Name is duplicated. Please choose another one.');
            }
        }

        $cardProgram = new CardProgram();

        if (Bundle::isEsSolo()) {
            $cardProgram->setPlatform(Platform::find(Platform::NAME_ES_SOLO));

            $bp = $this->em->getRepository(BrandPartner::class)->findBy([
                'name' => BrandPartner::NAME_ES_SOLO,
            ])[0] ?? null;
            if ($bp) {
                $cardProgram->setBrandPartner($bp);
            }

            $processor = $this->em->getRepository(Processor::class)->findBy([
                    'name' => Processor::NAME_GTP,
                ])[0] ?? null;
            if ($processor) {
                $cardProgram->setProcessor($processor);
            }

            $ib = $this->em->getRepository(IssuingBank::class)->findBy([
                    'name' => IssuingBank::NAME_UBA,
                ])[0] ?? null;
            if ($ib) {
                $cardProgram->setIssuingBank($ib);
            }

            $kp = $this->em->getRepository(KycProvider::class)->findBy([
                    'name' => KycProvider::NESTOR,
                ])[0] ?? null;
            if ($kp) {
                $cardProgram->setKycProvider($kp);
            }
        }

        //Major form to handle card program information
        $form = $this->createForm(CardProgramType::class, $cardProgram);
        //Detail form to handle card type details
        $cardTypeForm = $this->buildCardTypeDetailForm();
        //Fee Items List
        $feeItems = $this->getDoctrine()->getRepository(\CoreBundle\Entity\FeeItem::class)->findAll();
        //Detail form to handle fee item details
        $feeItemForm = $this->buildFeeItemDetailForm();
        //Revenue Form to handle fee item revenue
        $feeItemRevenueForm = $this->buildFeeItemRevenueForm();
        //Countries
        $countries = $this->getDoctrine()->getRepository(\CoreBundle\Entity\Country::class)->findBy([], [
            'name' => 'asc',
        ]);
        //Load partners
        $loadPartners = $this->getDoctrine()->getRepository(\CoreBundle\Entity\LoadPartner::class)->findAll();
        //Load partner settings
        $currencySettings = $this->getCardProgramCountryLoadPartnerMethodCurrencySettings(null, $countries, $loadPartners);
        //All currency codes
        $allCurrencyCodes = $this->getDoctrine()->getRepository(\CoreBundle\Entity\Currency::class)->getAllCurrencyCodes();
        //Kyc providers
        $kycProviders = $this->getDoctrine()->getRepository(\CoreBundle\Entity\KycProvider::class)->findAll();
        //Kyc provider settings
        $imageRequirementSettings = $this->getCardProgramKycProviderImageRequirementSettings(null, $countries, $kycProviders);

        if ($request->isMethod('POST')) {

            $form->handleRequest($request);

            if ($form->isValid()) {
                $cardTypes = $request->get("card_type_detail_info");
                if ((!$cardTypes || !$cardTypes[0]) && Bundle::isEsSolo()) {
                    $processor = $cardProgram->getProcessor();
                    $issuingBank = $cardProgram->getIssuingBank();
                    $rs = $this->getDoctrine()->getRepository(CardType::class)
                        ->createQueryBuilder('c')
                        ->where('c.processor = :processor')
                        ->andWhere('c.issuingBank = :issuingBank')
                        ->setParameter('processor', $processor)
                        ->setParameter('issuingBank', $issuingBank)
                        ->setMaxResults(1)
                        ->getQuery()
                        ->getResult();
                    if ($rs) {
                        $cardType = $rs[0];
                    } else {
                        $cardType = new CardType();
                        $cardType->setName($processor->getName() . ' ' . $issuingBank->getName() . ' Card')
                            ->setProcessor($processor)
                            ->setIssuingBank($issuingBank)
                            ->setMaxBalance(10000)
                            ->setMaxLoad(10000)
                            ->setMinLoad(1)
                            ->setIsAutoCreated(true)
                            ->setCurrencies('USD')
                            ->setNetwork('')
                            ->setSupportUpgrade(false)
                            ->setIsreloaded(true)
                            ->persist();
                    }

                    $cardTypes = [
                        json_encode([
                            'id' => $cardType->getId(),
                            'custName' => $cardType->getName(),
                            'maxBalance' => $cardType->getMaxBalance(),
                            'maxLoad' => $cardType->getMaxLoad(),
                            'minLoad' => $cardType->getMinLoad(),
                            'cardsAllowed' => 0,
                            'isAutoCreated' => 'Yes',
                            'artwork' => '/var/upload/span/card_type_15723331814161.png',
                            'description' => ' ',
                            'currencies' => $cardType->getCurrencies(),
                            'benefit' => ' ',
                            'faccountnum' => '',
                        ]),
                    ];
                }

                $repo->generateNewCardProgram($cardProgram, array(
                        'request' => $request,
                        'loadPartners' => $loadPartners,
                        'kycProviders' => $kycProviders,
                        'cardTypeDetails'=> $cardTypes,
                        'feeItemDetails'=>$request->get("fee_item_detail_info"),
                        'feeItemRevenues'=>$request->get("fee_item_revenue_info"),
                        'languagetype'=>$_POST['languagetype'] ?? null,
                    ));
                $cardProgram->setBin($request->get('bin'));
                $cardProgram->setDeliveryMethod($request->get('deliveryMethod'));
                $cardProgram->persist();

                return $this->redirect($this->generateUrl('admin_card_program'));
            }
        }
        return array(
            'form' => $form->createView(),
            'cardTypeForm'=>$cardTypeForm->createView(),
            'feeItemForm'=> $feeItemForm->createView(),
            'feeItemRevenueForm'=>$feeItemRevenueForm->createView(),
            'countries' => $countries,
            'loadPartners' => $loadPartners,
            'currencySettings' => $currencySettings,
            "allCurrencyCodes" => $allCurrencyCodes,
            'kycProviders' => $kycProviders,
            'imageRequirementSettings' => $imageRequirementSettings,
            "identityTypeAll" => IdentityType::All,
            "imageRequirementAll" => ImageRequirement::All,
            'feeItems' => $feeItems
        );
    }

    private  function buildCardTypeDetailForm()
    {
        $form = $this->createFormBuilder(array())
            ->add('cardType', EntityType::class, array(
                'label'=>'Card Type',
                'translation_domain' => 'forms',
                "class" => \CoreBundle\Entity\CardType::class,
                'choice_label'       => 'name',
                'multiple'           => false,
                'expanded'           => false,
                'required'           => true,
                'disabled' =>  !Util::user()->authEditable(Module::ID_CARD_PROGRAM_LIST),
            ))
            ->add('description', TextareaType::class, array('label'=>''))
            ->add('benefit', TextareaType::class, array('label'=>''))
            ->getForm();
        return $form;
    }

    private function buildFeeItemDetailForm()
    {
        $form = $this->createFormBuilder(array())
            ->add('targetId', HiddenType::class)
            ->add('feeToCusFixed', IntegerType::class, array('label'=>'Fee to Consumer Fixed'))
            ->add('feeToCusRatio', IntegerType::class, array('label'=>'Fee to Consumer Ratio'))
            ->add('feeToBPFixed', IntegerType::class, array('label'=>'Fee to Brand Partner Fixed'))
            ->add('feeToBPRatio', IntegerType::class, array('label'=>'Fee to Brand Partner Ratio'))
            ->add('brandPartnerName', EntityType::class, array(
                'label'=>'Brand Partner Name',
                'translation_domain' => 'forms',
                "class" => \CoreBundle\Entity\BrandPartner::class,
                'choice_label'       => 'name',
                'multiple'           => false,
                'expanded'           => false,
                'required'           => true
            ))
            ->getForm();
        return $form;
    }

    private function buildFeeItemRevenueForm()
    {
        $form = $this->createFormBuilder(array())
            ->add('targetId', HiddenType::class)
            ->add('targetTenant', ChoiceType::class, array(
                'label'=>'Tenant',
                'choices' => array_flip(TenantType::getConstants()),
                'placeholder' => 'Choose a Tenant Type'
            ))
            ->getForm();
        return $form;
    }

    public  function  fielditem($fielditem,$cardprogramid)
    {
        $fielditemx=array();
        foreach ($fielditem as $item)
        {
            if($item->getCardProgram()->getId()==$cardprogramid)
            {
                $fielditemx[]=$item;
            }

        }
        return $fielditemx;
    }

    public  function  fieldoneitem($fielditem,$cardprogramid)
    {
        $fieldoneitemx='';
        foreach ($fielditem as $item)
        {
            if($item->getCardProgram()->getId()==$cardprogramid)
            {
                $fieldoneitemx=$item;
            }

        }
        return $fieldoneitemx;
    }
    public  function  fieldcuntryitem($fielditem,$cardprogramid,$cuntryid)
{
    $fieldcuntryitemx=array();
    foreach ($fielditem as $item)
    {
        if(($item->getCardProgram()->getId()==$cardprogramid)&&($item->getCountry()->getId()==$cuntryid))
        {
            $fieldcuntryitemx[]=$item;
        }

    }
    return $fieldcuntryitemx;
}
    public  function  fieldcuntryoneitem($fielditem,$cardprogramid,$cuntryid)
    {
        $fieldcuntryitemx='';
        foreach ($fielditem as $item)
        {
            if(($item->getCardProgram()->getId()==$cardprogramid)&&($item->getCountry()->getId()==$cuntryid))
            {
                $fieldcuntryitemx=$item;
            }

        }
        return $fieldcuntryitemx;
    }


    public  function  fieldcuntryoneitemxx($fielditem,$cardprogramid,$cuntryid,$kycProvider)
    {
        $fieldcuntryitemx='';
        foreach ($fielditem as $item)
        {
            if(($item->getCardProgram()->getId()==$cardprogramid)&&($item->getCountry()->getId()==$cuntryid)&&($item->getKycProvider()->getId()==$kycProvider))
            {
                $fieldcuntryitemx=$item;
            }

        }
        return $fieldcuntryitemx;
    }
    public  function  fieldonecpclitem($fielditem,$cardprogramid,$loadPartnerid,$loadMethodid,$cuntryid)
    {
        $fieldonecpclitemx='';
        foreach ($fielditem as $item)
        {
            if(($item->getCardProgram()->getId()==$cardprogramid)&&($item->getLoadPartner()->getId()==$loadPartnerid)&&($item->getLoadMethod()->getId()==$loadMethodid)&&($item->getCountry()->getId()==$cuntryid))
            {
                $fieldonecpclitemx=$item;
            }

        }
        return $fieldonecpclitemx;
    }
    public  function  fieldonefeeshareclitem($fielditem,$cardprogramid,$feeshareid,$tenantType,$tenantId)
    {
        $fieldonecpclitemx='';
        foreach ($fielditem as $item)
        {
            if(($item->getCardProgram()->getId()==$cardprogramid)&&($item->getFeeItem()->getId()==$feeshareid)&&($item->getTenantType()==$tenantType)&&($item->getTenantId()==$tenantId))
            {
                $fieldonecpclitemx=$item;
            }

        }
        return $fieldonecpclitemx;
    }
    public  function  fieldonefeeclitem($fielditem,$cardprogramid,$feeitemid)
    {
        $fieldonecpclitemx='';
        foreach ($fielditem as $item)
        {
            if(($item->getCardProgram()->getId()==$cardprogramid)&&($item->getFeeItem()->getId()==$feeitemid))
            {
                $fieldonecpclitemx=$item;
            }

        }
        return $fieldonecpclitemx;
    }
    /**
     * Get all load partner currency settings [country_id][load_partner_id][load_method_id] => [currencies=>{xx;xx},
     * enabled=>{0/1}, moreCurrencies=>[xx,xx]]
     */
    private function getCardProgramCountryLoadPartnerMethodCurrencySettings($cardProgramId, $countries, $loadPartners)
    {
        if ($cardProgramId) {
            $em = $this->getDoctrine()->getManager();
            $cpclpmc = $em->getRepository(\CoreBundle\Entity\CardProgramCountryLoadPartnerMethodCurrency::class)->findBy([
                'cardProgram' => $cardProgramId,
            ]);
            $settingsRepo = $this->getDoctrine()->getRepository(\CoreBundle\Entity\CardProgramCountryLoadPartnerMethodCurrency::class);
            $defaultSettingsRepo = $this->getDoctrine()->getRepository(\CoreBundle\Entity\CountryLoadPartnerMethodCurrency::class);
            $currencySettings = [];
            foreach ($countries as $country) {
                $countryId = $country->getId();
                $currencySettings[$countryId] = [];

                foreach ($loadPartners as $loadPartner) {
                    $loadPartnerId = $loadPartner->getId();
                    $currencySettings[$countryId][$loadPartnerId] = [];

                    foreach ($loadPartner->getLoadMethods() as $loadMethod) {
                        $loadMethodId = $loadMethod->getId();
                        $settings = null;
                        $moreCurrencies = [];   // currencies in default settings, but not enabled in card program settings
                        if ($cardProgramId) {
                            $settings = $settingsRepo->getMappedCurrenciesSettings($cardProgramId, $countryId, $loadPartnerId, $loadMethodId);
                            // get more currencies from default settings
                            $default = $defaultSettingsRepo->getMappedCurrenciesSettings($countryId, $loadPartnerId, $loadMethodId);
                            $defaultCurrencies = explode(";", $default ? $default["currencies"] : $country->getCurrency());
                            $settings["moreCurrencies"] = $default["currencies"];
                            $settings["currenciesx"]=$settings["currencies"];
                            $settings["currencies"]=$default["currencies"];
                            $currencySettings[$countryId][$loadPartnerId][$loadMethodId] = $settings;
                            $select=$settings&&$default;
                            if($select===false)
                            {
                                $cpclpmcone='';
                                foreach ($cpclpmc as $itemss) {
                                 if($itemss->getCardProgram()->getId()==$cardProgramId&&$itemss->getCountry()->getId()==$countryId&&$itemss->getLoadPartner()->getId()==$loadPartnerId&&$itemss->getLoadMethod()->getId()==$loadMethodId){
                                     $cpclpmcone=$itemss;
                                 }
                                }
                                if($cpclpmcone){
                                    $em->remove($cpclpmcone);
                                    $em->flush();
                                }
                            }

                        }
                    }
                }
            }
        }else{
            $settingsRepo = $this->getDoctrine()->getRepository(\CoreBundle\Entity\CardProgramCountryLoadPartnerMethodCurrency::class);
            $defaultSettingsRepo = $this->getDoctrine()->getRepository(\CoreBundle\Entity\CountryLoadPartnerMethodCurrency::class);
            $defaultSettings = $this->getDoctrine()->getRepository(\CoreBundle\Entity\CountryLoadPartnerMethodCurrency::class)->findAll();
            $defaultSettingsid=array();
            foreach ($defaultSettings as $item){
                if(!in_array($item->getCountry()->getId(),$defaultSettingsid)){
                    $defaultSettingsid[]=$item->getCountry()->getId();
                }
            }
            $currencySettings = [];
            foreach ($countries as $country) {
                $countryId = $country->getId();
                $currencySettings[$countryId] = [];

                foreach ($loadPartners as $loadPartner) {
                    $loadPartnerId = $loadPartner->getId();
                    $currencySettings[$countryId][$loadPartnerId] = [];

                    foreach ($loadPartner->getLoadMethods() as $loadMethod) {
                        $loadMethodId = $loadMethod->getId();

                        $settings = null;
                        $moreCurrencies = [];   // currencies in default settings, but not enabled in card program settings
                        if ($cardProgramId) {
                            $settings = $settingsRepo->getMappedCurrenciesSettings($cardProgramId, $countryId, $loadPartnerId, $loadMethodId);
                            // get more currencies from default settings
                            $default = $defaultSettingsRepo->getMappedCurrenciesSettings($countryId, $loadPartnerId, $loadMethodId);
                            $defaultCurrencies = explode(";", $default ? $default["currencies"] : $country->getCurrency());

                            foreach ($defaultCurrencies as $cur) {
                                if ($cur && strpos($settings["currencies"], $cur) === false) {
                                    $moreCurrencies[] = $cur;
                                }
                            }
                        }
                        if (!$settings) {
//                            if(!in_array($countryId,$defaultSettingsid)){
//                                $settings = array(
//                                "currencies" => $country->getCurrency(),
//                                "enabled" => true,
//                            );
//                            }else {
                                $settingsx = $defaultSettingsRepo->getMappedCurrenciesSettingsx($countryId, $loadPartnerId, $loadMethodId);
                                $settings = array(
                                    "currencies" => $settingsx["currencies"],
                                    "enabled" => $settingsx["enabled"],
                                );
//                            }
                        }
//                        if (!$settings) {
//                            // Default
//                            $settings = array(
//                                "currencies" => $country->getCurrency(),
//                                "enabled" => true,
////                            "isenabled" => 1
//                            );
//                        }

//                        $settings["moreCurrencies"] = $moreCurrencies;
                        $currencySettings[$countryId][$loadPartnerId][$loadMethodId] = $settings;
                    }
                }
            }
        }
        return $currencySettings;
    }

    /**
     * Get all kyc provider image requirement settings [country_id][kyc_provider_id][identity_type] =>
     * [requirement=>{requirement}, enabled=>{enabled}]
     */
    private function getCardProgramKycProviderImageRequirementSettings($cardProgramId, $countries, $kycProviders)
    {
        if($cardProgramId) {
            $em = $this->getDoctrine()->getManager();
            $cpclpmc = $em->getRepository(\CoreBundle\Entity\CardProgramCountryKycIds::class)->findBy([
                'cardProgram' => $cardProgramId,
            ]);
            $settingsRepo = $this->getDoctrine()->getRepository(\CoreBundle\Entity\CardProgramCountryKycIds::class);
            $defaultSettingsRepo = $this->getDoctrine()->getRepository(\CoreBundle\Entity\CountryKycIds::class);
            $imageSettings = [];
            foreach ($countries as $country) {
                $countryId = $country->getId();
                $imageSettings[$countryId] = [];

                foreach ($kycProviders as $kycProvider) {
                    $kycProviderId = $kycProvider->getId();
                    $imageSettings[$countryId][$kycProviderId] = [];

                    foreach (IdentityType::All as $idType) {
                        $settings = null;
                        if ($cardProgramId) {
                            $settings = $settingsRepo->getMappedImageRequirementSettings($cardProgramId, $countryId, $kycProviderId, $idType);
                            $default = $defaultSettingsRepo->getMappedImageRequirementSettingsx($countryId, $kycProviderId, $idType);
                            if ( ! $default) {
                                $default = array(
                                    "requirement" => ImageRequirement::BOTH,
                                    "enabled" => true
                                );
                            }

                            $settings["requirement"]=$default["requirement"];
                            $imageSettings[$countryId][$kycProviderId][$idType] = $settings;
                            $select=$settings&&$default;
                            if($select===false)
                            {
                                $cpclpmcone='';
                                foreach ($cpclpmc as $itemss) {
                                    if($itemss->getCardProgram()->getId()==$cardProgramId&&$itemss->getCountry()->getId()==$countryId&&$itemss->getKycProvider()->getId()==$kycProviderId&&$itemss->getIdentityType()==$idType){
                                        $cpclpmcone=$itemss;
                                    }
                                }
                                if($cpclpmcone){
                                    $em->remove($cpclpmcone);
                                    $em->flush();
                                }
                            }
                        }
                        if (!$settings) {
                            $settings = $defaultSettingsRepo->getMappedImageRequirementSettings($countryId, $kycProviderId, $idType);
                        }
                        if (!$settings) {
                            // Default
                            $settings = array(
                                "requirement" => ImageRequirement::BOTH,
                                "enabled" => true
                            );
                        }
                    }
                }
            }
        }else{
            $settingsRepo = $this->getDoctrine()->getRepository(\CoreBundle\Entity\CardProgramCountryKycIds::class);
            $defaultSettingsRepo = $this->getDoctrine()->getRepository(\CoreBundle\Entity\CountryKycIds::class);
            $imageSettings = [];
            foreach ($countries as $country) {
                $countryId = $country->getId();
                $imageSettings[$countryId] = [];

                foreach ($kycProviders as $kycProvider) {
                    $kycProviderId = $kycProvider->getId();
                    $imageSettings[$countryId][$kycProviderId] = [];

                    foreach (IdentityType::All as $idType) {
                        $settings = null;
                        if ($cardProgramId) {
                            $settings = $settingsRepo->getMappedImageRequirementSettings($cardProgramId, $countryId, $kycProviderId, $idType);
                        }
                        if (!$settings) {
                            $settingsx = $defaultSettingsRepo->getMappedImageRequirementSettings($countryId, $kycProviderId, $idType);
                            if ( ! $settingsx) {
                                $settingsx = array(
                                    "requirement" => ImageRequirement::BOTH,
                                    "enabled" => true
                                );
                            }
                            $settings = array(
                                "requirement" => $settingsx["requirement"],
                                "enabled" => $settingsx["enabled"],
                            );
                        }
//                        if (!$settings) {
//                            // Default
//                            $settings = array(
//                                "requirement" => ImageRequirement::BOTH,
//                                "enabled" => true
//                            );
//                        }

                        $imageSettings[$countryId][$kycProviderId][$idType] = $settings;
                    }
                }
            }
        }
//        die;
        return $imageSettings;
    }

    //Begin Add By Bob Wen Bao on 2017-03-26 to handle card information in sign up process
    /**
     * @Route("/admin/card-program/wizard/image",name="admin_card_program_wizard_image")
     */
    public function cardTypeImageUploadAction(Request $request)
    {
        if (empty($_FILES['upFile'])) {
            return new Response('Please upload the artwork!');
        }
        //$dir = dirname($this->get("kernel")->getRootDir())."/web/upload/";
        $dir = $this->getParameter('upload_root');
        $type = substr($_FILES['upFile']['name'],strrpos($_FILES['upFile']['name'],'.'));
        $filename="card_type_".time().rand(1000,9999).$type;
        if(is_uploaded_file($_FILES['upFile']['tmp_name'])){
            move_uploaded_file($_FILES['upFile']['tmp_name'],$dir.$filename);
            return new Response($dir.$filename);
        }else{
            return new Response("Error when upload image, please try later!");
        }

    }

    /**
     * @Route("/admin/card-program/bank/image",name="admin_bank_image")
     */
    public function bankImageUploadAction(Request $request)
    {
        if (empty($_FILES['upFile'])) {
            return new Response('Please upload the artwork!');
        }
//        $dir = dirname($this->get("kernel")->getRootDir())."/web/upload/";
//        $dir = dirname($this->get("kernel")->getRootDir())."/web/upload/";
        $dir = $this->getParameter('upload_root');
        $type = substr($_FILES['upFile']['name'],strrpos($_FILES['upFile']['name'],'.'));
        $filename="card_type_".time().rand(1000,9999).$type;
        if(is_uploaded_file($_FILES['upFile']['tmp_name'])){
            move_uploaded_file($_FILES['upFile']['tmp_name'],$dir.$filename);
            return new Response($dir.$filename);
        }else{
            return new Response("Error when upload image, please try later!");
        }

    }
    //End

    //Begin Add by Bob Wen Bao on 2017-03-29 for card program unique name check
    /**
     * @Route("/admin/card-program/wizard/name", name="admin_card_program_wizard_name")
     */
    public function cardProgramNameUniqueCheck(Request $request)
    {
        $targetName = $request->get('target');
        $result = $this->getDoctrine()
            ->getRepository(\CoreBundle\Entity\CardProgram::class)
            ->findOneBy(array('name'=>$targetName));
        if($result != null)
        {
            return new Response('false');
        }else
        {
            return new Response('true');
        }

    }
    //End

}
