<?php
/**
 * Created by PhpStorm.
 * User: hans
 * Date: 2018/3/30
 * Time: 12:36
 */

namespace AdminBundle\Controller\Tenant;


use AdminBundle\Controller\BaseController;
use AdminBundle\Response\FailedMsgResponse;
use AdminBundle\Response\MsgResponse;
use AdminBundle\Response\SuccessMsgResponse;
use CoreBundle\Entity\FeeItem;
use CoreBundle\Entity\Tenant;
use CoreBundle\Entity\TenantFeeOption;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\QueryListParams;
use CoreBundle\Utils\Traits\DbTrait;
use CoreBundle\Utils\Util;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class FeeOptionController extends BaseController
{
    use DbTrait;

    /**
     * DefaultController constructor.
     */
    public function __construct()
    {
        parent::__construct();

        $this->authPermissionForTenant('/admin/tenants/(\d+)/fee-options(/.*)?');
    }

    /**
     * @Route("/admin/tenants/fee-options", name="admin_tenant_fee_options")
     * @throws \LogicException
     */
    public function all()
    {
        $paginator = $this->get('knp_paginator');
        return $this->render('@Admin/Tenant/FeeOption/index.html.twig', [
            'tenant' => null,
            'tenants' => Tenant::findAll(),
            'list' => $paginator->paginate([]),
        ]);
    }

    /**
     * @Route("/admin/tenants/{tenant}/fee-options")
     * @param Tenant $tenant
     * @return \Symfony\Component\HttpFoundation\Response
     * @throws \LogicException
     */
    public function index(Tenant $tenant)
    {
        $paginator = $this->get('knp_paginator');
        return $this->render('@Admin/Tenant/FeeOption/index.html.twig', [
            'tenant' => $tenant,
            'list' => $paginator->paginate([]),
        ]);
    }

    /**
     * @Route("/admin/tenants/{tenant}/fee-options/list/{page}/{limit}")
     * @param Request $request
     * @param string $tenant
     * @param int $page
     * @param int $limit
     * @return \Symfony\Component\HttpFoundation\Response
     * @throws \LogicException
     */
    public function list(Request $request, $tenant, $page = 1, $limit = 10)
    {
        $paginator = $this->get('knp_paginator');
        $tenant = $this->em->getRepository(\CoreBundle\Entity\Tenant::class)->find($tenant);
        $query = $this->query($request, $tenant);
        return $this->render('@Admin/Tenant/FeeOption/list.html.twig', [
            'tenant' => $tenant,
            'list' => $paginator->paginate($query, $page, $limit),
        ]);
    }

    /**
     * @Route("/admin/tenants/{tenant}/fee-options/data")
     * @param Request $request
     * @param Tenant $tenant
     * @return \Symfony\Component\HttpFoundation\Response
     * @throws \LogicException
     */
    public function data(Request $request, Tenant $tenant)
    {
        $query = $this->query($request, $tenant);
        $data = $query->getQuery()->getArrayResult();
        return new SuccessResponse($data);
    }

    public function query(Request $request, Tenant $tenant = null)
    {
        $query = $this->em->getRepository(\CoreBundle\Entity\TenantFeeOption::class)
            ->createQueryBuilder('fi')
            ->join('fi.tenant', 'tenant');
        if ($tenant) {
            $query->where('fi.tenant = :tenant')
                ->setParameter('tenant', $tenant);
        }

        $params = new QueryListParams($query, $request, 'fi');
        $params->searchFields = [
            'tenant.name',
            'fi.originName',
            'fi.tranCode',
            'fi.function',
            'fi.transactionType',
            'fi.transactionStatus',
        ];
        $params->orderBy = [
            'tenant.name' => 'asc',
        ];
        return $this->queryListForPaginator($params);
    }

    protected function detailPageData (Tenant $tenant) {
        return [
            'tenant' => $tenant,
        ];
    }

    /**
     * @Route("/admin/tenants/{tenant}/fee-options/new", methods={"GET"})
     * @param Tenant $tenant
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function add(Tenant $tenant)
    {
        return $this->render('@Admin/Tenant/FeeOption/detail.html.twig',
            array_merge($this->detailPageData($tenant), [
                'entity' => null,
            ]));
    }

    /**
     * @Route("/admin/tenants/{tenant}/fee-options/{feeOption}/edit", methods={"GET"})
     * @param Tenant $tenant
     * @param TenantFeeOption $feeOption
     * @return \Symfony\Component\HttpFoundation\Response
     * @internal param FeeItem $feeItem
     */
    public function edit(Tenant $tenant, TenantFeeOption $feeOption)
    {
        return $this->render('@Admin/Tenant/FeeOption/detail.html.twig',
            array_merge($this->detailPageData($tenant), [
                'entity' => $feeOption,
            ]));
    }

    /**
     * @Route("/admin/tenants/{tenant}/fee-options/save", methods={"POST"})
     * @param Request $request
     * @param Tenant $tenant
     * @return MsgResponse
     */
    public function save(Request $request, Tenant $tenant)
    {
        $id = $request->get('id');
        if ($id) {
            $feeOption = $this->em->getRepository(\CoreBundle\Entity\TenantFeeOption::class)->find($id);
        } else {
            $feeOption = new TenantFeeOption();
            $feeOption->setTenant($tenant);
        }
        $this->fillEntity($feeOption);
        Util::persist($feeOption);
        return new SuccessMsgResponse('/admin/tenants/' . $tenant->getId() . '/fee-options');
    }

    /**
     * @Route("/admin/tenants/{tenant}/fee-options/{feeOption}/delete", methods={"POST"})
     * @param Tenant $tenant
     * @param TenantFeeOption $feeOption
     * @return \Symfony\Component\HttpFoundation\Response
     * @throws \Doctrine\ORM\OptimisticLockException
     * @throws \Doctrine\ORM\ORMInvalidArgumentException
     * @internal param FeeItem $feeItem
     */
    public function remove(Tenant $tenant, TenantFeeOption $feeOption)
    {
        if (!Util::eq($feeOption->getTenant(), $tenant)) {
            return new FailedMsgResponse();
        }
        $this->em->remove($feeOption);
        $this->em->flush();
        return new SuccessResponse();
    }
}