<?php
/**
 * User: Bob
 * Date: 2017/5/5
 * Time: 12:04
 */

namespace AdminBundle\Controller\Affiliate;


use AdminBundle\Controller\BaseController;
use Carbon\Carbon;
use CoreBundle\Entity\Affiliate;
use CoreBundle\Entity\AffiliateRevenueShare;
use CoreBundle\Entity\Module;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\QueryListParams;
use CoreBundle\Utils\Traits\DbTrait;
use CoreBundle\Utils\Util;
use Money\Exception\UnknownCurrencyException;
use SalexUserBundle\Entity\User;
use CoreBundle\Attribute\Template;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

/**
 * Affiliate Management
 * Class DefaultController
 * @package AdminBundle\Controller
 */
class DefaultController extends BaseController
{
    use DbTrait;

    /**
     * DefaultController constructor.
     */
    public function __construct()
    {
        parent::__construct();

        $this->authPermission(Module::ID_AFFILIATE);
    }

    /**
     * @Route("/admin/affiliate_index/{page}/{limit}",defaults={"page" = 1,"limit" = 10},name="admin_affiliate")
     */
    #[Template()]
    public function indexAction(Request $request,$page,$limit)
    {
        $allCurrencyCodes = $this->getDoctrine()
            ->getRepository(\CoreBundle\Entity\Currency::class)
            ->getAllCurrencyCodes();
        $paginator = $this->get('knp_paginator');
        $pagination= $paginator->paginate([], $page,$limit);
        return array(
            'list'             => $pagination,
            'allCurrencyCodes' => $allCurrencyCodes,
            'types'            => Affiliate::getTypes(),
        );
    }

    public function query(Request $request)
    {
        $query = $this->em->getRepository(\CoreBundle\Entity\Affiliate::class)
            ->createQueryBuilder('a');

        $params = new QueryListParams($query, $request, 'a');
        $params->orderBy = [
            'a.id' => 'ASC',
        ];
        $params->searchFields = [
            'a.affName',
            'a.affId',
            'a.affType',
        ];
        return $this->queryListForPaginator($params);
    }

    /**
     * @Route("/admin/affiliate/list/{page}/{limit}")
     * @param Request $request
     * @param int $page
     * @param int $limit
     * @return \Symfony\Component\HttpFoundation\Response
     * @throws \LogicException
     */
    public function listAction(Request $request, $page = 1, $limit = 10)
    {
        $paginator  = $this->get('knp_paginator');
        $query = $this->query($request);
        return $this->render('@Admin/Affiliate/Default/list.html.twig', [
            'list' => $paginator->paginate($query, $page, $limit),
            'host' => Util::host(),
        ]);
    }

    /**
     * @Route("/admin/affiliate/delete", name="admin_affiliate_delete")
     */
    public function deleteAction(Request $request)
    {
        if ($request->isMethod('POST')) {
            $affiliateId = $request->get('affiliate_id');

            $em = $this->getDoctrine()->getManager();
            $affiliate = $em->getRepository(\CoreBundle\Entity\Affiliate::class)->find($affiliateId);
            if (!$affiliate) {
                throw $this->createNotFoundException('No Affiliate found for id ' . $affiliateId);
            }

            $em->remove($affiliate);
            $em->flush();
        }

        return $this->redirect($this->generateUrl('admin_affiliate'));
    }

    /**
     * @Route("/admin/affiliate/set",name="admin_affiliate_set")
     * @throws \UnexpectedValueException
     * @throws \LogicException
     * @throws \InvalidArgumentException
     */
    public function setAction(Request $request)
    {
        $tenantId = $request->get('tenant');
        $returnUrl = $request->get('return_url');

        $tmp = $this->getDoctrine()
            ->getRepository(\CoreBundle\Entity\Affiliate::class)
            ->findBy(array(
                'tenant' => $tenantId,
            ));

        if(empty($tmp)) {
            $tenant = $this->em->getRepository(\CoreBundle\Entity\Tenant::class)->find($tenantId);

            $affiliate = new Affiliate();
            $affiliate->setTenant($tenant);

            $em = $this->getDoctrine()->getManager();
            $em->persist($affiliate);
            $em->flush();

            $affiliate->setAffName($affiliate->getAffName())
                ->persist();
            $affiliateStatus = 'Y';
        }else{
            $affiliateStatus = 'N';
        }
        return $this->redirect($this->generateUrl($returnUrl, array('affiliateStatus'=>$affiliateStatus)),301);
    }

    /**
     * @Route("/admin/getAffiliateRevenueShare.json", name="admin_get_affiliate_revenue_share")
     * @throws \UnexpectedValueException
     * @throws \InvalidArgumentException
     * @throws \LogicException
     */
    public function getAffiliateRevenueShare(Request $request)
    {
        $affiliateId = $request->get('affiliate_id');
        $affiliate = $this->em->getRepository(\CoreBundle\Entity\Affiliate::class)->find($affiliateId);
        $revenueInfo = $this->em->getRepository(\CoreBundle\Entity\AffiliateRevenueShare::class)->findBy(
                array('affiliate' =>$affiliate)
            );
        $arr = array();
        foreach ($revenueInfo as $revenue)
        {
            $tmpArr = array();
            $tmpArr['id'] = $revenue->getId();
            if($revenue->getCurrency() != null) {
                $tmpArr['fixed'] = $revenue->getFixed()
                    ? Money::formatAmount($revenue->getFixed(), $revenue->getCurrency())
                    : '';
                $tmpArr['cost'] = $revenue->getCost()
                    ? Money::formatAmount($revenue->getCost(), $revenue->getCurrency())
                    : '';
            }
            $tmpArr['currency'] = $revenue->getCurrency();
            $tmpArr['ratio'] = $revenue->getRatio();
            $tmpArr['start'] = $revenue->getStart();
            $arr[$revenue->getType()] = $tmpArr;
        }
        return new Response(json_encode($arr));
    }

    /**
     * @Route("/admin/setAffiliateRevenueShare.json", name="admin_set_affiliate_revenue_share")
     * @throws \Exception
     */
    public function setAffiliateRevenueShare(Request $request)
    {
        Util::longRequest();

        $id = $request->get('id');
        $type = $request->get('type');
        $fixed = $request->get('fixed');
        $cost = $request->get('cost');
        $currency = $request->get('currency');
        $ratio = $request->get('ratio');
        $start = $request->get('start');
        $affiliateId = $request->get('affiliate_id');

        $em = $this->getDoctrine()->getManager();
        $affiliate = $em->getRepository(\CoreBundle\Entity\Affiliate::class)->find($affiliateId);
        try
        {
            if ($id) {
                $revenue = $em->getRepository(\CoreBundle\Entity\AffiliateRevenueShare::class)->find($id);
            } else {
                $revenue = new AffiliateRevenueShare();
                $revenue->setType($type)->setAffiliate($affiliate);
            }
            $revenue->setFixed($fixed ? Money::normalizeAmount($fixed, $currency) : null)
                ->setCost($cost ? Money::normalizeAmount($cost, $currency) : null)
                ->setRatio($ratio ?: null)
                ->setStart($start ?: null)
                ->setCurrency($currency);
            $em->persist($revenue);
            $em->flush();
        } catch (UnknownCurrencyException $e) {
            return new Response(json_encode(['status'=>'failed', 'errMsg'=> 'Do not support currency : ' .$currency]));
        }

        $since = $request->get('since');
        if ($since) {
            $since = new Carbon($since);
            $em = Util::em();
            $q = $em->getRepository(\CoreBundle\Entity\UserCardLoad::class)
                ->createQueryBuilder('ucl')
                ->join('ucl.userCard', 'uc')
                ->join('uc.user', 'u');
            $e = $q->expr();
            $rs = $q->where($e->in('ucl.loadStatus', ':statuses'))
                ->andWhere('ucl.type = :loadType')
                ->andWhere('u.affiliate = ' . $affiliateId)
                ->andWhere($e->gte('ucl.receivedAt', ':since'))
                ->setParameters([
                    'loadType' => UserCardLoad::TYPE_LOAD_CARD,
                    'statuses' => UserCardLoad::RECEIVED_STATUS_ARRAY,
                    'since' => $since,
                ])
                ->getQuery()
                ->getResult();
            /** @var UserCardLoad $r */
            foreach ($rs as $r) {
                $r->fillCommission(true);
                $em->persist($r);
            }
            $em->flush();
        }

        return new Response(json_encode(['status'=>'success', 'type'=>$type, 'updateId'=>$revenue->getId()]));
    }

    /**
     * @\Symfony\Component\Routing\Annotation\Route("/admin/user/update-accessible-affiliates/{user}", methods={"POST"})
     * @param Request $request
     * @param User $user
     * @return SuccessResponse
     * @throws \Exception
     */
    public function updateAccessibleAffiliates(Request $request, User $user)
    {
        $this->authSuperAdmin();

        foreach ($user->getAccessibleAffiliates() as $affiliate) {
            $user->removeAccessibleAffiliate($affiliate);
        }

        $em = Util::em();
        $repo = $em->getRepository(\CoreBundle\Entity\Affiliate::class);
        $items = $request->get('items');
        foreach ((array)$items as $item) {
            $affiliate = $repo->find($item);
            $user->addAccessibleAffiliate($affiliate);
        }

        $em->persist($user);
        $em->flush();

        return new SuccessResponse();
    }

}
