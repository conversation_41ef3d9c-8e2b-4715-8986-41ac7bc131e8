<?php
/**
 * Created by PhpStorm.
 * User: hans
 * Date: 2017/6/15
 * Time: 下午1:28
 */

namespace AdminBundle\Controller;


use CoreBundle\Entity\Tenant;
use CoreBundle\Exception\RenderViewException;
use CoreBundle\Utils\Util;
use PortalBundle\Controller\Common\AuthenticatedController;
use Stringy\Stringy;
use Symfony\Component\HttpFoundation\Request;

class BaseController extends AuthenticatedController
{
    public function __construct()
    {
        parent::__construct();

        $this->authAdmin();

        // Redirect to new theme
        $uri = $this->request->getRequestUri();
        if (strpos($uri, '?v=old') === false) {
            $maps = [
                '^\/admin\/user_index$' => '/a/user-management/user',
            ];
            foreach ($maps as $from => $to) {
                $replace = preg_replace('|' . $from . '|', $to, $uri);
                if ($replace !== $uri) {
                    throw new RenderViewException('@Admin/md-redirect.html.twig', [
                        'route' => $replace,
                    ]);
                }
            }
        }
    }

    public function fillEntityHook($entity, array &$input, array $fields)
    {}

    public function fillEntity($entity, array $input = null, array $fields = null)
    {
        if ($fields === null) {
            $rc = new \ReflectionClass($entity);
            if ($rc->hasConstant('FILLS')) {
                $fields = $rc->getConstant('FILLS');
            }
        }
        if ($input === null) {
            $input = Util::request()->request->all();
        }
        $this->fillEntityHook($entity, $input, $fields);
        foreach ((array)$fields as $field) {
            if (isset($input[$field])) {
                $method = 'set' . Stringy::create($field)->upperCamelize();
                if (method_exists($entity, $method)) {
                    $entity->$method($input[$field]);
                }
            }
        }
    }


    public function authPermissionForTenant($pattern)
    {
        $path = Util::request()->getPathInfo();
        $matches = [];
        preg_match('|' . $pattern . '|', $path, $matches);
        if (!empty($matches[1])) {
            $tenant = $this->em->getRepository(\CoreBundle\Entity\Tenant::class)->find($matches[1]);
            if ($tenant) {
                $type = $tenant->getType();
                if ($type && isset(Tenant::MODULE_MAP[$type])) {
                    $this->authPermission(Tenant::MODULE_MAP[$type]);
                }
            }
        }
    }

     /**
     * @param $request Request
     * @param $method
     * @param $prefix
     *
     * @return string
     */
    public function createCacheKey($request, $method = '', $prefix = 'CORE') {
      $path = $request->headers->get('SIMULATE_PATH_INFO') ?? $request->getPathInfo();

      $p = $request->query->all();
      $cacheKey = $prefix . '.' . $method . '.' . str_replace('/','.', $path);
      $cacheKey = str_replace('..','.',$cacheKey);
      $cacheKey = ltrim($cacheKey,'.');
      foreach($p AS $key => $value) {
          if ($key != '_t')
          try {
              $cacheKey = $cacheKey . '.' . $key . '.' . $value;
          }
          catch(\Exception $e) {
              $cacheKey = $cacheKey . '.' . $key . '.' . implode(".", $value);
          }
      }

      return $cacheKey;
  }
}
