<?php
/**
 * Created by PhpStorm.
 * User: hans
 * Date: 2017/9/11
 * Time: 上午10:26
 */

namespace AdminBundle\Controller;


use Carbon\Carbon;
use CoreBundle\Entity\Affiliate;
use CoreBundle\Entity\GrayCountry;
use CoreBundle\Entity\Module;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Bundle;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\QueryListParams;
use CoreBundle\Utils\Service;
use CoreBundle\Utils\Traits\DbTrait;
use CoreBundle\Utils\Traits\ExcelTrait;
use CoreBundle\Utils\Util;
use Doctrine\ORM\Query\Parameter;
use Doctrine\ORM\QueryBuilder;
use Faker\Factory;
use SalexUserBundle\Entity\User;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use function Stringy\create as s;
use UsUnlockedBundle\Services\Privacy\PrivacyAPI;

class LoadTransactionController extends BaseController
{
    use ExcelTrait;
    use DbTrait;
    protected $tzUTC;
    protected $tzUser;
    /**
     * DefaultController constructor.
     */
    public function __construct()
    {
        parent::__construct();

        $this->authPermission(Module::ID_LOAD_TRANSACTION_LIST);
    }

    protected function getGrayCountries($onlyId = false) {
      $cp = Util::$cardProgram;
      $rs = Util::em()->getRepository(\CoreBundle\Entity\GrayCountry::class)->findBy([
       'cardProgram' => $cp
       ]);

       if ($onlyId) {
        $ids = [];
        foreach ($rs as $item) {
          $ids[] = $item->getCountry()->getId();
        }
        return $ids;
       }

       $grayCountries = $rs ? [
        [
          'id' => 'all',
          'name' => 'All'
        ]
       ] : [];

       /** @var GrayCountry $item */
       foreach ($rs as $item) {
         $grayCountries[] = [
           'id' => $item->getCountry()->getId(),
           'name' => $item->getCountry()->getName()
         ];
       }
      return $grayCountries;
    }

    public function getSearchFilters()
    {
        $em = $this->em;
        $user = $this->authAdmin();

        return [
            'status'            => '',
            'grayCountries'     => $this->getGrayCountries(),
            'loadStatuses'      => UserCardLoad::ALL_LOAD_STATUS,
            'types'             => UserCardLoad::ALL_TYPES,
            'cardPrograms'      => $user->getOpenCardPrograms(),
            'cardTypes'         => $em->getRepository(\CoreBundle\Entity\CardType::class)->findAll(),
            'cpCardTypes'       => $em->getRepository(\CoreBundle\Entity\CardProgramCardType::class)->findAll(),
            'programManagers'   => $em->getRepository(\CoreBundle\Entity\ProgramManager::class)->findAll(),
            'marketingPartners' => $em->getRepository(\CoreBundle\Entity\MarketingPartner::class)->findAll(),
            'processors'        => $em->getRepository(\CoreBundle\Entity\Processor::class)->findAll(),
            'countries'         => $em->getRepository(\CoreBundle\Entity\Country::class)->countries(),
            'regions'           => $em->getRepository(\CoreBundle\Entity\Country::class)->regions(),
            'states'            => $em->getRepository(\CoreBundle\Entity\State::class)->findAllMinFields(),
            'reshippers'        => $em->getRepository(\CoreBundle\Entity\Reshipper::class)->findAll(true),
            'loadPartners'      => $em->getRepository(\CoreBundle\Entity\LoadPartner::class)->findAll(),
            'loadMethods'       => $em->getRepository(\CoreBundle\Entity\LoadMethod::class)->findAll(),
            'affiliates'        => $em->getRepository(\CoreBundle\Entity\Affiliate::class)->findAll(),
            'currencies'        => $em->getRepository(\CoreBundle\Entity\Currency::class)->getAllCurrencyCodes(),
        ];
    }

    public function getTopChartData(QueryBuilder $query) {
        $request = Util::request();
        $ranges = $request->get('range');
        $start = $end = null;
        if ($ranges) {
            if (is_string($ranges)) {
                $ranges = (array)json_decode($ranges, TRUE);
            }
            foreach ($ranges as $field => $dates) {
                $start = $dates['start'] ?? null;
                $end = $dates['end'] ?? null;
                if ($start) {
                    $start = Util::timeUTC($start);
                }
                if ($end) {
                    $end = Util::timeUTC($end);
                }
            }
        }

        $dates = [];
        $rangeType = Util::getRangeType($start, $end);
        $allQuery = clone $query;
        $rs = $allQuery->orderBy('ucl.loadAt')
            ->select('uc.id, min(ucl.loadAt) loadAt, ucl.loadAmountUSD loadAmountUSD')
            ->groupBy('ucl.id')
            ->getQuery()
            ->getArrayResult();

        $demoRs = [];
        if (Util::isDemo()) {
            $faker = Factory::create();
            $count = $faker->numberBetween(30, 100);
            for ($i = 0; $i < $count; $i++) {
                $demoRs[] = [
                    'id' => $faker->unique(),
                    'loadAt' => $faker->dateTimeBetween($start, $end)->format('c'),
                    'loadAmountUSD' => $faker->numberBetween(5000, 100000),
                    'reload' => $faker->boolean,
                ];
            }
            $rs = array_merge($rs, $demoRs);
            Util::usort($rs, [
                'loadAt' => true,
            ]);
        }

        $dataA = $this->dataWithLoadAt($rs, $dates, $rangeType, 'all');
        $dates = $dataA['dates'];

        $qFirst = clone $query;

        $qFirst->andWhere('ct.isreloaded = 1');
        $rs = $qFirst->orderBy('ucl.loadAt')
            ->select('uc.id, min(ucl.loadAt) loadAt, ucl.loadAmountUSD loadAmountUSD')
            ->groupBy('uc.id')
            ->getQuery()
            ->getArrayResult();

        if ($demoRs) {
            $rs = array_merge($rs, array_filter($demoRs, function ($d) {
                return $d['reload'];
            }));
            Util::usort($rs, [
                'loadAt' => true,
            ]);
        }

        $dataR = $this->dataWithLoadAt($rs, $dates, $rangeType, 'reload');
        $dates = $dataR['dates'];

        $qReload = clone $query;
        $qReload->andWhere('ct.isreloaded = 0');
        $rs = $qReload->orderBy('ucl.loadAt')
            ->select('uc.id, min(ucl.loadAt) loadAt, ucl.loadAmountUSD loadAmountUSD')
            ->groupBy('uc.id')
            ->getQuery()
            ->getArrayResult();

        if ($demoRs) {
            $rs = array_merge($rs, array_filter($demoRs, function ($d) {
                return !$d['reload'];
            }));
            Util::usort($rs, [
                'loadAt' => true,
            ]);
        }

        $dataF = $this->dataWithLoadAt($rs, $dates, $rangeType, 'first');
        $dates = $dataF['dates'];
        $result = [
            'dates' => array_keys($dates),
            'all' => [],
            'first' => [],
            'reload' => [],
        ];
        $result['allLoad'] = $dataA['loadAmount'];
        $result['reloadLoad'] = $dataR['loadAmount'];
        $result['firstLoad'] = $dataF['loadAmount'];

        foreach ($dates as $date => $meta) {
            $result['all'][] = $meta['all'];
            $result['first'][] = $meta['first'];
            $result['reload'][] = $meta['reload'];
        }

        return $result;
    }
    protected function dataWithLoadAt($rs, $dates, $rangeType, $type) {
        $loadAmount = 0;
        foreach ($rs as $r) {
            $key = $this->getRangeKey($r['loadAt'], $rangeType);
            if (!isset($dates[$key])) {
                $dates[$key] = [
                    'all' => 0,
                    'first' => 0,
                    'reload' => 0,
                ];
            }
            $dates[$key][$type] += $r['loadAmountUSD'];
            $loadAmount += $r['loadAmountUSD'];
        }
        $data['dates'] = $dates;
        $data['loadAmount'] = $loadAmount;
        return $data;
    }

    protected function getRangeKey($dateString, $type)
    {
        $date = new Carbon($dateString, $this->tzUTC);
        $date->setTimezone($this->tzUser);
        return $date->format($type);
    }


    /**
     * @Route("/admin/load_transaction_list/{page}/{limit}",defaults={"page" = 1,"limit" =
     *                                                                       10},name="admin_load_transaction_list")
     * @Route("/admin/load_queue/{page}/{limit}",defaults={"page" = 1,"limit" = 10}, name="admin_load_queue")
     * @param Request $request
     * @param $page
     * @param $limit
     * @return \Symfony\Component\HttpFoundation\Response
     * @throws \Doctrine\ORM\NonUniqueResultException
     * @throws \Doctrine\ORM\NoResultException
     * @throws \LogicException
     */
    public function indexAction(Request $request, $page, $limit)
    {
        $paginator  = $this->get('knp_paginator');
        if ($request->isXmlHttpRequest()) {
            $query = $this->query($request);

            $notRequest = Util::createRequestForPredicateNot($request);
            if ($notRequest) {
                $notSearchQuery = $this->query($notRequest);
                Util::parseDQLForPredicateNot($query, $notSearchQuery, 'ucl.id');
            }

            $originalQuery = clone $query;

            $originalRealQuery = clone $originalQuery;
            Util::queryRealConsumers($originalRealQuery);

            $filter = $request->request->all('filter');
            if (isset($filter['ucl.loadStatus='])) {
                $data['status'] = $filter['ucl.loadStatus='];
            }

            $data['loadCount'] = (clone $originalRealQuery)->select('count(ucl)')->getQuery()->getSingleScalarResult();
            $data['loadAmount'] = (clone $originalRealQuery)
                ->andWhere('ucl.loadStatus = :__loadStatus')
                ->setParameter('__loadStatus', UserCardLoad::LOAD_STATUS_LOADED)
                ->select('sum(ucl.loadAmountUSD)')
                ->getQuery()
                ->getSingleScalarResult();
            $data['loadMembershipFee'] = (clone $originalRealQuery)
                ->andWhere('ucl.loadStatus = :__loadStatus')
                ->setParameter('__loadStatus', UserCardLoad::LOAD_STATUS_LOADED)
                ->select('sum(ucl.membershipFeeUSD)')
                ->getQuery()
                ->getSingleScalarResult();
            $data['loadFee'] = (clone $originalRealQuery)
                ->andWhere('ucl.loadStatus = :__loadStatus')
                ->setParameter('__loadStatus', UserCardLoad::LOAD_STATUS_LOADED)
                ->select('sum(ucl.loadFeeUSD)')
                ->getQuery()
                ->getSingleScalarResult();
            $data['loadCost'] = (clone $originalRealQuery)->select('sum(ucl.cost)')->getQuery()->getSingleScalarResult();
            $data['revenue'] = (clone $originalRealQuery)->select('sum(ucl.revenue)')->getQuery()->getSingleScalarResult();

            $currency = $filter['ucl.receivedLocalCurrency='] ?? '';
            if ($currency) {
                $data['loadLocalCurrency'] = $currency;
                $data['loadLocalAmount'] = (clone $originalRealQuery)->select('sum(ucl.receivedLocalAmount)')->getQuery()->getSingleScalarResult();
            }

            if (Bundle::isUsUnlocked()) {
                $expr = Util::expr();
                $totalBalanceSubQuery = clone $originalRealQuery;
                $totalBalanceUcQuery = $this->em->getRepository(UserCard::class)
                    ->createQueryBuilder('_uc')
                    ->join('_uc.user', '_u')
                    ->join('_uc.loads', '_ucl')
                    ->where('_uc.type = :_type')
                    ->andWhere('_uc.balance is not null')
                    ->andWhere('_uc.balance > 0')
                    ->andWhere('_u.status = :_userStatus')
                    ->andWhere($expr->eq('_ucl.id', $expr->any($totalBalanceSubQuery->getDQL())))
                    ->select('_uc.id')
                    ->distinct()
                ;

                $totalBalanceQuery = $this->em->getRepository(UserCard::class)
                    ->createQueryBuilder('__uc')
                    ->where($expr->eq('__uc.id', $expr->any($totalBalanceUcQuery->getDQL())))
                    ->setParameter('_type', PrivacyAPI::CARD_TYPE_DUMMY)
                    ->setParameter('_userStatus', User::STATUS_ACTIVE);

                /** @var Parameter $parameter */
                foreach ($totalBalanceSubQuery->getParameters() as $parameter) {
                    $totalBalanceQuery->setParameter($parameter->getName(), $parameter->getValue());
                }
                $totalBalanceSubQuery->setParameters([]);

                $data['totalBalance'] = (clone $totalBalanceQuery)->select('sum(__uc.balance)')
                    ->getQuery()
                    ->getSingleScalarResult() ?: 0;

                $data['usersWithBalance'] = (clone $totalBalanceQuery)->select('count(__uc)')
                    ->getQuery()
                    ->getSingleScalarResult() ?: 0;
            }

            if (Util::isMdRequest()) {
                $chartData = null;
                if ($request->get('isFromDashboard')) {
                    $chartData = $this->getTopChartData($query);
                }
                $data['avgLoadAmount'] = $data['loadCount'] ? round($data['loadAmount'] / $data['loadCount'], 2) : 0;
                return new SuccessResponse([
                    'data' => array_map(function (UserCardLoad $load) {
                        return $load->toApiArrayWithNegative();
                    }, (clone $query)
                        ->setFirstResult(($page - 1) * $limit)
                        ->setMaxResults($limit)
                        ->getQuery()
                        ->getResult()),
                    'total' => $data['loadCount'],
                    'quick' => $data,
                    'chartData' => $chartData,
                ]);
            }

            $data['userCardLoads'] = $paginator->paginate($query, $page, $limit);
            return $this->render('@Admin/LoadTransaction/list.html.twig', $data);
        }
        return $this->render('@Admin/LoadTransaction/index.html.twig', array_merge($this->getSearchFilters(), [
            'userCardLoads'     => $paginator->paginate([]),
            'status'            => strpos($request->getUri(), '/admin/load_queue') !== false ? 'received' : '',
        ]));
    }

    /**
     * @Route("/admin/load_transaction/filters")
     * @return SuccessResponse
     */
    public function searchFiltersAction() {
        $all = $this->getSearchFilters();

        $data = Util::allToFilterOptions($all, [
            'loadStatuses' => null,
            'types' => null,
            'cardPrograms' => null,
            'cardTypes' => null,
            'cpCardTypes' => null,
            'programManagers' => null,
            'marketingPartners' => null,
            'processors' => null,
            'countries' => null,
            'regions' => [
                'value' => 'region',
                'label' => 'region',
            ],
            'states' => [
                'label' => 'stateName',
                'country' => 'country',
            ],
            'reshippers' => null,
            'loadPartners' => null,
            'loadMethods' => null,
            'affiliates' => [
                'type' => 'type',
            ],
            'currencies' => null,
            'grayCountries' => null
        ]);

        $data['affiliateTypes'] = Util::toFilterOptions([
            ['id' => 'load_partner', 'name' => 'Load Partner'],
            ['id' => 'kyc_provider', 'name' => 'Kyc Provider'],
            ['id' => 'marketing_partner', 'name' => 'Marketing Partner'],
            ['id' => 'reshipper', 'name' => 'Reshipper'],
        ]);

        return new SuccessResponse($data);
    }

    /**
     * @Route("/admin/constraints/load_report")
     */
    public function constraintsAction()
    {
        $data = $this->getSearchFilters();
        $data['page'] = '@Admin/LoadTransaction/search.html.twig';
        $data['reportSchedule'] = true;
        return $this->render('@Admin/constraints.html.twig', $data);
    }

    protected function isLoadQueue(Request $request)
    {
        $filter = $request->get('filter');
        $status = $filter['ucl.loadStatus='] ?? null;
        return $status === UserCardLoad::LOAD_STATUS_RECEIVED;
    }

    protected function query(Request $request)
    {
        Util::adjustTimeRangeForDemo($request);
        $serverKey = Util::getServerKey();

        $filter = $request->request->all('filter');
        if (isset($filter['ucl.loadStatus']) && $filter['ucl.loadStatus'] === 'all') {
            $filter['ucl.loadStatus'] = '';
            $request->request->set('filter', $filter);
        }

        $query = $this->em()->getRepository(\CoreBundle\Entity\UserCardLoad::class)
            ->createQueryBuilder('ucl');

        $params = new QueryListParams($query, $request, 'ucl, uc, u');
        $params->searchFields = [
            'ucl.transactionNo',
            'ucl.paymentReference',
            'u.id',
        ];
        $params->joins = [
            'uc' => 'ucl.userCard',
            'u' => 'uc.user',
            'c' => 'uc.card',
            'ct' => 'c.cardType',
            'cp' => 'c.cardProgram',
            'country' => 'u.country',
        ];
        $params->optionalJoins = [
            'pm' => 'ct.programManager',
            'p' => 'ct.processor',
            'ib' => 'ct.issuingBank',
            'bp' => 'cp.brandPartner',
            'mp' => 'cp.marketingPartner',
            'ba' => 'uc.billingAddress',
            'r' => 'ba.reshipper',
            'country' => 'u.country',
            'state' => 'u.state',
            'aff' => 'u.affiliate',
        ];
        $params->distinct = false;

        if ($this->isLoadQueue($request)) {
            $params->orderBy = [
                'ucl.receivedAt' => 'asc',
            ];
        } else {
            $params->orderBy = [
                'ucl.createdAt' => 'desc',
            ];
        }

        $params->extraQueryCallback = function (QueryBuilder $query, Request $request) use ($serverKey) {
            $expr = $query->expr();
            $user = $this->getUser();
            $query->andWhere($expr->in('cp', ':__cps'))
                ->setParameter('__cps', $user->getOpenCardPrograms());

            $query->andWhere('ucl.serverKey = :__serverKey')
                ->andWhere($expr->notIn('ucl.type', ':__types'))
                ->setParameter('__serverKey', $serverKey)
                ->setParameter('__types', [
                    UserCardLoad::TYPE_LOAD_TEST,
                    UserCardLoad::TYPE_UNLOAD_TEST,
                ]);

            if ($user->isAffiliate()) {
                $query->andWhere($expr->in('u.affiliate', ':__affiliates'))
                    ->setParameter('__affiliates', $user->getManagingAffiliates(true));
            }

            $feeType = $request->get('fee_type');
            if ($feeType) {
                $query->andWhere($expr->isNotNull('ucl.'. $feeType . 'USD'));
                $feeMin = $request->get('fee_type_min');
                if ($feeMin) {
                    $query->andWhere($expr->gte('ucl.'. $feeType . 'USD', $feeMin * 100));
                } else {
                    $query->andWhere($expr->gt('ucl.'. $feeType . 'USD', 0));
                }
                $feeMax = $request->get('fee_type_max');
                if ($feeMax) {
                    $query->andWhere($expr->lte('ucl.'. $feeType . 'USD', $feeMax * 100));
                }
            }

            $grayCountry = $request->get('grayCountryId');
            if ($grayCountry) {
              if ($grayCountry == 'all') {
                $query->andWhere($expr->in('country.id', $this->getGrayCountries(true)));
              } else {
                $query->andWhere($expr->eq('country.id', $grayCountry));
              }
            }
            $query->andWhere($expr->orX(
                $expr->isNotNull('ucl.paymentReference'),
                $expr->in('ucl.loadStatus', ':__loadStatuses')
            ))->setParameter('__loadStatuses', [
                UserCardLoad::LOAD_STATUS_RECEIVED,
                UserCardLoad::LOAD_STATUS_LOADED,
            ]);
        };
        return $this->queryListForPaginator($params);
    }

    /**
     * @Route("/admin/load_transaction_export")
     * @param Request $request
     * @return Response
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     */
    public function reportAction(Request $request) {
        Util::longRequest();

        $query = $this->query($request);
        if ($request->get('query_count')) {
            $count = $this->queryCount($query, 'count(distinct ucl)');
            return new SuccessResponse($count);
        }

        $from  = (int)$request->get('query_from', 0);
        $limit = (int)$request->get('query_limit', 5000);

        $userCardLoads = $query->setFirstResult($from)
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();

        $headers = [
            'Txn ID'               => 16,
            'Payment ID'           => 16,
            'Time'                 => 18,
            'Type'                 => 16,
            'Status'               => 10,
            'Card Type'            => 24,
            'User ID'              => 10,
            'First Name'           => 20,
            'Last Name'            => 20,
            'Email address'        => 30,
            'Account number'       => 20,
            'Billing Address type' => 20,
            'Load Partner'         => 16,
            'Load Method'          => 16,
            'Country'              => 12,
            'City'                 => 14,
            'State'                => 10,
            'Region'               => 10,
            'Initial Amount'       => 16,
            'Load fee'             => 10,
            'Membership Fee'       => 16,
            'Replacement Fee'      => 17,
            'Unload Fee'           => 12,
            'Discount'             => 10,
            'Currency Rate'        => 20,
            'Total Cost'           => 16,
            'Total Cost currency'  => 16,
            'Revenue'              => 16,
            'Received Amount'      => 16,
            'Received Currency'    => 16,
            'Received Amount(USD)' => 16,
            'Load Amount'          => 14,
            'Total Loaded'         => 16,
            'Load Date'            => 18,
            'Load Error'           => 60,
            'Affiliate'            => 20,
            'Affiliate Commission' => 30,
        ];
        $textColumns = [1, 10];

        $filename = 'Load transaction report_' . $request->get('query_timestamp', time());
        $destination = Util::uploadDir('report') . $filename . '.xlsx';
        if (file_exists($destination)) {
            $excel = $this->loadExcel($destination);
        } else {
            $excel = $this->createXSLObject($filename);
        }

        $excel = $this->generateSheetAppendToExcel($excel, $from . ' ~ ' . ($from + $limit),
            $headers, $userCardLoads, function ($col, UserCardLoad $load) {
                $uc = $load->getUserCard();
                $user = $uc->getUser();
                $ba = $uc->getBillingAddress();
                /** @var UserCardLoad $load */
                switch ($col) {
                    case 0:
                        return $load->getTransactionNo();
                    case 1:
                        return $load->getPaymentReference();
                    case 2:
                        return Util::formatDateTime($load->getCreatedAt());
                    case 3:
                        return s($load->getType())->humanize();
                    case 4:
                        return ucfirst($load->getLoadStatus());
                    case 5:
                        return $uc->getCard()->getFullName();
                    case 6:
                        return $user->getId();
                    case 7:
                        return $user->getFirstName();
                    case 8:
                        return $user->getLastName();
                    case 9:
                        return $user->getEmail();
                    case 10:
                        return $uc->getAccountNumber();
                    case 11:
                        return $ba ? $ba->getReshipper()->getDba() : '';
                    case 12:
                        return $load->getPartnerName();
                    case 13:
                        return $load->getMethodName();
                    case 14:
                        return $user->getCountry() ? $user->getCountry()->getName() : '';
                    case 15:
                        return $user->getCity();
                    case 16:
                        return $user->getState() ? $user->getState()->getStateName() : '';
                    case 17:
                        return $user->getCountry() ? $user->getCountry()->getRegion() : '';
                    case 18:
                        return Money::formatAmount($load->getInitialAmount(), 'USD', '');
                    case 19:
                        return Money::formatAmount($load->getLoadFeeUSD(), 'USD', '');
                    case 20:
                        return Money::formatAmount($load->getMembershipFeeUSD(), 'USD', '');
                    case 21:
                        return Money::formatAmount($load->getReplacementFeeUSD(), 'USD', '');
                    case 22:
                        return Money::formatAmount($load->getUnloadFeeUSD(), 'USD', '');
                    case 23:
                        return Money::formatAmount($load->getDiscountUSD(), 'USD', '');
                    case 24:
                        $rate = $load->getCurrencyRate();
                        if (!$rate) {
                            return '';
                        }
                        return $load->getInitialCurrency() . ' -> ' . $load->getPayCurrency() . ': ' . $rate;
                    case 25:
                        return $load->getCost()
                            ? Money::formatAmount($load->getCost(), 'USD', '')
                            : '';
                    case 26:
                        return $load->getPayCurrency();
                    case 27:
                        return $load->getRevenue()
                            ? Money::formatAmount($load->getRevenue(), 'USD', '')
                            : '';
                    case 28:
                        return $load->getReceivedLocalCurrency()
                            ? Money::formatAmount($load->getReceivedLocalAmount(), $load->getReceivedLocalCurrency(), '')
                            : '';
                    case 29:
                        return $load->getReceivedLocalCurrency();
                    case 30:
                        return Money::formatAmount($load->getReceivedAmountUSD(), 'USD', '');
                    case 31:
                        return Money::formatAmount($load->getLoadAmountUSD(), 'USD', '');
                    case 32:
                        if ($load->getLoadStatus() === UserCardLoad::LOAD_STATUS_LOADED) {
                            return Money::formatAmount($load->getLoadAmountUSD(), 'USD', '');
                        }
                        return '';
                    case 33:
                        return Util::formatDateTime($load->getLoadAt());
                    case 34:
                        return Util::json($load, 'error', 'load');
                    case 35:
                        return $user->getAffiliateName();
                    case 36:
                        return $load->getCommissionUSD()
                            ? Money::formatAmount($load->getCommissionUSD(), 'USD', '')
                            : '';
                    default:
                        return '';
                }
            }, $textColumns);
        $count = count($userCardLoads);
        foreach (['S', 'T', 'U', 'V', 'X', 'Y', 'AB', 'AC', 'AD', 'AE'] as $column) {
            $this->setColumnNumberMoney($column, $count);
        }
        $this->setColumnWrapText('AC', $count);

        $excel->setActiveSheetIndex(0);
        $this->saveExcelTo($excel, $destination);

        return new SuccessResponse('report/' . $filename . '.xlsx');
    }

    /**
     * @Route("/admin/load_transaction/mark_refunded/{load}")
     * @param Request $request
     * @param UserCardLoad $load
     * @return SuccessResponse
     * @throws \Exception
     */
    public function refunded(Request $request, UserCardLoad $load)
    {
        $load->setLoadStatus(UserCardLoad::LOAD_STATUS_REFUNDED)
            ->persist();
        $load->getUserCard()->getUser()
            ->addNote('Refunded load transaction ' . $load->getTransactionNo());
        return new SuccessResponse();
    }

    /**
     * @Route("/admin/load_transaction/mark_error/{load}")
     * @param Request $request
     * @param UserCardLoad $load
     * @return SuccessResponse
     * @throws \Exception
     */
    public function markError(Request $request, UserCardLoad $load)
    {
        $load->setLoadStatus(UserCardLoad::LOAD_STATUS_ERROR)
            ->persist();
        $load->getUserCard()->getUser()
            ->addNote('Mark load transaction ' . $load->getTransactionNo() . ' error and remove from the load queue.');
        return new SuccessResponse();
    }

    /**
     * @Route("/admin/load_transaction/force_load/{load}")
     * @param Request $request
     * @param UserCardLoad $load
     * @return SuccessResponse
     * @throws \Exception
     */
    public function forceLoad(Request $request, UserCardLoad $load)
    {
        Util::updateMeta($load, 'delayedLoadAt');

        Service::loadCard($load);

        $load->getUserCard()->getUser()
            ->addNote('Force load transaction ' . $load->getTransactionNo() . ' by ' . $this->getUser()->getId());
        return new SuccessResponse();
    }
}
