<?php
/**
 * User: Bob
 * Date: 2017/2/21
 * Time: 12:42
 * This is used to handle all the actions for Fee Global Name Menu
 */

namespace AdminBundle\Controller\Fee;


use AdminBundle\Controller\BaseController;
use CoreBundle\Entity\FeeGlobalName;
use CoreBundle\Entity\Module;
use CoreBundle\Utils\QueryListParams;
use CoreBundle\Utils\Traits\DbTrait;
use Stringy\Stringy;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use CoreBundle\Attribute\Template;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;


class GlobalNameController extends BaseController
{
    use DbTrait;

    /**
     * DefaultController constructor.
     */
    public function __construct()
    {
        parent::__construct();

        $this->authPermission(Module::ID_FEE_GLOBAL_NAME);
    }

    /**
     * @Route("/admin/fee-global-name",name="admin_fee_global_name")
     * @throws \LogicException
     */
    #[Template()]
    public function indexAction()
    {
        return Array(
            'items'=> $this->get('knp_paginator')->paginate([]),
            'categories' => $this->em->getRepository(\CoreBundle\Entity\FeeCategory::class)->findAll(),
            'types' => FeeGlobalName::getConstantsValuesStartWith('TYPE'),
        );
    }

    /**
     * @Route("/admin/fee-global-name/list/{page}/{limit}")
     * @param Request $request
     * @param int $page
     * @param int $limit
     * @return \Symfony\Component\HttpFoundation\Response
     * @throws \LogicException
     */
    #[Template()]
    public function list(Request $request, $page = 1, $limit = 15)
    {
        $query = $this->query($request);
        return $this->render('@Admin/Fee/GlobalName/list.html.twig', [
            'items' => $this->get('knp_paginator')->paginate($query, $page, $limit),
        ]);
    }

    protected function query(Request $request)
    {
        $query = $this->em->getRepository(\CoreBundle\Entity\FeeGlobalName::class)
            ->createQueryBuilder('g');

        $params = new QueryListParams($query, $request, 'g');
        $params->orderBy = [
            'g.name' => 'asc',
        ];
        return $this->queryListForPaginator($params);
    }

    /**
     * @Route("/admin/fee-global-name/new", name="admin_fee_global_name_new")
     */
    #[Template()]
    public function newAction(Request $request)
    {
        $feeGlobalName = new FeeGlobalName();
        $form = $this->createFeeGlobalNameForm($feeGlobalName);

        if ($request->isMethod('POST')) {
            $form->handleRequest($request);

            if ($form->isValid()) {
                $em = $this->getDoctrine()->getManager();
                $em->persist($feeGlobalName);
                $em->flush();
                return $this->redirect($this->generateUrl('admin_fee_global_name'));
            }
        }
        return(array('form' => $form->createView(),));
    }

    /**
     * @Route("/admin/fee-global-name/modify", name="admin_fee_global_name_modify")
     */
    #[Template()]
    public function modifyAction(Request $request)
    {
        $feeGlobalNameId = $request->get("fee_global_name_id");

        $em = $this->getDoctrine()->getManager();
        $feeGlobalName = $em->getRepository(\CoreBundle\Entity\FeeGlobalName::class)->find($feeGlobalNameId);
        if (!$feeGlobalName) {
            throw $this->createNotFoundException('No Fee Global Name found for id ' . $feeGlobalNameId);
        }
        $form = $this->createFeeGlobalNameForm($feeGlobalName);

        if ($request->isMethod('POST')) {
            $form->handleRequest($request);

            if ($form->isValid()) {
                $em->flush();

                return $this->redirect($this->generateUrl('admin_fee_global_name'));
            }
        }

        return array('form' => $form->createView(), 'fee_global_name_id' => $feeGlobalNameId);
    }

    /**
     * @Route("/admin/fee-global-name/delete", name="admin_fee_global_name_delete")
     */
    #[Template()]
    public function deleteAction(Request $request)
    {
        if ($request->isMethod('POST')) {
            $feeGlobalNameId = $request->get("fee_global_name_id");

            $em = $this->getDoctrine()->getManager();
            $feeGlobalName = $em->getRepository(\CoreBundle\Entity\FeeGlobalName::class)->find($feeGlobalNameId);
            if (!$feeGlobalName) {
                throw $this->createNotFoundException('No Fee Global Name found for id ' . $feeGlobalNameId);
            }

            $em->remove($feeGlobalName);
            $em->flush();
        }

        return $this->redirect($this->generateUrl('admin_fee_global_name'));
    }

    protected function createFeeGlobalNameForm(FeeGlobalName $feeGlobalName)
    {
        $builder = $this->createFormBuilder($feeGlobalName);
        $form = $builder
            ->add('name', TextType::class, array('label' => 'Fee Global Name'))
            ->add('feeCategory', EntityType::class, array(
                'label'              => 'Category',
                'translation_domain' => 'forms',
                "class" => \CoreBundle\Entity\FeeCategory::class,
                'choice_label'       => 'name',
                'multiple'           => false,
                'expanded'           => false,
                'required'           => true,
            ))
            ->add('type', ChoiceType::class, array(
                'label' => 'Type',
                'choices' => FeeGlobalName::getConstantsStartWith('TYPE'),
                'choice_label' => function ($value) {
                    return Stringy::create($value)->humanize();
                }
            ))
            ->getForm();
        return $form;
    }

}
