<?php
/**
 * Created by PhpStorm.
 * User: hans
 * Date: 2018/3/16
 * Time: 20:24
 */

namespace AdminBundle\Controller\Fee;


use AdminBundle\Controller\BaseController;
use AdminBundle\Response\FailedMsgResponse;
use AdminBundle\Response\SuccessMsgResponse;
use CoreBundle\Entity\Currency;
use CoreBundle\Entity\FeeGlobalName;
use CoreBundle\Entity\FeeItem;
use CoreBundle\Entity\Module;
use CoreBundle\Entity\TenantFeeOption;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\QueryListParams;
use CoreBundle\Utils\Traits\DbTrait;
use CoreBundle\Utils\Util;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class EngineController extends BaseController
{
    use DbTrait;

    /**
     * DefaultController constructor.
     */
    public function __construct()
    {
        parent::__construct();

        $this->authPermission(Module::ID_FEE_SCHEDULE);
    }

    /**
     * @Route("/admin/fee-engine", name="admin_fee_engine_all")
     * @return \Symfony\Component\HttpFoundation\Response
     * @throws \LogicException
     */
    public function index()
    {
        $paginator = $this->get('knp_paginator');
        return $this->render('@Admin/Fee/Engine/index.html.twig', [
            'globals' => $this->em->getRepository(\CoreBundle\Entity\FeeGlobalName::class)
                ->createQueryBuilder('g')
                ->leftJoin('g.feeCategory', 'c')
                ->orderBy('c.name')
                ->addOrderBy('g.name')
                ->getQuery()->getResult(),
            'items' => $paginator->paginate([]),
        ]);
    }

    /**
     * @Route("/admin/fee-engine/list/{page}/{limit}")
     * @param Request $request
     * @param $page
     * @param $limit
     * @return \Symfony\Component\HttpFoundation\Response
     * @throws \LogicException
     */
    public function list(Request $request, $page = 1, $limit = 10)
    {
        $paginator = $this->get('knp_paginator');
        $query = $this->query($request);
        return $this->render('@Admin/Fee/Engine/list.html.twig', [
            'items' => $paginator->paginate($query, $page, $limit),
        ]);
    }

    public function query(Request $request)
    {
        $query = $this->em->getRepository(\CoreBundle\Entity\FeeItem::class)
            ->createQueryBuilder('fi');

        $params = new QueryListParams($query, $request, 'fi');
        $params->orderBy = [
            'fi.createdAt' => 'desc',
        ];
        return $this->queryListForPaginator($params);
    }

    protected function detailPageData () {
        $types = [];
        foreach (FeeItem::ENTITY_TYPES as $key => $name) {
            $class = FeeItem::ENTITY_MAP[$key] ?? null;
            $types[$key] = [
                'name' => $name,
                'items' => $class ? $this->em->getRepository($class)
                    ->createQueryBuilder('c')
                    ->select('c.id, c.name')
                    ->orderBy('c.name')
                    ->getQuery()
                    ->getArrayResult() : [],
            ];
        }
        $categories = $this->em->getRepository(\CoreBundle\Entity\FeeCategory::class)
            ->createQueryBuilder('c')
            ->select('c.id, c.name, c.tenantTypes')
            ->getQuery()
            ->getArrayResult();
        $categories = array_map(function ($category) {
            $types = Util::s2j($category['tenantTypes']);
            $category['tenantTypes'] = array_map(function ($type) {
                return FeeItem::TENANT_TYPE_MAP_REVERSE[$type] ?? $type;
            }, $types);
            return $category;
        }, $categories);
        return [
            'entity' => null,
            'tenantTypes' => $types,
            'categories' => $categories,
            'globals' => $this->em->getRepository(\CoreBundle\Entity\FeeGlobalName::class)
                ->createQueryBuilder('g')
                ->join('g.feeCategory', 'c')
                ->select('g.id, g.name, c.id category')
                ->getQuery()
                ->getArrayResult(),
            'ways' => FeeItem::getConstantsKeyedStartWith('WAY_'),
            'currencies' => Currency::getCodes(),
        ];
    }

    /**
     * @Route("/admin/fee-engine/new", methods={"GET"})
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function add()
    {
        return $this->render('@Admin/Fee/Engine/detail.html.twig', $this->detailPageData());
    }

    /**
     * @Route("/admin/fee-engine/{feeItem}/edit", methods={"GET"})
     * @param FeeItem $feeItem
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function edit(FeeItem $feeItem)
    {
        return $this->render('@Admin/Fee/Engine/detail.html.twig', array_merge($this->detailPageData(), [
            'entity' => $feeItem,
        ]));
    }

    /**
     * @Route("/admin/fee-engine/new", methods={"POST"})
     * @param Request $request
     * @return Response
     */
    public function save(Request $request)
    {
        $all = $request->request->all();
        if (empty($all['id'])) {
            $fi = new FeeItem();
        } else {
            $fi = $this->em->getRepository(\CoreBundle\Entity\FeeItem::class)->find($all['id']);
        }
        if (!$fi) {
            return new FailedMsgResponse();
        }

        if ($all['original_name'] === 'new' && empty($all['original_name_custom'])) {
            return new FailedMsgResponse('Please input custom fee original name!');
        }

        $category = $this->em->getRepository(\CoreBundle\Entity\FeeCategory::class)->find($all['fee_category']);
        $global = $all['fee_global_name'];
        if ($global === 'new') {
            if (empty($all['fee_global_name_custom'])) {
                return new FailedMsgResponse('Please input custom fee global name!');
            }
            $global = new FeeGlobalName();
            $global->setFeeCategory($category)
                ->setName($all['fee_global_name_custom'])
                ->persist();
        } else {
            $global = $this->em->getRepository(\CoreBundle\Entity\FeeGlobalName::class)->find($global);
        }

        $original = $all['original_name'] === 'new' ? $all['original_name_custom'] : $all['original_name'];
        $all['api'] = $all['api'] ?? '';
        $all['status'] = $all['status'] ?? '';
        $all['tran_code'] = $all['tran_code'] ?? '';
        $all['tran_type'] = $all['tran_type'] ?? '';

        $fi->setFeeEntityType($all['tenant_type'])
            ->setFunction($all['api'])
            ->setTransactionType($all['tran_type'])
            ->setTransactionStatus($all['status'])
            ->setFeeEntityId($all['tenant'])
            ->setFeeGlobalName($global)
            ->setFeeName($original)
            ->setTranCode($all['tran_code'])
            ->setAppliedBy($all['way'] ?? null)
            ->setCostTernFixedCurrency($all['currency'])
            ->setCostTernFixed(Money::normalizeAmount($all['fixed'], $all['currency']))
            ->setCostTernRatio($all['percent'])
            ->persist();

        TenantFeeOption::save(
            $this->em->getRepository(\CoreBundle\Entity\Tenant::class)->find($all['tenant']),
            Util::removeNA($original),
            Util::removeNA($all['tran_code']),
            Util::removeNA($all['api']),
            Util::removeNA($all['tran_type']),
            Util::removeNA($all['status'])
        );

        return new SuccessMsgResponse('/admin/fee-engine');
    }

    /**
     * @Route("/admin/fee-engine/{feeItem}/delete", methods={"POST"})
     * @param FeeItem $feeItem
     * @return \Symfony\Component\HttpFoundation\Response
     * @throws \Doctrine\ORM\ORMInvalidArgumentException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function remove(FeeItem $feeItem)
    {
        if (!$feeItem->getCardProgramFeeItem()->isEmpty()) {
            return new FailedResponse('This fee item is already connected in a card program. 
                Please remove from their first.');
        }

        $this->em->remove($feeItem);
        $this->em->flush();
        return new SuccessResponse();
    }
}