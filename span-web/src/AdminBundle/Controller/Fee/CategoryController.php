<?php
/**
 * User: Bob
 * Date: 2017/2/21
 * Time: 12:41
 * This is used to handle all the actions for Fee Category Management Menu
 */

namespace AdminBundle\Controller\Fee;

use AdminBundle\Controller\BaseController;
use AdminBundle\Response\FailedMsgResponse;
use CoreBundle\Entity\FeeCategory;
use CoreBundle\Entity\Module;
use CoreBundle\Entity\Tenant;
use CoreBundle\Utils\Util;
use Doctrine\Common\Collections\Collection;
use CoreBundle\Attribute\Template;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class CategoryController extends BaseController
{
    /**
     * DefaultController constructor.
     */
    public function __construct()
    {
        parent::__construct();

        $this->authPermission(Module::ID_FEE_CATEGORY);
    }

    /**
     * @Route("/admin/fee-category",name="admin_fee_category")
     */
    #[Template()]
    public function indexAction()
    {
        $feeCategories = $this->getDoctrine()
            ->getRepository(\CoreBundle\Entity\FeeCategory::class)
            ->findAll();
        return Array('feeCategories'=> $feeCategories,
            /* Comment on 2017-02-23 replace with jquery data table
             * 'search_info' => ''*/);
    }

    /**
     * Comment on 2017-02-23 replace with jquery data table
     * @Route("/admin/fee-category/search",name="admin_fee_category_search")
     */
    /*public function searchAction(Request $request)
    {
        $searchInfo = $request->get("search_info");
        $feeCategories = $this->getDoctrine()
            ->getRepository(\CoreBundle\Entity\FeeCategory::class)
            ->findLikeName($searchInfo);
        return $this->render('@Admin/FeeCategory/index.html.twig', array(
            'feeCategories'=> $feeCategories, 'search_info' => $searchInfo
        ));
    }*/

    /**
     * @Route("/admin/fee-category/new", name="admin_fee_category_new")
     */
    #[Template()]
    public function newAction(Request $request)
    {
        $feeCategory = new FeeCategory();
        $form = $this->createFeeCategoryForm($feeCategory);

        $original = clone $request;
        if ($request->isMethod('POST')) {
            $form->handleRequest($request);

            if ($form->isValid()) {
                $feeCategory->setTenantTypes(Util::j2s($original->get('tenantTypes')));

                $em = $this->getDoctrine()->getManager();
                $em->persist($feeCategory);
                $em->flush();
                return $this->redirect($this->generateUrl('admin_fee_category'));
            }
        }
        return array(
            'form' => $form->createView(),
            'tenantTypes' => array_keys(Tenant::ClASS_TYPE_MAP),
        );
    }

    /**
     * @Route("/admin/fee-category/modify", name="admin_fee_category_modify")
     */
    #[Template()]
    public function modifyAction(Request $request)
    {
        $feeCategoryId = $request->get("fee_category_id");

        $em = $this->getDoctrine()->getManager();
        /** @var FeeCategory $feeCategory */
        $feeCategory = $em->getRepository(\CoreBundle\Entity\FeeCategory::class)->find($feeCategoryId);
        if (!$feeCategory) {
            throw $this->createNotFoundException('No Category found for id ' . $feeCategoryId);
        }
        $form = $this->createFeeCategoryForm($feeCategory);

        $original = clone $request;
        if ($request->isMethod('POST')) {
            $form->handleRequest($request);

            if ($form->isValid()) {
                $feeCategory->setTenantTypes(Util::j2s($original->get('tenantTypes')));
                $em->flush();

                return $this->redirect($this->generateUrl('admin_fee_category'));
            }
        }

        return array(
            'form' => $form->createView(),
            'fee_category_id' => $feeCategoryId,
            'entity' => $feeCategory,
            'tenantTypes' => array_keys(Tenant::ClASS_TYPE_MAP),
        );
    }

    /**
     * @Route("/admin/fee-category/delete", name="admin_fee_category_delete")
     */
    #[Template()]
    public function deleteAction(Request $request)
    {
        if ($request->isMethod('POST')) {
            $feeCategoryId = $request->get("fee_category_id");

            $em = $this->getDoctrine()->getManager();
            $feeCategory = $em->getRepository(\CoreBundle\Entity\FeeCategory::class)->find($feeCategoryId);
            if (!$feeCategory) {
                throw $this->createNotFoundException('No Category found for id ' . $feeCategoryId);
            }

            /** @var Collection $globals */
            $globals = $feeCategory->getGlobals();
            if ($globals && !$globals->isEmpty()) {
                return new FailedMsgResponse('Category cannot be deleted when it\'s being used.');
            }

            $em->remove($feeCategory);
            $em->flush();
        }

        return $this->redirect($this->generateUrl('admin_fee_category'));
    }

    protected function createFeeCategoryForm(FeeCategory $feeCategory)
    {
        $builder = $this->createFormBuilder($feeCategory);
        $form = $builder
            ->add('name', TextType::class, array('label' => 'Category Name'))
            ->getForm();
        return $form;
    }

}
