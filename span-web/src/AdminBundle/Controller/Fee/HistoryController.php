<?php


namespace AdminBundle\Controller\Fee;


use AdminBundle\Controller\BaseController;
use AdminBundle\Controller\Traits\FeeControllerTrait;
use CoreBundle\Entity\Module;
use CoreBundle\Entity\UserFeeHistory;
use CoreBundle\Entity\UserCardDecline;
use CoreBundle\Entity\FeeGlobalName;
use CoreBundle\Entity\UserCardTransaction;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\QueryListParams;
use CoreBundle\Utils\Traits\DbTrait;
use CoreBundle\Utils\Traits\ExcelTrait;
use CoreBundle\Utils\Util;
use Doctrine\ORM\QueryBuilder;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class HistoryController extends BaseController
{
    use ExcelTrait;
    use DbTrait;
    use FeeControllerTrait;

    /**
     * DefaultController constructor.
     */
    public function __construct()
    {
        parent::__construct();

        $this->authPermission(Module::ID_FEE_HISTORY);
    }

    /**
     * @Route("/admin/fee/history/filters")
     * @return SuccessResponse
     */
    public function searchFiltersAction() {
        return new SuccessResponse($this->getSearchFilters());
    }

    /**
     * @Route("/admin/fee/history/search/{page}/{limit}", defaults={"page" = 1,"limit" = 10})
     * @param Request $request
     * @param int     $page
     * @param int     $limit
     *
     * @return SuccessResponse
     */
    public function search(Request $request, $page = 1, $limit = 10)
    {
        $query = $this->query($request);
        $params = $this->queryListParams($query, $request);
        $params->pagination = [
            'page' => $page,
            'size' => $limit,
        ];
        $result = $this->queryListForAPI($params, function ($entity) {
          return $this->getRowData($entity);
        });

        $other = $this->getSummaryAndChartData($query, $request);
        $result = array_merge($result, $other);
        return new SuccessResponse($result);
    }

    protected function getRowData (UserFeeHistory $entity)
    {
        $user = $entity->getUser();
        $all = [
            'ID' => $entity->getId(),
            'Time' => $entity->getTime()->format('c'),
            'User ID' => $user->getId(),
            'User Name' => $user->getName(),
            'Email' => $user->getEmail(),
            'Fee Name' => $entity->getFeeName(),
            'Amount' => $entity->getAmount(),
            'Comment' => $entity->getComment(),
            'Card Type' => $entity->getCardType()
        ];

        if ($entity->getFeeName() === FeeGlobalName::DECLINED_TRANSACTION_FEE) {
            $ds = Util::em()->getRepository(UserCardDecline::class)->findBy([
                'txnId' => $entity->getEntityId(),
            ], null, 1);
            if ($ds) {
                /** @var UserCardDecline $ucd */
                $ucd = $ds[0];
                $all['Merchant'] = $ucd->getMerchantName();
                $all['Decline Reason'] = $ucd->getDeclineReason();
            }
        }
        if ($entity->getFeeName() === FeeGlobalName::APPROVED_TRANSACTION_FEE) {
            $ds = Util::em()->getRepository(UserCardTransaction::class)->findBy([
                'tranId' => $entity->getEntityId(),
            ], null, 1);
            if ($ds) {
                /** @var UserCardTransaction $uct */
                $uct = $ds[0];
                $all['Merchant'] = $uct->getMerchantName();
            }
        }
        return $all;
    }

    /**
     * @Route("/admin/fee/history/export", methods={"POST"})
     * @param Request $request
     *
     * @return SuccessResponse
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     */
    public function export(Request $request)
    {
        Util::longRequest();

        $query = $this->query($request);
        $params = $this->queryListParams($query, $request);
        if ($request->get('query_count')) {
            $count = $this->queryCountForExport($params);
            return new SuccessResponse($count);
        }

        $from  = (int)$request->get('query_from', 0);
        $limit = (int)$request->get('query_limit', 5000);

        $data = $this->queryListForExport($params, $from, $limit, function ($entity) {
          return $this->getRowData($entity);
        });

        $headers = [];
        $headers = array_merge($headers, [
            'ID'        => 10,
            'Time'      => 18,
            'User ID'   => 14,
            'User Name' => 22,
            'Email'     => 32,
            'Fee Name'  => 24,
            'Amount'    => 14,
            'Comment'   => 60,
            'Merchant'  => 32,
            'Decline Reason' => 45,
        ]);
        $textColumns = [];
        $filename = 'Fee History List_' . $request->get('query_timestamp', time());
        $destination = Util::uploadDir('report') . $filename . '.xlsx';
        $excel = $this->loadOrCreateExcel($destination, $filename);

        $excel = $this->generateSheetAppendToExcel($excel, $from . ' ~ ' . ($from + $limit),
            $headers, $data, static function ($col, $row, $excel, $title) {
                $value = $row[$title] ?? '';
                if ($title === 'Time') {
                    return Util::formatDateTime($value);
                }
                if ($title === 'Amount') {
                    return Money::formatAmountToNumber($value);
                }
                return $value;
            }, $textColumns);
        $moneyIndex = ['G'];
        $this->setColumnsNumberMoney($moneyIndex, count($data));
        $excel->setActiveSheetIndex(0);
        $this->saveExcelTo($excel, $destination);

        return new SuccessResponse('report/' . $filename . '.xlsx');
    }
}
