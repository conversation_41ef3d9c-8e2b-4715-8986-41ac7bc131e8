<?php
/**
 * User: Bob
 * Date: 2017/2/24
 * Time: 14:37
 */

namespace AdminBundle\Controller\Fee;


use AdminBundle\Controller\BaseController;
use CoreBundle\Constant\FrequencyUnit;
use CoreBundle\Entity\FeeItem;
use CoreBundle\Entity\Module;
use CoreBundle\Type\Measure;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Util;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use CoreBundle\Attribute\Template;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class ItemController extends BaseController
{
    /**
     * DefaultController constructor.
     */
    public function __construct()
    {
        parent::__construct();

        $this->authPermission(Module::ID_FEE_SCHEDULE);
    }

    /**
     * @Route("/admin/fee-item",name="admin_fee_item")
     */
    #[Template()]
    public function indexAction(Request $request)
    {

        $target = $request->get('target');
        if($target == "all"){
            return $this->redirect($this->generateUrl("admin_fee_item_all"));
        }

        $entityId = $request->get("entity_id");
        $entityType = $request->get("entity_type");

        $selectedFeeItems = $this->getDoctrine()
            ->getRepository(\CoreBundle\Entity\FeeItem::class)
            ->findBy(array('feeEntityId'=>$entityId,'feeEntityType'=>$entityType));
        /* Comment on 2017-03-24 remove add&remove functions from fee item
         * $unSelectedFeeItems = $this->getDoctrine()
            ->getRepository(\CoreBundle\Entity\FeeItem::class)
            ->findBy(array('feeEntityId'=>null,'feeEntityType'=>null));*/
        return array('selectedFeeItems'=> $selectedFeeItems,/*'unSelectedFeeItems'=>$unSelectedFeeItems,*/'entity_id'=>$entityId,'entity_type'=>$entityType);
    }

    /**
     * @Route("/admin/fee-item/all/{type}/{page}/{limit}",defaults={"type" = "valid", "page" = 1,"limit" = 10},name="admin_fee_item_all")
     */
    #[Template()]
    public function allAction(Request $request,$type,$page,$limit)
    {
        $feeItems = $this->getDoctrine()
            ->getRepository(\CoreBundle\Entity\FeeItem::class)
            ->findRelatedFeeItems($type);

        $paginator = $this->get('knp_paginator');
        $pagination= $paginator->paginate($feeItems, $page,$limit);
        return array('feeItems'=> $pagination, 'type' => $type);
    }

    /**
     * @Route("/admin/fee-item/new",name="admin_fee_item_new")
     */
    #[Template()]
    public function newAction(Request $request)
    {
        $feeItem = new FeeItem();
        $em = $this->getDoctrine()->getManager();
        $entityId = $request->get("entity_id");
        $entityType = $request->get("entity_type");
        $feeGlobalNames = $em->getRepository(\CoreBundle\Entity\FeeGlobalName::class)->findAll();
        $frequencyUnit = array_flip(FrequencyUnit::getConstants());
        $currencies = $this->getDoctrine()->getRepository(\CoreBundle\Entity\Currency::class)->findAll();
        $currencyChoices = array();
        foreach ($currencies as $currency)
        {
            $currencyChoices[$currency->getCurCode()] = $currency->getCurCode();
        }
        $measureChoices = array_merge(array('Times'=>'Times'), $currencyChoices);
        //$form = $this->createForm(FeeItemType::class, $feeItem);
        $form = $this->buildFeeItemForm($feeItem);
        if ($request->isMethod('POST')) {
//            $form->handleRequest($request);
            $Fixed=$_POST['CostTernFixed'];
            $Currency=$_POST['Costternfixedcurrency'];
            $FeeGlobalName=$_POST['FeeGlobalName'];
            $FeeName=$_POST['FeeName'];
            $TransactionCode=$_POST['TransactionCode'];
            $AppliedBy='Admin';
            $Measure=$_POST['Measure'];
//            $Measuremeasurestr=$_POST['Measuremeasurestr'];
            $Measureunit=$_POST['Measureunit'];
            $CostToTernRatio=$_POST['CostToTernRatio'];
            $Entityid=$_POST['entityid'];
            $Entitytype=$_POST['entitytype'];
//            if ($form->isValid()) {
            $feeGlobalName = $em->getRepository(\CoreBundle\Entity\FeeGlobalName::class)->findOneBy(array('id'=>$FeeGlobalName));
            $feeItem->setFeeEntityId($Entityid)->setFeeEntityType($Entitytype);
            $feeItem->setCostTernFixed(Money::normalizeAmount($Fixed,$Currency));
            $feeItem->setFeeName($FeeName)->setTranCode($TransactionCode)->setAppliedBy($AppliedBy)->setFeeGlobalName($feeGlobalName);
            $feeItem->setMeasureNumber($Measure)->setCostTernRatio((double)$CostToTernRatio)->setCostTernFixedCurrency($Currency);
            $feeItem->setMeasureUnit($Measureunit);
            $em = Util::em();
            $em->persist($feeItem);
            $em->flush();
            return $this->redirect($this->generateUrl('admin_fee_item',array('entity_id'=>$Entityid,'entity_type'=>$Entitytype)),301);
//            }
//            }
        }
        return array('form' => $form->createView(),'entity_id'=>$entityId,'entity_type'=>$entityType,'feeGlobalNames'=>$feeGlobalNames,'measureChoices'=>$measureChoices,'frequencyUnit'=>$frequencyUnit,'currencyChoices'=>$currencyChoices);
    }

    /**
     * @Route("/admin/fee-item/modify",name="admin_fee_item_modify")
     */
    #[Template()]
    public function modifyAction(Request $request)
    {
        $target = $request->get('target');
        $feeItemId = $request->get("item_id");
        $entityId = $request->get("entity_id");
        $entityType = $request->get("entity_type");
        $em = $this->getDoctrine()->getManager();
        $feeGlobalNames = $em->getRepository(\CoreBundle\Entity\FeeGlobalName::class)->findAll();
        $frequencyUnit = array_flip(FrequencyUnit::getConstants());
        $currencies = $this->getDoctrine()->getRepository(\CoreBundle\Entity\Currency::class)->findAll();
        $currencyChoices = array();
        foreach ($currencies as $currency)
        {
            $currencyChoices[$currency->getCurCode()] = $currency->getCurCode();
        }
        $measureChoices = array_merge(array('Times'=>'Times'), $currencyChoices);
        $feeItem = $em->getRepository(\CoreBundle\Entity\FeeItem::class)->findOneBy(array('id'=>$feeItemId,'feeEntityId'=>$entityId,'feeEntityType'=>$entityType));
        if (!$feeItem) {
            throw $this->createNotFoundException('No Fee Item found for id ' . $feeItemId);
        }

        /** @var Measure $measure */
        $measure = $feeItem->getMeasure();
        if ($measure) {
            $measure->setNumber($measure->getNumber() ?: 0);
        }

//        if(!$feeItem->getMeasure())
//        {
//            $feeItem->setMeasure(new Measure());
//        }
        //$form = $this->createForm(FeeItemType::class, $feeItem);
        $form = $this->buildFeeItemForm($feeItem);

        if ($request->isMethod('POST')) {
//            $form->handleRequest($request);;
         if($feeItem->getMeasure())
        {
            $feeItem->setMeasure(new Measure());
        }
            $Fixed=$_POST['CostTernFixed'];
            $Currency=$_POST['Costternfixedcurrency'];
            $FeeGlobalName=$_POST['FeeGlobalName'];
            $FeeName=$_POST['FeeName'];
            $TransactionCode=$_POST['TransactionCode'];
            $AppliedBy='Admin';
            $Measure=$_POST['Measure'];
//            $Measuremeasurestr=$_POST['Measuremeasurestr'];
            $Measureunit=$_POST['Measureunit'];
            $CostToTernRatio=$_POST['CostToTernRatio'];
            $Entityid=$_POST['entityid'];
            $Entitytype=$_POST['entitytype'];
//            if ($form->isValid()) {
            $feeGlobalName = $em->getRepository(\CoreBundle\Entity\FeeGlobalName::class)->findOneBy(array('id'=>$FeeGlobalName));
            $feeItem->setFeeEntityId($Entityid)->setFeeEntityType($Entitytype);
            $feeItem->setCostTernFixed(Money::normalizeAmount($Fixed,$Currency));
            $feeItem->setFeeName($FeeName)->setTranCode($TransactionCode)->setAppliedBy($AppliedBy)->setFeeGlobalName($feeGlobalName);
            $feeItem->setMeasureNumber($Measure)->setCostTernRatio((double)$CostToTernRatio)->setCostTernFixedCurrency($Currency);
            $feeItem->setMeasureUnit($Measureunit);
            $em = Util::em();
            $em->persist($feeItem);
            $em->flush();
            if('all' == $target)
            {
                return $this->redirect($this->generateUrl('admin_fee_item_all'));
            }
            return $this->redirect($this->generateUrl('admin_fee_item',array('entity_id'=>$Entityid,'entity_type'=>$Entitytype)),301);
//            }
        }

        return array('form' => $form->createView(),
            'item_id'=>$feeItemId,
            'entity_id'=>$entityId,
            'entity_type'=>$entityType,
            'target'=>$target,
            'Fixed'=> $feeItem->getCostTernFixed(),
            'Cou'=>$feeItem->getCostTernFixedCurrency(),
            'feeGlobalNames'=>$feeGlobalNames,
            'measureChoices'=>$measureChoices,
            'frequencyUnit'=>$frequencyUnit,
            'currencyChoices'=>$currencyChoices,
            'feeGlobalName'=>$feeItem->getFeeGlobalName()->getId(),
            'feeName'=>$feeItem->getFeeName(),
            'transactionCode'=>$feeItem->getTranCode(),
            'measure'=>$feeItem->getMeasureNumber(),
            'measureunit'=>$feeItem->getMeasureUnit(),
            'currency'=>$feeItem->getCostTernFixedCurrency(),
            'costToTernRatio'=>$feeItem->getCostTernRatio(),
            );
    }

    /**
     * @Route("/admin/tenantType.json", name="admin_tenant_type_fee")
     */
    public function tenantWithType(Request $request)
    {
        $data = [];
        $tenantType = $request->get("tenantType");
        if($tenantType == "Processor"){
            $data = $this->getDoctrine()->getRepository(\CoreBundle\Entity\Processor::class)->findAll();
        }
        elseif ($tenantType == "Program"){
            $data = $this->getDoctrine()->getRepository(\CoreBundle\Entity\ProgramManager::class)->findAll();
        }
        elseif ($tenantType == "LoadPartner"){
            $data = $this->getDoctrine()->getRepository(\CoreBundle\Entity\LoadPartner::class)->findAll();
        }
        elseif ($tenantType == "KycProvider"){
            $data = $this->getDoctrine()->getRepository(\CoreBundle\Entity\KycProvider::class)->findAll();
        }
        elseif ($tenantType == "IssuingBank"){
            $data = $this->getDoctrine()->getRepository(\CoreBundle\Entity\IssuingBank::class)->findAll();
        }
        elseif ($tenantType == "Service"){
            $data = $this->getDoctrine()->getRepository(\CoreBundle\Entity\ServiceManager::class)->findAll();
        }
        elseif ($tenantType == "Platform"){
            $data = $this->getDoctrine()->getRepository(\CoreBundle\Entity\Platform::class)->findAll();
        }
        $arr = array();
        foreach ($data as $tenant)
        {
            $arr[$tenant->getId()] = $tenant->getName();
        }
        return new Response(json_encode($arr));
    }

    /**
     * @Route("/admin/fee-item/delete",name="admin_fee_item_delete")
     */
    #[Template()]
    public function deleteAction(Request $request)
    {
        $entityId = null;
        $entityType = null;
        $target = $request->get('target');
        if ($request->isMethod('POST')) {
            $feeItemId = $request->get("item_id");
            $entityId = $request->get("entity_id");
            $entityType = $request->get("entity_type");

            $em = $this->getDoctrine()->getManager();
            $feeItem = $em->getRepository(\CoreBundle\Entity\FeeItem::class)->findOneBy(array('id'=>$feeItemId,'feeEntityId'=>$entityId,'feeEntityType'=>$entityType));
            if (!$feeItem) {
                throw $this->createNotFoundException('No Fee Item found for id ' . $feeItemId);
            }

            $em->remove($feeItem);
            $em->flush();
        }

        if('all' == $target)
        {
            return $this->redirect($this->generateUrl('admin_fee_item_all'));
        }

        return $this->redirect($this->generateUrl('admin_fee_item',array('entity_id'=>$entityId,'entity_type'=>$entityType)),301);

    }

    //Begin Add by Bob Wen Bao on 2017-03-30 to build the new fee item form
    //Moved from form type to controller because need generate the array for choice type with entity manager
    //May need enhancement later
    private function buildFeeItemForm(FeeItem $feeItem)
    {
        $frequencyUnit = array_flip(FrequencyUnit::getConstants());
        $currencies = $this->getDoctrine()->getRepository(\CoreBundle\Entity\Currency::class)->findAll();
        $currencyChoices = array();
        foreach ($currencies as $currency)
        {
            $currencyChoices[$currency->getCurCode()] = $currency->getCurCode();
        }
        $measureChoices = array_merge(array('Times'=>'Times'), $currencyChoices);
        $builder = $this->createFormBuilder($feeItem);
        $form = $builder
            ->add('feeGlobalName', EntityType::class, array(
                'label'              => ' Fee Global Name',
                'translation_domain' => 'forms',
                "class" => \CoreBundle\Entity\FeeGlobalName::class,
                'choice_label'       => 'name',
                'multiple'           => false,
                'expanded'           => false,
                'required'           => true,
            ))
            ->add('feeEntityId', HiddenType::class)
            ->add('feeEntityType', HiddenType::class)
            ->add('feeName', TextType::class, array('label' => 'Fee Name'))
            ->add('tranCode', TextType::class, array('label' => 'Transaction Code'))
            ->add('appliedBy', TextType::class, array('label' => 'Applied By'))
            ->add('measureNumber', IntegerType::class, array('label'=>'Measure'))
//            ->add('measureMeasureStr', ChoiceType::class, array(
//                'choices'            => $measureChoices,
//                'multiple'           => false,
//                'expanded'           => false,
//                'required'           => false
//            ))
            ->add('measureUnit', ChoiceType::class,array(
                'choices' => $frequencyUnit,
                'expanded' => false,
                'multiple' => false
            ))
//            ->add('costTernFixed', IntegerType::class, array('label' => 'Cost To Tern Fixed', 'required' => false))
            ->add('costTernFixedCurrency', ChoiceType::class, array(
                'choices'            => $currencyChoices,
                'multiple'           => false,
                'expanded'           => false,
                'required'           => false,
                'placeholder'        => ' '
            ))
//            ->add('costTernRatio', IntegerType::class, array('label' => 'Cost To Tern Ratio', 'required' => false))
            ->getForm();
        return $form;
    }
    //End

}
