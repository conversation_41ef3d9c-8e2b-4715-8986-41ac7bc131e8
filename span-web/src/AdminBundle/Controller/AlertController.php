<?php


namespace AdminBundle\Controller;


use AdminBundle\Controller\User\UserSearchController;
use AdminBundle\Form\Type\AlertType;
use Carbon\Carbon;
use CoreBundle\Entity\Alert;
use CoreBundle\Entity\Module;
use CoreBundle\Response\ErrorResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\QueryListParams;
use CoreBundle\Utils\Traits\DbTrait;
use CoreBundle\Utils\Traits\ExcelTrait;
use CoreBundle\Utils\Util;
use DateTime;
use Doctrine\ORM\AbstractQuery;
use Doctrine\ORM\QueryBuilder;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;


class AlertController extends UserSearchController
{
    use ExcelTrait;
    use DbTrait;

    private $timezone;


    /**
     * DefaultController constructor.
     */
    public function __construct()
    {
        parent::__construct();

        $this->authPermission(Module::ID_ALERT_LIST);
    }

    /**
     * @Route("/admin/alert/filters")
     * @return SuccessResponse
     */
    public function searchFiltersAction() {
        return new SuccessResponse([]);
    }

    /**
     * @Route("/admin/alert/search/{page}/{limit}", defaults={"page" = 1,"limit" = 10}, name="alert")
     * @param Request $request
     * @param int     $page
     * @param int     $limit
     *
     * @return SuccessResponse
     */
    public function search(Request $request, $page = 1, $limit = 10)
    {
        $query = $this->query($request);
        $params = $this->queryListParams($query, $request);
        $result = $this->queryListForAPI($params, function ($entity) {
            return $this->getRowData($entity);
        });
        $totalQuery = $query->getQuery()
        ->getArrayResult();
        $result['data'] = $this->parseQueryResult($result['data'], $page, $limit);
        $result['quick'] = [
            'totalSize' => count($totalQuery)
        ];
        return new SuccessResponse($result);
    }

    public function query(Request $request)
    {
        $query = Util::em()->getRepository(Alert::class)
            ->createQueryBuilder('alert');
        return $query;
    }

    protected function parseQueryResult($data, $page, $limit)
    {
        $new = [];
        foreach ($data as $item) {
            $time = $item['creationDate'];
            if (!isset($new[$time])) {
                $new[] = [
                    'Alert ID' => $item['id'],
                    'Name' => $item['name'],
                    'Description' => $item['description'],
                    'Creation Date' => $time,
                    'Type' => $item['type'],
                    'Message' => $item['message'],
                    'Author' => $item['authorId'],
                    'Unable to Close' =>  $item['isSystem'],
                    'Status' => $item['archived'] ? 'Close' : 'Active'
                ];
            }
        }
        krsort($new);
        $new = array_values($new);
        return $this->sliceQueryArrayBy($new, $page, $limit);
    }

    protected function queryListParams(QueryBuilder $query, Request $request)
    {
        // cutoff to be 11:59:59 each day Eastern Time (NY)
        $range = $request->get('range');
        $user = $this->getUser();
        $this->timezone = (new \Carbon\Carbon)->getTimeZone();
        if (!empty($range['alert.creationDate']['start'])) {
            $start = Util::timeUTC($range['alert.creationDate']['start']);
            $start = Carbon::create($start->year, $start->month, $start->day, 0, 0, 0, $this->timezone);
            $range['alert.creationDate']['start'] = $start->format('c');
        }
        if (!empty($range['alert.creationDate']['end'])) {
            $end = Util::timeUTC($range['alert.creationDate']['end']);
            $end = Carbon::create($end->year, $end->month, $end->day, 0, 0, 0, $this->timezone);
            $range['alert.creationDate']['end'] = $end->format('c');
        }
        $request->request->set('range', $range);

        // Create the QueryListParams
        $params = new QueryListParams($query, $request, 'alert', 'count(distinct date(alert.creationDate))');
        $params->orderBy = [
            'alert.creationDate' => 'asc',
        ];
        $params->pagination = false;
        $params->hydrationMode = AbstractQuery::HYDRATE_ARRAY;
        $params->dataSelect = 'alert.id, alert.description, alert.creationDate, alert.name, alert.type, alert.authorId, alert.message, alert.isSystem, alert.archived';
        return $params;
    }

    protected function getRowData($row)
    {
        /** @var DateTime $time */
        $time = $row['creationDate'];
        $user = $this->getUser();
        $this->timezone = (new \Carbon\Carbon)->getTimeZone();
        $row['creationDate'] = $time->setTimezone($this->timezone)
            ->format(Util::DATE_FORMAT);
        return $row;
    }

    /**
     * @Route("admin/alert/new",name="admin_alert_new")
     * @param Request $request
     * @return ErrorResponse|array|RedirectResponse|Response
     */
    public function newAction(Request $request) {
        $this->authPermission(Module::ID_USER_ADD_USER); //TODO: Update this with new module.

        // Create new Alert and form to receive alert info.
        $alert = new Alert();
        $form = $this->createForm(AlertType::class, $alert, array(
            'action' => $this->generateUrl('admin_alert_new'),
            'method' => 'POST',
        ));

        // If submitting POST response, create the new Alert.
        $form->handleRequest($request);
        if ($request->isMethod('POST') && $form->getData()->getName()) {
            $em = $this->getDoctrine()->getManager();
            $repo = $em->getRepository(\CoreBundle\Entity\Alert::class);
            $other = $repo->findOneBy(['name' => $form->getData()->getName()]);
            if ($other) {
                return new ErrorResponse('This name has been used before. Please try again with a unique name.');
            }

            // Get user & time info for author field.
            $user = $this->getUser();
            $userId = $user->getId();
            $timeStamp = new DateTime();
//            $time = $timeStamp->format('n/d/Y g');

            // Set AuthorId and Creation Date for Alert.
            $alert->setAuthorId($userId);
            $alert->setCreationDate($timeStamp);
            // Persist new alert to database.
            $em->persist($alert);
            $em->flush();
            return new SuccessResponse();
            // return $this->redirectToRoute('a/alert/search');
        }

        return $this->render('@Admin/Alerts/new_alert.html.twig', [
            'alerts'     => $alert,
            'form'      => $form->createView(),
            'errorFlag' => FALSE,
        ]);
    }

    /**
     * @Route("admin/alert/archive/{alert}", name="admin_alert_archive")
     * @param Request $request
     * @param Alert $alert
     *
     * @return RedirectResponse
     */
    public function archive(Request $request, Alert $alert): ?RedirectResponse
    {
        $alert->setArchived(true)
            ->persist();

        return $this->redirectToRoute('alert');
    }
}
