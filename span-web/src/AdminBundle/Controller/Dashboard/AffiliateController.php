<?php
/**
 * Created by PhpStorm.
 * User: hans
 * Date: 02/03/2018
 * Time: 14:22
 */

namespace AdminBundle\Controller\Dashboard;

use AdminBundle\Response\FailedMsgResponse;
use Carbon\Carbon;
use CoreBundle\Entity\Affiliate;
use Core<PERSON>undle\Entity\AffiliateApply;
use CoreBundle\Entity\AffiliateMaterial;
use CoreBundle\Entity\AffiliatePayment;
use CoreBundle\Entity\Config;
use CoreBundle\Entity\LoadPartner;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\QueryListParams;
use CoreBundle\Utils\Traits\DbTrait;
use CoreBundle\Utils\Util;
use PortalBundle\Exception\PortalException;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class AffiliateController extends BaseController
{
    use DbTrait;

    /**
     * AffiliateController constructor.
     * @throws \PortalBundle\Exception\PortalException
     */
    public function __construct()
    {
        parent::__construct();
        $user = $this->getUser();
        if (!$user->isAffiliate()) {
            throw new PortalException('Permission denied!');
        }
    }

    /**
     * @Route("/admin/dashboard/affiliate")
     * @param Request $request
     */
    public function index(Request $request)
    {
        $user = $this->getUser();
        /** @var Affiliate $affiliate */
        $affiliate = $user->getManagingAffiliate();

        $paymentMethod = null;
        if ($affiliate) {
            $paymentMethod['type'] = $affiliate->getPayoutMethod();
            $paymentMethod['detail'] = $affiliate->getPayoutCustom();
        }

        return $this->render('@Admin/Dashboard/Affiliate/index.html.twig', [
            'apply' => AffiliateApply::createFromUser($user),
            'editable' => true,
            'unpaid' => $affiliate ? $affiliate->getUnpaidAmountUSD() : 0,
            'domains' => $affiliate ? $affiliate->getDomains() : [],
            'countries' => $this->em->getRepository(\CoreBundle\Entity\Country::class)->countries(),
            'payments' => $this->get('knp_paginator')->paginate([], 1, 8),
            'paymentMethod' => $paymentMethod,
            'materials' => $affiliate ? $affiliate->getAccessibleMaterials() : [],
            'languages' => Config::array(Config::CONFIG_LANGUAGES),
        ]);
    }

    public function queryPayments(Request $request)
    {
        $user = $this->getUser();
        $affiliate = $user->getManagingAffiliate();
        $query = $this->em->getRepository(\CoreBundle\Entity\AffiliatePayment::class)
            ->createQueryBuilder('p')
            ->join('p.affiliate', 'a')
            ->where('p.affiliate = :affiliate')
            ->setParameter('affiliate', $affiliate->getId());

        $params = new QueryListParams($query, $request, 'p, a');
        $params->orderBy = [
            'p.requestedAt' => 'desc',
        ];
        return $this->queryListForPaginator($params);
    }

    /**
     * @Route("/admin/dashboard/affiliate/payments/{page}/{limit}")
     * @param Request $request
     * @param int $page
     * @param int $limit
     * @return \Symfony\Component\HttpFoundation\Response
     * @throws \LogicException
     */
    public function payments(Request $request, $page = 1, $limit = 8)
    {
        $paginator  = $this->get('knp_paginator');
        $query = $this->queryPayments($request);
        return $this->render('@Admin/Dashboard/Affiliate/partials/paymentList.html.twig', [
            'payments' => $paginator->paginate($query, $page, $limit),
        ]);
    }

    /**
     * @Route("/admin/dashboard/affiliate/save-contact", methods={"POST"})
     * @param Request $request
     * @return SuccessResponse
     */
    public function saveContactInfo(Request $request)
    {
        $user = $this->getUser();
        AffiliateApply::updateUser($user, $request->request->all());
        Util::persist($user);

        return new SuccessResponse();
    }

    /**
     * @Route("/admin/dashboard/affiliate/save-preference", methods={"POST"})
     * @param Request $request
     * @return FailedResponse|SuccessResponse
     */
    public function savePreference(Request $request)
    {
        $user = $this->getUser();
        $affiliate = $user->getManagingAffiliate();
        if (!$affiliate) {
            return new FailedResponse('Failed to find connected affiliate!');
        }
        $affiliate->setLanguage($request->get('language', 'en'))
            ->persist();

        return new SuccessResponse();
    }

    /**
     * @Route("/admin/dashboard/affiliate/request-payment", methods={"POST"})
     * @return FailedResponse|SuccessResponse
     */
    public function requestPayment()
    {
        $user = $this->getUser();
        $affiliate = $user->getManagingAffiliate();
        if (!$affiliate) {
            return new FailedResponse('Failed to find connected affiliate!');
        }
        $payment = new AffiliatePayment();
        $payment->setAffiliate($affiliate)
            ->setRequestedBy($user)
            ->setRequestedAt(new Carbon())
            ->setPrepaid($affiliate->getPrepaid())
            ->setEarned($affiliate->getUnpaidAmountUSD());
        Util::persist($payment);

        $affiliate->setPrepaid(0)
            ->persist();

        $expr = Util::expr();

        $rs = Util::em()->getRepository(\CoreBundle\Entity\UserCardLoad::class)
            ->createQueryBuilder('ucl')
            ->join('ucl.userCard', 'uc')
            ->join('uc.user', 'u')
            ->where('u.affiliate = :affiliate')
            ->andWhere('ucl.affiliatePayment is null')
            ->andWhere('ucl.partner <> :partner')
            ->andWhere($expr->orX(
                $expr->gt('ucl.membershipCommissionUSD', ':min'),
                $expr->gt('ucl.loadCommissionUSD', ':min')
            ))
            ->andWhere($expr->in('ucl.loadStatus', ':loadStatus'))
            ->setParameter('affiliate', $affiliate->getId())
            ->setParameter('partner', LoadPartner::system())
            ->setParameter('min', 0)
            ->setParameter('loadStatus', UserCardLoad::RECEIVED_STATUS_ARRAY)
            ->select('ucl.id')
            ->getQuery()
            ->getArrayResult();
        $ids = Util::flattenArray($rs, 'id');

        Util::em()->createQueryBuilder()
            ->update('CoreBundle:UserCardLoad', 'ucl')
            ->set('ucl.affiliatePayment', $payment->getId())
            ->where($expr->in('ucl.id', $ids))
            ->getQuery()
            ->execute();

        return new SuccessResponse();
    }

    /**
     * @Route("/admin/dashboard/affiliate/save-payment-method", methods={"POST"})
     * @param Request $request
     * @return SuccessResponse
     */
    public function savePaymentMethod(Request $request)
    {
        $user = $this->getUser();
        /** @var Affiliate $affiliate */
        $affiliate = $user->getManagingAffiliate();

        $type = $request->get('type');
        $detail = $request->get('detail');
        if ($type && $detail) {
            $affiliate->setPayoutMethod($request->get('type'));
            if ($type == Affiliate::PAYOUT_METHOD_CUSTOM) {
                $affiliate->setPayoutCustom($detail);
            }
            if ($type == Affiliate::PAYOUT_METHOD_CONTACT) {
                $userId = $detail;
                if ($userId) {
                    $user = $this->em->getRepository(\SalexUserBundle\Entity\User::class)->find($userId);
                    $affiliate->setPayoutContact($user);
                }
            }
        }
        Util::persist($affiliate);

        return new SuccessResponse();
    }

    /**
     * @Route("/admin/dashboard/affiliate/edit-aff-id", methods={"POST"})
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function editAffId(Request $request)
    {
        $user = $this->getUser();
        /** @var Affiliate $affiliate */
        $affiliate = $user->getManagingAffiliate();

        $affId = $request->get('aff_id');
        if (!$affId) {
            return new FailedResponse('Invalid affiliate ID!');
        }

        if (Affiliate::isAffIdUsed($affId, $affiliate)) {
            return new FailedResponse('Duplicated affiliate ID!');
        }

        $affiliate->setAffId($affId)
            ->persist();

        return new SuccessResponse();
    }

    /**
     * @Route("/admin/dashboard/affiliate/material/{hash}/download", methods={"GET"})
     * @param $hash
     * @return \Symfony\Component\HttpFoundation\Response
     * @throws \InvalidArgumentException
     */
    public function download($hash)
    {
        $material = AffiliateMaterial::findByHash($hash);
        if (!$material || !$material->getFile()) {
            return new FailedMsgResponse('Failed to find the material file!');
        }
        $material->setDownloaded($material->getDownloaded() + 1)
            ->persist();

        return $this->get('app.admin_attachment_controller')->downloadAction($hash);
    }
}