<?php


namespace AdminBundle\Controller\System;


use AdminBundle\Controller\BaseController;
use CoreBundle\Entity\Platform;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Util;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class PlatformParameterController extends BaseController
{
    /**
     * @Route("/admin/system/platform-parameter/{platform}/get/detail")
     *
     * @param Request  $request
     * @param Platform $platform
     *
     * @return SuccessResponse
     */
    public function detailAction(Request $request, Platform $platform)
    {
        $fields = [
            [
                'value' => 'enablePortals', 'label' => 'Enable Admin/Client/Member portals', 'type' => 'checkbox',
            ],
            [
                'value' => 'enableAPIs', 'label' => 'Enable APIs & widgets', 'type' => 'checkbox',
            ],
            [
              'value' => 'enableLoadTypeConfigure', 'label' => 'Enable Load type configuration for Clients', 'type' => 'checkbox',
            ],
            [
                'value' => 'rapidAgentName', 'label' => 'Rapid Agent Name',
            ],
            [
                'value' => 'rapidAgentNumber', 'label' => 'Rapid Agent Number',
            ],
            [
                'value' => 'rapidAgentAni', 'label' => 'Rapid Agent ANI',
            ],
            [
                'value' => 'rapidCardProgram', 'label' => 'Rapid CardProgram Name',
            ],
            [
                'value' => 'rapidCardProgramId', 'label' => 'Rapid Card Program ID',
            ],
            [
                'value' => 'rapidUserProfileGroupName', 'label' => 'Rapid User Profile Group Name',
            ],
            [
                'value' => 'rapidUserProfileGroupId', 'label' => 'Rapid User Profile Group ID',
            ],
            [
              	'value' => 'kycRequire', 'label' => 'ID Scan Required', 'type' => 'checkbox',
            ],
            [
                'value' => 'kycSkipCountryValidation', 'label' => 'Skip the country validation in the ID doc', 'type' => 'checkbox',
            ],
            [
              	'value' => 'clientsRequire', 'label' => 'Clients Required', 'type' => 'checkbox',
            ],
            [
              	'value' => 'institutionName', 'label' => 'Client Institution Name',
            ],
            [
              	'value' => 'institutionAddress', 'label' => 'Client Institution Address',
            ],
            [
              	'value' => 'accountNumber', 'label' => 'Client Account Number',
            ],
            [
              	'value' => 'routingNumber', 'label' => 'Client Routing Number',
            ],
            [
              	'value' => 'customerSupportPhone', 'label' => 'Customer Support Phone',
            ],
            [
              	'value' => 'customerSupportEmail', 'label' => 'Customer Support Email',
			],
			[
                'value' => 'slackInternalChannel', 'label' => 'Slack Internal Channel',
            ],
            [
                'value' => 'slackExternalChannel', 'label' => 'Slack External Channel',
            ],
            [
                'value' => 'memberDormancyPeriod', 'label' => 'Member Dormancy period (days)', 'type' => 'number', 'min' => 30, 'max' => 732
            ],
        ];
        $values = Util::meta($platform) ?: [
            'id' => $platform->getId(),
        ];
        foreach ($fields as $f) {
            $key = $f['value'];
            $type = $f['type'] ?? 'input';
            if ($type === 'checkbox' && !array_key_exists($key, $values)) {
                $values[$key] = false;
            }
        }
        $entity = [
            'id' => $platform->getId(),
            'name' => $platform->getName(),
        ];
        return new SuccessResponse(compact('fields', 'values', 'entity'));
    }

    /**
     * @Route("/admin/system/platform-parameter/{platform}/save", methods={"POST"})
     *
     * @param Request  $request
     * @param Platform $platform
     *
     * @return SuccessResponse
     */
    public function saveAction(Request $request, Platform $platform)
    {
        $values = $request->request->all();
        if ($values) {
            Util::updateMeta($platform, $values);
        }
        return new SuccessResponse();
    }
}
