<?php
/**
 * Created by PhpStorm.
 * User: hans
 * Date: 2018/5/16
 * Time: 14:01
 */

namespace AdminBundle\Controller;


use AdminBundle\Response\MsgResponse;
use AdminBundle\Response\SuccessMsgResponse;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\CardProgramFeeItem;
use CoreBundle\Entity\Currency;
use CoreBundle\Entity\Module;
use CoreBundle\Entity\Promotion;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\QueryListParams;
use CoreBundle\Utils\Traits\DbTrait;
use CoreBundle\Utils\Util;
use SalexUserBundle\Entity\User;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class PromotionController extends BaseController
{
    use DbTrait;

    /**
     * PromotionController constructor.
     */
    public function __construct()
    {
        parent::__construct();

        $this->authPermission(Module::ID_PROMOTION);
    }

    /**
     * @Route("/admin/promotion", name="admin_promotion")
     */
    public function index()
    {
        return $this->render('@Admin/Promotion/index.html.twig', [
            'cardPrograms' => $this->getUser()->getOpenCardPrograms(true),
            'items' => $this->get('knp_paginator')->paginate([]),
        ]);
    }

    protected function query(Request $request)
    {
        $query = $this->em->getRepository(\CoreBundle\Entity\Promotion::class)
            ->createQueryBuilder('p')
            ->leftJoin('p.fees', 'f')
            ->leftJoin('f.cardProgram', 'cp')
            ->where(Util::expr()->in('cp', ':__cps'))
            ->setParameter('__cps', $this->getUser()->getOpenCardPrograms());

        $params = new QueryListParams($query, $request, 'p');
        $params->orderBy = [
            'p.createdAt' => 'desc',
        ];
        $params->searchFields = [
            'p.name',
        ];
        return $this->queryListForPaginator($params);
    }

    /**
     * @Route("/admin/promotion/search/{page}/{limit}")
     * @param Request $request
     * @param int $page
     * @param int $limit
     * @return \Symfony\Component\HttpFoundation\Response
     * @throws \Exception
     */
    public function search(Request $request, $page = 1, $limit = 10) {
        $query = $this->query($request);
        return $this->render('@Admin/Promotion/list.html.twig', [
            'items' => $this->get('knp_paginator')->paginate($query, $page, $limit),
        ]);
    }

    protected function detailPageData () {
        $user = $this->getUser();
        $cps = $user->getOpenCardPrograms(true);
        $fees = [];
        /** @var CardProgram $cp */
        foreach ($cps as $cp) {
            if ($cp->isUsUnlockedLegacy()) {
                continue;
            }
            $fis = $cp->getCardProgramFeeItem()->toArray();
            $fees = array_merge($fees, $fis);
        }

        return [
            'fees' => $fees,
            'currencies' => Currency::getCodes(),
        ];
    }

    /**
     * @Route("/admin/promotion/new", methods={"GET"})
     * @param Request $request
     *
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function add(Request $request)
    {
        $user_id = $request->get('user_id');
        $user = null;
        if ($user_id) {
            $user = $this->em->getRepository(\SalexUserBundle\Entity\User::class)->find($user_id);
        }
        return $this->render('@Admin/Promotion/detail.html.twig',
            array_merge($this->detailPageData(), [
                'entity' => null,
                'onUser' => $user,
            ]));
    }

    /**
     * @Route("/admin/promotion/{promotion}/edit", methods={"GET"})
     * @param Promotion $promotion
     *
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function edit(Promotion $promotion)
    {
        $feeIds = $promotion->getFees()->map(function (CardProgramFeeItem $fee) {
            return $fee->getId();
        })->toArray();
        return $this->render('@Admin/Promotion/detail.html.twig',
            array_merge($this->detailPageData(), [
                'entity' => $promotion,
                'feeIds' => $feeIds,
                'onUser' => null,
            ]));
    }

    /**
     * @param Promotion $entity
     * @param array $input
     * @param array $fields
     */
    public function fillEntityHook($entity, array &$input, array $fields)
    {
        parent::fillEntityHook($entity, $input, $fields);

        if (isset($input['fees'])) {
            foreach ($entity->getFees() as $fee) {
                $entity->removeFee($fee);
            }
            $feeRepo = $this->em->getRepository(\CoreBundle\Entity\CardProgramFeeItem::class);
            foreach ($input['fees'] as $feeId) {
                $fee = $feeRepo->find($feeId);
                if ($fee) {
                    $entity->addFee($fee);
                }
            }
        }

        if (isset($input['discountFixed'])) {
            $input['discountFixed'] = Money::normalizeAmount($input['discountFixed'] ?: 0, 'USD');
        }

        if (isset($input['minLoad'])) {
            $input['minLoad'] = Money::normalizeAmount($input['minLoad'] ?: 0, 'USD');
        }

        if (isset($input['startAt'])) {
            $input['startAt'] = $input['startAt'] ? Util::timeUTC($input['startAt']) : null;
        }

        if (isset($input['endAt'])) {
            $input['endAt'] = $input['endAt'] ? Util::timeUTC($input['endAt']) : null;
        }

        if (isset($input['redemptionLimit']) && $input['redemptionLimit'] === '') {
            unset($input['redemptionLimit']);
            $entity->setRedemptionLimit(null);
        }
    }

    /**
     * @Route("/admin/promotion/save", methods={"POST"})
     * @param Request $request
     *
     * @return MsgResponse
     */
    public function save(Request $request)
    {
        $id = $request->get('id');
        if ($id) {
            $promotion = $this->em->getRepository(\CoreBundle\Entity\Promotion::class)->find($id);
        } else {
            $promotion = new Promotion();
        }
        $this->fillEntity($promotion);
        Util::persist($promotion);
        return new SuccessMsgResponse('/admin/promotion');
    }

    /**
     * @Route("/admin/promotion/{promotion}/delete", methods={"POST"})
     * @param Promotion $promotion
     * @return \Symfony\Component\HttpFoundation\Response
     * @throws \Doctrine\ORM\OptimisticLockException
     * @throws \Doctrine\ORM\ORMInvalidArgumentException
     * @internal param FeeItem $feeItem
     */
    public function remove(Promotion $promotion)
    {
        $this->em->remove($promotion);
        $this->em->flush();
        return new SuccessResponse();
    }

    /**
     * @Route("/admin/promotion/{promotion}/set-status/{status}", methods={"POST"})
     * @param Promotion $promotion
     * @param           $status
     *
     * @return SuccessResponse
     */
    public function setStatus(Promotion $promotion, $status)
    {
        $promotion->setStatus($status === 'active' ? null : $status)
            ->persist();
        return new SuccessResponse();
    }
}