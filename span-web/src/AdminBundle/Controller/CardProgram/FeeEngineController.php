<?php
/**
 * Created by PhpStorm.
 * User: hans
 * Date: 2018/4/17
 * Time: 20:06
 */

namespace AdminBundle\Controller\CardProgram;


use AdminBundle\Controller\BaseController;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\CardProgramFeeItem;
use CoreBundle\Entity\Currency;
use CoreBundle\Entity\FeeItem;
use Symfony\Component\Routing\Annotation\Route;

class FeeEngineController extends BaseController
{
    /**
     * @Route("/admin/card-program/{cp}/fee-items")
     * @param CardProgram $cp
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function index(CardProgram $cp)
    {
        return $this->render('@Admin/CardProgram/FeeEngine/index.html.twig', [
            'cp' => $cp,
            'items' => $this->em->getRepository(\CoreBundle\Entity\FeeItem::class)
                ->createQueryBuilder('fi')
                ->where('fi.feeName <> :feeName')
                ->setParameter('feeName', 'unknown_trancode')
                ->getQuery()
                ->getResult(),
        ]);
    }

    /**
     * @Route("/admin/card-program/{cp}/fee-item/{feeItem}")
     * @param CardProgram $cp
     * @param FeeItem $feeItem
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function edit(CardProgram $cp, FeeItem $feeItem)
    {
        return $this->render('@Admin/CardProgram/FeeEngine/detail.html.twig', [
            'cp' => $cp,
            'applyTos' => CardProgramFeeItem::getConstantsStartWith('APPLY_TO'),
            'measureUnits' => CardProgramFeeItem::getConstantsStartWith('MEASURE_UNIT'),
            'recurringTypes' => CardProgramFeeItem::getConstantsStartWith('RECURRING_TYPE'),
            'recurringTimes' => CardProgramFeeItem::getConstantsStartWith('RECURRING_TIME'),
            'currencies' => Currency::getCodes(),
            'feeItem' => $feeItem,
            'entity' => null,
        ]);
    }
}