<?php
/**
 * User: Bob
 * Date: 2017/2/16
 * Time: 15:18
 * This class is used to handle all the actions for Processor(s) Management Menu
 */

namespace AdminBundle\Controller;

use AdminBundle\Form\Type\ProcessorType;
use CoreBundle\Entity\Module;
use CoreBundle\Entity\Processor;
use CoreBundle\Attribute\Template;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Processor Management
 * Class ProcessorController
 * @package AdminBundle\Controller
 */
class ProcessorController extends BaseController
{

    /**
     * DefaultController constructor.
     */
    public function __construct()
    {
        parent::__construct();

        $this->authPermission(Module::ID_PROCESSOR_MANAGEMENT);
    }

    /**
     * @Route("/admin/processor_index/{page}/{limit}",defaults={"page" = 1,"limit" = 10},name="admin_processor")
     */
    #[Template()]
    public function indexAction(Request $request,$page,$limit)
    {
        $processors = $this->getDoctrine()
            ->getRepository(\CoreBundle\Entity\Processor::class)
            ->findAll();
        $paginator = $this->get('knp_paginator');
        $pagination= $paginator->paginate($processors, $page,$limit);
        return array('processors'=> $pagination,
            /* Comment on 2017-02-23 replace with jquery data table
             * 'search_info' => ''*/);
    }

    /**
     * @Route("/admin/processor/new", name="admin_processor_new")
     */
    #[Template()]
    public function newAction(Request $request)
    {
        $processor = new Processor();
        $form = $this->createForm(ProcessorType::class, $processor);

        if ($request->isMethod('POST')) {
            $form->handleRequest($request);

            if ($form->isValid()) {
                $em = $this->getDoctrine()->getManager();
                $em->persist($processor);
                $em->flush();
                return $this->redirect($this->generateUrl('admin_processor'));
            }
        }
        return array('form' => $form->createView());
    }

    /**
     * @Route("/admin/processor/modify", name="admin_processor_modify")
     */
    #[Template()]
    public function modifyAction(Request $request)
    {
        $processorId = $request->get("processor_id");

        $em = $this->getDoctrine()->getManager();
        $processor = $em->getRepository(\CoreBundle\Entity\Processor::class)->find($processorId);
        if (!$processor) {
            throw $this->createNotFoundException('No Processor found for id ' . $processorId);
        }

        $form = $this->createForm(ProcessorType::class, $processor);

        if ($request->isMethod('POST')) {
            $form->handleRequest($request);

            if ($form->isValid()) {
                $em->flush();

                return $this->redirect($this->generateUrl('admin_processor'));
            }
        }

        return array(
            'form' => $form->createView(),
            'processor_id' => $processorId,
        );
    }

    /**
     * @Route("/admin/processor/delete", name="admin_processor_delete")
     */
    public function deleteAction(Request $request)
    {
        if ($request->isMethod('POST')) {
            $processorId = $request->get("processor_id");
            $em = $this->getDoctrine()->getManager();
            $processor = $em->getRepository(\CoreBundle\Entity\Processor::class)->find($processorId);
            if (!$processor) {
                throw $this->createNotFoundException('No Processor found for id ' . $processorId);
            }

            $em->remove($processor);
            $em->flush();
        }

        return $this->redirect($this->generateUrl('admin_processor'));
    }

    /**
     * @Route("/admin/processor/ajaxdelete", name="admin_processor_ajaxdelete")
     */
    public function ajaxdeleteAction(Request $request)
    {
        $pid=$_GET['pid'];
        $em = $this->getDoctrine()->getManager();
        $cardtype = $em->getRepository(\CoreBundle\Entity\CardType::class)->findAll();
        $cardtypearry=array();
        foreach ($cardtype as $item){
            if($item->getProcessor()) {
                if($item->getProcessor()->getId()) {
                    if ($item->getProcessor()->getId() == $pid) {
                        $cardtypearry[] = $item;
                    }
                }
            }
        }
        if($cardtypearry)
            exit('no');
        else
            exit('yes');

    }

    /**
     * @Route("/admin/processor/ajaxydelete", name="admin_processor_ajaxydelete")
     */
    public function ajaxydeleteAction(Request $request)
    {
            $processorId = $_GET['pid'];
            $em = $this->getDoctrine()->getManager();
            $processor = $em->getRepository(\CoreBundle\Entity\Processor::class)->find($processorId);
            if (!$processor) {
                throw $this->createNotFoundException('No Processor found for id ' . $processorId);
            }

            $em->remove($processor);
            $em->flush();
            exit('ok');
    }
}
