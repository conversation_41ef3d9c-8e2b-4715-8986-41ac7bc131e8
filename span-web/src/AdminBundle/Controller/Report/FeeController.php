<?php


namespace AdminBundle\Controller\Report;


use AdminBundle\Controller\BaseController;
use AdminBundle\Controller\Traits\FeeControllerTrait;
use Carbon\Carbon;
use CoreBundle\Entity\FeeGlobalName;
use CoreBundle\Entity\Module;
use CoreBundle\Entity\ReportFee;
use CoreBundle\Entity\UserFeeHistory;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\QueryListParams;
use CoreBundle\Utils\Traits\DbTrait;
use CoreBundle\Utils\Traits\ExcelTrait;
use CoreBundle\Utils\Util;
use DateTime;
use Doctrine\ORM\AbstractQuery;
use Doctrine\ORM\QueryBuilder;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class FeeController extends BaseController
{
    use ExcelTrait;
    use DbTrait;
    use FeeControllerTrait;

    protected $timezone;

    public $generateCache = false;

    /**
     * DefaultController constructor.
     */
    public function __construct()
    {
        parent::__construct();

        $this->authPermission(Module::ID_FEE_REPORT);
        $this->timezone = Util::tzNewYork();
    }

    /**
     * @Route("/admin/report/fee/filters")
     * @return SuccessResponse
     */
    public function searchFiltersAction() {
        return new SuccessResponse($this->getSearchFilters());
    }

    /**
     * @Route("/admin/report/fee/search/{page}/{limit}", defaults={"page" = 1,"limit" = 10})
     * @param Request $request
     * @param int     $page
     * @param int     $limit
     *
     * @return SuccessResponse
     */
    public function search(Request $request, $page = 1, $limit = 10)
    {
        $query = $this->query($request);
        $params = $this->queryListParams($query, $request);
        $result = $this->queryListForAPI($params, function ($entity) {
            return $this->getRowData($entity);
        });
        if ($this->generateCache) {
            return $result['data'];
        }
        $result['data'] = $this->parseQueryResult($result['data'], $page, $limit);

        $other = $this->getSummaryAndChartData($query, $request);
        $result = array_merge($result, $other);
        return new SuccessResponse($result);
    }

    protected function parseQueryResult($data, $page, $limit)
    {
        $new = [];
        foreach ($data as $item) {
            $time = $item['time'];
            if (!isset($new[$time])) {
                $new[$time] = [
                    'Date' => $time,
                    'Membership Fee' => 0,
                    'Membership Count' => 0,
                    'PayPal Subscription Fee' => 0,
                    'PayPal Subscription Annual Fee' => 0,
                    'PayPal Subscription Monthly Fee' => 0,
                    'PayPal Subscription Annual Count' => 0,
                    'PayPal Subscription Monthly Count' => 0,
                    'Load Fee' => 0,
                    'Load Count' => 0,
                    'Card Creation Fee' => 0,
                    'Card Creation Count' => 0,
                    'Transaction Fee' => 0,
                    'Transaction Count' => 0,
                    'Decline Fee' => 0,
                    'Decline Count' => 0,
                    'Monthly Fee' => 0,
                    'Monthly Count' => 0,
                    'Unload Fee' => 0,
                    'Unload Count' => 0,
                    'Sum' => 0,
                ];
            }
            $fee = $item['feeName'];
            $amount = $item['amount'];
            $count = $item['count'];
            if ($fee !== FeeGlobalName::PAYPAL_SUBSCRIPTION_MONTHLY_FEE && $fee !== FeeGlobalName::PAYPAL_SUBSCRIPTION_ANNUAL_FEE) {
                $new[$time]['Sum'] += $amount;
            }
            
            if ($fee === FeeGlobalName::ONE_TIME_MEMBERSHIP_FEE) {
                $new[$time]['Membership Fee'] += $amount;
                $new[$time]['Membership Count'] += $count;
            } else if ($fee === FeeGlobalName::PAYPAL_SUBSCRIPTION_FEE) {
                $new[$time]['PayPal Subscription Fee'] += $amount;
            } else if ($fee === FeeGlobalName::PAYPAL_SUBSCRIPTION_ANNUAL_FEE) {
                $new[$time]['PayPal Subscription Annual Fee'] += $amount;
                $new[$time]['PayPal Subscription Annual Count'] += $count;
            } else if ($fee === FeeGlobalName::PAYPAL_SUBSCRIPTION_MONTHLY_FEE) {
                $new[$time]['PayPal Subscription Monthly Fee'] += $amount;
                $new[$time]['PayPal Subscription Monthly Count'] += $count;
            } else if ($fee === FeeGlobalName::CARD_CREATION_FEE) {
                $new[$time]['Card Creation Fee'] += $amount;
                $new[$time]['Card Creation Count'] += $count;
            } else if ($fee === FeeGlobalName::APPROVED_TRANSACTION_FEE || $fee === 'Transaction fee to spendr') {
                $new[$time]['Transaction Fee'] += $amount;
                $new[$time]['Transaction Count'] += $count;
            } else if ($fee === FeeGlobalName::DECLINED_TRANSACTION_FEE) {
                $new[$time]['Decline Fee'] += $amount;
                $new[$time]['Decline Count'] += $count;
            } else if ($fee === FeeGlobalName::MONTHLY_FEE) {
                $new[$time]['Monthly Fee'] += $amount;
                $new[$time]['Monthly Count'] += $count;
            } else if ($fee === FeeGlobalName::UNLOAD_FEE) {
                $new[$time]['Unload Fee'] += $amount;
                 $new[$time]['Unload Count'] += $count;
            } else if (!in_array($fee, [
                FeeGlobalName::ADMIN_DEBIT,
                'Consumer ach return fee to spendr',
                'Correct refund refund fee',
                'Transaction fee to spendr',
            ]) && !Util::containsSubString(strtolower($fee), [
                'spendr', 'atm',
                'refund', 'return', 'ach',
            ])) {
                $new[$time]['Load Fee'] += $amount;
                $new[$time]['Load Count'] += $count;
            }
        }
        krsort($new);
        $new = array_values($new);
        return $this->sliceQueryArrayBy($new, $page, $limit);
    }

    public function query(Request $request)
    {
        if ($this->generateCache) {
            $query = $this->em->getRepository(UserFeeHistory::class)
                ->createQueryBuilder('ufh')
                ->join('ufh.user', 'u')
                ->join('u.cards', 'uc')
                ->join('uc.card', 'c')
                ->join('c.cardProgram', 'cp')
                ->join('c.cardProgram', '__cp')
            ;
            Util::queryRealConsumers($query);
        } else {
            $query = $this->em->getRepository(ReportFee::class)
                ->createQueryBuilder('ufh')
                ->join('ufh.cardProgram', 'cp')
                ->join('ufh.cardProgram', '__cp')
            ;
        }

        if (Util::$platform) {
            $query->andWhere(Util::expr()->in('__cp', ':__cps'))
                ->setParameter('__cps', Util::$platform->getCardPrograms());
        }

        return $query;
    }

    protected function queryListParams(QueryBuilder $query, Request $request)
    {
        // cutoff to be 11:59:59 each day Eastern Time (NY)
        $range = $request->get('range');
        if (!empty($range['ufh.time']['start'])) {
            $start = Util::timeUTC($range['ufh.time']['start']);
            $start = Carbon::create($start->year, $start->month, $start->day, 0, 0, 0, $this->timezone);
            $range['ufh.time']['start'] = $start->format('c');
        }
        if (!empty($range['ufh.time']['end'])) {
            $end = Util::timeUTC($range['ufh.time']['end']);
            $end = Carbon::create($end->year, $end->month, $end->day, 0, 0, 0, $this->timezone);
            $range['ufh.time']['end'] = $end->format('c');
        }
        $request->request->set('range', $range);

        // Create the QueryListParams
        $params = new QueryListParams($query, $request, 'ufh', 'count(distinct date(ufh.time))');
        $params->orderBy = [
            'ufh.time' => 'desc',
        ];
        $params->pagination = false;
        $params->hydrationMode = AbstractQuery::HYDRATE_ARRAY;
        $params->dataSelect = 'ufh.id, ufh.time, ufh.feeName, ufh.amount';
        if ($this->generateCache) {
            $params->dataSelect .= ', ufh.meta';
        } else {
             $params->dataSelect .= ', ufh.count';
        }

        return $params;
    }

    protected function getRowData($row)
    {
        /** @var DateTime $time */
        $time = $row['time'];
        $row['time'] = $time->setTimezone($this->timezone)
            ->format(Util::DATE_FORMAT_SEARCH);
        return $row;
    }

    /**
     * @Route("/admin/report/fee/export", methods={"POST"})
     * @param Request $request
     *
     * @return SuccessResponse
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     */
    public function export(Request $request)
    {
        Util::longRequest();

        $query = $this->query($request);
        $params = $this->queryListParams($query, $request);
        if ($request->get('query_count')) {
            $count = $this->queryCountForExport($params);
            return new SuccessResponse($count);
        }

        $from  = 0;
        $limit = 999999;

        $data = $this->queryListForExport($params, $from, $limit, function ($entity) {
            return $this->getRowData($entity);
        });
        $data = $this->parseQueryResult($data, 1, $limit);
        $sum = [
            'Date'            => 'Sum',
            'Membership Fee'  => 0,
            'PayPal Subscription Fee' => 0,
            'Load Fee'        => 0,
            'Card Creation Fee' => 0,
            'Transaction Fee' => 0,
            'Decline Fee'     => 0,
            'Monthly Fee'     => 0,
            'Unload Fee'      => 0,
            'Sum'             => 0,
        ];
        foreach ($data as $item) {
            foreach ($item as $field => $value) {
                if ($field === 'Date') {
                    continue;
                }
                $sum[$field] += $value;
            }
        }
        $data[] = $sum;

        $headers = [];
        $headers = array_merge($headers, [
            'Date'            => 12,
            'Membership Fee'  => 20,
            'PayPal Subscription Fee'  => 30,
            'Load Fee'        => 20,
            'Card Creation Fee' => 20,
            'Transaction Fee' => 20,
            'Decline Fee'     => 20,
            'Monthly Fee'     => 20,
            'Unload Fee'      => 20,
            'Sum'             => 20,
        ]);
        $textColumns = [];
        $time = ceil($request->get('query_timestamp', time() * 1000) / 1000);
        $filename = 'Fee Report ' . date(Util::DATE_TIME_FILE_FORMAT, $time);
        $destination = Util::uploadDir('report') . $filename . '.xlsx';
        $excel = $this->loadOrCreateExcel($destination, $filename);

        $excel = $this->generateSheetAppendToExcel($excel, 'Fee Report',
            $headers, $data, static function ($col, $row, $excel, $title) {
                $value = $row[$title] ?? '';
                if ($title === 'Sum' || Util::endsWith($title, ' Fee')) {
                    return Money::formatAmountToNumber($value);
                }
                return $value;
            }, $textColumns);
        $moneyIndex = ['B', 'C', 'D', 'E', 'F', 'G', 'H'];
        $this->setColumnsNumberMoney($moneyIndex, count($data));
        $excel->setActiveSheetIndex(0);
        $this->saveExcelTo($excel, $destination);

        return new SuccessResponse('report/' . $filename . '.xlsx');
    }
}
