<?php


namespace AdminBundle\Controller;


use AdminBundle\Controller\User\UserSearchController;
use Carbon\Carbon;
use CoreBundle\Entity\Alert;
use CoreBundle\Entity\AlertJob;
use CoreBundle\Entity\Module;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\QueryListParams;
use CoreBundle\Utils\Service;
use CoreBundle\Utils\Traits\DbTrait;
use CoreBundle\Utils\Util;
use Doctrine\ORM\AbstractQuery;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\QueryBuilder;
use Icicle\Awaitable;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class AlertJobController extends UserSearchController
{
    use DbTrait;

    private $timezone;

    public function processQuery($chunkedResults, $target_id, $job): ?\Generator
    {
        $processCount = 0;

        // For each user queried, add the alert if it is not already present.
        foreach ($chunkedResults as $result)
        {
            $resultArray = $result->getNotifications() ?: [];
            if (!in_array($target_id, $resultArray, true))
            {
                $resultArray[] = $job->getId();
                $result->setNotifications($resultArray);
                $processCount++;
            }
            yield Awaitable\resolve($processCount);
        }
    }

    public function splitArray(array $input_array, int $size, $preserve_keys = null): array
    {
        // Function for splitting any length array into specific number of even arrays.
        $nr = (int)ceil(count($input_array) / $size);

        if ($nr > 0) {
            return array_chunk($input_array, $nr, $preserve_keys);
        }

        return $input_array;
    }

    /**
     * @Route("/admin/alert_job_submit/{target_id}", methods={"POST"}, name="alert_job_submit")
     * @param Request $request
     * @param int $target_id
     *
     * @return SuccessResponse
     */
    public function submit(Request $request, $target_id): ?SuccessResponse
    {
        // Get advanced user search filters
        $filters = self::getSearchParams($request);

//        $jsonRequest = json_encode($request, JSON_THROW_ON_ERROR);

        // Create simple array of filters being used.
        $parsedFilters = [];
        foreach ($filters as $filterKey => $filter)
        {
            if ($filter !== "" && $filter !== null)
            {
                $parsedFilters[] = $filterKey . ': ' . $filter;
            }
        }

        // Create new job to assign to users.
        $user = $this->getUser();

        $job = new AlertJob();
        $job->setAlert($target_id)
            ->setCriteria($parsedFilters)
            ->setCreationDate(new \DateTime())
            ->setCreatedBy($user->getId())
            ->setTotalSent(0)
            ->setTotalViewed(0)
            ->setQuery($filters)
            ->persist();

        $jobId = $job->getId();

        //Trigger a separate async service to assign jobs. This is done to prevent timeout from modifying so many queries at once.
        Service::sendAsync('/t/cron/alert_job_assign/' . $jobId);

        return new SuccessResponse();
    }

    /**
     * @Route("admin/alert_job/delete_job/{alertJob}",name="admin_alert_delete")
     * @param Request $request
     * @param AlertJob $alertJob
     *
     * @throws OptimisticLockException
     */
    public function deleteJob(Request $request, AlertJob $alertJob): \Symfony\Component\HttpFoundation\RedirectResponse
    {
        $this->em->remove($alertJob);
        $this->em->flush();

//        return new RedirectResponse('admin/alert_job/search');
        return $this->redirectToRoute('alert_job');
    }

    /**
     * DefaultController constructor.
     */
    public function __construct()
    {
        parent::__construct();

        $this->authPermission(Module::ID_ALERT_JOB_LIST);
    }

    /**
     * @Route("/admin/alert_job/filters")
     * @return SuccessResponse
     */
    public function searchFiltersAction() {
        return new SuccessResponse([]);
    }

    /**
     * @Route("/admin/alert_job/search/{page}/{limit}", defaults={"page" = 1,"limit" = 10}, name="alert_job")
     * @param Request $request
     * @param int     $page
     * @param int     $limit
     *
     * @return SuccessResponse
     */
    public function search(Request $request, $page = 1, $limit = 10)
    {
        $query = $this->query($request);
        $params = $this->queryListParams($query, $request);
        $result = $this->queryListForAPI($params, function ($entity) {
            return $this->getRowData($entity);
        });
        $totalQuery = $query->getQuery()
            ->getArrayResult();
        $result['data'] = $this->parseQueryResult($result['data'], $page, $limit);
        $result['quick'] = [
            'totalSize' => count($totalQuery)
        ];
        return new SuccessResponse($result);
    }

    public function query(Request $request)
    {
        $query = Util::em()->getRepository(AlertJob::class)
            ->createQueryBuilder('alertJob');
        return $query;
    }

    protected function parseQueryResult($data, $page, $limit)
    {
        $alertRepo = Util::em()->getRepository(Alert::class);
        $new = [];
        foreach ($data as $item) {
            $alert = $alertRepo->find($item['alert']);
            $description = $alert->getDescription();
            $time = $item['creationDate'];
            if (!isset($new[$time])) {
                $new[] = [
                    'Alert Job ID' => $item['id'],
                    'Creation Date' => $time,
                    'Alert ID' => $item['alert'],
                    'Criteria' => $item['criteria'],
                    'Creator' => $item['createdBy'],
                    'Total Sent' => $item['totalSent'],
                    'Total Viewed' => $item['totalViewed'],
                    'Description' => $description
                ];
            }
        }
        krsort($new);
        $new = array_values($new);
        return $this->sliceQueryArrayBy($new, $page, $limit);
    }

    protected function queryListParams(QueryBuilder $query, Request $request)
    {
        // cutoff to be 11:59:59 each day Eastern Time (NY)
        $range = $request->get('range');
        $user = $this->getUser();
        $this->timezone = (new \Carbon\Carbon)->getTimeZone();
        if (!empty($range['alertJob.creationDate']['start'])) {
            $start = Util::timeUTC($range['alertJob.creationDate']['start']);
            $start = Carbon::create($start->year, $start->month, $start->day, 0, 0, 0, $this->timezone);
            $range['alertJob.creationDate']['start'] = $start->format('c');
        }
        if (!empty($range['alertJob.creationDate']['end'])) {
            $end = Util::timeUTC($range['alertJob.creationDate']['end']);
            $end = Carbon::create($end->year, $end->month, $end->day, 0, 0, 0, $this->timezone);
            $range['alertJob.creationDate']['end'] = $end->format('c');
        }
        $request->request->set('range', $range);

        // Create the QueryListParams
        $params = new QueryListParams($query, $request, 'alertJob', 'count(distinct date(alertJob.creationDate))');
        $params->orderBy = [
            'alertJob.creationDate' => 'asc',
        ];
        $params->pagination = false;
        $params->hydrationMode = AbstractQuery::HYDRATE_ARRAY;
        $params->dataSelect = 'alertJob.id, alertJob.creationDate, alertJob.alert, alertJob.criteria, alertJob.createdBy, alertJob.totalSent, alertJob.totalViewed ';
        return $params;
    }

    protected function getRowData($row)
    {
        /** @var \DateTime $time */
        $time = $row['creationDate'];
        $user = $this->getUser();
        $this->timezone = (new \Carbon\Carbon)->getTimeZone();
        $row['creationDate'] = $time->setTimezone($this->timezone)
            ->format(Util::DATE_FORMAT);
        return $row;
    }
}
