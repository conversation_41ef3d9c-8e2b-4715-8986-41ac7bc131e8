<?php


namespace TransferMexBundle\Entity;


use Carbon\Carbon;
use CoreBundle\Entity\CardProgramCardType;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\Processor;
use CoreBundle\Entity\UserCard;
use CoreBundle\Services\SSLEncryptionService;
use CoreBundle\Utils\Util;
use FaasBundle\Services\BOTM\BotmAPI;
use SalexUserBundle\Entity\User;
use TransferMexBundle\Services\RapidAPI;
use CoreBundle\Utils\Log;

trait UserCardTransferMexTrait
{
    /**
     * @param                               $an
     * @param Platform|Processor|false|null $platformOrProcessor
     * @param bool                          $withLegacy
     *
     * @return null|UserCard
     */
    public static function findByAccountNumberWithLegacies($an, $platformOrProcessor = null, bool $withLegacy = true)
    {
        $em = Util::em();
        $cards = [];
        if ($platformOrProcessor instanceof Platform) {
            $cp = $platformOrProcessor->getCardPrograms()->first();
            if ($cp) {
                $cards[] = CardProgramCardType::getForCardProgram($cp);
            }
        } else if ($platformOrProcessor instanceof Processor) {
            $cards = $em->getRepository(CardProgramCardType::class)
                ->createQueryBuilder('cpct')
                ->join('cpct.cardProgram', 'cp')
                ->where('cp.processor = :processor')
                ->setParameter('processor', $platformOrProcessor)
                ->getQuery()
                ->getResult();
        } else if ($platformOrProcessor === null) {
            $cp = Util::cardProgram();
            $cards[] = CardProgramCardType::getForCardProgram($cp);
        }
        if (!$cards) {
            return null;
        }

        $expr = Util::expr();
        $q = $em->getRepository(\CoreBundle\Entity\UserCard::class)
            ->createQueryBuilder('uc')
            ->where($expr->in('uc.card', ':cards'))
            ->setParameter('cards', $cards)
            ->setMaxResults(1);

        $rs = (clone $q)->andWhere('uc.accountNumber = :an')
            ->setParameter('an', $an)
            ->getQuery()
            ->getResult();
        if ($rs) {
            return $rs[0];
        }

        if ($withLegacy) {
            $expr = Util::expr();
            $rs = $q->andWhere($expr->like('uc.meta', ':meta'))
                ->setParameter('meta', '%"oldAccountNumbers"%"' . $an . '"%')
                ->getQuery()
                ->getResult();
            if ($rs) {
                return $rs[0];
            }
        }

        return null;
    }

    /**
     * @param                               $an
     *
     * @return null|UserCard
     */
    public static function findByDepositNumber($depositNumber) {
      $em = Util::em();
      $expr = Util::expr();
      $nickName = hash_hmac('sha256', $depositNumber, Util::getConfigKey('botm_account_number_hash'));
      if (Util::isStaging()) {
        Log::debug($depositNumber);
        Log::debug($nickName);
      }
      $rs = $em->getRepository(\CoreBundle\Entity\UserCard::class)
              ->createQueryBuilder('uc')
              ->where($expr->eq('uc.nickName', ':nickName'))
              ->setParameter('nickName', $nickName)
              ->setMaxResults(1)
              ->getQuery()
              ->getResult();
      if ($rs) {
          return $rs[0];
      }
      return null;
    }

    public function getIssueStatus($title = true)
    {
        $status = $this->getStatus();
        if ($status === 'active' && !$this->isIssued()) {
            $status = $this->getAccountNumber() ? 'active_though_pending_to_enroll' : 'active_though_pending_to_assign';
        }
        if ($title) {
            $nativeStatus = $this->getNativeStatus();
            if ($nativeStatus) {
                $status = $nativeStatus;
                if ($nativeStatus === RapidAPI::CARD_STATUS_ACTIVE && !$this->isIssued()) {
                    $status .= ' though pending to enroll';
                }
            }
        }
        return $title ? Util::title($status) : $status;
    }

    public function toApiArrayForTransferMex()
    {
        $group = Util::user()->getPrimaryGroup();
        $balance = (is_null($group) || !$group->getAllowBalanceViewing()) ? 0 : $this->getBalance();

        $legacies = [];
        foreach ($this->getLegacyAccountNumbers() as $legacy) {
            if (RapidAPI::isAccountNumberValid($legacy)) {
                $legacies[] = $legacy;
            }
        }

        return [
            'id' => $this->getId(),
            'accountNumber' => '', // $this->getAccountNumber(), // Client don't need this number
            'legacies' => count($legacies),
            'balance' => $balance,
            'status' => $this->getIssueStatus(false),
			'maskedCardNumber' => $this->getMaskedCardNumber(),
			'cardStatus' => $this->getStatus(),
            'processor' => $this->getCurrentProcessor(),
        ];
    }

    public function isTransferMexSandbox()
    {
        return Util::meta($this, 'transferMexSandbox') === true;
    }

    public function getDepositInfoViewLogs()
    {
        $logs = Util::meta($this, 'depositInfoViewLogs') ?: [];
        return array_map(function ($log) {
            return [
                'time' => Carbon::createFromTimestampUTC($log['time'])
                    ->format(Util::DATE_TIME_FORMAT),
                'name' => $log['uname'],
            ];
        }, $logs);
    }

    public function addDepositInfoViewLog(User $user)
    {
        $logs = Util::meta($this, 'depositInfoViewLogs') ?: [];
        array_unshift($logs, [
            'time' => time(),
            'uid' => $user->getId(),
            'uname' => $user->getName(),
        ]);
        Util::updateMeta($this, [
            'depositInfoViewLogs' => $logs,
        ]);
    }

    public function getUpdatedOrApiPIN()
    {
        return '****';
    }

    public function setUpdatedPIN($pin = null)
    {
        $an = $this->getLast4();
        Util::updateMeta($this, [
            'updatedPIN' => '',
            'updatedPinAt_' . $an => Carbon::now()->format(Util::DATE_FORMAT_ISO_DATE_TIME),
        ]);
    }

    public function decryptCardNumber()
    {
        $no = Util::meta($this, 'cardNumber');
        if (!$no) {
            return $this->getPan();
        }
        return SSLEncryptionService::tryToDecrypt($no);
    }

    public function saveMaskedCardNumber($number)
    {
        $number = str_replace([' ', '-', ',', '.'], '', $number);
        return $this->setPan(Util::maskPan($number));
    }

    public function getMaskedCardNumber($visible = 2)
    {
        $pan = $this->getPan() ?: $this->getDbaNo();
        return Util::maskPan($pan, $visible);
    }

    public function getMaskedPan($visible = 4)
    {
        return Util::maskPan($this->getPan(), $visible);
    }

    public function isUsingBaseRapidAccount()
    {
        $useBase = Util::meta($this, 'useBaseRapidAgent');
        return $useBase ?? false;
    }

    public function getSignature()
    {
        $an = $this->getAccountNumber();
        $pan = substr($this->getLast4(), -2);
        if ($pan) {
            $an .= ' (' . $pan . ')';
        }
        return $an;
    }

    public function getLegacyAccountNumbers()
    {
        $oc = Util::meta($this, 'oldAccountNumbers') ?? [];
        $an = $this->getAccountNumber();
        $oc = array_filter($oc, function($item) use ($an) {
            return $item !== $an;
        });
        return array_values(array_unique($oc));
    }

    public function getAllAccountNumbers()
    {
        $all = [];
        $an = $this->getAccountNumber();
        if ($an) {
            $all[] = $an;
        }
        $oc = Util::meta($this, 'oldAccountNumbers') ?? [];
        if ($oc) {
            $all = array_merge($all, $oc);
        }
        return array_values(array_unique($all));
    }

    public function getRetiredRapidAccountNumber()
    {
        return Util::meta($this, 'retiredRapidAccount');
    }

    public function getMostRecentRapidAccountNumber()
    {
        $an = $this->getAccountNumber();
        if (RapidAPI::isAccountNumberValid($an)) {
            return $an;
        }
        return $this->getRetiredRapidAccountNumber();
    }

    public function getTransferFromAccountNumber()
    {
        $an = $this->getAccountNumber();
        if ($an) {
            return $an;
        }
        return $this->getBotmTransferFromAccountNumber();
    }

    public function getBotmTransferFromAccountNumber()
    {
        $bua = $this->getUser()->ensureConfig()->getBotmUserAccountId();
        if ($bua) {
            return 'BOTM-' . $bua;
        }
        return null;
    }
}
