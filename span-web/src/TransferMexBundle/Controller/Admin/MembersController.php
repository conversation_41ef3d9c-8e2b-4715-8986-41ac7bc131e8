<?php


namespace TransferMexBundle\Controller\Admin;


use AppBundle\Command\InstantBackgroundCommand;
use Carbon\Carbon;
use CoreBundle\Entity\Attachment;
use CoreBundle\Entity\CardProgramCardType;
use CoreBundle\Entity\Config;
use CoreBundle\Entity\Country;
use CoreBundle\Entity\ExternalInvoke;
use CoreBundle\Entity\Processor;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\Tag;
use CoreBundle\Entity\Transfer;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserCardTransaction;
use CoreBundle\Entity\UserGroup;
use CoreBundle\Entity\UserOtherInfo;
use CoreBundle\Exception\FailedException;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Services\Common\CardService;
use CoreBundle\Services\KycService;
use CoreBundle\Utils\Bundle;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\QueryListParams;
use CoreBundle\Utils\Service;
use CoreBundle\Utils\Util;
use Doctrine\ORM\QueryBuilder;
use FaasBundle\Services\BOTM\BotmAPI;
use FaasBundle\Services\BOTM\BotmService;
use FaasBundle\Services\FaasMemberService;
use FaasBundle\Services\ProcessorHub;
use PortalBundle\Exception\PortalException;
use SalexUserBundle\Entity\User;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use TransferMexBundle\Entity\EmployeeSSN;
use TransferMexBundle\Entity\EmployerPayout;
use TransferMexBundle\Entity\ImportMemberConfigTransferFreeRecord;
use TransferMexBundle\Entity\ImportMemberMigrateCardsRecord;
use TransferMexBundle\Entity\ImportMembersRecord;
use TransferMexBundle\Entity\ImportMemberUnloadRecord;
use TransferMexBundle\Entity\ImportUpdateLastFourSsnRecord;
use TransferMexBundle\Entity\SplashPage;
use TransferMexBundle\Entity\UserTransferMexTrait;
use TransferMexBundle\Services\BatchConfigTransferFreeService;
use TransferMexBundle\Services\BatchUpdateService;
use TransferMexBundle\Services\EmployerService;
use TransferMexBundle\Services\MemberService;
use TransferMexBundle\Services\ProcessorMemberService;
use TransferMexBundle\Services\RapidAPI;
use TransferMexBundle\Services\RapidService;
use TransferMexBundle\Services\SlackService;
use TransferMexBundle\Services\TransactionService;
use TransferMexBundle\Services\UniTellerRemittanceService;
use TransferMexBundle\TransferMexBundle;
use UsUnlockedBundle\Controller\IDologyControllerTrait;
use UsUnlockedBundle\Services\IDologyService;

class MembersController extends AgentsController
{
    use IDologyControllerTrait;
    use CardHolderControllerTrait;
    use UserTransferMexTrait;

    protected function getAccessibleRoles()
    {
        return UserTransferMexTrait::getAgentRoles();
    }

    /**
     * @Route("/admin/mex/members/list/{page}/{limit}", methods={"GET"}, defaults={"page" = 1,"limit" = 10})
     * @param Request $request
     * @param int     $page
     * @param int     $limit
     *
     * @return SuccessResponse
     */
    public function search(Request $request, $page = 1, $limit = 10)
    {
        $resp = parent::search($request, $page, $limit);
        $result = $resp->getResult();

        $result['quick']['active'] = $this->query($request)
            ->andWhere('u.register_step = :registerStep')
            ->setParameter('registerStep', MemberService::STEP_ACTIVE)
            ->select('count(distinct u)')
            ->getQuery()
            ->getSingleScalarResult();

        $result['quick']['balance'] = $this->queryTotal($request)
            ->select('sum(uc.balance)')
            ->getQuery()
            ->getSingleScalarResult();

        $result['quick']['cost'] = MemberService::calculateKycCost($this->query($request));
        $result['quick']['lastImport'] = MemberService::getLastImport($this->user, Util::cardProgram());
        $result['quick']['lastMigrate'] = MemberService::getLastMigrate($this->user, Util::cardProgram());
        $result['quick']['lastImportSsn'] = MemberService::getLastImportSsn($this->user);

        return new SuccessResponse($result);
    }

    protected function otherQuery(Request $request) {
      $q = $this->em->getRepository(UserOtherInfo::class)
                ->createQueryBuilder('uo')
                ->join('uo.user', 'user');
      return $this->queryUserOtherInfo($q, $request);
    }
    protected function query(Request $request)
    {
        $query = $this->em->getRepository(User::class)
            ->createQueryBuilder('u')
            ->join('u.teams', 't')
            ->join('u.userGroups', 'g')
            ->join('g.adminConfigs', 'ac')
            ->join('u.config', 'config')
            ->join('ac.user', 'e')
            ->join('u.cards', 'uc')
            ->join('uc.card', 'c')
            ->where(Util::expr()->in('t.name', ':roles'))
            ->andWhere('c.cardProgram = :cardProgram')
            ->setParameter('cardProgram', Util::cardProgram())
            ->setParameter('roles', Bundle::getMemberRoles());
        if (!$this->user->inTeam(Role::ROLE_MASTER_ADMIN)) {
            $query->andWhere(Util::expr()->notIn('u.id', ':hideIds'))
                  ->setParameter('hideIds', TransferMexBundle::hideUsers());
        }
        $otherFields = $request->get('otherInfo');
        $not = $request->get('__not');
        $otherInfoNotFlag = false;
        $notIsBotm = '';
        $notBotmLinked = '';
        if (is_array($not)) {
          foreach($not as $key => $value) {
            if (Util::startsWith($key, 'otherInfo')) {
              $otherInfoNotFlag = true;
            }
            if ($key == 'is_botm') {
              $notIsBotm = $value;
            }
            if ($key == 'botm_linked') {
              $notBotmLinked = $value;
            }
          }
        }
        $isBotm = $request->get('is_botm');
        if ( $isBotm) {
          $isBotm == 'yes' ? $query->andWhere(Util::expr()->like('uc.accountNumber',':isBotm')) : $query->andWhere(Util::expr()->notLike('uc.accountNumber', ':isBotm'));
          $query->setParameter('isBotm', '%-%');
        }

        if ($notIsBotm) {
          $notIsBotm == 'yes' ? $query->andWhere(Util::expr()->notLike('uc.accountNumber',':notIsBotm')) : $query->andWhere(Util::expr()->like('uc.accountNumber', ':notIsBotm'));
          $query->setParameter('notIsBotm', '%-%');
        }

        $botmLinked = $request->get('botm_linked');
        if ( $botmLinked ) {
          $botmLinked == 'yes' ? $query->andWhere(Util::expr()->isNotNull('config.botmUserAccountId')) : $query->andWhere(Util::expr()->isNull('config.botmUserAccountId'));
        }

        if ($notBotmLinked) {
          $notBotmLinked == 'yes' ? $query->andWhere(Util::expr()->isNull('config.botmUserAccountId')) : $query->andWhere(Util::expr()->isNotNull('config.botmUserAccountId'));
        }

        if ($otherFields || $otherInfoNotFlag) {
          $expr = Util::expr();
          $subQuery = $this->otherQuery($request)->select('user.id')->getDQL();
          $query->andWhere($expr->eq('u.id', $expr->any($subQuery)));
        }
        return $this->queryByDateRange($query, 'u.createdAt', $request);
    }

    protected function queryTotal(Request $request)
    {
        $query = $this->em->getRepository(User::class)
            ->createQueryBuilder('u')
            ->join('u.teams', 't')
            ->join('u.cards', 'uc')
            ->join('uc.card', 'c')
            ->where(Util::expr()->in('t.name', ':roles'))
            ->andWhere('c.cardProgram = :cardProgram')
            ->setParameter('cardProgram', Util::cardProgram())
            ->setParameter('roles', Bundle::getMemberRoles());
        if (!$this->user->inTeam(Role::ROLE_MASTER_ADMIN)) {
            $query->andWhere(Util::expr()->notIn('u.id', ':hideIds'))
                  ->setParameter('hideIds', TransferMexBundle::hideUsers());
        }
        return $this->queryByDateRange($query, 'u.createdAt', $request);
    }

    protected function queryListParams(QueryBuilder $query, Request $request)
    {
        $filter = $request->get('filter');
        if (isset($filter['uc.status=']) && $filter['uc.status='] === 'active') {
            $filter['uc.issued='] = 1;
            $request->query->set('filter', $filter);
        }

        $params = new QueryListParams($query, $request, 'u', 'count(distinct u)');
        $params->distinct = true;
        $params->orderBy = [
            'u.id' => 'desc',
        ];
        $params->searchFields = [
            'u.id',
            'u.name',
            'u.email',
            'uc.accountNumber',
            'u.title',
            'uc.meta',
        ];
        $params->fixRangeMoney = true;
        return $params;
    }

    /**
     * @param User $entity
     *
     * @return array
     */
    protected function getRowData($entity)
    {
        $uc = $entity->getCurrentPlatformCard();
        $an = $uc ? $uc->getAccountNumber() : null;

        $useBaseRapidAgent = null;
        if ($uc && $this->user->isMasterAdmin()) {
            $group = $entity->getPrimaryGroup();
            if (Util::meta($group, 'rapidAgentNumber')) {
                $useBaseRapidAgent = $uc->isUsingBaseRapidAccount();
            }
        }

        $userGroup = $entity->getPrimaryGroup();
        $employer = $userGroup ? $userGroup->getName() : null;
        $employerFundingType = ($userGroup ? $userGroup->getFundingType() : null) ?? UserGroup::FUNDING_TYPE_ACH;

        $cardStatus = $an && $uc && $uc->isIssued() ? ucfirst($uc->getStatus()) : '';
        $nativeStatus = $uc ? $uc->getNativeStatus() : '';
        if ($nativeStatus && $nativeStatus !== $cardStatus) {
            $cardStatus .= ' (' . $nativeStatus . ')';
        }
        /** @var UserOtherInfo $userOtherInfo */
        $userOtherInfo = UserOtherInfo::findByUser($entity);
        if (!$userOtherInfo) {
          $userOtherInfo = new UserOtherInfo();
        }
        $splashList = Util::meta($entity, 'splashList');
        $splashListArray = [];
        if ($splashList) {
          $splashListArray = Util::em()->getRepository(SplashPage::class)
                  ->createQueryBuilder('sp')
                  ->where(Util::expr()->eq('sp.status', ':status'))
                  ->andWhere(Util::expr()->in('sp.id', ':idList'))
                  ->andWhere(Util::expr()->gt('sp.endTime', ':endTime'))
                  ->setParameter('idList', explode(',', $splashList))
                  ->setParameter('status', SplashPage::STATUS_ACTIVE)
                  ->setParameter('endTime', Carbon::now())
                  ->select('sp.id, sp.title')
                  ->getQuery()
                  ->getArrayResult();
        }
        // $bankTransferInfo = $this->getTransferInfo($entity, 'bank');
        // $cashTransferInfo = $this->getTransferInfo($entity, 'cash');
        // $recordsInfo = Data::getArray('member_report_records_info' . $entity->getId());
        // if (empty($recordsInfo)) {
        //   $recordsInfo = [
        //     'deposit' => [
        //       'total' => 0,
        //       'count' =>0,
        //       'avg' => 0,
        //       'last' => ''
        //     ],
        //     'spend' => [
        //       'total' => 0,
        //       'count' =>0,
        //       'avg' => 0,
        //       'last' => ''
        //     ],
        //     'atm' => [
        //       'total' => 0,
        //       'count' =>0,
        //       'avg' => 0,
        //       'last' => ''
        //     ]
        //   ];
        // }
        // $iosLastLogin = Util::meta($entity, 'iosLastLogin');
        // $androidLastLogin = Util::meta($entity, 'androidLastLogin');
        // $webLastLogin = Util::meta($entity, 'webLastLogin');
        $config = $uc?->getUser()?->getConfig();
        $lastFourSsn = EmployeeSSN::findByUser($entity->getId());
        return [
            'Create Date' => Util::formatDateTime($entity->getCreatedAt()),
            'Employer ID' => $entity->getPrimaryGroupAdmin() ? $entity->getPrimaryGroupAdmin()->getId() : null,
            'Employer' => $employer,
            'Employer Funding Type' => $employerFundingType,
            'Client' => $employer,
            'Client Funding Type' => $employerFundingType,
            'Member ID' => $entity->getId(),
            'External Employee ID' => $entity->getExternalId(),
            'Opt-in Disclaimer' => $entity->getOptInDisclaimer(),
            'First Name' => $entity->getFirstName(),
            'Last Name' => $entity->getLastName(),
            'Sec Last Name' => Util::meta($entity, 'secLastName'),
            'Email' => $entity->getEmail(),
            'Email Address' => $entity->getEmail(),
            'Phone' => $entity->getMobilephone(),
            'Mobile Phone' => $entity->getMobilephone(),
            'Program' => $uc ? $uc->getCardProgram()->getName() : '',
            'Address' => $entity->getSimpleFullAddress(),
            'CountryId' => $entity->getCountryid(),
            'Country' => $entity->getCountryName(),
            'Home Address' => $entity->getAddress(),
            'State/Province' => $entity->getStateName(),
            'City' => $entity->getCity(),
            'Postal Code' => $entity->getZip(),
            'CURP ID' => $entity->getCurpId(),
            'Gov ID (SSN/CURP)' => $entity->getCurpId(),
            'Date of Birth' => '' . Util::formatBirthday($entity->getBirthday(), Util::DATE_FORMAT),
            'Barcode Number' => $uc ? $uc->getAccountNumber() : '',
            'Balance' => $an ? Money::formatWhen($uc->getBalance()) : '',
            'Enrolled' => $an ? ($uc->isIssued() ? 'Yes' : 'No') : '',
            'Card Status' => $cardStatus,
            'Card Native Status' => $nativeStatus,
            'Status' => $entity->getOnboardStatus(),
            'Account Status' => $entity->getOnboardStatus(),
            'kycStatuses' => $entity->getAllKycStatus(),
            'Last Login Time' =>  Util::formatDateTime($entity->getLastLogin()),
            'useBaseRapidAgent' => $useBaseRapidAgent,
            'incomeAndSpend' => TransactionService::getIncomeAndSpend($entity),
            'isCreatedUniTellerUser'   => Util::meta($entity, 'UniTellerUserID'),
            'Season Name'   => Util::meta($entity, 'seasonName'),
            'BOTM User ID' => $config?->getBotmUserId(),
            'BOTM Account ID' => $config?->getBotmUserAccountId(),
            'Legacy Balance' => Money::formatWhen($uc?->getLocalBalance()),
            'Total Of Deposit' => Money::formatWhen($userOtherInfo->getTotalDeposit()),
            'Count Of Deposit' =>  $userOtherInfo->getCountDeposit(),
            'Avg Of Deposit' =>  Money::formatWhen($userOtherInfo->getAvgDeposit()),
            'Last Date Of Deposit' =>   Util::formatDateTime($userOtherInfo->getLastDepositAt()),
            'Total Of Bank Transfers' => Money::formatWhen($userOtherInfo->getTotalBankTransfer()),
            'Count Of Bank Transfers' =>  $userOtherInfo->getCountBankTransfer(),
            'Avg Of Bank Transfers' =>  Money::formatWhen($userOtherInfo->getAvgBankTransfer()),
            'Last Date Of Bank Transfer' => Util::formatDateTime($userOtherInfo->getLastBankAt()),
            'Bank Transfer % of Deposit' => round($userOtherInfo->getBankTransferPercent() / 100, 2) . '%',
            'Total Of Cash Pickups' =>  Money::formatWhen($userOtherInfo->getTotalCashPickup()),
            'Count Of Cash Pickups' => $userOtherInfo->getCountCashPickup(),
            'Avg Of Cash Pickups' =>  Money::formatWhen($userOtherInfo->getAvgCashPickup()),
            'Cash Pickups % of Deposit' => round($userOtherInfo->getCashCickupPercent() / 100, 2) . '%',
            'Last Date Of Cash Pickup' =>  Util::formatDateTime($userOtherInfo->getLastCashPickUpAt()),
            'Total Of Card Spend' =>  Money::formatWhen($userOtherInfo->getTotalSpend()),
            'Count Of Card Spend' => $userOtherInfo->getCountSpend(),
            'Avg Of Card Spend' =>  Money::formatWhen($userOtherInfo->getAvgSpend()),
            'Spend % of Deposit' => round($userOtherInfo->getSpendPercent() / 100, 2) . '%',
            'Last Date Of Spend' => Util::formatDateTime($userOtherInfo->getLastSpendAt()),
            'Total Of ATM W/Ds' =>  Money::formatWhen($userOtherInfo->getTotalAtm()),
            'Count Of ATM W/Ds' => $userOtherInfo->getCountAtm(),
            'Avg Of ATM W/Ds' =>  Money::formatWhen($userOtherInfo->getAvgAtm()),
            'ATM W/Ds % of Deposit' => round($userOtherInfo->getAtmPercent() / 100, 2) . '%',
            'Last Date Of ATM W/Ds' =>  Util::formatDateTime($userOtherInfo->getLastAtmAt()),
            'Android Device ID' => $userOtherInfo->getAndroidDeviceId(),
            'Last Android Login Date' =>  Util::formatDateTime($userOtherInfo->getLastAndroidLogin()),
            'Apple Device ID' =>  $userOtherInfo->getIosDeviceId(),
            'Last Apple Login Date' => Util::formatDateTime($userOtherInfo->getLastIosLogin()),
            'Last Web Login Date' => Util::formatDateTime($userOtherInfo->getLastWebLogin()),
            'currentProcessor' => $uc ? $uc->getCurrentProcessor() : Processor::NAME_RAPID,
            'Tags' => $entity->getTags()->map(function (Tag $tag) {
                return $tag->getName();
            })->toArray(),
            'viewPan' => $uc && $uc->isBotmCard() && (Util::isStaging() || (Util::isDev() && $this->user->isMasterAdmin()) ? true : false),
            'splashList' => $splashListArray,
            'useIntermex' => Util::meta($entity, 'useIntermex'),
            'Last Four SSN' => $lastFourSsn ? $lastFourSsn->getLastFourSsn() : ''
        ];
    }

    protected function getTransferInfo(User $user, $type) {
      $query = $this->em->getRepository(Transfer::class)
                ->createQueryBuilder('t')
                ->join('t.sender', 'u')
                ->where(Util::expr()->eq('u.id', ':userId'))
                ->andWhere(Util::expr()->eq('t.payoutType', ':type'))
                ->andWhere(Util::expr()->in('t.status', ':statusList'))
                ->setParameter('userId', $user->getId())
                ->setParameter('type', $type)
                ->setParameter('statusList', [
                    Transfer::STATUS_CREATED,
                    Transfer::STATUS_COMPLETED,
                    Transfer::STATUS_HOLD,
                    Transfer::STATUS_QUEUED,
                ]);
      $lastOne = $query->orderBy('t.sendAt', 'desc')->select('t.sendAt')
                        ->setMaxResults(1)
                        ->getQuery()
                        ->getResult();
      $total = $query->select('sum(t.sendAmount + t.transferFee)')
              ->getQuery()
              ->getSingleScalarResult();

      $count = $query->select('count(distinct t)')
                      ->getQuery()
                      ->getSingleScalarResult();

      return [
          'total' => $total ?? 0,
          'count' => $count,
          'avg' => Util::percent($total, $count),
          'lastOne' => $lastOne ? $lastOne[0]['sendAt']: null,
      ];
    }

    /**
     * @Route("/admin/mex/members/filters")
     * @return SuccessResponse
     */
    public function filtersAction() {
        return new SuccessResponse([
            'employers' => $this->listForSelection($this->getCurrentEmployerRole()),
            'splashList' => SplashPage::selectList()
        ]);
    }

    protected function validateUser(User $user = null)
    {
        if (!$user || !$user->inTeam($this->getCurrentMemberRole())) {
            throw PortalException::temp('Unknown user!');
        }
    }

    /**
     * @Route("/admin/mex/members/update-kyc-cost")
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function updateKycCost(Request $request)
    {
        $result = [
            'cost' => MemberService::calculateKycCost($this->query($request), true),
            'costNew' => MemberService::calculateKycCostByCostTable($this->query($request), true),
        ];
        return new SuccessResponse($result);
    }

    /**
     * @Route("/admin/mex/members/{uid}/detail")
     * @param $uid
     *
     * @return FailedResponse|SuccessResponse
     */
    public function detailAction($uid)
    {
        $data = [];
        if ($uid) {
            $user = User::find($uid);
            $this->validateUser($user);

            $admin = $user->getPrimaryGroupAdmin();
            $kyc = KycService::getKycStatusFields($user);

            $data['entity'] = array_merge($this->getRowData($user), [
                'Employer' => $admin ? $admin->getId() : null,
                'Street Address' => $user->getAddress(),
                'CountryId' => $user->getCountryid(),
                'StateId' => $user->getStateid(),
                'City' => $user->getCity(),
                'Postal Code' => $user->getZip(),
            ], $kyc);
            $userGroup = $user->getPrimaryGroup();
            if ($userGroup && $userGroup->isBOTM()) {
              $data['entity']['kycStep'] = $kyc['kycStatuses']['OFAC'] === true ? MemberService::STEP_KYC_PASSED : $kyc['kycStep'];
              $data['entity']['kycStatuses'] = ['OFAC' => $kyc['kycStatuses']['OFAC']];
              $data['entity']['isAllKycPassed'] = $kyc['kycStatuses']['OFAC'];
            }
        }
        $data['employers'] = EmployerService::listForSelection($this->getCurrentEmployerRole());

        return new SuccessResponse($data);
    }

    /**
     * @Route("/admin/mex/members/save", methods={"POST"})
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function saveAction(Request $request)
    {
        $uid = $this->getPostData('Member ID');
        $email = $this->getPostData('Email');
        $newUser = false;
        $oldEmail = null;
        $oldPhone = null;
        if ($uid) {
            $user = User::find($uid);
            $this->validateUser($user);

            $oldEmail = $user->getEmail();
            $oldPhone = $user->getMobilephone();
        } else {
            $user = User::findPlatformUserByEmail($email, Bundle::getMemberRoles());
            if ($user) {
                throw PortalException::temp('Duplicated accounts with the same email address!');
            }
            $newUser = true;
            $user = new User();
            $user->setEnabled(true)
                ->setPlainPassword(Util::generatePassword())
                ->setStatus(User::STATUS_ACTIVE);
        }

        $country = Country::find($this->getPostData('CountryId'));

        $user->setFirstName($this->getPostData('First Name'))
            ->setLastName($this->getPostData('Last Name'))
            ->changeMobilePhone(Util::inputPhone($this->getPostData('Phone'), $country), Bundle::getMemberRoles())
            ->setBirthday(Util::toUTC(Util::timeLocal($this->getPostData('Date of Birth'))))
            ->setAddress($this->getPostData('Street Address'))
            ->setCountry($country)
            ->setStateid($this->getPostData('StateId'))
            ->setCity($this->getPostData('City'))
            ->setZip($this->getPostData('Postal Code'))
            ->changeTransferMexCurpId($this->getPostData('CURP ID'))
            ->changeEmail($email, Bundle::getMemberRoles())
            ->ensureRole(Bundle::getMemberRoles())
            ->persist();

        $user->setOptInDisclaimer($this->getPostData('Opt-in Disclaimer'))
            ->setExternalId($this->getPostData('External Employee ID') ?? $user->getExternalId())
            ->persist();

        if ($newUser) {
            $cardType = CardProgramCardType::getForCardProgram(Util::cardProgram());
            $uc = CardService::create($user, $cardType);

            if (!Util::isLive()) {
            	$sandbox = $this->platform->isTransferMex() ? 'transferMexSandbox' : 'faasSandbox';
            	$sandboxData[$sandbox] = true;
                Util::updateMeta($uc, $sandboxData);
            }
            // disable Rapyd and enable intermexBank
            Util::updateMeta($user, [
              'disableRapydBank' => true,
              'disableIntermexBank' => false,
              'useIntermex' => true
            ]);
        } else {
            $reason = $this->getPostData('changeReason');
            $suffix = '';
            if ($reason) {
                $suffix = ' with reason "' . $reason . '"';
            }

            if ($oldEmail !== $email) {
                $user->addNote('Changed the email from ' . $oldEmail . ' to: ' . $email . $suffix,
                    TRUE, $this->user->getId());
            }
            if ($oldPhone !== $user->getMobilephone()) {
                $user->addNote('Changed the mobile phone number from ' . $oldPhone . ' to ' . $user->getMobilephone() . $suffix,
                    TRUE, $this->user->getId());
            }
        }
        $oldEmployer = $user->getPrimaryGroupAdmin();
        $employer = User::find($this->getPostData('Employer'));
        MemberService::ensureInEmployer($user, $employer);

        MemberService::updateOnboardStatus($user);
        $user->ensureConfig();
        ProcessorHub::ensureProcessorUserSilently($user);
        $secLastName = $this->getPostData('Sec Last Name');
        Util::updateMeta($user, [
          'secLastName' => $secLastName
        ]);
        if (!$newUser) {
            Service::sendAsync('/t/cron/faas/processor/update-consumer-info/' . $user->getId());
        }
        $ssnRecord = EmployeeSSN::findByUser($user->getId());
        if (!$ssnRecord) {
          $ssnRecord = new EmployeeSSN();
        }
        $ssnRecord->setLastFourSsn($this->getPostData('Last Four SSN'))
                  ->setEmployee($user)
                  ->setEmployer($employer);
        if ($oldEmployer != $employer) {
          $ssnRecord->setLastEmployer($oldEmployer);
        }
        $ssnRecord->persist();
        if (Util::meta($user, 'UniTellerUserID')) {
          try {
            UniTellerRemittanceService::editUserProfile($user);
          } catch (\Exception $e){
            return new FailedResponse($e->getMessage());
          }
        }
        return new SuccessResponse($this->getRowData($user));
    }

    /**
     * @Route("/admin/mex/members/{user}/change-status", methods={"POST"})
     * @param Request $request
     * @param User    $user
     *
     * @return SuccessResponse
     */
    public function changeStatusAction(Request $request, User $user)
    {
        $this->validateUser($user);
        $uc = $user->getCurrentPlatformCard();

        $reason = $this->getPostData('reason', 'Unknown reason');
        $status = $this->getPostData('status');

        // Update local account status/uc first.
        Util::updateMeta($user, [
            'changeStatusReason' => $reason,
        ], false);
        $user->addNote('Changed Member Status to "' . $status . '". Reason: ' . $reason, true, $this->user->getId());

        if ($status === MemberService::STEP_ON_HOLD) {
            $user->setStatus(User::STATUS_INACTIVE);
            $uc->setStatus(UserCard::STATUS_INACTIVE, true, false);
        } else if ($status === MemberService::STEP_CLOSED) {
            $user->setStatus(User::STATUS_CLOSED);
            $uc->setStatus(UserCard::STATUS_INACTIVE, true, false);
        } else if ($status === MemberService::STEP_ACTIVE) {
            $user->setStatus(User::STATUS_ACTIVE);
            $uc->setStatus(UserCard::STATUS_ACTIVE, true, false);
        }
        $em = Util::em();
        $em->persist($user);
        $em->persist($uc);
        $em->flush();

        if ($uc->isBotmCard()) {
            $api = BotmAPI::getForUserCard($uc);
            ExternalInvoke::host($api->updateUserAccountStatus($user, $user->isActive()));
        }

        MemberService::updateOnboardStatus($user);

        // Update card status next
        $oldCardStatus = $uc->getNativeStatus();
        $newCardStatus = null;
        if (in_array($status, [
            MemberService::STEP_ON_HOLD,
            MemberService::STEP_CLOSED,
        ])) {
            if ($uc->isBotmCard()) {
                if (BotmAPI::isCardStatusOnHoldable($oldCardStatus)) {
                    $newCardStatus = BotmAPI::CARD_STATUS_PAUSED;
                }
            } else if (RapidAPI::isCardStatusOnHoldable($oldCardStatus)) {
                $newCardStatus = RapidAPI::CARD_STATUS_ON_HOLD;
            }
        } else if ($status === MemberService::STEP_ACTIVE) {
            if ($uc->isBotmCard()) {
                if (!$oldCardStatus || in_array($oldCardStatus, [
                    BotmAPI::CARD_STATUS_PAUSED,
                ])) {
                    $newCardStatus = BotmAPI::CARD_STATUS_ACTIVE;
                }
            } else if (!$oldCardStatus || in_array($oldCardStatus, [
                RapidAPI::CARD_STATUS_SUSPENDED,
                RapidAPI::CARD_STATUS_ON_HOLD,
            ])) {
                $newCardStatus = RapidAPI::CARD_STATUS_ACTIVE;
            }
        }

        if ($newCardStatus && $uc->getAccountNumber() && $uc->isIssued()) {
            $api = $uc->getProcessorApiImpl();
            /** @var ExternalInvoke $ei */
            [$ei, ] = ExternalInvoke::mute(function () use ($api, $uc, $newCardStatus) {
                return $api->updateStatus($uc, $newCardStatus);
            });
            if ($ei->isFailed()) {
                if ($api instanceof RapidAPI && RapidAPI::isDisabled()) {
                    Log::debug($uc->getAccountNumber() . ' : Failed to update account status: ' .
                               $ei->getErrorSignature());
                } else {
                    SlackService::$channel = 'client';
                    SlackService::alert('Failed to update account status: ' . $ei->getErrorSignature(), [
                        'member' => $user->getSignature(),
                        'account_number' => $uc->getAccountNumber(),
                        'user_status' => $status,
                        'old_status' => $oldCardStatus,
                        'new_status' => $newCardStatus,
                    ], SlackService::MENTION_HANS);
                }
            }

            RapidService::updateBalanceAndStatusSilently($uc);
        }

        $newCardStatus = $uc->getNativeStatus();
        if ($newCardStatus !== $oldCardStatus) {
            $an = $uc->getSignature();
            $user->addNote('Changed the card ' . $an . ' status from ' . $oldCardStatus . ' to ' . $newCardStatus,
                TRUE, $this->user->getId());
        }

        ProcessorMemberService::pauseAllLegacyCards($uc);

        $message = '';
        if ($uc->getAccountNumber() && $status === MemberService::STEP_ACTIVE && !in_array($newCardStatus, [
            null,
            '',
            RapidAPI::CARD_STATUS_ACTIVE,
            BotmAPI::CARD_STATUS_ACTIVE,
        ])) {
            $message = 'Updated the account status to Active. The card status is `'
                       . $newCardStatus . '` now. Please change it on the profile page manually if needed.';
        }
        // update user card active time
        $uc->setActivityTime(Carbon::now())->persist();
        // remove the on hold flag
        if (Data::get('card_on_hold_by_password_' . $user->getId())) {
          Data::del('card_on_hold_by_password_' . $user->getId());
        }
        return new SuccessResponse($status, $message);
    }

    /**
     * @Route("/admin/mex/members/0/multi/change-card-status", methods={"POST"})
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function changeMultipleCardStatusAction(Request $request)
    {
        $status = $this->getPostData('cardStatus');
        if (!$status) {
            return new FailedResponse('Unknown status!');
        }

        $ids = $this->getPostData('ids');
        if (empty($ids)) {
            return new FailedResponse('Empty users!');
        }

        Service::sendAsync('/t/cron/mex/members/0/multi/change-card-status?cardStatus='
                           . urlencode($status) . '&ids=' . implode(',', $ids), true);
        return new SuccessResponse(null, 'Submitted the request. Please monitor the Slack alerts and also check the list later.');
    }

    /**
     * @Route("/admin/mex/members/0/multi/send-message", methods={"POST"})
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function changeMultipleMessageAction(Request $request)
    {
        $content = $this->getPostData('content');
        if (!$content) {
            return new FailedResponse('Unknown message content!');
        }

        $ids = $this->getPostData('ids');
        $count = count($ids);
        if ($count <= 0) {
            return new FailedResponse('Empty users!');
        }
        if ($count > 1000) {
            return new FailedResponse('You can send messages to at most 1000 users in each batch!');
        }

        Service::sendAsync('/t/cron/mex/members/0/multi/send-message?content='
                           . urlencode($content) . '&ids=' . implode(',', $ids), true);
        return new SuccessResponse(null, 'Submitted the request. The message is being sent in the background. A Slack alert will be sent once it\'s done.');
    }

    /**
     * @Route("/admin/mex/members/{user}/id-ofac", methods={"POST"})
     * @param Request $request
     * @param User    $user
     *
     * @return SuccessResponse
     */
    public function idOfacAction(Request $request, User $user)
    {
        $this->validateUser($user);
        $this->doOfacOnUser($user);
        $kycInfo = KycService::getKycStatusFields($user);
        $userGroup = $user->getPrimaryGroup();
        if ($userGroup && $userGroup->isBOTM()) {
          $kycInfo['kycStep'] = $kycInfo['kycStatuses']['OFAC'] === true ? MemberService::STEP_KYC_PASSED : $kycInfo['kycStep'];
          $kycInfo['isAllKycPassed'] = $kycInfo['kycStatuses']['OFAC'];
          $kycInfo['kycStatuses'] = ['OFAC' => $kycInfo['kycStatuses']['OFAC']];
        }
        return new SuccessResponse($kycInfo);
    }

    private function doOfacOnUser(User $user)
    {
        IDologyService::ofac($user);
        MemberService::updateOnboardStatus($user);
        ProcessorHub::ensureProcessorUserSilently($user);
    }

    /**
     * @Route("/admin/mex/members/{user}/id-verify", methods={"POST"})
     * @param Request $request
     * @param User    $user
     *
     * @return SuccessResponse
     */
    public function idVerifyAction(Request $request, User $user)
    {
        $this->validateUser($user);
        return $this->uploadVerify($request, $user);
    }

    /**
     * @Route("/admin/mex/members/{user}/id-manual", methods={"POST"})
     * @param Request $request
     * @param User    $user
     *
     * @return SuccessResponse
     */
    public function idManualApproveAction(Request $request, User $user)
    {
        $this->validateUser($user);

        $meta = [
            'idManualApprovalChecks' => $this->getPostData('checks'),
            'idManualApprovalSignature' => $this->getPostData('signature'),
            'idManualApprover' => $this->user->getId(),
            'idManualApprovedAt' => Carbon::now()->format('c'),
        ];
        if ($user->getOfacStatus() !== true) {
            $meta['ofacManual'] = true;
        }
        if ($user->getIdScanStatus() !== true) {
            $meta['idScanManual'] = true;
        }
        Util::updateMeta($user, $meta);

        MemberService::updateOnboardStatus($user);
        return new SuccessResponse($user->getAllKycStatus());
    }

    /**
     * @Route("/admin/mex/members/{user}/id-download")
     * @param Request $request
     * @param User    $user
     *
     * @return FailedResponse|SuccessResponse
     */
    public function idDownloadAction(Request $request, User $user)
    {
        $uiv = $user->getIdVerify();
        if (!$uiv) {
            $suffix = Util::meta($user, 'idScanManual') ? 'The KYC was manually approved.' : '';
            return new FailedResponse('No KYC Documents for this member. ' . $suffix);
        }
        $front = $uiv->frontImage();
        $back = $uiv->backImage();
        if (!$front && !$back) {
            return new FailedResponse('Empty KYC Documents for this member.');
        }
        $files = [];
        if ($front) {
            $files[] = $front->getUrl();
        }
        if ($back) {
            $files[] = $back->getUrl();
        }
        return new SuccessResponse($files);
    }

    /**
     * @Route("/admin/mex/members/{user}/assign", methods={"POST"})
     * @param Request $request
     * @param User    $user
     *
     * @return FailedResponse|SuccessResponse
     */
    public function assignAction(Request $request, User $user)
    {
        return $this->traitAssign($request, $user);
    }

    /**
     * @Route("/admin/mex/members/{user}/replace", methods={"POST"})
     * @param Request $request
     * @param User    $user
     *
     * @return FailedResponse|SuccessResponse
     */
    public function replaceAction(Request $request, User $user)
    {
        return $this->traitReplace($request, $user);
    }

    /**
     * @Route("/admin/mex/members/{user}/enroll", methods={"POST"})
     * @param Request $request
     * @param User    $user
     *
     * @return FailedResponse|SuccessResponse
     */
    public function enrollAction(Request $request, User $user)
    {
        return $this->traitEnroll($request, $user);
    }

    /**
     * @Route("/admin/mex/members/{user}/reassign", methods={"POST"})
     * @param Request $request
     * @param User    $user
     *
     * @return FailedResponse|SuccessResponse
     */
    public function reassignAction(Request $request, User $user)
    {
        $this->validateUser($user);

        try {
            $uc = $user->getCurrentPlatformCard();
            MemberService::reassign($uc);
        } catch (PortalException $pe) {
            return new FailedResponse($pe->getMessage());
        }

        return new SuccessResponse();
    }

    /**
     * @Route("/admin/mex/members/import", methods={"POST"})
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function importAction(Request $request)
    {
        $aid = $this->getPostData('attachment');
        if (!$aid) {
            return new FailedResponse('Invalid parameters!');
        }
        $attachment = Attachment::find($aid);
        $category = $this->platform->isTransferMex() ? 'transferMexMembers' : 'faasMembers';
        if (!$attachment || $attachment->getCategory() !== $category) {
            return new FailedResponse('Unknown or invalid file!');
        }
        $path = $attachment->prepareForLocalRead();
        if (!Util::endsWith(strtolower($path), '.xlsx')) {
            return new FailedResponse('Only XLSX file is supported!');
        }

        Util::longRequest();

        $users = MemberService::importFromCsvFile($path);
        if (is_string($users) && $users === 'cronJob') {
          $last = MemberService::getLastImport($this->user, Util::cardProgram());
          if ($last && $last['status'] === 'In Progress') {
            return new FailedResponse('A file is being processed');
          }
          // store file path for cron and start cron job
          if (ImportMembersRecord::findByfileName($attachment->getName())) {
            return new FailedResponse('The name ' . $attachment->getName(). ' file has been imported before');
          }
          // store file path for cron and start cron job
          $importRecord = new ImportMembersRecord();
          $importRecord->setPath($path)
                        ->setCardProgram(Util::cardProgram())
                        ->setEmployer($this->user)
                        ->setFailedCount(0)
                        ->setSuccessCount(0)
                        ->setPlatform($this->platform)
                        ->setFileName($attachment->getName())
                        ->setStatus(ImportMembersRecord::IMPORT_INIT)
                        ->persist();
          // Service::sendAsync('/t/cron/mex/import');
          Service::sendAsync('/t/cron/mex/import/members/' . $importRecord->getId());
          return new SuccessResponse('Import in processing');
        } else {
          $importRecord = new ImportMembersRecord();
          $importRecord->setPath($path)
                        ->setCardProgram(Util::cardProgram())
                        ->setEmployer($this->user)
                        ->setFailedCount(0)
                        ->setPlatform($this->platform)
                        ->setFileName($attachment->getName())
                        ->setSuccessCount(count($users))
                        ->setStatus(ImportMembersRecord::IMPORT_COMPLETE)
                        ->persist();
        }

        /** @var User $user */
        foreach ($users as $user) {
            try {
                $this->doOfacOnUser($user);
            } catch (\Exception $exception) {
                Log::warn('Failed to verify OFAC: ' . $exception->getMessage(), [
                    'user' => $user->getId(),
                ]);
            }
        }

        return new SuccessResponse();
    }

    /**
     * @Route("/admin/mex/members/{user}/del", methods={"POST"})
     * @param User    $user
     *
     * @return FailedResponse|SuccessResponse
     */
    public function del(User $user)
    {
        $this->validateUser($user);

        $uc = $user->getCurrentPlatformCard();
        if ($uc && $uc->getAccountNumber()) {
            return new FailedResponse('There is already a card assigned to this member. Failed to delete!');
        }

        $user->setDeletedAt(Carbon::now())
            ->persist();

        return new SuccessResponse();
    }

    protected function validatePinToken(Request $request, User $user)
    {
        $this->validatePinTokenCode($request);

        if ($user->getOnboardStatus() !== 'Active') {
            throw new FailedException('This member is not active!');
        }

        $uc = $user->getCurrentPlatformCard();
        if (!$uc || !$uc->getAccountNumber()) {
            throw new FailedException('Unknown card!');
        }
        return $uc;
    }

    /**
     * @Route("/admin/mex/members/{user}/deposit-info", methods={"POST"})
     * @param Request $request
     * @param User    $user
     *
     * @return FailedResponse|SuccessResponse
     */
    public function depositInfo(Request $request, User $user)
    {
        $uc = $user->getCurrentPlatformCard();
//        $uc = $this->validatePinToken($request, $user);
        $uc->addDepositInfoViewLog($this->user);
        $api = $uc->getProcessorApiImpl();
        return new SuccessResponse([
            'Account Number' => $api->getDirectDepositNumber($uc),
            'Routing Number' => $api->getRoutingNumber($uc),
            'logs' => $uc->getDepositInfoViewLogs(),
        ]);
    }

    /**
     * @Route("/admin/mex/members/{user}/update-rapid-account", methods={"POST"})
     * @param Request $request
     * @param User    $user
     *
     * @return FailedResponse|SuccessResponse
     */
    public function updateRapidAccount(Request $request, User $user)
    {
        $uc = $user->getCurrentPlatformCard();
        $type = $this->getPostData('type');
        Util::updateMeta($uc, [
            'useBaseRapidAgent' => $type === 'base',
        ]);

        return new SuccessResponse();
    }

    /**
     * @Route("/admin/mex/members/{user}/invalidate-session", methods={"POST"})
     * @param Request $request
     * @param User    $user
     *
     * @return SuccessResponse
     */
    public function invalidateSession(Request $request, User $user)
    {
        $this->validateUser($user);

        $uts = $user->getTokens();
        foreach ($uts as $ut) {
            $this->em->remove($ut);
        }
        $this->em->flush();

        return new SuccessResponse();
    }

    /**
     * @Route("/admin/mex/members/{user}/reset-passcode", methods={"POST"})
     * @param Request $request
     * @param User    $user
     *
     * @return SuccessResponse
     */
    public function resetPasscode(Request $request, User $user)
    {
        $this->validateUser($user);

        Util::updateMeta($user, 'passcode');

        $uts = $user->getTokens();
        foreach ($uts as $ut) {
            $this->em->remove($ut);
        }
        $this->em->flush();

        return new SuccessResponse();
    }

    /**
     * @Route("/admin/mex/members/{user}/change-pin", methods={"POST"})
     * @param Request $request
     * @param User    $user
     *
     * @return FailedResponse|SuccessResponse
     */
    public function changePin(Request $request, User $user)
    {
        $uc = $user->getCurrentPlatformCard();
//        $uc = $this->validatePinToken($request, $user);
        $newPin = $this->getPostData('pin');
        if (!$newPin || strlen($newPin) !== 4) {
            return new FailedResponse('Invalid PIN!');
        }

        if (Util::checkCardPin($user, $newPin)) {
          return new FailedResponse(Util::checkCardPin($user, $newPin));
        }

        $pan = $this->getPostData('fullCardNumber');
        ProcessorHub::validateFullNumberPan($pan);

        $an = $this->getPostData('accountNumber') ?? $uc->getAccountNumber();
        $tempUc = ProcessorHub::getTempUserCard($uc, $an);
        $api = ProcessorHub::getForUserCardAccountNumber($tempUc, $an);
        $api->validateFullCardNumberWithCards($tempUc, $pan, 'changing PIN by admin');
        if ($api instanceof BotmAPI) {
            $fullPan = BotmService::changePin($tempUc, $newPin, $pan);
        } elseif ($api instanceof RapidAPI) {
            $fullPan = RapidService::changePin($tempUc, $newPin, $pan);
        } else {
            return new FailedResponse('Unsupported card provider to change PIN');
        }
        Util::storeCardPin($user, $newPin);

        $user->addNote('Changed the PIN of the card ending with ' . Util::last2($fullPan) . ' under account ' . $an,
            TRUE, $this->user->getId());

        return new SuccessResponse();
    }

    /**
     * @Route("/admin/mex/members/{user}/prepare-pin")
     * @param Request $request
     * @param User    $user
     *
     * @return FailedResponse|SuccessResponse
     */
    public function preparePin(Request $request, User $user)
    {
        $uc = $user->getCurrentPlatformCard();
//        $uc = $this->validatePinToken($request, $user);

        $number = '****';
        $pan = $uc->getPan() ?: $uc->getDbaNo();
        $an = $request->get('accountNumber');
        if ($an && $an !== $uc->getAccountNumber()) {
            $pan = null;
        }

        if ($pan) {
            $number = '**' . substr($pan, -2);
        } else {
            try {
                $api = ProcessorHub::getForUserCardAccountNumber($uc, $an);
                $cards = $api->getCardDetailsData($uc);
                $lastCard = $cards[count($cards) - 1] ?? null;
                if ($lastCard) {
                    $masked = $lastCard['maskedCardNumber'] ?? null;
                    if ($masked) {
                        if (!$an || $an === $uc->getAccountNumber()) {
                            $uc->setPan(Util::maskPan($masked))
                                ->persist();
                        }
                        $number = '**' . substr($masked, -2);
                    }
                }
            } catch (\Exception $ex) {
                Log::debug('Failed to get the card details: ' . $ex->getMessage(), [
                    'uc' => $uc->getId(),
                ]);
            }
        }

        return new SuccessResponse([
            'Full Card Number' => $number,
        ]);
    }

    /**
     * @Route("/admin/mex/members/{user}/load-unload", methods={"POST"})
     * @param Request $request
     * @param User    $user
     *
     * @return FailedResponse|SuccessResponse
     */
    public function loadUnload(Request $request, User $user)
    {
        if ($this->platform->isFaasPlatforms()) {
          $uc = $user->getCurrentPlatformCard();
          if (!$uc->getAccountNumber()) {
            Data::set('create_uc_' . $uc->getId(). '_by', $this->user->getId());
            RapidService::createCard($uc);
          }
          // if ($uc->getAccountNumber() && !$uc->getIssued()) {
          //   RapidService::enroll($uc);
          // }
          if ($user->inTeam(Role::ROLE_FAAS_MEMBER)) {
            FaasMemberService::updateOnboardStatus($user);
          }
          $group = $user->getAdminGroup();
          $isReload = $group ?  $group->getMemberLoadType() : false;
          $type = $this->getPostData('type');
          $str = $this->platform->isFaasPlatforms() ? 'Member' : 'Employee';
          $lastPayout = $this->em->getRepository(UserCardTransaction::class)
                      ->createQueryBuilder('uct')
                      ->where(Util::expr()->eq('uct.tranCode', ':type'))
                      ->andwhere(Util::expr()->in('uct.accountStatus', ':status'))
                      ->andwhere(Util::expr()->like('uct.tranDesc', ':employeeId'))
                      ->setParameter('type', 'Pay_employee')
                      ->setParameter('status', [EmployerPayout::PAYOUT_SYNC_PENDING, EmployerPayout::PAYOUT_COMPLETE])
                      ->setParameter('employeeId', '%'. $str. ':' . $user->getId() . '%')
                      ->orderBy('uct.txnTime', 'desc')
                      ->setMaxResults(1)
                      ->getQuery()
                      ->getOneOrNullResult();
          if ($this->platform->isLoadConfigureEnabled() && !$isReload && $lastPayout && $uc->getAccountNumber() && $uc->getIssued()) {
            return new FailedResponse('This card is a single-load, non-reloadable card, and the user has activated it!');
          }
        } else if ($this->platform->isTransferMex()) {
          if (!$this->user->inTeams([Role::ROLE_MASTER_ADMIN, Role::ROLE_TRANSFER_MEX_ADMIN]) || ($this->user->inTeam(Role::ROLE_TRANSFER_MEX_ADMIN) && $this->getPostData('type') == 'load')) {
            return new FailedResponse('No permission!');
          }
        }
        return $this->traitLoadUnload($request, $user, $this->platform->isFaasPlatforms());
    }

    /**
     * @Route("/admin/mex/members/batch-update", methods={"POST"})
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function batchUpdate(Request $request)
    {
        $aid = $this->getPostData('attachment');
        if (!$aid) {
            return new FailedResponse('Invalid parameters!');
        }
        $attachment = Attachment::find($aid);
        if (!$attachment) {
            return new FailedResponse('Unknown or invalid file!');
        }
        $path = $attachment->prepareForLocalRead();
        if (!Util::endsWith(strtolower($path), '.xlsx')) {
            return new FailedResponse('Only XLSX file is supported!');
        }

        Util::longRequest();
        $updated = BatchUpdateService::run($path);
        return new SuccessResponse($updated);
    }

    /**
     * @Route("/admin/mex/members/batch-move", methods={"POST"})
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function batchMove(Request $request)
    {
        $eid = $this->getPostData('employer');
        if (!$eid) {
            return new FailedResponse('Invalid employer!');
        }
        $employer = $this->em->getRepository(User::class)
            ->find($eid);
        if (!$employer) {
            return new FailedResponse('Unknown employer ' . $eid . '!');
        }
        $memIds = $this->getPostData('members');
        if (!$memIds) {
            return new FailedResponse('No selected members to move!');
        }
        $expr = $this->em->getExpressionBuilder();
        $q = $this->em->getRepository(User::class)
            ->createQueryBuilder('u')
            ->where($expr->in('u.id', ':ids'))
            ->setParameter('ids', $memIds);
        $rs = $this->queryMembersInCurrentPlatform($q)
            ->getQuery()
            ->getResult();
        /** @var User $r */
        foreach ($rs as $r) {
            MemberService::ensureInEmployer($r, $employer, false);
        }
        $this->em->flush();
        return new SuccessResponse();
    }

    /**
     * @Route("/admin/mex/members/favorites", methods={"GET"})
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function getFavorites(Request $request)
    {
        $favorites = Config::array(TransferMexBundle::CONFIG_FAVORITE_MEMBERS);
        return new SuccessResponse($favorites);
    }

    /**
     * @Route("/admin/mex/members/save-favorites", methods={"POST"})
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function addToFavorites(Request $request)
    {
        $memIds = $this->getPostData('ids');
        if (!$memIds) {
            return new FailedResponse('No selected members to move!');
        }
        $name = $this->getPostData('name', 'Untitled');
        $favorites = Config::array(TransferMexBundle::CONFIG_FAVORITE_MEMBERS);
        $favorites[Util::randTimeNumber()] = [
            'name' => $name,
            'members' => $memIds,
            'user' => $this->user->getSignature(),
            'time' => Carbon::now()->format('c'),
        ];
        Config::set(TransferMexBundle::CONFIG_FAVORITE_MEMBERS, $favorites);
        return new SuccessResponse();
    }

    /**
     * @Route("/admin/mex/members/remove-favorite/{id}", methods={"POST"})
     * @param Request $request
     * @param         $id
     *
     * @return SuccessResponse
     */
    public function removeFavorite(Request $request, $id)
    {
        $favorites = Config::array(TransferMexBundle::CONFIG_FAVORITE_MEMBERS);
        unset($favorites[$id]);
        Config::set(TransferMexBundle::CONFIG_FAVORITE_MEMBERS, $favorites);
        return new SuccessResponse();
    }

    /**
     * @Route("/admin/mex/members/precheck-card/{barcode}", methods={"POST"})
     * @param Request $request
     * @param string  $barcode
     *
     * @return FailedResponse|SuccessResponse
     * @throws FailedException
     */
    public function precheckCard(Request $request, string $barcode)
    {
        if (!BotmAPI::isAccountNumberValid($barcode)) {
            return new FailedResponse('Invalid barcode number!');
        }
        $api = new BotmAPI();
        $data = ExternalInvoke::host($api->retrieveCardRaw($barcode));
        $accountId = $data['user_account_id'] ?? null;
        $result = [
            'Assigned' => $accountId ? ('Yes, account ID: ' . $accountId) : 'No',
            'Cardholder' => trim(($data['first_name'] ?? '') . ' ' . ($data['last_name'] ?? '')),
            'Card Status' => $data['card_status'] ?? '',
            'Created At' => $data['created_at'] ?? '',
            'Activation Date' => $data['activation_date'] ?? '',
        ];
        if ($accountId) {
            $a = ExternalInvoke::host($api->retrieveUserAccountById($accountId));
            $result['Account Name'] = $a['name'] ?? '';
            $result['Account Balance'] = Money::formatWhen($a['available_balance'] ?? null);
            $result['Account Status'] = $a['status_name'] ?? '';
            $result['Business Name'] = $a['dba_business_name'] ?? null;

            $user = BotmService::findUserByAccountId($accountId);
            if ($user) {
                $result['DB User'] = $user->getSignature();
            }
        }

        return new SuccessResponse($result);
    }

    /**
     * @Route("/admin/mex/members/export", methods={"POST"})
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function export(Request $request)
    {
        $title = 'TransferMex';
        if ($this->platform->isFaasPlatforms()) {
          $title = $this->cardProgram->getName();
        }
        $type = $request->get('query_type', 'all');
        if ($type == 'personal') {
            return $this->commonExport($request, $title . '  Members Personal', [
              'Member ID'         => 12,
              'First Name'        => 20,
              'Last Name'         => 20,
              'Employer ID'       => 20,
              'Date of Birth'     => 20,
              'CURP ID'           => 20,
              'Last Four SSN'     => 20,
              'Home Address'      => 30,
              'City'              => 20,
              'State/Province'    => 20,
              'Postal Code'       => 20,
              'Country'           => 20,
              'Email Address'     => 20,
              'Mobile Phone'      => 20,
              'External Employee ID' => 20
            ]);
        }
        return $this->commonExport($request, $title . ' Members', [
            'Create Date'       => 20,
            'Member ID'         => 12,
            'First Name'        => 20,
            'Last Name'         => 20,
            'Email'             => 40,
            'Phone'             => 20,
            'External Employee ID' => 40,
            'Opt-in Disclaimer' => 20,
            'Program'           => 30,
            'Address'           => 60,
            'Gov ID (SSN/CURP)' => 25,
            'Last Four SSN'     => 20,
            'Barcode Number'    => 20,
            'Balance'           => 20,
            'Enrolled'          => 20,
            'Card Status'       => 15,
            'Status'            => 20,
            'BOTM User ID'      => 20,
            'BOTM Account ID'   => 20,
            'Legacy Balance'    => 20,
            'Last Login Time'   => 20,
            'Total Of Deposit' => 20,
            'Count Of Deposit' =>  20,
            'Avg Of Deposit' => 20,
            'Last Date Of Deposit' =>  20,
            'Total Of Bank Transfers' => 20,
            'Count Of Bank Transfers' => 20,
            'Avg Of Bank Transfers' => 20,
            'Last Date Of Bank Transfer' => 20,
            'Bank Transfer % of Deposit' => 20,
            'Total Of Cash Pickups' => 20,
            'Count Of Cash Pickups' => 20,
            'Avg Of Cash Pickups' => 20,
            'Cash Pickups % of Deposit' =>20,
            'Last Date Of Cash Pickup' => 20,
            'Total Of Card Spend' => 20,
            'Count Of Card Spend' => 20,
            'Avg Of Card Spend' => 20,
            'Spend % of Deposit' => 20,
            'Last Date Of Spend' => 20,
            'Total Of ATM W/Ds' => 20,
            'Count Of ATM W/Ds' => 20,
            'Avg Of ATM W/Ds' => 20,
            'ATM W/Ds % of Deposit' => 20,
            'Last Date Of ATM W/Ds' => 20,
            'Android Device ID' => 20,
            'Last Android Login Date' => 20,
            'Apple Device ID' => 20,
            'Last Apple Login Date' => 20,
            'Last Web Login Date' => 20
        ], [5, 8, 9]);
    }

    /**
     * @Route("/admin/mex/members/getQuestions", methods={"GET"})
     * @param Request $request
     * @param int     $page
     * @param int     $limit
     *
     * @return SuccessResponse
     */
    public function getQuestions(Request $request, $page = 1, $limit = 10)
    {
        $res = UniTellerRemittanceService::securityQuestions();
        return new SuccessResponse($res);
    }

    /**
     * @Route("/admin/mex/members/{user}/createUniTiller", methods={"POST"})
     * @param Request $request
     * @param User    $user
     *
     * @return FailedResponse|SuccessResponse
     */
    public function createUniTiller(Request $request, User $user)
    {
        $this->validateUser($user);

        // check user
        if (UniTellerRemittanceService::checkDupllcateUser($user)) {
          return new FailedResponse('The member has registed UniTeller');
        }

        $answer = [
          'id' => $this->getPostData('questionId'),
          'answer' => $this->getPostData('answer'),
          'answerHint' => $this->getPostData('answerHint')
        ];
        /** @var ExternalInvoke $ei */
        [$ei, $res] = UniTellerRemittanceService::registerUniTeller($user, $answer);

        if ($ei->isFailed()) {
          throw PortalException::temp($ei->getError());
        }

        Util::updateMeta($user, [
            'UniTellerUserID' => $user->getId() . $user->getEmail(),
            'UniTellerUserQuestion' => $answer
          ]);
       return new SuccessResponse('Create successfull');
    }

    /**
     * @Route("/admin/mex/member/{user}/set-season-name", methods={"POST"})
     * @param Request $request
     * @param User    $user
     *
     * @return SuccessResponse
     */
    public function setSeasonName(Request $request, User $user)
    {
        $seasonName = $this->getPostData('seasonName');
        Util::updateMeta($user, [
          'seasonName' => $seasonName
        ]);
        return new SuccessResponse();
    }

     /**
     * @Route("/admin/mex/members/batch-setting-seasonName", methods={"POST"})
     * @param Request $request
     * @param User    $user
     *
     * @return SuccessResponse
     */
    public function batchSettingSeasonName(Request $request) {
      $seasonName = $this->getPostData('seasonName');
      if (!$seasonName) {
          return new FailedResponse('Please input the Season Name!');
      }
      $memIds = $this->getPostData('members');
      $attachmentId = $this->getPostData('attachmentId');
      if (!$memIds && !$attachmentId) {
          return new FailedResponse('No selected members to set!');
      }

      if ($attachmentId) {
        $attachment = Attachment::find($attachmentId);
        $category =  'transferMexMemberBatchActions';
        if (!$attachment || $attachment->getCategory() !== $category) {
          return new FailedResponse('Unknown or invalid file!');
        }
        $memIds = MemberService::getMemberIdsFromFile($attachment->getPath());
      }
      if (!$memIds) {
        return new FailedResponse('No selected members to set!');
      }

      if (Data::get('batch_setting_season_name_doing')) {
        return new FailedResponse('A update is running, please try latter!');
      }
      if (count($memIds) > 20) {
        Data::setArray('batch_setting_season_name', $memIds);
        Data::set('batch_setting_season_name_doing', true);
        InstantBackgroundCommand::add('span:mex:member-batch-setting-season-name', [
          '--seasonName' => $seasonName
        ]);
        return new SuccessResponse('Updating in processing');
      }
      /** @var User $r */
      foreach ($memIds as $id) {
          $r = User::find($id);
          if (Util::isStaging()) {
            Log::debug('Start update the season Name for the member ' . $id);
          }
          if (!$r || !$r->inTeams([Role::ROLE_TRANSFER_MEX_MEMBER])) {
            continue;
          }
          Util::updateMeta($r, [
            'seasonName' => $seasonName
          ]);
      }

      return new SuccessResponse();
    }


     /**
     * @Route("/admin/mex/members/batch-setting-splash", methods={"POST"})
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function batchSettingSplash(Request $request) {
      $eid = $this->getPostData('splash');
      if (!$eid) {
          return new FailedResponse('Invalid splash!');
      }
      $splash = $this->em->getRepository(SplashPage::class)
                     ->find($eid);
      if (!$splash) {
          return new FailedResponse('Unknown splash ' . $eid . '!');
      }
      $memIds = $this->getPostData('members');
      $attachmentId = $this->getPostData('attachmentId');
      if (!$memIds && !$attachmentId) {
          return new FailedResponse('No selected members to set!');
      }

      if ($attachmentId) {
        $attachment = Attachment::find($attachmentId);
        $category =  'transferMexMemberBatchActions';
        if (!$attachment || $attachment->getCategory() !== $category) {
          return new FailedResponse('Unknown or invalid file!');
        }
        $memIds = MemberService::getMemberIdsFromFile($attachment->getPath());
      }
      if (!$memIds) {
        return new FailedResponse('No selected members to set!');
      }
      if (Data::get('batch_setting_splash_doing_' . $splash->getId())) {
        return new FailedResponse('A setup is running, please try latter!');
      }
      $type = $this->getPostData('type', 'set');
      if (count($memIds) > 20) {
        Data::setArray('batch_setting_splash_' . $splash->getId(), $memIds);
        Data::set('batch_setting_splash_doing_' . $splash->getId(), true);
        InstantBackgroundCommand::add('span:mex:member-batch-setting-splash', [
          '--splashId' => $eid,
          '--type' => $type
        ]);
        return new SuccessResponse('Setting in processing');
      }
      /** @var User $r */
      foreach ($memIds as $id) {
          $r = User::find($id);
          if (!$r || !$r->inTeams([Role::ROLE_TRANSFER_MEX_MEMBER])) {
            continue;
          }
          $splashList = Util::meta($r, 'splashList');
          $splashListArray = $splashList ? explode(',', $splashList) : [];
            if ($type == 'set') {
            if (!$splashList) {
              Util::updateMeta($r, [
                'splashList' => $splash->getId()
              ]);
            } else if (!in_array($splash->getId(), $splashListArray)) {
              Util::updateMeta($r, [
                'splashList' => $splashList . ',' . $splash->getId()
              ]);
            }
          } else {
            if (in_array($splash->getId(), $splashListArray)) {
              Util::updateMeta($r, [
                'splashList' => implode(',', array_diff($splashListArray, [$eid]))
              ]);
            }
          }
      }

      return new SuccessResponse();
    }

     /**
     * @Route("/admin/mex/members/batch-clear-splash", methods={"POST"})
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function batchClearSplash(Request $request) {
      $memIds = $this->getPostData('members');
      $attachmentId = $this->getPostData('attachmentId');
      if (!$memIds && !$attachmentId) {
          return new FailedResponse('No selected members to clear!');
      }
      if ($attachmentId) {
        $attachment = Attachment::find($attachmentId);
        $category =  'transferMexMemberBatchActions';
        if (!$attachment || $attachment->getCategory() !== $category) {
          return new FailedResponse('Unknown or invalid file!');
        }
        $memIds = MemberService::getMemberIdsFromFile($attachment->getPath());
      }
      /** @var User $user */
      foreach ($memIds as $id) {
        $user = User::find($id);
        if ($user && $user->inTeams([Role::ROLE_TRANSFER_MEX_MEMBER])) {
          Util::updateMeta($user, [
              'splashList' => ''
            ]);
          }
      }

      return new SuccessResponse();
    }

     /**
     * @Route("/admin/mex/members/batch-unload", methods={"POST"})
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function batchUnload(Request $request) {
      $aid = $this->getPostData('attachment');
        if (!$aid) {
            return new FailedResponse('Invalid parameters!');
        }
        $attachment = Attachment::find($aid);
        $category =  'transferMexMemberBatchUnload';
        if (!$attachment || $attachment->getCategory() !== $category) {
            return new FailedResponse('Unknown or invalid file!');
        }
        $path = $attachment->getPath();
        if (!Util::endsWith(strtolower($path), '.xlsx')) {
            return new FailedResponse('Only XLSX file is supported!');
        }

        Util::longRequest();
        $importRecord = new ImportMemberUnloadRecord();
        $importRecord->setPath($path)
                      ->setFailedCount(0)
                      ->setSuccessCount(0)
                      ->setFileName($attachment->getName())
                      ->setStatus(ImportMemberUnloadRecord::IMPORT_INIT)
                      ->persist();
        Service::sendAsync('/t/cron/mex/import/batchunload/' . $importRecord->getId());
        return new SuccessResponse('Import in processing');
    }

    /**
     * @Route("/admin/mex/member/new-tag", methods={"POST"})
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function addTag(Request $request) {

      $name = $this->getPostData('name');
      if (!$name) {
          return new FailedResponse('Invalid tag name!');
      }
      if (Tag::find($name)) {
        return new SuccessResponse();
      } else {
        $tag = new Tag();
        $tag->setName($name)
            ->setPlatform($this->platform)
            ->persist();
      }
      return new SuccessResponse();
    }

    /**
     * @Route("/admin/mex/member/{user}/add-tags", methods={"POST"})
     * @param Request $request
     * @param User    $user
     *
     * @return SuccessResponse
     */
    public function addTagsToMember(Request $request, User $user) {

      $tags = $this->getPostData('tags');
      $user->clearTags();
      foreach ($tags as $tag) {
        $user->ensureTag($tag);
      }
      return new SuccessResponse();
    }

    /**
     * @Route("/admin/mex/member/{user}/get-tags")
     * @param Request $request
     * @param User    $user
     *
     * @return SuccessResponse
     */
    public function getTags(Request $request, User $user) {
      $tags = Tag::tagList();
      $data['tags'] = array_column($tags, 'name');
      $data['selectTags'] = $user->getTags()->map(function (Tag $tag) {
        return $tag->getName();
      })->toArray();
      return new SuccessResponse($data);
    }

     /**
     * @Route("/admin/mex/members/batch-migrate-cards", methods={"POST"})
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function batchMigrateCards(Request $request) {
      $aid = $this->getPostData('attachment');
        if (!$aid) {
            return new FailedResponse('Invalid parameters!');
        }
        $attachment = Attachment::find($aid);
        $category =  'transferMexMemberBatchMigrateCards';
        if (!$attachment || $attachment->getCategory() !== $category) {
            return new FailedResponse('Unknown or invalid file!');
        }
        $path = $attachment->getPath();
        if (!Util::endsWith(strtolower($path), '.xlsx')) {
            return new FailedResponse('Only XLSX file is supported!');
        }

        Util::longRequest();
        $importRecord = new ImportMemberMigrateCardsRecord();
        $importRecord->setPath($path)
                      ->setFailedCount(0)
                      ->setSuccessCount(0)
                      ->setFileName($attachment->getName())
                      ->setStatus(ImportMemberMigrateCardsRecord::IMPORT_INIT)
                      ->persist();
        Service::sendAsync('/t/cron/mex/import/batchMigrateCards/' . $importRecord->getId());
        return new SuccessResponse('Import in processing');
    }

    /**
     * @Route("/admin/mex/member/{user}/viewpan")
     * @param Request $request
     * @param User    $user
     *
     * @return SuccessResponse|FailedResponse
     */
    public function viewPan(Request $request, User $user) {
        if (Util::isLive() && !Util::isDev()) {
            return new FailedResponse('404 route not found');
        }
      $uc = $user->getCurrentPlatformCard();
      if ($uc && $uc->isBotmCard()) {
          $pan = BotmService::decryptCachedPan($uc);
          return new SuccessResponse($pan);
      }
      return new FailedResponse('Please check again, the member has not been assigned a card or the card is not a BOTM card');
    }

    /**
     * @Route("/admin/mex/member/{user}/view-ofac-details", methods={"GET"})
     * @param Request $request
     * @param User    $user
     *
     * @return Response
     */
    public function viewPayoutDetailsAction(Request $request, User $user)
    {
        $this->authSuperAdmin();
        $ei = ExternalInvoke::findByEntity(User::class, $user->getId(), 'idology_pa-standalone');
        $data = [
          'Request' => $ei ? $ei->getRequest(true) : '',
          'Response' => $ei ? $ei->getResponse(true)  : '',
          'Response At' => $ei ? Util::formatDateTime($ei->getRespondAt() ?: $ei->getCreatedAt()) : ''
        ];
        return $this->render('@Core/Common/json.html.twig', [
            'title' => 'Member OFAC Details',
            'id' => $user->getId(),
            'data' => $data,
        ]);
    }

    /**
     * @Route("/admin/mex/members/{user}/revert-to-old-card", methods={"POST"})
     * @param Request $request
     *
     * @param User $user
     *
     * @return FailedResponse|SuccessResponse
     */
    public function revertToOldCardAction(Request $request, User $user)
    {
        $cardNumber = $this->getPostData('cardNumber');
        if (!$cardNumber) {
            return new FailedResponse('Unknown card Number!');
        }

        $uc = $user->getCurrentPlatformCard();
        $oldNumber = $uc->getAccountNumber();
        $retiredCard = $uc->getRetiredRapidAccountNumber();
        if ( $retiredCard != $cardNumber) {
          return new FailedResponse('The card can not been revert as old card!');
        }
        $uc->setAccountNumber($retiredCard)
           ->persist();

        $meta = Util::meta($uc);
        $oan = isset($meta['oldAccountNumbers']) ? $meta['oldAccountNumbers'] : [];
        if (in_array($retiredCard,  $oan)) {
          $meta['oldAccountNumbers'] = array_diff($oan, [$retiredCard]);
        }
        unset($meta['retiredRapidAccount']);
        $uc->setMeta(Util::j2s($meta));
        Util::persist($uc);
        $user->addNote('Reverted the card to from ' . $oldNumber . ' to: ' . $retiredCard,
        TRUE, $this->user->getId());
        // update card balance
        try {
          ProcessorHub::updateBalanceAndStatus($uc);
        } catch (\Throwable $t) {
          if (RapidAPI::isAccountNumberError($t->getMessage())) {
              $api = RapidAPI::getForUserCard($uc);
              $api->getBalance($uc, true);
              RapidService::updateBalanceAndStatus($uc);
          } else {
              throw $t;
          }
        }
        return new SuccessResponse();
    }

    /**
     * @Route("/admin/mex/members/{user}/reset-spending-limit", methods={"POST"})
     * @param Request $request
     *
     * @param User $user
     *
     * @return FailedResponse|SuccessResponse
     */
    public function resetSpendingLimit(Request $request, User $user)
    {
        $proxyValue = $this->getPostData('proxyValue');
        if (!$proxyValue) {
            return new FailedResponse('Unknown card proxy value!');
        }
        $uc = new UserCard();
        $api = BotmAPI::getForUserCard($uc);
        $api->proxyValue = $proxyValue;
        $data = ExternalInvoke::host($api->updateCard($uc, []));
        $limit = $data['spend_limit'] ?? null;
        if ($limit === null) {
            $limit = 'unknown';
        } else if ($limit === 0) {
            $limit = 'unlimited';
        } else {
            $limit = Money::formatUSD($limit);
        }
        return new SuccessResponse(null, 'Updated spending limit to ' . $limit);
    }

     /**
     * @Route("/admin/mex/members/batch-config-transfer-free", methods={"POST"})
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function batchConfigTransferFree(Request $request)
    {
        $aid = $this->getPostData('attachment');
        if (!$aid) {
            return new FailedResponse('Invalid parameters!');
        }
        $attachment = Attachment::find($aid);
        if (!$attachment) {
            return new FailedResponse('Unknown or invalid file!');
        }
        $path = $attachment->prepareForLocalRead();
        if (!Util::endsWith(strtolower($path), '.xlsx')) {
            return new FailedResponse('Only XLSX file is supported!');
        }
        $startDate = $this->getPostData('startDate');
        $endDate = $this->getPostData('endDate');
        if (!$startDate || !$endDate) {
          return new FailedResponse('Please select the start and end date!');
        }

        $import = new ImportMemberConfigTransferFreeRecord();
        $import->setStartTime(new Carbon($startDate))
               ->setExpiredTime(new Carbon($endDate))
               ->setStatus(ImportMemberConfigTransferFreeRecord::IMPORT_INIT)
               ->setSuccessCount(0)
               ->setFailedCount(0)
               ->setPath($path)
               ->setFileName($attachment->getFileName())
               ->persist();
        Service::sendAsync('/t/cron/mex/import/ConfigTransferFree/' . $import->getId());
        return new SuccessResponse();
    }

     /**
     * @Route("/admin/mex/members/{user}/config-transfer-with-intermex", methods={"POST"})
     * @param Request $request
     * @param User $user
     * @return FailedResponse|SuccessResponse
     */
    public function configTransferWithIntermer(Request $request, User $user)
    {
        $type = $this->getPostData('type');
        if ($type == true) {
          Util::updateMeta($user, ['useIntermex' => true]);
        } else {
          Util::updateMeta($user, ['useIntermex' => false]);
        }
        return new SuccessResponse();
    }

    /**
     * @Route("/admin/mex/members/batch-update-last-four-ssn", methods={"POST"})
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function batchUpdateLastFourSsn(Request $request)
    {
        $aid = $this->getPostData('attachment');
        if (!$aid) {
            return new FailedResponse('Invalid parameters!');
        }
        $attachment = Attachment::find($aid);
        if (!$attachment) {
            return new FailedResponse('Unknown or invalid file!');
        }
        $path = $attachment->prepareForLocalRead();
        if (!Util::endsWith(strtolower($path), '.xlsx')) {
            return new FailedResponse('Only XLSX file is supported!');
        }
        $employerId = $this->getPostData('employer');
        if (!$employerId) {
          return new FailedResponse('Please select the employer!');
        }
        $employer = User::find($employerId);
        if (!$employer) {
          return new FailedResponse('Please select the employer!');
        }
        $import = new ImportUpdateLastFourSsnRecord();
        $import->setStatus(ImportUpdateLastFourSsnRecord::IMPORT_INIT)
               ->setSuccessCount(0)
               ->setFailedCount(0)
               ->setPath($path)
               ->setFileName($attachment->getFileName())
               ->setEmployer($employer)
               ->persist();
        Service::sendAsync('/t/cron/mex/import/UpdateLastFourSsn/' . $import->getId());
        return new SuccessResponse();
    }

}
