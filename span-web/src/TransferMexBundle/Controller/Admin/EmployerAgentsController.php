<?php


namespace TransferMexBundle\Controller\Admin;


use Carbon\Carbon;
use CoreBundle\Entity\Email;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserGroup;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Services\UserService;
use CoreBundle\Utils\Bundle;
use CoreBundle\Utils\QueryListParams;
use CoreBundle\Utils\Util;
use Doctrine\ORM\QueryBuilder;
use PortalBundle\Exception\PortalException;
use SalexUserBundle\Entity\User;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use TransferMexBundle\Entity\EmployerSubCompany;
use TransferMexBundle\Entity\UserTransferMexTrait;


class EmployerAgentsController extends AgentsController
{
    use UserTransferMexTrait;

    /** @var User */
    public $employer;
    /** @var UserGroup */
    public $group;

    public function __construct()
    {
        parent::__construct();

        $this->employer = $this->getGroupAdminUser($this->user);
        $this->group = $this->employer->getAdminGroup();
    }

    protected function getAccessibleRoles()
    {
        return [
            User::getCurrentEmployerRole(),
            Role::ROLE_TRANSFER_MEX_EMPLOYER_ADMIN,
            Role::ROLE_TRANSFER_MEX_EMPLOYER_ADMIN_ACH,
        ];
    }

    protected function validateUser(User $user = null)
    {
        if (!$user || !$user->inTeams($this->getAccessibleRoles())) {
            throw PortalException::temp('Unknown user!');
        }
    }

    protected function validateRole()
    {
        if ($this->user->inTeams([
            Role::ROLE_TRANSFER_MEX_EMPLOYER_ADMIN,
            Role::ROLE_TRANSFER_MEX_EMPLOYER_ADMIN_ACH,
        ]) && !Util::meta($this->user, 'manageAdmin')) {
          throw new PortalException('Permission denied!');
        }
    }

    /**
     * @Route("/admin/mex/employer/admins/list/{page}/{limit}", methods={"GET"}, defaults={"page" = 1,"limit" = 10})
     * @param Request $request
     * @param int     $page
     * @param int     $limit
     *
     * @return SuccessResponse
     */
    public function search(Request $request, $page = 1, $limit = 10)
    {
        $this->validateRole();
        $resp = $this->traitSearch($request, [
            'createdAt',
        ], $page, $limit);
        $result = $resp->getResult();
        $result['quick'] = [
            'total' => $this->query($request)->select('count(distinct u)')
                ->distinct()
                ->getQuery()
                ->getSingleScalarResult(),
        ];
        return new SuccessResponse($result);
    }

    protected function query(Request $request)
    {
        $currentEmployerId = Util::meta($this->user, 'currentEmployer') ?? $this->user->getId();
        $query = $this->em->getRepository(User::class)
            ->createQueryBuilder('u')
            ->join('u.teams', 't')
            ->join('u.config', 'config')
            ->join('config.group', 'g')
            ->join('g.adminConfigs', 'ac')
            ->join('ac.user', 'e')
            ->where(Util::expr()->in('t.name', ':roles'))
            ->andwhere(Util::expr()->eq('e.id', ':employerId'))
            ->setParameter('employerId', $currentEmployerId)
            ->setParameter('roles', [
                UserTransferMexTrait::getCurrentEmployerAdminRole(),
                Role::ROLE_TRANSFER_MEX_EMPLOYER_ADMIN_ACH
            ]);

        return $this->queryByDateRange($query, 'u.createdAt', $request);
    }

    protected function queryListParams(QueryBuilder $query, Request $request)
    {
        $params = new QueryListParams($query, $request, 'u', 'count(distinct u)');
        $params->distinct = true;
        $params->orderBy = [
            'u.id' => 'desc',
        ];
        $params->searchFields = [
            'u.name',
            'u.email',
            'g.name'
        ];
        return $params;
    }

    /**
     * @param User $entity
     *
     * @return array
     */
    protected function getRowData($entity)
    {
		return [
            'Create Date' => Util::formatDateTime($entity->getCreatedAt()),
            'User ID' => $entity->getId(),
            'First Name' => $entity->getFirstName(),
            'Last Name' => $entity->getLastName(),
            'Email' => $entity->getEmail(),
            'Mobile Phone' => $entity->getMobilephone(),
            'Last Login Time' =>  Util::formatDateTime($entity->getLastLogin()),
            'Status' => $entity->getStatus() === User::STATUS_ACTIVE ? 'Active' : 'Archived',
            'Manage Admins' => Util::meta($entity, 'manageAdmin') ? true : false,
            'Read Only' => Util::meta($entity, 'readOnlyAdmin') ? true : false,
            'Receive System Message' => Util::meta($entity, 'receiveSystemMessage') ? true : false,
            'subEmployerIds' => EmployerSubCompany::getSubCompany($entity)
        ];
    }

	/**
	 * @Route("/admin/mex/employer/admins/save", methods={"POST"})
	 * @param Request $request
	 *
	 * @return FailedResponse|SuccessResponse
	 * @throws PortalException
	 */
    public function saveAction(Request $request)
    {
        $this->validateRole();
        $uid = $request->get('User ID');
        $email = $request->get('Email');

        $memberRoles = Bundle::getMemberRoles();
        $inviteCode = false;
        if ($uid) {
            $user = User::find($uid);
            $this->validateUser($user);
        } else {

            $user = User::findPlatformUserByEmail($email, array_merge($memberRoles, Bundle::getAdminRoles()));
            if ($user) {
                throw PortalException::temp('Duplicated accounts with the same email address!');
            }
            $user = new User();
            $inviteCode = Util::generatePassword();
            $user->setEnabled(true)
                ->setPlainPassword($inviteCode)
                ->setStatus(User::STATUS_ACTIVE)
                ->setSource('mex_employer');
        }
        $group = $this->group;
        $user->setFirstName($request->get('First Name'))
            ->setLastName($request->get('Last Name'))
            ->changeMobilePhone(Util::inputPhone($request->get('Mobile Phone'), 'US'), array_merge($memberRoles))
            ->changeEmail($email, array_merge($memberRoles, Bundle::getAdminRoles()))
            ->ensureRole( $group->getFundingType() == UserGroup::FUNDING_TYPE_ACH ? Role::ROLE_TRANSFER_MEX_EMPLOYER_ADMIN_ACH : UserTransferMexTrait::getCurrentEmployerAdminRole())
            ->persist();


        $config = $user->ensureConfig();
        $config->setGroup($group)->persist();
        if ($inviteCode) {
            $user->addAccessiblePlatform($this->platform)
                ->addAccessibleCardProgram($this->cardProgram)
                ->persist();
            if ($this->platform->isFaasPlatforms()) {
              UserService::sendResetPasswordEmail($user);
            } else {
              Email::sendWithTemplateToUser($user, Email::TEMPLATE_TRANSFER_MEX_INVITE_AGENT, [
                  'invitor' => $this->user->getName(),
                  'role' => UserTransferMexTrait::getCurrentEmployerAdminRole(),
                  'action_url' => Util::host() . '/login/reset-password',
              ]);
            }
        }
        $manageAdmin = $request->get('Manage Admins');
        $readOnlyAdmin = $request->get('Read Only');
        $receiveSystemMessage = $request->get('Receive System Message');

        if ($manageAdmin === true) {
          Util::updateMeta($user, ['manageAdmin' => true]);
        } else {
          Util::updateMeta($user, ['manageAdmin' => false]);
        }
        if ($readOnlyAdmin === true) {
          Util::updateMeta($user, ['readOnlyAdmin' => true]);
        } else {
          Util::updateMeta($user, ['readOnlyAdmin' => false]);
        }
        Util::updateMeta($user, ['receiveSystemMessage' => $receiveSystemMessage]);
        return new SuccessResponse($this->getRowData($user));
    }

    /**
     * @Route("/admin/mex/employer/admins/{user}/del", methods={"POST"})
     * @param User    $user
     *
     * @return FailedResponse|SuccessResponse
     */
    public function del(User $user)
    {
        $this->validateRole();
        $this->validateUser($user);

        $user->setDeletedAt(Carbon::now())
            ->persist();

        return new SuccessResponse();
    }

    /**
     * @Route("/admin/mex/employer/admins/{user}/toggle-status", methods={"POST"})
     * @param Request $request
     * @param User    $user
     *
     * @return FailedResponse|SuccessResponse
     * @throws PortalException
     */
    public function toggleStatusAction(Request $request, User $user)
    {
        $this->validateRole();
        $this->validateUser($user);
        $status = $request->get('Status', 'Active') === 'Active'
            ? User::STATUS_ACTIVE
            : User::STATUS_CLOSED;
        $user->setStatus($status, false)
            ->persist();

        return new SuccessResponse($this->getRowData($user));
    }

    /**
     * @Route("/admin/mex/employer/admins/{user}/resetFA", methods={"POST"})
     * @param Request $request
     * @param User    $user
     *
     * @return SuccessResponse
     */
    public function resetFaAction(Request $request, User $user)
    {
        $this->validateRole();
        $this->validateUser($user);

        $platformName = $this->getPlatformName();
        Util::updateMeta($user, [
            $platformName . '-twoFactorSecret' => '',
            $platformName . '-twoFactorType' => ''
        ]);
        return new SuccessResponse($this->getRowData($user));
    }

    /**
     * @Route("/admin/mex/employer/admins/export", methods={"POST"})
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function export(Request $request)
    {
        $this->validateRole();
        $title = $this->platform->isFaasPlatforms() ? $this->cardProgram->getName() . ' Admins' : 'TransferMex Employer Admins';
        return $this->commonExport($request, $title, [
            'Create Date'  => 20,
            'User ID'      => 14,
            'First Name'   => 20,
            'Last Name'    => 20,
            'Email'        => 40,
            'Mobile Phone' => 20,
            'Status'       => 20,
            'Last Login Time' => 20,
        ]);
    }

    /**
     * @Route("/admin/mex/employers/admins/{user}/config-sub-company", methods={"POST"})
     * @param Request $request
     * @param User    $user
     *
     * @return FailedResponse|SuccessResponse
     * @throws PortalException
     */
    public function configSubEmployer(Request $request, User $user)
    {
        $subEmployers = $this->getPostData('subEmployerIds');
        $subEmployerIds = explode(',',  $subEmployers);
        EmployerSubCompany::updateSubEmployer($user, $subEmployerIds);
        return new SuccessResponse(null, 'Update successfully.');
    }
}
