<?php


namespace TransferMexBundle\Controller\Admin;


use Carbon\Carbon;
use CoreBundle\Controller\ListControllerTrait;
use CoreBundle\Entity\Country;
use CoreBundle\Entity\ExternalInvoke;
use CoreBundle\Entity\UserGroup;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Util;
use FaasBundle\Services\BOTM\BotmAPI;
use FaasBundle\Services\FaasMemberService;
use FaasBundle\Services\ProcessorHub;
use PortalBundle\Exception\PortalException;
use SalexUserBundle\Entity\User;
use Symfony\Component\HttpFoundation\Request;
use TransferMexBundle\Services\EmployerService;
use TransferMexBundle\Services\RapidAPI;
use TransferMexBundle\Services\Tern\TernAPI;


trait EmployersControllerTrait
{
	use ListControllerTrait;
	public static $currentRoles = null;

	public function getCurrentRoles() {
		self::$currentRoles = Util::getCurrentRoles();
	}

	public function traitEmployerSearch(Request $request, $page = 1, $limit = 10)
	{
		$this->getCurrentRoles();

		$resp = $this->traitSearch($request, [
			'createdAt',
		], $page, $limit);
		$result = $resp->getResult();

		$result['quick'] = [
			'total' => $this->query($request)
				->select('count(distinct u)')
				->distinct()
				->getQuery()
				->getSingleScalarResult(),
		];

        $expr = Util::expr();
		$q = Util::em()->getRepository(User::class)
			->createQueryBuilder('u')
			->join('u.teams', 't')
			->where($expr->in('t.name', ':roles'))
			->andWhere($expr->in('u.register_step', ':steps'))
			->setParameter('roles', [
				self::$currentRoles['member'],
			])
			->setParameter('steps', [
                FaasMemberService::STEP_ONBOARDED,
                FaasMemberService::STEP_ACTIVE,
                FaasMemberService::STEP_ON_HOLD,
            ])
        ;
        $members = $this->queryMembersInCurrentPlatform($q)
            ->select('count(distinct u)')
            ->distinct()
            ->getQuery()
            ->getSingleScalarResult();
		$average = Util::percent($members, $result['quick']['total']);

		$result['quick']['average'] = round($average);

		return $result;
	}

	/**
	 * @param User $entity
	 *
	 * @return array
	 */
	protected function traitGetRowData($entity)
	{
		$this->getCurrentRoles();
		$group = $entity->getAdminGroup();
		$uc = $entity->getOneCardInPlatform($this->platform->get($this->platform->getName()));
		$an = $uc ? $uc->getAccountNumber() : null;
		$rapidBalance = '';
		$botmBalance = '';
		$ternBalance = '';
        if ($this->getUser()->isMasterAdmin()) {
            if (RapidAPI::hasUserGroupAccount($group)) {
                $api = RapidAPI::getForUserGroup($group);
                $rapidBalance = Money::formatUSD($api->getCachedBalance());
            }
        }
        if (BotmAPI::hasUserGroupAccount($group)) {
          $api = BotmAPI::getForUserGroup($group);
          $botmBalance = Money::formatUSD($api->getCachedBalance());
        }
		if (TernAPI::hasUserGroupAccount($group)) {
			$api = TernAPI::getForUserGroup($group);
			$ternBalance = Money::formatUSD($api->getCachedBalance());
		}
		
		$data = [
			'Create Date' => Util::formatDateTime($entity->getCreatedAt()),
			'Contact Name' => $entity->getName(),
			'Email' => $entity->getEmail(),
			'Phone' => $entity->getMobilephone(),
			'First Name' => $entity->getFirstName(),
			'Last Name' => $entity->getLastName(),
			'Street Address' => $entity->getAddress(),
			'CountryId' => $entity->getCountryid(),
			'State' => $entity->getStateid(),
			'City' => $entity->getCity(),
			'Postal Code' => $entity->getZip(),
			'Date of Birth' => Util::formatBirthday($entity->getBirthday()),
			'Funding Type' => $group->getFundingType() ?: UserGroup::FUNDING_TYPE_ACH,
			'Account Number' => $an,
			'Account Balance' => $an ? Money::formatWhen($uc->getBalance()) : '',
			'Rapid Agent Balance' => $rapidBalance,
			'BOTM Balance' => $botmBalance,
			'Tern Balance' => $ternBalance,
			'Enrolled' => $an ? ($uc->isIssued() ? 'Yes' : 'No') : '',
			'Card Status' => $an && $uc->isIssued() ? ucfirst($uc->getStatus()) : '',
			'Last Login Time' => $entity->getLastLogin(true),
			'Status' => $entity->getStatus() === User::STATUS_ACTIVE ? 'Active' : 'Inactive',
			'loading' => false,
			'migratedToTern' => TernAPI::hasUserGroupAccount($group),
		];

		return ['data' => $data, 'group' => $group];
	}

	protected function traitSaveAction(Request $request, $roles) {
		$this->getCurrentRoles();
		$uid = $this->getPostData('Employer ID');
		$email = $this->getPostData('Email');
		$newUser = false;
		if ($uid) {
			$user = User::find($uid);
			$this->validateUser($user);

			$status = $this->getPostData('Status', 'Active') === 'Active'
				? User::STATUS_ACTIVE
				: User::STATUS_INACTIVE;
			$user->setStatus($status);
		} else {
			if (!$roles) return false;
			$user = User::findPlatformUserByEmail($email, $roles);
			if ($user) {
				throw PortalException::temp('Duplicated accounts with the same email address!');
			}
			$newUser = true;
			$user = new User();
			$user->setEnabled(true)
				->setPlainPassword(Util::generatePassword())
				->setStatus(User::STATUS_ACTIVE)
                ->setSource('mex_employer_trait');
		}

		$country = Country::find($this->getPostData('CountryId'));

		$user->setFirstName($this->getPostData('First Name'))
			->setLastName($this->getPostData('Last Name'))
			->setMobilephone(Util::inputPhone($this->getPostData('Phone'), $country))
			->setAddress($this->getPostData('Street Address'))
			->setCountry($country)
			->setStateid($this->getPostData('State'))
			->setCity($this->getPostData('City'))
			->setBirthday(Util::toUTC(Util::timeLocal($this->getPostData('Date of Birth'))))
			->setZip($this->getPostData('Postal Code'))
			->changeEmail($email, self::$currentRoles['employer'])
			->ensureRole(self::$currentRoles['employer'])
			->persist();

		$config = $user->ensureConfig();
		if ($newUser) {
			$user->addAccessiblePlatform($this->platform)
				->addAccessibleCardProgram($this->cardProgram)
				->persist();

			$group = new UserGroup();
			$group->setPlatform($this->platform)
				->setCardProgram($this->cardProgram)
                ->addAdminConfig($config);
		} else {
			$group = $config->getGroup();
		}
		$group->setName($this->getPostData('Employer Name'))
			->setFundingType($this->getPostData('Funding Type', UserGroup::FUNDING_TYPE_ACH))
            ->setCardProvider($this->getPostData('Card Provider'));
		$config->setGroup($group);
		$em = Util::em();
		$em->persist($group);
		$em->persist($config);
		$em->flush();

        // if ($group->isPrefunded()) {
            EmployerService::getMexDummyCard($user);
        // }
        ProcessorHub::ensureEmployerConfig($group);

		return ['user' => $user, 'group' => $group, 'newUser' => $newUser];
	}

	protected function validateUser(User $user = null)
	{
		$this->getCurrentRoles();
		if (!$user || !$user->inTeam(self::$currentRoles['employer'])) {
			throw PortalException::temp('Unknown user!');
		}
	}

	protected function traitRapid(Request $request, User $user) {
		$this->validateUser($user);

		$group = $user->getAdminGroup();
		if (!$group) {
			$text = $this->platform->isTransferMex() ? 'employer' : 'client';
			return new FailedResponse('No user group for this ' . $text . '!');
		}

		$result = [
            'rapidAgentName' => Util::meta($group, 'rapidAgentName'),
            'rapidAgentNumber' => Util::meta($group, 'rapidAgentNumber'),
			'rapidAgentAni' => Util::meta($group, 'rapidAgentAni'),
            'rapidCardProgram' => Util::meta($group, 'rapidCardProgram'),
            'rapidCardProgramId' => Util::meta($group, 'rapidCardProgramId'),
			'rapidUserProfileGroupName' => Util::meta($group, 'rapidUserProfileGroupName'),
            'rapidUserProfileGroupId' => Util::meta($group, 'rapidUserProfileGroupId'),
        ];
		return new SuccessResponse($result);
	}

	protected function traitRapidSave(Request $request, User $user) {
		$this->validateUser($user);

		$number = trim($this->getPostData('Agent Number'));
		$ani = trim($this->getPostData('Agent ANI'));

		if (($number && !$ani) || (!$number && $ani)) {
			return new FailedResponse('Invalid parameters!');
		}

		$group = $user->getAdminGroup();
		if (!$group) {
			$text = $this->platform->isTransferMex() ? 'employer' : 'client';
			return new FailedResponse('No user group for this ' . $text . '!');
		}

		if ($number) {
			Util::updateMeta($group, [
                'rapidAgentName' => $this->getPostData('Agent Name'),
                'rapidAgentNumber' => $number,
                'rapidAgentAni' => $ani,
                'rapidCardProgram' => trim($this->getPostData('Card Program Name')),
                'rapidCardProgramId' => trim($this->getPostData('Card Program ID')),
                'rapidUserProfileGroupName' => trim($this->getPostData('User Profile Group Name')),
                'rapidUserProfileGroupId' => trim($this->getPostData('User Profile Group ID')),
			]);
		} else {
            Util::updateMeta($group, 'rapidAgentName', false);
            Util::updateMeta($group, 'rapidAgentNumber', false);
            Util::updateMeta($group, 'rapidAgentAni', false);
            Util::updateMeta($group, 'rapidCardProgram', false);
            Util::updateMeta($group, 'rapidCardProgramId', false);
            Util::updateMeta($group, 'rapidUserProfileGroupName', false);
            Util::updateMeta($group, 'rapidUserProfileGroupId', false);
            $group->persist();
		}

        ProcessorHub::ensureEmployerConfig($group);

		return new SuccessResponse();
	}

    protected function traitBotm(Request $request, User $user) {
        $this->validateUser($user);

        $group = $user->getAdminGroup();
        if (!$group) {
            $text = $this->platform->isTransferMex() ? 'employer' : 'client';
            return new FailedResponse('No user group for this ' . $text . '!');
        }

        $result = [
            'businessId' => $group->getBotmBusinessId(),
            'businessAccountId' => $group->getBotmBusinessAccountId(),
        ];
        return new SuccessResponse($result);
    }

    protected function traitBotmSave(Request $request, User $user) {
        $this->validateUser($user);
        $group = $user->getAdminGroup();
        if (!$group) {
            $text = $this->platform->isTransferMex() ? 'employer' : 'client';
            return new FailedResponse('No user group for this ' . $text . '!');
        }

        $accountId = trim($this->getPostData('Business Account ID'));
        if (!$accountId) {
            return new FailedResponse('The BOTM business account ID is required for all the employers!');
        }

        $botm = new BotmAPI(businessAccountId: $accountId);
        $data = ExternalInvoke::host($botm->retrieveBusinessAccount($accountId));
        $group->setBotmBusinessId($data['business_id'])
            ->setBotmBusinessAccountId($accountId)
            ->persist();

        ProcessorHub::ensureEmployerConfig($group);

        return new SuccessResponse();
    }

	protected function traitDel(User $user)
	{
		$this->validateUser($user);

		$count = $user->getAdminGroup()->getUserCount();
		if ($count > 0) {
			$textEmployer = $this->platform->isTransferMex() ? 'employer' : 'client';
			$textEmployee = $this->platform->isTransferMex() ? 'employees' : 'members';
			return new FailedResponse('There is 1 or more ' . $textEmployee . ' belong to this ' . $textEmployer . '. Failed to delete!');
		}

		$user->setDeletedAt(Carbon::now())
			->persist();

		return new SuccessResponse();
	}

    protected function traitRefreshAgentBalanceAction(Request $request, User $user)
    {
        $this->validateUser($user);

        $field = strtolower($request->get('field'));
        if (str_starts_with($field, 'botm')) {
            $api = BotmAPI::getForUserGroupAdmin($user);
        } else if (str_starts_with($field, 'rapid')) {
            $api = RapidAPI::getForUserGroupAdmin($user);
        } else {
            $group = $user->getAdminGroup();
            $api = $group->getApiImpl();
        }
        $balance = ProcessorHub::updateAgentBalance($api);

        return new SuccessResponse(Money::formatUSD($balance));
    }
}
