<?php


namespace TransferMexBundle\Controller\Admin;


use CoreBundle\Entity\Country;
use CoreBundle\Entity\Email;
use CoreBundle\Entity\Role;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreB<PERSON>le\Utils\Bundle;
use CoreB<PERSON>le\Utils\QueryListParams;
use CoreBundle\Utils\Util;
use Doctrine\ORM\QueryBuilder;
use PortalBundle\Exception\PortalException;
use SalexUserBundle\Entity\User;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use TransferMexBundle\Entity\UserTransferMexTrait;
use TransferMexBundle\TransferMexBundle;

class AgentsController extends BaseController
{
    /**
     * AgentsController constructor.
     */
    public function __construct()
    {
        parent::__construct();

        $this->authRoles($this->getAccessibleRoles());
    }

    protected function getAccessibleRoles()
    {
        return [
            Role::ROLE_TRANSFER_MEX_ADMIN,
            Role::ROLE_FAAS_ADMIN,
            Role::ROLE_TRANSFER_MEX_DEPARTMENT
        ];
    }

    /**
     * @Route("/admin/mex/agents/list/{page}/{limit}", methods={"GET"}, defaults={"page" = 1,"limit" = 10})
     * @param Request $request
     * @param int     $page
     * @param int     $limit
     *
     * @return SuccessResponse
     */
    public function search(Request $request, $page = 1, $limit = 10)
    {
        $resp = $this->traitSearch($request, [
            'createdAt',
        ], $page, $limit);
        $result = $resp->getResult();

        $result['quick'] = [
            'total' => $this->query($request)->select('count(distinct u)')
                ->distinct()
                ->getQuery()
                ->getSingleScalarResult(),
        ];

        return new SuccessResponse($result);
    }

    protected function query(Request $request)
    {
        $query = $this->em->getRepository(User::class)
            ->createQueryBuilder('u')
            ->join('u.teams', 't')
            ->where(Util::expr()->in('t.name', ':roles'))
            ->setParameter('roles', UserTransferMexTrait::getAgentRoles());

        return $this->queryByDateRange($query, 'u.createdAt', $request);
    }

    protected function queryListParams(QueryBuilder $query, Request $request)
    {
        $params = new QueryListParams($query, $request, 'u', 'count(distinct u)');
        $params->distinct = true;
        $params->orderBy = [
            'u.id' => 'desc',
        ];
        $params->searchFields = [
            'u.name',
            'u.email',
        ];
        return $params;
    }

    /**
     * @param User $entity
     *
     * @return array
     */
    protected function getRowData($entity)
    {
        $userType = '';
        switch ( $entity->getTeamName()) {
          case Role::ROLE_TRANSFER_MEX_ADMIN: $userType = 'Administrator'; break;
          case Role::ROLE_TRANSFER_MEX_DEPARTMENT: $userType = 'Department'; break;
          case Role::ROLE_TRANSFER_MEX_AGENT:  $userType = 'Agent';  break;
          case Role::ROLE_TRANSFER_MEX_CAPTURE: $userType = 'Capture'; break;
        }

        return [
            'Create Date' => Util::formatDateTime($entity->getCreatedAt()),
            'User ID' => $entity->getId(),
            'First Name' => $entity->getFirstName(),
            'Last Name' => $entity->getLastName(),
            'User Type' =>  $userType,
            'Email' => $entity->getEmail(),
            'Mobile Phone' => $entity->getMobilephone(),
            'Last Login Time' =>  Util::formatDateTime($entity->getLastLogin()),
            'Status' => $entity->getStatus() === User::STATUS_ACTIVE ? 'Active' : 'Archived',
            'permissions' => Util::meta($entity, 'uPermissions') ?? [],
        ];
    }

    /**
     * @Route("/admin/mex/agents/filters")
     * @return SuccessResponse
     */
    public function filtersAction() {
        return new SuccessResponse([]);
    }

    protected function validateUser(User $user = null)
    {
        if (!$user || !$user->inTeams(UserTransferMexTrait::getAgentRoles())) {
            throw PortalException::temp('Unknown user!');
        }
    }

    /**
     * @Route("/admin/mex/agents/{user}/detail")
     * @param User $user
     *
     * @return FailedResponse|SuccessResponse
     */
    public function detailAction(User $user)
    {
        $this->validateUser($user);
        return new SuccessResponse($this->getRowData($user));
    }

    /**
     * @Route("/admin/mex/agents/{user}/toggle-status", methods={"POST"})
     * @param Request $request
     * @param User    $user
     *
     * @return FailedResponse|SuccessResponse
     * @throws PortalException
     */
    public function toggleStatusAction(Request $request, User $user)
    {
        $this->validateUser($user);
        $status = $this->getPostData('Status', 'Active') === 'Active'
            ? User::STATUS_ACTIVE
            : User::STATUS_CLOSED;
        $user->setStatus($status, false)
            ->persist();

        return new SuccessResponse($this->getRowData($user));
    }

    /**
     * @Route("/admin/mex/agents/save", methods={"POST"})
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function saveAction(Request $request)
    {
        $uid = $this->getPostData('User ID');
        $email = $this->getPostData('Email');
        $inviteCode = false;
        if ($uid) {
            $user = User::find($uid);
            $this->validateUser($user);

            $status = $this->getPostData('Status', 'Active') === 'Active'
                ? User::STATUS_ACTIVE
                : User::STATUS_CLOSED;
            $user->setStatus($status, false);
        } else {
            $user = User::findPlatformUserByEmail($email, Bundle::getAdminRoles());
            if ($user) {
                throw PortalException::temp('Duplicated accounts with the same email address!');
            }
            $user = new User();

            $inviteCode = Util::generatePassword();
            $user->setEnabled(true)
                ->setPlainPassword($inviteCode)
                ->setStatus(User::STATUS_ACTIVE)
                ->setCountry(Country::findByCode('USA'))
                ->setSource('mex_agent');
        }

        $user->setFirstName($this->getPostData('First Name'))
            ->setLastName($this->getPostData('Last Name'))
            ->setMobilephone(Util::inputPhone($this->getPostData('Mobile Phone'), 'US'))
            ->changeEmail($email, Role::ROLE_TRANSFER_MEX_AGENT)
            ->persist();
        $role = Role::ROLE_TRANSFER_MEX_AGENT;
        switch ( $this->getPostData('User Type')) {
          case 'Administrator': $role = Role::ROLE_TRANSFER_MEX_ADMIN; break;
          case 'Department': $role = Role::ROLE_TRANSFER_MEX_DEPARTMENT; break;
          case 'Agent': $role = Role::ROLE_TRANSFER_MEX_AGENT; break;
          case 'Capture': $role = Role::ROLE_TRANSFER_MEX_CAPTURE; break;
        }
        // $role = $request->get('User Type') === 'Administrator'
        //     ? Role::ROLE_TRANSFER_MEX_ADMIN
        //     : Role::ROLE_TRANSFER_MEX_AGENT;
        $user->clearRoles(UserTransferMexTrait::getAgentRoles())
            ->ensureRole($role);

        Util::updateMeta($user, 'uPermissions', false);
        Util::updateMeta($user, [
            'uPermissions' => $this->getPostData('permissions', []),
            'passwordExpiresDisabled' => false
        ]);

        if ($inviteCode) {
            $user->addAccessiblePlatform($this->platform)
                ->addAccessibleCardProgram($this->cardProgram)
                ->persist();

            Email::sendWithTemplateToUser($user, Email::TEMPLATE_TRANSFER_MEX_INVITE_AGENT, [
                'invitor' => $this->user->getName(),
                'role' => $role,
                'action_url' => Util::host() . '/login/reset-password',
            ]);
        }

        return $this->detailAction($user);
    }

    /**
     * @Route("/admin/mex/agents/export", methods={"POST"})
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function export(Request $request)
    {
        return $this->commonExport($request, 'TransferMex Agents', [
            'Create Date'  => 20,
            'User ID'      => 14,
            'First Name'   => 20,
            'Last Name'    => 20,
            'User Type'    => 20,
            'Email'        => 40,
            'Mobile Phone' => 20,
            'Status'       => 20,
            'Last Login Time' => 20,
        ]);
    }

     /**
     * @Route("/admin/mex/agents/{user}/update-password", methods={"POST"})
     * @param Request $request
     * @param User    $user
     *
     * @return FailedResponse|SuccessResponse
     * @throws PortalException
     */
    public function updatePasswordAction(Request $request, User $user)
    {
        $this->validateUser($user);
        Util::updateMeta($user, [
          'forceUpdatePassword' => true
        ]);

        return new SuccessResponse($this->getRowData($user));
    }

     /**
     * @Route("/admin/mex/agents/{user}/resetFA", methods={"POST"})
     * @param Request $request
     * @param User    $user
     *
     * @return FailedResponse|SuccessResponse
     * @throws PortalException
     */
    public function resetFAAction(Request $request, User $user)
    {
        $this->validateUser($user);
        $platformName = $this->getPlatformName();
        Util::updateMeta($user, [
          $platformName . '-twoFactorSecret' => '',
          $platformName . '-twoFactorType' => ''
        ]);
        return new SuccessResponse($this->getRowData($user));
    }
}
