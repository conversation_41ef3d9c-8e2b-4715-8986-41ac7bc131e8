<?php


namespace TransferMexBundle\Controller\Admin;


use AppBundle\Command\InstantBackgroundCommand;
use CoreBundle\Entity\Attachment;
use CoreBundle\Entity\ExternalInvoke;
use CoreBundle\Entity\Transfer;
use CoreBundle\Entity\UserCardTransaction;
use CoreBundle\Exception\FailedException;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\QueryListParams;
use CoreBundle\Utils\Service;
use CoreBundle\Utils\Util;
use Doctrine\ORM\QueryBuilder;
use FaasBundle\Services\BOTM\BotmAPI;
use FaasBundle\Services\ProcessorHub;
use PortalBundle\Exception\PortalException;
use SalexUserBundle\Entity\User;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\Routing\Annotation\Route;
use TransferMexBundle\Command\Transfer\RefundFailedDueToDeductionErrorCommand;
use TransferMexBundle\Entity\UserTransferMexTrait;
use TransferMexBundle\Services\IntermexAPI;
use TransferMexBundle\Services\IntermexRemittanceService;
use TransferMexBundle\Services\RapidAPI;
use TransferMexBundle\Services\RapidService;
use TransferMexBundle\Services\RapydAPI;
use TransferMexBundle\Services\RapydDisburseService;
use TransferMexBundle\Services\RapydQueue;
use TransferMexBundle\Services\SlackService;
use TransferMexBundle\Services\TransferProvider;
use TransferMexBundle\Services\TransferReviewService;
use TransferMexBundle\Services\UniTellerRemittanceService;

class MembersTransactionsController extends BaseController
{
    public function __construct()
    {
        parent::__construct();

        $this->authRoles(UserTransferMexTrait::getAgentRoles());
    }

    /**
     * @Route("/admin/mex/members/transactions/list/{page}/{limit}", methods={"GET"}, defaults={"page" = 1,"limit" = 10})
     * @param Request $request
     * @param int     $page
     * @param int     $limit
     *
     * @return SuccessResponse
     */
    public function search(Request $request, $page = 1, $limit = 10)
    {
        $force = $request->get('force');
        if (is_numeric($force)) {
            $member = User::find($request->get('member'));
            if ($member) {
                Service::sendAsync('/t/cron/mex/rapid/update-transactions/' . $member->getId() . '/' . $force);
            }
        }

        return $this->traitSearch($request, [
            'createdAt',
        ], $page, $limit);
    }

    protected function query(Request $request)
    {
        $query = $this->em->getRepository(UserCardTransaction::class)
            ->createQueryBuilder('uct');

        $member = User::find($request->get('member'));
        if ($member) {
            $query->where('uct.userCard = :uc')
                ->setParameter('uc', $member->getCurrentPlatformCard());
        } else {
            $query->where('uct.id < 0');
        }

        return $query;
    }

    protected function queryListParams(QueryBuilder $query, Request $request)
    {
        $params = new QueryListParams($query, $request, 'uct', 'count(distinct uct)');
        $params->distinct = true;
        $params->orderBy = [
            'uct.txnTime' => 'desc',
            'uct.id' => 'desc',
        ];
        $params->searchFields = [
            'uct.tranDesc',
            'uct.userField2',
        ];
        return $params;
    }

    /**
     * @param UserCardTransaction $entity
     *
     * @return array
     */
    protected function getRowData($entity)
    {
        $amount = Money::formatWhen($entity->getTxnAmountUSD());
        $botm = $entity->isBOTM();

        $sign = '';
        if ( ! $botm) {
            $sign = $entity->getTranCode() === RapidAPI::TRANSACTION_TYPE_CREDIT ? '+' : '-';
        }

        $message = $entity->getTransferMexResultStr();
        $isTransfer = $entity->isTransfer();
        if (!$message && $isTransfer) {
            $message = 'Transfer #' . $entity->getTransferId();
        }
        if (!$message) {
            $message = $entity->parseRealTxnAmountFromDesc();
        }

        $meta = Util::meta($entity);
        $botmData = $meta['botmData'] ?? [];
        $desc = $entity->getSimplifiedDescriptionForAdmin($botmData);

        $tooltip = '';
        $partnerId = '';
        $sourceType = $botmData['transaction_source_type'] ?? null;
        if (in_array($sourceType, [
            'card_transaction',
            'ach_transactions',
            'account_transfer',
        ])) {
            $partnerId = $botmData['related_id'] ?? '';
            $tooltip = 'Token: ' . $partnerId . ', BOTM ' .
                       Util::humanize($sourceType) . ' ID: ' . ($botmData['transaction_id'] ?? '');
        }

        if ($desc === 'Deposited by Agent') {
            $parts = explode(',', $entity->getTranDesc() ?? '');
            if (!empty($parts[0]) && Util::startsWith($parts[0], 'Deposited by')) {
                $desc = $parts[0];
            }
        }

        if (Util::startsWith($entity->getTranDesc(), 'Fund Reversal') && strpos($entity->getTranDesc(), ' - Change account to') !== false) {
          $parts = explode(' - ', $entity->getTranDesc());
          $desc .= isset($parts[2]) ? ' ' . $parts[2] : '';
        }

        $uc = $entity->getUserCard();
        $forceStatus = false;
        $rapydId = '';
        $partner = '';
        $uniTellerId = '';
        $intermexId = '';
        $payoutType = '';
        $status = $entity->getStatus();
        if ($botm) {
            if (!empty($botmData['card_last_four'])) {
                $accountNumber = Util::maskPan($botmData['card_last_four']);
            } else {
                if (!empty($botmData['user_account_id'])) {
                    $accountNumber = 'BOTM ' . $botmData['user_account_id'];
                } else {
                    $accountNumber = 'BOTM ' . $uc->getUser()->ensureConfig()->getBotmUserAccountId();
                }
            }
        } else {
            $rapidData = $meta['rapidData'] ?? [];
            $accountNumber = $rapidData['customerAccountNumber'] ?? '';
        }
        $transfer = null;
        $formId = null;
        if ($isTransfer) {
            $transfer = $entity->getTransfer();
            if ($transfer) {
                $tooltip = $transfer->getPartnerId() ?
                    ucfirst($transfer->getPartner()) . ' ID: ' . $transfer->getPartnerId() :
                    ucfirst($transfer->getError()) ;

                if ($transfer->getPartner() === Transfer::PARTNER_RAPYD) {
                    $rapydId = $transfer->getPartnerId();
                }

                if ($transfer->getPartner() === Transfer::PARTNER_UNITELLER) {
                  $uniTellerId = $transfer->getPartnerId();
                }

                if ($transfer->getPartner() === Transfer::PARTNER_INTERMEX) {
                  // just return the pinnume after the wire is been released
                  $intermexId = Util::meta($transfer, 'wireId') ? $transfer->getPartnerId() : null;
                  $formId = Util::meta($transfer, 'hasCreateIntermexForm1025');
                }

                if (Util::meta($transfer, 'markAsCompleted')) {
                    $status = Transfer::STATUS_COMPLETED;
                }

                $rec = $transfer->getActualRecipientDetails();
                $desc = $rec['fullName'] ?? $rec['firstName'] ?? $desc;
                $accountNumber = isset($rec['accountNumber']) ? Util::maskPanWithBin($rec['accountNumber'], 4) : '';

                if ($transfer->getForcePayoutStatus()) {
                    $forceStatus = true;
                }
                $partner = $transfer->getPartner();
                $payoutType = $transfer->getPayoutType();
                $message = str_replace('Transfer #', $transfer->getPayoutTypeName() . ' #', $message);
            }
        }
        $canRefundPayout = false;
        if (str_contains($entity->getTranDesc(), 'Payout TranID') && !Util::meta($entity, 'refundPayout') && $entity->getTranCode() === 'CREDIT') {
            $canRefundPayout = true;
        }
        if (str_contains($entity->getTranDesc(), ' rapyd_transfer')) {
          $matches = [];
            preg_match('|rapyd_transfer - (\d+)|u', $desc, $matches);
            if (!empty($matches[1])) {
                $transfer = Transfer::find($matches[1]);
            } else {
                $matches = [];
                preg_match('|rapyd_transfer_reverse - (\d+)|u', $desc, $matches);
                if (!empty($matches[1])) {
                  $transfer = Transfer::find($matches[1]);
                }
            }
            if ($transfer) {
              $desc = str_replace('rapyd_', $transfer->getPartner() . '_', $desc);
            }
        }
        $result = [
            'Group' => $entity->getTransferMexGroup(),
            'Type' => $entity->getTransferMexType(),
            'User ID' => $uc->getUser()->getId(),
            'Full Name' =>  $uc->getUser()->getFullName(),
            'Message' => $botmData['card_transaction_result'] ?? $message,
            'Tooltip' => $tooltip,
            'Date & Time' => Util::formatDateTime($entity->getTxnTime()),
            'Transaction ID' => $entity->getId(),
            'Status' => $status,
            'Description' => $desc,
            'Amount' => $sign . $amount,
            'UniTeller ID' => $uniTellerId,
            'Rapyd ID' => $rapydId,
            'Intermex ID' => $intermexId,
            'Partner ID' => $partnerId,
            'Account Number' => $accountNumber,
            'forceStatus' => $forceStatus,
            'partner' => $partner,
            'Merchant' => implode(', ', array_filter([
                $botmData['card_transaction_merchant_description'] ?? null,
                $botmData['card_transaction_terminal_pos_type'] ?? null,
            ])) ?: ($botmData['via'] ?? ''),
            'MCC' => $botmData['card_transaction_merchant_mcc'] ?? '',
            'Address' => $this->getAddress($botmData),
            'Result' => $botmData['card_transaction_decline_reason'] ?? $botmData['category'] ?? '',
            'canRefundPayout' => $canRefundPayout,
            'payoutType' => $payoutType,
            'form1025' => $formId
        ];

        if ($transfer && $transfer->isUnderReviewByRapyd()) {
            $result['Under Review'] = true;
        }

        if ($transfer && $transfer->isFailedByRapydAndDidNotRefundDirectly()) {
            $result['KnownUnhandledRapydError'] = true;
        }

        if ($transfer && $transfer->isFailedToDeductBalance() && ! $transfer->isRefunded()) {
            $result['FailedToDeductBalance'] = true;
        }

        if (RapidService::canRefundApprovedAtmFee($entity)) {
            $result['canRefundAtmFee'] = true;
        }

        if (RapidService::canRefundMaintenanceFee($entity)) {
            $result['canRefundMaintenanceFee'] = true;
        }

        return $result;
    }

    protected function getAddress($botmData) {
        $address = [];
        if (isset($botmData['card_transaction_merchant_country'])) {
          $address['country'] = $botmData['card_transaction_merchant_country'];
        }
        if (isset($botmData['card_transaction_merchant_province']) ) {
          $address['province'] = $botmData['card_transaction_merchant_province'];
        }
        if (isset($botmData['card_transaction_merchant_city'])) {
          $address['city'] = $botmData['card_transaction_merchant_city'];
        }
        return implode(', ', $address);
    }

    /**
     * @Route("/admin/mex/rapyd/view-payout-details/{id}", methods={"GET"})
     * @param Request $request
     * @param         $id
     *
     * @return Response
     */
    public function viewPayoutDetailsAction(Request $request, $id)
    {
        $service = new RapydAPI();
        /** @var ExternalInvoke $ei */
        [$ei, $data] = $service->getPayout($id, true);
        if ($ei && $ei->isFailed()) {
            throw FailedException::fromEi($ei);
        }
        return $this->render('@Core/Common/json.html.twig', [
            'title' => 'Realtime Rapyd Payout Details',
            'id' => $id,
            'data' => $data,
        ]);
    }

    /**
     * @Route("/admin/mex/rapyd/submit-inquiry/{uct}", methods={"POST"})
     * @param Request             $request
     * @param UserCardTransaction $uct
     *
     * @return Response
     */
    public function submitInquiry(Request $request, UserCardTransaction $uct)
    {
        $transfer = $uct->getTransfer();
        if (!$transfer) {
            return new FailedResponse('Transfer not found');
        }

        TransferReviewService::sendInquiryEmail($transfer, $request->get('message'));

        return new SuccessResponse(null, 'The inquiry has been sent.');
    }

    /**
     * @Route("/admin/mex/rapyd/submit-under-review-inquiry/{uct}", methods={"POST"})
     * @param Request             $request
     * @param UserCardTransaction $uct
     *
     * @return Response
     */
    public function submitUnderReviewInquiry(Request $request, UserCardTransaction $uct)
    {
        $transfer = $uct->getTransfer();
        if (!$transfer) {
            return new FailedResponse('Transfer not found');
        }

        $under = $transfer->isUnderReviewByRapyd();
        if (!$under) {
            return new FailedResponse('Transfer is not under review');
        }

        $params = [
            '--transfer' => $transfer->getId(),
        ];
        if (!Util::isDev()) {
            $params['--notify'] = true;
        }
        Util::executeCommand('span:mex:remind-under-review-payout', $params);
        return new SuccessResponse(null, 'The inquiry has been sent.');
    }

    /**
     * @Route("/admin/mex/rapyd/claim-error/{uct}", methods={"POST"})
     * @param Request             $request
     * @param UserCardTransaction $uct
     *
     * @return Response
     */
    public function claimFailed(Request $request, UserCardTransaction $uct)
    {
        /** @var Transfer $transfer */
        $transfer = $uct->getTransfer();
        if (!$transfer) {
            return new FailedResponse('Transfer not found');
        }
        $force = $transfer->getForcePayoutStatus();
        //$force = Util::meta($transfer, 'forcePayoutStatus');
        if ($force !== 'Completed' || $force !== $transfer->getStatus()) {
            return new FailedResponse('Unexpected payout status: ' . $force . '. Please contact devs.');
        }
        foreach ([
            'fixForRapydMaintenance',
            'unhandledRapydError',
        ] as $metaKey) {
            if (Util::meta($transfer, $metaKey)) {
                return new FailedResponse('Skip claiming failed due to the marked key ' . $metaKey);
            }
        }
        Util::updateMeta($transfer, 'forcePayoutStatus');
        $transfer->setForcePayoutStatus(null)->persist();
        RapydQueue::updateTransfer($transfer->getPartnerId(), 'claim_failed');

        return new SuccessResponse(null, 'The payout status is being rechecked. Please refresh later.');
    }

    /**
     * @Route("/admin/mex/members/transactions/refund-failed-transfer-with-deduction/{uct}", methods={"POST"})
     * @param Request             $request
     * @param UserCardTransaction $uct
     *
     * @return Response
     */
    public function refundFailedTransferWithDeduction(Request $request, UserCardTransaction $uct)
    {
        /** @var Transfer $transfer */
        $transfer = $uct->getTransfer();
        if ( ! $transfer) {
            return new FailedResponse('Transfer not found');
        }
        RefundFailedDueToDeductionErrorCommand::precheck($transfer);
        InstantBackgroundCommand::add('span:mex:transfer:refund-failed-due-to-deduction', [
            'id' => $transfer->getId(),
            '--caller' => $this->getUser()->getId(),
        ]);
        return new SuccessResponse(
            null,
            'The system is validating and will refund when matches. Please monitor the Slack alerts and refresh later.'
        );
    }

    /**
     * @Route("/admin/mex/rapyd/refund-for-rapyd-error/{uct}", methods={"POST"})
     * @param Request             $request
     * @param UserCardTransaction $uct
     *
     * @return Response
     */
    public function refundForRapydError(Request $request, UserCardTransaction $uct)
    {
        $this->authSuperAdmin();

        /** @var Transfer $transfer */
        $transfer = $uct->getTransfer();
        if (!$transfer) {
            return new FailedResponse('Transfer not found');
        }
        RapydDisburseService::checkAndRefundUnhandledTransfer($transfer);

        return new SuccessResponse();
    }

    /**
     * @Route("/admin/mex/rapyd/refund-approved-atm-fee/{uct}", methods={"POST"})
     * @param Request             $request
     * @param UserCardTransaction $uct
     *
     * @return Response
     */
    public function refundApprovedAtmFee(Request $request, UserCardTransaction $uct)
    {
        if ( ! RapidService::canRefundApprovedAtmFee($uct)) {
            return new FailedResponse('This fee cannot be refunded!');
        }

        $uc = $uct->getUserCard();
        $baseAPI = ProcessorHub::getBaseAgentProcessor($uc);;
        $amount = min($uct->getTxnAmountUSD(), 100);
        ExternalInvoke::throwIfFailed(
            $baseAPI->loadAmount($uc, $amount, 'ATM Fee - Credit')
        );
        Util::updateMeta($uct, [
            'refundApprovedAtmFee' => true,
        ]);

        $member = $uc->getUser();
        $group = $member->getPrimaryGroup();
        SlackService::$channel = SlackService::CHANNEL_CLIENT;
        SlackService::bell('Refunded the approved ATM fee manually', [
            'employer' => $group->getName(),
            'member' => $member->getSignature(),
            'transaction' => $uct->getId(),
            'amount' => Money::formatUSD($amount),
            'operator' => Util::user()->getSignature(),
        ]);

        return new SuccessResponse();
    }

    /**
     * @Route("/admin/mex/rapyd/refund-maintenance-fee/{uct}", methods={"POST"})
     * @param Request             $request
     * @param UserCardTransaction $uct
     *
     * @return Response
     */
    public function refundMaintenanceFee(Request $request, UserCardTransaction $uct)
    {
        if ( ! RapidService::canRefundMaintenanceFee($uct)) {
            return new FailedResponse('This fee cannot be refunded!');
        }

        $uc = $uct->getUserCard();
        $baseAPI = ProcessorHub::getBaseAgentProcessor($uc);;
        $amount = min($uct->getTxnAmountUSD(), 100);
        ExternalInvoke::throwIfFailed(
            $baseAPI->loadAmount($uc, $amount, 'Maintenance Fee - Credit')
        );
        Util::updateMeta($uct, [
            'refundMaintenanceFee' => true,
        ]);

        $member = $uc->getUser();
        $group = $member->getPrimaryGroup();
        SlackService::$channel = SlackService::CHANNEL_CLIENT;
        SlackService::bell('Refunded the maintenance fee manually', [
            'employer' => $group->getName(),
            'member' => $member->getSignature(),
            'transaction' => $uct->getId(),
            'amount' => Money::formatUSD($amount),
            'operator' => Util::user()->getSignature(),
        ]);

        return new SuccessResponse();
    }

     /**
     * @Route("/admin/mex/uniteller/view-payout-details/{id}", methods={"GET"})
     * @param Request $request
     * @param         $id
     *
     * @return Response
     */
    public function viewUniTellerPayoutDetailsAction(Request $request, $id)
    {
        $transfer = Transfer::findByPartnerId($id, Transfer::PARTNER_UNITELLER);
        if (!$transfer) {
          throw PortalException::temp('Unknown transfer!');
        }
        /** @var ExternalInvoke $ei */
        [$ei, $data] = UniTellerRemittanceService::getTransactionDetail($transfer->getSender(), $id);
        if ($ei && $ei->isFailed()) {
            throw FailedException::fromEi($ei);
        }
        return $this->render('@Core/Common/json.html.twig', [
            'title' => 'Realtime UniTeller Payout Details',
            'id' => $id,
            'data' => $data,
        ]);
    }

     /**
     * @Route("/admin/mex/uniteller/view-payout-receipt/{id}", methods={"GET"})
     * @param Request $request
     * @param         $id
     *
     * @return Response
     */
    public function viewUniTellerPayoutReceiptAction(Request $request, $id)
    {
        $transfer = Transfer::findByPartnerId($id, Transfer::PARTNER_UNITELLER);
        if (!$transfer) {
          throw PortalException::temp('Unknown transfer!');
        }
        /** @var ExternalInvoke $ei */
        $data  = UniTellerRemittanceService::getTransactionReceipt($transfer->getSender(), $id);

        if (isset($data['disclaimer'])) {
          $data['disclaimer'] = preg_replace('/14pt/', '18pt', $data['disclaimer']);
        }
        return $this->render('@Core/Common/json.html.twig', [
            'title' => 'Realtime UniTeller Payout Receipt',
            'id' => $id,
            'data' => $data,
        ]);
    }

    /**
     * @Route("/admin/mex/intermex/view-payout-details/{id}", methods={"GET"})
     * @param Request $request
     * @param         $id
     *
     * @return Response
     */
    public function viewIntermexPayoutDetailsAction(Request $request, $id)
    {
        $transfer = Transfer::findByPartnerId($id, Transfer::PARTNER_INTERMEX);
        if (!$transfer) {
          throw PortalException::temp('Unknown transfer!');
        }
        /** @var ExternalInvoke $ei */
        $api = new IntermexAPI();
        $params = [
          'pinNumbers' => [$transfer->getPartnerId()]
        ];
        [$ei, $data] = $api->getWireStatus($params);
        if ($ei && $ei->isFailed()) {
            throw FailedException::fromEi($ei);
        }
        return $this->render('@Core/Common/json.html.twig', [
            'title' => 'Realtime Intermex Payout Details',
            'id' => $id,
            'data' => $data[0],
        ]);
    }

     /**
     * @Route("/admin/mex/intermex/view-payout-receipt/{id}", methods={"GET"})
     * @param Request $request
     * @param         $id
     *
     * @return Response
     */
    public function viewIntermexPayoutReceiptAction(Request $request, $id)
    {
        $transfer = Transfer::findByPartnerId($id, Transfer::PARTNER_INTERMEX);
        if (!$transfer) {
          throw PortalException::temp('Unknown transfer!');
        }
        /** @var ExternalInvoke $ei */
        $data  = IntermexRemittanceService::getTransactionReceipt($transfer->getSender());

        if (isset($data['disclaimer'])) {
          $data['disclaimer'] = preg_replace('/14pt/', '18pt', $data['disclaimer']);
        }
        return $this->render('@Core/Common/json.html.twig', [
            'title' => 'Realtime Intermex Payout Receipt',
            'id' => $id,
            'data' => $data,
        ]);
    }

     /**
     * @Route("/admin/mex/rapyd/sync-partner-status/{id}", methods={"POST"})
     * @param Request             $request
     * @param UserCardTransaction $uct
     *
     * @return Response
     */
    public function syncPartnerStatusAction(Request $request, UserCardTransaction $uct)
    {
        $transfer = $uct->getTransfer();
        if ($transfer) {
            TransferProvider::updateTransfer($transfer);
        }

        return new SuccessResponse();
    }

    /**
     * @Route("/admin/mex/rapyd/refund-payment/{uct}", methods={"POST"})
     * @param Request             $request
     * @param UserCardTransaction $uct
     *
     * @return Response
     */
    public function refundPayment(Request $request, UserCardTransaction $uct)
    {
        if (Util::meta($uct, 'refundPayout')) {
            return new FailedResponse('This payout cannot be refunded!');
        }
        $desc = $uct->getTranDesc();
        $payoutId = strpos($desc, 'Payout TranID:') !== false ? substr($desc , strpos($desc, 'TranID:') + strlen("TranID:")) : '';
        $uc = $uct->getUserCard();
        $baseAPI = BotmAPI::getForUserCard($uc);
        $amount = $uct->getTxnAmount();
        ExternalInvoke::throwIfFailed(
            $baseAPI->reverseFunds($uc, $amount, 'Refund Payout TranID:' . $payoutId)
        );
        Util::updateMeta($uct, [
            'refundPayout' => true,
        ]);

        $member = $uc->getUser();
        $group = $member->getPrimaryGroup();
        SlackService::$channel = SlackService::CHANNEL_CLIENT;
        SlackService::bell('Refunded the payout', [
            'employer' => $group->getName(),
            'member' => $member->getSignature(),
            'transaction' => $uct->getId(),
            'amount' => Money::formatUSD($amount),
            'operator' => Util::user()->getSignature(),
        ], SlackService::GROUP_DEV);

        return new SuccessResponse();
    }

     /**
     * @Route("/admin/mex/form1025/download", methods={"GET"})
     * @param Request $request
     *
     * @return Response
     */
    public function downloadAction(Request $request) {
      $attachment = $request->get('attachment');
      if (!$attachment) {
          throw PortalException::tempPage('Unknown file!');
      }
      $attachment = Attachment::find($attachment);
      if (!$attachment || !file_exists($attachment->getPath())) {
          throw PortalException::tempPage('File doesn\'t exist!');
      }
      if ($attachment->getCategory() !== 'mex_intermex_form_1025') {
        throw PortalException::tempPage('Access denied to this file!');
      }
      Util::verifyPathSecurity($attachment->getPath());
      $response = new BinaryFileResponse($attachment->getPath());
      $response->setContentDisposition(
          ResponseHeaderBag::DISPOSITION_ATTACHMENT,
          pathinfo($attachment->getPath(), PATHINFO_BASENAME)
      );
      return $response;
  }
}
