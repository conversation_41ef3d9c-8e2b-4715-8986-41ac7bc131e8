<?php


namespace TransferMexBundle\Controller\Admin;

use AppBundle\Command\InstantBackgroundCommand;
use Carbon\Carbon;
use CoreBundle\Entity\Attachment;
use CoreBundle\Entity\CardProgramCardType;
use CoreBundle\Entity\Country;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserCardTransaction;
use CoreBundle\Entity\UserGroup;
use CoreBundle\Entity\Config;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Services\Common\CardService;
use CoreBundle\Utils\Bundle;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\QueryListParams;
use CoreBundle\Utils\Service;
use CoreBundle\Utils\Util;
use Doctrine\ORM\QueryBuilder;
use FaasBundle\Services\BOTM\BotmAPI;
use FaasBundle\Services\FaasMemberService;
use FaasBundle\Services\ProcessorHub;
use PortalBundle\Exception\DeniedException;
use PortalBundle\Exception\PortalException;
use SalexUserBundle\Entity\User;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use TransferMexBundle\Entity\EmployerPayout;
use TransferMexBundle\Entity\ImportMembersRecord;
use TransferMexBundle\Entity\ImportPayoutRecord;
use TransferMexBundle\Entity\UserTransferMexTrait;
use TransferMexBundle\Services\EmployerService;
use TransferMexBundle\Services\MemberService;
use TransferMexBundle\Services\RapidAPI;
use TransferMexBundle\Services\RapidService;
use TransferMexBundle\TransferMexBundle;
use UsUnlockedBundle\Services\IDologyService;
use TransferMexBundle\Entity\PayrollAccess;

class EmployeesController extends AgentsController
{
	  use UserTransferMexTrait;
    public const CACHE_REVERSE_KEY = 'Employer_Reverse_Employee';
    /** @var User */
    public $employer;
    /** @var UserGroup */
    public $group;
    public $isExport = false;
    public function __construct()
    {
        parent::__construct();

        $this->employer = $this->getGroupAdminUser($this->user);
        $this->group = $this->employer->getAdminGroup();
    }

    protected function getAccessibleRoles()
    {
        return [
            Role::ROLE_TRANSFER_MEX_EMPLOYER,
            Role::ROLE_TRANSFER_MEX_EMPLOYER_ADMIN,
            Role::ROLE_FAAS_CLIENT,
            Role::ROLE_FAAS_CLIENT_ADMIN,
            Role::ROLE_TRANSFER_MEX_EMPLOYER_ADMIN_ACH
        ];
    }

    /**
     * @Route("/admin/mex/employer/employees/list/{page}/{limit}", methods={"GET"}, defaults={"page" = 1,"limit" = 10})
     * @param Request $request
     * @param int     $page
     * @param int     $limit
     *
     * @return SuccessResponse
     */
    public function search(Request $request, $page = 1, $limit = 10)
    {
        // if ($this->user->inTeam(Role::ROLE_TRANSFER_MEX_EMPLOYER_ADMIN_ACH)) {
        //   $result = [
        //       'count' => 0,
        //       'data' => [],
        //       'qucik' => []
        //   ];
        //   return new SuccessResponse($result);
        // }
        $resp = parent::search($request, $page, $limit);
        $result = $resp->getResult();

        // $result['quick']['total'] = $this->query($request)->select('count(u)')
        //     ->distinct()
        //     ->getQuery()
        //     ->getSingleScalarResult();
        // $average = Util::percent($members, $result['quick']['total']);

        // $result['quick']['average'] = ;

        $all = $this->query($request)
                ->getQuery()
                ->getResult();
        if ($request->get('paySearch')) {
          $list = [];
          // $group = $this->user->getAdminGroup();
          foreach($result['data'] as $item) {
            if ($this->platform->isLoadConfigureEnabled()) {
              if (($this->group->getMemberLoadType() || !$this->group->getMemberLoadType() && !$item['Last Payout ID']) && ($item['Card Status'] === 'Active' || $this->platform->isFaasPlatforms())) {
                if ($item['Account Status'] === 'Active' || $item['Account Status'] === 'Onboarded') {
                  $list[] =  $item;
                }
              }
            } else {
              if ($item['Card Status'] === 'Active' || $this->platform->isFaasPlatforms()) {
                if ($item['Account Status'] === 'Active' || $item['Account Status'] === 'Onboarded') {
                  $list[] =  $item;
                }
              }
            }
          }
          $result['data'] = [];
          if ( $this->group->isBOTM()) {
            foreach ($list as $item) {
              if (BotmAPI::isAccountNumberValid($item['Account Number'])) {
                $result['data'][] = $item;
              }
            }
          } else {
            $result['data'] = $list;
          }
          $result['count'] = count($result['data']);
        }
        $result['quick']['migrated'] = $this->query($request)
                                            ->andWhere(Util::expr()->like('uc.accountNumber', ':accountNumber'))
                                            ->setParameter('accountNumber', '%-%')
                                            ->select('count(distinct u)')
                                            ->distinct()
                                            ->getQuery()
                                            ->getSingleScalarResult();
        $result['quick']['bigBalanceMember'] = $this->query($request)
                                            ->andWhere(Util::expr()->gte('uc.balance', ':balance'))
                                            ->setParameter('balance', Config::get('employer_balance_filter_threshold', 600000) * 1)
                                            ->select('count(distinct u)')
                                            ->distinct()
                                            ->getQuery()
                                            ->getSingleScalarResult();
        $result['quick']['totalBalance'] = array_sum(array_map(function($val){return $val->getCurrentPlatformCard() ? $val->getCurrentPlatformCard()->getBalance() : 0 ;}, $all));
        $result['quick']['avgBalance'] = $result['quick']['total'] ? round($result['quick']['totalBalance'] / $result['quick']['total']) : 0;
        $result['quick']['lastImport'] = MemberService::getLastImport($this->getGroupAdminUser($this->user), Util::cardProgram());
        $result['quick']['lastImportPayout'] = MemberService::getLastImportPayout($this->getGroupAdminUser($this->user), $this->platform, $this->user);
        return new SuccessResponse($result);
    }

    protected function query(Request $request)
    {
        $query = $this->em->getRepository(User::class)
            ->createQueryBuilder('u')
            ->join('u.teams', 't')
            ->join('u.cards', 'uc')
            ->join('u.userGroups', 'g')
            ->join('g.adminConfigs', 'ac')
            ->join('ac.user', 'e')
            ->where(Util::expr()->in('t.name', ':roles'))
            ->setParameter('roles', Bundle::getMemberRoles())
            ->andWhere(Util::expr()->eq('e.id', ':groupId'))
            ->setParameter('groupId', $this->getGroupAdminUser($this->user)->getId() );

          $not = $request->get('__not');
          $notIsBotm = '';
          if (is_array($not)) {
            foreach($not as $key => $value) {
              if ($key == 'is_botm') {
                $notIsBotm = $value;
              }
            }
          }
          $isBotm = $request->get('is_botm');
          if ( $isBotm) {
            $isBotm == 'yes' ? $query->andWhere(Util::expr()->like('uc.accountNumber',':isBotm')) : $query->andWhere(Util::expr()->notLike('uc.accountNumber', ':isBotm'));
            $query->setParameter('isBotm', '%-%');
          }

          if ($notIsBotm) {
            $notIsBotm == 'yes' ? $query->andWhere(Util::expr()->notLike('uc.accountNumber',':notIsBotm')) : $query->andWhere(Util::expr()->like('uc.accountNumber', ':notIsBotm'));
            $query->setParameter('notIsBotm', '%-%');
          }
          if ($request->get('balanceFilter')) {
            $balanceFilter = Config::get('employer_balance_filter_threshold', 600000);
            $query->andWhere(Util::expr()->gte('uc.balance',':balance'));
            $query->setParameter('balance', $balanceFilter * 1);
          }
        return $this->queryByDateRange($query, 'u.createdAt', $request);
    }

    protected function queryListParams(QueryBuilder $query, Request $request)
    {
        $params = new QueryListParams($query, $request, 'u', 'count(distinct u)');
        $params->distinct = true;
        $params->orderBy = [
            'u.id' => 'desc',
        ];
        $params->searchFields = [
            'u.name',
            'u.email',
            'g.name',
            'u.id',
            'u.title'
        ];
        return $params;
    }

    /**
     * @param User $entity
     *
     * @return array
     */
    protected function getRowData($entity)
    {
        $str = $this->platform->isFaasPlatforms() ? 'Member' : 'Employee';
        $uc = $entity->getCurrentPlatformCard();
        $an = $uc ? $uc->getAccountNumber() : null;
        // get last payout from cache
        $lastPayout = Data::getArray('employee_last_payout_' . $entity->getId());
        $meta = [];
        if ($lastPayout) {
          $meta = $lastPayout['meta'];
        }
        // skip card status check when employer use Rapid but the member has migrated card to BOTM
        $skipStatus = false;
        // $group = $this->user->getAdminGroup();
        $retired = $uc ? Util::meta($uc, 'retiredRapidAccount') : null;
        if ($this->group->isRapid() && BotmAPI::isAccountNumberValid($an) && $retired ) {
          $skipStatus = true;
        }
        // we can load to botm account when the card is inactive
        if ($uc && $entity->ensureConfig()->getBotmUserAccountId() && Config::get('load_to_botm_account_flag')) {
          $skipStatus = true;
        }

        if ($this->group->isACH() || !$lastPayout) {
          $lastPayout = Data::getArray('employee_last_ach_' . $entity->getId());
        }

        $accountNumber = $uc ? $uc->getAccountNumber() : '';
        if ($this->isExport && $uc) {
          $api = $uc->getProcessorApiImpl();
          $accountNumber =  " " . $api->getDirectDepositNumber($uc);
        }
        return [
          'Employee ID' => $entity->getId(),
          'User ID' => $entity->getId(),
          'Member ID' => $entity->getId(),
          'External Employee ID' => $entity->getExternalId(),
          'Email' => $entity->getEmail(),
          'First Name' => $entity->getFirstName(),
          'Last Name' => $entity->getLastName(),
          'User Type' => $this->platform->isFaasPlatforms() ? 'Member' : 'Employee',
          'Create Date' => Util::formatDateTime($entity->getCreatedAt()),
          'Phone' => $entity->getMobilephone(),
          'Address' => $entity->getSimpleFullAddress(),
          'Card Status' => $an && $uc->isIssued() ? ucfirst($uc->getStatus()) : '',
          'Barcode Number' => $uc ? $uc->getAccountNumber() : '',
          'Account Number' => $accountNumber,
          'Card Proxy-Value number' => $uc ? $uc->getAccountNumber() : '',
          'Routing Number' => $uc && Util::isServer() ? $uc->getProcessorApiImpl()->getRoutingNumber($uc) : '',
          'Enrolled' => $an ? ($uc->isIssued() ? 'Yes' : 'No') : '',
          'Last Payout ID' => $lastPayout ? $lastPayout['id'] : null,
          'Last Payout Date' => $lastPayout ? $lastPayout['txnTime'] : '',
          'Last Payout Amount' => $lastPayout ? ( !empty($meta['LastDepositAmount']) ?  Money::formatWhen($meta['LastDepositAmount']) :  Money::formatWhen($lastPayout['txnAmount'])) : '',
          'Last Payout Amount Value' => $lastPayout ? ( !empty($meta['LastDepositAmount']) ? $meta['LastDepositAmount'] / 100 : $lastPayout['txnAmount'] / 100 ) : 0,
          'Balance' => $an ? Money::formatWhen($uc->getBalance()) : '',
          'Account Status' => $this->platform->isFaasPlatforms() ? ($entity->getFaasStatus($this->platform->isKYCRequired(), false)) : ($entity->getStatus() === User::STATUS_ACTIVE ? 'Active' : 'Inactive'),
          'App Activation' => $entity->getLastLogin() ? 'Active' : 'Inactive',
          'SkipCheckCardStatus' => Data::get('card_on_hold_by_password_' . $entity->getId()) ? true : $skipStatus,
          'Migrated to new card' => $uc && BotmAPI::isAccountNumberValid($uc->getAccountNumber()) ? 'Yes' : 'No'
        ];
    }


    protected function validateUser(User $user = null)
    {
        if (!$user || !$user->inTeams(Bundle::getMemberRoles())) {
            throw PortalException::temp('Unknown user!');
        }
    }

    /**
     * @Route("/admin/mex/employer/emlpoyee/{user}/detail")
     * @param User $user
     *
     * @return FailedResponse|SuccessResponse
     */
    public function detailAction(User $user)
    {
        $this->validateUser($user);
        return new SuccessResponse($this->getRowData($user));
    }

    /**
     * @Route("/admin/mex/employer/employees/export", methods={"POST"})
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function export(Request $request)
    {
        $title = 'TransferMex Employees';
        if ($this->platform->isFaasPlatforms()) {
          $title = $this->cardProgram->getName() . ' ' . $this->getGroupAdminUser($this->user)->getEmployerName() . ' Members';
        }
        $this->isExport = true;
        return $this->commonExport($request, $title, [
          'User ID' => 20,
          'Email' => 20,
          'First Name' => 20,
          'Last Name' => 20,
          'User Type' => 20,
          'External Employee ID' => 20,
          'Last Payout Date' => 20,
          'Last Payout Amount' => 20,
          'Card Proxy-Value number' => 30,
          'Card Status'  => 20,
          'Account Number' => 30,
          'App Activation' => 20,
          'Account Status' => 20
        ]);
    }

     /**
     * @Route("/admin/mex/employer/employees/exportTemplate", methods={"POST"})
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function exportTemplate(Request $request)
    {
        if ($this->group->getFundingType() == UserGroup::FUNDING_TYPE_ACH) {
          throw new DeniedException();
        }
        $title = 'TransferMex Payout Template';
        $str = 'Employee ID';
        if ($this->platform->isFaasPlatforms()) {
          $title = $this->cardProgram->getName() . ' ' . $this->getGroupAdminUser($this->user)->getEmployerName() . ' Payouts Template';
          $str = 'Member ID';
        }
        $headers = [
           $str => 20,
          'First Name'   => 20,
          'Last Name' => 20,
          'Account Number' => 20,
          'Payout Amount' => 20
        ];

        $all = $this->query($request)
                ->getQuery()
                ->getResult();

        // $group = $this->user->getAdminGroup();
        if (count($all) > 100 || Util::isStaging()) {
          InstantBackgroundCommand::add('span:mex:generate-payout-template', [
            '--employer' => $this->employer->getId()
          ]);
          return new SuccessResponse('', 'The amount of data is too large, it will take a long time to generate the payout template file, and the payout template file will be sent to you by email later.');
        }
        $res = [];
        foreach($all as $entity) {
          $item = $this->getRowData($entity);
          if ($this->platform->isLoadConfigureEnabled()) {
            if (($this->group->getMemberLoadType() || !$this->group->getMemberLoadType() && !$item['Last Payout ID']) && (Data::get('card_on_hold_by_password_' . $item['User ID']) || $item['Card Status'] === 'Active' || $this->platform->isFaasPlatforms())) {
              if ($item['Account Status'] === 'Active' || $item['Account Status'] === 'Onboarded' || Data::get('card_on_hold_by_password_' . $item['User ID'])) {
                $res[] =  [
                  $str => $item['User ID'],
                  'First Name' => $item['First Name'],
                  'Last Name' => $item['Last Name'],
                  'Account Number' => $item['Account Number'],
                  'Payout Amount' => null
                ];
              }
            }
          } else {
            if ($item['Card Status'] === 'Active' || $this->platform->isFaasPlatforms() || Data::get('card_on_hold_by_password_' . $item['User ID'])) {
              if ($item['Account Status'] === 'Active' || $item['Account Status'] === 'Onboarded' || Data::get('card_on_hold_by_password_' . $item['User ID'])) {
                $res[] =  [
                  $str => $item['User ID'],
                  'First Name'   => $item['First Name'],
                  'Last Name' => $item['Last Name'],
                  'Account Number' => $item['Account Number'],
                  'Payout Amount' => null
                ];
              }
            }
          }
        }

        if ( $this->group->isBOTM()) {
          $list = [];
          foreach ($res as $item) {
            if (BotmAPI::isAccountNumberValid($item['Account Number'])) {
              // if (in_array($group->getId(), Config::array('use_account_number_for_payout'))) {
              $user = User::find($item['Employee ID']);
              $uc = $user->getCurrentPlatformCard();
              $api = $uc->getProcessorApiImpl();
              $item['Account Number'] = " " . $api->getDirectDepositNumber($uc);
              // }
              $list[] = $item;
            }
          }
          $res = $list;
        }

        $filename = $title . '_' . $request->get('query_timestamp', time());
        $destination = Util::uploadDir('report') . $filename . '.xlsx';
        $excel = $this->loadOrCreateExcel($destination, $filename);

        $excel = $this->generateSheetAppendToExcel($excel, 0 . ' ~ ' . (5000),
            $headers, $res, static function ($col, $row, $excel, $title) {
                return $row[$title] ?? '';
            }, []);
        $this->saveExcelTo($excel, $destination);

        return new SuccessResponse('report/' . $filename . '.xlsx');
    }

     /**
     * @Route("/admin/mex/employer/employees/exportMemberTemplate", methods={"POST"})
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function exportMemberTemplate(Request $request)
    {
        if ($this->group->getFundingType() == UserGroup::FUNDING_TYPE_ACH) {
          throw new DeniedException();
        }
        $title = 'TransferMex Member Template';
        if ($this->platform->isFaasPlatforms()) {
          $title = $this->cardProgram->getName() . ' ' . $this->getGroupAdminUser($this->user)->getEmployerName() . ' Member Template';
        }
        $headers = [
          'First Name'   => 20,
          'Last Name' => 20,
          'Date of Birth' => 20,
          'Street Address' => 30,
          'City' => 20,
          'State/Province' => 20,
          'Postal Code' => 20,
          'Country' => 20,
          'Email' => 20,
          'Mobile Phone' => 20
        ];

        $res = [
          [
          'First Name'   => 'Morales Ortiz',
          'Last Name' => 'Javier Eduardo',
          'Date of Birth' => '06/27/1980',
          'Street Address' => 'Calle Maria del Carmen Godoy Numero 330 Fraccionamiento Purisima Del Jardin',
          'City' => 'Irapuato',
          'State/Province' => 'Guanajuato',
          'Postal Code' => '36555',
          'Country' => 'Mexico',
          'Email' => '<EMAIL>',
          'Mobile Phone' => 7678887676
          ]
        ];


        $filename = $title . '_' . $request->get('query_timestamp', time());
        $destination = Util::uploadDir('report') . $filename . '.xlsx';
        $excel = $this->loadOrCreateExcel($destination, $filename);

        $excel = $this->generateSheetAppendToExcel($excel, 0 . ' ~ ' . (5000),
            $headers, $res, static function ($col, $row, $excel, $title) {
                return $row[$title] ?? '';
            }, []);
        $this->saveExcelTo($excel, $destination);

        return new SuccessResponse('report/' . $filename . '.xlsx');
    }

     /**
     * @Route("/admin/mex/employee/{user}/id-download")
     * @param Request $request
     * @param User    $user
     *
     * @return FailedResponse|SuccessResponse
     */
    public function idDownloadAction(Request $request, User $user)
    {
        $uiv = $user->getIdVerify();
        if (!$uiv) {
            $suffix = Util::meta($user, 'idScanManual') ? 'The KYC was manually approved.' : '';
            return new FailedResponse('No KYC Documents for this member. ' . $suffix);
        }
        $front = $uiv->frontImage();
        $back = $uiv->backImage();
        if (!$front && !$back) {
            return new FailedResponse('Empty KYC Documents for this member.');
        }
        $files = [];
        if ($front) {
            $files[] = $front->getUrl();
        }
        if ($back) {
            $files[] = $back->getUrl();
        }
        return new SuccessResponse($files);
    }

    /**
     * @Route("/admin/mex/employer/employees/pay", methods={"POST"})
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function payEmployee(Request $request)
    {
      if ($this->group->getFundingType() == UserGroup::FUNDING_TYPE_ACH || Util::meta($this->user, 'readOnlyAdmin')) {
        throw new DeniedException();
      }
      $em = Util::em();
      $user = $this->getGroupAdminUser($this->user);
      $uc = EmployerService::getMexDummyCard($user);
      // $uc = $user->getCurrentPlatformCard();
      //        $uc = $this->validatePinToken($request, $user);
      // if (!$uc->isActive()) {
      //     return new FailedResponse('The card is not active!');
      // }
      $totalSub = $request->get('total');
      $total = Money::normalizeAmount(isset($totalSub['value']) && $totalSub ? $totalSub['value'] : 0, 'USD');
      // $balance = $uc->getBalance();
      $api = ProcessorHub::getForUserGroupAdmin($user);
      $balance = ProcessorHub::updateAgentBalance($api);
      if ($total <= 0) {
        return new FailedResponse('Please enter the correct amount.');
      }
      // $balance -= EmployerService::getHoldingPayoutsAmount($user);

      // Log::debug('Pay debug');
      // Log::debug($balance);
      // Log::debug($total);
      if ($balance < $total) {
        return new FailedResponse('The ' . strtolower($this->getEmployerText()) . ' does not have enough money to pay this');
      }
      // create the payout record
      $employees = $request->get('employees');
      // Log::debug(array_sum(array_map(function($val) { return $val['amount'];}, $employees)));
      if ($total !==  Money::normalizeAmount(array_sum(array_map(function($val) { return $val['amount'];}, $employees)), 'USD')) {
        return new FailedResponse('The total does not match, please check.');
      }

      foreach ($employees as $k => $item) {
        $employees[$k]['Last Payout ID'] = null;
        $employees[$k]['Last Payout Date'] = null;
        $employees[$k]['Last Payout Amount'] = null;
        $employees[$k]['Last Payout Amount Value']= null;
        $employees[$k]['Balance'] = null;
      }

      $signleStr = md5(json_encode($employees));

      if (Data::get('payroll_signle_' . $signleStr)) {
        return new FailedResponse('The same data has been created within a minute, please confirm again and try again.');
      }
      Data::set('payroll_signle_' . $signleStr, 1, true, 60);
      $error = [];
      $success = [];
      foreach ($employees as $item) {
        $employee = User::find($item['User ID']);

        if ($employee) {
          $employeeUc = $employee->getCurrentPlatformCard();
          //  check if the employee has Botm card when use Rapid
          // if ( $employeeUc->isBotmCard() && !$this->group->isBOTM()) {
          //   $item['resaon'] = 'The card provider used by the member is chanegd but the employer still use the old card provider, please check and change card for the member!';
          //   $error[] = $item;
          //   continue;
          // }

          // check if the employee has Rapid card when use BOTM
          // if ( !$employeeUc->isBotmCard() && $this->group->isBOTM()) {
          //   $item['resaon'] = 'The card provider used by the employer is the nerw card provider but the member still use the card with the old card provider, please check and change card for the member!';
          //   $error[] = $item;
          //   continue;
          // }

          $employerPayout = new EmployerPayout();
          $employerPayout->setEmployee($employee)
                         ->setAmount( Money::normalizeAmount($item['amount'], 'USD'))
                         ->setStatus(EmployerPayout::PAYOUT_INIT)
                         ->setEmployer($this->getGroupAdminUser($this->user))
                         ->setPlatform(Util::platform());
          $transaction = new UserCardTransaction();
          $transaction->setUserCard($uc)
                         ->setTxnAmount( Money::normalizeAmount($item['amount'], 'USD'))
                         ->setStatus(EmployerPayout::PAYOUT_INIT)
                         ->setProductName(UserCardTransaction::PRODUCT_TRANSFER_MEX)
                         ->setTranCode('Pay_employee');
                         // ->setEmployer($this->getUser());

          if ($item['postingDate'] === 'Immediately') {
            $employerPayout->setType(true);
            $transaction->setTxnTime(Carbon::now());
          } else {
            $transaction->setTxnTime(Util::toUTC(new Carbon($request->get('postingDate') . ' 4:00:00', Util::tzEST())));
            $employerPayout->setType(false)
                           ->setPostTime(Util::toUTC(new Carbon($request->get('postingDate') . ' 4:00:00', Util::tzEST())));
          }
          $employerPayout->persist();
          // $em->persist($employerPayout);
          // Log::debug($employerPayout->getId());
          $transaction->setTranDesc($this->getEmployeeText(). ' Payout ID:' . $employerPayout->getId() . ';' . $this->getEmployeeText() . ':' . $employee->getId());
          // if ($employeeUc->isBotmCard()) {
            $transaction->setActualTranCode('botm');
          // }
          $transaction->persist();
          $employerPayout->setUserCardTransaction($transaction)
              ->persist();
          $success[] = $item;
        } else {
          $error[] = $item;
        }
      }
      if (count($error)) {
        return new FailedResponse('Some employees used the card provider that did not match the card provider of the employer, please check and change cards for the members!');
      }
      $em->flush();
      if ($request->get('postingDate') === 'Immediately') {
        if(!Data::get('Mex_employer_pay_employee_confirm')) {
          // update master balance and user balance
          // Log::debug('Com member load confirm');
          Data::set('Mex_employer_pay_employee_confirm', 1, true);
          Service::sendAsync('/t/cron/mex/rapid/execute-all-pay-employee');
        }
      }
      return new SuccessResponse(['success' => $success, 'error' => $error]);
    }

     /**
     * @Route("/admin/mex/employee/checkPayEmployee", methods={"POST"})
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function importAction(Request $request)
    {
        if ($this->group->getFundingType() == UserGroup::FUNDING_TYPE_ACH || Util::meta($this->user, 'readOnlyAdmin')) {
          throw new DeniedException();
        }
        $aid = $request->get('attachment');
        $skipHashCheck = $request->get('skipHashCheck');
        if (!$aid) {
            return new FailedResponse('Invalid parameters!');
        }
        $attachment = Attachment::find($aid);
        $categoryName = $this->platform->isFaasPlatforms() ? 'faasPayMembers' : 'transferMexPayEmployees';
        if (!$attachment || $attachment->getCategory() !== $categoryName) {
            return new FailedResponse('Unknown or invalid file!');
        }
        $path = $attachment->prepareForLocalRead();
        if (!Util::endsWith(strtolower($path), '.xlsx') && $this->platform->isFaasPlatforms()) {
            return new FailedResponse('Only XLSX file is supported!');
        }

        if (!Util::endsWith(strtolower($path), '.csv') && !Util::endsWith(strtolower($path), '.xlsx') && !Util::endsWith(strtolower($path), '.dat') && !Util::endsWith(strtolower($path), '.mf') && !Util::endsWith(strtolower($path), '.txt')  && $this->platform->isTransferMex()) {
          return new FailedResponse('Only XLSX，CSV, TXT and NACHA files is supported!');
        }


        Util::longRequest();
        if (Util::endsWith(strtolower($path), '.dat') || Util::endsWith(strtolower($path), '.mf') || Util::endsWith(strtolower($path), '.txt')) {
            $employees = MemberService::checkPayoutFromNachaFile($path, $this->platform, $this->getEmployeeText());
        } else if (Util::endsWith(strtolower($path), '.csv')) {
            if (in_array($this->getGroupAdminUser($this->user)->getId(), TransferMexBundle::customEmployers())) {
              $employees = MemberService::checkPayoutFromCustomCsvFile($path, $this->platform, $this->getEmployeeText(), false, null, TransferMexBundle::getCustomFileType($this->getGroupAdminUser($this->user)->getId()));
            } else {
              $employees = MemberService::checkPayoutFromCsvFile($path, $this->platform, $this->getEmployeeText());
            }
        } else {
            $employees = MemberService::checkPayoutFromExcleFile($path, $this->platform, $this->getEmployeeText());
        }
        if (isset($employees['type']) && $employees['type'] === 'cronJob') {
            $type = 'XLS';
            if (Util::endsWith(strtolower($path), '.dat') || Util::endsWith(strtolower($path), '.mf')  || Util::endsWith(strtolower($path), '.txt')) {
              $type = 'NACHA';
            } else if (Util::endsWith(strtolower($path), '.csv')) {
              $type = 'CSV';
            }
            // store file path for cron and statr cron job
            if (ImportPayoutRecord::findByfileName($attachment->getName())) {
                return new FailedResponse('The name ' . $attachment->getName(). ' file has been imported before');
            }
            // store file path for cron and statr cron job
            $sameHash = ImportPayoutRecord::findByHash(hash_file('md5', $path));
            if ($sameHash && !$skipHashCheck) {
              return new FailedResponse('This file ' . $attachment->getName() . ' is the same as the previously uploaded file ' . $sameHash->getFileName() . ', please check and confirm or unchecked flie hash duplicate check!');
            }

            // $checkList =  is_array($employees['data']) ? $employees['data'] : [];
            // // check if the employer has Rapid card when use BOTM
            // $hasRapidCard = EmployerService::chechEmployerHasBotmCard($this->getGroupAdminUser($this->user), $checkList, $type, false);
            // if ( $hasRapidCard && $this->group->isBOTM()) {
            //   return new FailedResponse('The employer has changed the card provider but there are some employees who still use the old card provider, please check and change cards for the members!');
            // }


            // //  check if the employer has Botm card when use Rapid
            // $hasBotmCard = EmployerService::chechEmployerHasBotmCard($this->getGroupAdminUser($this->user), $checkList, $type);
            // if ( $hasBotmCard && !$this->group->isBOTM()) {
            //   return new FailedResponse('Some employees used the card provider that did not match the card provider of the employer, please check and change cards for the members!');
            // }
            $uploadedFlag = 'uploaded_payroll_file_flag_' . $this->getGroupAdminUser($this->user)->getId();
            if (!$request->get('confirm', false)) {
                $employees['fileType'] = $type;
                if (!is_array($employees['data'])) {
                  return new FailedResponse('The name' . $attachment->getName(). ' file import failed with special characters on line ' . ($employees['data'] + 1) .', please check and retry!');
                }
                if ($type === 'NACHA') {
                  $employees['total'] = 0;
                  $employees['total'] = array_sum(array_column($employees['data'], 'amount'));
                }
                $totalAmount = $request->get('totalAmount');
                $employees['rightTotal'] =  Money::normalizeAmount($totalAmount, 'USD') == $employees['total'];
                $employees['bigTotal'] =  Money::normalizeAmount($totalAmount, 'USD') < $employees['total'];
                $employees['customFields'] = TransferMexBundle::getCustomFileFields($this->getGroupAdminUser($this->user)->getId());
                $employees['isCustomCsv'] = in_array($this->getGroupAdminUser($this->user)->getId(), TransferMexBundle::customEmployers()) ? TransferMexBundle::getCustomFileType($this->getGroupAdminUser($this->user)->getId()) : '';
                // add a flag to mark the user has upload a payroll file
                Data::set($uploadedFlag, $attachment->getId());
                return new SuccessResponse($employees);
            }
            $last = MemberService::getLastImportPayout($this->getGroupAdminUser($this->user), $this->platform, $this->user);
            if ($last && $last['status'] === 'In Progress') {
                return new FailedResponse('A file is being processed');
            }
            //set uniqid flag
            if (Data::get('import_payroll_file_' . $this->getGroupAdminUser($this->user)->getId())) {
              return new FailedResponse('A file is being processed');
            }
            Data::set('import_payroll_file_' . $this->getGroupAdminUser($this->user)->getId(), true);
            $importRecord = new ImportPayoutRecord();
            $importRecord->setPath($path)
                        ->setType($type)
                        ->setFileName($attachment->getName())
                        ->setPlatform($this->platform)
                        ->setEmployer($this->getGroupAdminUser($this->user))
                        ->setFailedCount(0)
                        ->setSuccessCount(0)
                        ->setHash(hash_file('md5', $path))
                        ->setStatus(ImportPayoutRecord::IMPORT_INIT);
            if ($request->get('postingDate') !== 'Immediately') {
              $importRecord->setExecuteStatus(ImportPayoutRecord::EXECUTE_PENDING)
                           ->setPostTime( Util::toUTC(new Carbon($request->get('postingDate') . ' 4:00:00', Util::tzEST())));
            } else {
              $importRecord->setExecuteStatus(ImportPayoutRecord::EXECUTE_INPROGRESS);
            }
            $importRecord->persist();
            // Remove the upload flag
            Data::del($uploadedFlag);
            Data::del('import_payroll_file_' . $this->getGroupAdminUser($this->user)->getId());
            // Service::sendAsync('/t/cron/mex/import');
            Service::sendAsync('/t/cron/mex/import/payout/' . $importRecord->getId());
            return new SuccessResponse(['step' => 'Import processing', 'id' => $importRecord->getId()]);
        }

        return new SuccessResponse($employees);
    }

     /**
     * @Route("/admin/mex/employer/employees/editDeposit", methods={"POST"})
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function editDepositAction(Request $request)
    {
        if ($this->group->getFundingType() == UserGroup::FUNDING_TYPE_ACH || Util::meta($this->user, 'readOnlyAdmin')) {
          throw new DeniedException();
        }
        $user = $this->getGroupAdminUser($this->user);
        $uc = EmployerService::getMexDummyCard($user);
        $id = $request->get('id');
        if (!$id) {
          return new FailedResponse('Unknown transaction!');
        }
        $uct = UserCardTransaction::find($id);
        if (!$uct) {
          return new FailedResponse('Unknown transaction!');
        }
        $lastDeposit = Money::normalizeAmount($request->get('amount', 0), 'USD');
        if ($lastDeposit <= 0) {
            return new FailedResponse('Invalid amount!');
        }
        $employee = User::find($request->get('employee'));
        if (!$employee) {
          return new FailedResponse('Invalid ' . $this->getEmployeeText() . '!');
        }
        $employeeUc = $employee->getCurrentPlatformCard();
        $employeeOldBalance = $employeeUc->getBalance() ?: 0;

        // $meta = Util::meta($uct);
        // if (empty($meta['LastDepositAmount'])) {
        $amount = $uct->getTxnAmount() - $lastDeposit;
        // } else {
        //   $amount = $meta['LastDepositAmount'] - $lastDeposit;
        // }

        if ($employeeOldBalance < $amount) {
          return new FailedResponse('The ' . $this->getEmployeeText() . ' balance is not enough to refund!');
        }

        $transaction = new UserCardTransaction();
        $transaction->setUserCard($uc)
                       ->setTxnAmount( $amount)
                       ->setStatus(EmployerPayout::PAYOUT_INIT)
                       ->setTranCode('Reverse_employee')
                       ->setProductName(UserCardTransaction::PRODUCT_TRANSFER_MEX)
                       ->setTxnTime(Carbon::now())
                       ->setTranDesc($this->getEmployeeText() . ':' . $employee->getId())
                       ->persist();
        $res = RapidService::ReversePayout($uct, $amount, $employee, $transaction);
        if (is_string($res)) {
          $transaction->setStatus(EmployerPayout::PAYOUT_CANCEL)->persist();
          return new FailedResponse($res);
        }

        $lastPayout = Data::getArray('employee_last_payout_' . $employee->getId());

        $lastPayout['txnAmount'] = $lastDeposit;

        Data::setArray('employee_last_payout_' . $employee->getId(), $lastPayout);

        // Util::updateMeta($uct, [
        //   'LastDepositAmount' => $lastDeposit
        // ]);
        $uct->setTxnAmount($lastDeposit)->persist();
        $transaction->setAccountStatus(EmployerPayout::PAYOUT_SYNC_PENDING)->persist();

        // if the last deposit > 0, remove the cache and can edit it again
        if ($lastDeposit) {
          Data::del(self::CACHE_REVERSE_KEY . $uct->getId(), true);
        }
        return new SuccessResponse();
    }

     /**
     * @Route("/admin/faas/base/members/import", methods={"POST"})
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function importMemberAction(Request $request)
    {
        if ($this->group->getFundingType() === UserGroup::FUNDING_TYPE_ACH || Util::meta($this->user, 'readOnlyAdmin')) {
          throw new DeniedException();
        }
        $aid = $request->get('attachment');
        $type = $request->get('type');
        $memberInfo = $request->get('member');
        if (!$type) {
          return new FailedResponse('Invalid parameters!');
        }
        $members = [];
        if ($type === 'import') {
          if (!$aid) {
            return new FailedResponse('Invalid parameters!');
          }
          $attachment = Attachment::find($aid);
          $categoryName = 'FaasImportMembers';
          if (!$attachment || $attachment->getCategory() !== $categoryName) {
              return new FailedResponse('Unknown or invalid file!');
          }
          $path = $attachment->prepareForLocalRead();
          if (!Util::endsWith(strtolower($path), '.xlsx')) {
              return new FailedResponse('Only XLSX file is supported!');
          }

          Util::longRequest();

          $members = MemberService::checkMemberFromCsvFile($path, Util::cardProgram(), $this->getGroupAdminUser($this->user));
          if (is_string($members) && $members === 'cronJob') {
            $last = MemberService::getLastImport($this->getGroupAdminUser($this->user), Util::cardProgram());
            if ($last && $last['status'] === 'In Progress') {
              return new FailedResponse('A file is being processed');
            }
            // store file path for cron and statr cron job
            $importRecord = new ImportMembersRecord();
            $importRecord->setPath($path)
                          ->setCardProgram(Util::cardProgram())
                          ->setEmployer($this->getGroupAdminUser($this->user))
                          ->setFailedCount(0)
                          ->setSuccessCount(0)
                          ->setStatus(ImportMembersRecord::IMPORT_INIT)
                          ->persist();
            // Service::sendAsync('/t/cron/mex/import');
            Service::sendAsync('/t/cron/mex/import/members/' . $importRecord->getId());
            return new SuccessResponse('Import processing');
          } else {
            $importRecord = new ImportMembersRecord();
            $importRecord->setPath($path)
                        ->setCardProgram(Util::cardProgram())
                        ->setEmployer($this->getGroupAdminUser($this->user))
                        ->setFailedCount(0)
                        ->setSuccessCount(count($members))
                        ->setStatus(ImportMembersRecord::IMPORT_COMPLETE)
                        ->persist();
          }
        }

        if ($type == 'create') {
          if (!$memberInfo || empty($memberInfo['Email'])) {
            return new FailedResponse('Invalid parameters!');
          }
          $user = User::findPlatformUserByEmail($memberInfo['Email'], [
            Role::ROLE_FAAS_MEMBER,
           //  Role::ROLE_FAAS_ADMIN,
           //  Role::ROLE_FAAS_AGENT,
           //  Role::ROLE_FAAS_CLIENT
           ]);
          if ($user) {
              throw PortalException::temp('Duplicated accounts with the same email address!');
          }
          $country = Country::find($memberInfo['CountryId'] ? $memberInfo['CountryId'] : 232);
          $user = new User();
          $user->setEnabled(true)
              ->setPlainPassword(Util::generatePassword('FAAS'))
              ->setEmailVerified(true)
              ->setStatus(User::STATUS_ACTIVE)
              ->setSource($user->getSource() ?? 'mex_import_employee')
              ->setFirstName($memberInfo['First Name'])
              ->setLastName($memberInfo['Last Name'])
              ->changeMobilePhone(Util::inputPhone($memberInfo['Mobile Phone'], $country), Bundle::getMemberRoles())
              ->setBirthday(Util::toUTC(Util::timeLocal($memberInfo['Date of Birth'])))
              ->setAddress($memberInfo['Street Address'])
              ->setCountryid($memberInfo['CountryId'] ? $memberInfo['CountryId'] : 232)
              ->setStateid($memberInfo['StateId'])
              ->setCity($memberInfo['City'])
              ->setZip($memberInfo['Postal Code'])
              ->changeEmail($memberInfo['Email'])
              ->setUserName($memberInfo['Email'].'_faas')
              ->ensureRole(Role::ROLE_FAAS_MEMBER)
              ->persist();
            $cardType = CardProgramCardType::getForCardProgram($this->cardProgram);
            $uc = CardService::create($user, $cardType);
            MemberService::ensureInEmployer($user, $this->getGroupAdminUser($this->user));
            FaasMemberService::updateOnboardStatus($user);
            $members[] = $user;
          }
        $res = [];
        /** @var User $user */
        foreach ($members as $user) {
          try {
              $this->doOfacOnUser($user);
          } catch (\Exception $exception) {
              Log::warn('Failed to verify OFAC: ' . $exception->getMessage(), [
                  'user' => $user->getId(),
              ]);
          }
          $res[] = [
            'First Name' => $user->getFirstName(),
            'Last Name'  => $user->getLastName(),
            'User ID'    => $user->getId()
          ];
        }

        return new SuccessResponse($res);
    }

    private function doOfacOnUser(User $user)
    {
        IDologyService::ofac($user);
        FaasMemberService::updateOnboardStatus($user);
        ProcessorHub::ensureProcessorUserSilently($user);
    }

     /**
     * @Route("/admin/faas/base/members/countryTemplate", methods={"POST"})
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function exportCountryTemplate(Request $request)
    {
        if ($this->group->getFundingType() == UserGroup::FUNDING_TYPE_ACH || Util::meta($this->user, 'readOnlyAdmin')) {
          throw new DeniedException();
        }
        $title = $this->cardProgram->getName() . ' country states';
        $headers = [
          'Counrty' => 20,
          'State'   => 20
        ];
        $countries = $this->cardProgram->getCountries();
        $res = [];
        foreach ($countries as $country) {
          $states = $country->getStates();
          foreach($states as $state) {
            $res[] = [
              'Counrty' => $country->getName(),
              'State' => $state->getName()
            ];
          }

        }


        $filename = $title . '_' . $request->get('query_timestamp', time());
        $destination = Util::uploadDir('report') . $filename . '.xlsx';
        $excel = $this->loadOrCreateExcel($destination, $filename);

        $excel = $this->generateSheetAppendToExcel($excel, 0 . ' ~ ' . (5000),
            $headers, $res, static function ($col, $row, $excel, $title) {
                return $row[$title] ?? '';
            }, []);
        $this->saveExcelTo($excel, $destination);

        return new SuccessResponse('report/' . $filename . '.xlsx');
    }

    /**
     * @Route("/admin/mex/employer/employees/upload-dd-pdf", methods={"POST"})
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function uploadDdPdf(Request $request)
    {
        if (Util::meta($this->user, 'readOnlyAdmin')) {
          throw new DeniedException();
        }

        $url = $this->employer->getId() . '/' . $request->get('attachment');

        $by = Util::getImpersonatingUser();
        if ($by) {
            $url .= '/' . $by->getId();
        } else if ($this->employer->getId() != $this->user->getId()) {
          $url .= '/' . $this->user->getId();
        }

        $prefix = $request->get('prefix');
        if ($prefix) {
            $url .= '?prefix=' . urlencode($prefix);
        }

        $isW2s = $this->getPostData('isWs');
        if ($isW2s) {
          Service::sendAsync('/t/cron/mex/extract-ws-pdf/' . $url);
        } else {
          Service::sendAsync('/t/cron/mex/extract-dd-pdf/' . $url);
        }

        return new SuccessResponse('The file is processing in the background. We will send you an email when it is completed.');
    }

     /**
     * @Route("/admin/mex/employee/{user}/deposit-info", methods={"POST"})
     * @param Request $request
     * @param User    $user
     *
     * @return FailedResponse|SuccessResponse
     */
    public function depositInfo(Request $request, User $user)
    {
        if (Util::meta($this->user, 'readOnlyAdmin')) {
          throw new DeniedException();
        }
        $uc = $user->getCurrentPlatformCard();
        $uc->addDepositInfoViewLog($this->user);
        $api = $uc->getProcessorApiImpl();
        return new SuccessResponse([
            'Account Number' => $api->getDirectDepositNumber($uc),
            'Routing Number' => $api->getRoutingNumber($uc),
            'logs' => []
        ]);
    }

       /**
     * @Route("/admin/mex/employee/batch-move", methods={"POST"})
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function batchMove(Request $request)
    {
        $eid = $this->getPostData('employer');
        if (!$eid) {
            return new FailedResponse('Invalid employer!');
        }
        $employer = $this->em->getRepository(User::class)
            ->find($eid);
        if (!$employer) {
            return new FailedResponse('Unknown employer ' . $eid . '!');
        }
        $memIds = $this->getPostData('members');
        if (!$memIds) {
            return new FailedResponse('No selected members to move!');
        }
        $expr = $this->em->getExpressionBuilder();
        $q = $this->em->getRepository(User::class)
            ->createQueryBuilder('u')
            ->where($expr->in('u.id', ':ids'))
            ->setParameter('ids', $memIds);
        $rs = $this->queryMembersInCurrentPlatform($q)
            ->getQuery()
            ->getResult();
        /** @var User $r */
        foreach ($rs as $r) {
            MemberService::ensureInEmployer($r, $employer, false);
        }
        $this->em->flush();
        return new SuccessResponse();
    }
}
