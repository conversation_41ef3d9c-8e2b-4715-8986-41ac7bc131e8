<?php

namespace TransferMexBundle\Controller\Mobile;

use Carbon\Carbon;
use CoreBundle\Entity\Config;
use CoreBundle\Entity\Country;
use CoreBundle\Entity\Email;
use CoreBundle\Entity\ExternalInvoke;
use CoreBundle\Entity\MessageBatch;
use CoreBundle\Entity\MessageRecord;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserPin;
use CoreBundle\Entity\UserToken;
use CoreBundle\Exception\FailedException;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Services\AuthService;
use CoreBundle\Services\SSLEncryptionService;
use CoreBundle\Services\UserPinService;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Security;
use CoreBundle\Utils\Util;
use Exception;
use FaasBundle\Services\BOTM\BotmAPI;
use FaasBundle\Services\ProcessorHub;
use MobileBundle\MobileBundle;
use PortalBundle\Exception\PortalException;
use SalexUserBundle\Entity\User;
use SalexUserBundle\Security\UserDeviceService;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use TransferMexBundle\Entity\SplashPage;
use TransferMexBundle\Entity\TransactionGroups;
use TransferMexBundle\Services\IntermexRemittanceService;
use TransferMexBundle\Services\MemberService;
use TransferMexBundle\Services\RapidAPI;
use TransferMexBundle\Services\ProcessorMemberService;
use TransferMexBundle\Services\RapydDisburseService;
use TransferMexBundle\Services\SlackService;
use TransferMexBundle\Services\UniTellerRemittanceService;

class UserController extends BaseController
{
    use UserControllerTrait;

    /**
     * @Route("/mex/m/user/profile")
     */
    public function profile()
    {
        $this->validateAppVersion();

        $profile = $this->traitGetUserProfile($this->user);
        $profile['payoutMethods'] = RapydDisburseService::getCachedFilteredMethodTypes();
//        $profile['payoutMethods'] = [
//            'bank.MX.MXN' => [],
//            'cash.MX.MXN' => [],
//        ];
        $uc = $this->user->getCurrentPlatformCard();
        $platform = [
            'id' => $this->platform->getId(),
            'name' => $this->platform->getName(),
        ];

        $isoCodes = [
            'US', // United States
            'MX', // Mexico
            'SV', // El Salvador
            // 'GT', // Guatemala
            'HN', // Honduras
            'PE', // Peru
        ];
        $countries = [];
        foreach ($isoCodes as $iso) {
            $country = Country::findByCode($iso);
            $c = $country->toApiMinArray();
            $c['states'] = Util::toApiArray($country->getSortedStates());
            $countries[] = $c;
        }
        $platform['countries'] = $countries;
        $profile['platform'] = $platform;
        $profile['payoutOptions'] = RapydDisburseService::getPayoutOptions(uc: $uc);
        $profile['payoutOptionsUniTeller'] = UniTellerRemittanceService::getPayoutOptions();
        $profile['requiredFieldsMap'] = RapydDisburseService::BENEFICIARY_REQUIRED_FIELDS_MAP;
        $group = $this->user->getPrimaryGroup();
        $profile['allowBalancePinView'] = !is_null($group) && $group->getAllowBalanceViewing();
        $profile['supportUniTeller'] = Util::meta($this->user, 'UniTellerUserID') ? true : false;
        $profile['hasPasscode'] = $this->user->hasPasscode();
        $profile['requireMfa'] = true;
        $profile['isEmailValid'] = Email::isValid($this->user->getEmail());
        $profile['canMfaByEmail'] = true;
        $profile['isNextTransferFree'] = MemberService::checkIsNextTransferFree($this->user);

        $splashDisplayCount = Data::get('splashDisplayCount');
        $profile['splashDisplayCount'] = $splashDisplayCount;

        $profile['unReadCount'] = Util::em()->getRepository(MessageRecord::class)
                                            ->createQueryBuilder('mr')
                                            ->where('mr.status = :status')
                                            ->andWhere('mr.sentTo = :sentTo')
                                            ->andWhere(Util::expr()->isNull('mr.viewDate'))
                                            ->setParameter('sentTo', $this->user->getId())
                                            ->setParameter('status', MessageBatch::STATUS_SENT)
                                            ->select('count(distinct mr)')
                                            ->getQuery()
                                            ->getSingleScalarResult();
        $mfa = AuthService::ensureSecret($this->user, 'supportId2Fa');
        $profile['supportId2Fa'] = Security::aesEncrypt(Security::rsaDecrypt($mfa));
        $profile['hasBankRecipient'] = MemberService::hasRecipient($this->user, 'bank');
        $profile['hasCashRecipient'] =  MemberService::hasRecipient($this->user, 'cash');
        $profile['isBankFree'] = MemberService::checkTransferFree($this->user, 'bank');
        $profile['isCashFree'] =  MemberService::checkTransferFree($this->user, 'cash');

        $profile['chatbotUrl'] = Util::host() . '/static/mex/query.html';
        $profile['splashList'] = $this->getSplash();
        $profile['enableIntermex'] = true; // Util::meta($this->user, 'useIntermex');
        $profile['cancelReasonList'] = IntermexRemittanceService::getReasonList();
        $platformConfig = [
          'intermexBank' => Util::meta(Util::platform(), 'enableIntermexBank'),
          'intermexCashPickup' => Util::meta(Util::platform(), 'enableIntermexCashPickup'),
          'rapydBank' => Util::meta(Util::platform(), 'enableRapydBank'),
          'uniTellerCashPickup' => Util::meta(Util::platform(), 'enableUniTellerCashPickup'),
          'uniTellerBank' => Util::meta(Util::platform(), 'enableUniTellerBank')
        ];
        $employer = $this->user->getPrimaryGroupAdmin();
        $employerUnitellerBankMethod = $employer && Util::meta($employer, 'unitellerBankTransfer');
        $profile['transferMethods'] = [
          'intermexBank' => ($platformConfig['uniTellerBank'] || $employerUnitellerBankMethod)  ? false : ($platformConfig['intermexBank'] ? (Util::meta($this->user, 'disableIntermexBank') ? false : true) :  false),
          'intermexCashPickup' => $platformConfig['intermexCashPickup'] ? (Util::meta($this->user, 'disableIntermexCashPickup') ? false : true) : false,
          'rapydBank' => $platformConfig['rapydBank'] ? (Util::meta($this->user, 'disableRapydBank') ? false : true) : false,
          'uniTellerCashPickup' => $platformConfig['uniTellerCashPickup'] ? (Util::meta($this->user, 'disableCashPickup') ? false : true) :  false,
          'uniTellerBank' => ($platformConfig['uniTellerBank'] || $employerUnitellerBankMethod) ? (Util::meta($this->user, 'disableUniTellerBank') ? false : true) :  false,
        ];
        $profile['agreeIntermexTerms'] = Util::meta($this->user, 'agreeIntermexTerms') ? true : false;
        return new SuccessResponse($profile);
    }

    protected function getSplash() {
      $employeeSplashList = SplashPage::getSettingList($this->user);
      $employerSplashList =  SplashPage::getSettingList($this->user->getPrimaryGroupAdmin());
      $splashList = array_merge($employeeSplashList, $employerSplashList);
      $splashDisplayCount = Data::get('splashDisplayCount');
      $splashListString = array_map('serialize', $splashList);
      $uniqueSplashListString = array_unique($splashListString);
      $splashList = array_map('unserialize', $uniqueSplashListString);

      foreach ($splashList as $index => $splash) {
        $hasDisplayed = Util::meta($this->user, 'hasDisplayed' . $splash['id']);
        if ( $splashDisplayCount == '-1') {
          $splashList[$index]['splashDisplayCount'] = 1;
        } else {
          $splashList[$index]['splashDisplayCount'] = $splashDisplayCount == '0' ? 0 : (int)($splashDisplayCount - $hasDisplayed);
        }
      }
      return $splashList;
    }

    /**
     * @Route("/mex/m/user/home")
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function info(Request $request)
    {
        $this->validateAppVersion();
        $data = $this->traitInfo($request, $this->user);
        return new SuccessResponse($data);
    }

    /**
     * @Route("/mex/m/user/update", methods={"POST"})
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function update(Request $request)
    {
        $this->validateAppVersion();
        return $this->traitUpdate($request, $this->user);
    }

    /**
     * @Route("/mex/m/user/change-pin", methods={"POST"})
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function changePin(Request $request)
    {
        $this->checkReadonlyWhenLoggingAs();
        $this->disableWebPortalAccess();
        $this->validateAppVersion();
        $this->validateEmployerMemberStatus($this->user, validateCardNativeStatus: true);

        $uc = $this->validatePinToken($request, $this->user);
        $newPin = $request->get('pin');
        if (!$newPin || strlen($newPin) < 4) {
            return new FailedResponse('Invalid PIN!');
        }

        $newPin = Security::rsaDecryptWhenNecessary($newPin);
        if (Util::checkCardPin($this->user, $newPin)) {
          return new FailedResponse(Util::checkCardPin($this->user, $newPin));
        }

        $pan = $request->get('cardNumber') ?: $uc->decryptCardNumber();
        $pan = Security::rsaDecryptWhenNecessary($pan);
        ProcessorHub::validateFullNumberPan($pan);

        $api = $uc->getProcessorApiImpl();
        $api->validateFullCardNumberWithCards($uc, $pan, 'changing PIN');

        $uc->getProcessorApiService()::changePin($uc, $newPin, $pan);

        Util::storeCardPin($this->user, $newPin);

        try {
            ProcessorHub::updateBalanceAndStatusSilently($uc);

            $newStatus = null;
            if ($this->user->getRegisterStep() === MemberService::STEP_ACTIVE) {
                if ($api instanceof RapidAPI && $uc->getNativeStatus() === RapidAPI::CARD_STATUS_ON_HOLD) {
                    $newStatus = RapidAPI::CARD_STATUS_ACTIVE;
                } else if ($api instanceof BotmAPI && $uc->getNativeStatus() === BotmAPI::CARD_STATUS_PAUSED) {
                    $newStatus = BotmAPI::CARD_STATUS_ACTIVE;
                }
            }
            if ($newStatus) {
                ExternalInvoke::host($api->updateStatus($uc, $newStatus));
                ProcessorHub::updateBalanceAndStatusSilently($uc);
                if (in_array($uc->getNativeStatus(), [
                    RapidAPI::CARD_STATUS_ACTIVE,
                    BotmAPI::CARD_STATUS_ACTIVE,
                ])) {
                    $this->user->addNote(
                        'Resume card ' . $uc->getAccountNumber() . ' since PIN changed.',
                        true,
                        $this->user->getId()
                    );
                }
            }
        } catch (Exception $ex) {
            Log::warn('Failed to resume the card after PIN changes: ' . $ex->getMessage());
        }

        return new SuccessResponse();
    }

    /**
     * @Route("/mex/m/user/reset-avatar", methods={"POST"})
     *
     * @return SuccessResponse
     */
    public function resetAvatar()
    {
        return $this->traitResetAvatar($this->user);
    }

    /**
     * @Route("/mex/m/user/pin-send")
     *
     * @return FailedResponse|SuccessResponse
     */
    public function sendPinUnlockCode(Request $request)
    {
        $this->checkReadonlyWhenLoggingAs();
        $this->validatePasscode($request);
        $this->validateEmployerMemberLevelStatus($this->user);

        $uc = $this->user->getCurrentPlatformCard();
        if (!$uc->isIssued() || !$uc->getAccountNumber()) {
            return new FailedResponse('Your card is not activated yet!');
        }

        $method = UserPin::MSG_TYPE_SMS;
        $target = $request->get('target');
        if ($target === 'email') {
            $method = UserPin::MSG_TYPE_EMAIL;
        }

        $debug = false;
        // To facilitate Indigo account testing, the SMS verification code is 123456
        if ($method === UserPin::MSG_TYPE_SMS && in_array($this->user->getId(), [
            *********, // Jake Brovda, <EMAIL>, +1 516-965-2185, Rapid
            *********, // Indigo Glaze, <EMAIL>, +1 470-985-7010, Rapid
            *********, // John Doe, <EMAIL>, +52 313 111 2222, BOTM
        ])) {
            $debug = true;
        }
        UserPinService::createForMethod($this->user, $method, $debug,
            durationLimitInMinutes: 3,
            format: UserPin::FORMAT_SIX_DIGITS);

        return new SuccessResponse();
    }

    /**
     * @Route("/mex/m/user/pin-verify")
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function verifyPinUnlockCode(Request $request)
    {
        $uc = $this->user->getCurrentPlatformCard();
        if (!$uc->isIssued() || !$uc->getAccountNumber()) {
            return new FailedResponse('Your card is not activated yet!');
        }
        /** @var UserPin $userPin */
        $userPin = null;
        $pass = UserPinService::verify($this->user, $request->get('code'), userPin: $userPin);
        if (!$pass || !$userPin) {
            return new FailedResponse('Empty or invalid code!');
        }
        $action = $request->get('action');
        $result = null;
        if (!$action || $action === 'pin') {
            $result = $uc->getUpdatedOrApiPIN();
        } elseif ($action === 'pin_token') {
            $result = $this->user->generatePinToken();
            $ud = UserDeviceService::instance($this->user);
            if (UserDeviceService::canBeSavedAsTrusted($ud, $userPin)) {
                UserDeviceService::saveTrustedFingerprint($ud, $result);
            }
        }
        return new SuccessResponse($result);
    }

    /**
     * @Route("/mex/m/user/view-pin")
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function viewPIN(Request $request)
    {
        $this->validateAppVersion();
        $uc = $this->validatePinToken($request, $this->user);

        $group = $this->user->getPrimaryGroup();
        if ($group && !$group->getAllowBalanceViewing()) {
            return new FailedResponse('PIN viewing is temporarily disabled. Please try again later.');
        }

        $result = $uc->getUpdatedOrApiPIN();
        return new SuccessResponse($result);
    }

    /**
     * @Route("/mex/m/user/webview/urls")
     */
    public function getWebViewUrls() {
        $urlsList = Config::array(Config::CONFIG_MEX_WEBVIEW_URLS) ?? [];
        $internals = Platform::getAllInternalDomains();
        $hasInternal = false;
        $urls = [];;
        $language = MemberService::getCurrentLanguage($this->user);
        foreach ($urlsList as $i => $url) {
            if (isset($url['status']) && $url['status'] != 'Active') {
              continue;
            }
            $internal = false;
            foreach ($internals as $in) {
                if (Util::startsWith($url['url'], 'https://' . $in)) {
                    $internal = true;
                    $hasInternal = true;
                    break;
                }
            }
            $url['internal'] = $internal;
            if ($language == 'es' && !empty($url['es_title'])) {
                $url['title'] = $url['es_title'];
            }
            $urls[] = $url;
        }

        if ($hasInternal) {
            $ut = UserToken::create($this->user, Carbon::now()->addMinutes(30))
                ->getToken();
            foreach ($urls as $i => $url) {
                if (!empty($url['internal'])) {
                    $urls[$i]['url'] = Util::appendQueryParam($url['url'], 'x-token', $ut);
                }
            }
        }

        return new SuccessResponse($urls);
    }

    /**
     * @Route("/mex/m/user/verify-passcode")
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function verifyPasscode(Request $request)
    {
        $verified = $this->user->verifyPasscode($request->get('input'));
        if (!$verified) {
            return new FailedResponse('Invalid passcode!');
        }
        return new SuccessResponse();
    }

    /**
     * @Route("/mex/m/user/set-passcode")
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function setPasscode(Request $request)
    {
        $this->checkReadonlyWhenLoggingAs();
        $this->disableWebPortalAccess();
        $this->validateAppVersion();

        $summary = Util::jsonRequest($request, true, function (&$headers, &$query, &$post, Request $request) {
            Util::maskArrayField($post, [
                'old', 'new',
            ], false);
        });
        Log::debug('Setting passcode by the member', $summary);

        $verified = $this->user->verifyPasscode($request->get('old'));
        if (!$verified) {
            return new FailedResponse('Invalid old passcode!');
        }

        $new = Security::rsaDecrypt($request->get('new'));
        if (!$new) {
            return new FailedResponse('Invalid passcode!');
        }

        $hash = sha1('mex_passcode_' . $new . '_RcX$K4L!@HXzJAHq!GmTH');
        $passcodeHistory = Util::meta($this->user, 'passcodeHistory') ?? [];
        if (in_array($hash, $passcodeHistory)) {
            return new FailedResponse('The new passcode cannot be the same as the previous 5 codes!');
        }

        $passcodeHistory[] = $hash;
        $passcodeHistory = array_slice($passcodeHistory, count($passcodeHistory) - 5);

        Util::updateMeta($this->user, [
            'passcode' => SSLEncryptionService::encrypt($new),
            'passcodeHistory' => $passcodeHistory,
        ]);
        return new SuccessResponse();
    }

    /**
     * @Route("/mex/m/user/pause")
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function pauseUser(Request $request)
    {
        $user = $this->user;
        $uc = $user->getCurrentPlatformCard();

        $reason = 'Too many failed passcode attempts.';
        $status = MemberService::STEP_ON_HOLD;

        // Update local account status/uc first.
        Util::updateMeta($user, [
            'changeStatusReason' => $reason,
        ], false);
        $user->addNote('Changed Member Status to "' . $status . '". Reason: ' . $reason, true, $this->user->getId());

        $user->setStatus(User::STATUS_INACTIVE, false)
            ->persist();
        $uc->setStatus(UserCard::STATUS_INACTIVE, true, false)
            ->persist();

        MemberService::updateOnboardStatus($user);

        // Update card status next
        $oldCardStatus = $uc->getNativeStatus();
        $newCardStatus = null;
        /** @var ExternalInvoke $ei */
        $ei = null;
        if ($uc->getAccountNumber() && $uc->isIssued()) {
            $api = $uc->getProcessorApiImpl();
            if ($uc->isBotmCard()) {
                $newCardStatus = BotmAPI::CARD_STATUS_PAUSED;
                if (BotmAPI::isCardStatusOnHoldable($oldCardStatus)) {
                    [$ei, ] = $api->updateStatus($uc, $newCardStatus);
                }
            } else {
                $newCardStatus = RapidAPI::CARD_STATUS_ON_HOLD;
                if (RapidAPI::isCardStatusOnHoldable($oldCardStatus)) {
                    [$ei, ] = $api->updateStatus($uc, $newCardStatus);
                }
            }
        }

        if ($ei && $ei->isFailed()) {
            if (RapidAPI::isDisabled() && RapidAPI::isAccountNumberValid($uc->getAccountNumber())) {
                Log::debug($uc->getAccountNumber() .
                           ': Failed to update account status when pausing a member: ' . $ei->getErrorSignature());
            } else {
                SlackService::alert('Failed to update account status when pausing a member: ' . $ei->getErrorSignature(), [
                    'member' => $user->getSignature(),
                    'account_number' => $uc->getAccountNumber(),
                    'user_status' => $status,
                    'old_status' => $oldCardStatus,
                    'new_status' => $newCardStatus,
                ], SlackService::MENTION_HANS);
            }
        }

        ProcessorHub::updateBalanceAndStatusSilently($uc);

        ProcessorMemberService::pauseAllLegacyCards($uc);

        $uts = $user->getTokens();
        foreach ($uts as $ut) {
            $this->em->remove($ut);
        }
        $this->em->flush();

        SlackService::$channel = SlackService::CHANNEL_CLIENT;
        SlackService::info('Paused the member due to `' . $reason . '`.', [
            'member' => $user->getSignature(),
            'account_number' => $uc->getAccountNumber(),
        ]);
        // add a redis flag to mark the on hold reason
        Data::set('card_on_hold_by_password_' . $user->getId(), true);
        return new SuccessResponse();
    }

    /**
     * @Route("/mex/m/user/getQuestions", methods={"GET"})
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function getQuestions(Request $request)
    {
        $res = UniTellerRemittanceService::securityQuestions();
        return new SuccessResponse($res);
    }

     /**
     *
     * @Route("/mex/m/user/createUniTeller", methods={"POST"})
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function createUniTellerUser(Request $request){

      // check user
      if (UniTellerRemittanceService::checkDupllcateUser($this->user)) {
        return new FailedResponse('The member has registered UniTeller');
      }

      $answer = [
        'id' => $request->get('questionId'),
        'answer' => $request->get('answer'),
        'answerHint' => $request->get('answerHint')
      ];
      /** @var ExternalInvoke $ei */
      [$ei, $res] = UniTellerRemittanceService::registerUniTeller($this->user, $answer);

      if ($ei->isFailed()) {
        throw PortalException::temp($ei->getError());
      }
      $email = Util::InitSpecialEmail($this->user->getId() . $this->user->getEmail());
      Util::updateMeta($this->user, [
          'UniTellerUserID' => $email,
          'UniTellerUserQuestion' => $answer
        ]);

     return new SuccessResponse();
  }

  /**
     * @Route("/mex/m/user/getStateDisclaimer", methods={"GET"})
     * @param Request $request
     *
     * @return FailedException|SuccessResponse
     */

    public function getStateDisclaimer(Request $request)
    {
        Util::longerRequest();

        if ($this->user->getCountry() && $this->user->getCountry()->isUSA() ) {
          $graoupAdmin = $this->user;
        } else {
          $graoupAdmin = $this->user->getPrimaryGroupAdmin();
        }

        $stateIsoCode = $graoupAdmin->getState() ? $graoupAdmin->getState()->getAbbr() : '';

        /** @var ExternalInvoke $ei */
        [$ei, $result] = UniTellerRemittanceService::getStateDisclaimer($stateIsoCode);

        if ($ei->isFailed()) {
          throw PortalException::temp($ei->getError());
        }

        $disclaimer = null;
        if ($result['page']) {
          foreach ($result['page'] as $key => $item) {
            if ($key == $stateIsoCode) {
              if (!Util::isLive() && $item['disclaimerUrl']) {
                $item['disclaimerUrl'] = Util::host() . '/files/upload/test.pdf';
              }
              $disclaimer = $item;
            }
          }
        }

        return new SuccessResponse($disclaimer);

    }


     /**
     *
     * @Route("/mex/m/user/updateDisplayedCount", methods={"POST"})
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function updateDisplayedCount(Request $request){
        $splashId = $request->get('splashId', '');
        $oldLogicHasDisplayed = Util::meta($this->user, 'hasDisplayed');
        $hasDisplayed = Util::meta($this->user, 'hasDisplayed' .  $splashId );
        Util::updateMeta($this->user, [
          'hasDisplayed' => $oldLogicHasDisplayed + 1,
          'hasDisplayed' .  $splashId  => $hasDisplayed + 1
        ]);
        return new SuccessResponse();
    }

    /**
     * @Route("/mex/m/user/getQuestionAnswer", methods={"GET"})
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function getQuestionAnswer(Request $request)
    {
        $res = UniTellerRemittanceService::securityQuestions();

        $answer = Util::meta($this->user,'UniTellerUserQuestion');
        foreach ($res as $item) {
          if ($item['value'] == $answer['id']) {
            $answer['title'] = $item['label'];
          }
        }
        return new SuccessResponse($answer);
    }


    /**
     * @Route("/mex/m/user/storeToken", methods={"POST"})
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function storeFireBaseToken(Request $request)
    {
        $this->checkReadonlyWhenLoggingAs();
        $this->disableWebPortalAccess();
        $this->validateAppVersion();
        $token = $request->get('token');
        Util::updateMeta($this->user, [
          'fireBaseToken' => $token
        ]);
        return new SuccessResponse();
    }

    /**
     * @Route("/mex/m/user/{messageRecord}/storeViewDate", methods={"GET"})
     * @param Request $request
     *
     * @return SuccessResponse
     */
     public function storeViewDate(Request $request, MessageRecord $messageRecord)
     {
        $deviceInfo = MobileBundle::getPlatformInfo();
        $messageRecord->setViewDevice($deviceInfo['device'])
                      ->setViewDate(Carbon::now())
                      ->persist();
        return new SuccessResponse();
     }

    /**
     * @Route("/mex/m/user/{messageRecord}/storeCTA", methods={"GET"})
     * @param Request $request
     *
     * @return SuccessResponse
     */
     public function storeCTA(Request $request, MessageRecord $messageRecord)
     {
        $messageRecord->setCatDate(Carbon::now())
                      ->persist();
        return new SuccessResponse();
     }

     /**
     * @Route("/mex/m/user/readAll", methods={"GET"})
     * @param Request $request
     *
     * @return SuccessResponse
     */
     public function setAllRead(Request $request)
     {
        $deviceInfo = MobileBundle::getPlatformInfo();
        Util::em()->createQueryBuilder()
                  ->update('CoreBundle:MessageRecord', 'mr')
                  ->set('mr.viewDate', ':viewDate')
                  ->set('mr.viewDevice', ':viewDevice')
                  ->where('mr.sentTo = :sentTo')
                  ->andWhere('mr.status = :status')
                  ->andWhere(Util::expr()->isNull('mr.viewDate'))
                  ->setParameters([
                    'viewDate' => Carbon::now(),
                    'viewDevice' => $deviceInfo['device'],
                    'sentTo' => $this->user->getId(),
                    'status' => MessageBatch::STATUS_SENT
                ])
                ->getQuery()
                ->execute();
        return new SuccessResponse();
     }

     /**
     * @Route("/mex/m/message/list/{page}/{pageSize}", methods={"GET"})
     * @param Request $request
     * @param int     $page
     *
     * @return SuccessResponse
     */
     public function getMessage(Request $request, $page = 1, $pageSize = 30)
     {
        $em = Util::em();
        $expr = Util::expr();
        $q = $em->getRepository(MessageRecord::class)
                ->createQueryBuilder('mr')
                ->where('mr.status = :status')
                ->andWhere('mr.sentTo = :sentTo')
                ->setParameter('sentTo', $this->user->getId())
                ->setParameter('status', MessageBatch::STATUS_SENT)
                ;

        $total = (int)((clone $q)->select('count(distinct mr)')
                ->getQuery()
                ->getSingleScalarResult());
        $result = [
          'count' => $total,
          'data' => []
        ];
        $all = $q->orderBy('mr.viewDate', 'asc')
                ->addOrderBy('mr.sentDate','desc')
                ->setFirstResult(($page - 1) * $pageSize)
                ->setMaxResults($pageSize)
                ->getQuery()
                ->getResult();
        /** @var MessageRecord $item */
        foreach ($all as $item) {
            $result['data'][] = $item->toApiArray();
        }
        return new SuccessResponse($result);
     }

     /**
     * @Route("/mex/m/transactionGroup/list", methods={"GET"})
     * @param Request $request
     *
     * @return SuccessResponse
     */
     public function getTransactionGroup(Request $request)
     {
        $em = Util::em();
        $q = $em->getRepository(TransactionGroups::class)
                ->createQueryBuilder('tg');

        $all = $q->orderBy('tg.id', 'desc')
                 ->getQuery()
                 ->getResult();
        $blockTransactionGroups = Util::meta($this->user, 'blockTransactionGroups');
        $blockTransactionGroupsList =  $blockTransactionGroups ? explode(',', $blockTransactionGroups) : [];
        $result = [];
        /** @var TransactionGroups $item */
        foreach ($all as $item) {
            $result[] = [
              'status'  => in_array($item->getId(), $blockTransactionGroupsList) ? 'Blocked' : $item->getStatus(),
              'id'      => $item->getId(),
              'ename'    => $item->getEname(),
              'sname'    => $item->getSname(),
              'edesc'    => $item->getEdesc(),
              'sdesc'    => $item->getSdesc(),
              'mccs'    => $item->getMccs()
            ];
        }
        return new SuccessResponse($result);
     }

    /**
     * @Route("/mex/m/transactionGroup/lockunlock", methods={"POST"})
     * @param Request $request
     *
     * @return SuccessResponse|FailedResponse
     */
     public function lockUnlockTransactionGroup(Request $request)
     {
        $id = $request->get('id');
        $type = $request->get('type');
        $transactionGroup = TransactionGroups::find($id);
        if (!$transactionGroup) {
          return new FailedResponse('The transaction group does not exist!');
        }
        if ($transactionGroup->getStatus() === 'Blocked') {
          return new FailedResponse('The transaction group has been blocked by the platform, you can not change it!');
        }
        $blockTransactionGroups = Util::meta($this->user, 'blockTransactionGroups');
        $blockTransactionGroupsList =  $blockTransactionGroups ? explode(',', $blockTransactionGroups) : [];
        if ($type === 'allow' && in_array($id, $blockTransactionGroupsList)) {
          $blockTransactionGroupsList = array_diff($blockTransactionGroupsList, [$id]);
        }
        if ($type === 'block' && !in_array($id, $blockTransactionGroupsList)) {
          $blockTransactionGroupsList[] = $id;
        }
        Util::updateMeta($this->user, [
          'blockTransactionGroups' => implode(',', $blockTransactionGroupsList)
        ]);
        return new SuccessResponse();
     }

    /**
     *
     * @Route("/mex/m/user/agreeIntermexTerms")
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function agreeIntermexTerns(Request $request){
        $this->checkReadonlyWhenLoggingAs();
        Util::updateMeta($this->user, [
          'agreeIntermexTerms' => Carbon::now()
        ]);

      return new SuccessResponse();
    }

     /**
     *
     * @Route("/mex/m/user/getIntermexTerms")
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function getIntermexTerns(Request $request){
      $this->checkReadonlyWhenLoggingAs();
      $data['url'] = MobileBundle::isSpanish() ? 'https://www.intermexonline.com/es/user-agreement-terms-conditions#/' : 'https://www.intermexonline.com/user-agreement-terms-conditions#/';

      return new SuccessResponse($data);
    }

}
