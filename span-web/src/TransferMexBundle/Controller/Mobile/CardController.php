<?php


namespace TransferMexBundle\Controller\Mobile;


use CoreBundle\Entity\ExternalInvoke;
use CoreBundle\Entity\Processor;
use CoreBundle\Entity\UserCard;
use CoreBundle\Exception\FailedException;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Util;
use FaasBundle\Services\BOTM\BotmAPI;
use FaasBundle\Services\BOTM\BotmCardService;
use FaasBundle\Services\BOTM\BotmService;
use FaasBundle\Services\ProcessorHub;
use PortalBundle\Exception\PortalException;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use TransferMexBundle\Services\MemberService;
use TransferMexBundle\Services\RapidAPI;
use TransferMexBundle\Services\RapidService;
use TransferMexBundle\Services\SlackService;

class CardController extends BaseController
{
    /**
     * @Route("/mex/m/card/activate")
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function activateCard(Request $request)
    {
        $this->checkReadonlyWhenLoggingAs();
        $this->validateAppVersion();
        $this->validateEmployerMemberStatus($this->user);

        $uc = $this->user->getCurrentPlatformCard();
        $pan = $request->get('pan');
        if ($pan && strlen($pan) === 16) {
            $api = RapidAPI::getForUserCard($uc);
            $bypass = false;
            $context = [
                'member' => $this->user->getSignature(),
                'pan' => Util::maskPan($pan),
            ];
            try {
                $number = $api->getAccountNumberByPan($pan);
            } catch (PortalException $exception) {
                $msg = $exception->getMessage() ?? '';
                $context['error'] = $msg;
                if (Util::containsSubString($msg, [
                    'Please confirm the card or account number and retry',
                    'Account not found for given search criteria',
                ])) {
                    // Bypass the validation
                    $bypass = true;
                    $number = $uc->getAccountNumber();
                    Log::warn('Failed to get account number by PAN', $context);
                } else {
                    SlackService::alert('Failed to get account number by PAN', $context);
                    throw $exception;
                }
            }

            if ($bypass) {
                $pan = $api->validateFullCardNumberWithCards($uc, $pan);
            }
        } else {
            $number = $request->get('number');
        }

        if (!$number) {
            return new FailedResponse('Invalid number!');
        }

        $number = strtoupper(trim($number));
        if (strlen($number) === 13) {
            $number = str_replace('-', '', $number);
        }
        if (strlen($number) === 12) {
            $number = substr($number, 0, 4) . '-' . substr($number, 4, 4) . '-' . substr($number, 8);
        }

        $verifyMatch = true;
        $saveLegacy = true;
        $oldNumber = $uc->getAccountNumber();
        $newProcessor = ProcessorHub::getCurrentProcessor($number);
        if ( ! $newProcessor) {
            return new FailedResponse('Unknown barcode number format!');
        }

        if ($newProcessor === Processor::NAME_RAPID) {
            return new FailedResponse('We do not support activating this card type any more. Please contact support.');
        }

        if ( ! $oldNumber && $newProcessor === Processor::NAME_BOTM) {
            $verifyMatch = false;
        } else {
            $oldProcessor = ProcessorHub::getCurrentProcessor($oldNumber);

            // If they are different processors, just retire the old one and allow to activate the new account
            if ($oldProcessor !== $newProcessor) {
                $verifyMatch = false;
            }

            // If the old BOTM card was invalid or not enrolled yet, just replace it without saving as legacy
            else if ($oldProcessor === Processor::NAME_BOTM) {
                $validate = BotmService::validateBarcodeNumber($oldNumber, true);
                if (is_string($validate) && BotmAPI::isCardNotFoundError($validate)) {
                    $verifyMatch = false;
                    $saveLegacy = false;
                } else if (!empty($validate['card_status'])) {
                    if (in_array($validate['card_status'], [
                        BotmAPI::CARD_STATUS_PENDING_ACTIVATION,
                        BotmAPI::CARD_STATUS_PENDING_FULFILLMENT,
                    ])) {
                        $verifyMatch = false;
                        $saveLegacy = false;
                    } else if ($validate['card_status'] === BotmAPI::CARD_STATUS_CLOSED) {
                        $verifyMatch = false;
                    }
                }
            }
        }

        if ($verifyMatch && $number !== $oldNumber) {
            return new FailedResponse('Unmatched number!');
        }

        if ( ! $verifyMatch) {
            Data::singleton('mex_mobile_app_activating_' . $number, function () use ($uc, $newProcessor, $number, $oldNumber, $saveLegacy) {
                if ($newProcessor === Processor::NAME_BOTM) {
                    $validate = BotmService::validateBarcodeNumber($number);
                    if (is_string($validate)) {
                        $validate = BotmAPI::translateErrorMessage($validate, [
                            'number' => $number,
                        ]);
                        throw new FailedException($validate);
                    }
                    $another = BotmCardService::isCardAssignedToOthers($uc->getUser(), $number, true);
                    if ($another) {
                        throw new FailedException('This card had already been assigned to another account. Please contact support.');
                    }
                }

                $other = UserCard::findByAccountNumberWithLegacies($number);
                if ($other && !Util::eq($other, $uc)) {
                    throw new FailedException('This card was already assigned to another account!');
                }

                if ($oldNumber && $oldNumber !== $number) {
                    ProcessorHub::retireOldCard($uc, $saveLegacy);
                }
                $uc->setAccountNumber($number)
                    ->persist();
                MemberService::updateOnboardStatus($uc->getUser());
            }, false, 100, true);
        }

        try {
            MemberService::enroll($uc);
        } catch (\Throwable $pe) {
            $error = BotmAPI::translateErrorMessage($pe->getMessage(), [
                'number' => $uc->getAccountNumber(),
            ]);
            return new FailedResponse($error);
        }

        $newPin = Util::randNumber(4);
        if ($pan) {
            $uc->saveMaskedCardNumber($pan)
                ->persist();
            RapidService::changePin($uc, $newPin, $pan);
        } else if ($uc->isBotmCard()) {
            try {
                $uc->getProcessorApiImpl()->changePin($uc, $newPin, '');
            } catch (\Throwable $t) {
                SlackService::warning('Failed to change PIN after enrollment', [
                    'error' => Util::getExceptionBrief($t),
                    'user' => $this->user->getId(),
                ], [
                    SlackService::MENTION_HANS,
                ]);
            }
        }

        return new SuccessResponse($uc->toApiArrayForTransferMex());
    }

    /**
     * @Route("/mex/m/card/legacies")
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function legacyCards(Request $request)
    {
        $this->validateAppVersion();
        $uc = $this->user->getCurrentPlatformCard();
        $ans = $uc->getAllAccountNumbers();
        $result = [];

        foreach ($ans as $an) {
            try {
                $api = ProcessorHub::getForUserCardAccountNumber($uc, $an);
                $cards = $api->getCachedCardDetailsData($api->getUserCard());
                foreach ($cards as $card) {
                    $card['Barcode Number'] = '********' . Util::maskPan($an, 3, false);
                    $card['Card Number'] = Util::maskPan($card['maskedCardNumber'], 2);
                    $card['Balance'] = Money::usdFormat($card['availableBalance']);
                    $card['Status'] = $card['cardStatus'] ?? '';
                    $result[] = $card;
                }
            } catch (\Throwable $t) {
                Log::warn('Failed to list cards of member for mobile app: ' . Util::getExceptionBrief($t), [
                    'member' => $request->get('member'),
                    'an' => $an,
                ]);
            }
        }
          $hasBotmCard = $uc->isBotmCard();
         // if the member does not have a botm card but have a botm account, we need show the account balance
         if (!$hasBotmCard && $this->user->ensureConfig()->getBotmUserAccountId()) {
          $api = new BotmAPI();
          $balance = $api->getBalance($uc);
          $result[] = [
            'Barcode Number' => 'New Account ' . $this->user->ensureConfig()->getBotmUserAccountId(),
            'Card Number' => '',
            'Balance' => Money::usdFormat($balance),
            'Status' => $this->user->getOnboardStatus()
          ];
        }
        return new SuccessResponse($result);
    }

    /**
     * @Route("/mex/m/card/legacies/page")
     * @param Request $request
     *
     * @return Response
     */
    public function legacyCardsPage(Request $request)
    {
        $resp = $this->legacyCards($request);
        return $this->render('@TransferMex/App/cards.html.twig', [
            'items' => $resp->getResult(),
        ]);
    }

    /**
     * @Route("/mex/m/card/legacies/move-balance/page", methods={"GET"})
     * @param Request $request
     *
     * @return Response
     */
    public function moveBalancePage(Request $request)
    {
        $this->validateAppVersion();
        $uc = $this->user->getCurrentPlatformCard();
        if (!$uc->isBotmCard() || !$uc->isActive()) {
            return new Response(Util::t('No active valid card to transfer balance to.'));
        }

        $ans = $uc->getLegacyAccountNumbers();
        $cards = [];
        $total = 0;
        foreach ($ans as $an) {
            if ( ! RapidAPI::isAccountNumberValid($an)) {
                continue;
            }
            try {
                $api = RapidAPI::getForUserCardAccountNumber($uc, $an);
                $tempUc = $api->getUserCard();
                $details = $api->getCardDetailsData($tempUc);
                foreach ($details as $detail) {
                    $balance = $detail['availableBalance'] ?? 0;
                    $_an = $detail['accountNumber'] ?? $an;
                    $total += $balance;
                    $cards[$_an] = [
                        'account' => $_an,
                        'barcode' => $details[0]['maskedCardNumber'] ?? '****',
                        'balance' => $balance,
                    ];
                }
            } catch (\Throwable $t) {
                $cards[$an] = [
                    'account' => $an,
                    'barcode' => $an,
                    'error' => $t->getMessage(),
                ];
            }
        }
        
        return $this->render('@TransferMex/App/move-balance.html.twig', [
            'cards' => $cards,
            'total' => $total,
        ]);
    }

    /**
     * @Route("/mex/m/card/legacies/move-balance", methods={"POST"})
     * @param Request $request
     *
     * @return Response
     */
    public function moveBalance(Request $request)
    {
        return new SuccessResponse();
    }
}
