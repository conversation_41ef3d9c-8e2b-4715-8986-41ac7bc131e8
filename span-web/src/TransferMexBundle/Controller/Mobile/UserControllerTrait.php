<?php

namespace TransferMexBundle\Controller\Mobile;

use CoreBundle\Entity\Config;
use CoreBundle\Exception\FailedException;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Util;
use FaasBundle\Services\ProcessorHub;
use MobileBundle\MobileBundle;
use SalexUserBundle\Entity\User;
use Symfony\Component\HttpFoundation\File\File;
use Symfony\Component\HttpFoundation\Request;
use TransferMexBundle\Services\TransactionService;
use TransferMexBundle\Services\UniTellerRemittanceService;
use TransferMexBundle\TransferMexBundle;

trait UserControllerTrait
{
	protected function traitGetUserProfile(User $user)
	{
		return [
            'id' => $user->getId(),
            'email' => $user->getEmail(),
            'firstName' => $user->getFirstName(),
            'lastName' => $user->getLastName(),
            'fullName' => $user->getFullName(),
            'mobile' => $user->getMobilephone(),
            'country' => $user->getCountryCode(),
            'address' => $user->getSimpleFullAddress(),
            'status' => $user->getStatus(),
            'avatar' => $user->getAvatarUrl(),
            'twoFAEnabled' => $user->isTwoFAEnabled(),
		];
	}

	public function traitInfo(Request $request, User $user)
	{
        $transactions = TransactionService::getRecentTransactions($user);
        if (! TransferMexBundle::isSecureApp()) {
            array_unshift($transactions, [
                'id' => 0,
                'time' => Util::formatApiDateTime(),
                'amount' => 0,
                'txnId' => '0',
                'group' => 'debit',
                'type' => 'Debit',
//                'description' => 'Please upgrade your app!',
                'description' => '¡Actualice su aplicación!',
//                'result' => 'You are using an old version. Please upgrade your app to achieve better security and performance. We will deprecate the old version on August 8th or even earlier. ',
                'result' => 'Estás usando una versión antigua. Actualice su aplicación para lograr una mejor seguridad y rendimiento. Desactivaremos la versión anterior el 8 de agosto o incluso antes.',
                'status' => 'Atención',
                'category' => null,
                'icon' => null,
            ]);
        } else if (! TransferMexBundle::isUniTellerApp() && Config::get('transfermex_upgrade_advice', false)) {
            $spanish = MobileBundle::isSpanish();
            array_unshift($transactions, [
                'id' => 0,
                'time' => Util::formatApiDateTime(),
                'amount' => 0,
                'txnId' => '0',
                'group' => 'credit',
                'type' => 'Credit',
                'description' => $spanish ? 'Consejos de actualización' : 'Upgrade advice',
                'result' => $spanish ? 'Actualice su aplicación para lograr una mejor seguridad y características como un método mejorado de pago en efectivo.' : 'Please upgrade your app to achieve better security and features like an enhanced cash pickup method.',
                'status' => $spanish ? 'Consejo' : 'Advice',
                'category' => null,
                'icon' => null,
            ]);
        }

		return [
			'card' => $this->cardInfo($request, $user),
			'transactions' => $transactions,
		];
	}

	public function cardInfo(Request $request, User $user)
	{
		$uc = $user->getCurrentPlatformCard();
		$force = $request->get('force');
		if ($force === true || $force === 'true') {
			ProcessorHub::updateBalanceAndStatusSilently($uc);
		}
		return $uc->toApiArrayForTransferMex();
	}

	public function traitUpdate(Request $request, User $user)
	{
        if (method_exists($this, 'checkReadonlyWhenLoggingAs')) {
            $this->checkReadonlyWhenLoggingAs();
        }

        if (method_exists($this, 'disableWebPortalAccess')) {
            $this->disableWebPortalAccess();
        }

		/** @var File $avatar */
		$avatar = $request->files->get('avatar');
		if ($avatar) {
			$user->setProfilePictureFile($avatar);
		}

		$mobile = $request->get('mobile');
		if ($mobile) {
            return new FailedResponse('Please contact support if you want to change the phone number.');
		}
        $user->persist();

        if (Util::meta($user, 'UniTellerUserID')) {
            try {
                UniTellerRemittanceService::editUserProfile($user);
            } catch (\Exception $e) {
                return new FailedResponse($e->getMessage());
            }
        }
		return new SuccessResponse($this->traitGetUserProfile($user));
	}

	protected function validatePinToken(Request $request, User $user)
	{
		$this->validateSecureSession(); // @phpstan-ignore-line

		$uc = $user->getCurrentPlatformCard();
		if (!$uc || !$uc->getAccountNumber()) {
			throw new FailedException('Unknown card!');
		}
		return $uc;
	}

	public function traitResetAvatar(User $user)
	{
        $this->checkReadonlyWhenLoggingAs(); // @phpstan-ignore-line

		$user->setProfilePicture(null)
			->persist();
		return new SuccessResponse($this->traitGetUserProfile($user));
	}
}
