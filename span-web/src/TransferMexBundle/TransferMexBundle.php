<?php

namespace TransferMexBundle;

use CoreB<PERSON>le\CommonBundleTrait;
use CoreBundle\Entity\Module;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserCard;
use CoreBundle\Exception\RedirectException;
use CoreBundle\Utils\Util;
use MobileBundle\MobileBundle;
use PortalBundle\Exception\PortalException;
use Symfony\Component\HttpKernel\Bundle\Bundle;
use function Clue\StreamFilter\remove;
use TransferMexBundle\Entity\HideTransactionRecord;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Data;
use Carbon\Carbon;
use CoreBundle\Entity\Config;
use CoreBundle\Entity\UserGroup;
use SalexUserBundle\Entity\User;

class TransferMexBundle extends Bundle
{
    use CommonBundleTrait;

    public const MIDDLE_ACCOUNT = '***********';
    public const CONFIG_FAVORITE_MEMBERS = 'transfermex_favorites';

    public const useCustomFileA = [
      *********, // AGRILABOR, Inc
      *********, // Magana Labor Services, Inc
    ];
    public const useCustomFileB = [
      *********, // Sierra-Cascade Nursery, Inc
    ];
    // hide users transactions
    public static function hideUsers()
    {
        return [
          *********, // Brion
          *********, // The new middle card, ***********
        ];
    }

    // Use the custom prefunding format file
    public static function customEmployers() {
      return array_merge(self::useCustomFileA, self::useCustomFileB);
    }

    public static function getCustomFileFields ($employerId) {
      if (in_array($employerId, self::useCustomFileA)) {
        return ['Account Number', 'Amount', 'Name', 'Payment Date'];
      } else if (in_array($employerId, self::useCustomFileB)) {
        return ['Account Number', 'Amount', 'Employee ID',];
      }
    }

    public static function getCustomFileType($employerId) {
      // file A
      // A = amount
      // B = Name
      // C = Account number
      // D = Routing
      // E = Type
      // F = Payment Date
      if (in_array($employerId, self::useCustomFileA)) {
        return 'A';
      } else if (in_array($employerId, self::useCustomFileB)) {
        return 'B';
      }
      // A = Account number
      // B = Employee ID
      // C = Amount
      // D = Description
    }

    // hide Rapid transactions set_include_path
    public static function hideRapidTransactions() {
      $res = HideTransactionRecord::findHideRecord('rapid');
      // Log::debug('rapid', $res);
      return $res;
      // return [
      //   ////https://app.asana.com/0/****************/****************/f
      //   422962, //
      //   422926, //
      // ];
    }

    // hide user card transactions
    public static function hideTransactions()
    {
       $res = HideTransactionRecord::findHideRecord('user_card_transaction');
        // Log::debug('user_card_transaction', $res);
        return $res;
        // return [
        //   // https://app.asana.com/0/****************/****************/f
        //   913103,
        //   912493,
        //   913117,
        //   914290,
        //   // https://app.asana.com/0/****************/****************/f
        //   946872,
        //   // Fix for https://app.asana.com/0/****************/1201863409764180/f
        //   1042069,
        //   1042115,
        //   1042118,
        //   1042127,
        //   1042129,
        //   // Move the amount to base agent to fix load from bace agent when changing card for  500176784
        //   1428792,
        //   // Refund the balance that was not refunded when the user change the card on 2021-12-27
        //   931853, // 500135804
        //   931915, // 500134307
        //   931930, // 500135861,
        //   // move the record load from base
        //   1829576, // 500118357
        //   1850034, // 500118357,
        //   // refund money since create transfer error and change card error
        //   1863872, // 500188999 create transfer error
        //   1863876, // 500195887 change card error
        //   // https://app.asana.com/0/****************/1203329405783739/f
        //   2189759,
        //   // https://app.asana.com/0/****************/****************/f
        //   2699344, // hide 2492.21  loading balance after change card
        //   2699341, // hide 3500 for loading balance after change card
        //   2699302, // hide 5992.21 for change card after change employer
        //   2699315, // hide 5992.21 for change card after change employer
        //   2727409, // hide the 161 since refund for the transfer failed
        //   // hide those record for the transfer failed (under review) and  manual load back
        //   2727417,
        //   2727416,
        //   2727413,
        //   2727412,
        //   2727411,
        //   2727410,
        //   2727407,
        //   2727406,
        //   2727405,
        //   2727404,
        //   2727403
        // ];
    }


    public static function hideAccountNumbers()
    {
        return [
            '***********', // Brion
            '***********', // The new middle card
        ];
    }

    public static function getCurpHash($value)
    {
        return sha1('transfermex_' . ($value ?? '') . '_d6,A,%unI=kEe/e3Lme');
    }

    public static function getUserDashboardType()
    {
        return 'mex';
    }

    public static function getAdminDomain()
    {
        if (Util::isLive()) {
            return 'https://admin.transfermex.com';
        }
        if (Util::isStaging()) {
            return 'https://transfermex-test.virtualcards.us';
        }
        return Util::request()->getScheme() . '://mex.span.local';
    }

    public static function getAppDomain()
    {
        if (Util::isLive()) {
            return 'https://app.transfermex.com';
        }
        if (Util::isStaging()) {
            return 'https://transfermex-app-test.virtualcards.us';
        }
        return Util::request()->getScheme() . '://mex-app.span.local';
    }

    public static function getWebAppEntrance()
    {
        return '/mex';
    }

    public static function getWebAppPath()
    {
        return '/static/mex/web/';
    }

    public static function limitAdminDomain()
    {
        $host = strtolower(Util::request()->getSchemeAndHttpHost());
        if (Util::endsWith($host, '.virtualcards.us')) {
            return;
        }
        if (self::getAdminDomain() !== $host) {
            $ex = PortalException::tempPage('Please log into the admin domain to access this page.');
            $ex->redirect = self::getAdminDomain();
            throw $ex;
        }
    }

    public static function apiAdminUserProfile($data)
    {
        $data['cpKey'] = 'cp_mex';
        $user = Util::user();
        if ($user->inTeams([Role::ROLE_TRANSFER_MEX_EMPLOYER, Role::ROLE_TRANSFER_MEX_EMPLOYER_ADMIN, Role::ROLE_TRANSFER_MEX_EMPLOYER_ADMIN_ACH])) {
          $data['adminLayout'] = 'h';
          $data['group'] = $user->getAdminGroup()->toApiArray();
        } else {
          $data['adminLayout'] = 'j'; // New admin portal layout
        }
        return $data;
    }

    public static function getMemberRoles()
    {
        return [
            Role::ROLE_TRANSFER_MEX_MEMBER,
        ];
    }

    public static function getAdminRoles()
    {
        return [
            Role::ROLE_TRANSFER_MEX_ADMIN,
            Role::ROLE_TRANSFER_MEX_AGENT,
            Role::ROLE_TRANSFER_MEX_DEPARTMENT,
            Role::ROLE_TRANSFER_MEX_CAPTURE,
            Role::ROLE_TRANSFER_MEX_EMPLOYER,
            Role::ROLE_TRANSFER_MEX_EMPLOYER_ADMIN,
            Role::ROLE_TRANSFER_MEX_EMPLOYER_ADMIN_ACH
        ];
    }

    public static function getMenusForCurrentUser()
    {
        $user = Util::user();
        $currentEmployer = Util::meta($user, 'currentEmployer');
        if ($currentEmployer) {
          $user = User::find($currentEmployer) ?? $user;
        }
        $sa = $user->isSuperAdmin();
        $admin = $user->inTeam(Role::ROLE_TRANSFER_MEX_ADMIN);
        $department = $user->inTeam(Role::ROLE_TRANSFER_MEX_DEPARTMENT);
        $agent = $user->inTeam(Role::ROLE_TRANSFER_MEX_AGENT);
       // $capture = $user->inTeam(Role::ROLE_TRANSFER_MEX_CAPTURE);
        if (!$sa && !$user->inTeams(\CoreBundle\Utils\Bundle::getAdminRoles())) {
            return [];
        }

        if ($user->inTeam(Role::ROLE_TRANSFER_MEX_EMPLOYER)) {
          $group = $user->getAdminGroup();
          if ($group->getFundingType() == UserGroup::FUNDING_TYPE_ACH) {
            $all = [
              [
                'id' => Module::ID_MEX_EMPLOYER_AGENT,
                'name' => 'Admins',
                'route' => '',
                'mdRoute' => 'mex/employer/admins',
                'icon' => 'fa fa-fw fa-users',
                'mdIcon' => 'mdi-account-multiple',
                'svgIcon' => 'mex_employee_agent',
                'children' => [],
              ],
              [
                'id' => Module::ID_MEX_EMPLOYER_EMPLOYEE,
                'name' => 'Employees',
                'route' => '',
                'mdRoute' => 'mex/employer/employee',
                'icon' => 'fa fa-fw fa-dashboard',
                'mdIcon' => 'mdi-view-dashboard',
                'svgIcon' => 'mex_employees',
                'children' => [],
              ],
            ];
            if (Util::meta($user, 'balanceFilter')) {
              $all = array_merge($all, [
                [
                  'id' => Module::ID_REPORTS,
                  'name' => 'Reports',
                  'route' => '',
                  'mdRoute' => '',
                  'icon' => 'fa fa-fw fa-file-text',
                  'mdIcon' => 'mdi-file-chart-outline font-25',
                  'children' => [
                      [
                          'id' => Module::ID_MEX_REPORT_REVENUE,
                          'name' => 'High Balance Report',
                          'route' => '',
                          'mdRoute' => 'mex/employer/reports/high_balance',
                      ],
                  ]
                ]
              ]);
            }
            return $all;
          }
          $all = [
            [
              'id' => Module::ID_DASHBOARD,
              'name' => 'Dashboard',
              'route' => '',
              'mdRoute' => 'mex/employer/dashboard',
              'icon' => 'fa fa-fw fa-dashboard',
              'mdIcon' => 'mdi-view-dashboard',
              'svgIcon' => 'dashboard',
              'children' => [],
            ],
            [
                  'id' => Module::ID_MEX_EMPLOYER_AGENT,
                  'name' => 'Admins',
                  'route' => '',
                  'mdRoute' => 'mex/employer/admins',
                  'icon' => 'fa fa-fw fa-users',
                  'mdIcon' => 'mdi-account-multiple',
                  'svgIcon' => 'mex_employee_agent',
                  'children' => [],
            ],
            [
              'id' => Module::ID_MEX_EMPLOYER_EMPLOYEE,
              'name' => 'Employees',
              'route' => '',
              'mdRoute' => 'mex/employer/employee',
              'icon' => 'fa fa-fw fa-dashboard',
              'mdIcon' => 'mdi-view-dashboard',
              'svgIcon' => 'mex_employees',
              'children' => [],
            ],
            [
              'id' => Module::ID_MEX_EMPLOYER_PAYMENTS,
              'name' => 'Payments & Deposits',
              'route' => '',
              'mdRoute' => 'mex/employer/payments',
              'icon' => 'fa fa-fw fa-dashboard',
              'mdIcon' => 'mdi-view-dashboard',
              'svgIcon' => 'mex_payment',
              'children' => [],
            ]
          ];
          $report = [
                    'id' => Module::ID_REPORTS,
                    'name' => 'Reports',
                    'route' => '',
                    'mdRoute' => '',
                    'icon' => 'fa fa-fw fa-file-text',
                    'mdIcon' => 'mdi-file-chart-outline font-25',
                    'children' => [
                        [
                            'id' => Module::ID_MEX_REPORT_REVENUE,
                            'name' => 'Daily Employer Activity Report',
                            'route' => '',
                            'mdRoute' => 'mex/employer/reports/daily',
                        ],
                        [
                          'id' => Module::ID_MEX_REPORT_REVENUE,
                          'name' => 'Deposits & Payouts Report',
                          'route' => '',
                          'mdRoute' => 'mex/employer/reports/month',
                        ],
                        [
                          'id' => Module::ID_MEX_PLATFORM_REVENUE,
                          'name' => 'Batch Summary Report',
                          'route' => '',
                          'mdRoute' => 'mex/employer/reports/batchSummary',
                        ],
                        [
                          'id' => Module::ID_MEX_PAYROLL_EXCEPTIONS,
                          'name' => 'Payroll Exceptions',
                          'route' => '',
                          'mdRoute' => 'mex/employer/reports/payroll_exceptions',
                        ],
                    ],
                  ];
          if (Util::meta($user, 'balanceFilter')) {
            $report['children'] = array_merge($report['children'], [
              [
                'id' => Module::ID_MEX_REPORT_REVENUE,
                'name' => 'High Balance Report',
                'route' => '',
                'mdRoute' => 'mex/employer/reports/high_balance',
              ],
            ]);
          }
          return array_merge($all, [$report]);
        }

        if ($user->inTeam(Role::ROLE_TRANSFER_MEX_EMPLOYER_ADMIN)) {
            $all = [
              [
                  'id' => Module::ID_DASHBOARD,
                  'name' => 'Dashboard',
                  'route' => '',
                  'mdRoute' => 'mex/employer/dashboard',
                  'icon' => 'fa fa-fw fa-dashboard',
                  'mdIcon' => 'mdi-view-dashboard',
                  'svgIcon' => 'dashboard',
                  'children' => [],
              ]
            ];
            if (Util::meta($user, 'manageAdmin')) {
              $all = array_merge($all, [
                  [
                        'id' => Module::ID_MEX_EMPLOYER_AGENT,
                        'name' => 'Admins',
                        'route' => '',
                        'mdRoute' => 'mex/employer/admins',
                        'icon' => 'fa fa-fw fa-users',
                        'mdIcon' => 'mdi-account-multiple',
                        'svgIcon' => 'mex_employee_agent',
                        'children' => [],
                  ]
              ]);
            }
            $report =  [
                          [
                              'id' => Module::ID_MEX_REPORT_REVENUE,
                              'name' => 'Daily Employer Activity Report',
                              'route' => '',
                              'mdRoute' => Util::isMasterAdminLoggedInAs() ? 'mex/employer/reports/daily-master' : 'mex/employer/reports/daily',
                          ],
                          [
                            'id' => Module::ID_MEX_REPORT_REVENUE,
                            'name' => 'Deposits & Payouts Report',
                            'route' => '',
                            'mdRoute' => 'mex/employer/reports/month',
                          ],
                          [
                            'id' => Module::ID_MEX_PLATFORM_REVENUE,
                            'name' => 'Batch Summary Report',
                            'route' => '',
                            'mdRoute' => 'mex/employer/reports/batchSummary',
                          ],
                          [
                            'id' => Module::ID_MEX_PAYROLL_EXCEPTIONS,
                            'name' => 'Payroll Exceptions',
                            'route' => '',
                            'mdRoute' => 'mex/employer/reports/payroll_exceptions',
                          ],
                        ];
            if (Util::meta($user, 'balanceFilter')) {
              $report = array_merge($report, [
                [
                  'id' => Module::ID_MEX_REPORT_REVENUE,
                  'name' => 'High Balance Report',
                  'route' => '',
                  'mdRoute' => 'mex/employer/reports/high_balance',
                ],
              ]);
            }
            return array_merge($all, [
                  [
                      'id' => Module::ID_MEX_EMPLOYER_EMPLOYEE,
                      'name' => 'Employees',
                      'route' => '',
                      'mdRoute' => 'mex/employer/employee',
                      'icon' => 'fa fa-fw fa-dashboard',
                      'mdIcon' => 'mdi-view-dashboard',
                      'svgIcon' => 'mex_employees',
                      'children' => [],
                  ],
                  [
                      'id' => Module::ID_MEX_EMPLOYER_PAYMENTS,
                      'name' => 'Payments & Deposits',
                      'route' => '',
                      'mdRoute' => 'mex/employer/payments',
                      'icon' => 'fa fa-fw fa-dashboard',
                      'mdIcon' => 'mdi-view-dashboard',
                      'svgIcon' => 'mex_payment',
                      'children' => [],
                  ],
                  [
                      'id' => Module::ID_REPORTS,
                      'name' => 'Reports',
                      'route' => '',
                      'mdRoute' => '',
                      'icon' => 'fa fa-fw fa-file-text',
                      'mdIcon' => 'mdi-file-chart-outline font-25',
                      'children' => $report,   
                  ]
              ]);
        }

        if ($user->inTeam(Role::ROLE_TRANSFER_MEX_EMPLOYER_ADMIN_ACH)) {
          $all = [
                  [
                      'id' => Module::ID_MEX_EMPLOYER_EMPLOYEE,
                      'name' => 'Employees',
                      'route' => '',
                      'mdRoute' => 'mex/employer/employee',
                      'icon' => 'fa fa-fw fa-dashboard',
                      'mdIcon' => 'mdi-view-dashboard',
                      'svgIcon' => 'mex_employees',
                      'children' => [],
                  ]
          ];
          if (Util::meta($user, 'manageAdmin')) {
            $all = array_merge($all, [
                [
                      'id' => Module::ID_MEX_EMPLOYER_AGENT,
                      'name' => 'Admins',
                      'route' => '',
                      'mdRoute' => 'mex/employer/admins',
                      'icon' => 'fa fa-fw fa-users',
                      'mdIcon' => 'mdi-account-multiple',
                      'svgIcon' => 'mex_employee_agent',
                      'children' => [],
                ]
            ]);
          }
          if (Util::meta($user, 'balanceFilter')) {
            $all = array_merge($all, [
              [
                'id' => Module::ID_REPORTS,
                'name' => 'Reports',
                'route' => '',
                'mdRoute' => '',
                'icon' => 'fa fa-fw fa-file-text',
                'mdIcon' => 'mdi-file-chart-outline font-25',
                'children' => [
                    [
                        'id' => Module::ID_MEX_REPORT_REVENUE,
                        'name' => 'High Balance Report',
                        'route' => '',
                        'mdRoute' => 'mex/employer/reports/high_balance',
                    ],
                ]
              ]
            ]);
          }
          return $all;
        }


        $all = [];

        // dashboad menu
        if ($sa || $admin || $department || $agent) {
          $all[] = [
                      'id' => Module::ID_DASHBOARD,
                      'name' => 'Dashboard',
                      'route' => '',
                      'mdRoute' => 'mex/dashboard',
                      'icon' => 'fa fa-fw fa-dashboard',
                      'mdIcon' => 'mdi-view-dashboard',
                      'svgIcon' => 'dashboard',
                      'children' => [],
                    ];
        }
        // agent menu
        if ($sa || $admin || $department) {
            $all = array_merge($all,  [
               // program menu
                [
                  'id' => Module::ID_MEX_PROGRAM,
                  'name' => 'Program',
                  'route' => '',
                  'mdRoute' => 'mex/program',
                  'icon' => 'fa fa-fw fa-users',
                  'mdIcon' => 'mdi-account-multiple',
                  'children' => [],
                ],
                // promo menu
                [
                  'id' => Module::ID_MEX_PROMO,
                  'name' => 'Promo Summary',
                  'route' => '',
                  'mdRoute' => 'mex/promo',
                  'icon' => 'fa fa-fw fa-users',
                  'mdIcon' => 'mdi-account-multiple',
                  'svgIcon' => 'mex_promo',
                  'children' => [],
                ],
                // agent menu
                [
                  'id' => Module::ID_MEX_AGENTS,
                  'name' => 'Agents',
                  'route' => '',
                  'mdRoute' => 'mex/agents',
                  'icon' => 'fa fa-fw fa-users',
                  'mdIcon' => 'mdi-account-multiple',
                  'svgIcon' => 'mex_agents',
                  'children' => [],
                ],
                // employer menu
                [
                  'id' => Module::ID_MEX_EMPLOYERS,
                  'name' => 'Employers',
                  'route' => '',
                  'mdRoute' => 'mex/employers',
                  'icon' => 'fa fa-fw fa-users',
                  'mdIcon' => 'mdi-account-multiple',
                  'svgIcon' => 'mex_employers',
                  'children' => [],
                ]
            ]);
        }

         // employee menu
        $all = array_merge($all, [
            [
                'id' => Module::ID_MEX_MEMBERS,
                'name' => 'Members',
                'route' => '',
                'mdRoute' => 'mex/members',
                'icon' => 'fa fa-fw fa-users',
                'mdIcon' => 'mdi-account-multiple',
                'svgIcon' => 'mex_members',
                'children' => [],
            ]
        ]);

        // transfer menu
        if ($sa || $admin || $agent) {
          $all[] =  [
                        'id' => Module::ID_MEX_TRANSFERS,
                        'name' => 'Transfers',
                        'route' => '',
                        'mdRoute' => 'mex/transfers',
                        'icon' => 'fa fa-fw fa-users',
                        'mdIcon' => 'mdi-account-multiple',
                        'svgIcon' => 'mex_transfers',
                        'children' => [],
                    ];
        }
        if ($sa || $admin) {
            $all[] = [
                'id' => Module::ID_REPORTS,
                'name' => 'Reports',
                'route' => '',
                'mdRoute' => '',
                'icon' => 'fa fa-fw fa-file-text',
                'mdIcon' => 'mdi-file-chart',
                'children' => [
                    [
                        'id' => Module::ID_MEX_MONTHLY_REPORT_PARTNER,
                        'name' => 'Partner Snapshot Report',
                        'route' => '',
                        'mdRoute' => 'mex/admin/monthly/partner',
                    ],
                    [
                        'id' => Module::ID_MEX_WORKERS_REPORT,
                        'name' => 'Workers Report',
                        'route' => '',
                        'mdRoute' => 'mex/worker_report',
                    ],
                    [
                        'id' => 'mex_admin_ach_reports',
                        'name' => 'Employer ACH Direct Report',
                        'route' => '',
                        'mdRoute' => 'mex/ach_report',
                    ],
                    [
                      'id' => Module::ID_MEX_PAYROLL_EXCEPTIONS,
                      'name' => 'Payroll Exceptions',
                      'route' => '',
                      'mdRoute' => 'mex/payroll_exceptions',
                    ],
                    [
                      'id' => Module::ID_MEX_PAYROLL_REPORT,
                      'name' => 'Payroll detail Report',
                      'route' => '',
                      'mdRoute' => 'mex/payroll_detail_report',
                    ],
                    [
                      'id' => Module::ID_MEX_PAYROLL_BATCH_REPORT,
                      'name' => 'Payroll Batch Report',
                      'route' => '',
                      'mdRoute' => 'mex/payroll_batch_report',
                    ],
                    [
                      'id' => Module::ID_MEX_LOAD_TRANSFER_REPORT,
                      'name' => 'Load & Transfer Report',
                      'route' => '',
                      'mdRoute' => 'mex/load_transfer_report',
                    ],
                    [
                      'id' => Module::ID_MEX_KYC_EXCEPTIONS,
                      'name' => 'KYC Exceptions',
                      'route' => '',
                      'mdRoute' => 'mex/kyc_exceptions',
                    ]
                ],
            ];
        }

        if ($sa) {
            $all[] = [
                'id' => Module::ID_MEX_ADMIN,
                'name' => 'Administration',
                'route' => '',
                'mdRoute' => '',
                'icon' => 'fa fa-fw fa-users',
                'mdIcon' => 'mdi-cube-outline',
                'svgIcon' => false,
                'children' => [
                    [
                        'id' => Module::ID_MEX_TRANSFER_FUNDINGS,
                        'name' => 'Rapyd Daily Settlement',
                        'route' => '',
                        'mdRoute' => 'mex/fundings'
                    ],
                    [
                      'id' => Module::ID_MEX_UNITELLER_FUNDINGS,
                      'name' => 'UniTeller Daily Settlement',
                      'route' => '',
                      'mdRoute' => 'mex/uniteller-fundings'
                   ],
                   [
                    'id' => Module::ID_MEX_INTERMEX_FUNDINGS,
                    'name' => 'Intermex Daily Settlement',
                    'route' => '',
                    'mdRoute' => 'mex/intermex-fundings'
                    ],
                    [
                        'id' => Module::ID_MEX_ADMIN_RAPYD_HISTORY,
                        'name' => 'Rapyd Transfers Account History',
                        'route' => '',
                        'mdRoute' => 'mex/admin/rapyd_history',
                    ],
                    [
                      'id' => Module::ID_MEX_ADMIN_UNITELLER_HISTORY,
                      'name' => 'UniTeller Transfers Account History',
                      'route' => '',
                      'mdRoute' => 'mex/admin/uniteller_history',
                    ],
                    [
                        'id' => 'mex_total_revenue',
                        'name' => 'Total Revenue',
                        'route' => '',
                        'mdRoute' => 'mex/revenue/total'
                    ],
//                    [
//                        'id' => Module::ID_MEX_PLATFORM_REVENUE,
//                        'name' => 'Platform Revenue',
//                        'route' => '',
//                        'mdRoute' => 'mex/revenue/platform'
//                    ],
                    [
                        'id' => Module::ID_MEX_MONTHLY_REPORT,
                        'name' => 'Platform Snapshot Report',
                        'route' => '',
                        'mdRoute' => 'mex/admin/monthly/platform',
                    ],
                    [
                        'id' => Module::ID_MEX_MONTHLY_BANK_REPORT,
                        'name' => 'Bank Snapshot Report',
                        'route' => '',
                        'mdRoute' => 'mex/admin/monthly_bank/report',
                    ],
                    [
                        'id' => Module::ID_MEX_ADMIN_RAPID_HISTORY,
                        'name' => 'Rapid Agent Account History',
                        'route' => '',
                        'mdRoute' => 'mex/admin/rapid_history',
                    ],
//                    [
//                        'id' => Module::ID_MEX_REPORT_REVENUE,
//                        'name' => 'Partner Revenue (Legacy)',
//                        'route' => '',
//                        'mdRoute' => 'mex/revenue/partner',
//                    ],
                    [
                       'id' => 'uniteller_transfers',
                       'name' => 'UniTeller Transfers',
                       'route' => '',
                       'mdRoute' => 'mex/admin/uniteller-transfers',
                    ],
                    [
                      'id' => 'intermex_transfers_report',
                      'name' => 'Intermex Transfer Wires',
                      'route' => '',
                      'mdRoute' => 'mex/admin/intermex-transfers',
                    ],
                    [
                      'id' => 'uniteller_transfers_report',
                      'name' => 'UniTeller Transfer Summary',
                      'route' => '',
                      'mdRoute' => 'mex/admin/uniteller-transfer-report',
                    ],
                    [
                      'id' => Module::ID_MEX_INACTIVE_MEMBER_FEE_REPORT,
                      'name' => 'Inactive Member Fee Record',
                      'route' => '',
                      'mdRoute' => 'mex/inactive_fee/report'
                    ],
                ],
            ];
            $all[] = [
              'id' => Module::ID_MEX_PROGRAM_SUMMARY,
              'name' => 'Program Summary Reports',
              'route' => '',
              'mdRoute' => '',
              'icon' => 'fa fa-fw fa-users',
              'mdIcon' => 'mdi-file-chart',
              'svgIcon' => false,
              'direction' => 'column',
              'children' => [
                  [
                      'id' => Module::ID_MEX_PROGRAM_SUMMARY_PREFUND_ACH,
                      'name' => 'Prefund Employer ACH Deposit Report',
                      'route' => '',
                      'mdRoute' => 'mex/summary/prefund-ach'
                  ],
                  [
                      'id' => Module::ID_MEX_PROGRAM_SUMMARY_PREFUND_LOAD,
                      'name' => 'Prefund Employer to Employee Load Report',
                      'route' => '',
                      'mdRoute' => 'mex/summary/prefund-load',
                  ],
                  [
                      'id' => Module::ID_MEX_PROGRAM_SUMMARY_EMPLOYER_ACH,
                      'name' => 'Employer to Employee ACH Direct Report',
                      'route' => '',
                      'mdRoute' => 'mex/summary/employer-ach'
                  ],
                  [
                      'id' => Module::ID_MEX_PROGRAM_SUMMARY_TRANSFER,
                      'name' => 'Employee to Recipient Mexico Transfer Report',
                      'route' => '',
                      'mdRoute' => 'mex/summary/employer-transfer'
                  ],
                  [
                    'id' => Module::ID_MEX_PROGRAM_SUMMARY_TRANSFER,
                    'name' => 'Employer Report',
                    'route' => '',
                    'mdRoute' => 'mex/summary/employer-report'
                ],
              ],
          ];
        }

        if ($sa || $admin) {
            $all = array_merge($all, [
              [
                'id' => Module::ID_MEX_MESSAGE_CENTER,
                'name' => 'Message Center',
                'route' => '',
                'mdRoute' => 'mex/message-center',
                'icon' => '',
                'mdIcon' => 'mdi-message-processing-outline',
                'svgIcon' => false,
                'children' => [
                  [
                    'id' => Module::ID_MEX_MESSAGE_CENTER_BATCH,
                    'name' => 'Campaign Summary Report',
                    'route' => '',
                    'mdRoute' => 'mex/message-center/batch'
                ],
                [
                    'id' => Module::ID_MEX_MESSAGE_CENTER_RECORD,
                    'name' => 'Message Detail Report',
                    'route' => '',
                    'mdRoute' => 'mex/message-center/record'
                ],
                ],
              ]
            ]);
            $all[] = [
                'id' => Module::ID_MEX_SETTINGS,
                'name' => 'System Settings',
                'route' => '',
                'mdRoute' => 'mex/settings',
                'icon' => 'fa fa-fw fa-users',
                'mdIcon' => 'mdi-account-multiple',
                'svgIcon' => 'mex_settings',
                'children' => [
                    [
                        'id' => Module::ID_MEX_SETTINGS_PAYERS,
                        'name' => 'Uniteller Payers',
                        'route' => '',
                        'mdRoute' => 'mex/settings/payers'
                    ],
                    [
                        'id' => Module::ID_MEX_SETTINGS_LOCATION,
                        'name' => 'Locations',
                        'route' => '',
                        'mdRoute' => 'mex/settings/locations'
                    ],
                    [
                        'id' => Module::ID_MEX_SETTINGS_LOCATION_REQUEST,
                        'name' => 'Location Requests',
                        'route' => '',
                        'mdRoute' => 'mex/settings/location-requests'
                    ],
                    [
                        'id' => Module::ID_MEX_SETTINGS_WEBVIEW_URLS,
                        'name' => 'Mobile/Web App',
                        'route' => '',
                        'mdRoute' => 'mex/settings/webview'
                    ],
                    [
                        'id' => Module::ID_MEX_NOTIFIY_EMAIL,
                        'name' => 'Notification Email Recipients',
                        'route' => '',
                        'mdRoute' => 'mex/settings/notify-email'
                    ],
                    [
                        'id' => Module::ID_MEX_SPLASH_PAGE,
                        'name' => 'Splash Page',
                        'route' => '',
                        'mdRoute' => 'mex/settings/splash-page'
                    ],
                    [
                        'id' => Module::ID_MEX_TRANSACTION_GROUP,
                        'name' => 'Transaction Groups',
                        'route' => '',
                        'mdRoute' => 'mex/settings/transaction-groups'
                    ]
                    // [
                    //     'id' => Module::ID_MEX_INTERMEX_PAYER,
                    //     'name' => 'Intermex Payer',
                    //     'route' => '',
                    //     'mdRoute' => 'mex/settings/branch'
                    // ]
                ],
            ];
        }

        return $all;
    }

    public static function getLocalizeJsOptions()
    {
        return [
            'key' => 'hdtYbyw94kL0F',
            'rememberLanguage' => true,
            'autoApprove' => true,
        ];
    }

    public static function getAdminPortalHtmlSuffix()
    {
        $options = Util::j2s(self::getLocalizeJsOptions());
        return <<<EOT
<script src="https://global.localizecdn.com/localize.js"></script>
<script>!function(a){if(!a.Localize){a.Localize={};for(var e=["translate","untranslate","phrase","initialize","translatePage","setLanguage","getLanguage","detectLanguage","getAvailableLanguages","untranslatePage","bootstrap","prefetch","on","off","hideWidget","showWidget","getSourceLanguage"],t=0;t<e.length;t++)a.Localize[e[t]]=function(){}}}(window);</script>

<script>
  Localize.initialize($options);
</script>
EOT;
    }

    public static function isIntermexEnabled()
    {
        $config = Util::meta(Util::platform(), 'enableIntermex');
        return $config !== false;
    }

    public static function isTransferEnabled()
    {
        $config = Util::meta(Util::platform(), 'enableTransfer');
        return $config !== false;
    }

    public static function isCashPickupTransferEnabled()
    {
        $config = Util::meta(Util::platform(), 'enableCashPickupTransfer');
        return $config !== false;
    }

    public static function checkMaintenance ($type) {
        $maintenanceTimeList = Data::getArray('transfer_mex_maintenance_list_' . $type);
        if (empty($maintenanceTimeList)) {
          return false;
        }
        $flag = false;
        foreach ($maintenanceTimeList as $maintenanceTime) {
          $now = Carbon::now();
          $start = new Carbon($maintenanceTime['Start Date']);
          $end = new Carbon($maintenanceTime['End Date']);
          if ($now->gte($start) && $now->lte($end)) {
            $flag = true;
          }
        }

        return $flag;
    }

    public static function isTransferInstantEnabled()
    {
        $config = Util::meta(Util::platform(), 'enableTransferInstant');
        return $config !== false;
    }

    public static function isCashPickTransferInstantEnabled()
    {
        $config = Util::meta(Util::platform(), 'enableCashPickupTransferInstant');
        return $config !== false;
    }

    public static function isIntermexTransferInstantEnabled()
    {
        $config = Util::meta(Util::platform(), 'enableIntermexTransferInstant');
        return $config !== false;
    }

    public static function isBalanceViewingAllowed()
    {
        $config = Util::meta(Util::platform(), 'enableBalance');
        return $config !== false;
    }

    public static function getMinTransferAmount(UserCard $uc = null)
    {
        if ($uc && $uc->isRapidCard()) {
            return 10;
        }
        return (int)(Util::meta(Util::platform(), 'minTransferAmount') ?? 2500);
    }

    public static function getUniTellerMinTransferAmount()
    {
        return (int)(Util::meta(Util::platform(), 'minUniTellerTransferAmount') ?? 10000);
    }

    public static function getMaxTransferAmount()
    {
        return (int)(Util::meta(Util::platform(), 'maxTransferAmount') ?? 500000);
    }

    public static function getMaxUniTellerTransferAmount()
    {
        return (int)(Util::meta(Util::platform(), 'maxUniTellerTransferAmount') ?? 250000);
    }

    public static function getIntermexMinTransferAmount()
    {
        return (int)(Util::meta(Util::platform(), 'minIntermexTransferAmount') ?? 4500);
    }

    public static function getIntermexMaxTransferAmount()
    {
        return (int)(Util::meta(Util::platform(), 'maxIntermexTransferAmount') ?? 300000);
    }

    public static function isSecureApp()
    {
        return MobileBundle::getClientInnerVer() >= 20220725 || Util::isWebRequest();
    }

    public static function isUniTellerApp()
    {
        return MobileBundle::getClientInnerVer() >= 20230506 || Util::isWebRequest();
    }
}
