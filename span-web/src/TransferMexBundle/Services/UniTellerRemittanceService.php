<?php


namespace TransferMexBundle\Services;


use Carbon\Carbon;
use CoreBundle\Entity\Config;
use CoreBundle\Entity\ExternalInvoke;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\Transfer;
use CoreBundle\Entity\UserCard;
use CoreBundle\Exception\FailedException;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Util;
use PortalBundle\Exception\PortalException;
use SalexUserBundle\Entity\User;
use TransferMexBundle\Entity\ImportLocationErrorRecord;
use TransferMexBundle\Entity\Recipient;
use TransferMexBundle\TransferMexBundle;
use CoreBundle\Entity\Country;
use CoreBundle\Entity\BaseState;
use CoreBundle\Entity\Currency;
use TransferMexBundle\Entity\ImportLocationsRecord;
use CoreBundle\Utils\Excel;
use CoreBundle\Entity\Email;

class UniTellerRemittanceService
{
    public const FEE_BANK_TRANSFER = 100; // $1.00 USD
    public const FEE_CASH_PICKUP = 300; // $3.00 USD

    public const OTHER_COUNTRY_FEE = [
      'bank' => [
        'SV' => 500 // $5.00 USD
      ],
      'cash' => [
        'SV' => 500 // $5.00 USD
      ]
    ];

    public const COST_BANK_TRANSFER = 0; // Fee charged by UniTeller
    public const COST_CASH_PICKUP = 0; // Fee charged by UniTeller

    public const RATE_MARK_UP_PARTNER = 0.02; // Partner(TransferMex)'s revenue
    public const RATE_MARK_UP_PLATFORM = 0; // Tern Platform's revenue. Was 0.006 for some initial transfers

    public const LOW_BALANCE_FOR_REGULAR_TRANSFER = -********; // -100k. UniTeller balance has a delay

    public const STATUSES_FOR_SETTLEMENT_REVERSE = [
      Transfer::STATUS_COMPLETED,
      Transfer::STATUS_CANCELED,
      Transfer::STATUS_ERROR,
      Transfer::STATUS_RETURNED
    ];

    public const CASH_PICK_DISABLE_LIST = [
      'ENVICON',
      'FAHORRO'
    ];
    public const COST_CASH_PICKUP_LIST  = [
      'EK699'       => 550, // Elektra
      'EK699_BANCO' => 550, // Banco Azteca
      'BANCO_AZ'    => 550, // Banco Azteca
      'BBV1'        => 425, // BBVA Bancomer
      'OXXOTN'      => 425, // OXXO
      'BCP99'       => 325, // BanCoppel
      'TLC'         => 325, // Telecomm
      'INTERMEX'    => 325, // Intermex
      'BCL97'       => 325, // BANCO COPPEL A
      'BSF99'       => 275, // Bansefi
      '7ELEVEN'     => 275, // 7-eleven
      'WMT99'       => 275, // Walmart
      'WMTAUR99'    => 275, // Bodega Aurrera
      'BANORTE'     => 275, // Banorte
      'WMTSUP99'    => 275,   // Walmart Express,
      'CPM'         => 275, // C.P.M
      // 'ENVICON'     => 275, // Env .Confianza    ------
      'FAMSATN'     => 275, // Famsa
      'FRAGUA'      => 275, // Far Guadalajara
      'ISSEG'       => 275, // ISSEG
      'SORIANA'     => 275, // Soriana
      'SOL'         => 275, // Del Sol
      'WOO'         => 275, // Woolworth
      'SUBURBIA'    => 275, // Suburbia,
      'WLD97'       => 275, // WALDOS
      // 'FAHORRO'     => 275, // Farmacias Del Ahorro
      'LIVERPL'     => 275, // Liverpool
      'SCOTIA'      => 275, // Scotiabank don't support cash pickup,but still set 2.75
      'SANTANDER'   => 275, // Santander don't support cash pickup,but still set 2.75
      'QUE10'       => 275,
      // HN Payer
      'HCA7'        => 275, // B. de Occidente
      'HCA2'        => 275, // Banco FICOHSA
      'HCA10'       => 275, // Cooperativa Ahorro Cacil
      'EK499'       => 275, // Elektra
      'HCA9'        => 275, // Banrural
      'HCA6'        => 275, // Banco Atlantida
      'BNPS94'      => 275, // BANPAIS
      'HCA11'       => 275, // Banco Popular S.A
      'HSH99'       => 275, // Banco Davivienda Honduras
      // EL Salvador Payer
      'SCA4'        => 275,  // Banco Agricola
      'SCA10'       => 275, // Davivienda El Salvador
      'SCA2'        => 275, // Banco Promerica
      'SCA7'        => 275, // Fedecredito
      'CUSC98'      => 275, // Banco Cuscatlan
      'ATLSV'       => 275, // Banco Atlantida El Salvador
      'BISV94'      => 275, // BANCO INDUSTRIAL
      'CITS0298'    => 275, // B. F. A
      'SCA5'        => 275, // Fedecaces
      'CRS99'       => 275, // Banco Credomatic
    ];
    public const MIN_CASH_PICKUP_LIST  = [
      'EK699'       => 12500, // Elektra
      'EK699_BANCO' => 12500, // Banco Azteca
      'BANCO_AZ'    => 12500, // Banco Azteca
      'BBV1'        => 7500, // BBVA Bancomer
      'OXXOTN'      => 7500, // OXXO
      'BSF99'       => 5000, // Bansefi
      'BCP99'       => 5000, // BanCoppel
      'TLC'         => 5000, // Telecomm ---
      'INTERMEX'    => 5000, // Intermex
      'BCL97'       => 5000, // BANCO COPPEL A ---
      '7ELEVEN'     => 2600, // 7-eleven
      'WMT99'       => 2600, // Walmart
      'WMTAUR99'    => 2600, // Bodega Aurrera
      'BANORTE'     => 2600, // Banorte
      'WMTSUP99'    => 2600,   // Walmart Express,
      'SCOTIA'      => 2600, // Scotiabank  -----
      'CPM'         => 2600, // C.P.M
      // 'ENVICON'     => 2600, // Env .Confianza    ------
      'FAMSATN'     => 2600, // Famsa   -----
      'FRAGUA'      => 2600, // Far Guadalajara  -----
      'ISSEG'       => 2600, // ISSEG
      'SORIANA'     => 2600, // Soriana
      'SOL'         => 2600, // Del Sol ------
      'WOO'         => 2600, // Woolworth
      'SANTANDER'   => 2600, // Santander  -----
      'SUBURBIA'    => 2600, // Suburbla,
      'WLD97'       => 2600, // WALDOS
      // 'FAHORRO'     => 2600, // Farmacias Del Ahorro
      'LIVERPL'     => 2600, // Liverpool
      'QUE10'       => 2600,

      // HN Payer
      'HCA7'        => 2600, // B. de Occidente
      'HCA2'        => 2600, // Banco FICOHSA
      'HCA10'       => 2600, // Cooperativa Ahorro Cacil
      'EK499'       => 2600, // Elektra
      'HCA9'        => 2600, // Banrural
      'HCA6'        => 2600, // Banco Atlantida
      'BNPS94'      => 2600, // BANPAIS
      'HCA11'       => 2600, // Banco Popular S.A
      'HSH99'       => 2600, // Banco Davivienda Honduras
    ];
    public const CASH_PICKUP_NAME_LIST  = [
      'EK699'       => 'Elektra',
      'EK699_BANCO' => 'Banco Azteca',
      'BANCO_AZ'    => 'Banco Azteca', // Banco Azteca
      'BBV1'        => 'BBVA Bancomer',
      'OXXOTN'      => 'OXXO',
      'BSF99'       => 'Bansefi',
      'BCP99'       => 'BanCoppel',
      'TLC'         => 'Telecomm',
      'INTERMEX'    => 'Intermex',
      'BCL97'       => 'BANCO COPPEL',
      '7ELEVEN'     => '7-eleven',
      'WMT99'       => 'Walmart',
      'WMTAUR99'    => 'Bodega Aurrera',
      'BANORTE'     => 'Banorte',
      'WMTSUP99'    => 'Walmart Express',
      'SCOTIA'      => 'Scotiabank',
      'CPM'         => 'C.P.M',
      // 'ENVICON'     => 'Env .Confianza',
      'FAMSATN'     => 'Famsa',
      'FRAGUA'      => 'Far Guadalajara',
      'ISSEG'       => 'ISSEG',
      'SORIANA'     => 'Soriana',
      'SOL'         => 'Del Sol',
      'WOO'         => 'Woolworth',
      'SANTANDER'   => 'Santander',
      'SUBURBIA'    => 'Suburbla',
      'WLD97'       => 'WALDOS',
      // 'FAHORRO'     => 'Farmacias Del Ahorro',
      'LIVERPL'     => 'Liverpool',
      'QUE10'       => 'CAJA POPULAR LAS HUASTECAS',

      // HN Payer
      'HCA7'        => 'B. de Occidente',
      'HCA2'        => 'Banco FICOHSA',
      'HCA10'       => 'Cooperativa Ahorro Cacil',
      'EK499'       => 'Elektra',
      'HCA9'        => 'Banrural',
      'HCA6'        => 'Banco Atlantida',
      'BNPS94'      => 'BANPAIS',
      'HCA11'       => 'Banco Popular S.A',
      'HSH99'       => 'Banco Davivienda Honduras',
      // EL Salvador Payer
      'SCA4'        => 'Banco Agricola',
      'SCA10'       => 'Davivienda El Salvador',
      'SCA2'        => 'Banco Promerica',
      'SCA7'        => 'Fedecredito',
      'CUSC98'      => 'Banco Cuscatlan',
      'ATLSV'       => 'Banco Atlantida El Salvador',
      'BISV94'      => 'BANCO INDUSTRIAL',
      'CITS0298'    => 'B. F. A',
      'SCA5'        => 'Fedecaces',
      'CRS99'       => 'Banco Credomatic',
    ];
    public const CASH_PICKUP_LOGO_LIST  = [
      'EK699'       => 'EK699.jpg',
      'EK699_BANCO' => 'EK699_BANCO.jpg', // Banco Azteca
      'BANCO_AZ'    => 'EK699_BANCO.jpg', // Banco Azteca
      'BBV1'        => 'BBV1.jpg', // BBVA Bancomer
      'OXXOTN'      => 'OXXOTN.jpg', // OXXO
      'BSF99'       => 'BSF99.jpg', // Bansefi
      'BCP99'       => 'BCP99.jpg', // BanCoppel
      'TLC'         => 'TLC.png', // Telecomm ---
      'INTERMEX'    => 'INTERMEX.jpg', // Intermex
      'BCL97'       => 'BCP99.jpg', // BANCO COPPEL A ---
      '7ELEVEN'     => '7ELEVEN.jpg', // 7-eleven
      'WMT99'       => 'WMT99.jpg', // Walmart
      'WMTAUR99'    => 'WMTAUR99.jpg', // Bodega Aurrera
      'BANORTE'     => 'BANORTE.jpg', // Banorte
      'WMTSUP99'    => 'WMTSUP99.jpg',   // Walmart Express,
      'SCOTIA'      => 'SCOTIA.png', // Scotiabank  -----
      'CPM'         => 'CPM.jpg', // C.P.M
      // 'ENVICON'     => 'ENVICON.jpg', // Env .Confianza    ------
      'FAMSATN'     => 'FAMSATN.png', // Famsa   -----
      'FRAGUA'      => 'FRAGUA.png', // Far Guadalajara  -----
      'ISSEG'       => 'ISSEG.jpg', // ISSEG
      'SORIANA'     => 'SORIANA.jpg', // Soriana
      'SOL'         => 'SOL.png', // Del Sol ------
      'WOO'         => 'WOO.jpg', // Woolworth
      'SANTANDER'   => 'SANTANDER.png', // Santander  -----
      'SUBURBIA'    => 'SUBURBIA.jpg', // Suburbla,
      'WLD97'       => 'WLD97.jpg', // WALDOS
      // 'FAHORRO'     => 'FAHORRO.jpg', // Farmacias Del Ahorro
      'LIVERPL'     => 'LIVERPL.jpg', // Liverpool
      'QUE10'       => 'QUE10.jpg'
    ];
    public const COST_BANK_LIST = [
      'EK699_BANCO' => 350, // Banco Azteca
      'BANCO_AZ'    => 350,
      'BCP99'       => 285
    ];
    public const COUNTRY_COST_BANK_LIST = [
      'SV'  => 300,
      'HN'  => 275,
      'PA'  => 300
    ];

    public const COUNTRY_MIN_BANK_LIST = [
      'SV'  => 2500,
      'HN'  => 9000,
      'PA'  => 10000
    ];

    public const MIN_BANK_LIST = [
      'EK699_BANCO' => 12500, // Banco Azteca
      'BANCO_AZ'    => 12500,
      'BCP99'       => 9500
    ];
    public static function getMinAmountByType($payerCoed, $type, $countryCode = 'MX') {
      if ($type == 'cash') {
        return UniTellerRemittanceService::MIN_CASH_PICKUP_LIST[$payerCoed] ?? 2500;
      }
      if ($type == 'bank') {
        $min = UniTellerRemittanceService::MIN_BANK_LIST[$payerCoed] ?? 2500;
        if ($countryCode != 'MX') {
          $min = isset(self::COUNTRY_MIN_BANK_LIST[$countryCode]) ? self::COUNTRY_MIN_BANK_LIST[$countryCode] : 10000;
        }
        if (Config::get('uniteller_bank_test_mode', 0)) {
          $min = 100;
        }
        return $min;
      }
      return 2500;
    }
    public static function getLowestBalanceForRegularTransfer()
    {
        return (int)Config::get('uniteller_low_balance_for_transfers',
            self::LOW_BALANCE_FOR_REGULAR_TRANSFER);
    }

    public static function feeByType($type, $countryCode)
    {
        $fee = $type === 'bank' ? self::FEE_BANK_TRANSFER : self::FEE_CASH_PICKUP;
        if ($countryCode) {
          $fee = isset(self::OTHER_COUNTRY_FEE[$type][$countryCode]) ? self::OTHER_COUNTRY_FEE[$type][$countryCode] : $fee;
        }
        return $fee;
    }

    public static function costByType($type, $methodType, $countryCode = 'MX')
    {
        if ( $type === 'bank') {
          if ($countryCode != 'MX') {
            return isset(self::COUNTRY_COST_BANK_LIST[$countryCode]) ? self::COUNTRY_COST_BANK_LIST[$countryCode] : 300;
          }
          return isset(self::COST_BANK_LIST[$methodType]) ? self::COST_BANK_LIST[$methodType] : 100;
        }
        return isset(self::COST_CASH_PICKUP_LIST[$methodType]) ? self::COST_CASH_PICKUP_LIST[$methodType] : 0;
    }

    public static function revenueByType($type, $amount, $methodType, Recipient $rr, $isTransferFree = false)
    {
        $fee = $isTransferFree ? 0 : self::feeByType($type, $rr->getUser()->getCountry()->getIsoCode());
        $cost = self::costByType($type, $methodType, $rr->getUser()->getCountry()->getIsoCode());
        $rate = $rr->getCurrency() == 'USD' ? 0 : self::RATE_MARK_UP_PARTNER;
        $feeRevenue = $fee - $cost;
        $rateRevenue = $amount * $rate;

        return $feeRevenue + $rateRevenue;
    }

    public static function platformRevenueByType($amount, Transfer $transfer = null)
    {
        $rate = self::RATE_MARK_UP_PLATFORM;
        if ($transfer) {
            $rates = Util::meta($transfer, 'fxRateMarkup');
            if (isset($rates[1])) {
                $rate = $rates[1];
            }
        }
        return $amount * $rate;
    }

    protected static function validatePayout(Transfer $transfer)
    {
        if (!$transfer->getPartnerId()) {
            throw PortalException::create('The transfer has not been initialized yet!');
        }
    }

    public static function updateTransfer(Transfer $transfer)
    {
        self::validatePayout($transfer);
        /** @var ExternalInvoke $ei */
        [$ei, $data] = self::getTransactionDetail($transfer->getSender(), $transfer->getPartnerId());
        if ($ei && $ei->isFailed()) {
            throw PortalException::create($ei->getError());
        }
        return self::updateTransferByPayout($data, $transfer);
    }

    public static function securityQuestions() {
        $api = new UniTellerAPI();
        $params = [
          'partnerCode' => Util::getConfigKey('uniteller_partner_code'),
        ];
        $questions = [];
        [$ei, $res] = $api->getSecurityQuestions($params);
        if (isset($res['securityQuestion'])) {
          $questions = $res['securityQuestion'];
        }

        if ($ei->isFailed()) {
          $msg =  'Sorry that the operation failed due to an error (code: 08). We are investigating. Please reach out to our support if it failed constantly.';
          throw PortalException::temp($msg);
        }
        $res = [];
        foreach ($questions as $item) {
          $res[] = [
            'value' => $item['questionId'],
            'label' => $item['question']
          ];
        }
        return $res;
    }

    public static function getUniTellerId(User $user) {
      return Util::meta($user, 'UniTellerUserID') ?? $user->getId() . $user->getEmail();
    }

    public static function checkDupllcateUser(User $user) {
        $api = new UniTellerAPI();

        $email = Util::InitSpecialEmail($user->getId() . $user->getEmail());

        $params = [
          'partnerCode' => Util::getConfigKey('uniteller_partner_code'),
          'emailAddress' => $email
        ];
        $isDupllcate = false;
        [$ei, $res] = $api->checkDupllcateUser($params);
        if (isset($res['responseCode']) && $res['responseCode'] == '50107007' ) {
          $isDupllcate = true;
        }

        if ($ei->isFailed()) {
          $msg =  'Sorry that the operation failed due to an error (code: 09). We are investigating. Please reach out to our support if it failed constantly.';
          throw PortalException::temp($msg);
        }

        return $isDupllcate;
    }

    public static function registerUniTeller(User $user, $answer) {
        $api = new UniTellerAPI();
        if ($user->getCountry() && $user->getCountry()->isUSA() ) {
          $graoupAdmin = $user;
        } else {
          $graoupAdmin = $user->getPrimaryGroupAdmin();
        }
        $email = Util::InitSpecialEmail($user->getId() . $user->getEmail());

        $params = [
          'partnerCode' => Util::getConfigKey('uniteller_partner_code'),
          'privacyPolicyAgreement' => 'Yes',
          'userIPAddress' => $api->getIP(),
          'questionId' => $answer['id'],
          'answer' => $answer['answer'],
          'answerHint' => $answer['answerHint'] ?? '',
          'remitterPersonalInfo' => [
            'emailAddress' => $email,
            'firstName' => $user->getFirstName(),
            'lastName' => $user->getLastName(),
            'birthDate' => Util::formatBirthday($user->getBirthday(), 'mdY'),
            'cellPhone' => Util::getPhoneDigitsWithOutCountryIso($user->getMobilePhone()),
            'destinationCountryISOCode' => 'MX', // $user->getCountry() ? $user->getCountry()->getIsoCode() : '',
            'address' => [
              'address1' => $graoupAdmin->getAddress(),
              'city' => $graoupAdmin->getCity(),
              'state' => $graoupAdmin->getState() ? $graoupAdmin->getState()->getAbbr() : '',
              'stateName' => $graoupAdmin->getState() ? $graoupAdmin->getState()->getAbbrOrName() : '',
              'country' => $graoupAdmin->getCountry() ? $graoupAdmin->getCountry()->getName() : '',
              'countryISOCode' => $graoupAdmin->getCountry() ? $graoupAdmin->getCountry()->getIsoCode() : '',
              'zipCode' => $graoupAdmin->getZip()
            ]
          ]
        ];
        return $api->registerUser($params);
    }


    public static function getUserProfileInfo(User $user) {
        $api = new UniTellerAPI();
        $params = [
          'partnerCode' => Util::getConfigKey('uniteller_partner_code'),
          'userId' => self::getUniTellerId($user),
        ];
        return $api->getUserProfileInfo($params, $user);
    }

    public static function editUserProfile(User $user) {
        $api = new UniTellerAPI();
        if ($user->getCountry() && $user->getCountry()->isUSA() ) {
          $graoupAdmin = $user;
        } else {
          $graoupAdmin = $user->getPrimaryGroupAdmin();
        }
        $params = [
          'partnerCode' => Util::getConfigKey('uniteller_partner_code'),
          'userId' => self::getUniTellerId($user),
          'remitterInfo' => [
            'birthDate' => Util::formatBirthday($user->getBirthday(), 'dmY'),
            'cellPhone' => Util::getPhoneDigitsWithOutCountryIso($user->getMobilePhone()),
            'destinationCountryISOCode' => $user->getCountry() ? $user->getCountry()->getIsoCode() : '',
            'address' => [
              'address1' => $graoupAdmin->getAddress(),
              'city' => $graoupAdmin->getCity(),
              'state' => $graoupAdmin->getState() ? $graoupAdmin->getState()->getAbbr() : '',
              'stateName' => $graoupAdmin->getState() ? $graoupAdmin->getState()->getAbbrOrName() : '',
              'country' => $graoupAdmin->getCountry() ? $graoupAdmin->getCountry()->getName() : '',
              'countryISOCode' => $graoupAdmin->getCountry() ? $graoupAdmin->getCountry()->getIsoCode() : '',
              'zipCode' => $graoupAdmin->getZip()
            ]
          ]
        ];
        return $api->editUserProfile($params, $user);
    }

    public static function getFilteredMethodTypes(Country $country, BaseState $state)
    {
        $service = new UniTellerAPI();

        /** @var ExternalInvoke $ei */
        $params = [
          'partnerCode' => Util::getConfigKey('uniteller_partner_code'),
          'countryISOCode' => $country->getIsoCode(),
          'currencyISOCode' => $country->getCurrency(),
          'stateISOCode' =>  $state->getAbbr3()
        ];

        [$ei, $result] = $service->getPayerWithReceptionMethodsList($params);

        if ($ei->isFailed()) {
          $msg =  'Sorry that the operation failed due to an error (code: 10). We are investigating. Please reach out to our support if it failed constantly.'
                  . ' (EI: ' . $ei->getId() . ')';
          throw PortalException::temp($msg);
        }

        $list = [];
        $bankList = [];
        $cashPickList = [];
        if ($result['payerWithReceptionMethods'] && count($result['payerWithReceptionMethods'])) {
          foreach ($result['payerWithReceptionMethods'] as $payer) {
            $payerItem = [
              'id' => $payer['payer']['id'],
              'label' => $payer['payer']['name'],
              'value' => $payer['payer']['payerSpecificCode'],
              'logo'  =>  isset(self::CASH_PICKUP_LOGO_LIST[$payer['payer']['payerSpecificCode']]) ? 'static/mex/img/uniteller/' . self::CASH_PICKUP_LOGO_LIST[$payer['payer']['payerSpecificCode']] : '',
              'min'   => isset(self::MIN_CASH_PICKUP_LIST[$payer['payer']['payerSpecificCode']]) ? self::MIN_CASH_PICKUP_LIST[$payer['payer']['payerSpecificCode']] : 2500,
              'max' => TransferMexBundle::getMaxUniTellerTransferAmount(),
              'additionalFields' => [],
              'branchList'       => [],
              'beneAccountRegex' => $payer['payer']['beneAccountRegex'],
              'accountRegexMsg' => $payer['payer']['accountRegexMsg'],
            ];
            foreach ($payer['receptionMethods'] as $item) {
              if ($item['isAddtionalFieldRequired'] == 'YES') {
                $params = [
                  'partnerCode' => Util::getConfigKey('uniteller_partner_code'),
                  'payerSpecificCode' => $payerItem['value'],
                  'currencyISOCode' => $country->getCurrency(),
                  'receptionMethodName' =>  $item['name']
                ];
                [$ei, $res] = $service->getPayerAdditionalFields($params);
                $additionalFields = [];
                if (!$ei->isFailed()) {
                  foreach ($res['additionalFieldInfo'] as $additionalField) {
                    $additionalFields[] = [
                      'label' => $additionalField['fieldLabel'],
                      'name'  => $additionalField['fieldName'],
                      'type'  => $additionalField['fieldType'],
                      'options' => $additionalField['fieldOptions'],
                    ];
                  }
                }
                $payerItem['additionalFields'] = $additionalFields;
              }
              if ($item['isPayerBranchRequired'] == 'YES') {
                $params = [
                  'partnerCode' => Util::getConfigKey('uniteller_partner_code'),
                  'payerSpecificCode' => $payerItem['value'],
                  'countryISOCode'  => $country->getIsoCode(),
                  'currencyISOCode' => $country->getCurrency(),
                  'receptionMethodName' =>  $item['name'],
                  'stateISOCode' => $state->getAbbr3()
                ];
                [$ei, $res] = $service->getPayerBranch($params);
                $branchList = [];
                if (!$ei->isFailed()) {
                  foreach ($res['payerBranches'] as $payerBranch) {
                    $branchList[] = [
                      'label' => $payerBranch['branchName'],
                      'value'  => $payerBranch['branchCode'],
                      'time'  => $payerBranch['branchHours'],
                      'phone'  => $payerBranch['phoneNumber1'],
                      'address'  => $payerBranch['address']['address1'] . ' ' . $payerBranch['address']['city'],
                    ];
                  }
                }
                $payerItem['branchList'] = $branchList;
              }
              $payerItem['max'] = $item['txnLimit'];

              if ($item['name'] === 'Cash Pickup') {
                $cashPickList[] = $payerItem;
                Data::set('uniteller_cash_pickup_' . $payerItem['value'] . '_max_limit', $payerItem['max']);
              } else if ($item['name'] === 'Account Credit') {
                $payerItem['min'] = self::getMinAmountByType($payerItem['value'], 'bank', $country->getIsoCode());
                $bankList[] = $payerItem;
                $key = 'uniteller_account_validate_' . $payerItem['value'];
                $errorKey = 'uniteller_account_validate_error_' . $payerItem['value'];
                if ($payerItem['beneAccountRegex'] && !Data::get($key)) {
                  Data::set($key, $payerItem['beneAccountRegex']);
                  Data::set($errorKey, $payerItem['accountRegexMsg']);
                }
              }
            }
          }
        }
        //if (!Util::isLive()) {
        $list['bankUniTeller'] = $bankList;
        //}
        $list['cashUniTeller'] = $cashPickList;
        return $list;
    }

    public static function getBeneficiaryDataArray(User $user, Recipient $recipient = null)
    {
        $recipient = $recipient ?? $user->ensureTransferMexRecipient();

        $payer = Util::meta($recipient, 'payerSpecificCode');
        $list = Util::meta($recipient, 'UniTellerAdditionalFields');
        $payerBranch = Util::meta($recipient, 'payerBranch');
        foreach ($list as $key => $item) {
            $additionalFieldInfo[] = [
                'fieldName'     => $key,
                'fieldValue'    => $item
            ];
        }

        $beneficiarySummary = [
            'firstName' => $recipient->getUser()->getFirstName(),
            'lastName' => $recipient->getUser()->getLastName(),
            'midName'  => Util::meta( $recipient->getUser(), 'midName'),
            'secLastName'  => Util::meta( $recipient->getUser(), 'secLastName'),
            'cellPhone' => Util::getPhoneDigitsWithOutCountryIso($recipient->getUser()->getMobilephone()),
            'receptionMethod' => $recipient->getPayoutType() == 'Bank Transfer' ? 'Account Credit' : 'Cash Pickup',
            'destCurrencyISOCode' => $recipient->getCurrency(),
            'destCountryISOCode' => $recipient->getUser()->getCountry()->getIsoCode(),
            'payerSpecificCode' => $payer,
            'address' => [
                'address1' => $recipient->getUser()->getAddress(),
                'city' => $recipient->getUser()->getCity(),
                'state' => $recipient->getUser()->getState()->getAbbr3(),
                'stateName' => $recipient->getUser()->getState()->getAbbrOrName(),
                'country' => $recipient->getUser()->getCountry()->getName(),
                'countryISOCode' => $recipient->getUser()->getCountry()->getIsoCode(),
                'zipCode' => $recipient->getUser()->getZip()
            ]
        ];

        if ($recipient->getPayoutType() === 'Bank Transfer') {
            $beneficiarySummary['accNumber'] = $recipient->getDecryptedAccountNumber();
        }

        if (!empty($additionalFieldInfo)) {
            $beneficiarySummary['additionalFieldInfo'] = $additionalFieldInfo;
        }

        if ($payerBranch) {
            $beneficiarySummary['payerBranchCode'] = $payerBranch;
        }

        return $beneficiarySummary;
    }

    public static function createBeneficaiary(User $user, Recipient $recipient) {
        $api = new UniTellerAPI();
        $beneficiarySummary = self::getBeneficiaryDataArray($user, $recipient);

        $params = [
            'userId' => UniTellerRemittanceService::getUniTellerId($user),
            'beneficiarySummary' => $beneficiarySummary,
            'locale' => 'EN'
        ];
        $params['partnerCode'] = Util::getConfigKey('uniteller_partner_code');
        return $api->AddBeneficiary($params, $user);
    }

    public static function editBeneficiary(User $user, Recipient $recipient) {
        $api = new UniTellerAPI();
        $beneficiarySummary = self::getBeneficiaryDataArray($user, $recipient);
        $uniTellerBeneficiaryData = Util::meta($recipient, 'uniTellerBeneficiaryData');
        $beneficiarySummary = array_merge($beneficiarySummary, [
            'id' => $uniTellerBeneficiaryData['beneficiarySummary']['id'],
            'nickName' => $uniTellerBeneficiaryData['beneficiarySummary']['nickName'],
            'midName' => $uniTellerBeneficiaryData['beneficiarySummary']['midName'],
            'secLastName' => $uniTellerBeneficiaryData['beneficiarySummary']['secLastName'],
        ]);

        $params = [
            'userId' => UniTellerRemittanceService::getUniTellerId($user),
            'beneficiarySummary' => $beneficiarySummary,
            'locale' => 'EN'
        ];
        $params['partnerCode'] = Util::getConfigKey('uniteller_partner_code');
        return $api->editBeneficiary($params, $user);
  }

    public static function deactivateBeneficiary(User $user, Recipient $recipient){
        $api = new UniTellerAPI();
        $uniTellerBeneficiaryData = Util::meta($recipient, 'uniTellerBeneficiaryData');
        $params = [
          'userId'        => UniTellerRemittanceService::getUniTellerId($user),
          'beneficiaryId' => $uniTellerBeneficiaryData['beneficiarySummary']['id'],
          'locale' => 'EN'
        ];
        $params['partnerCode'] = Util::getConfigKey('uniteller_partner_code');

        return $api->deactivateBeneficiary($params, $user);
    }

    public static function getDailyRate($force = false, $countryIsoCode = 'MX', $currencyIsoCode = 'MXN')
    {
        $args = compact('countryIsoCode', 'currencyIsoCode');
        if ($force) {
            Data::del(Data::key(__METHOD__, $args));
        }
        return Data::callback(__METHOD__, $args, function () use ($countryIsoCode, $currencyIsoCode) {
            $api = new UniTellerAPI();
            /** @var ExternalInvoke $ei */
            $params = [
              'partnerCode' => Util::getConfigKey('uniteller_partner_code'),
              'countryIsoCode' => $countryIsoCode,
              'currencyIsoCode' => $currencyIsoCode
            ];
             /** @var ExternalInvoke $ei  */
            [$ei, $data] = $api->getDailyRate($params);
            if ($ei && $ei->isFailed()) {
                $message = 'Sorry that the operation failed due to ' . $ei->getError(). ' . We are investigating. Please reach out to our support if it failed constantly.';
                throw PortalException::create($message);
            }
            if (!isset($data['exchangeRate'])) {
                $message = 'Sorry that the operation failed due to an error (code: 05). We are investigating. Please reach out to our support if it failed constantly.';
                throw PortalException::create($message);
            }
            Data::set('uniteller_rate_currently_' . $currencyIsoCode, $data['exchangeRate']['exchangeRate']);
            return $data['exchangeRate']['exchangeRate'];//* (1 - self::RATE_MARK_UP_PARTNER - self::RATE_MARK_UP_PLATFORM);
        }, 3600);
    }

    public static function getPayoutOptions(User $recipient = null, $force = false)
    {
        $defaultRateInfo = Currency::find($recipient ? $recipient->getCountry()->getCurrency() : 'MXN');
        $defaultRate = $defaultRateInfo ? $defaultRateInfo->getOneUsd() : 18.511111;

        try {
            $currencyIsoCode = 'MXN';
            if ($recipient) {
              $countryIsoCode = $recipient->getCountry()->getIsoCode();
              $currencyIsoCode = $recipient->getCountry()->getCurrency();
              $dailyRate = $currencyIsoCode == 'USD' ? 1 : self::getDailyRate($force, $countryIsoCode, $currencyIsoCode);
            } else {
              $dailyRate = self::getDailyRate($force);
            }
        } catch (\Exception $e) {
            Log::warn('Failed to query UniTeller rate: ' . $e->getMessage());
            $dailyRate =  Data::get('uniteller_rate_currently_' .$currencyIsoCode) ?? $defaultRate;
            if (!Data::get('uniteller_rate_currently_' . $currencyIsoCode)) {
              throw PortalException::create('Failed to query UniTeller rate: ' . $e->getMessage());
            }
        }
        return [
            'minAmount' => 0, // TransferMexBundle::getUniTellerMinTransferAmount(),
            'maxAmount' => TransferMexBundle::getMaxUniTellerTransferAmount(),
            'dailyRate' => $dailyRate * 1.0,
            'feeCash' => self::FEE_CASH_PICKUP,
            'feeBank' => self::FEE_BANK_TRANSFER,
            'otherCountryFee' => self::OTHER_COUNTRY_FEE
        ];
    }

    public static function getSendingMethodSummaryList(User $user) {
        $api = new UniTellerAPI();
        $params = [
          'partnerCode' =>  Util::getConfigKey('uniteller_partner_code'),
          'userId' => self::getUniTellerId($user),
          'sendingMethodStatus' => 'All'
        ];
        $method = null;
        try {
          [$ei, $res] = $api->getSendingMethodSummaryList($params,$user);
          if ($ei->isFailed()) {
            return null;
          }
          $sendingMethodSummary = $res['sendingMethodSummary'];
          foreach ($sendingMethodSummary as $item) {
            if ($item['sendingMethodName'] == 'Wlp Wallet') {
              $method = $item;
            }
          }
        } catch (PortalException $ex) {
          return null;
        }
        return $method;
    }

    // check fee
    public static function ExternalQuickQuotes(User $user, Recipient $recipient, $amount ) {
        $api = new UniTellerAPI();
        $uniTellerBeneficiaryData = Util::meta($recipient, 'uniTellerBeneficiaryData');
        $beneficaiary = isset($uniTellerBeneficiaryData['beneficiarySummary']) ? $uniTellerBeneficiaryData['beneficiarySummary'] : null;
        if (!$beneficaiary) {
            throw PortalException::temp('Get cost fee error!');
        }
        $originStateIsoCode = '';
        try {
        [$ei, $data] = self::getUserProfileInfo($user);
          if ($ei->isFailed()) {
            $originStateIsoCode = '';
          } else {
            $originStateIsoCode = $data['remitterPersonalInfo']['address']['state'];
          }
        } catch (\Exception $exception) {
          $originStateIsoCode = '';
        }
        $params = [
          'partnerCode' => Util::getConfigKey('uniteller_partner_code'),
          'userId' => self::getUniTellerId($user),
          'locale' => 'en',
          'destCountryISOCode' => $beneficaiary['destCountryISOCode'],
          'destCurrencyISOCode' => $beneficaiary['destCurrencyISOCode'],
          'transactionAmount' => Money::formatAmount($amount, 'USD', ''),
          'payerSpecificCode' => $beneficaiary['payerSpecificCode'],
          'receptionMethodName' => $beneficaiary['receptionMethod'],
          'sendingMethodName' => 'Wlp Wallet',
        ];
        if ($originStateIsoCode) {
          $params['originStateIsoCode'] = $originStateIsoCode;
        }
        /** @var ExternalInvoke $ei */
        [$ei, $res] = $api->externalQuickQuotes($params);
        if ($ei->isFailed()) {
            $message = $ei->getError();
            if (UniTellerAPI::isOopsError($message)) {
                $message = 'Sorry that the operation failed due to an error (code: 04). We are investigating. Please reach out to our support if it failed constantly.';
            }
          throw PortalException::temp($message);
        }
        return $res;
    }

    // preview money
    public static function sendMoneyPreview(Transfer $transfer) {
        $api = new UniTellerAPI();
        $uniTellerBeneficiaryData = Util::meta($transfer->getRecipient()->ensureTransferMexRecipient(), 'uniTellerBeneficiaryData');

        $beneficaiary = $uniTellerBeneficiaryData['beneficiarySummary'];
        if (!$beneficaiary) {
            throw PortalException::temp('The recipient\'s payout options are not configured yet. Please edit the recipient details first.');
        }
        // get sending method
        $sendingMethod = self::getSendingMethodSummaryList($transfer->getSender());
        if (!$sendingMethod) {
          throw PortalException::temp('The recipient\'s payout options are not configured yet. Please edit the recipient details first.');
        }
        $params = [
          'userId' => self::getUniTellerId($transfer->getSender()),
          'beneficiaryId' => $beneficaiary['id'],
          'sendingMethodId' => $sendingMethod['id'], //  $sendingMethodId,
          'sendingMethodName' => $sendingMethod['sendingMethodName'], // $payoutType,
          'transactionAmount' => Money::formatAmount($transfer->getSendAmount(), 'USD', ''),
          // 'paymentAmount' => Money::formatAmount($transfer->getPayoutAmount()),
          'selectedCurrencyIsoCode' => $beneficaiary['destCurrencyISOCode'],
          // 'transactionCurrencyISOCode' => $beneficaiary['destCurrencyISOCode'],
          "extraFields" => [
            [
            "name" => "EXTERNAL_SERVICE_FEE",
            "value" => "0"
            ]
          ]
        ];
        $params['partnerCode'] = Util::getConfigKey('uniteller_partner_code');

        [$ei, $transacton] = $api->sendMoneyPreview($params, $transfer->getSender());
        if ($ei->isFailed()) {
            $transfer->setEi($ei);
            $message = $ei->getError();
            if ($transacton['responseCode'] == '59905008') {
              $message = 'Some additional information is required or added 6 or more months ago for this recipient, Please provide it by editing recipient.';
            }
            // amount limit error
            if ($transacton['responseCode'] == '51104001' || strpos($message, 'the maximum transaction limit.') !== false) {
              $message = 'The amount exceeds the maximum transaction limit, please reduce the transaction amount.';
            }
            throw PortalException::temp($message);
        }
        Util::updateMeta($transfer, [
            'transactionInternalReference' => $transacton['transactionInternalReference']
        ]);

        $transfer->setStatus(Transfer::STATUS_CONFIRMATION)
                 ->persist();
        return $transfer;
    }

    public static function sendMoneyConfirm(Transfer $oldTransfer) {
        $transfer = Transfer::find($oldTransfer->getId());
        if (!in_array($transfer->getStatus(), [
            Transfer::STATUS_CONFIRMATION
        ])) {
            throw PortalException::temp('Invalid transfer status!');
        }

        if ($transfer->getPartnerId()) {
          throw PortalException::temp('The transfer was already sent!');
        }

        $recipient = $transfer->getRecipient();
        $from = $transfer->getSender();
        if (!$recipient || !$from) {
            throw PortalException::temp('Invalid recipient or sender!');
        }

        $api = new UniTellerAPI();
        $transactionInternalReference = Util::meta($transfer, 'transactionInternalReference');
        $params = [
          'partnerCode' => Util::getConfigKey('uniteller_partner_code'),
          'userId' => self::getUniTellerId($transfer->getSender()),
          'transactionInternalReference' => $transactionInternalReference,
        ];
        [$ei, $data] = $api->sendMoneyConfirm($params, $transfer->getSender());

        $transfer->setEi($ei);

        if ($ei->isFailed()) {
            $transfer->setStatus(Transfer::STATUS_ERROR)
            ->setCancelAt(Carbon::now())
            ->setError($ei->getError())
            ->persist();
            throw PortalException::temp($ei->getError());
        }

        $transfer->setPartnerId($data['txNumber']);

        if (!empty($data['creationTimestamp'])) {
            $transfer->setSendAt($data['creationTimestamp'] ? Util::toUTC(Carbon::create(substr($data['creationTimestamp'], 4 ,4), substr($data['creationTimestamp'], 0, 2), substr($data['creationTimestamp'], 2 ,2), substr($data['creationTimestamp'], 8 ,2), substr($data['creationTimestamp'], 10 ,2), substr($data['creationTimestamp'], 12 ,2), Util::tzNewYork())) : null);
        }
        // send slack message after create a cash pickup
        $context = self::getTransferContext($transfer);
        $msg = 'Created Cash Pickup';
        if (Data::has('process_payout_queue_' . $transfer->getId())) {
            $msg .= ' *from the payout queue*';
        }
        SlackService::tada($msg, $context);
        self::updateTransfer($transfer);
        return $transfer;
    }

    public static function getTransferStatus($status) {
        if (in_array($status, [Transfer::UNITELLER_STATUS_HOLD, Transfer::UNITELLER_STATUS_PAYABLE])) {
          return Transfer::STATUS_CREATED;
        } else if (in_array($status, [Transfer::UNITELLER_STATUS_PAID])) {
          return Transfer::STATUS_COMPLETED;
        } else if (in_array($status, [Transfer::UNITELLER_STATUS_CANCELED, Transfer::UNITELLER_STATUS_CANCELPENDING, Transfer::UNITELLER_STATUS_REFUND])) {
          return Transfer::STATUS_CANCELED;
        }
    }
    public static function updateTransferByPayout($data, Transfer $transfer = null, $initiative = false, $isCancel = false)
    {
        $receiveAt = null;
        $transactionNumber = null;
        $status = null;
        $paidAmount = 0;
        if (isset($data['transactionDetail'])) {
          $transactionNumber = $data['transactionDetail']['transactionNumber'];
          // $receiveAt = Carbon::create(substr($data['transactionDetail']['transactionCreateDate'], 4 ,4), substr($data['transactionDetail']['transactionCreateDate'], 0, 2), substr($data['transactionDetail']['transactionCreateDate'], 2 ,2), substr($data['transactionDetail']['transactionCreateDate'], 8 ,2), substr($data['transactionDetail']['transactionCreateDate'], 10 ,2), substr($data['transactionDetail']['transactionCreateDate'], 12 ,2), Util::tzNewYork());
          $status = $data['transactionDetail']['transactionStatus'];
          $paidAmount = $data['transactionDetail']['payingAmount'];
        } else if (isset($data['txNumber'])) {
          $transactionNumber = $data['txNumber'];
          // $receiveAt = isset($data['creationTimestamp']) ? Carbon::create(substr($data['creationTimestamp'], 4 ,4), substr($data['creationTimestamp'], 0, 2), substr($data['creationTimestamp'], 2 ,2), substr($data['creationTimestamp'], 8 ,2), substr($data['creationTimestamp'], 10 ,2), substr($data['creationTimestamp'], 12 ,2), Util::tzNewYork()) : null;
          $status = $data['txStatus'];
          $paidAmount = $data['totalReceivableAmount'];
        } else {
          $transactionNumber = isset($data['transactionNumber']) ? $data['transactionNumber'] : '';
          $status = $data['transactionStatus'];
          $paidAmount = isset($data['payingAmount']) ? $data['payingAmount'] : 0;
        }
        if ($transfer === null) {
            $transfer = Transfer::findByPartnerId($transactionNumber, Transfer::PARTNER_UNITELLER);
            if (!$transfer) {
                return $transfer;
            }
        }
        $oldStatus = $transfer->getStatus();

        if ($status === Transfer::UNITELLER_STATUS_PAYABLE) {
          // sent email to sender, they can share the cash pickup to the recipient
           self::sentEmailToSender($transfer);
        }

        $paidAmount = Money::normalizeAmount($paidAmount, $transfer->getReceiveCurrency());

        if (self::getTransferStatus($status) === Transfer::STATUS_COMPLETED && !$transfer->getReceiveAt() ) {
            $receiveAt = Carbon::now();
        }

        $transfer->setStatus(self::getTransferStatus($status))
            ->setReceiveAmount($paidAmount)
            ->setReceiveAt($receiveAt ? Util::toUTC($receiveAt) : $transfer->getReceiveAt())
            ->setError($data['error'] ?? null)
            ->setSyncAt(new \DateTime());

        if ($transfer->getStatus() === Transfer::STATUS_CANCELED && !$transfer->getCancelAt()) {
            $transfer->setCancelAt(Carbon::now());
        }

        Util::updateMeta($transfer, [
            'UniTellerSendMoney' => $data,
            'UniTellerSyncAt' => Carbon::now()->format('c'),
        ]);

        TransactionService::updateTransactionByTransfer($transfer);

        $newStatus = $transfer->getStatus();
        if ($oldStatus !== Transfer::STATUS_COMPLETED && $newStatus === Transfer::STATUS_COMPLETED) {
             $context = self::getTransferContext($transfer);
             if (in_array($oldStatus, [
                Transfer::STATUS_ERROR,
                Transfer::STATUS_CANCELED
            ])) {
                SlackService::alert('`' . $oldStatus . '` payout becomes completed!', $context);

                RapidService::updateBalanceBy($transfer->getSenderCard(), -$transfer->getTotalAmount(),
                    'rapyd_transfer', $transfer->getId(), 'Transfer in ' . $transfer->getId(), $transfer, true);
                SlackService::info('Deducted the transfer amount from the member.', $context);
            } else {
                $msg = 'Completed Cash Pickup';
                if (Data::has('process_payout_queue_' . $transfer->getId())) {
                    $msg .= ' *from the payout queue*';
                }
                TransferService::updatePromo($transfer);
                SlackService::tada($msg, $context);
            }
        } else if (!$initiative && in_array($oldStatus, [
                Transfer::STATUS_CREATED,
                Transfer::STATUS_PENDING,
                Transfer::STATUS_QUEUED,
                Transfer::STATUS_PROCESSING,
                Transfer::STATUS_COMPLETED,
            ]) && in_array($newStatus, [
                Transfer::STATUS_ERROR,
                Transfer::STATUS_CANCELED
            ]))
        {
            $context = self::getTransferContext($transfer);
            SlackService::alert('`' . $oldStatus . '` payout becomes `' . $newStatus . '`!', $context);

            if (!$transfer->isRefunded()) {
                // $api = null;
                // $fromBase = false;
                // if (Util::meta($transfer, 'payoutToBase')) {
                //     $fromBase = true;
                //     $api = new RapidAPI(); // Refund from base agent since it was moved there
                // }
                $refunded = RapidService::updateBalanceBy(
                    $transfer->getSenderCard(),
                    $transfer->getTotalAmount(),
                    'rapyd_transfer_reverse', $transfer->getId(),
                    $newStatus . ' transfer', $transfer,
                    true
                );
                $context['refunded'] = $refunded;
                $transfer->setRefunded(true);
                // Util::updateMeta($transfer, 'payoutToBase');
                $transfer->setPayoutToBase(null)
                        ->persist();
                $msg = 'Refunded the transfer amount to the member';
                // if ($fromBase) {
                //     $msg .= ' *from the base agent*';
                // }
                SlackService::info($msg, $context);

                if ($refunded) {
                    TransferHealthService::saveRefundCache($transfer);
                }
            }
            // check the fee promo and return
            TransferService::updatePromo($transfer, true);
        }

        return $transfer;
    }

    public static function getTransferContext(Transfer $transfer)
    {
        return [
            'transfer' => $transfer->getId(),
            'employer' => $transfer->getSender()->getPrimaryGroupName(),
            'sender' => $transfer->getSender()->getSignature(),
            'type' => $transfer->getPayoutType(),
            'method' => $transfer->getPayoutMethodType(),
            'amount' => Money::formatWhen($transfer->getSendAmount(), $transfer->getSendCurrency(), true),
        ];
    }

    public static function cancelTransactions(Transfer $transfer) {
        if (!in_array($transfer->getStatus(), [
            Transfer::STATUS_PENDING,
            Transfer::STATUS_QUEUED,
            Transfer::STATUS_CREATED,
            Transfer::STATUS_CONFIRMATION,
        ])) {
            throw new FailedException('This transfer is not cancelable!');
        }

        if ($transfer->getPartner() === Transfer::PARTNER_UNITELLER && $transfer->getPartnerId()) {
          try {
              self::updateTransfer($transfer);
              $oldStatus = $transfer->getStatus();

              // $api = self::createApiFromTransfer($transfer);
              $api = new UniTellerAPI();
              $params = [
                'userId' => self::getUniTellerId($transfer->getSender()),
                'transactionNumber' => $transfer->getPartnerId(),
                'reasonforCancel' => 'User voluntarily cancels',
                'comments' =>  $transfer->getSender()->getFullName() . ' do the cancel.'
              ];
              $params['partnerCode'] = Util::getConfigKey('uniteller_partner_code');

              /** @var ExternalInvoke $ei */
              [$ei, $data] = $api->cancelTransaction($params, $transfer->getSender());

              if ($ei->isFailed()) {
                  $error = $ei->getError();
                  if (strpos($error, 'Transaction cancellation is in process') !== false) {
                      $transfer->setCancelAt($transfer->getCancelAt() ?? Carbon::now())
                          ->persist();
                  }

                  $msg = 'Sorry that the operation failed due to an error (code: 07). We are investigating. Please reach out to our support if it failed constantly.';
                  throw PortalException::temp($msg);
              }

              $transfer->setCancelAt($transfer->getCancelAt() ?? Carbon::now())
                      ->persist();

              self::updateTransferByPayout($data, $transfer, true, true);

              if (in_array($oldStatus, [
                    Transfer::STATUS_CREATED,
                    Transfer::STATUS_PENDING,
                    Transfer::STATUS_QUEUED,
                    Transfer::STATUS_PROCESSING,
                    Transfer::STATUS_COMPLETED,
                    Transfer::STATUS_CONFIRMATION,
              ]) && $transfer->getStatus() === Transfer::STATUS_CANCELED && !$transfer->isRefunded()) {
                  RapidService::updateBalanceBy($transfer->getSenderCard(), $transfer->getTotalAmount(),
                      'rapyd_transfer_reverse', $transfer->getId(), 'Cancelled transfer', $transfer, true);
                  $transfer->setRefunded(true);
                  if (Util::isStaging()) {
                    Log::debug('Update the refunded status');
                    Log::debug($transfer->isRefunded());
                  }
                  $context = self::getTransferContext($transfer);
                  $context['canceled_by'] = Util::field(Util::user(), 'signature');
                  SlackService::alert('The transfer was cancelled.', $context);
              }
          } catch (\Exception $exception) {
              $msg =  'Sorry that the operation failed due to an error (code: 07). We are investigating. Please reach out to our support if it failed constantly.';
              throw PortalException::temp($msg);
          }
      } else {
          $oldStatus = $transfer->getStatus();
          if ($transfer->isDeducted() && !$transfer->isRefunded()) {
              RapidService::updateBalanceBy($transfer->getSenderCard(), $transfer->getTotalAmount(),
                  'rapyd_transfer_reverse', $transfer->getId(), 'Cancelled transfer', $transfer, true);
              $transfer->setRefunded(true);

              $context = self::getTransferContext($transfer);
              $context['canceled_by'] = Util::field(Util::user(), 'signature');
              SlackService::alert('The ' . strtolower($oldStatus) . ' transfer was cancelled and refunded to the member.', $context);
          }

          $transfer->setStatus(Transfer::STATUS_CANCELED)
              ->persist();
          TransactionService::updateTransactionByTransfer($transfer);
      }

        return $transfer;
    }

    public static function getTransactionSummaryList(User $user, $saveEi = true) {
        $api = new UniTellerAPI();
        $params = [
          'partnerCode' => Util::getConfigKey('uniteller_partner_code'),
          'userId' => self::getUniTellerId($user),
        ];
        return $api->getTransactionSummaryList($params, $user, $saveEi);
    }

    public static function getTransactionDetail(User $user,  $transactionNumber, $saveEi = true) {
      $api = new UniTellerAPI();
      $params = [
        'partnerCode' => Util::getConfigKey('uniteller_partner_code'),
        'userId' => self::getUniTellerId($user),
        'transactionNumber' => $transactionNumber
      ];
      return $api->getTransactionDetail($params, $user, $saveEi);
    }

    public static function getAllTransactions( $status = null, $page_number = null, $page_size = null, $start_date = null, $end_date = null) {
      $api = new UniTellerAPI();
      return $api->getAllTransactions( $status, $page_number, $page_size, $start_date, $end_date );
    }

    public static function getStateDisclaimer($stateIsoCode) {
      $api = new UniTellerAPI();
      $params = [
        'partnerCode' => Util::getConfigKey('uniteller_partner_code'),
        'stateIsoCode' => $stateIsoCode
      ];
      return $api->getStateDisclaimer($params);
    }

    public static function getPayBranchList($payer, $type) {
        $api = new UniTellerAPI();
        // $graoupAdmin = $user->getPrimaryGroupAdmin();
        $params = [
          'partnerCode' => Util::getConfigKey('uniteller_partner_code'),
          'payerSpecificCode' => $payer['payerSpecificCode'],
          'countryISOCode' => $payer['countryISOCode'],
          'currencyISOCode' => $payer['currencyISOCode'],
          'receptionMethodName' => $type,
          'stateISOCode' => $payer['stateISOCode']
        ];
        return $api->getPayerBrancahList($params);
    }

    public static function getTransactionReceipt(User $user,  $transactionNumber) {
        $api = new UniTellerAPI();
        $params = [
          'partnerCode'       => Util::getConfigKey('uniteller_partner_code'),
          'userId'            => self::getUniTellerId($user),
          'transactionNumber' => $transactionNumber
        ];
        try {
          [$ei, $data] = $api->getTransactionReceipt($params, $user);
          if ($ei->isFailed()) {
             return null;
          }
        } catch(\Exception $exception) {
          return null;
        }
        if (!Util::isLive()) {
            Log::debug('Receipt: ', $data);
        }
        return $data;
    }

    public static function importFromCsvFile($path, $isCronImport = false, $importLocationsRecord = null)
    {
        $em = Util::em();

        $countries = $em->getRepository(Country::class)->getAllKeyedBy();

        $excel = Excel::openExcel($path);
        $sheet = $excel->getActiveSheet();
        $rows = $sheet->toArray();
        $errors = [];
        $candidates = [];
        $errorMessage = '';
        if (!$isCronImport) {
            return 'cronJob';
        }

        // Log::debug('countries', $countries);
        $required = [
            0 => 'Payer',
            1 => 'Fpb Code',
            2 => 'Name',
            3 => 'Phone',
            4 => 'Address',
            8 => 'State',
            9 => "City",
            10 => "Town",
            11 => "Postal Code",
            12 => "Country",
            13 => "Weekday Open",
            14 => "Weekday Close",
            15 => "Sat Open",
            16 => "Sat Close",
            17 => "Sun Open",
            18 => "Sun Close"
        ];

        foreach ($rows as $i => $row) {
            if ($i <= 0) {
                continue;
            }

            $errorMessage = '';
            if (!isset($row[18])) {
                $errorMessage = 'Row ' . ($i + 1) . ': Incorrect column count!';
                $errors[] = $errorMessage;
                goto has_error;
            }

            foreach ($required as $index => $name) {
                $row[$index] = trim($row[$index]);
                if ($row[$index] === null || $row[$index] === '') {
                    $errorMessage = 'Row ' . ($i + 1) . ': Required field "' . $name . '" is empty!';
                    $errors[] = $errorMessage;
                    goto has_error;
                }
            }
            // Log::debug(ucwords(strtolower($row[10])));
            if (!isset($countries[ucwords(strtolower($row[12]))])) {
                $errorMessage = 'Row ' . ($i + 1) . ': Unknown Country!';
                $errors[] = $errorMessage;
                goto has_error;
            }

            /** @var Country $country */
            $country = $countries[ucwords(strtolower($row[12]))];
            if (!in_array($country->getIso3Code(), ['MEX'])) {
                $errorMessage = 'Row ' . ($i + 1) . ': Only country MEX is allowed!';
                $errors[] = $errorMessage;
                goto has_error;
            }

            $row['country'] = $country;

            /** @var State $state */
            $stateName = $row[8];
            if ( $stateName == 'BAJA CALIFORNIA NORTE') {
              $stateName = 'BAJA CALIFORNIA';
            }
            if (Util::startsWith($stateName, 'MEXICO ')) {
              $stateName = str_replace('MEXICO ', '', $stateName,);
            }
            $state = $country->findStateByName($stateName);
            if (!$state) {
                $errorMessage = 'Row ' . ($i + 1) . ': Unknown State!(' . $row[8] . ')';
                $errors[] = $errorMessage;
                goto has_error;
            }
            $row['state'] = $state;

            $candidates[] = $row;

            has_error:
            if ($errorMessage) {
                $errorRecord = new ImportLocationErrorRecord();
                $errorRecord->setReason($errorMessage)
                    ->setImportLocationRecord($importLocationsRecord)
                    ->persist();
            }
        }


        $result = $candidates;

        $importLocationsRecord->setFailedCount(count($errors))
                              ->persist();

        return $result;
    }

    public static function updateFromCsvFile($path, $isCronImport = false, $importLocationsRecord = null)
    {
        $em = Util::em();

        $countries = $em->getRepository(Country::class)->getAllKeyedBy();

        $excel = Excel::openExcel($path);
        $sheet = $excel->getActiveSheet();
        $rows = $sheet->toArray();
        $errors = [];
        $candidates = [];
        $errorMessage = '';
        if (!$isCronImport) {
            return 'cronJob';
        }

        // Log::debug('countries', $countries);
        $required = [
            0 => 'Fpc Code',
            1 => 'Fpc Name',
            2 => 'Fpb Name',
            3 => 'Name',
            4 => 'Phone',
            5 => "Address",
            6 => "Fpb State",
            7 => "City",
            8 => "Town",
            9 => "Postal Code",
            10 => "Country"
        ];

        foreach ($rows as $i => $row) {
            if ($i <= 0) {
                continue;
            }

            $errorMessage = '';
            if (!isset($row[11])) {
                $errorMessage = 'Row ' . ($i + 1) . ': Incorrect column count!';
                $errors[] = $errorMessage;
                goto has_error;
            }

            foreach ($required as $index => $name) {
                $row[$index] = trim($row[$index]);
                if ($row[$index] === null || $row[$index] === '') {
                    $errorMessage = 'Row ' . ($i + 1) . ': Required field "' . $name . '" is empty!';
                    $errors[] = $errorMessage;
                    goto has_error;
                }
            }
            // Log::debug(ucwords(strtolower($row[10])));
            if (!isset($countries[ucwords(strtolower($row[10]))])) {
                $errorMessage = 'Row ' . ($i + 1) . ': Unknown Country!';
                $errors[] = $errorMessage;
                goto has_error;
            }

            /** @var Country $country */
            $country = $countries[ucwords(strtolower($row[10]))];
            if (!in_array($country->getIso3Code(), ['MEX'])) {
                $errorMessage = 'Row ' . ($i + 1) . ': Only country MEX is allowed!';
                $errors[] = $errorMessage;
                goto has_error;
            }

            $row['country'] = $country;

            /** @var State $state */
            $stateName = $row[6];
            if ( $stateName == 'BAJA CALIFORNIA NORTE') {
              $stateName = 'BAJA CALIFORNIA';
            }
            if (Util::startsWith($stateName, 'MEXICO ')) {
              $stateName = str_replace('MEXICO ', '', $stateName,);
            }
            $state = $country->findStateByName($stateName);
            if (!$state) {
                $errorMessage = 'Row ' . ($i + 1) . ': Unknown State!(' . $row[6] . ')';
                $errors[] = $errorMessage;
                goto has_error;
            }
            $row['state'] = $state;

            $candidates[] = $row;

            has_error:
            if ($errorMessage) {
                // $errorRecord = new ImportLocationErrorRecord();
                // $errorRecord->setReason($errorMessage)
                //     ->setImportLocationRecord($importLocationsRecord)
                //     ->persist();
            }
        }


        $result = $candidates;

        $importLocationsRecord->setFailedCount(count($errors))
                              ->persist();

        return $result;
    }

    public static function getLastImport($cardProgram, $type = 'import') {
        $last = Util::em()->getRepository(ImportLocationsRecord::class)
            ->createQueryBuilder('im')
            ->andWhere(Util::expr()->eq('im.cardProgram', $cardProgram->getId()))
            ->andWhere(Util::expr()->eq('im.type', ':type'))
            ->setParameter('type', $type)
            ->orderBy('im.createdAt', 'desc')
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
        $res = null;
        if ($last) {
            $res = [
                'id'     => $last->getId(),
                'createdAt' => Util::formatDateTime($last->getCreatedAt(), Util::DATE_TIME_FORMAT, 'EST'),
                'success' => $last->getSuccessCount() ? $last->getSuccessCount() : 0,
                'fail'    => $last->getFailedCount() ? $last->getFailedCount() : 0,
                'status'  => $last->getStatus() === ImportLocationsRecord::IMPORT_COMPLETE ? 'Completed' : 'In Progress'
            ];
        }
        return $res;
    }

    public static function getErrorList (ImportLocationsRecord $importLocationsRecord) {
        $res = Util::em()->getRepository(ImportLocationErrorRecord::class)
            ->createQueryBuilder('lr')
            ->join('lr.importLocationRecord', 'lp')
            ->andWhere(Util::expr()->eq('lp.id', ':batchId'))
            ->setParameter('batchId', $importLocationsRecord->getId())
            ->distinct()
            ->getQuery()
            ->getResult();
        $list = [];
        foreach ($res as $item) {
            $list[] = [
                'reason' => $item->getReason()
            ];
        }
        return $list;
    }
    public static function simulateCompletingPayout(Transfer $transfer, $amount) {
        $transfer->setStatus(Transfer::STATUS_COMPLETED)->persist();
    }

    public static function getCancelTransactions($status) {
        $api = new UniTellerAPI();
        // $graoupAdmin = $user->getPrimaryGroupAdmin();
        $params = [
          'partnerCode' => Util::getConfigKey('uniteller_partner_code'),
          'action'    => 'FETCH',
          'txStatus' => $status
        ];
        return $api->syncTxStatus($params);
    }

    public static function updateTransactions($status, Transfer $transfer) {
      $api = new UniTellerAPI();
      // $graoupAdmin = $user->getPrimaryGroupAdmin();
      $params = [
        'partnerCode' => Util::getConfigKey('uniteller_partner_code'),
        'action'    => "UPDATE",
        'txStatus' => $status,
        'transactionNumber' => $transfer->getPartnerId()
      ];
      return $api->syncTxStatus($params);
  }
  public static function sentEmailToSender(Transfer $transfer) {
      if (Data::get('cash_pickup_code_email_' . $transfer->getId())) {
        return;
      }
      $sender = $transfer->getSender();
      $to = [$sender->getEmail()];
      $currentLanguage = Data::get('current-language-' . $transfer->getSender()->getId()) ?: 'es';
      // Dtefault is es
      if (!$currentLanguage || $currentLanguage == 'es') {
        $subject = 'Código de pago en efectivo';
        $body = '<p>El código de recolección de la transferencia (ID: ' .  $transfer->getId() .') que has enviado a ' . $transfer->getRecipient()->getFullName() .' está disponible, puedes compartirlo con el destinatario.</p>';
      } else {
        $subject = 'Cash Pickup Code';
        $body = '<p>The Cash Pickup Code of the transfer(ID: ' . $transfer->getId() . ') you sent to ' . $transfer->getRecipient()->getFullName() . ' is available, you can share it to the recipient. </p>' ;
      }
      Email::sendWithTemplate($to, Email::TEMPLATE_SIMPLE_LAYOUT, [
        '_lang' => $currentLanguage,
        'subject' => $subject,
        'body' => $body,
    ], $sender);
    Data::set('cash_pickup_code_email_' . $transfer->getId(), true);
  }

  public static function checkAccountNumber($type, $accountNumber)
  {
    $key = 'uniteller_account_validate_' . $type;
    $message = null;
    $accountRegex = Data::get($key);
    $errorKey = 'uniteller_account_validate_error_' . $type;
    if (Util::isStaging()) {
      Log::debug('The rule is ' . $accountRegex);
      Log::debug($type);
      Log::debug(Data::get($errorKey));
    }
    if ($accountRegex) {
      $message = preg_match('/' . $accountRegex . '/', $accountNumber) === 1 ? null : Data::get($errorKey);
    } else {
      if (!$accountNumber || strlen($accountNumber) !== 18) {
          $message = 'The CLABE account must be 18 digits.';
      }
      $validate = ClabeValidator::validate($accountNumber);
      if (empty($validate['ok'])) {
          $message = 'CLABE error: ' . ($validate['message'] ?? 'unknown message');
      }
    }

    return $message;
  }
}
