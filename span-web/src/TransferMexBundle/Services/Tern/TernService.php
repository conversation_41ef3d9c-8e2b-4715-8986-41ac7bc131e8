<?php

namespace TransferMexBundle\Services\Tern;

use App\Entity\AuthRule;
use Carbon\Carbon;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\Email;
use CoreBundle\Entity\ExternalInvoke;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserGroup;
use CoreBundle\Services\SSLEncryptionService;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\JwtUtil;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Security;
use CoreBundle\Utils\Util;
use FaasBundle\Services\IProcessor;
use FaasBundle\Services\ProcessorTrait;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use PortalBundle\Exception\PortalException;
use SalexUserBundle\Entity\User;
use TransferMexBundle\Services\SlackService;
use FaasBundle\Services\IProcessorService;

/**
 * @property-read string agentNumber
 */
class TernService // implements IProcessorService
{
   public static function syncEmployerAccountNumbers(UserGroup $group)
    {
        $api = TernAPI::getForUserGroup($group);
        // $api->affirmBusinessId();
        // $accountId = $api->affirmBusinessAccountId();
        // $data = ExternalInvoke::host($api->retrieveBusinessAccount($accountId));

        $employer = $group->getPrimaryAdmin();
        Util::updateMeta($employer, [
            'Institution Name' => TernAPI::BANK_NAME,
            // 'Account Number' => SSLEncryptionService::encrypt($data['internal_deposit_account']),
            // 'Routing Number' => SSLEncryptionService::encrypt($data['routing_number']),
        ]);

        return []; //$data;
    }

     public static function ensureUserGroupBusiness(UserGroup $ug)
    {
        $businessId = $ug->getTernBusinessId();
        $businessAccountId = $ug->getTernBusinessAccountId();
        if ($businessId && $businessAccountId) {
            return $businessAccountId;
        }

        $_ids = Util::em()->getRepository(UserGroup::class)
            ->createQueryBuilder('ug')
            ->where('ug.ternBusinessAccountId is not null')
            ->select('ug.ternBusinessId')
            ->getQuery()
            ->getArrayResult();
        $ids = array_pluck($_ids, 'ternBusinessId');

        $ugName = strtolower($ug->getName());
        $api = new TernAPI();
        $exists = ExternalInvoke::host($api->getOrganizations());
        foreach ($exists as $e) {
            $name = strtolower($e['legal_business_name']);
            if ($name === $ugName && !in_array($e['id'], $ids)) {
                $ug->setTernBusinessId($e['id'])
                    ->persist();
                break;
            }
        }

        $api = TernAPI::getForUserGroup($ug);
        $api->ensureBusinessId($ug, true);
        return $api->ensureBusinessAccountId($ug, true);
    }
}
