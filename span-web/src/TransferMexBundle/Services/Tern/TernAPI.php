<?php

namespace TransferMexBundle\Services\Tern;

use App\Entity\AuthRule;
use Carbon\Carbon;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\Email;
use CoreBundle\Entity\ExternalInvoke;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserGroup;
use CoreBundle\Services\SSLEncryptionService;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\JwtUtil;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Security;
use CoreBundle\Utils\Util;
use FaasBundle\Services\IProcessor;
use FaasBundle\Services\ProcessorTrait;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use PortalBundle\Exception\PortalException;
use SalexUserBundle\Entity\User;
use TransferMexBundle\Services\SlackService;

/**
 * @property-read string agentNumber
 */
final class TernAPI // implements IProcessor
{
    use ProcessorTrait {
        getPlatform as traitGetPlatform;
    }
   
    public const BANK_NAME = 'TransPecos Bank, N.A';
    public $live = true;
    public $platformKey = 'mex';
    public $businessId;
    public 
    /**
     *
     * @param null|false $live
     */
    public function __construct($live = null)
    {
        if ($live === null) {
            $live = Util::isLive();
        }
        $this->live = $live;
    }

    public static function hasUserGroupAccount(UserGroup $ug): mixed
    {
        return $ug->getTernBusinessAccountId();
    }

    public static function getForUserGroup(UserGroup $ug): static
    {
        $api = new self();
        $api->entity = $ug;
        return $api;
    }

    public function getClientToken($update = false, $type = 'program-client')
    {
        $key = 'tern_token_' . ($this->live ? 'live' : 'test');
        if (!$update) {
            $token = Data::get($key);
            if ($token) {
                return $token;
            }
        }
        $url = $this->getParameter('tern_url');
        $client = new Client();
        try {
            $clientId = $this->getParameter('tern_client_id');
            $clientSecret = $this->getParameter('tern_client_secret');
            $programId = $this->getParameter('tern_program_id');
            Log::debug($url);
            Log::debug($url . '/auth/' . $type . '/' . $programId. '/token');
            $response = $client->post($url . '/auth/' . $type . '/' . $programId. '/token', [
                    'headers' => [
                        'Content-Type'        => 'application/json',
                    ],
                    'json' => [
                        'grant_type'    => 'client_credentials',
                        'client_id'     => $clientId,
                        'client_secret' => $clientSecret
                        
                    ]
                ]);
        } catch (RequestException $exception) {
            $response = $exception->getResponse();
            if (!$response) {
                throw PortalException::create('Failed to refresh the tern token.');
            }

            $content = $response->getBody()->getContents() ?: '{}';
            $content = Util::s2j($content) ?? [];
            $message = $content['error_description'] ?? $content['errorMessage'] ?? $exception->getMessage();
            throw PortalException::create('Error occurred when trying to refresh the tern  token.');
        }

        $content = $response->getBody()->getContents();
        $content = Util::s2j($content) ?? [];

        if (Util::isDev()) {
            Log::debug('tern token response', $content);
        }

        if (empty($content['access_token'])) {
            Log::error('Empty tern token', $content);
            throw PortalException::create('Empty tern token!');
        }

        Data::set($key, $content['access_token'], false, ($content['expires_in'] ?? 60) - 5);
        return $content['access_token'];
    }


    protected function request($method, $endpoint, $params = [], $endpointSuffix = '', $saveEi = true)
    {
        $method = strtoupper($method);
        $post = in_array($method, ['POST', 'PUT', 'PATCH']);
        $url = $this->getParameter('tern_url');

        $context = $params;
        $context['__url'] = $endpoint . $endpointSuffix;
        
        if (!Util::isLive()) {
            Log::debug('Tern API URL: ' . ($url ?: 'NULL'));
            Log::debug('Tern API Endpoint: ' . $endpoint);
            Log::debug('Tern API Full URL: ' . ($url ?: 'NULL') . $endpoint);
        }
        $token = $this->getClientToken(false);
        $ei = ExternalInvoke::create('tern_' . $method . '_' . $endpoint, $context, null, false);

        $ei->setRequestKey(Util::guid());
        if ($saveEi) {
            $ei->persist();
        }

        if ($endpointSuffix) {
          $endpoint .= $endpointSuffix;
        }

        $client = new Client();
        try {

            $headers = [
                'Content-Type' => 'application/json',
                'Accept-Language' => 'en-us',
                'Authorization' => 'Bearer ' . $token,
            ];
            if (!Util::isLive()) {
              Log::debug('Request header: ', $headers);
            }
            $options = [
                'headers' => $headers,
            ];
            if ($params) {
                if ($post) {
                    $options['json'] = $params;
                } else {
                    $options['query'] = $params;
                }
            }

            $response = $client->request($method, $url . $endpoint, $options);
        } catch (RequestException $exception) {
            $response = $exception->getResponse();
            if (!$response) {
                $msg = 'Empty response when call Tern API: ' . $exception->getMessage();
                $ei->fail(null, $msg)
                    ->persist();
                Log::exception($msg, $exception);
                $this->cacheErrorToPool($ei);
                return [$ei, null];
            }

            $rawContent = $response->getBody()->getContents();
            $content = Util::s2j($rawContent ?: '{}') ?? [];
            $status = $content['status'] ?? null;
            if (isset($content['isSuccess']) && !$content['isSuccess']) {
              $msg = $content['errorMessage'];
            } else {
              if ($status > 500) {
                $this->cacheErrorToPool($ei);
              }
              $msg = $exception->getMessage();
            }
            $msg = 'Error occurred when call the Tern API: ' . $msg;
            Log::debug($msg);
            $ei->fail($rawContent, $msg)
                ->persist();
            return [$ei, $content];
        }

        $rawContent = $response->getBody()->getContents();
        if (!Util::isLive()) {
          Log::debug($rawContent);
        }
        $content = Util::s2j($rawContent) ?? [];
        $ei->succeed($content);

        if ($saveEi) {
            $ei->persist();
        }
        return [$ei, $content];
    }

    public function getParameter(string $key, bool $encrypted = true)
    {
        if (Util::isLocal()) {
            return Util::getConfigKey($this->live ? $key : ($key . '_test'));
        }
        return Util::getKmsParameter($this->live ? $key : ($key . '_test'));
    }

    private function cacheErrorToPool(ExternalInvoke $ei)
    {
        $error = $ei->getError();
        if (!$error) {
            return;
        }
        $key = 'tern_error_pool';
        $pool = Data::getArray($key);
        $pool[] = $ei->getId();
        Data::setArray($key, $pool);
    }

    //region ================== Program =================
    public function getPlatform(): ?Platform
    {
        $p = $this->traitGetPlatform();
        if ($p) {
            $this->platform = $p;
            return $p;
        }
        if (!$this->platform && ($this->platformKey === 'mex' || $this->platformKey === 'mex_wire' )) {
            $this->platform = Platform::transferMex();
        }
        return $this->platform;
    }

    public function getProgramId()
    {
        return $this->getParameter('tern_program_id', false); // tern_mex_program_id
    }
    //endregion

    //region ================== Organizations =================

     public function getBaseBusinessId()
    {
        return $this->getPlatform()->getTernBusinessId();
    }

    public function getBaseBusinessAccountId()
    {
        return $this->getPlatform()->getTernBusinessAccountId();
    }


    private function getBusinessId(UserGroup $group = null, $force = true)
    {
        $group = $group ?? $this->getUserGroup();
        if (!$group) {
            return $this->businessId;
        }
        $id = $group->getTernBusinessId();
        if (!$id && $force) {
            throw PortalException::create('Please configure the prefunding parameters of employer ' . $group->getName());
        }
        return (int)($id ?? $this->getBaseBusinessId());
    }

    private function getBusinessAccountId(UserGroup $group = null, $force = true)
    {
        $group = $group ?? $this->getUserGroup();
        if (!$group) {
            return $this->businessAccountId;
        }
        $id = $group->getTernBusinessAccountId();
        if (!$id && $force) {
            throw PortalException::create('Please configure all the prefunding parameters of employer ' . $group->getName());
        }
        return (int)($id ?? $this->getBaseBusinessAccountId());
    }

    public function ensureBusinessId(UserGroup $group = null, $individual = false)
    {
        $group = $group ?? $this->getUserGroup();
        $bid = $this->getBusinessId($group, false);
        if ($individual && $bid && $bid === $this->getBaseBusinessId()) {
            $bid = null;
        }
        if (!$bid) {
            if (!$group) {
                throw PortalException::create('Invalid user group when creating Tern business ID.');
            }
            $result = ExternalInvoke::host($this->createOrganization($group));
            $bid = $result['business_id'] ?? null;
            if ($bid) {
                $group->setTernBusinessId($bid)
                    ->persist();
            }
        }
        if (!$bid) {
            throw PortalException::create('Failed to ensure the Tern business ID.', [
                'group' => $group?->getId(),
            ]);
        }
        return $bid;
    }


    public function getOrganizations() {
        $programId = $this->getProgramId();
        return $this->request('get', '/identity/programs/' . $programId . '/organizations');
    }

    //  public function createOrganization() {
    //     $programId = $this->getParameter('tern_program_id');
    //     return $this->request('get', '/identity/programs/' . $programId . '/organizations');
    // }

    public function createOrganization(UserGroup $group)
    {
        $admin = $group->getPrimaryAdmin();
        $programId = $this->getProgramId();
        return $this->request('POST', '/identity/programs/' . $programId . '/organizations', [
            'name' => $group->getName(),
            'address_1' => Util::maxLength($admin?->getAddress(), 50),
            'address_2' => Util::maxLength($admin?->getAddressline(), 50),
            'locality' => Util::maxLength($admin?->getCity(), 50),
            'state_or_province' => Util::field($admin?->getState(), 'abbr'),
            'postal_code' => $admin?->getZip(),
            'country' => Util::field($admin?->getCountry(), 'iso3Code'),
            'dba' => Util::formatBirthday($admin?->getBirthday()),
        ], saveEi: true);
    }
    //endregion

}
