<?php

namespace TransferMexBundle\Services\Tern;

use App\Entity\AuthRule;
use Carbon\Carbon;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\Email;
use CoreBundle\Entity\ExternalInvoke;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserGroup;
use CoreBundle\Services\SSLEncryptionService;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\JwtUtil;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Security;
use CoreBundle\Utils\Util;
use FaasBundle\Services\IProcessor;
use FaasBundle\Services\ProcessorTrait;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use PortalBundle\Exception\PortalException;
use SalexUserBundle\Entity\User;
use TransferMexBundle\Services\SlackService;
use TransferMexBundle\Services\Tern\TernService;

/**
 * @property-read string agentNumber
 */
final class TernAPI implements IProcessor
{
    use ProcessorTrait {
        getPlatform as traitGetPlatform;
    }
   
    public const BANK_NAME = 'TransPecos Bank, N.A';
    public $live = true;
    private readonly string $platformKey;
    public int $businessId = 0;
    public int $businessAccountId = 0;
    /**
     *
     * @param null|false $live
     */
    public function __construct($live = null)
    {
        if ($live === null) {
            $live = Util::isLive();
        }
        $this->live = $live;
        $this->platformKey = 'mex';
    }

    public static function hasUserGroupAccount(UserGroup $ug): mixed
    {
        return $ug->getTernBusinessAccountId();
    }

    public static function getForUserCardAccountNumber(UserCard $uc, $an = null): static
    {
        $api = self::getForUserCard($uc);
        $api->entity = $uc;
        return $api;
    }

    public static function isAccountNumberValid($an): bool
    {
        // Implement Tern-specific account number validation
        return $an && is_string($an) && strlen($an) > 0;
    }

    public static function isCardStatusOnHoldable($status)
    {
        // Define which statuses can be put on hold for Tern
        return !$status || in_array($status, [
            'ACTIVE',
            'INACTIVE',
        ]);
    }

    public static function getProcessorService(): string
    {
        return TernService::class;
    }


    private static function getSupportedPlatformKey(Platform $platform)
    {
        if ($platform->isTransferMex()) {
            return 'mex';
        }
        return null;
    }

    public static function getForUserGroup(UserGroup $ug): static
    {
        $api = new self();
        $api->entity = $ug;
        return $api;
    }

      public static function getForPlatform(Platform $platform = null): static
    {
        $platform = $platform ?? Util::platform();
        $api = new self(self::getSupportedPlatformKey($platform));
        $api->platform = $platform;
        return $api;
    }

    public static function getForAgent($agentNumber): static
    {
        if ( ! str_starts_with($agentNumber, 'tern_')) {
            throw new PortalException('Invalid tern agent number!');
        }
        $businessAccountId = str_replace_first('term_', '', $agentNumber);
        /** @var Platform|UserGroup|null $entity */
        $entity = self::findEntityByBusinessAccountId($businessAccountId);
        if ( ! $entity) {
            throw new PortalException('Unknown Tern agent number!');
        }
        $key = 'mex';
        if ($entity instanceof Platform) {
            $key = self::getSupportedPlatformKey($entity);
        }
        $api = new self();
        $api->entity = $entity;
        return $api;
    }

    public static function getForUserCard(UserCard $uc): static
    {
        $api = new self();
        $api->entity = $uc;
        return $api;
    }

    /**
     * @param $accountId
     *
     * @return Platform|UserGroup|null
     */
    public static function findEntityByBusinessAccountId($accountId)
    {
        $where = [
            'ternBusinessAccountId' => $accountId,
        ];
        $rs = Util::em()->getRepository(Platform::class)->findBy($where, null, 1);
        if ($rs) {
            return $rs[0];
        }
        $rs = Util::em()->getRepository(UserGroup::class)->findBy($where, null, 1);
        if ($rs) {
            return $rs[0];
        }
        return null;
    }
    
    public function getClientToken($update = false, $type = 'program-client')
    {
        $key = 'tern_token_' . ($this->live ? 'live' : 'test');
        if (!$update) {
            $token = Data::get($key);
            if ($token) {
                return $token;
            }
        }
        $url = $this->getParameter('tern_url');
        $client = new Client();
        try {
            $clientId = $this->getParameter('tern_client_id');
            $clientSecret = $this->getParameter('tern_client_secret');
            $programId = $this->getParameter('tern_program_id');
            Log::debug($url);
            Log::debug($url . '/auth/' . $type . '/' . $programId. '/token');
            $response = $client->post($url . '/auth/' . $type . '/' . $programId. '/token', [
                    'headers' => [
                        'Content-Type'        => 'application/json',
                    ],
                    'json' => [
                        'grant_type'    => 'client_credentials',
                        'client_id'     => $clientId,
                        'client_secret' => $clientSecret
                        
                    ]
                ]);
        } catch (RequestException $exception) {
            $response = $exception->getResponse();
            if (!$response) {
                throw PortalException::create('Failed to refresh the tern token.');
            }

            $content = $response->getBody()->getContents() ?: '{}';
            $content = Util::s2j($content) ?? [];
            $message = $content['error_description'] ?? $content['errorMessage'] ?? $exception->getMessage();
            throw PortalException::create('Error occurred when trying to refresh the tern  token.');
        }

        $content = $response->getBody()->getContents();
        $content = Util::s2j($content) ?? [];

        if (Util::isDev()) {
            Log::debug('tern token response', $content);
        }

        if (empty($content['access_token'])) {
            Log::error('Empty tern token', $content);
            throw PortalException::create('Empty tern token!');
        }

        Data::set($key, $content['access_token'], false, ($content['expires_in'] ?? 60) - 5);
        return $content['access_token'];
    }


    protected function request($method, $endpoint, $params = [], $endpointSuffix = '', $saveEi = true)
    {
        $method = strtoupper($method);
        $post = in_array($method, ['POST', 'PUT', 'PATCH']);
        $url = $this->getParameter('tern_url');

        $context = $params;
        $context['__url'] = $endpoint . $endpointSuffix;
        
        if (!Util::isLive()) {
            Log::debug('Tern API URL: ' . ($url ?: 'NULL'));
            Log::debug('Tern API Endpoint: ' . $endpoint);
            Log::debug('Tern API Full URL: ' . ($url ?: 'NULL') . $endpoint);
        }
        $token = $this->getClientToken(false);
        $ei = ExternalInvoke::create('tern_' . $method . '_' . $endpoint, $context, null, false);

        $ei->setRequestKey(Util::guid());
        if ($saveEi) {
            $ei->persist();
        }

        if ($endpointSuffix) {
          $endpoint .= $endpointSuffix;
        }

        $client = new Client();
        try {

            $headers = [
                'Content-Type' => 'application/json',
                'Accept-Language' => 'en-us',
                'Authorization' => 'Bearer ' . $token,
            ];
            if (!Util::isLive()) {
              Log::debug('Request header: ', $headers);
            }
            $options = [
                'headers' => $headers,
            ];
            if ($params) {
                if ($post) {
                    $options['json'] = $params;
                } else {
                    $options['query'] = $params;
                }
            }

            $response = $client->request($method, $url . $endpoint, $options);
        } catch (RequestException $exception) {
            $response = $exception->getResponse();
            if (!$response) {
                $msg = 'Empty response when call Tern API: ' . $exception->getMessage();
                $ei->fail(null, $msg)
                    ->persist();
                Log::exception($msg, $exception);
                $this->cacheErrorToPool($ei);
                return [$ei, null];
            }

            $rawContent = $response->getBody()->getContents();
            $content = Util::s2j($rawContent ?: '{}') ?? [];
            $status = $content['status'] ?? null;
            if (isset($content['isSuccess']) && !$content['isSuccess']) {
              $msg = $content['errorMessage'];
            } else {
              if ($status > 500) {
                $this->cacheErrorToPool($ei);
              }
              $msg = $exception->getMessage();
            }
            $msg = 'Error occurred when call the Tern API: ' . $msg;
            Log::debug($msg);
            $ei->fail($rawContent, $msg)
                ->persist();
            return [$ei, $content];
        }

        $rawContent = $response->getBody()->getContents();
        if (!Util::isLive()) {
          Log::debug($rawContent);
        }
        $content = Util::s2j($rawContent) ?? [];
        $ei->succeed($content);

        if ($saveEi) {
            $ei->persist();
        }
        return [$ei, $content];
    }

    public function getParameter(string $key, bool $encrypted = true)
    {
        if (Util::isLocal()) {
            return Util::getConfigKey($this->live ? $key : ($key . '_test'));
        }
        return Util::getKmsParameter($this->live ? $key : ($key . '_test'));
    }

    private function cacheErrorToPool(ExternalInvoke $ei)
    {
        $error = $ei->getError();
        if (!$error) {
            return;
        }
        $key = 'tern_error_pool';
        $pool = Data::getArray($key);
        $pool[] = $ei->getId();
        Data::setArray($key, $pool);
    }

    //region ================== Program =================
    public function getPlatform(): ?Platform
    {
        $p = $this->traitGetPlatform();
        if ($p) {
            $this->platform = $p;
            return $p;
        }
        if (!$this->platform && ($this->platformKey === 'mex' || $this->platformKey === 'mex_wire' )) {
            $this->platform = Platform::transferMex();
        }
        return $this->platform;
    }

    public function getProgramId()
    {
        return $this->getParameter('tern_program_id', false); // tern_mex_program_id
    }
    //endregion

    //region ================== Organizations =================


    public function getBalanceCacheKey(): string
    {
        return $this->platformKey . '_tern_business_balance_' . $this->businessAccountId;
    }

    public function getKey(): string
    {
        return 'TERN';
    }

    public function getAgentNumber(): string
    {
        return 'tern_' . $this->businessAccountId;
    }

    public function getRoutingNumber(UserCard $uc = null): string
    {
        // Return Tern-specific routing number
        return '*********'; // TransPecos Bank routing number
    }

    public function getDirectDepositNumber(UserCard $uc = null): string
    {
        // Return direct deposit account number for Tern
        $uc = $uc ?? $this->getUserCard();
        if (!$uc || !$uc->getId()) {
            return '';
        }
        return $uc->getAccountNumber() ?? '';
    }

     public function getBaseBusinessId()
    {
        return $this->getPlatform()->getTernBusinessId();
    }

    public function getBaseBusinessAccountId()
    {
        return $this->getPlatform()->getTernBusinessAccountId();
    }


    private function getBusinessId(UserGroup $group = null, $force = true)
    {
        $group = $group ?? $this->getUserGroup();
        if (!$group) {
            return $this->businessId;
        }
        $id = $group->getTernBusinessId();
        if (!$id && $force) {
            throw PortalException::create('Please configure the prefunding parameters of employer ' . $group->getName());
        }
        return $id ?? $this->getBaseBusinessId();
    }

    private function getBusinessAccountId(UserGroup $group = null, $force = true)
    {
        $group = $group ?? $this->getUserGroup();
        if (!$group) {
            return $this->businessAccountId;
        }
        $id = $group->getTernBusinessAccountId();
        if (!$id && $force) {
            throw PortalException::create('Please configure all the prefunding parameters of employer ' . $group->getName());
        }
        return $id ?? $this->getBaseBusinessAccountId();
    }

    public function affirmBusinessAccountId()
    {
        $accountId = $this->getBusinessAccountId();
        if ( ! $accountId) {
            $extra = $this->getBusinessId();
            $group = $this->getUserGroup();
            if ($group) {
                $extra .= ', ' . $group->getName();
            }
            throw PortalException::create('Empty employer business account (' . $extra . ')');
        }
        return $accountId;
    }

    public function ensureBusinessId(UserGroup $group = null, $individual = false)
    {
        $group = $group ?? $this->getUserGroup();
        $bid = $this->getBusinessId($group, false);
        if ($individual && $bid && $bid === $this->getBaseBusinessId()) {
            $bid = null;
        }
        if (!$bid) {
            if (!$group) {
                throw PortalException::create('Invalid user group when creating Tern business ID.');
            }
            $result = ExternalInvoke::host($this->createOrganization($group));
            $bid = $result['id'] ?? null;
            if ($bid) {
                $group->setTernBusinessId($bid)
                    ->persist();
            }
        } else if ($group && $group->getTernBusinessId()) {
            $result = ExternalInvoke::host($this->updateOrganization($group));
        }
        if (!$bid) {
            throw PortalException::create('Failed to ensure the Tern business ID.', [
                'group' => $group?->getId(),
            ]);
        }
        
        return $bid;
    }

    public function ensureBusinessAccountId(UserGroup $group = null, $individual = false)
    {
        $group = $group ?? $this->getUserGroup();
        $accountId = $this->getBusinessAccountId($group, false);
        if ($individual && $accountId && $accountId === $this->getBaseBusinessAccountId()) {
            $accountId = null;
        }
        if (!$accountId) {
            $result = ExternalInvoke::host($this->listAccounts($group));
            if (count($result)) {
                foreach($result as $r) {
                    if ($r['organization_id'] && $r['organization_id'] === $this->getBusinessId($group)) {
                        $accountId = $r['id'];
                        break;
                    }
                }
            }
            if ($accountId) {
                if ($group) {
                    $group->setTernBusinessAccountId($accountId);
                } else {
                    try {
                        throw new \Exception(__METHOD__);
                    } catch (\Throwable $t) {
                        Log::debug('Filling platform Tern business account ID unexpectedly', [
                            'trace' => $t->getTraceAsString(),
                            'businessId' => $this->getBusinessId($group),
                            'businessAccountId' => $accountId,
                        ]);
                    }
                }
                Util::flush();
            }
        }
        // if (!$accountId && $group) {
        //     $result = ExternalInvoke::host($this->createBusinessAccount($group));
        //     $accountId = $result['id'] ?? null;
        //     if ($accountId) {
        //         $group->setTernBusinessAccountId($accountId);
        //         Util::flush();
        //     }
        // }
        if (!$accountId) {
            throw PortalException::create('Failed to ensure the Tern business account ID.', [
                'group' => $group?->getId(),
                'business_id' => $this->getBusinessId($group),
            ]);
        }
        return $accountId;
    }



    public function listOrganizations() {
        $programId = $this->getProgramId();
        return $this->request('get', '/identity/programs/' . $programId . '/organizations');
    }

    public function listAccounts($isOrganizationAccount = true) {
        $programId = $this->getProgramId();
        $params = [
            'program_id' => $programId,
            'status' => 'active'
        ];

        return $this->request('get', '/ledger/accounts', $params);
    }

    public function createOrganization(UserGroup $group)
    {
        $admin = $group->getPrimaryAdmin();
        $programId = $this->getProgramId();
        return $this->request('POST', '/identity/programs/' . $programId . '/organizations', [
            'name' => $group->getName(),
            'address_1' => Util::maxLength($admin?->getAddress(), 50),
            'address_2' => Util::maxLength($admin?->getAddressline(), 50),
            'locality' => Util::maxLength($admin?->getCity(), 50),
            'state_or_province' => Util::field($admin?->getState(), 'abbr'),
            'postal_code' => $admin?->getZip(),
            'country' => Util::field($admin?->getCountry(), 'iso3Code'),
            'dba' => Util::formatBirthday($admin?->getBirthday()),
        ], saveEi: true);
    }

    public function updateOrganization(UserGroup $group)
    {
        $admin = $group->getPrimaryAdmin();
        $programId = $this->getProgramId();
        return $this->request('PUT', '/identity/programs/' . $programId . '/organizations/' . $group->getTernBusinessId(), [
            'name' => $group->getName(),    
            'address_1' => Util::maxLength($admin?->getAddress(), 50),
            'address_2' => Util::maxLength($admin?->getAddressline(), 50),
            'locality' => Util::maxLength($admin?->getCity(), 50),
            'state_or_province' => Util::field($admin?->getState(), 'abbr'),
            'postal_code' => $admin?->getZip(),
            'country' => Util::field($admin?->getCountry(), 'iso3Code'),
            'dba' => Util::formatBirthday($admin?->getBirthday()),
        ], saveEi: true);
    }

    // public function createBusinessAccount(UserGroup $group)
    // {
    //     $programId = $this->getProgramId();
    //     return $this->request('POST', '/ledger/accounts', [   
    //         'name' => $group->getName(),     
    //         'program_id' => $programId,
    //         'organization_id' => $this->getBusinessId($group),
    //         'ledger_id' => '',
    //         'type' => '',
    //         'rules_id' => ''
    //     ], saveEi: true);
    // }
    public function retrieveBusinessAccount($accountId = null)
    {
        $accountId = $accountId ?? $this->businessAccountId;
        return $this->request('GET', '/ledger/accounts/' . $accountId);
    }
    //endregion

    //region ================== Card Operations =================
    public function createCard(UserCard $uc): array
    {
        // Implement Tern card creation logic
        return ExternalInvoke::tempFailedArray('Tern card creation not yet implemented');
    }

    public function enroll(UserCard $uc): array
    {
        // Implement Tern card enrollment logic
        return ExternalInvoke::tempFailedArray('Tern card enrollment not yet implemented');
    }

    public function activate(UserCard $uc): array
    {
        // Implement Tern card activation logic
        return ExternalInvoke::tempFailedArray('Tern card activation not yet implemented');
    }

    public function getBalance(UserCard $uc): int
    {
        // Implement Tern balance retrieval logic
        return 0;
    }

    public function assignCard(UserCard $uc, #[\SensitiveParameter] string $cardNumber): array
    {
        // Implement Tern card assignment logic
        return ExternalInvoke::tempFailedArray('Tern card assignment not yet implemented');
    }

    public function changePin(UserCard $uc, #[\SensitiveParameter] string $newPin, #[\SensitiveParameter] ?string $cardNumber = null): array
    {
        // Implement Tern PIN change logic
        return ExternalInvoke::tempFailedArray('Tern PIN change not yet implemented');
    }

    public function reassignCard(UserCard $uc, #[\SensitiveParameter] string $cardNumber): array
    {
        // Implement Tern card reassignment logic
        return ExternalInvoke::tempFailedArray('Tern card reassignment not yet implemented');
    }

    public function updateStatus(UserCard $uc, string $status, ?string $lastForDigits = null, bool $accountLevel = false): array
    {
        // Implement Tern status update logic
        return ExternalInvoke::tempFailedArray('Tern status update not yet implemented');
    }

    public function loadAmount(UserCard $uc, int $txnAmount, string $txnDescription): array
    {
        // Implement Tern load amount logic
        return ExternalInvoke::tempFailedArray('Tern load amount not yet implemented');
    }

    public function reverseFunds(UserCard $uc, int $txnAmount, string $txnDescription): array
    {
        // Implement Tern reverse funds logic
        return ExternalInvoke::tempFailedArray('Tern reverse funds not yet implemented');
    }

    public function getConsumerDetailsData(UserCard $uc): array
    {
        // Implement Tern consumer details logic
        return [
            'accountNumber' => $uc->getAccountNumber(),
            'availableBalance' => $uc->getBalance(),
            'currentBalance' => $uc->getBalance(),
        ];
    }

    public function getCardDetailsData(UserCard $uc): array
    {
        // Implement Tern card details logic
        return [
            'accountNumber' => $uc->getAccountNumber(),
            'availableBalance' => $uc->getBalance(),
            'currentBalance' => $uc->getBalance(),
        ];
    }

    public function getAgentAvailableBalance(): int
    {
        // Implement Tern agent balance logic
        return 0;
    }

    public function consumerTransactions(UserCard $uc, Carbon $fromDate, Carbon $toDate,
                                        int $page = 1, int $pageSize = 120,
                                        string $status = 'All',
                                        array $otherParams = []): array
    {
        // Implement Tern transaction retrieval logic
        return [];
    }

    public function validateFullCardNumberWithCards(UserCard $uc, $pan, $when = 'enrolling'): string
    {
        // Implement Tern card validation logic
        return '';
    }
    //endregion

}
