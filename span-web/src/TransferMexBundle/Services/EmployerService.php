<?php


namespace TransferMexBundle\Services;


use Carbon\Carbon;
use CoreBundle\Entity\Attachment;
use CoreBundle\Entity\CardProgramCardType;
use CoreBundle\Entity\Config;
use CoreBundle\Entity\Email;
use CoreBundle\Entity\NotificationEmails;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\Processor;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\Transfer;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserGroup;
use CoreBundle\Services\Common\CardService;
use CoreBundle\Services\Pdf\PdfBoxService;
use CoreBundle\Services\ZipService;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Util;
use Doctrine\ORM\QueryBuilder;
use FaasBundle\Services\ProcessorHub;
use FaasBundle\Services\ProcessorTrait;
use PhpZip\Model\ZipEntry;
use SalexUserBundle\Entity\User;
use Symfony\Component\HttpFoundation\Response;
use TransferMexBundle\Entity\TransferPayoutRecord;
use UsUnlockedBundle\Services\Privacy\PrivacyAPI;
use CoreBundle\Utils\Data;
use FaasBundle\Services\BOTM\BotmAPI;
use FaasBundle\Services\BOTM\BotmService;
use TransferMexBundle\Entity\EmployeeSSN;
use TransferMexBundle\Entity\EmployerSubCompany;
use TransferMexBundle\Entity\UserTransferMexTrait;

class EmployerService
{
    public const DD_PDF_FORMAT_STANDARD = 'standard';
    public const DD_PDF_FORMAT_LOWER_ID = 'lowerId';
    public const DD_PDF_FORMAT_AG_EMPLEO = 'agEmpleo';
    public const DD_PDF_FORMAT_ELE_TRANS = 'ele_trans';
    public const DD_PDF_FORMAT_TREVINO = 'trevino';
    public const DD_PDF_FORMAT_TREVINO2 = 'trevino2';
    public const DD_PDF_FORMAT_MAGANA_LABOR = 'magana_labor';
    public const DD_PDF_FORMAT_CONSOLIDATED_CITRUS = 'consolidated_citrus';
    public const DD_PDF_FORMAT_MEMBER_ID = 'memberId';
    public const DD_PDF_FORMAT_RITTER_FARMS = 'ritter_farms';
    public const DD_PDF_CHAPA_GLOBAL = 'chapa_global';
    public const DD_PDF_FORMAT_SIERRA_CASCADE_NURSERY = 'sierra_cascade_nursery';
    public const DD_PDF_FORMAT_VALICOFF_FRUIT = 'valicoff_fruit';
    public const DD_PDF_FORMAT_N_J_HARVESTING = 'n_j_harvesting';
    public const DD_PDF_FORMAT_CHAPA_GLOBAL = 'chapa_global';
    public const DD_PDF_VERMILLION_RANCH_LIMITED_PARTNERSHIP = 'Vermillion_Ranch_Limited_Partnership';
    public const DD_PDF_J_E_BERRY_FARMS_LLC = 'j_e_berry_farms_llc';
    public const DD_PDF_REITER_BROTHERS_INC = 'Reiter_Brothers_inc';
    public const DD_PDF_MOUNTAIN_FRESH_LLC = 'Mountain_Fresh_LLC ';
    public const DD_PDF_K_SCHLEGEL_FRUIT_FARM_LLC = 'K_SCHLEGEL_FRUIT_FARM_LLC';

    public const W2S_PDF_FORMAT_STANDARD = 'standard';
    public const W2S_PDF_FORMAT_N_J_HARVESTING = 'n_j_harvesting';
    public const W2S_PDF_FORMAT_CHAPA_GLOBAL = 'chapa_global';

    public static function getAllKeyedBy($by = 'id')
    {
        $all = Util::em()->getRepository(User::class)
            ->createQueryBuilder('u')
            ->join('u.teams', 't')
            ->join('u.config', 'config')
            ->join('config.group', 'g')
            ->where(Util::expr()->in('t.name', ':roles'))
            ->setParameter('roles', [
                Role::ROLE_TRANSFER_MEX_EMPLOYER,
            ])
            ->orderBy('g.name')
            ->distinct()
            ->getQuery()
            ->getResult();
        $method = 'get' . ucfirst($by);
        $result = [];
        /** @var User $item */
        foreach ($all as $item) {
            $group = $item->getAdminGroup();
            if ($by === 'id') {
                $key = $item->$method();
            } else {
                $key = $group->$method();
            }
            $result[$key] = $item;
        }
        return $result;
    }

    public static function listForSelection($role)
    {
        $userList = Util::em()->getRepository(User::class)
            ->createQueryBuilder('u')
            ->join('u.teams', 't')
            ->join('u.config', 'config')
            ->join('config.group', 'g')
            ->where(Util::expr()->in('t.name', ':roles'))
            ->setParameter('roles', [
                $role,
            ])
            ->orderBy('g.name')
            ->distinct()
            // ->select('u.id as value, g.name as label')
            ->getQuery()
            ->getResult();
        $res = [];
        foreach ($userList as $user) {
          $group = $user->getAdminGroup();
          $res[] = [
            'value' => $user->getId(),
            'label' => $group->getName(),
            'isBotm' => $group->isBOTM(),
            'isPrimaryCompany' => Util::meta($user, 'isPrimaryEmployer')
          ];
        }
        return $res;
    }

    public static function getUserGroupsWithCustomRapidAccount($platform)
    {
        $q = Util::em()->getRepository(UserGroup::class)
            ->createQueryBuilder('ug')
            ->where('ug.meta like :meta')
            ->setParameter('meta', '%"rapidAgentNumber":"%');

        if ($platform) {
            $q->andWhere('ug.platform = :platform')
                ->setParameter('platform', $platform);
        }

        return $q->getQuery()
            ->getResult();
    }

     /**
     * Create/get the dummy card for employer to hold the user balance info
     *
     * @param User $user
     *
     * @return UserCard
     */
    public static function getMexDummyCard(User $user)
    {
        $cards = $user->getCardsInPlatformsByType(Util::cardProgram(), PrivacyAPI::CARD_TYPE_DUMMY, Util::cardProgram());

        if ($cards->count()) {
            return $cards->first();
        }
        $cpct = CardProgramCardType::getForCardProgram(Util::cardProgram());
        $uc = CardService::create($user, $cpct);
        $uc->setType(PrivacyAPI::CARD_TYPE_DUMMY)
            ->setIssued(true)
            ->setStatus(UserCard::STATUS_ACTIVE)
            ->persist();
        $user->addCard($uc);
        return $uc;
    }

    public static function getEmployerPublicBalance(User $user, $force = false)
    {
        $group = $user->getAdminGroup();
        $type = $group->getFundingType();
        if ($type === UserGroup::FUNDING_TYPE_ACH) {
            $uc = $user->getOneCardInPlatform();
            return $uc ? $uc->getBalance() : 0;
        }
        if ($type === UserGroup::FUNDING_TYPE_PREFUNDED) {
            return self::getAvailableBalance($user, $force);
        }
        return 0;
    }

    public static function getAvailableBalance(User $employer, $realtime = true)
    {
        $api = ProcessorHub::getForUserGroupAdmin($employer);
        if ($realtime) {
            $balance = ProcessorHub::updateAgentBalance($api);
        } else {
            $balance = $api->getCachedBalance();
        }

        $holding = self::getHoldingPayoutsAmount($employer);
        return $balance - $holding;
    }

    public static function getBOTMAvailableBalance(User $employer, $realtime = true)
    {
        $api = ProcessorHub::getForUserGroupAdmin($employer);
        if ($realtime) {
            $balance = ProcessorHub::updateAgentBalance($api);
        } else {
            $balance = $api->getCachedBalance();
        }

        return $balance;
    }

    public static function getHoldingPayoutsAmount(User $employer, &$parts = null, $withQueued = true, $forceUpdate = true)
    {
        $group = $employer->getAdminGroup();
        $agentNumber = $group->getRawRapidAgentNumber();
        if (!$agentNumber) {
            return 0;
        }

        // Since Rapid balance has been transferred to BOTM, and they don't allow us to call their APIs
        // to move to base, the holding balance is just the agent account balance
        return RapidAPI::getForUserGroupAdmin($employer)->getCachedBalance();
    }

    public static function extractDirectDepositZip(User $employer, Attachment $zip, $prefix = null, $by = null)
    {
        $dir = Util::secureDir('mex_dd_pdf') . $zip->getId();
        $dir = Util::ensureFile($dir, true);

        $targetDir = Util::ensureFile(Util::secureDir('mex_dd_pdf') . 'parsed', true);

        $files = ZipService::extract($zip->prepareForLocalRead(), $dir);
        $prefix = $prefix ?: '';
        $parsed = [];

        $group = $employer->getAdminGroup();
        $query = $group->queryUsers(null, true);

        Log::debug('Starting to extract dd files', [
            'employer' => $group->getName(),
            'targetDir' => $targetDir,
            'zip' => $zip->getFileName(),
            'prefix' => $prefix,
        ]);

        $box = new PdfBoxService();

        /**
         * @var string $path
         * @var ZipEntry $entry */
        foreach ($files as $path => $entry) {
            if (!Util::endsWith(strtolower($path), '.pdf')) {
                continue;
            }
            if (strpos($path, '__MACOSX') !== false) {
                continue;
            }

            $name = $entry->getName();
            Log::debug('Extracting dd file: ' . $name);

            $originName = pathinfo($name, PATHINFO_FILENAME);
            $parsed[$name] = [];

            $hts = $box->htmlPages($path);
//            var_dump($hts);
            $total = count($hts);
            $box->split($path);

            foreach ($hts as $i => $ht) {
                Log::debug(($i + 1) . '/' . $total . ': Mapping dd file to employee');
                $list = explode('/', str_replace($dir . '/', '', $path));

                if (count($list)) {
                  $page = str_replace($list[count($list) - 1], $originName . '-' . ($i + 1) . '.pdf',  $path) ;
                }  else {
                  $page = $dir . '/' . $originName . '-' . ($i + 1) . '.pdf';
                }
                if (!file_exists($page)) {
                    $parsed[$name[$i]] = 'No page ' . ($i + 1);
                    continue;
                }
                $newPath = $targetDir . '/dd_orc_temp_' . $zip->getId() . '_' . ($i + 1) . '.pdf';
                if (!Util::isLive()){
                  Log::debug($ht);
                }
                // orc pdf
                if ((Util::isStaging() || strlen($ht) < 100 || in_array($employer->getId(), Config::array('transfermex_dd_use_orm'))) && Util::executeORC($page, $newPath)) {
                  $tempList = $box->htmlPages($newPath);
                  $ht = !empty($tempList[0]) ? $tempList[0] : '';
                }

                $pdfType = self::DD_PDF_FORMAT_STANDARD;
                $matches = [];
                $fullName = null;
                $isW2Page = false;
                $payDate = null;
                // Standard PDF format
                preg_match('| ID:([0-9a-zA-Z]+) SSN:|m', $ht, $matches);
                $id = $matches[1] ?? null;
                if (empty($id)) {
                    $pdfType = self::DD_PDF_FORMAT_LOWER_ID;
                    $matches = [];
                    // PDF format with anchor `Id`, like "RUN 28717.pdf"
                    preg_match('| Id: ([0-9a-zA-Z]+) SSN:|m', $ht, $matches);
                    $id = $matches[1] ?? null;
                    if (empty($id)) {
                        // AgEmpleo format
                        $matches = [];
                        $pdfType = self::DD_PDF_FORMAT_AG_EMPLEO;
                        if ($group->getPrimaryAdmin()->getId() === *********) {
                          preg_match('`EMP#([A-Za-z\d#.,&;/\- ]+?) (MX_)?(\d+) .+(\n.+)+ Account XXX`m', $ht, $matches);
                        } else {
                          preg_match('`<p>([A-Za-z\d#.,&;/\- ]+?) (MX_)?(\d+) .+(\n.+)+ Account XXX`m', $ht, $matches);
                        }
                        $fullName = $matches[1] ?? null;
                        $id = $matches[3] ?? null;
                        $employersList = Util::isLive() ? [*********,*********,*********,*********] : [*********];
                        if ($id && !in_array($group->getPrimaryAdmin()->getId(), $employersList)) {
                            $id = Util::removePrefix($id, 'MX_');
                        } else {
                            // The format with ELECTRONIC TRANSFER
                            $matches = [];
                            $pdfType = self::DD_PDF_FORMAT_ELE_TRANS;
                            preg_match('|<p><b>Vendor: .+ ID: ([A-Za-z\d]+)\n|m', $ht, $matches);
                            $id = $matches[1] ?? null;
                            if (!$id) {
                              $matches = [];
                              $pdfType = self::DD_PDF_FORMAT_TREVINO;
                              preg_match('`[\d\-]+ State Filing Stat`', $ht, $matches);
                              $id = isset($matches[0]) ? Util::removeSuffix($matches[0], ' State Filing Stat') :  null;
                            }

                            if (!$id) {
                              $matches = [];
                              $pdfType = self::DD_PDF_FORMAT_MAGANA_LABOR;
                              preg_match('`Empl #:\s*\d*\s* SS #:`', $ht, $matches);
                              $id = isset($matches[0]) ? Util::removeSuffix(Util::removePrefix($matches[0], 'Empl #:'), 'SS #:') :  null;
                            }

                            if (!$id) {
                              $matches = [];
                              $pdfType = self::DD_PDF_FORMAT_CONSOLIDATED_CITRUS;
                              preg_match('`Identification#\s*<\/p>\s*<p>\s*(<b>)*(\s*\d*\s*)`', $ht, $matches);
                              $id = isset($matches[2]) ? $matches[2] : null;
                            }
                            if (!$id) {
                              $matches = [];
                              $pdfType = self::DD_PDF_FORMAT_MEMBER_ID;
                              preg_match('|\s*TMX:\s*([0-9]+)|m', $ht, $matches);
                              $id = $matches[1] ?? null;
                            }
                            if (!$id) {
                                $matches = [];
                                $pdfType = self::DD_PDF_FORMAT_TREVINO2;
                                preg_match('|TREVINOS AG SERVICES, LLC +([0-9]+) +(.*)|m', $ht, $matches);
                                $id = $matches[1] ?? null;
                                $fullName = $matches[2] ?? null;
                            }
                            if (!$id) {
                              $matches = [];
                              $pdfType = self::DD_PDF_FORMAT_SIERRA_CASCADE_NURSERY;
                              preg_match('|Employee Code:\s*([A-Za-z\d]+)\s*|', $ht, $matches);
                              $id = $matches[1] ?? null;
                              $fullName = $matches[2] ?? null;
                            }

                            if (!$id) {
                              $matches = [];
                              $pdfType = self::DD_PDF_FORMAT_RITTER_FARMS;
                              preg_match('|Employee ID:\s*([0-9]+)\s*|', $ht, $matches);
                              $id = $matches[1] ?? null;
                              $fullName = $matches[2] ?? null;
                            }
                            if (!$id) {
                              $matches = [];
                              $pdfType = self::DD_PDF_CHAPA_GLOBAL;
                              preg_match('|Employee ID:\s*([0-9a-zA-Z]+)\s*|', $ht, $matches);
                              $id = $matches[1] ?? null;
                              $fullName = $matches[2] ?? null;
                            }
                            if (!$id) {
                              $matches = [];
                              $pdfType = self::DD_PDF_FORMAT_VALICOFF_FRUIT;
                              preg_match('|Emp ID\s*</b>\s*([0-9A-Za-z]+)\s*|', $ht, $matches);
                              $id = $matches[1] ?? null;
                              $fullName = $matches[2] ?? null;
                            }
                            if (!$id) {
                              $matches = [];
                              $pdfType = self::DD_PDF_FORMAT_N_J_HARVESTING;
                              preg_match('`Acct\s*#:\s*([0-9]+)\s*`', $ht, $matches);
                              $id = $matches[1] ?? null;
                              $fullName = $matches[2] ?? null;
                            }
                            // if (!$id) {
                            //   $matches = [];
                            //   $pdfType = self::DD_PDF_FORMAT_N_J_HARVESTING;
                            //   preg_match('`Control number\s*</p>\s*<p>\s*([0-9]+)\s*`', $ht, $matches);
                            //   $id = $matches[1] ?? null;
                            //   $fullName = $matches[2] ?? null;
                            //   $isW2Page = true;
                            // }
                            // if (!$id) {
                            //   $matches = [];
                            //   $pdfType = self::DD_PDF_FORMAT_CHAPA_GLOBAL;
                            //   preg_match('`00000([1-9A-Za-z][0-9A-Za-z]+)\s*`', $ht, $matches);
                            //   $id = $matches[1] ?? null;
                            //   $fullName = $matches[2] ?? null;
                            //   // $isW2Page = true;
                            // }
                            if (!$id) {
                              $matches = [];
                              $pdfType = self::DD_PDF_VERMILLION_RANCH_LIMITED_PARTNERSHIP;
                              preg_match('`\(ID Del Empleado\)\s*(\d(?:_\d+)*)`', $ht, $matches);
                              $id = !empty($matches[1]) ?  str_replace('_','', $matches[1]) : null;
                              $fullName = '';
                              // Log::debug($path);
                              if (!$id) {
                                $matches = [];
                                preg_match('`Employee ID#\s*</b>\(ID Del Empleado\)\s*`', $ht, $matches);
                                if (!empty($matches)) {
                                  $outPath = str_replace('.pdf', '.json', $path);
                                  $res = Util::executePdfCpu($path, $outPath);
                                  if ($res) {
                                    $forms = $res['forms'];
                                    foreach($forms as $form) {
                                      if (isset($form['textfield'])) {
                                        foreach ($form['textfield'] as $textfield) {
                                          if ($textfield['name'] == 'Emplyee ID' || $textfield['name'] == 'Employee ID' ) {
                                            $id = $textfield['value'];
                                          }
                                        }
                                      }
                                      if (isset($form['datefield'])) {
                                        foreach ($form['datefield'] as $textfield) {
                                          if ($textfield['name'] == 'Pay Date' ) {
                                            $payDate = $textfield['value'];
                                          }
                                        }
                                      }
                                    }
                                  }
                                }
                              }
                            }
                            if (!$id) {
                              $matches = [];
                              $pdfType = self::DD_PDF_J_E_BERRY_FARMS_LLC;
                              preg_match('/EMP\s*#.*?(\d+).*?Account/s', $ht, $matches);
                              $id = $matches[1] ?? null;
                              $fullName = $matches[2] ?? null;
                            }
                            if (!$id) {
                              $matches = [];
                              $pdfType = self::DD_PDF_REITER_BROTHERS_INC;
                              preg_match('/<p>[[A-Za-z\s\.]*(\d+)\s*XXX-XX/s', $ht, $matches);
                              $id = $matches[1] ?? null;
                              $fullName = $matches[2] ?? null;
                              Log::debug('====', $matches);
                            }
                            if (!$id) {
                              $matches = [];
                              $pdfType = self::DD_PDF_MOUNTAIN_FRESH_LLC;
                              preg_match('`\*\*\*-\*\*-([0-9]{4})`', $ht, $matches);
                              Log::debug('matches', $matches);
                              $id = !empty($matches[1]) ? trim($matches[1]) : null;
                            }
                            if (!$id) {
                              $matches = [];
                              $pdfType = self::DD_PDF_K_SCHLEGEL_FRUIT_FARM_LLC;
                              preg_match('/\s*XXX-XX-([0-9]{4})/s', $ht, $matches);
                              $id = !empty($matches[1]) ? trim($matches[1]) : null;
                            }
                        }
                    }
                }
                $id = trim($id);
                $fullName = trim($fullName);

                if (empty($id)) {
                    $parsed[$name][$i] = 'No ID found.';
                    continue;
                }

                if (in_array($pdfType, [
                    self::DD_PDF_FORMAT_STANDARD,
                    self::DD_PDF_FORMAT_LOWER_ID,
                    self::DD_PDF_FORMAT_MEMBER_ID
                ])) {
                    $matches = [];
                    preg_match('|Employee: ([0-9a-zA-Z\- ]+)( Check: \d+)?\n|m', $ht, $matches);
                    $fullName = $matches[1] ?? null;
                    if ($fullName) {
                        $fullName = preg_replace('| +|m', ' ', $fullName);
                    }
                } else if ($pdfType === self::DD_PDF_FORMAT_ELE_TRANS) {
                    $matches = [];
                    preg_match('|<p><b>ELECTRONIC TRANSFER\n</b></p>\n<p>(.+)\n|m', $ht, $matches);
                    $fullName = $matches[1] ?? null;
                }
                else if ($pdfType === self::DD_PDF_FORMAT_SIERRA_CASCADE_NURSERY) {
                  $matches = [];
                  preg_match('|Employee:\s*([0-9a-zA-Z\- ]+)\s*Employee|m', $ht, $matches);
                  $fullName = $matches[1] ?? null;
                } else if ($pdfType === self::DD_PDF_FORMAT_RITTER_FARMS || $pdfType === self::DD_PDF_CHAPA_GLOBAL) {
                  $matches = [];
                  preg_match('|' . strtoupper($group->getName()) . '\.\s*([0-9a-zA-Z\- ]+)\s*Employee ID:|', $ht, $matches);
                  $fullName = $matches[1] ?? null;
                } else if ($pdfType === self::DD_PDF_FORMAT_N_J_HARVESTING && !$isW2Page) {
                  $matches = [];
                  preg_match('`\.\s*([a-zA-Z ]+)\s*Acct#:`', $ht, $matches);
                  $fullName = $matches[1] ?? null;
                } else  if ($pdfType == self::DD_PDF_MOUNTAIN_FRESH_LLC) {
                  $matches = [];
                  preg_match('`Employee SSN\s*</p>\s*<p>\s*([A-Za-z\s\.]*),`', $ht, $matches);
                  $fullName = $matches[1] ?? null;
                  Log::debug('adada', $matches);
                }

                $fullName = trim($fullName);
                /** @var User|string $member */
                if (in_array($pdfType, [self::DD_PDF_MOUNTAIN_FRESH_LLC, self::DD_PDF_K_SCHLEGEL_FRUIT_FARM_LLC])) {
                  $member = self::queryEmployeeBySSN($group->getPrimaryAdmin(), $id, $fullName);
                } else {
                  $member = self::queryEmployeeByExternalId($query, $id, $prefix, $fullName);
                }
                if (is_string($member)) {
                    if ($pdfType === self::DD_PDF_FORMAT_LOWER_ID) {
                        $member = self::queryEmployeeByExternalId($query, $id, $prefix . 'H0', $fullName, true);
                        if (is_string($member)) {
                            $member = self::queryEmployeeByExternalId($query, $id, $prefix . 'H1', $fullName, true);
                        }
                    } else if ($pdfType === self::DD_PDF_FORMAT_AG_EMPLEO) {
                        $member = self::queryEmployeeByExternalId($query, $id, $prefix, $fullName, 'fullName');
                    } else if ($pdfType === self::DD_PDF_FORMAT_MEMBER_ID || $pdfType == self::DD_PDF_VERMILLION_RANCH_LIMITED_PARTNERSHIP) {
                        $member = self::queryEmployeeById($query, $id, $prefix, $fullName);
                    }
                }
                if (is_string($member)) {
                    $parsed[$name][$i] = $member;
                    continue;
                }

                $id = $member->getExternalId();

                $matches = [];
                preg_match('|(<p>)*Ck Dt:.*?(<p>)*(\d+/\d+/\d+)+|ms', $ht, $matches);
                $ckDt = isset($matches[3]) ? $matches[3] : null;
                if ($ckDt) {
                    $ckDt = Carbon::parse($ckDt)->format(Util::DATE_FORMAT_ISO_FULL_DATE);
                }

                if (!$ckDt && in_array($pdfType, [self::DD_PDF_FORMAT_TREVINO, self::DD_PDF_FORMAT_ELE_TRANS, self::DD_PDF_FORMAT_MEMBER_ID, self::DD_PDF_FORMAT_SIERRA_CASCADE_NURSERY])) {
                    $matches = [];
                    preg_match('`Check Date:\s*(\d+/\d+/\d+)`', $ht, $matches);
                    $ckDt = isset($matches[0]) ? Carbon::parse(Util::removePrefix($matches[0], 'Check Date:'))->format(Util::DATE_FORMAT_ISO_FULL_DATE) :  null;
                }
                if (!$ckDt && $pdfType === self::DD_PDF_FORMAT_TREVINO2) {
                    $matches = [];
                    preg_match('`<p>Check date: (\d+/\d+/\d+)`', $ht, $matches);
                    $ckDt = isset($matches[1]) ? Carbon::createFromFormat('m/d/y', $matches[1])->format(Util::DATE_FORMAT_ISO_FULL_DATE) :  null;
                }
                if (!$ckDt && $pdfType === self::DD_PDF_FORMAT_CONSOLIDATED_CITRUS) {
                  $matches = [];
                  preg_match('`(\s*(\d+/\d+/\d+)\s*){3}`', $ht, $matches);
                  $ckDt = isset($matches[2]) ? Carbon::parse($matches[2])->format(Util::DATE_FORMAT_ISO_FULL_DATE) :  null;
                }
                if (!$ckDt && in_array($pdfType, [self::DD_PDF_FORMAT_RITTER_FARMS, self::DD_PDF_CHAPA_GLOBAL])) {
                  $matches = [];
                  preg_match('`Pay date:\s*</b>(\d+/\d+/\d+)\s*`', $ht, $matches);
                  $ckDt = isset($matches[1]) ? Carbon::parse($matches[1])->format(Util::DATE_FORMAT_ISO_FULL_DATE) :  null;
                }
                if (!$ckDt && in_array($pdfType, [self::DD_PDF_FORMAT_VALICOFF_FRUIT, self::DD_PDF_MOUNTAIN_FRESH_LLC])) {
                  $matches = [];
                  preg_match('`Pay Date:\s*</b>(\d+/\d+/\d+)\s*`', $ht, $matches);
                  $ckDt = isset($matches[1]) ? Carbon::parse($matches[1])->format(Util::DATE_FORMAT_ISO_FULL_DATE) :  null;
                }
                if (!$ckDt && $pdfType === self::DD_PDF_FORMAT_AG_EMPLEO) {
                  $matches = [];
                  preg_match('`Payment Date\s*</b></p>\s*<p>\s*(\d+/\d+/\d+)\s*`', $ht, $matches);
                  $ckDt = isset($matches[1]) ? Carbon::parse($matches[1])->format(Util::DATE_FORMAT_ISO_FULL_DATE) :  null;
                }
                if (!$ckDt && $pdfType === self::DD_PDF_FORMAT_N_J_HARVESTING) {
                  $matches = [];
                  preg_match('`\s*Date:\s*(\d+/\d+/\d+)\s*`', $ht, $matches);
                  $ckDt = isset($matches[1]) ? Carbon::parse($matches[1])->format(Util::DATE_FORMAT_ISO_FULL_DATE) :  null;
                }
                if (!$ckDt && $pdfType === self::DD_PDF_VERMILLION_RANCH_LIMITED_PARTNERSHIP) {
                  $matches = [];
                  preg_match('`Pay Date\s*</b>\s*_*(([\d_]+)\/([\d_]+)\/([\d_]+))_*`', $ht, $matches);
                  $ckDt = isset($matches[1]) ? Carbon::parse(str_replace('_','', $matches[1]))->format(Util::DATE_FORMAT_ISO_FULL_DATE) :  null;
                  if (!$ckDt && $payDate) {
                    $ckDt =  Carbon::parse($payDate)->format(Util::DATE_FORMAT_ISO_FULL_DATE);
                  }
                }
                if (!$ckDt && $pdfType === self::DD_PDF_J_E_BERRY_FARMS_LLC) {
                  $matches = [];
                  preg_match('`Payment Date\s*\n*</p>\n*\s*<p>\s*(\d+/\d+/\d+)\s*`', $ht, $matches);
                  $ckDt = isset($matches[1]) ? Carbon::parse($matches[1])->format(Util::DATE_FORMAT_ISO_FULL_DATE) :  null;
                }
                Log::debug($ckDt);
                if (!$ckDt && $pdfType === self::DD_PDF_REITER_BROTHERS_INC) {
                  $matches = [];
                  preg_match('`DEPOSIT NUMBER\s*((\d{2}\/\d{2}\/\d{2})\s*)+`', $ht, $matches);
                  $ckDt = isset($matches[1]) ? Carbon::parse($matches[1])->format(Util::DATE_FORMAT_ISO_FULL_DATE) :  null;
                  Log::debug($ckDt);
                }
               if (!$ckDt && $pdfType == self::DD_PDF_K_SCHLEGEL_FRUIT_FARM_LLC) {
                  $matches = [];
                  preg_match('`<p>\s*(\d+/\d+/\d+)\s*\d{5}`', $ht, $matches);
                  $ckDt = isset($matches[1]) ? Carbon::parse($matches[1])->format(Util::DATE_FORMAT_ISO_FULL_DATE) :  null;
                  Log::debug($ckDt);
                }
                $md5 = substr(md5($ht), 0, 16);
                $newPage = $targetDir . '/' . $member->getId() . '_' . $ckDt . '_' . $md5 . '.pdf';
                rename($page, $newPage);

                $att = Attachment::createFromHostedFile($newPage, 'mex_dd_pdf');
                $att->setCreateBy($member)
                    ->persist();

                if ($isW2Page) {
                  $pdfs = Util::meta($member, 'mexW2Pdfs') ?? [];
                  $pdfs[] = $att->getId();
                  Util::updateMeta($member, 'mexW2Pdfs', false);
                  Util::updateMeta($member, [
                      'mexW2Pdfs' => array_unique($pdfs),
                  ]);
                } else {
                  $pdfs = Util::meta($member, 'mexDdPdfs') ?? [];
                  $pdfs[] = $att->getId();
                  Util::updateMeta($member, 'mexDdPdfs', false);
                  Util::updateMeta($member, [
                      'mexDdPdfs' => array_unique($pdfs),
                  ]);
                }
                $parsed[$name][$i] = 'Attached to the employee ' . $id . ' (' . $member->getSignature() . ')';
            }
        }
//        dd($parsed);

        $mailBody = Util::render('@TransferMex/Employer/dd_pdf_parsed.html.twig', [
            'attachment' => $zip,
            'parsed' => $parsed,
        ]);
        $cc = [
          '<EMAIL>',
          '<EMAIL>',
        ];
        if ($by) {
          $cc = array_merge($cc, [$employer->getEmail()]);
        }
        $by = $by ? User::find($by) : $employer;
        if (Util::isLive()) {
          $cc= array_merge($cc, NotificationEmails::findByPlatform(Platform::transferMex()));
        }

        $cc = array_merge($cc, self::findRecipientByEmployer($employer));
        if (!Util::isLive()) {
          Log::debug('CC emails: ', $cc);
        }
        Email::sendWithTemplateToUser($by, Email::TEMPLATE_SIMPLE_LAYOUT, [
            'subject' => 'Direct Deposit PDFs Parsing Report',
            'body' => $mailBody,
            '_cc' => $cc,
        ]);

        return $parsed;
    }


    public static function extractW2sZip(User $employer, Attachment $zip, $prefix = null, $by = null)
    {
        $dir = Util::secureDir('mex_w2s_pdf') . $zip->getId();
        $dir = Util::ensureFile($dir, true);

        $targetDir = Util::ensureFile(Util::secureDir('mex_w2s_pdf') . 'parsed', true);

        $files = ZipService::extract($zip->prepareForLocalRead(), $dir);
        $prefix = $prefix ?: '';
        $parsed = [];
        $group = $employer->getAdminGroup();
        $query = $group->queryUsers(null, true);
        Log::debug('Starting to extract w2s files', [
            'employer' => $group->getName(),
            'targetDir' => $targetDir,
            'zip' => $zip->getFileName(),
            'prefix' => $prefix,
        ]);

        $box = new PdfBoxService();

        /**
         * @var string $path
         * @var ZipEntry $entry */
        foreach ($files as $path => $entry) {
            if (!Util::endsWith(strtolower($path), '.pdf')) {
                continue;
            }
            if (strpos($path, '__MACOSX') !== false) {
                continue;
            }

            $name = $entry->getName();
            Log::debug('Extracting w2s file: ' . $name);

            $originName = pathinfo($name, PATHINFO_FILENAME);
            $parsed[$name] = [];

            $hts = $box->htmlPages($path);

            $total = count($hts);
            $box->split($path);

            foreach ($hts as $i => $ht) {
                Log::debug(($i + 1) . '/' . $total . ': Mapping w2s file to employee');
                $list = explode('/', str_replace($dir . '/', '', $path));
                // Log::debug($ht);
                if (count($list)) {
                  $page = str_replace($list[count($list) - 1], $originName . '-' . ($i + 1) . '.pdf',  $path) ;
                }  else {
                  $page = $dir . '/' . $originName . '-' . ($i + 1) . '.pdf';
                }
                $newPath = $targetDir . '/orctemp.pdf';

                if (!file_exists($page)) {
                    $parsed[$name[$i]] = 'No page ' . ($i + 1);
                    continue;
                }

                // orc pdf
                if ((Util::isStaging() || strlen($ht) < 100) && Util::executeORC($page, $newPath)) {
                  $tempList = $box->htmlPages($newPath);
                  $ht = !empty($tempList[0]) ? $tempList[0] : '';
                }

                $pdfType = self::W2S_PDF_FORMAT_STANDARD;
                $matches = [];
                $useExternalId = false;
                // Standard PDF format
                preg_match('`XXX-XX-([0-9]{4})`', $ht, $matches);
                $lastSsn = $matches[1] ?? null;
                $lastSsn = trim($lastSsn);
                if (empty($lastSsn)) {
                    $pdfType = self::W2S_PDF_FORMAT_N_J_HARVESTING;
                    preg_match('`</p>\s*<p>\s*[0-9]{3}\s*[0-9]{2}\s*([0-9]{4})\s+`', $ht, $matches);
                    $lastSsn = $matches[1] ?? null;
                    $lastSsn = trim($lastSsn);
                    if (empty($lastSsn)) {
                      preg_match('`</p>\s*<p>\s*[0-9]{3}\s*[0-9]{2}\s*(([0-9]{1}\s*){4})\s+`', $ht, $matches);
                      $lastSsn = $matches[1] ?? null;
                      $lastSsn = preg_replace('/\s/', '', $lastSsn);
                      if (empty($lastSsn)) {
                        preg_match('`</p>\s*<p>\s*[0-9\s]{5}\s*[0-9]{2}\s*([0-9]{4})\s+`', $ht, $matches);
                        $lastSsn = $matches[1] ?? null;
                        $lastSsn = preg_replace('/\s/', '', $lastSsn);
                        if (empty( $lastSsn )) {
                          preg_match('`\s+[0-9]{3}\s*[0-9]{2}\s*([0-9]{4})\s+`', $ht, $matches);
                          $lastSsn = $matches[1] ?? null;
                          $lastSsn = preg_replace('/\s/', '', $lastSsn);
                          if (empty( $lastSsn )) {
                            preg_match('`Control number\s*</p>\s*<p>\s*([0-9]+)\s*`', $ht, $matches);
                            $lastSsn = $matches[1] ?? null;
                            $useExternalId = $lastSsn ? true : false;
                          }
                        }
                      }
                    }
                    if (empty($lastSsn)) {
                      $pdfType = self::W2S_PDF_FORMAT_CHAPA_GLOBAL;
                      preg_match('`00000([1-9A-Za-z][0-9A-Za-z]+)\s*`', $ht, $matches);
                      $lastSsn = $matches[1] ?? null;
                      $useExternalId = true;
                    }

                }
                $lastSsn = trim($lastSsn);

                if (empty($lastSsn)) {
                  // var_dump($ht);
                  $parsed[$name][$i] = 'No Last Four SSN or SSN# found.';
                  continue;
                }
                $fullName = null;
                if ($pdfType == self::W2S_PDF_FORMAT_STANDARD) {
                  preg_match('`\n<p>(.*)13 Statutory`', $ht, $matches);
                  $fullName = $matches[1] ?? null;;
                  $fullName = trim($fullName);
                  if (empty($fullName)) {
                    $parsed[$name][$i] = 'No employee fullName found.';
                    continue;
                  }
                }

                /** @var User|string $member */
                if ($useExternalId) {
                  $member = self::queryEmployeeByExternalId($query, $lastSsn);
                } else {
                  $member = self::queryEmployeeBySSN($employer, $lastSsn, $fullName);
                }
                if (is_string($member)) {
                    $member = $useExternalId ? $member : self::queryEmployeeBySSN($employer, $lastSsn, $fullName, true);
                    if (is_string($member)) {
                      $parsed[$name][$i] = $member;
                      continue;
                    }
                }

                $id = $member->getId();

                $md5 = substr(md5($ht), 0, 16);
                $newPage = $targetDir . '/' . $member->getId() . '_' . $md5 . '.pdf';
                rename($page, $newPage);

                $att = Attachment::createFromHostedFile($newPage, 'mex_w2s_pdf');
                $att->setCreateBy($member)
                    ->persist();

                $pdfs = Util::meta($member, 'mexW2Pdfs') ?? [];
                $pdfs[] = $att->getId();
                Util::updateMeta($member, 'mexW2Pdfs', false);
                Util::updateMeta($member, [
                    'mexW2Pdfs' => array_unique($pdfs),
                ]);
                $parsed[$name][$i] = 'Attached to the employee ' . $id . ' (' . $member->getSignature() . ')';
            }
        }

        $mailBody = Util::render('@TransferMex/Employer/dd_pdf_parsed.html.twig', [
            'attachment' => $zip,
            'parsed' => $parsed,
        ]);
        $cc = [
          '<EMAIL>',
          '<EMAIL>',
        ];
        if ($by) {
          $cc = array_merge($cc, [$employer->getEmail()]);
        }
        $by = $by ? User::find($by) : $employer;
        if (Util::isLive()) {
          $cc = array_merge($cc, NotificationEmails::findByPlatform(Platform::transferMex()));
        }

        $cc = array_merge($cc, self::findRecipientByEmployer($employer));
        if (!Util::isLive()) {
          Log::debug('CC emails: ', $cc);
        }

        Email::sendWithTemplateToUser($by, Email::TEMPLATE_SIMPLE_LAYOUT, [
            'subject' => 'Direct Deposit W2S Parsing Report',
            'body' => $mailBody,
            '_cc' => $cc,
        ]);

        return $parsed;
    }

    /**
     * @param QueryBuilder $query
     * @param              $id
     * @param              $prefix
     * @param string|null  $fullName required if $fuzzy is not false
     * @param string|bool  $fuzzy true if validate the full name, and 'fullName' if only validate the name leading
     *
     * @return mixed|string
     */
    protected static function queryEmployeeBySSN(User $employer, $ssn, $fullName, $checkWithLastEmployer = false)
    {
        $expr = Util::expr();
        $q = Util::em()->getRepository(EmployeeSSN::class)
                        ->createQueryBuilder('es')
                        ->join('es.employer', 'employer')
                        ->join('es.employee', 'employee')
                        ->where($expr->eq('es.lastFourSsn',':lastFourSsn'));
        if ($checkWithLastEmployer) {
          $q->andWhere($expr->eq('es.lastEmployer', ':employer'));
        } else {
          $q->andWhere($expr->eq('es.employer', ':employer'));
        }
        // var_dump($fullName);
        if (str_contains($fullName, '.')) {
          $fullName = str_replace('.', '', $fullName);
        }

        if ($fullName) {
          $nameList = preg_split('/\s+/', $fullName);
          $fullName = implode('%', $nameList) . '%';
          $firstName = $nameList[0] . '%';
          $lastName = '%' . $nameList[count($nameList) - 1];
          $q->andWhere($expr->orX(
            $expr->like(
              $expr->concat(
                  'employee.firstName',
                  $expr->concat($expr->literal(' '), 'employee.lastName')
              ),
              ':fullName'),
            $expr->like('employee.firstName', ':firstName'),
            $expr->like('employee.lastName', ':lastName')
            ))
            ->setParameter('firstName', $firstName)
            ->setParameter('lastName', $lastName)
            ->setParameter('fullName', $fullName);
        }
        $us = $q->setParameter('lastFourSsn', $ssn)
                ->setParameter('employer', $employer)
                ->distinct()
                ->getQuery()
                ->getResult();
        if (!$us) {
            return 'Unknown employee with ssn ' . $ssn . ( $fullName ? '(' . $fullName . ')' : '');
        }
        if (count($us) > 1) {
            return 'Duplicated employees with the same last four SSN' . ( $fullName ? ' and user name ' . $fullName : '');
        }
        return $us[0]->getEmployee();
    }

    /**
     * @param QueryBuilder $query
     * @param              $id
     * @param              $prefix
     * @param string|null  $fullName required if $fuzzy is not false
     * @param string|bool  $fuzzy true if validate the full name, and 'fullName' if only validate the name leading
     *
     * @return mixed|string
     */
    protected static function queryEmployeeByExternalId(QueryBuilder $query, $id, $prefix = '', $fullName = null, $fuzzy = false)
    {
        $key = $prefix . $id;
        $expr = Util::expr();

        $q = clone $query;
        $q->andWhere('u.title = :eid')
            ->setParameter('eid', $key);

        if ($fuzzy) {
            $q->andWhere($expr->like(
                $expr->concat(
                    'u.firstName',
                    $expr->concat($expr->literal(' '), 'u.lastName')
                ),
                ':fullName'
            ))->setParameter('fullName', $fuzzy === 'fullName' ? ($fullName . '%') : $fullName);
        }

        $us = $q->getQuery()
            ->getResult();
        if (!$us) {
            return 'Unknown employee with the ID ' . $id . ($fullName ? (' (' . $fullName . ')') : '');
        }
        if (count($us) > 1) {
            return 'Duplicated employees with the same ID ' . $key . ' and user name ' . $fullName;
        }
        return $us[0];
    }

    /**
     * @param QueryBuilder $query
     * @param              $id
     * @param              $prefix
     * @param string|null  $fullName required if $fuzzy is not false
     * @param string|bool  $fuzzy true if validate the full name, and 'fullName' if only validate the name leading
     *
     * @return mixed|string
     */
    protected static function queryEmployeeById(QueryBuilder $query, $id, $prefix = '', $fullName = null, $fuzzy = false)
    {
        $key = $prefix . $id;
        $expr = Util::expr();

        $q = clone $query;
        $q->andWhere('u.id = :uid')
            ->setParameter('uid', $key);

        if ($fuzzy) {
            $q->andWhere($expr->like(
                $expr->concat(
                    'u.firstName',
                    $expr->concat($expr->literal(' '), 'u.lastName')
                ),
                ':fullName'
            ))->setParameter('fullName', $fuzzy === 'fullName' ? ($fullName . '%') : $fullName);
        }

        $us = $q->getQuery()
            ->getResult();
        if (!$us) {
            return 'Unknown employee ' . $id . ($fullName ? (' (' . $fullName . ')') : '');
        }
        if (count($us) > 1) {
            return 'Duplicated employees with the same ID ' . $key . ' and user name ' . $fullName;
        }
        return $us[0];
    }

    public static function chechEmployerHasBotmCard(User $employer, $employees, $type, $checkHasBotmCard = true)
    {
        $q = Util::em()->getRepository(UserCard::class)
                           ->createQueryBuilder('uc')
                           ->join('uc.user', 'u')
                           ->join('u.teams', 't')
                           ->join('u.userGroups', 'g')
                           ->join('g.adminConfigs', 'ac')
                           ->join('ac.user', 'e');
       if ($checkHasBotmCard ) {
        $q->where(Util::expr()->like('uc.accountNumber', ':botmCard'))
          ->setParameter('botmCard', '%-%');
       } else {
        $q->where(Util::expr()->notLike('uc.accountNumber', ':botmCard'))
          ->andWhere(Util::expr()->isNotNull('uc.accountNumber'))
          ->setParameter('botmCard', '%-%');
       }

        $useCardList = $q->andWhere(Util::expr()->eq('e.id', ':employerId'))
                          ->setParameter('employerId', $employer->getId())
                          ->distinct()
                          ->select('uc.accountNumber, uc.meta')
                          ->getQuery()
                          ->getArrayResult();
        $accountNumberList = [];
        foreach ($useCardList as $userCard) {
          $accountNumberList[] = $userCard['accountNumber'];
          $meta = Util::s2j($userCard['meta']);
          $olds = isset($meta['oldAccountNumbers']) ? $meta['oldAccountNumbers'] : [];
          $accountNumberList = array_merge($accountNumberList, $olds);
        }
        $field = 'Account Number';
        if ($type == 'NACHA' ) {
          $field = 'accountNumber';
        }
        $checkArray = array_column($employees, $field);
        if (Util::isStaging()) {
          Log::debug('employee', $employees);
          Log::debug('checkArray', $checkArray);
          Log::debug('accountNumberList', $accountNumberList);
        }
        return count(array_intersect($accountNumberList, $checkArray));
    }

    public static function findRecipientByEmployer(User $employer) {
        $res = Util::em()->getRepository(User::class)
            ->createQueryBuilder('u')
            ->join('u.teams', 't')
            ->join('u.config', 'config')
            ->join('config.group', 'g')
            ->join('g.adminConfigs', 'ac')
            ->join('ac.user', 'e')
            ->where(Util::expr()->in('t.name', ':roles'))
            ->andwhere(Util::expr()->eq('e.id', ':employerId'))
            ->setParameter('employerId', $employer->getId())
            ->setParameter('roles', [
                UserTransferMexTrait::getCurrentEmployerAdminRole(),
                Role::ROLE_TRANSFER_MEX_EMPLOYER_ADMIN_ACH
            ])
            ->getQuery()
            ->getResult();
        $emails = [];
        foreach ($res as $item) {
          if (Util::meta($item, 'receiveSystemMessage')) {
            $emails[] = $item->getEmail();
          }
        }
        if (!Util::isLive()) {
          Log::debug('Employer Admin Emails: ', $emails);
        }
        // find the employer admin by sub employer
        $subEmployers = Util::em()->getRepository(EmployerSubCompany::class)
                                  ->createQueryBuilder('esc')
                                  ->join('esc.employer', 'e')
                                  ->where(Util::expr()->eq('esc.subEmployer', ':subEmployer'))
                                  ->setParameter('subEmployer', $employer)
                                  ->getQuery()
                                  ->getResult();
        if (!Util::isLive()) {
            Log::debug('count: ' . count($subEmployers));
            Log::debug('count: ', $subEmployers);
        }
        foreach($subEmployers as $subEmployer) {
          $employer = $subEmployer->getEmployer();
          if (Util::meta($employer, 'receiveSystemMessage') || $employer->getTeamName() === Role::ROLE_TRANSFER_MEX_EMPLOYER) {
            $emails[] = $employer->getEmail();
          }
        }
        if (!Util::isLive()) {
          Log::debug('Employer Admin Emails and subEmployers: ', $emails);
        }
        return $emails;
    }
}
