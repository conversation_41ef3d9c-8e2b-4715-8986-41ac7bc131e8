<?php


namespace TransferMexBundle\Services;

use Carbon\Carbon;
use CoreBundle\Entity\Attachment;
use CoreBundle\Entity\BaseState;
use CoreBundle\Entity\Config;
use CoreBundle\Entity\Country;
use CoreBundle\Entity\ExternalInvoke;
use CoreBundle\Entity\RapidOperationQueue;
use CoreBundle\Entity\State;
use CoreBundle\Entity\Transfer;
use CoreBundle\Exception\FailedException;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Image;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Util;
use PortalBundle\Exception\PortalException;
use Ramsey\Uuid\Uuid;
use SalexUserBundle\Entity\User;
use TransferMexBundle\Entity\Recipient;
use TransferMexBundle\TransferMexBundle;


class IntermexRemittanceService
{
    public const SETTLEMENT_ENTITIES = [
      'NY' => 'Tern WAAS TII',
      'CA' => 'Tern WAAS TCOR',
      'Other' => 'Tern WAAS TLLC'
    ];
    public const BANK_TIME_LIMIT = [
      'MX-0107' => 'Monday to Sunday 07:00-22:00 CST',
      'MX-0108' => 'Monday to Friday 07:00-22:00 CST',
      'MX-0109' => 'Monday to Friday 07:00-22:00 CST',
      'MX-0111' => 'Monday to Friday 07:00-22:00 CST',
      'MX-0112' => 'Monday to Friday 07:00-22:00 CST',
      'MX-0113' => 'Monday to Friday 07:00-22:00 CST',
      'MX-0114' => 'Monday to Friday 07:00-22:00 CST',
      'MX-0115' => 'Monday to Friday 07:00-22:00 CST',
      'MX-0123' => 'Monday to Friday 07:00-22:00 CST',
      'MX-0129' => 'Monday to Friday 07:00-22:00 CST',
      'MX-0138' => 'Monday to Sunday 09:00-19:00 CST',
      'MX-0149' => 'Monday to Sunday 07:00-20:30 CST',
      'MX-0154' => 'Monday to Sunday 07:00-22:00 CST',
      'MX-0155' => 'Monday to Sunday 07:00-22:00 CST',
      'MX-0178' => 'Monday to Sunday 07:00-22:00 CST',
      'MX-0176' => 'Monday to Sunday 07:00-20:30 CST',
      'MX-0200' => 'Monday to Sunday 07:00-22:00 CST',
      'MX-0215' => 'Monday to Friday 07:00-20:30 CST',
      'MX-0217' => 'Monday to Friday 07:00-20:30 CST'
    ];
    public const BANK_TIME_LIMIT_ES = [
      'MX-0107' => 'Lunes - Domingo 07:00-22:00 CST',
      'MX-0108' => 'Lunes - Viernes 07:00-22:00 CST',
      'MX-0109' => 'Lunes - Viernes 07:00-22:00 CST',
      'MX-0111' => 'Lunes - Viernes 07:00-22:00 CST',
      'MX-0112' => 'Lunes - Viernes 07:00-22:00 CST',
      'MX-0113' => 'Lunes - Viernes 07:00-22:00 CST',
      'MX-0114' => 'Lunes - Viernes 07:00-22:00 CST',
      'MX-0115' => 'Lunes - Viernes 07:00-22:00 CST',
      'MX-0123' => 'Lunes - Viernes 07:00-22:00 CST',
      'MX-0129' => 'Lunes - Viernes 07:00-22:00 CST',
      'MX-0138' => 'Lunes - Domingo 09:00-19:00 CST',
      'MX-0149' => 'Lunes - Domingo 07:00-20:30 CST',
      'MX-0154' => 'Lunes - Domingo 07:00-22:00 CST',
      'MX-0155' => 'Lunes - Domingo 07:00-22:00 CST',
      'MX-0178' => 'Lunes - Domingo 07:00-22:00 CST',
      'MX-0176' => 'Lunes - Domingo 07:00-20:30 CST',
      'MX-0200' => 'Lunes - Domingo 07:00-22:00 CST',
      'MX-0215' => 'Lunes - Viernes 07:00-20:30 CST',
      'MX-0217' => 'Lunes - Viernes 07:00-20:30 CST'
    ];
    public const BANK_LENGTH_VERIFY_RULES = [
      'MX-0107'  => [11, 16, 18], // AFIRME DEPOSITOS
      'MX-0108'  => [16, 18], // BAJIO DEPOSITOS
      'MX-0109'  => [11, 16, 18], // BANAMEX DEPOSITOS
      'MX-0111'  => [16, 18], // BANREGIO DEPOSITOS
      'MX-0112'  => [16, 18], // COMPARTAMOS DEPOSITOS
      'MX-0113'  => [11, 16, 18], // HSBC DEPOSITOS
      'MX-0114'  => [16, 18], //  INBURSA DEPOSITOS
      'MX-0115'  => [11, 16, 18], // SANTANDER DEPOSITOS
      'MX-0123'  => [11, 16, 18], // SCOTIABANK DEPOSITOS
      'MX-0129'  => [16, 18], // OTROS BANCOS (ALL BANKS IN MEXICO)
      'MX-0133'  => [10], // CAJERO AUTOMÁTICO BANCOMER
      'MX-0138' => [11, 19], // BANCO AUTOFIN
      'MX-0143' => [], // PAGAPHONE
      'MX-0149' => [10], // BBVA BANCOMER - MOBILE
      'MX-0151' => [10], // UNIPAGOS
      'MX-0154' => [14, 16, 18], // BANCO AZTECA DEPOSITOS
      'MX-0155' => [11,16,18], // BANCOPPEL
      'MX-0176' => [10,16,18], // BANCOMER DEPOSITS
      'MX-0178' => [10,16,18], // BANORTE
      'MX-0200' => [10], // SPIN BY OXXO
      'MX-0205' => [11,16,18],// AFIRME DEPOSITOS_
      'MX-0206' => [16, 18], // BAJIO DEPOSITOS_
      'MX-0207' => [11, 16, 18], // BANAMEX DEPOSITOS_
      'MX-0208' => [16, 18], // BANREGIO DEPOSITOS_
      'MX-0209' => [16, 18], // COMPARTAMOS DEPOSITOS_
      'MX-0210' => [11, 16, 18], // HSBC DEPOSITOS_
      'MX-0211' => [16, 18],  // INBURSA DEPOSITOS_
      'MX-0212' => [11, 16, 18], // SANTANDER DEPOSITOS_
      'MX-0213' => [11, 16, 18], // SCOTIABANK DEPOSITOS_
      'MX-0214' => [11, 16, 18], // OTROS BANCOS_
      'MX-0215' => [10], // MERCADO PAGO
      'MX-0217' => [11, 16, 18], // BANCO BIENESTAR / BANSEFI
      'MX-0229' => [11], // CAJA POPULAR NUESTRA SENORA DE TONAYA
      'MX-0230' => [11], // CAJA SOLIDARIA VALLE DE GUADALUPE
      'MX-0231' => [11], // CAJA SAN JOSE DE CASIMIRO
      'MX-0232' => [11], // CAJA SOLIDARIA JALA
      'MX-0233' => [14], // COOPERATIVA ACREIMEX DEPOSITOS
      'MX-0236' => [10, 12], // BANKAYA
      'MX-0237' => [10, 12], // TECHREO
      'MX-0238' => [16, 18], // FINSUS,
      // EL
      'SA-0017' => [12, 13, 14, 15, 16, 17, 18, 19, 20, 21],
      'SA-0023' => [11],
      'SA-0024' => [14],
      'SA-0028' => [14],
      'SA-008'  => [9],
      'SA-009'  => [10, 11, 12],
      'SA-010'  => [12],
      'SA-02'   => [8, 13],
      'SA-05'   => [10, 11, 12],
      //HN
      'HO-0021' => [10, 11, 12, 14],
      'HO-0022' => [8, 9, 10],
      'HO-0025' => [6, 16],
      'HO-0030' => [8, 9, 10],
      'HO-0031' => [12],
      'HO-0033' => [12],
      'HO-0038' => [12, 21, 22],
      'HO-0039' => [14, 16, 17, 18],
      'HO-0044' => [9, 10, 11, 12, 13, 14],
      'HO-0046' => [12],
      'HO-015'  => [6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20],
      'HO-09'   => [10, 11, 12, 14],
      //Peru
      'PE-0009' => [20],
      'PE-0010' => [13],
      'PE-0011' => [13, 20],
      'PE-0017' => [20],
      'PE-0018' => [20],
      'PE-0020' => [13, 20],
      'PE-0021' => [9],
      'PE-0004' => [10, 15, 18, 20]
      // 'MX-0250'
    ];
    public const BANK_START_VERIFY_RULES = [
      'MX-0229' => [2209], // CAJA POPULAR NUESTRA SENORA DE TONAYA
      'MX-0230' => [2282], // CAJA SOLIDARIA VALLE DE GUADALUPE
      'MX-0231' => [2289], // CAJA SAN JOSE DE CASIMIRO
      'MX-0232' => [2333], // CAJA SOLIDARIA JALA
      'MX-0233' => [2322], // COOPERATIVA ACREIMEX DEPOSITOS
      'SA-0023' => [503]
    ];

    public const TRANSACTION_TYPE_LIST = [
      'cash' => 1,
      'bank' => 3
    ];
    public const ID_TYPE_LIST = [
      [
        'label' => 'Social Security number',
        'value' => 'SS'
      ],
      [
        'label' => "Driver's license",
        'value' => 'DL'
      ],
      [
        'label' => 'Identification Card',
        'value' => 'ID'
      ],
      [
        'label' => 'Residence card',
        'value' => 'RC'
      ],
      [
        'label' => 'Employment Authorization',
        'value' => 'EA'
      ],
      [
        'label' => 'Consular registration',
        'value' => 'MC'
      ],
      [
        'label' => 'Electoral card',
        'value' => 'TE'
      ],
      [
        'label' => 'Passport',
        'value' => 'PASS'
      ],
      [
        'label' => 'Birth certificate',
        'value' => 'AN'
      ],
      [
        'label' => 'National Identification',
        'value' => 'IN'
      ],
      [
        'label' => 'Others',
        'value' => 'OTHER'
      ],
      [
        'label' => 'Identity card',
        'value' => 'CC'
      ],
      [
        'label' => 'New IFE',
        'value' => 'IFEN'
      ],
      [
        'label' => 'Military Card',
        'value' => 'MILE'
      ],
      [
        'label' => 'Nationality Card',
        'value' => 'ZC'
      ],
      [
        'label' => 'Personal Identification Document',
        'value' => 'DPI'
      ]
    ];
    public const ID_TYPE_TDESC = [
      'DL' => 'DRIVER LICENSE',
      'SS' => 'SSN/ITIN',
      'ID'   => 'ID CARD',
      'RC' => 'RESIDENT CARD',
      'EA' => 'EMPLOYMENT AUTHORIZATION',
      'MC' => 'CONSULAR REGISTRATION',
      'TE' => 'ELECTORAL CARD',
      'PASS' => 'PASSPORT',
      'AN' => 'BIRTH CERTIFICATE',
      'IN' => 'NATIONAL IDENTIFICATION',
      'OTHER' => 'OTHER',
      'CC' => 'IDENTITY CARD',
      'IFEN' => 'IFE NEW',
      'MILC' => 'MILITARY CARD',
      'ZC' => 'CITIZENSHIP CARD',
      'DPI' => 'DPI - PERSONAL IDENTIFICATION DOCUMENT'
    ];
    public const ID_INCOME_TYPE_LIST = [
      [
        'label' => '040/1040A Form',
        'value' => '1'
      ],
      [
        'label' => 'Bank Statement',
        'value' => '11'
      ],
      [
        'label' => "Cashier's Check",
        'value' => '13'
      ],
      [
        'label' => '35',
        'value' => 'Tax Refund'
      ],
      [
        'label' => 'Tax Refund Check',
        'value' => '36'
      ],
      [
        'label' => 'W2',
        'value' => '38'
      ]
    ];
    public const FEE_BANK_TRANSFER = 100; // $1.00 USD
    public const FEE_CASH_PICKUP = 300; // $3.00 USD

    public const OTHER_COUNTRY_FEE = [
      'bank' => [
        'SV' => 500 // $5.00 USD
      ],
      'cash' => [
        'SV' => 500 // $5.00 USD
      ]
    ];
    public const COST_BANK_TRANSFER = [
      // Mexico
      'MX'   => 145,
      // Honduras, Guatemala (Central America country) Bank cost is $3.5
      'GT'   => 350,
      'HN'   => 350,
      // Panama (Central America country) Bank cost is $3.75
      'PA'   => 375,
      'SV'   => 375,
      // Peru South Country
      'PE'   => 375
    ]; // Fee charged by Intermex

    public const COST_CASH_TRANSFER = [
      // Honduras, Guatemala (Central America country) Bank cost is $3.5
      'GT'   => 350,
      'HN'   => 350,
      // Panama (Central America country) Bank cost is $3.5
      'PA'   => 375,
      'SV'   => 375,
      // Peru South Country
      'PE'   => 375
    ]; // Fee charged by Intermex
    // MX cast for cash pickup
    public const COST_CASH_PICKUP_LIST  = [
         // Group A
        'MX-0094' => 325, // BANORTE
        'MX-0101' => 325, // BODEGA AURRERA
        'MX-0146' => 325, // CAJA POPULAR LAS HUASTECAS
        'MX-0095' => 325, // CAJA POPULAR MEXICANA
        'MX-0096' => 325, // CHEDRAUI - CARTERA DINAMICA
        'MX-0085' => 325, // PAQUETERIA Y RECARGAS DEL VALLE
        'MX-0214' => 325, // S-MART
        'MX-0099' => 325, // SORIANA
        'MX-0136' => 325, // SUBURBIA
        'MX-0140' => 325, // TRANSFER DIRECTO
        'MX-0120' => 325, // WALDO'S
        'MX-0102' => 325, // WALMART - MEXICO
        'MX-0087' => 325, // WOOLWORTH,
        'MX-0145' => 325, // MODATELAS
        'MX-22'   => 325,// INTERMEX PUEBLA (P)
        'MX-0078' => 325,// INTERMEX PUEBLA (N)
        'MX-0077' => 325,// INTERMEX PUEBLA (L)
        'MX-0105' => 325,// INPAMEX
        'MX-0134' => 325,// FUNDACION DONDE
        'MX-0217' => 325,// FARMACIAS PRODUCTIVAS APPRIZA
        'MX-0074' => 325,// FARMACIAS GUADALAJARA
        'MX-0220' => 325,// FARMACIAS DEL AHORRO DT
        'MX-0097' => 325,// ENVIOS CONFIANZA (ENVICON)
        'MX-0121' => 325,// ELECZION
        'MX-0106' => 325,// COMERCIAL MEXICANA
        'MX-0126' => 325,// CHEDRAUI EFECTIVO
        // Group B
        'MX-0164' => 400, // BANCOPPEL
        'MX-0104' => 400, // BANCOPPEL- O"
        'MX-0156' => 400, // BANCOPPEL_",
        'MX-0083' => 400, // PAGO EXPRESS_
        // Group C $5
        'MX-02'   => 500, // BANCOMER
        'MX-0073' => 500,// ELEKTRA

    ];

    public const RATE_MARK_UP_PARTNER = 0.027; // Partner(TransferMex) and Tern share this revenue, 50% vs 50%
    public const RATE_MARK_UP_PLATFORM = 0; // Tern Platform's revenue. Was 0.01 for some initial transfers

    public static function feeByType($type, $countryCode = null)
    {
        $fee = $type === 'bank' ? self::FEE_BANK_TRANSFER : self::FEE_CASH_PICKUP;
        if ($countryCode) {
          $fee = isset(self::OTHER_COUNTRY_FEE[$type][$countryCode]) ? self::OTHER_COUNTRY_FEE[$type][$countryCode] : $fee;
        }
        return $fee;
    }

    public static function costByType($type, $methodType, $countryCode = 'MX')
    {
        if ($type === 'bank') {
          return isset(self::COST_BANK_TRANSFER[$countryCode]) ? self::COST_BANK_TRANSFER[$countryCode] : 0;
        }
        if ( $countryCode != 'MX') {
          return isset(self::COST_CASH_TRANSFER[$countryCode]) ? self::COST_CASH_TRANSFER[$countryCode] : 0;
        }
        $cashPickupCost = isset(self::COST_CASH_PICKUP_LIST[$methodType]) ? self::COST_CASH_PICKUP_LIST[$methodType] : 350;
        return $cashPickupCost;
    }

    public static function getMinTransferAmount ($type, $methodType, $countryCode = 'MX') {
      $cost = self::costByType($type, $methodType, $countryCode);
      $min = 0;
      if (Util::isStaging()) {
        Log::debug('Cost: ' . $cost);
        Log::debug('Type: ' . $type);
        Log::debug('MethodType: ' . $methodType);
        Log::debug('Country Code: ' . $countryCode);
      }
      if ($type == 'bank') {
        switch ($cost) {
          case 145: $min = 2500;break;
          case 350: $min = 9500;break;
          case 375: $min = 10500; break;
        }
      } else {
        switch ($cost) {
          case 350: $min = 9500;break;
          case 375: $min = 10500; break;
          case 400: $min = 4000;break;
          case 500: $min = 7500;break;
          case 625: $min = 12500;break;
        }
      }
      if (Config::get('intermex_need_more_cost_for_transaction', true)) {
        $min += 3000;
      }
      if (Config::get('intermex_test_mode', false)) {
        $min = 100;
      }
      return $min;
    }

    public static function getPartnerRateMarkup()
    {
        return  self::RATE_MARK_UP_PARTNER;
    }

    public static function getPlatformRateMarkup()
    {
        return self::RATE_MARK_UP_PLATFORM;
    }

    public static function revenueByType($type, $amount, $methodType,  Recipient $rr, $isTransferFree = false)
    {
        $fee = $isTransferFree ? 0 : self::feeByType($type, $rr->getUser()->getCountry()->getIsoCode());
        $cost = self::costByType($type, $methodType, $rr->getUser()->getCountry()->getIsoCode());
        $rate = $rr->getCurrency() == 'USD' ? 0 : self::getPartnerRateMarkup();
        $feeRevenue = $fee - $cost;
        $rateRevenue = $amount * $rate;
        return $feeRevenue + $rateRevenue;
    }

    public static function platformRevenueByType($amount, Transfer $transfer = null)
    {
        $rate = self::getPlatformRateMarkup();
        if ($transfer) {
            $rates = Util::meta($transfer, 'fxRateMarkup');
            if (isset($rates[1])) {
                $rate = $rates[1];
            }
        }
        return $amount * $rate;
    }

    // create transfer wire
    public static function createWire(Transfer $transfer) {
      $api = new IntermexAPI();
      $stateFee = 0;
      $sender = $transfer->getSender();
      $recipient = $transfer->getRecipient();
      $re = $recipient->ensureTransferMexRecipient();
      $preparationID = Uuid::uuid4()->toString();
      if ( $recipient->getMethodsGroup()) {
        $receiver = $recipient->getMethodsGroup()->getPrimaryAdmin();
      } else {
        $receiver = $recipient;
      }
      if ($sender->getCountry() && $sender->getCountry()->isUSA() ) {
        $groupAdmin = $sender;
      } else {
        $groupAdmin = $sender->getPrimaryGroupAdmin();
      }
      $accountNumber = $transfer->getPayoutType() == 'bank' ? ($re->getDecryptedAccountNumber() ?? '') : '';

      $params = [
        'AgSenderCode' => Util::getConfigKey('intermex_ag_sender_code'),
        'PreparationID' => $preparationID,
        'senderId' => $sender->getId(),
        'sndFirstName' => Util::ascii($sender->getFirstName()),
        'sndLast1' => Util::ascii($sender->getLastName()),
        'sndLast2' => Util::ascii(Util::meta($sender, 'secLastName') ?? ''),
        'SndDOB' => Util::formatApiDateTime($sender->getBirthday()),
        'sndAddress' => $groupAdmin->getAddress(),
        'sndCountry' => $groupAdmin->getCountry() ? $groupAdmin->getCountry()->getName() : '',
        'sndState' => Util::ascii($groupAdmin->getState() ? $groupAdmin->getState()->getName() : ''),
        'sndCity' => $groupAdmin->getCity(),
        'sndZip' => $groupAdmin->getZip(),
        'sndPhone' => Util::getPhoneDigitsWithOutCountryIso($sender->getMobilephone()),
        'isCellPhone' => (bool)$sender->getMobilephone(),
        'receiverId' => $receiver->getId(),
        'rcvFirstName' => Util::ascii($receiver->getCompleteFirstName()),
        'rcvLast1' => Util::ascii($receiver->getLastName()),
        'rcvLast2' => Util::ascii(Util::meta($receiver, 'secLastName') ?? ''),
        'rcvAddress' => $recipient->getAddress(),
        'rcvCountry' => $receiver->getCountry() ? $receiver->getCountry()->getName() : '',
        'rcvState' => $recipient->getState() ? Util::removeAccents($recipient->getState()->getName()) : '',
        'rcvCity' => $recipient->getCity(),
        'rcvZip' => $recipient->getZip(),
        'rcvPhone' =>  Util::getPhoneDigitsWithOutCountryIso($receiver->getMobilephone()),
        'RcvDOB' => Util::formatApiDateTime($receiver->getBirthday()),
        'agSenderState' => Util::ascii($groupAdmin->getState() ? strtoupper($groupAdmin->getState()->getName()) : 'GEORGIA'),
        'agSenderCity' => $groupAdmin->getCity() ? strtoupper($groupAdmin->getCity()) : 'ATLANTA',
        'agSenderCountry' => 'UNITED STATES', // always UNITED STATES
        'PayerId' => $re->getIntermexPayerCode(),
        'destCountry' => $receiver->getCountry() ? $receiver->getCountry()->getIntermexCountryId() : '',
        'destState' => $recipient->getState() ? $recipient->getState()->getIntermexStateId() : '',
        'destCity' =>  $recipient->getCity(),
        'tranTypeID' => self::TRANSACTION_TYPE_LIST[$transfer->getPayoutType()],
        'deliveryType' => 'W',
        'oriAmount' => round($transfer->getSendAmount() * 0.01, 2),
        'oriCurrency' => $transfer->getSendCurrency(),
        'charges' => 0,
        'oriToDestExRate' => $transfer->getFxRate(),
        'wireStateFee' => $stateFee,
        'wireTotalAmount' => round(($transfer->getSendAmount() + $stateFee) * 0.01, 2),
        'destAmount' =>  round($transfer->getPayoutAmount() * 0.01, 2),
        'destCurrency' => $transfer->getReceiveCurrency(),
        'agSenderCommission' => 0, // always 0
        'accountNumber' => preg_replace('/[^0-9]/', '', $accountNumber),
        'deptBankName' => $re->getIntermexBankName(),
        'accountType' => $transfer->getPayoutType() == 'bank' ? ($re->getIntermexAccountType() ?? 0) : 0,
        'bankBranchCode' => 0,
        'FundSource' => '',
        "occupation" => '',
        'CreatedBy' => 'sender_id_' . $transfer->getSender()->getId()
      ];

      [$ei, $transaction] = $api->createWire($params);
      if ($ei->isFailed()) {
          $transfer->setEi($ei);
          $message = $ei->getError();
          throw PortalException::temp($message);
      }
      Util::updateMeta($transfer, [
          'transactionPreparationID' => $preparationID,
          'branchId' => Util::meta($re, 'intermexId'),
          'intermexTransaction' => $transaction,
          'updateAgSenderState' => true
      ]);

      $transfer->setStatus(Transfer::STATUS_CONFIRMATION)
              ->setTransferRegion(self::getTransferRegion($groupAdmin->getState()))
              ->persist();
      return $transfer;
    }

    public static function getTransferRegion(BaseState $state) {
      $region = 'Other';
      $settlementRegions = Config::array('intermex_settlement_region');
      if (in_array($state->getAbbr(), array_merge($settlementRegions, ['NY', 'CA', 'WI']))) {
        $region = $state->getAbbr() == 'WI' ? 'NY' : $state->getAbbr();
      }
      return $region;
    }

    public static function addComplianceInfo(Transfer $transfer, $info = [], $isThrowable = false) {
      $api = new IntermexAPI();
      $intermexTransaction = Util::meta($transfer, 'intermexTransaction');
      if (!empty($intermexTransaction['stsComplianceOk']) && $intermexTransaction['stsComplianceOk'] == true) {
        return true;
      }
      if (!empty($intermexTransaction['requireSS']) && $intermexTransaction['requireSS'] && !Util::meta($transfer->getSender(), 'ssValue') && !isset($info['ssValue']) && !$info['ssValue']) {
        return false;
      }
      if (!empty($intermexTransaction['citizenship']) && $intermexTransaction['citizenship'] && !Util::meta($transfer->getSender(), 'citizenship') && !isset($info['citizenship']) && !$info['citizenship']) {
        return false;
      }

      $ss = isset($info['ssValue']) ? $info['ssValue'] : Util::meta($transfer->getSender(), 'ssValue');
      $citizenship = isset($info['citizenship']) ? $info['citizenship'] : Util::meta($transfer->getSender(), 'citizenship');
      try {
        $employer = $transfer->getSender()->getPrimaryGroupAdmin();
        $params = [
          'PreparationID'     =>  Util::meta($transfer, 'transactionPreparationID'),
        ];

        if (!empty($intermexTransaction['requireSndDOB']) && $intermexTransaction['requireSndDOB']) {
          $params['SndDOB'] = Util::formatApiDateTime($transfer->getSender()->getBirthday());
        }
        if (!empty($intermexTransaction['requireRcvDOB']) && $intermexTransaction['requireRcvDOB']) {
          $params['RcvDOB'] = Util::formatApiDateTime($transfer->getRecipient()->getBirthday());
        }
        if (!empty($intermexTransaction['requireWireAddInf']) && $intermexTransaction['requireWireAddInf']) {
          $params['WirePurpose'] = $info['wirePurpose'];
          $params['FundSource'] = $info['fundSource'];
        }
        if (!empty($intermexTransaction['requireEmploymentInfo']) && $intermexTransaction['requireEmploymentInfo']) {
          $params['SndEmployerName'] = $employer->getFullName();
          $params['SndEmployerPhone'] = $employer->getMobilephone();
        }
        if (!empty($intermexTransaction['requireSS']) && $intermexTransaction['requireSS']) {
          $params['SndSsNumber'] = $ss;
        }
        if (!empty($intermexTransaction['requiredNationality']) && $intermexTransaction['requiredNationality']) {
          $params['Citizenship'] = $citizenship;
        }
        [$ei, $transaction] = $api->addComplianceInfo($params);
        if ($ei->isFailed()) {
            $message = $ei->getError();
            throw PortalException::temp($message);
        }
      } catch (\Throwable $exception) {
        // Add Compliance Info error
        $message = 'Add Compliance Info Failed' . $exception->getMessage();
        Log::debug($message);
        empty($info) ? Util::updateMeta($transfer, ['automaticallAddComplianceInfoFailed' => true]) : '';
        return $isThrowable ? throw PortalException::temp($message) : false;
      }
      return true;
    }

    public static function addComplianceFile($params, &$error = null) {
      $api = new IntermexAPI();
      try {
        /** @var ExternalInvoke $ei */
        [$ei] = $api->addComplianceFile($params);
        if ($ei->isFailed()) {
            $message = $ei->getError();
            throw PortalException::temp($message . ' (EI: ' . $ei->getId() . ')');
        }
      } catch (\Throwable $exception) {
        // Add Compliance Info error
        Log::debug('Add Compliance File Failed' . $exception->getMessage());
        $error = $exception->getMessage();
        return false;
      }
      return true;
    }
    // confirm transfer wire
    public static function wireConfirm(Transfer $oldTransfer) {
      $transfer = Transfer::find($oldTransfer->getId());
      if (!in_array($transfer->getStatus(), [
          Transfer::STATUS_CONFIRMATION
      ])) {
          throw PortalException::temp('Invalid transfer status!');
      }

      if ($transfer->getPartnerId()) {
        throw PortalException::temp('The transfer was already confirm!');
      }

      $recipient = $transfer->getRecipient();
      $from = $transfer->getSender();
      if (!$recipient || !$from) {
          throw PortalException::temp('Invalid recipient or sender!');
      }

      $api = new IntermexAPI();
      $transactionPreparationID = Util::meta($transfer, 'transactionPreparationID');
      $params = [
        'preparationID' => $transactionPreparationID,
      ];
      [$ei, $data] = $api->confirmWire($params);

      $transfer->setEi($ei);

      if ($ei->isFailed()) {
          $transfer->setStatus(Transfer::STATUS_ERROR)
          ->setCancelAt(Carbon::now())
          ->setError($ei->getError())
          ->persist();
          throw PortalException::temp($ei->getError());
      }
      $transfer->setPartnerId($data['pinNumber'])
               ->persist();;
      return $transfer;
    }

    public static function wireRelease(Transfer $oldTransfer) {
      $transfer = Transfer::find($oldTransfer->getId());
      if (!in_array($transfer->getStatus(), [
          Transfer::STATUS_CONFIRMATION
      ])) {
          throw PortalException::temp('Invalid transfer status!');
      }

      if (!$transfer->getPartnerId()) {
        throw PortalException::temp('The transfer was not confirm yet!');
      }

      $recipient = $transfer->getRecipient();
      $from = $transfer->getSender();
      if (!$recipient || !$from) {
          throw PortalException::temp('Invalid recipient or sender!');
      }

      $api = new IntermexAPI();
      $params = [
        'pinNumber' => $transfer->getPartnerId(),
      ];
      [$ei, $data] = $api->releaseWire($params);

      $transfer->setEi($ei);

      if ($ei->isFailed()) {
          $transfer->setStatus(Transfer::STATUS_ERROR)
          ->setCancelAt(Carbon::now())
          ->setError($ei->getError())
          ->persist();
          throw PortalException::temp($ei->getError());
      }

      $transfer->setSendAt(new \DateTime())
               ->setStatus(Transfer::STATUS_CREATED)
               ->persist();
      Util::updateMeta($transfer, ['wireId' => $data['wireId']]);
      // send slack message after create a intermemx
      $context = self::getTransferContext($transfer);
      $msg = 'Created Intermex Transfer';
      if (Data::has('process_payout_queue_' . $transfer->getId())) {
          $msg .= ' *from the payout queue*';
      }
      $date =  Util::formatDateTime(Carbon::now(), Util::DATE_FORMAT, 'America/New_York');
      $dailyAmountKey = 'daily_transfer_amount_' . $transfer->getSender()->getId() . '_' . $date;
      $dailyAmount = Data::get($dailyAmountKey) ?? 0;
      Data::set($dailyAmountKey,  $dailyAmount + $transfer->getSendAmount(), true,  24 * 3600);
      $month =  Util::formatDateTime(Carbon::now(), Util::DATE_FORMAT_MONTH, 'America/New_York');
      $monthlyAmountKey = 'monthly_transfer_amount_' . $transfer->getSender()->getId() . '_' . $month;
      $monthlyAmount = Data::get($monthlyAmountKey) ?? 0;
      Data::set($monthlyAmountKey,  $monthlyAmount + $transfer->getSendAmount(), true,  31 * 24 * 3600);

      SlackService::tada($msg, $context);
      // self::updateTransfer($transfer);
      return $transfer;
    }

    public static function getTransferContext(Transfer $transfer)
    {
        return [
            'transfer' => $transfer->getId(),
            'employer' => $transfer->getSender()->getPrimaryGroupName(),
            'sender' => $transfer->getSender()->getSignature(),
            'type' => $transfer->getPayoutType(),
            'method' => $transfer->getPayoutMethodType(),
            'amount' => Money::formatWhen($transfer->getSendAmount(), $transfer->getSendCurrency(), true),
        ];
    }

    public static function getDailyRate($force = false, Country $country = null)
    {
        if (!$country) {
          $country = Country::findByCode('MX');
        }
        $intermexCountryId = $country->getIntermexCountryId();
        $args = [
          'intermexCountryId' => $intermexCountryId
        ];
        if ($force) {
            Data::del(Data::key(__METHOD__, $args));
        }
        return Data::callback(__METHOD__, $args, function () use ($intermexCountryId) {
            $api = new IntermexAPI();
            /** @var ExternalInvoke $ei */
            $params = [
              'PartnerId' => Util::getConfigKey('intermex_partner'),
              'LanguageId' => 1,
            ];
            $data = Data::getArray('IntermexCountriesList');
            if (empty($data)) {
              [$ei, $data] = $api->getCountries($params);
              if ($ei && $ei->isFailed()) {
                  $message = 'Sorry that the operation failed due to an error (code: 15). We are investigating. Please reach out to our support if it failed constantly.';
                  throw PortalException::create($message);
              }
              Data::setArray('IntermexCountriesList', $data, 3600);
            }
            $exRate = 0;
            foreach($data as $intermexCountry) {
              if ($intermexCountry['id'] == $intermexCountryId) {
                $exRate = $intermexCountry['exRate'];
              }
            }
            if (!$exRate) {
              $message = 'Sorry that the operation failed due to an error (code: 15). We are investigating. Please reach out to our support if it failed constantly.';
              throw PortalException::create($message);
            }
            return $exRate * (1 - self::getPartnerRateMarkup() - self::getPlatformRateMarkup());
        }, 3600);
    }

    public static function getDailyRateV2 (Recipient $recipient = null, $force = false)
    {
      $args = [
        'payerCode' => $recipient ? $recipient->getIntermexPayerCode() : Util::getConfigKey('default_intermex_payer_code')
      ];
      $payerCode = $recipient ? $recipient->getIntermexPayerCode() : Util::getConfigKey('default_intermex_payer_code');
      $country = $recipient ? $recipient->getUser()->getCountry() : Country::findByCode('MX');
      if ($force) {
        Data::del(Data::key(__METHOD__, $args));
      }
      if ($recipient && $country->getCurrency() == 'USD') {
        return 1;
      }
      return Data::callback(__METHOD__, $args, function () use ($payerCode, $country) {
        $api = new IntermexAPI();
        /** @var ExternalInvoke $ei */
        $params = [
          'PartnerId' => Util::getConfigKey('intermex_partner'),
          'LanguageId' => 1,
          'PayerCode'  => $payerCode,
          'DestCountry' => $country->getName(),
          'DestCurrency' => $country->getCurrency(),
          'OriCurrency' => 'USD',
          'TransactionType' => 3
        ];

        /** @var ExternalInvoke $ei  */
        [$ei, $data] = $api->getDailyRate($params);
        if ($ei && $ei->isFailed()) {
            $message = 'Sorry that the operation failed due to ' . $ei->getError(). '. We are investigating. Please reach out to our support if it failed constantly.';
            throw PortalException::create($message);
        }
        $exRate = isset($data['exRate']) ? $data['exRate'] : 0;
        if (!$exRate) {
          $message = 'Sorry that the operation failed due to get rate error. We are investigating. Please reach out to our support if it failed constantly.';
          throw PortalException::create($message);
        }
        $currency = $country->getCurrency();

        // store intermex rate in redis
        Data::set('intermex_real_rate' . '_' . $currency , $exRate);
        return round($exRate * (1 - self::getPartnerRateMarkup() - self::getPlatformRateMarkup()), 4);
      }, 3600);
    }

    public static function getPayoutOptions(?User $recipient = null)
    {
        $methodGroup = $recipient?->getMethodsGroup();
        if ($methodGroup) {
          if (Util::isStaging()) {
            Log::debug('Has method group');
            Log::debug($methodGroup->getUserCount());
          }
          $recipients = $methodGroup->getUsers();
        } else {
          $recipients = [$recipient];
        }

        $rate = 0;
        $defaultMin = TransferMexBundle::getIntermexMinTransferAmount();
        $minAmount = [
          'bank' => $defaultMin,
          'cash' => $defaultMin,
        ];
        $intermexRecipient = null;
        foreach ($recipients as $item) {
            if (!$item) {
                continue;
            }
          if (Util::isStaging()) {
            Log::debug($item->getId());
            Log::debug('===== Recipient Id======');
          }
          $rr = $item->ensureTransferMexRecipient();
          if (!$rr || !$rr->getIsIntermex()) {
            continue;
          }
          $intermexRecipient = $rr;
          $type = $rr->getPayoutType() === 'Cash Pickup' ? 'cash' : 'bank';
          $minAmount[$type] = self::getMinTransferAmount($type, $rr->getPayoutMethodType(), $item->getCountry()->getIsoCode());
        }

        $error = 'Failed to get the Intermex exchange rate.';
        try {
          if (Config::get('use_intermex_rate_version_1', 1)) {
            $rate = self::getDailyRate(true, $recipient?->getCountry());
          } else {
            $rate = self::getDailyRateV2($intermexRecipient, true);
          }
          if (Util::isStaging()) {
            Log::debug('The intemrex rate is' . $rate);
          }
        } catch (\Exception $e) {
            $error = 'Failed to query Intermex bank rate: ' . $e->getMessage();
            Log::warn($error);
        }
        $currency = $intermexRecipient ? $intermexRecipient->getUser()->getCountry()->getCurrency() : 'MXN';
        if (!$rate || $rate <= 0.1) {
            $rate = Data::get('intermex_rate_currently' . '_' . $currency);
        }
        if (!$rate || $rate <= 0.1) {
            throw PortalException::create($error);
        }

        Data::set('intermex_rate_currently' . '_' . $currency, $rate);

        return [
            'minBankAmount' => $minAmount['bank'],
            'minCashAmount' => $minAmount['cash'],
            'maxAmount' => TransferMexBundle::getIntermexMaxTransferAmount(),
            'dailyRate' => $rate * 1.0,
            'intermex_real_rate' => Data::get('intermex_real_rate' . '_' . $currency)
        ];
    }

    public static function cancelTransactions(Transfer $transfer, $reasonId = 13) {
      if (!in_array($transfer->getStatus(), [
          Transfer::STATUS_PENDING,
          Transfer::STATUS_CREATED,
          Transfer::STATUS_CONFIRMATION,
      ])) {
          throw new FailedException('This transfer is not cancelable!');
      }

      if ($transfer->getPartner() === Transfer::PARTNER_INTERMEX && $transfer->getPartnerId()) {
        try {
            self::updateTransfer($transfer);
            $oldStatus = $transfer->getStatus();
            if ($oldStatus == Transfer::STATUS_CANCELED) {
              return $transfer;
            }
            $api = new IntermexAPI();
            $params = [
              'pinNumber' => $transfer->getPartnerId(),
              'cancelReasonID' => $reasonId,
              'cancelBy' => 'sender_id_' . $transfer->getSender()->getId()
            ];

            /** @var ExternalInvoke $ei */
            [$ei, $data] = $api->cancelWire($params);

            if ($ei->isFailed()) {
                $error = $ei->getError();
                Log::debug('Intermex response', $data);
                if (isset($data['errorCode']) && $data['errorCode'] == '1317') {
                  $transfer->setStatus(Transfer::STATUS_CANCELED)
                          ->setCancelAt($transfer->getCancelAt() ?? Carbon::now())
                          ->persist();
                } else if (isset($data['errorCode']) && in_array($data['errorCode'], ['10854', '11260', '1318'])) {
                  $error = $data['errorMessage'];
                  throw PortalException::temp($error);
                } else {
                  $msg = 'Sorry that the operation failed due to an error (code: 17). We are investigating. Please reach out to our support if it failed constantly.';
                  throw PortalException::temp($msg);
                }
            }

            $transfer->setStatus(Transfer::STATUS_CANCELED)
                     ->setCancelAt($transfer->getCancelAt() ?? Carbon::now())
                     ->persist();

            // remove the debug flag
            if (Util::isStaging() && Util::meta($transfer, 'simulateCashPickup')) {
              Util::updateMeta($transfer, [
                'simulateCashPickup' => false
              ]);
            }
            if (Util::meta($transfer, 'wireId')) {
              self::updateTransfer($transfer);
            } else {
              TransactionService::updateTransactionByTransfer($transfer);
            }

            if (in_array($oldStatus, [
                  Transfer::STATUS_CREATED,
                  Transfer::STATUS_PENDING,
                  Transfer::STATUS_QUEUED,
                  Transfer::STATUS_PROCESSING,
                  Transfer::STATUS_COMPLETED,
                  Transfer::STATUS_CONFIRMATION,
            ]) && $transfer->getStatus() === Transfer::STATUS_CANCELED && !$transfer->isRefunded()) {
                RapidService::updateBalanceBy($transfer->getSenderCard(), $transfer->getTotalAmount(),
                    'rapyd_transfer_reverse', $transfer->getId(), 'Cancelled transfer', $transfer, true);
                $transfer->setRefunded(true);

                $context = self::getTransferContext($transfer);
                $context['canceled_by'] = Util::field(Util::user(), 'signature');
                SlackService::alert('The transfer was cancelled.', $context);
            }
        } catch (\Exception $exception) {
            $msg =  $exception->getMessage();
            throw PortalException::temp($msg);
        }
      } else {
          $oldStatus = $transfer->getStatus();
          if ($transfer->isDeducted() && !$transfer->isRefunded()) {
              RapidService::updateBalanceBy($transfer->getSenderCard(), $transfer->getTotalAmount(),
                  'rapyd_transfer_reverse', $transfer->getId(), 'Cancelled transfer', $transfer, true);
              $transfer->setRefunded(true);

              $context = self::getTransferContext($transfer);
              $context['canceled_by'] = Util::field(Util::user(), 'signature');
              SlackService::alert('The ' . strtolower($oldStatus) . ' transfer was cancelled and refunded to the member.', $context);
          }
          // remove the debug flag
          if (Util::isStaging() && Util::meta($transfer, 'simulateCashPickup')) {
            Util::updateMeta($transfer, [
              'simulateCashPickup' => false
            ]);
          }
          $transfer->setStatus(Transfer::STATUS_CANCELED)
                   ->persist();
          TransactionService::updateTransactionByTransfer($transfer);
      }

      return $transfer;
  }

  protected static function validatePayout(Transfer $transfer)
  {
      if (!$transfer->getPartnerId()) {
          throw PortalException::create('The transfer has not been initialized yet!');
      }
  }

  public static function updateTransferByPayout($data, Transfer $transfer = null, $initiative = false, $isCancel = false)
  {
      if (empty($data)) {
        return $transfer;
      }
      $statusInfo = $data[0];
      $receiveAt = null;
      $pinNumber = null;
      $statusId = null;
      if (isset($statusInfo['wireStatusId'])) {
        $pinNumber = $statusInfo['pinNumber'];
        $statusId = $statusInfo['wireStatusId'];
      }
      if ($transfer === null) {
          $transfer = Transfer::findByPartnerId($pinNumber, Transfer::PARTNER_INTERMEX);
          if (!$transfer) {
              return $transfer;
          }
      }
      if (!isset(Transfer::INTERMEX_STATUS_LIST[$statusId])) {
        Log::debug('The status is error for the intermex wire, status ID: '  . $statusId);
        return $transfer;
      }
      $oldStatus = $transfer->getStatus();
      $currentStatus = Config::get('use_intermex_status_version_1', 1) && Util::isLive() ? Transfer::INTERMEX_STATUS_TEST[$statusId] : Transfer::INTERMEX_STATUS_LIST[$statusId];
      if ( $currentStatus=== Transfer::STATUS_COMPLETED && !$transfer->getReceiveAt() ) {
          $receiveAt = Carbon::now();
      }

      $transfer->setStatus($currentStatus)
          ->setReceiveAmount($transfer->getPayoutAmount())
          ->setReceiveAt($receiveAt ? Util::toUTC($receiveAt) : $transfer->getReceiveAt())
          ->setSyncAt(new \DateTime());

      if ($transfer->getStatus() === Transfer::STATUS_CANCELED && !$transfer->getCancelAt()) {
          $transfer->setCancelAt(Carbon::now());
          if ($transfer->isIntermex()) {
            Util::updateMeta($transfer, [
              'cancelByIntermex' => true
            ]);
          }
      }

      Util::updateMeta($transfer, [
          'IntermexStatus' => $statusInfo,
          'IntermexSyncAt' => Carbon::now()->format('c'),
      ]);

      TransactionService::updateTransactionByTransfer($transfer);

      if ($oldStatus !== Transfer::STATUS_COMPLETED && $currentStatus === Transfer::STATUS_COMPLETED) {
           $context = self::getTransferContext($transfer);
           if (in_array($oldStatus, [
              Transfer::STATUS_ERROR,
              Transfer::STATUS_CANCELED
          ])) {
              SlackService::alert('`' . $oldStatus . '` payout becomes completed!', $context);

              try {
                RapidService::updateBalanceBy($transfer->getSenderCard(), -$transfer->getTotalAmount(),
                  'rapyd_transfer', $transfer->getId(), 'Transfer in ' . $transfer->getId(), $transfer, true);
                SlackService::info('Deducted the transfer amount from the member.', $context);
              } catch (\Exception $e) {
                SlackService::alert('Add to operation queue since the error: ' . $e->getMessage(), $context, SlackService::GROUP_DEV);
                RapidOperationQueue::add('update_balance_rapyd_transfer_' . $transfer->getId())
                    ->setUser($transfer->getSender())
                    ->setUserCard($transfer->getSenderCard())
                    ->setTransfer($transfer)
                    ->setAgentNumber(null)
                    ->setType('rapyd_transfer')
                    ->setAmount(-$transfer->getTotalAmount())
                    ->setComment('Transfer in ' . $transfer->getId() . ': ' . $e->getMessage())
                    ->queue();
              }
          } else {
              $msg = 'Completed ' . $transfer->getPayoutTypeName() . ' (Intermex)';
              if (Data::has('process_payout_queue_' . $transfer->getId())) {
                  $msg .= ' *from the payout queue*';
              }
              TransferService::updatePromo($transfer);
              SlackService::tada($msg, $context);
          }
      } else if (!$initiative && in_array($oldStatus, [
              Transfer::STATUS_CREATED,
              Transfer::STATUS_PENDING,
              Transfer::STATUS_QUEUED,
              Transfer::STATUS_PROCESSING,
              Transfer::STATUS_COMPLETED,
          ]) && in_array($currentStatus, [
              Transfer::STATUS_ERROR,
              Transfer::STATUS_CANCELED
          ]))
      {
          $context = self::getTransferContext($transfer);
          SlackService::alert('`' . $oldStatus . '` payout becomes `' . $currentStatus . '`!', $context);

          if (!$transfer->isRefunded()) {
              $refunded = RapidService::updateBalanceBy(
                  $transfer->getSenderCard(),
                  $transfer->getTotalAmount(),
                  'rapyd_transfer_reverse', $transfer->getId(),
                  $currentStatus . ' transfer', $transfer,
                  true
              );
              $context['refunded'] = $refunded;
              $transfer->setRefunded(true);
              $msg = 'Refunded the transfer amount to the member';
              SlackService::info($msg, $context);
              // update amount limit
              $date =  Util::formatDateTime($transfer->getSendAt(), Util::DATE_FORMAT, 'America/New_York');
              $dailyAmountKey = 'daily_transfer_amount_' . $transfer->getSender()->getId() . '_' . $date;
              $dailyAmount = Data::get($dailyAmountKey) ?? 0;
              $month =  Util::formatDateTime($transfer->getSendAt(), Util::DATE_FORMAT_MONTH, 'America/New_York');
              $monthlyAmountKey = 'monthly_transfer_amount_' . $transfer->getSender()->getId() . '_' . $month;
              $monthlyAmount = Data::get($monthlyAmountKey) ?? 0;
              if ($dailyAmount) {
                Data::set($dailyAmountKey,  $dailyAmount - $transfer->getSendAmount(), true,  24 * 3600);
              }
              if ($monthlyAmount) {
                Data::set($monthlyAmountKey,  $monthlyAmount - $transfer->getSendAmount(), true,  31 * 24 * 3600);
              }
              if ($refunded) {
                  TransferHealthService::saveRefundCache($transfer);
              }
          }
          // check the fee promo and return
          TransferService::updatePromo($transfer, true);
      }
      return $transfer;
  }

  public static function updateTransfer(Transfer $transfer)
  {
      self::validatePayout($transfer);
      /** @var ExternalInvoke $ei */
      $api = new IntermexAPI();
      $params = [
        'pinNumbers' => [$transfer->getPartnerId()]
      ];
      [$ei, $data] = $api->getWireStatus($params);
      if ($ei && $ei->isFailed()) {
          throw PortalException::create($ei->getError(), alert: false);
      }
      // add debug code
      if (Util::isStaging() && (Util::meta($transfer, 'simulateCashPickup') || in_array($transfer->getPartnerId(), Config::array('intermex_debug_pinnumbers')))) {
        $data = [
          [
            'pinNumber' => $transfer->getPartnerId(),
            'control' => 298003453,
            'wireStatusId' => 4,
            'wireStatusDescription' => 'Ready To Send',
            'cancelationReasonId' => null,
            'cancelationReasonDescription' => null,
            'dateStatus' => '2024-10-16T22:22:54.073',
            'branchCode' => null,
            'wireFound' => true
          ]
        ];
      }
      return self::updateTransferByPayout($data, $transfer);
  }

  public static function getTransactionReceipt(User $user) {
      if ($user->getCountry() && $user->getCountry()->isUSA() ) {
        $groupAdmin = $user;
      } else {
        $groupAdmin = $user->getPrimaryGroupAdmin();
      }
      $api = new IntermexAPI();
      $params = [
        'OriCountry'       => $groupAdmin->getCountry() ? $groupAdmin->getCountry()->getName() : '',
        'StateName'        => $groupAdmin->getState() ? $groupAdmin->getState()->getName() : '',
      ];
      try {
        [$ei, $data] = $api->getWireDisclaimer($params, $user);
        if ($ei->isFailed()) {
          return null;
        }
      } catch(\Exception $exception) {
        return null;
      }
      if (!Util::isLive()) {
          Log::debug('Receipt: ', $data);
      }
      return $data;
  }

  public static function getReasonList() {
      return [
        // [
        //   'value' => '2',
        //   'label' => ucfirst('WRONG AGENCY')
        // ],
        // [
        //   'value' =>  '3',
        //   'label' => ucfirst('CHANGE OF DESTINATION')
        // ],
        // [
        //   'value' => '5',
        //   'label' => ucfirst('WIRE OVER THE LIMIT TO SEND (CS DO NOT USE)')
        // ],
        // [
        //   'value' => '7',
        //   'label' => ucfirst('MISSING AGENCY AUTHORIZATION')
        // ],
        // [
        //   'value' => '9',
        //   'label' => ucfirst('REJECTED BY PAYER - WRONG ACCOUNT NUMBER')
        // ],
        [
          'value' => '13',
          'label' => ucfirst('WIRE NO LONGER NEEDED')
        ],
        [
          'value' => '14',
          'label' => ucfirst('DUPLICATED WIRES')
        ],
        [
          'value' => '15',
          'label' => ucfirst('CHANGE OF BANK/PAYER')
        ],
        // [
        //   'value' => '17',
        //   'label' => ucfirst('INCOMPLETE KYC/DOCUMENTS')
        // ],
        [
          'value' => '19',
          'label' => ucfirst('CHANGE / MISSPELLED BENEFICIARY')
        ],
        [
          'value' => '21',
          'label' => ucfirst('CHANGE OF AMOUNT')
        ],
        // [
        //   'value' => '23',
        //   'label' => ucfirst('EXPIRED WIRE-TRANSFERS')
        // ],
        // [
        //   'value' => '27',
        //   'label' => ucfirst('CASHDIRECT VOID')
        // ],
        // [
        //   'value' => '28',
        //   'label' => ucfirst('SYSTEM TESTING')
        // ],
        [
          'value' => '29',
          'label' => ucfirst('UNABLE TO COLLECT WIRE AT PAYER')
        ],
        [
          'value' => '32',
          'label' => ucfirst('CHANGE OF BENEFICIARY (AUTOMATIC)')
        ],
        // [
        //   'value' => '39',
        //   'label' => ucfirst('REJECTED BY PAYER')
        // ],
        // [
        //   'value' => '40',
        //   'label' => ucfirst('REJECTED BY PAYER - TC OUT OF RANGE')
        // ],
        // [
        //   'value' => '42',
        //   'label' => ucfirst('PAYER CANCELLATION')
        // ],
        // [
        //   'value' => '44',
        //   'label' => ucfirst('REJECTED CHANGE OF BENEFICIARY')
        // ],
        // [
        //   'value' => '45',
        //   'label' => ucfirst('PAYMENT REVERSAL')
        // ],
        // [
        //   'value' => '47',
        //   'label' => ucfirst('REJECTED BY PAYER - COMPLIANCE')
        // ],
        // [
        //   'value' => '49',
        //   'label' => ucfirst('WEBAGENT - CHARGE BACK')
        // ],
        // [
        //   'value' => '50',
        //   'label' => ucfirst('WEBAGENT - ERROR')
        // ],
        // [
        //   'value' => '51',
        //   'label' => ucfirst('WEBAGENT CHANGE OF BENEFICIARY')
        // ],
        // [
        //   'value' => '52',
        //   'label' => ucfirst('WEBAGENT - REJECTED BY CYBERSOURCE')
        // ],
        // [
        //   'value' => '53',
        //   'label' => ucfirst('WEBAGENT - CYBERSOURCE NOT AVAILABLE')
        // ],
        // [
        //   'value' => '54',
        //   'label' => ucfirst('WEBAGENT- COLLECTION FAIL')
        // ],
        // [
        //   'value' => '55',
        //   'label' => ucfirst('CHANGE OF PAYMENT METHOD')
        // ],
        // [
        //   'value' => '57',
        //   'label' => ucfirst('CARD DIRECT - COLLECTION FAIL')
        // ],
        // [
        //   'value' => '58',
        //   'label' => ucfirst('REJECTED BY KBA')
        // ],
        [
          'value' => '60',
          'label' => ucfirst('SUSPECTED FRAUD')
        ],
        // [
        //   'value' => '61',
        //   'label' => ucfirst('REJECTED BY INTERMEX COMPLIANCE')
        // ],
        // [
        //   'value' => '62',
        //   'label' => ucfirst('REJECTED BY PAYER - WRONG DESTINATION CURRENCY')
        // ],
        // [
        //   'value' => '63',
        //   'label' => ucfirst('REJECTED BY PAYER - EXCEEDED AMOUNT LIMITS')
        // ],
        // [
        //   'value' => '64',
        //   'label' => ucfirst('CAPTURE ERROR')
        // ],
        [
          'value' => '65',
          'label' => ucfirst('DECIDE TO USE ANOTHER SERVICE')
        ],
        // [
        //   'value' => '66',
        //   'label' => ucfirst('SENDER DID NOT PAY THE WIRE')
        // ],
        // [
        //   'value' => '67',
        //   'label' => ucfirst('WA-REJECTED FLP - CARD IS NOT UNDER REMITTER NAME')
        // ],
        // [
        //   'value' => '68',
        //   'label' => ucfirst('WA-REJECTED FLP - CHARGEBACK')
        // ],
        // [
        //   'value' => '69',
        //   'label' => ucfirst('WA-REJECTED FLP- DUPLICATED ACCOUNT')
        // ],
        // [
        //   'value' => '70',
        //   'label' => ucfirst('WA-REJECTED FLP- FRAUD TREND')
        // ],
        // [
        //   'value' => '71',
        //   'label' => ucfirst('WA-REJECTED FLP- INCONSISTENT INFORMATION')
        // ],
        // [
        //   'value' => '72',
        //   'label' => ucfirst('WA-REJECTED FLP- INVALID DOCUMENTATION')
        // ],
        // [
        //   'value' => '73',
        //   'label' => ucfirst('WA-REJECTED FLP- POSSIBLE IDENTITY THEFT')
        // ],
        // [
        //   'value' => '74',
        //   'label' => ucfirst('WA-REJECTED FLP- REMITTER OUT OF THE TERRITORY')
        // ],
        // [
        //   'value' => '75',
        //   'label' => ucfirst('WA-REJECTED FLP- SEND ON BEHALF OF OTHER PERSON')
        // ],
        // [
        //   'value' => '76',
        //   'label' => ucfirst('WA-REJECTED FLP- REMITTER /RECEIVER IS A MINOR')
        // ],
        // [
        //   'value' => '77',
        //   'label' => ucfirst('WA-REJECTED FLP- UNUSUAL BEHAVIOR')
        // ]
      ];
  }

  public static function getPayerByType (Country $country = null, State $state = null, $type = 'cash', $language = 'us') {
    $api = new IntermexAPI();
    $list = [];
    if ($type == 'cash') {
      $params = [
        'CountryId' => $country->getIntermexCountryId(),
        'StateId'   => $state->getIntermexStateId(),
        'DeliveryTypeId' => 'W',
        'RecordsByPage'  => 9999,
        'LanguageId' => 1
      ];
      [$ei, $data] = $api->getPayers($params);
      if ($ei && $ei->isFailed()) {
          throw PortalException::create($ei->getError());
      }
      foreach ($data['payers'] as $item) {
        $hours = '';
        if ($language != 'es') {
          $hours = isset(self::BANK_TIME_LIMIT[trim($item['payerCode'])]) ? self::BANK_TIME_LIMIT[trim($item['payerCode'])] : '';
        } else {
          $hours = isset(self::BANK_TIME_LIMIT_ES[trim($item['payerCode'])]) ? self::BANK_TIME_LIMIT_ES[trim($item['payerCode'])] : '';
        }
        $list[] = [
          'payerCode' => trim($item['payerCode']),
          'payerName' => trim($item['payerName']),
          'max'  => $item['maxDollarsLimit'] * 1,
          'city' => '',
          'address' => '',
          'phone' => '',
          'hours' => $hours,
          'hoursWeekend' => '',
          'type' => 'Cash Pickup'
        ];
      }
      self::updateCachedFilteredMethodTypes($list, 'cash');
    } else if ($type == 'bank') {
      if (!$country) {
        $country = Country::findByCode('MX');
      }
      $params = [
        'DestCountryId' => $country->getIntermexCountryId(),
        'DeliveryTypeId' => 'W',
        'DestCurrency'  => $country->getCurrency(),
        'PageSize'  => 9999,
        'LanguageId' => 1,
        'OriCurrency' => 'USD'
      ];
      [$ei, $data] = $api->getBanks($params);
      if ($ei && $ei->isFailed()) {
          throw PortalException::create($ei->getError());
      }
      foreach ($data['banks'] as $item) {
        if (in_array(trim($item['agPayerCode']), Config::array('disable_intermex_bank_payer'))) {
          continue;
        }
        if (Util::startsWith(trim($item['agPayerName']), 'OTROS')) {
          continue;
        }
        $hours = '';
        if ($language != 'es') {
          $hours = isset(self::BANK_TIME_LIMIT[trim($item['agPayerCode'])]) ? self::BANK_TIME_LIMIT[trim($item['agPayerCode'])] : '';
        } else {
          $hours = isset(self::BANK_TIME_LIMIT_ES[trim($item['agPayerCode'])]) ? self::BANK_TIME_LIMIT_ES[trim($item['agPayerCode'])] : '';
        }
        $accountTypes = $item['accountTypes'];
        if (in_array(trim($item['agPayerCode']), ['MX-0215', 'MX-0200'])) {
          $accountTypes = [
            [
              "id" => 0,
              "name" => $language != 'es' ? "Associated Mobile Number" : "Número de celular asociado"
            ]
          ];
        }
        $list[] = [
          'payerId'   => trim($item['agPayerId']),
          'payerCode' => trim($item['agPayerCode']),
          'payerName' => trim($item['bankName']),
          'bankName' => trim($item['bankName']),
          'accountType' => $accountTypes,
          'max'  => TransferMexBundle::getIntermexMaxTransferAmount(),
          'city' => '',
          'address' => '',
          'phone' => '',
          'hours' => $hours,
          'hoursWeekend' => '',
          'type' => 'Bank Deposit'
        ];
      }
      self::updateCachedFilteredMethodTypes($list, 'bank');
    }

    return $list;
  }

  public static function getCountryList() {
    $api = new IntermexAPI();
    $params = [
      'LanguageId' => 1
    ];
    [$ei, $data] = $api->getCountry($params);
    if ($ei && $ei->isFailed()) {
        throw PortalException::create($ei->getError());
    }
    return $data;
  }

  public static function generateForm1025(Transfer $transfer)
  {
      $targetDir = Util::ensureFile(Util::secureDir('mex_intermex_form_1025'), true);
      $roundStr = Util::randString();
      $path = $targetDir . $transfer->getId(). $roundStr .'.png';
      $url = TransferMexBundle::getAdminDomain() . '/t/cron/mex/intermex/form-1025/' . $transfer->getId();
      Image::captureWebsite($url, $path, 1488, delay: 1);
      return $path;
  }

  public static function createForm1025(Transfer $transfer) {
    if (Util::meta($transfer, 'hasCreateIntermexForm1025')) {
      return Attachment::find(Util::meta($transfer, 'hasCreateIntermexForm1025'));
    }
    $path = self::generateForm1025($transfer);
    if (file_exists($path)) {
      $att = Attachment::createFromHostedFile($path, 'mex_intermex_form_1025');
      $att->setCreateBy($transfer->getSender())
          ->persist();
      Util::updateMeta($transfer, ['hasCreateIntermexForm1025' => $att->getId()]);
      return $att;
    }
    Log::debug('Create Intermex Form 1025 failed for the transfer: ' . $transfer->getId());
    return false;
  }

  public static function checkBankNumber($bankNumber, $payerCode) {
    if (isset(self::BANK_LENGTH_VERIFY_RULES[$payerCode])) {
      $lengthRules = self::BANK_LENGTH_VERIFY_RULES[$payerCode];
      if (!in_array(strlen($bankNumber), $lengthRules)) {
        throw PortalException::temp('The length of the card number is incorrect, please write a valid character (' . implode(',', $lengthRules) . '), remember to use only numbers and avoid spaces!');
      }
    }
    if (isset(self::BANK_START_VERIFY_RULES[$payerCode])) {
      $startRules = self::BANK_START_VERIFY_RULES[$payerCode];
      $error = true;
      foreach ($startRules as $startRule) {
        if (Util::startsWith($bankNumber, $startRule)) {
          $error = false;
        }
      }
      if ($error) {
        throw PortalException::temp('Please write a valid character (should start with' . implode(',', $startRules) . '), remember use only numbers and avoid spaces!');
      }
    }
  }

  public static function updateCachedFilteredMethodTypes($list = [], $type = 'bank') {
    $key = 'transfermex_intermex_payout_methods';
    $all = Data::getArray($key);
    foreach ($list as $item) {
      if (!isset($all[$type . '-' .$item['payerCode']])) {
        $all[$type . '-' . $item['payerCode']] = [
          'type' => $type,
          'name' => $type == 'bank' ? $item['bankName'] :$item['payerName'],
          'value' => $item['payerCode']
        ];
      }
    }
    Data::setArray($key, $all);
  }

  public static function getCachedFilteredMethodTypes()
  {
      $key = 'transfermex_intermex_payout_methods';
      $all = Data::getArray($key);
      return count($all) ? $all : [];
  }

  public static function checkPayerIsAvailable(User $sender, $payerCode, $methodType = 'bank') {
    $isAvailable = false;
    try {
      $payerList = self::getPayerByType($sender->getCountry(), $sender->getState(), $methodType);
      foreach ($payerList as $payer) {
        if ($payer['payerCode'] == $payerCode) {
          $isAvailable = true;
        }
      }
    } catch (\Throwable $exception) {
      Log::debug('Failed to check the payer ' . $payerCode .  ' is available!');
      $isAvailable = true;
    }
    return $isAvailable;
  }
}
