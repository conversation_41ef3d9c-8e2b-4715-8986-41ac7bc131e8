<?php


namespace TransferMexBundle\Services;


use Carbon\Carbon;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\CardProgramCardType;
use CoreBundle\Entity\Config;
use CoreBundle\Entity\Country;
use CoreBundle\Entity\ExternalInvoke;
use CoreBundle\Entity\ExternalCost;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\State;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserGroup;
use CoreBundle\Entity\UserIdVerify;
use CoreBundle\Exception\FailedException;
use CoreBundle\Services\APIServices\TwilioService;
use CoreBundle\Services\Common\CardService;
use CoreBundle\Utils\Bundle;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Excel;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Service;
use CoreBundle\Utils\Util;
use Doctrine\ORM\Query\Parameter;
use Doctrine\ORM\QueryBuilder;
use Exception;
use FaasBundle\Services\BOTM\BotmService;
use FaasBundle\Services\IProcessor;
use FaasBundle\Services\ProcessorHub;
use FaasBundle\Services\ProcessorServiceTrait;
use PortalBundle\Exception\PortalException;
use SalexUserBundle\Entity\User;
use TransferMexBundle\TransferMexBundle;
use UsUnlockedBundle\Services\IDologyService;
use FaasBundle\Services\FaasMemberService;
use TransferMexBundle\Entity\ImportMembersRecord;
use TransferMexBundle\Entity\ImportMemberErrorRecord;
use TransferMexBundle\Entity\ImportPayoutRecord;
use TransferMexBundle\Entity\ImportPayoutErrorRecord;
use CoreBundle\Utils\Money;
use CoreBundle\Entity\UserCardTransaction;
use TransferMexBundle\Entity\EmployerPayout;
use CoreBundle\Entity\Email;
use CoreBundle\Entity\NotificationEmails;
use CoreBundle\Entity\Transfer;
use TransferMexBundle\Entity\Recipient;
use CoreBundle\Entity\MessageBatch;
use CoreBundle\Entity\MessageBatchErrorRecord;
use Google\Auth\ApplicationDefaultCredentials;
use GuzzleHttp\Client;
use GuzzleHttp\HandlerStack;
use TransferMexBundle\Services\UniTellerRemittanceService;
use TransferMexBundle\Services\RapydDisburseService;
use CoreBundle\Entity\MessageRecord;
use CoreBundle\Services\SSLEncryptionService;
use FaasBundle\Services\BOTM\BotmAPI;
use FaasBundle\Services\BOTM\BotmUserService;
use TransferMexBundle\Entity\EmployeePromo;
use TransferMexBundle\Entity\EmployeeSSN;
use TransferMexBundle\Entity\EmployeeTransferFreeConfig;
use TransferMexBundle\Entity\EmployerTransferFreeConfig;
use TransferMexBundle\Entity\ImportMemberMigrateCardsRecord;
use TransferMexBundle\Entity\ImportUpdateLastFourSsnRecord;

class MemberService
{
    public const STEP_INITIAL = 'Initial';

    public const STEP_KYC_NOT_STARTED = 'KYC Not Started'; // not in use, = Initial
    public const STEP_KYC_FAILED_OFAC = 'KYC Failed (OFAC)';
    public const STEP_KYC_SCAN_PENDING = 'KYC (Scan Pending)';
    public const STEP_KYC_FAILED_SCAN = 'KYC Failed (Scan)';
    public const STEP_KYC_FAILED_OFAC_SCAN = 'KYC Failed (OFAC & Scan)';
    public const STEP_KYC_PASSED = 'KYC Passed'; // not in use, = Onboarded
    public const STEP_KYC_MANUALLY_APPROVED = 'KYC Manually Approved'; // not in use, = Onboarded

    public const STEP_ONBOARDED = 'Onboarded';
    public const STEP_ACTIVE = 'Active';
    public const STEP_ON_HOLD = 'On Hold';
    public const STEP_CLOSED = 'Closed';

    public const MAX_MESSAGE_COUNT = 300;

    public static function getMemberIdsFromFile($path)
    {
        $excel = Excel::openExcel($path);
        $sheet = $excel->getActiveSheet();
        $rows = $sheet->toArray();
        $result = [];
        foreach ($rows as $i => $row) {
            if ($i <= 0) {
                continue;
            }
            if (empty($row[0])) {
                continue;
            }
            $result[] = $row[0];
        }
        return $result;
    }

    public static function importFromCsvFile($path, $isCronImport = false, $importMemberRecord = null)
    {
        $em = Util::em();
        $tz = Util::tzUTC();
        $employers = EmployerService::getAllKeyedBy();
        $countries = $em->getRepository(Country::class)->getAllKeyedBy();

        $excel = Excel::openExcel($path);
        $sheet = $excel->getActiveSheet();
        $rows = $sheet->toArray();
        $errors = [];
        $candidates = [];
        $errorMessage = '';
        if (!$isCronImport) {
          return 'cronJob';
        }

        $emails = []; // Emails will be validated for each user
        $curps = array_values(self::getAllCurpHash());
        $phones = array_values(self::getAllPhones());

        $required = [
            0 => 'First Name',
            1 => 'Last Name',
            2 => 'Employer ID',
            3 => 'Date of Birth',
            //4 => 'CURP ID #',
            5 => 'Home Address',
            6 => "City",
            7 => "State/Province",
            8 => "Postal Code",
            9 => "Country",
            10 => "Email Address",

        ];
        $earliest = Carbon::create(1900, 1, 1, 0, 0, 0);
        foreach ($rows as $i => $row) {
            if ($i <= 0) {
                continue;
            }
            if (empty($row[0]) && empty($row[1])) {
                continue;
            }

            $errorMessage = '';
            if (!isset($row[10])) {
                $errorMessage = 'Row ' . ($i + 1) . ': Incorrect column count!';
                $errors[] = $errorMessage;
                goto has_error;
            }

            foreach ($required as $index => $name) {
                $row[$index] = trim($row[$index]);
                if ($row[$index] === null || $row[$index] === '') {
                    $errorMessage = 'Row ' . ($i + 1) . ': Required field "' . $name . '" is empty!';
                    $errors[] = $errorMessage;
                    goto has_error;
                }
            }

            if (!isset($countries[$row[9]])) {
                $errorMessage = 'Row ' . ($i + 1) . ': Unknown Country!';
                $errors[] = $errorMessage;
                goto has_error;
            }

            /** @var Country $country */
            $country = $countries[$row[9]];
            if (!in_array($country->getIso3Code(), ['USA', 'MEX', 'SLV', 'HND', 'PER'])) {
                $errorMessage = 'Row ' . ($i + 1) . ': Only countries USA and MEX, SLV, HND, PER are allowed!';
                $errors[] = $errorMessage;
                goto has_error;
            }

            $row['country'] = $country;

            $curp = trim($row[4]);
            if ($curp && in_array(TransferMexBundle::getCurpHash($curp), $curps)) {
                $errorMessage = 'Row ' . ($i + 1) . ': Duplicated CURP ID #!';
                $errors[] = $errorMessage;
                goto has_error;
            }

            if (in_array($row[10], $emails)) {
                $errorMessage = 'Row ' . ($i + 1) . ': Duplicated Email Address!';
                $errors[] = $errorMessage;
                goto has_error;
            }

            if (!filter_var($row[10], FILTER_VALIDATE_EMAIL)) {
              $errorMessage = 'Row ' . ($i + 1) . ': Email format error!';
              $errors[] = $errorMessage;
              goto has_error;
            }

            if (!empty($row[11]) && in_array(Util::inputPhone($row[11], $country), $phones)) {
                $errorMessage = 'Row ' . ($i + 1) . ': Duplicated Mobile Phone!';
                $errors[] = $errorMessage;
                goto has_error;
            }

            // Log::debug($row[11]);
            // Log::debug(preg_match('/^[0-9+\-\s]*$/', $row[11]));
            if(!empty($row[11]) && !preg_match('/^[0-9+\-\s]*$/', $row[11])) {
              $errorMessage = 'Row ' . ($i + 1) . ': Mobile format error! Please enter a string consisting of 0-9 and + - space';
              $errors[] = $errorMessage;
              goto has_error;
            }

            $eid = (int)$row[2];
            if (!isset($employers[$eid])) {
                $errorMessage = 'Row ' . ($i + 1) . ': Unknown Employer ID!';
                $errors[] = $errorMessage;
                goto has_error;
            }
            /** @var User $employer */
            $employer = $employers[$eid];
            if ($employer && !$employer->isActive()) {
                $errorMessage = 'Row ' . ($i + 1) . ': Employer is not active!';
                $errors[] = $errorMessage;
                goto has_error;
            }

            $row['employer'] = $employer;
            $group = $employer->getAdminGroup();

            if (empty($curp) && !$group->isBOTM()) {
                $errorMessage = 'Row ' . ($i + 1) . ': CURP ID # is required!';
                $errors[] = $errorMessage;
                goto has_error;
            }

            /** @var State $state */
            $state = $country->findStateByName($row[7]);
            if (!$state) {
                $errorMessage = 'Row ' . ($i + 1) . ': Unknown State!';
                $errors[] = $errorMessage;
                goto has_error;
            }
            $row['state'] = $state;

            $member = User::findPlatformUserByEmail($row[10], Bundle::getMemberRoles());
            if ($member) {
                $errorMessage = 'Row ' . ($i + 1) . ': Duplicated accounts with the same email address!';
                $errors[] = $errorMessage;
                goto has_error;
            }

            $member = User::findPlatformUserByPhone(Util::inputPhone($row[11], $row['country']), Bundle::getMemberRoles());
            if ($member) {
                $errorMessage = 'Row ' . ($i + 1) . ': Duplicated accounts with the same phone!';
                $errors[] = $errorMessage;
                goto has_error;
            }

            try {
                $dob = Carbon::createFromFormat('d/m/Y', $row[3], $tz);
            } catch (Exception $e) {
                $dob = null;
            }
            if (!$dob || $dob->lessThan($earliest)) {
                $errorMessage = 'Row ' . ($i + 1) . ': Invalid Date of Birth format!';
                $errors[] = $errorMessage;
                goto has_error;
            }

            if (strlen($curp) === 18) {
                try {
                    // BAMU000717HPLNRRA0
                    $md = Carbon::createFromFormat('md', substr($curp, 6, 4), $tz);
                    if ($md && $md->month === $dob->day && $md->day === $dob->month) {
                        $dob = Carbon::create($dob->year, $md->month, $md->day, 0, 0, 0, $tz);
                    }
                } catch (Exception $e) {}
            }

            $row['dob'] = Util::toUTC($dob->startOfDay());

            $curps[] = TransferMexBundle::getCurpHash($curp);
            $emails[] = $row[10];
            $phones[] = $row[11];

            $candidates[] = $row;

            has_error:
            if ($errorMessage) {
            //   if (count($errors) >= 5) {
            //       break;
            //   }
            // } else if(count($errors)) {
              // create error record
              // if ($errorMessage != $errors[count($errors) - 1]) {
              //    $errorMessage = $errors[count($errors) - 1];
              // }
              $errorRecord = new ImportMemberErrorRecord();
              $errorRecord->setReason($errorMessage)
                          ->setImportMemberRecord($importMemberRecord)
                          ->persist();
            }
        }
        if ($errors && !$isCronImport) {
            throw PortalException::temp('Please fix below errors first: <ul><li>'
                                        . implode('</li><li>', $errors)
                                        . '</li></ul> Nothing is imported.');
        }

        $role = Role::find(Role::ROLE_TRANSFER_MEX_MEMBER);
        $cardType = CardProgramCardType::getForCardProgram(CardProgram::transferMexUSD());

        $result = [];
        foreach ($candidates as $can) {
            $cacheKey = 'mex_import_members_curp_' . trim($can[4]);
            if (Data::has($cacheKey)) {
                continue;
            }
            Data::set($cacheKey, true);

            $user = new User();
            $user->setEnabled(true)
                ->setPlainPassword(Util::generatePassword())
                ->setStatus(User::STATUS_ACTIVE)
                ->setFirstName($can[0])
                ->setLastName($can[1])
                ->setBirthday($can['dob'])
                ->setCurpId(trim($can[4]))
                ->setAddress($can[5])
                ->setCity($can[6])
                ->setState($can['state'])
                ->setZip(is_numeric($can[8]) ? (int)$can[8] : $can[8])
                ->setCountry($can['country'])
                ->changeEmail($can[10], Role::ROLE_TRANSFER_MEX_MEMBER)
                ->setMobilephone(empty($can[11]) ? null : Util::inputPhone($can[11], $can['country']))
                ->setExternalId('' . ($can[12] ?? ''))
                ->addTeam($role);
            $isCronImport ? $user->persist() : $em->persist($user);
            // store ssn
            if ($can[13] && strlen($can[13]) >= 4) {
              $ssn = substr($can[13], -4);
              $ssnRecord = new EmployeeSSN();

              $oldEmployer = $ssnRecord->getEmployer();
              $ssnRecord->setLastFourSsn($ssn)
                        ->setEmployee($user);
              if ($oldEmployer != $can['employer']) {
                $ssnRecord->setEmployer($can['employer'])
                          ->setLastEmployer($oldEmployer);
              }
              $isCronImport ? $ssnRecord->persist() : $em->persist($ssnRecord);
            }
            // store seasonName
            if ($can[14]) {
              Util::updateMeta($user, [
                'seasonName' => $can[14]
              ]);
            }
            $uc = CardService::create($user, $cardType, false);
            $isCronImport ? $uc->persist() : $em->persist($uc);

            self::ensureInEmployer($user, $can['employer'], false);
            self::updateOnboardStatus($user, false);

            $result[] = $user;
        }
        if (!$isCronImport) {
          $em->flush();
        } else {
          $importMemberRecord->setSuccessCount(count($result))
                            ->setFailedCount(count($errors))
                            ->setStatus(ImportMembersRecord::IMPORT_COMPLETE)
                            ->persist();
        }

        // send create payment error message
        if (count($errors)) {
          $platform = $importMemberRecord->getPlatform();
          SlackService::$channel = 'client';
          SlackService::prepareForPlatform();
          $message = sprintf(
              "Import Members failed: Created members based on the batch file %s, and %s failed.",
               $importMemberRecord->getFileName(),
              count($errors)
          );
          SlackService::alert($message, [
              'Batch ID'  => $importMemberRecord->getId(),
              'File Name'   =>  $importMemberRecord->getFileName(),
              'Success Count'  => count($result),
              'Error count' =>  count($errors)
          ], SlackService::GROUP_TRANSFER_MEX);
          //  send email
          $recipients = NotificationEmails::findByPlatform($platform);

          $bcc = [
              '<EMAIL>',
              '<EMAIL>',
          ];

         //  $errorList = [];
          $mailBody = Util::render('@TransferMex/Members/import_members_exceptions.html.twig', [
            'errorList' => self::getErrorList($importMemberRecord),
            'fileName'  => $importMemberRecord->getFileName()
          ]);

          Email::sendWithTemplate($recipients, Email::TEMPLATE_SIMPLE_LAYOUT, [
            'body' => $mailBody,
            'Product_Team' =>  $platform->getName(),
            'subject' => 'Import Members Exceptions',
            '_bcc' => $bcc,
          ], null, $importMemberRecord->getCardProgram() );
        }


        return $result;
    }

    protected static function getErrorList (ImportMembersRecord $importMemberRecord) {
      $res = Util::em()->getRepository(ImportMemberErrorRecord::class)
          ->createQueryBuilder('mr')
          ->join('mr.importMemberRecord', 'mp')
          ->andWhere(Util::expr()->eq('mp.id', ':batchId'))
          ->setParameter('batchId', $importMemberRecord->getId())
          ->distinct()
          ->getQuery()
          ->getResult();
      $list = [];
      foreach ($res as $item) {
        $list[] = [
          'reason' => $item->getReason()
        ];
      }
      return $list;
  }

    public static function ensureInEmployer(User $member, User $employer, $persist = true)
    {
        $removed = [];
        /** @var UserGroup $userGroup */
        foreach ($member->getUserGroups() as $userGroup) {
            $member->removeUserGroup($userGroup);
            $removed[] = $userGroup->getName();
        }

        $newGroup = $employer->getAdminGroup();
        $member->addUserGroup($newGroup);

        if ($persist) {
            $member->persist();
        }

        if ($removed) {
            $old = implode('/', $removed);
            if ($old !== $newGroup->getName()) {
                $member->addNote('Removed from employer `' . $old
                                 . '` and added to `' . $newGroup->getName() . '`', $persist, Util::getImpersonatingUser() ? Util::getImpersonatingUser()->getId() : Util::user()->getId());
            }
        }
    }

    public static function deactivateAccount(User $user, string $reason)
    {
        $user->setStatus(User::STATUS_INACTIVE, $reason)
            ->persist();
        $uc = $user->getCurrentPlatformCard();
        if ($uc) {
            $uc->setStatus(UserCard::STATUS_INACTIVE, true, false)
                ->persist();

            if ($uc->isBotmCard()) {
                $api = BotmAPI::getForUserCard($uc);
                ExternalInvoke::host($api->updateUserAccountStatus($user, $user->isActive()));
            }
        }

        self::updateOnboardStatus($user);
    }

    public static function updateOnboardStatus(User $user, $persist = true)
    {
        $step = $user->getRegisterStep();
        // check group
        $userGroup = $user->getPrimaryGroup();
        $isBotmUser = $userGroup && $userGroup->isBOTM();
        while(true) {
            $status = $user->getStatus();
            if ($status === User::STATUS_CLOSED) {
                $step = self::STEP_CLOSED;
                break;
            }

            if ($status === User::STATUS_INACTIVE) {
                $step = self::STEP_ON_HOLD;
                break;
            }

            $kycStep = $user->getAllKycStep();
            if ($kycStep === self::STEP_KYC_NOT_STARTED) {
                $step = self::STEP_INITIAL;
                break;
            }

            if (!in_array($kycStep, [
                self::STEP_KYC_PASSED,
                self::STEP_KYC_MANUALLY_APPROVED,
            ]) && !$isBotmUser) {
                $step = $kycStep;
                break;
            }

            if ($isBotmUser && $kycStep == self::STEP_KYC_FAILED_OFAC) {
                $step = $kycStep;
                break;
            }

            $uc = $user->getCurrentPlatformCard();
            if (!$uc || !$uc->getAccountNumber()) {
                $step = self::STEP_ONBOARDED;
                break;
            }

            $step = self::STEP_ACTIVE;
            break;
        }

        if ($user->getRegisterStep() !== $step) {
            $user->setRegisterStep($step);

            if ($persist) {
                $user->persist();
            } else {
                Util::em()->persist($user);
            }

            return true;
        }
        return false;
    }

    public static function getAllPhones( $role = [])
    {
        if (empty($role)) {
          $role = [
            Role::ROLE_TRANSFER_MEX_MEMBER,
          ];
        }
        $rs = Util::em()->getRepository(User::class)
            ->createQueryBuilder('u')
            ->join('u.teams', 't')
            ->where(Util::expr()->in('t.name', ':roles'))
            ->setParameter('roles', $role)
            ->distinct()
            ->select('u.id, u.mobilephone')
            ->getQuery()
            ->getArrayResult();
        $result = [];
        foreach ($rs as $r) {
            $result[$r['id']] = $r['mobilephone'];
        }
        return $result;
    }

    public static function getAllCurpHash()
    {
        $rs = Util::em()->getRepository(User::class)
            ->createQueryBuilder('u')
            ->join('u.teams', 't')
            ->where(Util::expr()->in('t.name', ':roles'))
            ->setParameter('roles', [
                Role::ROLE_TRANSFER_MEX_MEMBER,
            ])
            ->distinct()
            ->select('u.id, u.meta, u.source')
            ->getQuery()
            ->getArrayResult();
        $result = [];
        foreach ($rs as $r) {
            $meta = json_decode($r['meta'] ?: '{}', true);
            $hash = $meta['curpDetails']['hash'] ?? $r['source'];
            if ($hash) {
                $result[$r['id']] = $hash;
            }
        }
        return $result;
    }

    public static function calculateKycCost(QueryBuilder $sub, $force = false, $where = null)
    {
        $cacheKey = 'mex_calculateKycCost';
        if (!$where && !$force && Data::has($cacheKey)) {
            return Data::get($cacheKey);
        }

        $em = Util::em();
        $expr = Util::expr();

        $query = $em->getRepository(ExternalInvoke::class)
            ->createQueryBuilder('ei')
            ->where('ei.type = :type')
            ->andWhere('ei.entity = :entity')
            ->distinct()
            ->select('count(ei)');

        if ($where) {
            $where($query);
        }

        /** @var Parameter $parameter */
        foreach ($sub->getParameters() as $parameter) {
            $query->setParameter($parameter->getName(), $parameter->getValue());
        }
        $sub->setParameters([]);

        // ------------- CURP --------------
        $query1 = clone $query;
        $iq = $query1->andWhere($expr->in('ei.foreignKey', $sub->getDQL()))
            ->setParameter('type', ExternalInvoke::TYPE_IDOLOGY_IQ)
            ->setParameter('entity', User::class)
            ->getQuery()
            ->getSingleScalarResult();

        // ------------- OFAC --------------
        $ofac = $query1->setParameter('type', ExternalInvoke::TYPE_IDOLOGY_OFAC)
            ->getQuery()
            ->getSingleScalarResult();

        // ------------- ID Scan --------------
        $mid = $em->getRepository(UserIdVerify::class)
            ->createQueryBuilder('uiv')
            ->where($expr->in('uiv.user', $sub->getDQL()));
        $scan = $query->andWhere($expr->in('ei.foreignKey', $mid->getDQL()))
            ->setParameter('type', ExternalInvoke::TYPE_IDOLOGY_SCAN)
            ->setParameter('entity', UserIdVerify::class)
            ->getQuery()
            ->getSingleScalarResult();

        $sum = $iq * IDologyService::COST_IQ
               + $ofac * IDologyService::COST_OFAC_CHECK
               + $scan * IDologyService::COST_SCAN_VERIFY;

        if (!$where) {
            Data::set($cacheKey, $sum);
        }
        return $sum;
    }

    public static function calculateKycCostByCostTable(QueryBuilder $sub, $force = false, $where = null)
    {
        $cacheKey = 'mex_calculateKycCostByCostTable';
        if (!$where && !$force && Data::has($cacheKey)) {
            return Data::get($cacheKey);
        }

        $em = Util::em();
        $expr = Util::expr();

        $query = $em->getRepository(ExternalCost::class)
            ->createQueryBuilder('ec')
            ->where('ec.type = :type')
            ->andWhere('ec.entity = :entity')
            ->distinct()
            ->select('count(ec)');

        if ($where) {
            $where($query);
        }

        /** @var Parameter $parameter */
        foreach ($sub->getParameters() as $parameter) {
            $query->setParameter($parameter->getName(), $parameter->getValue());
        }
        $sub->setParameters([]);

        // ------------- CURP --------------
        $query1 = clone $query;
        $iq = $query1->andWhere($expr->in('ec.foreignKey', $sub->getDQL()))
            ->setParameter('type', ExternalInvoke::TYPE_IDOLOGY_IQ)
            ->setParameter('entity', User::class)
            ->getQuery()
            ->getSingleScalarResult();

        // ------------- OFAC --------------
        $ofac = $query1->setParameter('type', ExternalInvoke::TYPE_IDOLOGY_OFAC)
            ->getQuery()
            ->getSingleScalarResult();

        // ------------- ID Scan --------------
        $mid = $em->getRepository(UserIdVerify::class)
            ->createQueryBuilder('uiv')
            ->where($expr->in('uiv.user', $sub->getDQL()));
        $scan = $query->andWhere($expr->in('ec.foreignKey', $mid->getDQL()))
            ->setParameter('type', ExternalInvoke::TYPE_IDOLOGY_SCAN)
            ->setParameter('entity', UserIdVerify::class)
            ->getQuery()
            ->getSingleScalarResult();

        $sum = $iq * IDologyService::COST_IQ
               + $ofac * IDologyService::COST_OFAC_CHECK
               + $scan * IDologyService::COST_SCAN_VERIFY;

        if (!$where) {
            Data::set($cacheKey, $sum);
        }
        return $sum;
    }

    public static function enroll(UserCard $uc)
    {
        if ($uc->isBotmCard()) {
            BotmService::enroll($uc);
            return;
        }

        $group = RapidService::checkBeforeEnrollingCard($uc);

        $actions = [];
        $service = RapidAPI::getForUserCard($uc);
        if (!$uc->isIssued()) {
            if ($group && $group->isPrefunded()) {
                $groupAgent = Util::meta($group, 'rapidAgentNumber');
                if (!$groupAgent) {
                    SlackService::$channel = 'client';
                    SlackService::alert('The employer has not configured the Rapid agent info when attempting to enroll a card', [
                        'employer' => $group->getName(),
                        'member' => $uc->getUser()->getSignature(),
                        'account' => $uc->getAccountNumber(),
                    ], SlackService::GROUP_TRANSFER_MEX);
                    throw PortalException::temp('Failed to enroll the card since the employer is not well configured yet.');
                }
            }

            /** @var ExternalInvoke $ei */
            [$ei, ] = $service->enroll($uc);
            if ($ei && $ei->isFailed()) {
                if (RapidAPI::isAccountNumberError($ei->getError())) {
                    $cards = true;
                    try {
                        $baseApi = new RapidAPI();
                        $cards = $baseApi->getCardDetailsData($uc);
                    } catch (\Exception $e) {
                        Log::warn('Failed to get the card list when enrolling card failed: ' . $e->getMessage(), [
                            'account_number' => $uc->getAccountNumber(),
                            'enroll_error' => $ei->getErrorSignature(),
                        ]);
                    }
                    if (is_array($cards) && !$cards) {
                        throw PortalException::temp('This account has no cards. Please try again with another account.');
                    }
                }
                throw PortalException::temp($ei->getError());
            }
            $uc->setIssued(true)
                ->persist();
            $actions[] = 'enroll';

            // Your card has been enrolled. Please change the PIN as soon as possible.
            $content = 'Su tarjeta ha sido registrada. Cambie el PIN lo antes posible.';
            self::sendChangePinMessageToMembers($uc->getUser(), $content);
        }

        RapidService::updateBalanceAndStatus($uc);

        if ($uc->getStatus() === UserCard::STATUS_INACTIVE) {
            [$ei, ] = $service->activate($uc);
            if ($ei && $ei->isFailed()) {
                throw PortalException::temp($ei->getError());
            }
            RapidService::updateBalanceAndStatus($uc);
            $actions[] = 'activate';
        }

        if ($actions) {
            $uc->getUser()->addNote('Performed the ' . implode('/', $actions)
                                    . ' actions to the account ' . $uc->getAccountNumber(),
                TRUE, Util::user()->getId());
        }

        if (!$uc->isActive()) {
            SlackService::alert('Failed to activate the card.', [
                'member' => $uc->getUser()->getSignature(),
                'status' => $uc->getNativeStatus(),
            ]);
            try {
                [, $details] = $service->getCardDetails($uc);
                Log::warn('TransferMex card detail', $details);
            } catch (\Exception $e) {
                Log::exception('Failed to call getCardDetails', $e);
            }
            throw PortalException::temp('Your card is issued but not activated. Please try again a moment later.');
        }
    }

    public static function reassign(UserCard $uc)
    {
        $api = $uc->getProcessorApiImpl();
        if ($uc->isIssued()) {
            /** @var ExternalInvoke $ei */
            [$ei, ] = $api->reassignCard($uc, $uc->decryptCardNumber());
            if ($ei && $ei->isFailed()) {
                throw PortalException::temp($ei->getError());
            }
            $uc->setIssued(false)
                ->persist();
        }

        ProcessorHub::updateBalanceAndStatusSilently($uc);

        if ($uc->isActive()) {
            throw PortalException::temp('Failed to reassign/deactivate the card. Please contact support.');
        }
    }


    public static function checkMemberFromCsvFile($path, $cardProgram, $client, $isCronImport = false, $importMemberRecord = null)
    {
        $em = Util::em();
        $tz = Util::tzUTC();
        $result = [];
        $excel = Excel::openExcel($path);
        $sheet = $excel->getActiveSheet();
        $rows = $sheet->toArray();

        $accounList = array_filter($rows, function ($item) {
          return !($item[0] === null
           && $item[1] === null
           && $item[2] === null
           && $item[3] === null
           && $item[4] === null
           && $item[5] === null
           && $item[6] === null
           && $item[7] === null
           && $item[8] === null
           && $item[9] === null
           && $item[0] === null);
        });

        if (!$isCronImport) {
          return 'cronJob';
        }
        $errors = [];
        $candidates = [];
        $errorMessage = '';
        $countries = $em->getRepository(Country::class)->getAllKeyedBy();
        $cardProgramCountries = $cardProgram->getCountries();
        $emails = []; // Emails will be validated for each use
        $phones = array_values(self::getAllPhones([ Role::ROLE_FAAS_MEMBER]));

        $required = [
            0 => 'First Name',
            1 => 'Last Name',
            2 => 'Date of Birth',
            3 => 'Street Address',
            4 => 'City',
            5 => 'State/Province',
            6 => "Postal Code",
            7 => "Country",
            8 => "Email",
        ];
        $earliest = Carbon::create(1900, 1, 1, 0, 0, 0);
        foreach ($rows as $i => $row) {
            if ($i <= 0) {
              continue;
            }
            if (empty($row[0]) && empty($row[1])) {
                continue;
            }

            if (!isset($row[8])) {
                $errorMessage = 'Row ' . ($i + 1) . ': Incorrect column count!';
                $errors[] = $errorMessage;
                goto has_error;
            }

            foreach ($required as $index => $name) {
                $row[$index] = trim($row[$index]);
                if ($row[$index] === null || $row[$index] === '') {
                    $errorMessage = 'Row ' . ($i + 1) . ': Required field "' . $name . '" is empty!';
                    $errors[] = $errorMessage;
                    goto has_error;
                }
            }
            if (!isset($countries[$row[7]])) {
              $errorMessage = 'Row ' . ($i + 1) . ': Unknown Country!';
              $errors[] = $errorMessage;
              goto has_error;
            }


            $supportCountry = false;
            foreach($cardProgramCountries as $cardProgramCountry) {
              // Log::debug($cardProgramCountry->getId());
              if ($cardProgramCountry->getId() === $countries[$row[7]]->getId()) {
                $supportCountry = true;
              }
            }

            if (!$supportCountry) {
              $errorMessage = 'Row ' . ($i + 1) . ': The country ' . $row[7] . ' is not allowed!';
              $errors[] = $errorMessage;
              goto has_error;
            }
            if (in_array($row[8], $emails)) {
              $errorMessage = 'Row ' . ($i + 1) . ': Duplicated Email Address!';
              $errors[] = $errorMessage;
                goto has_error;
            }

            if (!filter_var($row[8], FILTER_VALIDATE_EMAIL)) {
              $errorMessage = 'Row ' . ($i + 1) . ': Email format error!';
              $errors[] = $errorMessage;
              goto has_error;
            }

            if (!empty($row[9]) && in_array($row[9], $phones)) {
                $errorMessage = 'Row ' . ($i + 1) . ': Duplicated Mobile Phone!';
                $errors[] = $errorMessage;
                goto has_error;
            }
            // Log::debug(preg_match('/^[0-9+\-\s]*$/u', $row[9]));
            // Log::debug($row[9]);
            if(!empty($row[9]) && !preg_match('/^[0-9+\-\s]*$/', $row[9])) {
                $errorMessage = 'Row ' . ($i + 1) . ': Mobile format error! Please enter a string consisting of 0-9 and + - space';
                $errors[] = $errorMessage;
                goto has_error;
            }
             /** @var Country $country */
             $country = $countries[$row[7]];
             $row['country'] = $country;
             /** @var State $state */
             $state = $country->findStateByName($row[5]);
             if (!$state) {
                 $errorMessage = 'Row ' . ($i + 1) . ': Unknown State!';
                 $errors[] = $errorMessage;
                 goto has_error;
             }
             $row['state'] = $state;

             $member = User::findPlatformUserByEmail($row[8],
             [
               Role::ROLE_FAAS_MEMBER,
              //  Role::ROLE_FAAS_ADMIN,
              //  Role::ROLE_FAAS_AGENT,
              //  Role::ROLE_FAAS_CLIENT
              ]);
            if ($member) {
                $errorMessage = 'Row ' . ($i + 1) . ': Duplicated accounts with the same email address!';
                $errors[] = $errorMessage;
                goto has_error;
            }

            try {
                $dob = Carbon::createFromFormat('m/d/Y', $row[2], $tz);
            } catch (Exception $e) {
                $dob = null;
            }

            if (!$dob ) {
                $errorMessage = 'Row ' . ($i + 1) . ': Invalid Date of Birth format!(Update to mm/dd/yyyy)';
                $errors[] = $errorMessage;
                goto has_error;
            }
            if ($dob->lessThan($earliest)) {
                $errorMessage = 'Row ' . ($i + 1) . ': Invalid date range (Year before 1900)';
                $errors[] = $errorMessage;
                goto has_error;
            }
            if (isset($row[10]) && $row[10]) {
              $other = UserCard::findByAccountNumberWithLegacies($row[10]);
              if ($other) {
                $errorMessage = 'Row ' . ($i + 1) . ': Duplicated accounts with the Account Number!';
                $errors[] = $errorMessage;
                goto has_error;
              }
            }

            $row['dob'] = Util::toUTC($dob->startOfDay());

            $emails[] = $row[8];
            $phones[] = $row[9];

            $candidates[] = $row;

            has_error:
            if (!$isCronImport) {
              if (count($errors) >= 5) {
                  break;
              }
            } else if (count($errors)) {
              // create error record
              // Log::debug('Error Message', $errors);
              // if ($errorMessage != $errors[count($errors) - 1]) {
              //   $errorMessage = $errors[count($errors) - 1];
              // }
              $errorRecord = new ImportMemberErrorRecord();
              $errorRecord->setReason($errorMessage)
                          ->setImportMemberRecord($importMemberRecord)
                          ->persist();
            }
        }
        if ($errors && !$isCronImport) {
            throw PortalException::temp('Please fix below errors first: <ul><li>'
                                        . implode('</li><li>', $errors)
                                        . '</li></ul> Nothing is imported.');
        }

        $role = Role::find(Role::ROLE_FAAS_MEMBER);
        $cardType = CardProgramCardType::getForCardProgram($cardProgram);

        $result = [];

        foreach ($candidates as $can) {
            $user = new User();
            $user->setEnabled(true)
                ->setPlainPassword(Util::generatePassword())
                ->setStatus(User::STATUS_ACTIVE)
                ->setFirstName($can[0])
                ->setLastName($can[1])
                ->setBirthday($can['dob'])
                ->setAddress($can[3])
                ->setCity($can[4])
                ->setState($can['state'])
                ->setZip($can[6])
                ->setCountry($can['country'])
                ->changeEmail($can[8], Role::ROLE_FAAS_MEMBER)
                ->setMobilephone(empty($can[9]) ? null : Util::inputPhone($can[9], $can['country']))
                ->addTeam($role);

            $isCronImport ? $user->persist() : $em->persist($user);
            $uc = CardService::create($user, $cardType, false);
            $isCronImport ? $uc->persist() : $em->persist($uc);

            self::ensureInEmployer($user, $client, false);
            // FaasMemberService::updateOnboardStatus($user, false);
            $result[] = $user;
        }
        if (!$isCronImport){
          $em->flush();
        } else {
          $importMemberRecord->setSuccessCount(count($result))
                            ->setFailedCount(count($errors))
                            ->setStatus(ImportMembersRecord::IMPORT_COMPLETE)
                            ->persist();
        }
        return $result;
    }

    public static function getLastImport($user, $cardProgram) {
      $last = Util::em()->getRepository(ImportMembersRecord::class)
              ->createQueryBuilder('im')
              ->where(Util::expr()->eq('im.employer', $user->getId()))
              ->andWhere(Util::expr()->eq('im.cardProgram', $cardProgram->getId()))
              ->orderBy('im.createdAt', 'desc')
              ->setMaxResults(1)
              ->getQuery()
              ->getOneOrNullResult();
      $res = null;
      if ($last) {
        $res = [
          'id'     => $last->getId(),
          'createdAt' => Util::formatDateTime($last->getCreatedAt(), Util::DATE_TIME_FORMAT, 'EST'),
          'success' => $last->getSuccessCount() ? $last->getSuccessCount() : 0,
          'fail'    => $last->getFailedCount() ? $last->getFailedCount() : 0,
          'status'  => $last->getStatus() === ImportMembersRecord::IMPORT_COMPLETE ? 'Completed' : 'In Progress'
        ];
      }
      return $res;
    }

    public static function getLastImportSsn($user) {
      $last = Util::em()->getRepository(ImportUpdateLastFourSsnRecord::class)
              ->createQueryBuilder('im')
              ->where(Util::expr()->eq('im.createBy', $user->getId()))
              ->orderBy('im.createdAt', 'desc')
              ->setMaxResults(1)
              ->getQuery()
              ->getOneOrNullResult();
      $res = null;
      if ($last) {
        $res = [
          'id'     => $last->getId(),
          'createdAt' => Util::formatDateTime($last->getCreatedAt(), Util::DATE_TIME_FORMAT, 'EST'),
          'success' => $last->getSuccessCount() ? $last->getSuccessCount() : 0,
          'fail'    => $last->getFailedCount() ? $last->getFailedCount() : 0,
          'status'  => $last->getStatus() === ImportUpdateLastFourSsnRecord::IMPORT_COMPLETE ? 'Completed' : 'In Progress'
        ];
      }
      return $res;
    }


    public static function getLastMigrate($user, $cardProgram) {
      $last = Util::em()->getRepository(ImportMemberMigrateCardsRecord::class)
              ->createQueryBuilder('imm')
              ->orderBy('imm.createdAt', 'desc')
              ->setMaxResults(1)
              ->getQuery()
              ->getOneOrNullResult();
      $res = null;
      if ($last) {
        $res = [
          'id'     => $last->getId(),
          'createdAt' => Util::formatDateTime($last->getCreatedAt(), Util::DATE_TIME_FORMAT, 'EST'),
          'success' => $last->getSuccessCount() ? $last->getSuccessCount() : 0,
          'fail'    => $last->getFailedCount() ? $last->getFailedCount() : 0,
          'status'  => $last->getStatus() === ImportMemberMigrateCardsRecord::IMPORT_COMPLETE ? 'Completed' : 'In Progress'
        ];
      }
      return $res;
    }

    public static function  checkPayoutFromCustomCsvFile($path, $platform, $employeeText, $isCronImport, $importPayoutRecord, $fileType)
    {
        $em = Util::em();
        $result = [];
        $excel = Excel::openExcel($path);
        $sheet = $excel->getActiveSheet();
        $rows = $sheet->toArray();

        $count = count($rows);
        Util::consoleLine('Parsed ' . $count . ' files from the method ' . __METHOD__ . ' with file type '
                          . $fileType . ' for file ' . $path);

        $errors = [];
        $errorMessage = '';
        $str = $platform->isFaasPlatforms() ? 'Member: ' : 'Employee: ';
        if (!$isCronImport) {
            $total = 0;
            foreach ($rows as $i => $row) {
                if ($fileType == 'A') {
                  $row[2] = str_replace(' ', '', $row[2]);
                  $row[0] = str_replace('$', '', $row[0]);
                  $accountNumber = strlen($row[2]) > 11 ? substr($row[2], -11) : $row[2];
                  $result[] = [
                    'Amount'         =>  $row[0] * 100 > 0 ? $row[0] * 100 : $row[0] * 100 * -1 ,
                    'Name'           =>  $row[1],
                    'Account Number' => $accountNumber,
                    'Routing'        =>  $row[3],
                    'Type'           =>  $row[4],
                    'Payment Date'   =>  $row[5]
                  ];
                  $total+= $row[0] * 100 > 0 ? $row[0] * 100 : $row[0] * 100 * -1;
                } else if ($fileType == 'B') {
                  if ($i > 0) {
                    $row[0] = str_replace(' ', '', $row[0]);
                    $row[2] = str_replace('$', '', $row[2]);
                    $accountNumber = strlen($row[0]) > 11 ? substr($row[0], -11) : $row[0];
                    $result[] = [
                      'Amount'         =>  $row[2] * 100 > 0 ? $row[2] * 100 : $row[2] * 100 * -1 ,
                      'Employee ID'    =>  $row[1],
                      'Account Number' =>  $accountNumber,
                      'Desc'           =>  $row[3]
                    ];
                    $total += $row[2] * 100 > 0 ? $row[2] * 100 : $row[2] * 100 * -1;
                  }
                }
            }
            Util::consoleLine('Checked data in non cronjob mode');
            return ['type' => 'cronJob', 'data' => $result, 'total' => $total];
        }
        $required = [];
        if ($fileType == 'A') {
          $required = [
            0 => 'Amount',
            2 => 'Account number'
          ];
        } else if ($fileType == 'B') {
          $required = [
            2 => 'Amount',
            0 => 'Account number',
            1 => 'Employee ID',
          ];
        }

        foreach ($rows as $i => $row) {
            $msgPrefix = $i . '/' . $count . ': ';
            $errorMessage = null;
            if ($i <= 0 && $fileType == 'B') {
                continue;
            }
            if ($row[0] === null || $row[0] === '' || $row[0] === 0) {
              continue;
            }
            Util::consoleLine($msgPrefix . ' Pre-checking row');

            $employee = null;
            $accountNumber = null;

            foreach ($required as $index => $name) {
                $row[$index] = trim($row[$index]);
                if ($row[$index] === null || $row[$index] === '') {
                    $errorMessage = 'Row ' . ($i + 1) . ': Required field "' . $name . '" is empty!';
                    $errors[] = $errorMessage;
                    goto has_error;
                }
            }
            if ($fileType == 'A') {
              if ($platform->isTransferMex() && trim($row[2]) && str_replace(' ', '', $row[2]) !== '') {
                $row[2] = str_replace(' ', '', $row[2]);
                $accountNumber = strlen($row[2]) > 11 ? substr($row[2], -11) : $row[2];
                Util::consoleLine($msgPrefix . 'Parsed account number from file type A');
                $employeeUc = Util::em()->getRepository(\CoreBundle\Entity\UserCard::class)->findOneBy(['accountNumber' => $accountNumber]);
                if (!$employeeUc) {
                  $employeeUc = UserCard::findByAccountNumberWithLegacies($accountNumber);
                  Util::consoleLine($msgPrefix . 'Completed findByAccountNumberWithLegacies');
                }
                $employee = $employeeUc ? $employeeUc->getUser() : null;
              }
            } else if ($fileType == 'B') {
              if ($platform->isTransferMex() && trim($row[0]) && str_replace(' ', '', $row[0]) !== '') {
                $row[0] = str_replace(' ', '', $row[0]);
                $accountNumber = strlen($row[0]) > 11 ? substr($row[0], -11) : $row[0];
                Util::consoleLine($msgPrefix . 'Parsed account number from file type B');
                $employeeUc = Util::em()->getRepository(\CoreBundle\Entity\UserCard::class)->findOneBy(['accountNumber' => $accountNumber]);
                if (!$employeeUc) {
                  $employeeUc = UserCard::findByAccountNumberWithLegacies($accountNumber);
                  Util::consoleLine($msgPrefix . 'Completed findByAccountNumberWithLegacies');
                }
                $employee = $employeeUc ? $employeeUc->getUser() : null;
              }
              if (!$employee) {
                $employee = User::findByExternalId( $row[1], $importPayoutRecord ? $importPayoutRecord->getEmployer() : null);
                Util::consoleLine($msgPrefix . 'Completed findByExternalId');
              }
            }

            if (!$employee) {
              $errorMessage = 'Row ' . ($i + 1) . ':' . $employeeText .' could not be found by account number with ' . $accountNumber . '!';
              $errors[] = $errorMessage;
              goto has_error;
            }
            Util::consoleLine($msgPrefix . ' Completed querying the employee ' . $employee->getId());

            $employer =  $employee->getPrimaryGroupAdmin();
            if (!$employer || $importPayoutRecord->getEmployer()->getId() !== $employer->getId()) {
                $errorMessage = $str . $employee->getSignature() . ' does not belong to this employer ' . $importPayoutRecord->getEmployer()->getId();
                $errors[] = $errorMessage;
                goto has_error;
            }

            $uc = $employee->getCurrentPlatformCard();
            if (!$uc) {
              $errorMessage = 'Row ' . ($i + 1) . ': ' . $employeeText . ' has no card!';
              $errors[] = $errorMessage;
              goto has_error;
            }
            $group = $employee->getPrimaryGroup();

            $useOldCard = false;
            $retired = Util::meta($uc, 'retiredRapidAccount');
            if ($group->isRapid() && BotmAPI::isAccountNumberValid($uc->getAccountNumber()) && $retired ) {
              $useOldCard = true;
            }
            // check if the member has BotmUserAccount if not, create for the member
            if (!$employee->ensureConfig()->getBotmUserAccountId() && Util::isServer()) {
              try {
                $api = BotmAPI::getForUserCard($uc);
                $api->ensureBotmUserAccountId($employee);
              } catch (\Throwable $t) {
                SlackService::alert('Create BOTM User Account error, please check this', [
                  'employer' => $group->getName(),
                  'member' => $uc->getUser()->getSignature(),
                  'errorMessage' => Util::getExceptionBrief($t),
                ], SlackService::GROUP_TRANSFER_MEX_DEV);
              }
            }
            Util::consoleLine($msgPrefix . ' Ensured the BOTM user account');

            $passCheckCardStatusAndNumber = Config::get('load_to_botm_account_flag');

            if (!$platform->isFaasPlatforms() && !$passCheckCardStatusAndNumber) {
              if (!$uc->getAccountNumber() || (!$uc->isIssued() && !$useOldCard)) {
                $errorMessage = 'Row ' . ($i + 1) . ': ' . $employeeText . ' has not been assigned a card!';
                $errors[] = $errorMessage;
                goto has_error;
              }
            }

            if (!$passCheckCardStatusAndNumber && ucfirst($uc->getStatus()) !== 'Active' && !Data::get('card_on_hold_by_password_' . $employee->getId()) && !$useOldCard ) {
              $errorMessage = 'Row ' . ($i + 1) . ': The ' . $employeeText . ' card is not active!';
              $errors[] = $errorMessage;
              goto has_error;
            }
            $payout = -1;
            if ($importPayoutRecord && in_array($importPayoutRecord->getEmployer()->getId(), TransferMexBundle::customEmployers())) {
              if ($fileType == 'A') {
                $row[0] = str_replace('$', '', $row[0]);
                $payout = $row[0] > 0 ? (float)($row[0]) : (float)($row[0]) * -1;
              } else if ($fileType == 'B') {
                $row[2] = str_replace('$', '', $row[2]);
                $payout = $row[2] > 0 ? (float)($row[2]) : (float)($row[2]) * -1;
              }
            }
            if ($payout < 0) {
              $errorMessage = 'Row ' . ($i + 1) . ': Payout amount must be greater than 0!';
              $errors[] = $errorMessage;
              goto has_error;
            }
            if (preg_match('/\.\d{3,}/', $payout)) {
              $errorMessage = 'Row ' . ($i + 1) . ': Payout amount more than 2 decimal places! Amount is ' . $payout;
              $errors[] = $errorMessage;
              goto has_error;
            }
            if ($platform->isLoadConfigureEnabled() && $group && !$group->getMemberLoadType()) {
              // $lastPayout = Util::em()->getRepository(UserCardTransaction::class)
              //         ->createQueryBuilder('uct')
              //         ->where(Util::expr()->eq('uct.tranCode', ':type'))
              //         ->andwhere(Util::expr()->in('uct.accountStatus', ':status'))
              //         ->andwhere(Util::expr()->like('uct.tranDesc', ':employeeId'))
              //         ->setParameter('type', 'Pay_employee')
              //         ->setParameter('status', [EmployerPayout::PAYOUT_SYNC_PENDING, EmployerPayout::PAYOUT_COMPLETE])
              //         ->setParameter('employeeId', '%'. $employeeText. ':' . $employee->getId() . '%')
              //         ->orderBy('uct.txnTime', 'desc')
              //         ->setMaxResults(1)
              //         ->getQuery()
              //         ->getOneOrNullResult();
              $lastPayout = Data::getArray('employee_last_payout_' . $employee->getId() );
              if(count($lastPayout)) {
                $errorMessage = 'Row ' . ($i + 1) . ': The ' . $employeeText . ' card is not reloadable';
                $errors[] = $errorMessage;
                goto has_error;
              }
            }
            $result[] = [
              'Row' => $i + 1,
              'User ID' => $employee->getId(),
              'Email' => $employee->getEmail(),
              'First Name' => $employee->getFirstName(),
              'Last Name' => $employee->getLastName(),
              'User Type' => $employeeText,
              'Card Status' => $uc->getAccountNumber() && $uc->isIssued() ? ucfirst($uc->getStatus()) : '',
              'Barcode Number' => $uc ? $uc->getAccountNumber() : '',
              'Routing Number' => Util::isServer() && $uc ? $uc->getProcessorApiImpl()->getRoutingNumber($uc) : null,
              'Last Payout Date' => '',
              'Last Payout Amount' => '',
              'amount' => $payout,
              'Payment Date' => $fileType == 'A' ? $row[5] : '',
              'Balance' => $uc->getAccountNumber() ? Money::formatWhen($uc->getBalance()) : '',
              'Status' => $employee->getStatus() === User::STATUS_ACTIVE ? 'Active' : 'Inactive'
            ];
            has_error:
            if (count($errors) && $errorMessage) {
                Util::consoleLine($msgPrefix . $errorMessage);
                $errorRecord = new ImportPayoutErrorRecord();
                $payout = 0;
                if ($fileType == 'A') {
                  $row[0] = str_replace('$', '', $row[0]);
                  $payout = $row[0] > 0 ? (float)($row[0]) : (float)($row[0]) * -1;
                } else if ($fileType == 'B') {
                  $row[2] = str_replace('$', '', $row[2]);
                  $payout = $row[2] > 0 ? (float)($row[2]) : (float)($row[2]) * -1;
                }
                $errorRecord->setReason($errorMessage)
                            ->setMember($employee??null)
                            ->setAmount((float)($payout))
                            ->setStatus(ImportPayoutErrorRecord::STATUS_FAILED)
                            ->setExternalEmployeeId($employee? $employee->getExternalId() : null)
                            ->setImportPayoutRecord($importPayoutRecord)
                            ->persist();
            }
        }

        if ($isCronImport) {
          $importPayoutRecord->setFailedCount(count($errors))
                            ->persist();
        }
        return $result;
    }

    public static function getAccountNumber($routingStr, $cardStr) {
      if (Util::endsWith('*********' ,$routingStr)) {
        return strlen($cardStr) > 11 ? substr($cardStr, -11) : $cardStr;
      }
      $isBotmCard = false;
      $routingNumbers = Config::array('transfermex_payroll_routing_numbers');
      foreach ($routingNumbers as $routingNumber) {
        if (Util::endsWith($routingNumber, $routingStr)) {
          $isBotmCard = true;
        }
      }
      return $isBotmCard ? $cardStr : '';
    }
    public static function  checkPayoutFromCsvFile($path, $platform, $employeeText, $isCronImport = false, $importPayoutRecord = null)
    {
        $em = Util::em();
        $result = [];
        $excel = Excel::openExcel($path);
        $sheet = $excel->getActiveSheet();
        $rows = $sheet->toArray();

        $count = count($rows);
        Util::consoleLine('Parsed ' . $count . ' files from the method ' . __METHOD__ . ' for file ' . $path);

        $errors = [];
        $errorMessage = '';
        $str = $platform->isFaasPlatforms() ? 'Member: ' : 'Employee: ';
        if (!$isCronImport) {
            $total = 0;
            foreach ($rows as $i => $row) {
                if ($i <= 0) {
                    continue;
                }
                $row[2] = str_replace('$', '', $row[2]);
                $row[1] = str_replace(' ', '', $row[1]);
                $accountNumber = $accountNumber = self::getAccountNumber($row[0], $row[1]);
                $result[] = [
                    $rows[0][0] =>  $row[0],
                    $rows[0][1] =>  $accountNumber,
                    $rows[0][2] =>  $row[2] * 100,
                    $rows[0][3] =>  $row[3],
                    $rows[0][4] =>  $row[4]
                ];
                $total+= $row[2] * 100;
            }
            Util::consoleLine('Checked data in non cronjob mode');
            return ['type' => 'cronJob', 'data' => $result, 'total' => $total];
        }
        $required = [
            1 => 'Account Number',
            2 => 'Account'
        ];

        foreach ($rows as $i => $row) {
            $msgPrefix = $i . '/' . $count . ': ';
            $errorMessage = null;
            if ($i <= 0) {
                continue;
            }
            if ($row[2] === null || $row[2] === '' || $row[2] === 0) {
              continue;
            }
            Util::consoleLine($msgPrefix . 'Pre-checking row');

            $employee = null;
            $accountNumber = null;

            $row[2] = str_replace('$', '', $row[2]);
            foreach ($required as $index => $name) {
                $row[$index] = trim($row[$index]);
                if ($platform->isTransferMex() && !$index) {
                  continue;
                }
                if ($row[$index] === null || $row[$index] === '') {
                    $errorMessage = 'Row ' . ($i + 1) . ': Required field "' . $name . '" is empty!';
                    $errors[] = $errorMessage;
                    goto has_error;
                }
            }

            if ($platform->isTransferMex() && trim($row[1]) && str_replace(' ', '', $row[1]) !== '') {
                $row[1] = str_replace(' ', '', $row[1]);
                $accountNumber = self::getAccountNumber($row[0], $row[1]);
                Util::consoleLine($msgPrefix . 'Parsed the account number');
                $employeeUc = Util::em()->getRepository(\CoreBundle\Entity\UserCard::class)->findOneBy(['accountNumber' => $accountNumber]);
                if (!$employeeUc) {
                  $employeeUc = UserCard::findByAccountNumberWithLegacies($accountNumber);
                }
                $employee = $employeeUc ? $employeeUc->getUser() : null;
            }
            if (!$employee && strlen($accountNumber) > 11) {
              $employeeUc = UserCard::findByDepositNumber($accountNumber);
              $employee = $employeeUc ? $employeeUc->getUser() : null;
              Util::consoleLine($msgPrefix . 'Completed finding the user card by deposit number');
            }

            if (!$employee) {
              $errorMessage = 'Row ' . ($i + 1) . ':' . $employeeText .' could not be found by account number with ' . $accountNumber . '!';
              $errors[] = $errorMessage;
              goto has_error;
            }
            Util::consoleLine($msgPrefix . ' Completed querying the employee ' . $employee->getId());

            $employer =  $employee->getPrimaryGroupAdmin();
            if (!$employer || $importPayoutRecord->getEmployer()->getId() !== $employer->getId()) {
                $errorMessage = $str . $employee->getSignature() . ' does not belong to this employer ' . $importPayoutRecord->getEmployer()->getId();
                $errors[] = $errorMessage;
                goto has_error;
            }

            $uc = $employee->getCurrentPlatformCard();
            if (!$uc) {
              $errorMessage = 'Row ' . ($i + 1) . ': ' . $employeeText . ' has no card!';
              $errors[] = $errorMessage;
              goto has_error;
            }
            $group = $employee->getPrimaryGroup();

            $useOldCard = false;
            $retired = Util::meta($uc, 'retiredRapidAccount');
            if ($group->isRapid() && BotmAPI::isAccountNumberValid($uc->getAccountNumber()) && $retired ) {
              $useOldCard = true;
            }

             // check if the member has BotmUserAccount if not, create for the member
             if (!$employee->ensureConfig()->getBotmUserAccountId() && Util::isServer()) {
              try{
                $api = BotmAPI::getForUserCard($uc);
                $api->ensureBotmUserAccountId($employee);
              } catch (\Throwable $t) {
                SlackService::alert('Create BOTM User Account error, please check this', [
                  'employer' => $group->getName(),
                  'member' => $uc->getUser()->getSignature(),
                  'errorMessage' => Util::getExceptionBrief($t),
                ], SlackService::GROUP_TRANSFER_MEX_DEV);
              }
            }
            Util::consoleLine($msgPrefix . ' Ensured the BOTM user account');

            $passCheckCardStatusAndNumber = Config::get('load_to_botm_account_flag');

            if (!$passCheckCardStatusAndNumber && !$platform->isFaasPlatforms()) {
              if (!$uc->getAccountNumber() || (!$uc->isIssued() && !$useOldCard)) {
                $errorMessage = 'Row ' . ($i + 1) . ': ' . $employeeText . ' has not been assigned a card!';
                $errors[] = $errorMessage;
                goto has_error;
              }
            }

            if (!$passCheckCardStatusAndNumber && ucfirst($uc->getStatus()) !== 'Active' && !Data::get('card_on_hold_by_password_' . $employee->getId()) && !$useOldCard) {
              $errorMessage = 'Row ' . ($i + 1) . ': The ' . $employeeText . ' card is not active!';
              $errors[] = $errorMessage;
              goto has_error;
            }

            $payout = (float)($row[2]);
            if ($payout < 0) {
              $errorMessage = 'Row ' . ($i + 1) . ': Payout amount must be greater than 0!';
              $errors[] = $errorMessage;
              goto has_error;
            }
            if (preg_match('/\.\d{3,}/', $payout)) {
              $errorMessage = 'Row ' . ($i + 1) . ': Payout amount more than 2 decimal places! Amount is ' . $payout;
              $errors[] = $errorMessage;
              goto has_error;
            }

            if ($platform->isLoadConfigureEnabled() && $group && !$group->getMemberLoadType()) {
              // $lastPayout = Util::em()->getRepository(UserCardTransaction::class)
              //                         ->createQueryBuilder('uct')
              //                         ->where(Util::expr()->eq('uct.tranCode', ':type'))
              //                         ->andwhere(Util::expr()->in('uct.accountStatus', ':status'))
              //                         ->andwhere(Util::expr()->like('uct.tranDesc', ':employeeId'))
              //                         ->setParameter('type', 'Pay_employee')
              //                         ->setParameter('status', [EmployerPayout::PAYOUT_SYNC_PENDING, EmployerPayout::PAYOUT_COMPLETE])
              //                         ->setParameter('employeeId', '%'. $employeeText. ':' . $employee->getId() . '%')
              //                         ->orderBy('uct.txnTime', 'desc')
              //                         ->setMaxResults(1)
              //                         ->getQuery()
              //                         ->getOneOrNullResult();
              $lastPayout = Data::getArray('employee_last_payout_' . $employee->getId() );
              Util::consoleLine($msgPrefix . 'Completed finding the last payout ' . (count($lastPayout) ? $lastPayout['id']: ''));
              if(count($lastPayout)) {
                $errorMessage = 'Row ' . ($i + 1) . ': The ' . $employeeText . ' card is not reloadable';
                $errors[] = $errorMessage;
                goto has_error;
              }
            }
            $result[] = [
              'Row' => $i + 1,
              'User ID' => $employee->getId(),
              'Email' => $employee->getEmail(),
              'First Name' => $employee->getFirstName(),
              'Last Name' => $employee->getLastName(),
              'User Type' => $employeeText,
              'Card Status' => $uc->getAccountNumber() && $uc->isIssued() ? ucfirst($uc->getStatus()) : '',
              'Barcode Number' => $uc ? $uc->getAccountNumber() : '',
              'Routing Number' => Util::isServer() && $uc ? $uc->getProcessorApiImpl()->getRoutingNumber($uc) : '',
              'Last Payout Date' => '',
              'Last Payout Amount' => '',
              'amount' => $payout,
              'Balance' => $uc->getAccountNumber() ? Money::formatWhen($uc->getBalance()) : '',
              'Status' => $employee->getStatus() === User::STATUS_ACTIVE ? 'Active' : 'Inactive'
            ];
            has_error:
            if (count($errors) && $errorMessage) {
                Util::consoleLine($msgPrefix . $errorMessage);
                $errorRecord = new ImportPayoutErrorRecord();
                $errorRecord->setReason($errorMessage)
                            ->setMember($employee??null)
                            ->setAmount((float)($row[2] * 100))
                            ->setStatus(ImportPayoutErrorRecord::STATUS_FAILED)
                            ->setExternalEmployeeId($employee? $employee->getExternalId() : null)
                            ->setImportPayoutRecord($importPayoutRecord)
                            ->persist();
            }
        }

        if ($isCronImport) {
          $importPayoutRecord->setFailedCount(count($errors))
                            ->persist();
        }
        return $result;
    }

    public static function  checkPayoutFromExcleFile($path, $platform, $employeeText, $isCronImport = false, $importPayoutRecord = null)
    {
        $em = Util::em();
        $result = [];
        $excel = Excel::openExcel($path);
        $sheet = $excel->getActiveSheet();
        $rows = $sheet->toArray();

        $count = count($rows);
        Util::consoleLine('Parsed ' . $count . ' files from the method ' . __METHOD__ . ' for file ' . $path);

        $errors = [];
        $candidates = [];
        $errorMessage = '';
        $accounList = array_filter($rows, function ($item) {
          return !($item[4] === null || $item[4] === '' || $item[4] === 0);
        });

        if (!$isCronImport) {
            $total = 0;
            foreach ($rows as $i => $row) {
                if ($i <= 0) {
                    continue;
                }

                if ($row[4] === null || $row[4] === '' || $row[4] === 0) {
                  continue;
                }

                $row[4] = str_replace('$', '', $row[4]);
                $result[] = [
                    $rows[0][0] =>  $row[0],
                    $rows[0][1] =>  $row[1],
                    $rows[0][2] =>  $row[2],
                    $rows[0][3] =>  $row[3],
                    $rows[0][4] =>  $row[4] ? $row[4] * 100 : 0
                ];
                $total += $row[4] ? $row[4] * 100 : 0;
            }
            Util::consoleLine('Checked data in non cronjob mode');
            return ['type' => 'cronJob', 'data' => $result, 'total' => $total];
        }
        $str = $platform->isFaasPlatforms() ? 'Member ID' : 'Employee ID';
        $required = [
            0 => $str,
            1 => 'First Name',
            2 => 'Last Name'
            // 3 => 'Account Number'
            // 3 => 'Payout Amount'
        ];

        foreach ($rows as $i => $row) {
            $msgPrefix = $i . '/' . $count . ': ';
            $errorMessage = null;
            if ($i <= 0) {
                continue;
            }
            if ($row[4] === null || $row[4] === '' || $row[4] === 0) {
              continue;
            }
            Util::consoleLine($msgPrefix . 'Pre-checking row');

            $row[4] = str_replace('$', '', $row[4]);
            $employee = null;
            $eid = null;
            $payout = null;
            foreach ($required as $index => $name) {
                $row[$index] = trim($row[$index]);
                if ($platform->isTransferMex() && !$index) {
                  continue;
                }
                if ($row[$index] === null || $row[$index] === '') {
                    $errorMessage = 'Row ' . ($i + 1) . ': Required field "' . $name . '" is empty!';
                    $errors[] = $errorMessage;
                    goto has_error;
                }
            }

            if ($platform->isTransferMex()) {
                $employeeUc = null;
                if (trim($row[3]) && strlen(trim($row[3])) > 14) {
                  $employeeUc = UserCard::findByDepositNumber(trim($row[3]));
                }
                if (!$employeeUc && trim($row[3]) && trim($row[3]) !== '') {
                  $employeeUc = Util::em()->getRepository(\CoreBundle\Entity\UserCard::class)->findOneBy(['accountNumber' => trim($row[3])]);
                  if (!$employeeUc) {
                    Util::consoleLine($msgPrefix . 'Querying by account number');
                    $employeeUc = UserCard::findByAccountNumberWithLegacies(trim($row[3]));
                  }
                }
                $employee = $employeeUc ? $employeeUc->getUser() : null;
            } else {
              $eid = (int)$row[0];
              $employee = User::find($eid);
            }
            if (!$employee) {
              $errorMessage = $platform->isTransferMex() ? 'Row ' . ($i + 1) . ':' . $employeeText .' could not be found by account number with '. trim($row[3]) . '!' : 'Row ' . ($i + 1) . ': Unknown ' . $employeeText . ' ID!';
              $errors[] = $errorMessage;
              goto has_error;
            }
            Util::consoleLine($msgPrefix . ' Completed querying the employee ' . $employee->getId());

            $employer =  $employee->getPrimaryGroupAdmin();
            if (!$employer || $importPayoutRecord->getEmployer()->getId() !== $employer->getId()) {
                $errorMessage = $str . $employee->getSignature() . ' does not belong to this employer ' . $importPayoutRecord->getEmployer()->getId();
                $errors[] = $errorMessage;
                goto has_error;
            }

            $uc = $employee->getCurrentPlatformCard();
            if (!$uc) {
              $errorMessage = 'Row ' . ($i + 1) . ': ' . $employeeText . ' has no card!';
              $errors[] = $errorMessage;
              goto has_error;
            }
            $group = $employee->getPrimaryGroup();

            $useOldCard = false;
            $retired = Util::meta($uc, 'retiredRapidAccount');
            if ($group->isRapid() && BotmAPI::isAccountNumberValid($uc->getAccountNumber()) && $retired ) {
              $useOldCard = true;
            }

             // check if the member has BotmUserAccount if not, create for the member
             if (!$employee->ensureConfig()->getBotmUserAccountId() && Util::isServer()) {
              try {
                $api = BotmAPI::getForUserCard($uc);
                $api->ensureBotmUserAccountId($employee);
              } catch (\Throwable $t) {
                SlackService::alert('Create BOTM User Account error, please check this', [
                  'employer' => $group->getName(),
                  'member' => $uc->getUser()->getSignature(),
                  'errorMessage' => Util::getExceptionBrief($t),
                ], SlackService::GROUP_TRANSFER_MEX_DEV);
              }
            }
            Util::consoleLine($msgPrefix . ' Ensured the BOTM user account');

            $passCheckCardStatusAndNumber = Config::get('load_to_botm_account_flag');

            if (!$passCheckCardStatusAndNumber && !$platform->isFaasPlatforms()) {
              if (!$uc->getAccountNumber() || (!$uc->isIssued() && !$useOldCard)) {
                $errorMessage = 'Row ' . ($i + 1) . ': ' . $employeeText . ' has not been assigned a card!';
                $errors[] = $errorMessage;
                goto has_error;
              }
            }
            $userStatus = $employee->getStatus();
            if ($platform->isFaasPlatforms()) {
              $userStatus = $employee->getFaasStatus($platform->isKYCRequired(), false);
            }

            if (!$passCheckCardStatusAndNumber && ucfirst($uc->getStatus()) !== 'Active' && !Data::get('card_on_hold_by_password_' . $employee->getId())  && !$useOldCard) {
              $errorMessage = 'Row ' . ($i + 1) . ': The ' . $employeeText . ' card is not active!';
              $errors[] = $errorMessage;
              goto has_error;
            }

            $payout = (float)($row[4]);
            if ($payout < 0) {
              $errorMessage = 'Row ' . ($i + 1) . ': Payout amount must be greater than 0!';
              $errors[] = $errorMessage;
              goto has_error;
            }
            if (preg_match('/\.\d{3,}/', $payout)) {
              $errorMessage = 'Row ' . ($i + 1) . ': Payout amount more than 2 decimal places! Amount is ' . $row[4];
              $errors[] = $errorMessage;
              goto has_error;
            }
            $group = $employee->getPrimaryGroup();

            if ($platform->isLoadConfigureEnabled() && $group && !$group->getMemberLoadType()) {
              // $lastPayout = Util::em()->getRepository(UserCardTransaction::class)
              //                         ->createQueryBuilder('uct')
              //                         ->where(Util::expr()->eq('uct.tranCode', ':type'))
              //                         ->andwhere(Util::expr()->in('uct.accountStatus', ':status'))
              //                         ->andwhere(Util::expr()->like('uct.tranDesc', ':employeeId'))
              //                         ->setParameter('type', 'Pay_employee')
              //                         ->setParameter('status', [EmployerPayout::PAYOUT_SYNC_PENDING, EmployerPayout::PAYOUT_COMPLETE])
              //                         ->setParameter('employeeId', '%'. $employeeText. ':' . $employee->getId() . '%')
              //                         ->orderBy('uct.txnTime', 'desc')
              //                         ->setMaxResults(1)
              //                         ->getQuery()
              //                         ->getOneOrNullResult();
              //                         // Log::debug($platform->isLoadConfigureEnabled());
              //                         // Log::debug($group->getMemberLoadType());
              //                         // Log::debug($group->getId());
              //                         // Log::debug($lastPayout);
              // Util::consoleLine($msgPrefix . 'Completed finding the last payout ' . $lastPayout?->getId());
              $lastPayout = Data::getArray('employee_last_payout_' . $employee->getId() );
              Util::consoleLine($msgPrefix . 'Completed finding the last payout ' . (count($lastPayout) ? $lastPayout['id']: ''));
              if(count($lastPayout)) {
                $errorMessage = 'Row ' . ($i + 1) . ': The ' . $employeeText . ' card is not reloadable';
                $errors[] = $errorMessage;
                goto has_error;
              }
            }
            $result[] = [
              'Row' => $i + 1,
              'User ID' => $employee->getId(),
              'Email' => $employee->getEmail(),
              'First Name' => $employee->getFirstName(),
              'Last Name' => $employee->getLastName(),
              'User Type' => $employeeText,
              'Card Status' => $uc->getAccountNumber() && $uc->isIssued() ? ucfirst($uc->getStatus()) : '',
              'Barcode Number' => $uc ? $uc->getAccountNumber() : '',
              'Routing Number' =>  Util::isServer() && $uc ? $uc->getProcessorApiImpl()->getRoutingNumber($uc) : null,
              'Last Payout Date' => '',
              'Last Payout Amount' => '',
              'amount' => $payout,
              'Balance' => $uc->getAccountNumber() ? Money::formatWhen($uc->getBalance()) : '',
              'Status' => $employee->getStatus() === User::STATUS_ACTIVE ? 'Active' : 'Inactive'
            ];
            has_error:
            if (count($errors) && $errorMessage) {
                Util::consoleLine($msgPrefix . $errorMessage);
                $errorRecord = new ImportPayoutErrorRecord();
                $errorRecord->setReason($errorMessage)
                            ->setMember($employee??null)
                            ->setAmount((float)($row[4] * 100))
                            ->setStatus(ImportPayoutErrorRecord::STATUS_FAILED)
                            ->setExternalEmployeeId($employee? $employee->getExternalId() : null)
                            ->setImportPayoutRecord($importPayoutRecord)
                            ->persist();
            }
        }

        if ($isCronImport) {
          $importPayoutRecord->setFailedCount(count($errors))
                            ->persist();
        }
        return $result;
    }

    public static function  checkPayoutFromNachaFile($path, $platform, $employeeText, $isCronImport = false, $importPayoutRecord = null)
    {
        $em = Util::em();
        $result = [];
        $errors = [];
        $candidates = [];
        $errorMessage = '';
        $rows = NachaParseService::parseFile($path);

        if (!$isCronImport) {
            return ['type' => 'cronJob', 'data' => $rows];
        }
        if (!is_array($rows)) {
          return [];
        }
        $str = $platform->isFaasPlatforms() ? 'Member: ' : 'Employee: ';

        $count = count($rows);
        Util::consoleLine('Parsed ' . $count . ' files from the method ' . __METHOD__ . ' for file ' . $path);

        foreach ($rows as $i => $row) {
            $errorMessage = null;
            $eid = $row['externalEmployeeId'];
            $msgPrefix = $i . '/' . $count . ': ';
            Util::consoleLine($msgPrefix . 'Checking the employee with external ID: ' . $eid);

            $payout = $row['amount'];
            $employee = null;
            Log::debug('Start create payment for ' . $i);
            if (!$row['isValid']) {
              $errorMessage = 'The RT number is wrong, please confirm whether the data is correct and process the data manually after modification. Data: ' . $row['data'];
              $errors[] = $errorMessage;
              goto has_error;
            }
            $accountNumber = $row['accountNumber'];
            if (!$row['isBotmCard']) {
              $employeeUc = Util::em()->getRepository(\CoreBundle\Entity\UserCard::class)->findOneBy(['accountNumber' => $accountNumber]);
            } else {
              $employeeUc = Util::em()->getRepository(\CoreBundle\Entity\UserCard::class)->findOneBy(['nickName' => hash_hmac('sha256', $accountNumber, Util::getConfigKey('botm_account_number_hash'))]);
            }
            Util::consoleLine($msgPrefix . 'Completed querying 1 by the account number');

            if (!$employeeUc) {
              $employeeUc = UserCard::findByAccountNumberWithLegacies($accountNumber);
              Util::consoleLine($msgPrefix . 'Completed querying 2 by the account number');
            }
            $employee = $employeeUc ? $employeeUc->getUser() : null;

            if (!$employee && $eid) {
              $employee = User::findByExternalId( $eid,  $importPayoutRecord ? $importPayoutRecord->getEmployer() : null );
              Util::consoleLine($msgPrefix . 'Completed querying 3 by the external ID ' . $eid);
            }

            if (!$employee) {
                $errorMessage = 'Could not be found by account number with ' . $accountNumber . '!';
                $errors[] = $errorMessage;
                goto has_error;
            }

            $employer =  $employee->getPrimaryGroupAdmin();
            if (!$employer || $importPayoutRecord->getEmployer()->getId() !== $employer->getId()) {
                $errorMessage = $str . $employee->getSignature() . ' does not belong to this employer ' . $importPayoutRecord->getEmployer()->getId();
                $errors[] = $errorMessage;
                goto has_error;
            }
            $uc = $employee->getCurrentPlatformCard();
            if (!$uc) {
                $errorMessage = $employee->getSignature() .' has no card!';
                $errors[] = $errorMessage;
                goto has_error;
            }
            $group = $employee->getPrimaryGroup();

             // check if the member has BotmUserAccount if not, create for the member
             if (!$employee->ensureConfig()->getBotmUserAccountId() && Util::isServer()) {
              try{
                $api = BotmAPI::getForUserCard($uc);
                $api->ensureBotmUserAccountId($employee);
              } catch (\Throwable $t) {
                SlackService::alert('Create BOTM User Account error, please check this', [
                  'employer' => $group->getName(),
                  'member' => $uc->getUser()->getSignature(),
                  'errorMessage' => Util::getExceptionBrief($t),
                ], SlackService::GROUP_TRANSFER_MEX_DEV);
              }
            }
            Util::consoleLine($msgPrefix . 'Ensured the BOTM user account');

            $passCheckCardStatusAndNumber = Config::get('load_to_botm_account_flag');
            if ((!$uc->getAccountNumber() && !$passCheckCardStatusAndNumber)) {
                 $errorMessage = $str . $employee->getSignature() . ' has not assigned a card of the new provider!';
                 $errors[] = $errorMessage;
                 goto has_error;
            }

            if ($payout < 0) {
                $errorMessage = $str . $employee->getSignature() .  ', Payout amount must be greater than 0!';
                $errors[] = $errorMessage;
                goto has_error;
            }

            if ($platform->isLoadConfigureEnabled() && $group && !$group->getMemberLoadType()) {
                // $lastPayout = Util::em()->getRepository(UserCardTransaction::class)
                //                         ->createQueryBuilder('uct')
                //                         ->where(Util::expr()->eq('uct.tranCode', ':type'))
                //                         ->andwhere(Util::expr()->in('uct.accountStatus', ':status'))
                //                         ->andwhere(Util::expr()->like('uct.tranDesc', ':employeeId'))
                //                         ->setParameter('type', 'Pay_employee')
                //                         ->setParameter('status', [EmployerPayout::PAYOUT_SYNC_PENDING, EmployerPayout::PAYOUT_COMPLETE])
                //                         ->setParameter('employeeId', '%'. $employeeText. ':' . $employee->getId() . '%')
                //                         ->orderBy('uct.txnTime', 'desc')
                //                         ->setMaxResults(1)
                //                         ->getQuery()
                //                         ->getOneOrNullResult();
                // Util::consoleLine($msgPrefix . 'Completed querying the last payout: ' . $lastPayout?->getId());
                $lastPayout = Data::getArray('employee_last_payout_' . $employee->getId() );
                Util::consoleLine($msgPrefix . 'Completed finding the last payout ' . (count($lastPayout) ? $lastPayout['id']: ''));
                if(count($lastPayout)) {
                  $errorMessage = 'The  card is not reloadable';
                  $errors[] = $errorMessage;
                  goto has_error;
                }
            }
            $result[] = [
                'Row' => $i + 1,
                'User ID' => $employee->getId(),
                'Email' => $employee->getEmail(),
                'First Name' => $employee->getFirstName(),
                'Last Name' => $employee->getLastName(),
                'User Type' => $employeeText,
                'Card Status' => $uc->getAccountNumber() && $uc->isIssued() ? ucfirst($uc->getStatus()) : '',
                'Barcode Number' => $uc ? $uc->getAccountNumber() : '',
                'Routing Number' =>  Util::isServer() && $uc ? $uc->getProcessorApiImpl()->getRoutingNumber($uc) : '',
                'Last Payout Date' => '',
                'Last Payout Amount' => '',
                'amount' => $payout,
                'Balance' => $uc->getAccountNumber() ? Money::formatWhen($uc->getBalance()) : '',
                'Status' => $employee->getStatus() === User::STATUS_ACTIVE ? 'Active' : 'Inactive'
            ];
            has_error:
            if ($errorMessage) {
                Util::consoleLine($msgPrefix . $errorMessage);
                $errorRecord = new ImportPayoutErrorRecord();
                $errorRecord->setReason($errorMessage)
                    ->setAmount($payout)
                    ->setExternalEmployeeId($eid)
                    ->setStatus(ImportPayoutErrorRecord::STATUS_FAILED)
                    ->setMember($employee ?? null)
                    ->setImportPayoutRecord($importPayoutRecord)
                    ->persist();
            }
        }

        if ($isCronImport) {
            $importPayoutRecord->setFailedCount(count($errors))
                ->persist();
        }
        return $result;
    }

    public static function getLastImportPayout($employer, $platfrom, $user) {
      $q = Util::em()->getRepository(ImportPayoutRecord::class)
              ->createQueryBuilder('im')
              ->where(Util::expr()->eq('im.employer', $employer->getId()))
              ->andWhere(Util::expr()->eq('im.platform', $platfrom->getId()));
      if ($user->inTeams([
        Role::ROLE_TRANSFER_MEX_EMPLOYER_ADMIN,
        Role::ROLE_FAAS_CLIENT_ADMIN,
        ])) {
          $q->andWhere(Util::expr()->eq('im.createBy', $user->getId()));
        }

      $last = $q ->orderBy('im.createdAt', 'desc')
              ->setMaxResults(1)
              ->getQuery()
              ->getOneOrNullResult();
      $res = null;
      if ($last) {
        $res = [
          'id'     => $last->getId(),
          'createdAt' => Util::formatDateTime($last->getCreatedAt(), Util::DATE_TIME_FORMAT, 'EST'),
          'success' => $last->getSuccessCount() ? $last->getSuccessCount() : 0,
          'fail'    => $last->getFailedCount() ? $last->getFailedCount() : 0,
          'status'  => $last->getStatus() === ImportPayoutRecord::IMPORT_COMPLETE ? 'Completed' : 'In Progress'
        ];
      }
      return $res;
    }

    /**
     * @param Platform|NULL $platform
     * @param string $type user or card
     *
     * @return array
     */
    public static function getAllBarcodesUsers(Platform $platform = null, $type = 'user')
    {
        $platform = $platform ?? Util::$platform;
        $cardProgram = $platform->getCardPrograms()->first();
        $expr = Util::expr();
        $cards = [
            CardProgramCardType::getForCardProgram($cardProgram),
        ];
        $ucs = Util::em()->getRepository(\CoreBundle\Entity\UserCard::class)
            ->createQueryBuilder('uc')
            ->where($expr->in('uc.card', ':cards'))
            ->andWhere($expr->orX(
                $expr->isNotNull('uc.accountNumber'),
                $expr->like('uc.meta', ':meta')
            ))
            ->setParameter('cards', $cards)
            ->setParameter('meta', '%"oldAccountNumbers"%')
            ->getQuery()
            ->getResult();
        $barcodes = [];
        /** @var UserCard $uc */
        foreach ($ucs as $uc) {
            $an = trim($uc->getAccountNumber());
            if ($type === 'user') {
                $uid = $uc->getUser()->getId();
            } else {
                $uid = $uc->getId();
            }
            $barcodes[$an] = $uid;

            $olds = Util::meta($uc, 'oldAccountNumbers') ?? [];
            foreach ($olds as $an) {
                $an = trim($an);
                $barcodes[$an] = $uid;
            }
        }
        return $barcodes;
    }

    public static function findDuplicatedAccountNumbers($returnAll = false)
    {
        $expr = Util::expr();
        $cards = [
            CardProgramCardType::getForCardProgram(CardProgram::transferMexUSD()),
        ];
        $ucs = Util::em()->getRepository(\CoreBundle\Entity\UserCard::class)
            ->createQueryBuilder('uc')
            ->where($expr->in('uc.card', ':cards'))
            ->andWhere($expr->orX(
                $expr->isNotNull('uc.accountNumber'),
                $expr->like('uc.meta', ':meta')
            ))
            ->setParameter('cards', $cards)
            ->setParameter('meta', '%"oldAccountNumbers"%')
            ->select('uc.id')
            ->distinct()
            ->getQuery()
            ->getArrayResult();
        $barcodes = [];
        foreach ($ucs as $i => $r) {
            if ($i && $i % 500 === 0) {
                Util::completeDoctrineBatch();
            }
            /** @var UserCard $uc */
            $uc = UserCard::find($r['id']);
            $an = trim($uc->getAccountNumber());
            if (!isset($barcodes[$an])) {
                $barcodes[$an] = [];
            }
            $id = $uc->getId();
            $uerId = $uc->getUser()->getId();
            $barcodes[$an][$id] = [
                'member' => $uerId,
                'an' => $an,
            ];
            $olds = Util::meta($uc, 'oldAccountNumbers') ?? [];
            foreach ($olds as $an) {
                $an = trim($an);
                if (!isset($barcodes[$an])) {
                    $barcodes[$an] = [];
                }
                $barcodes[$an][$id] = [
                    'member' => $uerId,
                    'an' => $an,
                ];
            }
        }

        $dupes = [];
        foreach ($barcodes as $an => $ucs) {
            if (count($ucs) > 1) {
                $dupes[$an] = $ucs;
            }
        }

        if ($returnAll) {
            return compact('dupes', 'barcodes');
        }

        return $dupes;
    }

    public static function sendMessageToMembers($members, $content, $public = false, $callback = null)
    {
        try {
            if ($members instanceof User) {
                $members = [$members];
            }
            $ids = [];
            foreach ($members as $member) {
                if ($member instanceof User) {
                    $ids[] = $member->getId();
                } else if (is_array($member)) {
                    $ids[] = $member['id'];
                } else if (is_numeric($member)) {
                    $ids[] = (int)$member;
                }
            }

            $max = self::MAX_MESSAGE_COUNT;
            if (count($ids) > $max) {
                $ids = array_slice($ids, 0, $max);
            }

            if ($callback) {
                $ids = $callback($ids);
            }

            if ($ids) {
                $channel = $public ? 'client' : 'internal';
                Service::sendAsync('/t/cron/mex/members/0/multi/send-message?channel=' . $channel . '&content='
                                   . urlencode($content) . '&ids=' . implode(',', $ids), true);
            }

            return array_values($ids);
        } catch (\Exception $ex) {
            Log::debug('Failed to send message to members: ' . $ex->getMessage(), [
                'members' => count($members),
                'content' => $content,
                'public' => $public,
            ]);
            return [];
        }
    }

    public static function sendChangePinMessageToMembers($members, $content)
    {
        if ($members instanceof User) {
            Data::del('mex_pin_remind_' . $members->getId());
        }
        return self::sendMessageToMembers($members, $content, false, function ($userIds) {
            $sendTo = [];
            foreach ($userIds as $userId) {
                if (self::isInsecurePinReminded($userId)) {
                    continue;
                }
                Data::set('mex_pin_remind_' . $userId, time(), false, 2592000); // 30 * 24 * 60 * 60
                $sendTo[] = $userId;
            }
            return $sendTo;
        });
    }

    public static function checkIsNextTransferFree(User $user, Transfer $transfer = null) {
        $status = $user->getOnboardStatus();
        // when will not check card status or balance when we pass a transfer record
        if (!$transfer) {
          if ($status !== 'Active') {
            return false;
          }

          $uc = $user->getCurrentPlatformCard();
          $an = $uc ? $uc->getAccountNumber() : null;

          if (!$an || $uc->getBalance() < 2000) {
            return false;
          }
        }

        // get transfer count
        $q = Util::em()->getRepository(Transfer::class)
                          ->createQueryBuilder('t')
                          ->where('t.sender = :sender')
                          ->andWhere(Util::expr()->in('t.status', ':status'))
                          ->setParameter('sender', $user)
                          ->setParameter('status', [
                              Transfer::STATUS_CONFIRMATION,
                              Transfer::STATUS_CREATED,
                              Transfer::STATUS_COMPLETED,
                              Transfer::STATUS_PENDING,
                              Transfer::STATUS_QUEUED,
                              Transfer::STATUS_PROCESSING
                          ]);
          if ($transfer) {
            $q->andWhere(Util::expr()->neq('t.id', ':except'))
              ->setParameter('except', $transfer->getId());
          }

          $count = $q->select('count(distinct t)')
                          ->getQuery()
                          ->getSingleScalarResult();
        return $count > 0 ? false : true ;
    }

    public static function isInsecurePinReminded($userId)
    {
        $old = (int)(Data::get('mex_pin_remind_' . $userId) ?: 0);
        return $old > 1000;
    }

    public static function  checkMessageFromExcleFile($path, $messageBatch = null)
    {
        $result = [];
        $excel = Excel::openExcel($path);
        $sheet = $excel->getActiveSheet();
        $rows = $sheet->toArray();
        $errors = [];
        $errorMessage = '';
        $errorStr = '';
        $required = [
            0 => 'Employee ID'
        ];

        foreach ($rows as $i => $row) {
            $errorMessage = null;
            if ($i <= 0) {
                continue;
            }

            $employee = null;
            $eid = null;

            foreach ($required as $index => $name) {
                $row[$index] = trim($row[$index]);
                if ($row[$index] === null || $row[$index] === '') {
                    $errorMessage = 'Row ' . ($i + 1) . ': Required field "' . $name . '" is empty!';
                    $errors[] = $errorMessage;
                    $errorStr .= $errorMessage;
                    goto has_error;
                }
            }

            $eid = (int)$row[0];
            $employee = User::find($eid);
            if (!$employee) {
              $errorMessage = 'Row ' . ($i + 1) . ': Unknown Employee ID!';
              $errors[] = $errorMessage;
              $errorStr .= $errorMessage;
              goto has_error;
            }

            $result[] = [
              'Row' => $i + 1,
              'User ID' => $employee->getId()
            ];
            has_error:
            if (count($errors) >= 5) {
              break;
            }
            if (count($errors) && $errorMessage) {
                $errorRecord = new MessageBatchErrorRecord();
                $errorRecord->setReason($errorMessage)
                            ->setMessageBatch($messageBatch)
                            ->persist();
            }
        }
        Log::debug('Error message: ' . $errorStr);
        if ($messageBatch) {
          $messageBatch->setFailedCount(count($errors))
                      ->persist();
           return $result;
        } else {
          return  $errorStr ? 'error:' . $errorStr : $result;
        }
    }

    public static function  checkMessageFromCsvFile($path, $messageBatch = null)
    {
        $em = Util::em();
        $result = [];
        $excel = Excel::openExcel($path);
        $sheet = $excel->getActiveSheet();
        $rows = $sheet->toArray();
        $errors = [];
        $errorMessage = '';
        $errorStr = '';
        $required = [
            1 => 'Emolyee ID'
        ];

        foreach ($rows as $i => $row) {
            $errorMessage = null;
            if ($i <= 0) {
                continue;
            }

            $employee = null;

            foreach ($required as $index => $name) {
                $row[$index] = trim($row[$index]);
                if ($row[$index] === null || $row[$index] === '') {
                    $errorMessage = 'Row ' . ($i + 1) . ': Required field "' . $name . '" is empty!';
                    $errors[] = $errorMessage;
                    $errorStr .= $errorMessage;
                    goto has_error;
                }
            }

            $eid = (int)$row[0];
            $employee = User::find($eid);

            if (!$employee) {
              $errorMessage = 'Row ' . ($i + 1) . ': Unknown Employee ID!';
              $errors[] = $errorMessage;
              $errorStr .= $errorMessage;
              goto has_error;
            }

            $result[] = [
              'Row' => $i + 1,
              'User ID' => $employee->getId()
            ];
            has_error:
            if (count($errors) >= 5) {
                break;
            }
            if (count($errors) && $errorMessage && $messageBatch) {
                $errorRecord = new MessageBatchErrorRecord();
                $errorRecord->setReason($errorMessage)
                            ->setMessageBatch($messageBatch)
                            ->persist();
            }
        }
        Log::debug('Error message: ' . $errorStr);
        if ($messageBatch) {
          $messageBatch->setFailedCount(count($errors))
                      ->persist();
           return $result;
        } else {
          return  $errorStr ? 'error:' . $errorStr : $result;
        }
    }

    public static function getCurrentLanguage(User $member)
    {
        return Data::get('current-language-' . $member->getId()) ?: 'es';
    }

    public static function sendMessage(User $member, $params, $subject = null, $otherData = [], $logPrefix = null)
    {
        $locale = self::getCurrentLanguage($member) ?: 'es';
        $content = null;
        $messageChannel = 'both';
        $errorMessage = '';
        if (is_array($params)) {
            $content = $params[$locale] ?? null;
            $messageChannel = $params['messageChannel'] ?? 'both';
            if ($messageChannel == 'sms') {
              $content =  $content ?? $params[$locale. '_sms'];
            }
        } else if (is_string($params)) {
            $content = $params;
        }
        if (!$content) {
            Util::consoleLine($logPrefix . ': Invalid message content ' . $member->getId() . ' - locale : ' . $locale, 'comment');
            $errorMessage = $logPrefix . ': Invalid message content ' . $member->getId() . ' - locale : ' . $locale;
            return $errorMessage;
        }
        if (is_array($subject)) {
            $subject = $subject[$locale] ?? null;
            if (!$subject) {
                Util::consoleLine($logPrefix . ': Invalid message subject ' . $member->getId() . ' - locale : ' . $locale, 'comment');
                $errorMessage = $logPrefix . ': Invalid message subject ' . $member->getId() . ' - locale : ' . $locale;
                return $errorMessage;
            }
        }
        $twilio = new TwilioService();
        try {
            Util::consoleLine( $messageChannel);
            if ($member->isEmailValid() && ($messageChannel == 'both' || $messageChannel == 'email')) {
                $sent = Email::sendWithTemplateToUser($member, Email::TEMPLATE_SIMPLE_LAYOUT, array_merge([
                    '_lang' => $locale,
                    'name' => $member->getFullName(),
                    'subject' => $subject ?: $content,
                    'body' => '<p>' . $content . '</p>',
                ], $otherData ?? []));

                if ($sent) {
                    Util::consoleLine($logPrefix . ': Sent as email to ' . $member->getId() . ' - locale : ' . $locale);
                    return 'email';
                }
            }
            if ($member->getMobilephone() && ($messageChannel == 'both' || $messageChannel == 'sms')) {
                if (is_array($params)) {
                    if ($locale && !empty($params[$locale . '_sms'])) {
                        $content = $params[$locale . '_sms'];
                    } else if (!empty($params['sms'])) {
                        $content = $params['sms'];
                    }
                }
                $info = array(
                    'to' => $member->getMobilephone(),
                    'msg' => $content,
                );
                $sent = $twilio->sendSMSMsg($info);
                if ($sent instanceof ExternalInvoke) {
                    Util::consoleLine($logPrefix . ': Sent as SMS to ' . $member->getId() . ' - locale : ' . $locale);
                    return 'sms';
                }
            }
            Util::consoleLine($logPrefix . ': No valid way to ' . $member->getId(), 'comment');
            $errorMessage = $logPrefix . ': No valid way to ' . $member->getId();
        } catch (\Exception $ex) {
            Util::consoleLine($logPrefix . ': Error to ' . $member->getId() . ' : ' . $ex->getMessage(), 'error');
            $errorMessage = $logPrefix . ': Error to ' . $member->getId() . ' : ' . $ex->getMessage();
        }
        return $errorMessage;
    }

    /**
     * @param User $user
     * @param MessagePayload[]|MessagePayload $params
     * @param array $channels push/email/sms/auto(email or sms)
     *
     * @return array
     */
    public static function sendInstantMessage (User $user, $params, $channels = ['push', 'auto']) {
        $locale = self::getCurrentLanguage($user);
        if (is_array($params)) {
            $params = $params[$locale] ?? $params['es'] ?? null;
        }
        if (!$params) {
            return [];
        }
        $sent = [];
        $ret = null;
        if (in_array('push', $channels)) {
            $ret = self::sendFirebaseMessage($user, $params);
            if ($ret) {
                $sent[] = 'push';
            }
        }
        if (in_array('auto', $channels)) {
            $ret = self::sendMessage($user, $params->message, $params->title);
        }
        if (in_array('sms', $channels)) {
            $ret = self::sendMessage($user, [
                'messageChannel' => 'sms',
                $locale . '_sms' => $params->message,
            ]);
        }
        if (in_array('email', $channels)) {
            $ret = self::sendMessage($user, [
                'messageChannel' => 'email',
                $locale => $params->message,
            ], [
                $locale => $params->title,
            ]);
        }
        if (in_array($ret, ['email', 'sms'])) {
            $sent[] = $ret;
        }
        return $sent;
    }

    /**
     * @param User  $user
     * @param MessagePayload[]|MessagePayload $params ['en' => ['title' => '', 'message' => '',], 'es' => ['title' => '', 'message' => '']]
     *
     * @return bool
     */
    public static function sendFirebaseMessage (User $user, $params) {
        if (is_array($params)) {
            $locale = self::getCurrentLanguage($user);
            $params = $params[$locale] ?? $params['es'] ?? null;
        }
        if (!$params) {
            return false;
        }
        return self::sentFirebase($user, $params->toArray(), true);
    }

    public static function sentFirebase (User $member, array $messageInfo, $isNotify = false, MessageRecord $message = null) {
        $token = Util::meta($member, 'fireBaseToken');
        if (!$token) {
            Log::debug('No token, cannot send Firebase message: user ' . $member->getId(), $messageInfo);
            if ($message) {
                $message->setErrorMessage('The user does not enable app push yet')
                    ->persist();
            }
            return false;
        }
        // define the scopes for your API call
        $path = Util::secureDir() . Util::getConfigKey('fire_base_key_file');
        putenv('GOOGLE_APPLICATION_CREDENTIALS=' . $path);
        $scopes = [Util::getConfigKey('fire_base_scopes')];
        // create middleware
        $middleware = ApplicationDefaultCredentials::getMiddleware($scopes);
        $stack = HandlerStack::create();
        $stack->push($middleware);
        $projectId =  Util::getConfigKey('fire_base_project_id'); //Util::getDecryptedParameter('fire_base_project_id');
        // create the HTTP client
        $client = new Client([
          'handler' => $stack,
          'base_uri' => Util::getConfigKey('fire_base_url'),
          'auth' => 'google_auth'  // authorize all requests
        ]);
        $json = [];
        if ($isNotify) {
          $json = [
             // "validate_only" => true,
             "message" => [
              'token' => $token,
              'notification' => [
                'title' => $messageInfo['title'],
                'body' => $messageInfo['message'],
              ]
             ]
          ];
        } else {
          $json = [
                  // "validate_only" => true,
                  "message" => [
                    'token' => $token,
                    'notification' => [
                      'title' => $messageInfo['title'],
                      'body' => $messageInfo['message'],
                    ],
                    'data' => [
                      'type' => $messageInfo['type'],
                      'cash' => UniTellerRemittanceService::FEE_CASH_PICKUP . '',
                      'bank' => RapydDisburseService::FEE_BANK_TRANSFER . '',
                      'messageId' => $messageInfo['id']
                    ]
                  ]
              ];
        }
        if (Util::isTestEnv()) {
            Log::debug('Skip sending the Firebase message in test env for member ' . $member->getId(), $messageInfo);
            return true;
        }
        try {
          $client->post('v1/projects/' . $projectId . '/messages:send', [
            // 'headers' => [
            //   'Accept' => 'application/json',
            // ],
            'json' => $json
          ]);
          Log::debug('Successfully sent the Firebase message to member ' . $member->getId(), $messageInfo);
          return true;
        } catch(\Exception $exception) {
          if ($message) {
            $message->setErrorMessage('An error occurred when sending push. Please try again')
                    ->persist();
          }
          $messageInfo['exception'] = $exception->getMessage();
          Log::warn('Failed to send the Firebase message to member ' . $member->getId(), $messageInfo);
          return false;
        }
    }
    public static function sentAppMessage(User $member, MessageRecord $message)
    {
        $locale = self::getCurrentLanguage($member) ?: 'es';
        $messageInfo = [
          'title' => $locale == 'es' ? $message->getTitleEs() : $message->getTitle(),
          'message' => $locale == 'es' ? $message->getMessageEs() : $message->getMessage(),
          'type' => $message->getType(),
          'id' => $message->getId() . ''
        ];
        $res = self::sentFirebase($member, $messageInfo, $message);
        if ($res) {
          $message->setStatus(MessageBatch::STATUS_SENT)
                  ->setSentDate(Carbon::now())
                  ->setErrorMessage('')
                  ->persist();
        } else {
          $message->setStatus(MessageBatch::STATUS_ERROR)
                ->persist();
        }
        return $res;
    }


    public static function getFirstPromoStatus (User $user) {
      $freeTransfer = Util::em()->getRepository(Transfer::class)
                    ->createQueryBuilder('t')
                    ->where('t.sender = :sender')
                    ->andWhere(Util::expr()->in('t.status', ':status'))
                    ->andWhere(Util::expr()->eq('t.transferFee', ':transferFee'))
                    ->andWhere(Util::expr()->notLike('t.meta', ':employeePromoId'))
                    ->setParameter('sender', $user)
                    ->setParameter('employeePromoId', '%employeePromoId%')
                    ->setParameter('transferFee', 0)
                    ->setParameter('status', [
                        Transfer::STATUS_COMPLETED
                    ])
                    ->select('count(distinct t)')
                    ->getQuery()
                    ->getSingleScalarResult();
       return $freeTransfer ? "Used" : "Not Met";
    }

    public static function getUserPromo (User $user, $type) {
      $expr = Util::expr();
      $status = $type == 'used' ? [EmployeePromo::STATUS_COMPLETE, EmployeePromo::STATUS_PENDING] : [EmployeePromo::STATUS_INIT];

      return Util::em()->getRepository(EmployeePromo::class)
                          ->createQueryBuilder('epro')
                          ->where($expr->in('epro.status', ':status'))
                          ->andWhere($type == 'used' ? $expr->isNotNull('epro.transfer') : $expr->isNull('epro.transfer'))
                          ->andWhere($expr->eq('epro.employee', ':employee'))
                          ->setParameter('employee', $user)
                          ->setParameter('status', $status)
                          ->select('count(distinct epro)')
                          ->getQuery()
                          ->getSingleScalarResult();
    }


    public static function checkTransferFree(User $user, $type = 'both', $isReturnObject = false) {
      $expr = Util::expr();
      $query = Util::em()->getRepository(EmployeePromo::class)
                          ->createQueryBuilder('epro')
                          ->where($expr->eq('epro.status', ':status'))
                          ->andWhere($expr->isNull('epro.transfer'))
                          ->andWhere($expr->eq('epro.employee', ':employee'))
                          ->setParameter('employee', $user)
                          ->setParameter('status', EmployeePromo::STATUS_INIT);
      if ($type == 'cash') {
        $query->andWhere($expr->in('epro.type', ':type'))
              ->setParameter('type', ['free_cash_pickup', 'free_transfer']);
      } else if ($type == 'bank') {
        $query->andWhere($expr->in('epro.type', ':type'))
              ->setParameter('type', ['free_bank', 'free_transfer']);
      }

      $promos = $query->orderBy('epro.id', 'desc')
                          ->getQuery()
                          ->getResult();
       // free for transfer start 4/9, we can disable this by configure item migrate_card_transfer_free
      $date =  Util::formatDateTime(Carbon::now(), Util::DATE_FORMAT, 'America/New_York');

      $feeKey = 'transfer_free_for_mirage_' . $date . '_' . $user->getId();
      $feeCountOnOneDay = Data::get($feeKey) ? (int)Data::get($feeKey) : 0;
      if ($feeCountOnOneDay < 7 && Config::get('migrate_card_transfer_free', false)) {
        return true;
      }
      // check the member is been configrued
      if (EmployeeTransferFreeConfig::checkHasConfigFree($user, $type)) {
        return true;
      }

       // check the employer is been configrued
       if (EmployerTransferFreeConfig::checkHasConfigFree($user, $type)) {
        return true;
      }

      return $isReturnObject && !empty($promos) ? $promos[0] : !empty($promos);
    }

    public static function hasRecipient(User $user, $type) {
      $group = $user->ensureRecipientsGroup();
      $userList = $group->queryUsers(null, true)->select('u.id')->getQuery()->getArrayResult();

      $userIds = array_column($userList, 'id');
      $query = Util::em()->getRepository(Recipient::class)
                          ->createQueryBuilder('mr')
                          ->join('mr.user', 'u')
                          ->andWhere(Util::expr()->in('u.id', ':userIds'))
                          ->setParameter('userIds', $userIds);

      if ($type == 'bank') {
        $query->andWhere(Util::expr()->eq('mr.payoutType', ':payoutType'))
                      ->setParameter('payoutType', 'Bank Transfer');
      } else if ($type == 'cash') {
        $query->andWhere(Util::expr()->eq('mr.payoutType', ':payoutType'))
                          ->setParameter('payoutType', 'Cash Pickup');
      }

      return  $query->select('count(distinct mr)')
                    ->getQuery()
                     ->getSingleScalarResult() ? true : false;
    }

    public static function syncBotmAccount(User $user, UserCard $uc)
    {
        $config = $user->ensureConfig();
        $api = BotmAPI::getForUserCard($uc);

        $an = $uc->getAccountNumber();
        if (BotmAPI::isAccountNumberValid($an)) {
            $card = ExternalInvoke::host($api->retrieveCard($uc));
            if (!empty($card['user_id'])) {
              $findUser = BotmUserService::findByBotmUserAccountId($card['user_account_id']);
              if ($findUser) {
                $linkedUser = $findUser->getSignature();
                $message = 'The BOTM user account has been linked to ' . $linkedUser . ', please change a new card or contact BOTM!';
                throw PortalException::temp($message);
              }
                $config->setBotmUserId($card['user_id'])
                    ->replaceBotmUserAccountId($card['user_account_id'] ?? null);
            }
            $config->persist();
        }

        if (!$config->getBotmUserId()) {
            $uid = BotmService::searchAndFillUser($user);
            if (!$uid) {
                Util::refresh($config);
                ExternalInvoke::host($api->ensureBotmUserId($user));
                Util::refresh($config);
            }
        }

        if ($config->getBotmUserId() && !$config->getBotmUserAccountId()) {
            $uc = $user->getCurrentPlatformCard();
            $api = BotmAPI::getForUserCard($uc);
            ExternalInvoke::host($api->ensureBotmUserAccountId($user));
        }

        $kycStatus = BotmService::getBotmKycStatus($user);
        Util::updateMeta($user, [
            'botm_kyc_status' => $kycStatus
        ]);
    }
}
