<?php

namespace TransferMexBundle\Command;

use Carbon\Carbon;
use CoreB<PERSON>le\Command\BaseHostedCommand;
use Core<PERSON><PERSON>le\Entity\ExternalInvoke;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\UserCardTransaction;
use CoreBundle\Entity\UserGroup;
use CoreBundle\Utils\Util;
use FaasBundle\Services\BOTM\BotmAPI;
use FaasBundle\Services\BOTM\BotmService;
use Symfony\Component\Console\Input\InputOption;
use TransferMexBundle\Services\SlackService;
use SalexUserBundle\Entity\User;

class UpdateBotmAgentTransactionsCommand extends BaseHostedCommand
{
    /**
     * @var \DateTimeZone $tz
     */
    public $tz;

    protected function configure()
    {
        $this
            ->setName('span:mex:update-botm-agent-transactions')
            ->setDescription('Pull and fill Botm agent transactions')
            ->addOption('platform', null, InputOption::VALUE_REQUIRED, 'The platform id. Omit to sync all platforms.')
            ->addOption('agent', null, InputOption::VALUE_REQUIRED, 'The agent account number. Omit to sync all agents.')
            ->addOption('end', null, InputOption::VALUE_OPTIONAL, 'The end date. Format: YYYY-MM-DD. Default to the start of tomorrow')
            ->addOption('days', null, InputOption::VALUE_OPTIONAL, 'Update from how many days ago. Default to 10', 10)
            ->addOption('type', null, InputOption::VALUE_REQUIRED, 'The type for the transactions.', 'program_to_business')
            ->addOption('notify', null, InputOption::VALUE_NONE, 'Whether to notify TransferMex in Slack')
        ;
    }

    public function getSingletonKey()
    {
        return $this->getName() . '_' . ($this->input->getOption('type') ?: 'program_to_business');
    }

    protected function hostedExecute()
    {
        $input = $this->input;
        $thePlatform = $input->getOption('platform');
        $theAgent = $input->getOption('agent');
        $notify = $input->getOption('notify');
        $type = $input->getOption('type') ?? 'program_to_business';

        // Query all the transactions
        $this->tz = Util::tzCentral();
        $days = $input->getOption('days');
        if (!$days && $days !== '0') {
            $days = 10;
        }
        $end = $input->getOption('end');
        if ($end) {
            $endAt = Carbon::parse($end, $this->tz);
        } else {
            $endAt = Carbon::now($this->tz);
        }
        $endAt->startOfDay();
        $startAt = $endAt->copy()->subDays($days);
        $startAtFormat = $startAt->format('Y-m-d');

        if (!Util::$platform || (Util::$platform && Util::$platform->isTernCommerce())) {
          $paltformList = [];
          $platforms = Util::em()->getRepository(Platform::class)->findAll();

          /** @var Platform $p */
          foreach ($platforms as $p) {
              if ($p->isTransferMex() || $p->isD2() || $p->isAmericaVoice() || $p->isIQStel() || $p->isRedCard() ) {
                $paltformList[] = $p;
              }
          }
        } else {
          $paltformList = [Util::$platform];
        }
        foreach ($paltformList  as $platform)  {
            if ($thePlatform && $platform->getId() !== (int)$thePlatform) {
                continue;
            }

          Util::$platform = $platform;
          $groups = BotmService::getUserGroupsWithCustomAccount($platform);
          $this->line('Checking platform: ' . $platform->getName());

          /** @var UserGroup $api */
            foreach ($groups as $group) {
              $api = BotmAPI::getForUserGroup($group);
              if (!$api->agentNumber) {
                continue;
              }
              if ($theAgent && (string)$api->agentNumber !== (string)$theAgent) {
                continue;
              }
              $platform = $api->getPlatform();
              Util::$platform = $platform;
              $employer = $group->getPrimaryAdmin();
              if (!$employer) {
                $this->line('Can find the employer for BOTM agent: ' . $platform->getName() . ' - ' . $api->agentNumber);
                continue;
              }
              if ($this->checkEmployerIsDeleted($employer->getId())) {
                $this->line('The employer has been deleted for BOTM agent: ' . $platform->getName() . ' - ' . $api->agentNumber);
                continue;
              }
              $this->line('Executing for BOTM agent: ' . $platform->getName() . ' - ' . $api->agentNumber);

              try {
                  $this->executeForApi($api, $group, $startAt, $endAt, $days, $type);
              } catch (\Exception $ex) {
                  $this->line('Failed to sync BOTM transactions: ' . $ex->getMessage(), 'error', [
                      'platform' => $platform->getName(),
                      'agent' => $api->agentNumber,
                  ]);
                  // update employer runnning balance
                  $this->updateRunningBalance($group, $api->agentNumber);
              }

              if ($notify && $theAgent) {
                  SlackService::prepareForPlatform(Platform::transferMex(), 'client');
                  SlackService::eyes('Synced the BOTM agent transactions since `' . $startAtFormat . '`.', [
                      'agent' => $api->getName(),
                  ]);
              }
          }

        }

        if ($notify && !$theAgent) {
            SlackService::prepareForPlatform(Platform::transferMex(), 'client');
            SlackService::eyes('Synced all BOTM agent transactions since `' . $startAtFormat . '`.', [
                'type' => $type,
            ]);
        }
    }

    protected function executeForApi(BotmApi $api,UserGroup $ug, Carbon $startAt, Carbon $endAt, $days, $type = null)
    {
        $agentNumber = $api->agentNumber;
        $logSuffix = ' in recent ' . $days . ' days between ' . $startAt->format('Y-m-d')
                     . ' and ' . $endAt->format('Y-m-d');
        $this->line('Querying BOTM agent ' . $agentNumber . ' transactions' . $logSuffix);

        $params = [];
        $params['start_date'] = Carbon::now()->subDays(30)->format('Y-m-d');
        $params['end_date'] = Carbon::tomorrow()->format('Y-m-d');
        $params['transactionType'] = $type;
        if ($type == 'program_to_business') {
          $params['onlyTransactions'] = false;
        }
        /** @var ExternalInvoke $ei */
        [$ei, $data] = $api->listBusinessTransactions(...$params);
        if ($ei->isFailed()) {
            $error = $ei->getErrorSignature();
            $msg = 'Failed to update BOTM agent ' . $agentNumber . '\'s transactions: ' . $error;
            if (strpos($error, 'No Record Found for details provided') !== false) {
                $this->line($msg, 'error');
            } else {
                SlackService::alert($msg);
            }
            return;
        }
        $data = array_reverse($data ?? []);
        $this->line('Found ' . count($data) . ' BOTM agent ' . $agentNumber . ' transactions' . $logSuffix);

        // Sync the database
        $repo = $this->em->getRepository(UserCardTransaction::class);

        $user = $ug->getPrimaryAdmin();
        $userCard = $user->getCurrentPlatformCard();
        foreach ($data as $t) {
            if (empty($t['transaction_id']) && $t['transaction_type'] != 'ACH Transfer') {
                continue;
            }
            $transactionId = $t['transaction_id'];
            if ($t['transaction_type'] == 'ACH Transfer') {
              $transactionId = 'ach_' . $t['id'];
            }
            $newTxn = false;
            /** @var UserCardTransaction $uct */
            $uct = $repo->findOneBy([
                'userCard' => $userCard,
                'tranId' => $transactionId,
            ]);
            if (!$uct) {
                $newTxn = $transactionId;
                $uct = new UserCardTransaction();
                $uct->setUserCard($userCard)
                    ->setTranId($transactionId);
            }
            $type = Util::startsWith($t['transfer_type'], 'business_to') ? 'DEBIT' : 'CREDIT';
            $amount = $t['amount'] * 100;

            if ($type === 'DEBIT') {
                $amount *= -1;
            }
            $status = BotmService::getTransactionStatus($t['status']);
            $desc = Util::cleanUtf8String($t['destination'] ?? null);
            $txnTime = empty($t['created_at']) ? null : Util::toUTC(Carbon::create(substr($t['created_at'], 0 ,4), substr($t['created_at'], 5, 2), substr($t['created_at'], 8 ,2), substr($t['created_at'], 11 ,2), substr($t['created_at'], 14 ,2), substr($t['created_at'], 17 ,2)));
            if ($transactionId == '1cb0eb15-971e-4269-b7c8-42ae4f372c65') {
                $created = Carbon::create(substr($t['created_at'], 0 ,4), substr($t['created_at'], 5, 2), substr($t['created_at'], 8 ,2), substr($t['created_at'], 11 ,2), substr($t['created_at'], 14 ,2), substr($t['created_at'], 17 ,2));
                $txnTime = Util::toUTC($created->subDays(4));
            }
            $uct->setTxnAmount($amount)
                ->setActualTranCode($t['transfer_type'])
                ->setProductName(Util::$platform->getName())
                ->setTranCode($type)
                ->setTranDesc($desc)
                ->setAccountStatus($status)
                ->setOutstandingAuths($api->agentNumber)
                ->setUserField2($t['other_entity'])
                ->setTxnTime($txnTime);
             $this->em->persist($uct);

            if ($newTxn) {
                $this->line('-- new transaction ' . $newTxn, 'info', [
                    'acGroup' => $ug ? $ug->getName() : null,
                    'uctId' => $uct->getId(),
                    'uctStatus' => $uct->getAccountStatus(),
                    'tranCode' => $uct->getTranCode(),
                    'desc' => $desc,
                    'uctUser' => $user ? $user->getId() : null
                ]);
            }
        }
        $this->em->flush();
        $this->line('Filled all the transactions of BOTM agent ' . $agentNumber . ' to db');
        $this->updateRunningBalance($ug, $agentNumber);
        $this->line('Updated the running balance of BOTM agent ' . $agentNumber);

    }

    public function updateRunningBalance(UserGroup $ug, $agentNumber)
    {
        $user = $ug->getPrimaryAdmin();
        $userCard = $user->getCurrentPlatformCard();
        $all = $this->em->getRepository(UserCardTransaction::class)
            ->findBy([
                'userCard' => $userCard,
                'outstandingAuths' => $agentNumber,
            ], [
                'txnTime' => 'asc'
            ]);
        $balance = 0;
        /** @var UserCardTransaction $rt */
        foreach ($all as $rt) {
            $rt->setPreBalance($balance);
            $balance += $rt->getTxnAmount();
            $rt->setCurBalance($balance);
            $this->em->persist($rt);
        }
        $this->em->flush();
    }

    protected function checkEmployerIsDeleted($id) {
        $user = User::findBy([
            'id' => $id,
            'deletedAt' => null
        ]);
        return $user ? false : true;
    }
}
