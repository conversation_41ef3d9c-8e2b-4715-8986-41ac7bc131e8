<?php

namespace TransferMexBundle\Command\Botm;

use Carbon\Carbon;
use CoreBundle\Command\BaseCommand;
use CoreBundle\Entity\Platform;
use CoreBundle\Utils\Util;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use CoreB<PERSON>le\Entity\UserCardTransaction;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserGroup;
use FaasBundle\Services\BOTM\BotmAPI;
use FaasBundle\Services\BOTM\BotmService;
use SalexUserBundle\Entity\User;
use Symfony\Component\Console\Input\InputOption;



class UpdateEmployerPayoutTransactionsCommand extends BaseCommand
{
    public const MAX_SINGLE_TRANSACTION_AMOUNT = 150000;

    protected function configure()
    {
        $this
            ->setName('span:mex:update-employer-payout-transactions')
            ->addOption('employer', null, InputOption::VALUE_REQUIRED, 'If there is a value, query the corresponding employer')
            ->addOption('employee', null, InputOption::VALUE_REQUIRED, 'If there is a value, query the corresponding employer')
            ->addOption('storeData', null, InputOption::VALUE_REQUIRED, 'If there is a value, query the corresponding storeData')
            ->setDescription("Check the payout to find which will use the base agent and transfer money to base agent")
          ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        Util::$platform = Platform::transferMex();
        $this->prepare($input, $output, true);
        // set memory limit
        Util::longRequest(0, -1);
        $employerId = $this->input->getOption('employer');
        $employee = $this->input->getOption('employee');
        $storeData = $this->input->getOption('storeData');
        $employer = User::find($employerId);
        if (!$employer || !$employer->inTeams([Role::ROLE_TRANSFER_MEX_EMPLOYER])) {
          $this->line('Please input the right employer!');
          $this->done();
          return 0;
        }
        $expr = Util::expr();
        // check the botm user
        $q = $this->em->getRepository(User::class)
                          ->createQueryBuilder('u')
                          ->join('u.teams', 't')
                          ->join('u.config', 'config')
                          ->join('u.userGroups', 'g')
                          ->join('g.adminConfigs', 'ac')
                          ->join('ac.user', 'e')
                          ->where(Util::expr()->isNotNull('config.botmUserAccountId'))
                          ->andwhere('t.name = :roleName')
                          ->andWhere($expr->eq('e.id', ':employer'))
                          ->setParameter('roleName', Role::ROLE_TRANSFER_MEX_MEMBER)
                          ->setParameter('employer', $employer);
        if ($employee) {
          $q->andWhere($expr->eq('u.id', ':employee'))
            ->setParameter('employee', $employee);
        }
        $userList = $q->getQuery()
                      ->getResult();
        $this->line('Find ' . count($userList) . ' employee need to update payout transactions.');
        $employerCard = $employer->getCurrentPlatformCard();
        $group = $employer->getAdminGroup();
        $botm = BotmAPI::getForUserGroup($group);
        // Sync the database
        $repo = $this->em->getRepository(UserCardTransaction::class);
        $failedCount = 0;
        foreach ($userList as $user) {
          $uc = $user->getCurrentPlatformCard();
          try {
            [,$data] = $botm->consumerTransactions(
              $uc,
              Carbon::now()->subDays(365),
              Carbon::tomorrow());
            $this->line('Find ' . count($data) . ' transaction for the employee ' .  $user->getId());
            $newCount = 0;
            foreach ($data as $t) {
              if ($t['mode'] !== 'BUSINESS_TO_USER') {
                continue;
              }
              // skip the debit for transfer_reverse
              if (!Util::startsWith($t['transfer_comment'], 'Payout TranID')) {
                 // remove the data
                 $uctReverse = $repo->findOneBy([
                    'userCard' => $employerCard,
                    'tranId' => $t['related_id'],
                  ]);
                  if ($uctReverse) {
                    $this->em->remove($uctReverse);
                  }
                  continue;
              }

              /** @var UserCardTransaction $uct */
                $uct = $repo->findOneBy([
                    'userCard' => $employerCard,
                    'tranId' => $t['related_id'],
                ]);
                // check if the employer's transaction
                if (!$uct && Util::startsWith($t['transfer_comment'], 'Payout TranID')) {
                  $uct = UserCardTransaction::findForTransferMex($t['related_id']);
                  if ($uct && $botm->agentNumber != $uct->getOutstandingAuths()) {
                    continue;
                  }
                }
                if (!$uct) {
                    $uct = new UserCardTransaction();
                    $uct->setUserCard($employerCard)
                        ->setTranId($t['related_id']);
                    $newCount++;
                }

                $amount = $t['amount'] * (-100);
                $desc = Util::cleanUtf8String($t['card_holder_name'] ?? null);
                $status = BotmService::getTransactionStatus($t['status']);
                $uct->setTxnAmount($amount)
                  ->setActualTranCode(strtolower($t['mode']))
                  ->setProductName(Util::$platform->getName())
                  ->setTranCode('DEBIT')
                  ->setTranDesc($desc)
                  ->setAccountStatus($status)
                  ->setOutstandingAuths($botm->agentNumber)
                  ->setUserField2($desc)
                  ->setTxnTime(Util::timeUTCNullable($t['balance_changed_date'] ?? null));
                if ($storeData) {
                  $this->em->persist($uct);
                }
            }
            Util::flush();
            $this->line('Find ' . $newCount . ' payment records need to store for the employer');
          } catch (\Throwable $t) {
            $this->line('Failed to pull transactions for employee ' . $user->getId() . ' : ' . Util::getExceptionBrief($t));
            $failedCount++;
            if ($failedCount >= 20) {
                break;
            }
          }
        }
        $this->updateRunningBalance($employer->getAdminGroup(), $botm->agentNumber);
        $this->done();
        return 0;
    }

    public function updateRunningBalance(UserGroup $ug, $agentNumber)
    {
        $user = $ug->getPrimaryAdmin();
        $userCard = $user->getCurrentPlatformCard();
        $all = $this->em->getRepository(UserCardTransaction::class)
            ->findBy([
                'userCard' => $userCard,
                'outstandingAuths' => $agentNumber,
            ], [
                'txnTime' => 'asc'
            ]);
        $balance = 0;
        /** @var UserCardTransaction $rt */
        foreach ($all as $rt) {
            $rt->setPreBalance($balance);
            $balance += $rt->getTxnAmount();
            $rt->setCurBalance($balance);
            $this->em->persist($rt);
        }
        $this->em->flush();
    }
}


