<?php

namespace TransferMexBundle\Command;

use Carbon\Carbon;
use CoreBundle\Command\BaseCommand;
use CoreBundle\Entity\Platform;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Util;
use PortalBundle\Exception\PortalException;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use CoreBundle\Entity\Role;
use SalexUserBundle\Entity\User;
use TransferMexBundle\TransferMexBundle;
use CoreBundle\Entity\UserCardTransaction;
use CoreBundle\Entity\UserOtherInfo;
use CoreBundle\Utils\Data;
use CoreBundle\Entity\Transfer;

class UpdateMemberRecordCommand extends BaseCommand
{
    protected function configure()
    {
        $this
            ->setName('span:mex:update-member-record')
            ->setDescription('Update member record deposit transfer spend atm')
            ->addOption('memberId', null, InputOption::VALUE_OPTIONAL, 'Member Id')
           ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        Util::$platform = Platform::transferMex();
        $this->prepare($input, $output, true);
        $memberId = $input->getOption('memberId');

        $query = Util::em()->getRepository(User::class)
                          ->createQueryBuilder('u')
                          ->join('u.teams', 't')
                          ->join('u.userGroups', 'g')
                          ->join('g.adminConfigs', 'ac')
                          ->join('ac.user', 'e')
                          ->join('u.cards', 'uc')
                          ->join('uc.card', 'c')
                          ->where(Util::expr()->in('t.name', ':roles'))
                          ->andWhere('c.cardProgram = :cardProgram')
                          // ->andWhere('uc.status = :cardStatus')
                          ->andWhere(Util::expr()->notIn('u.id', ':hideIds'))
                          ->setParameter('hideIds', TransferMexBundle::hideUsers())
                          // ->setParameter('cardStatus', 'active')
                          ->setParameter('cardProgram', Util::cardProgram())
                          ->setParameter('roles', [Role::ROLE_TRANSFER_MEX_MEMBER]);

        if ($memberId) {
          $query->andWhere('u.id = :userId')
                ->setParameter('userId', $memberId);
        } else {
          $date = Carbon::now()->startOfDay()->subDays(3);
          $transactionMembersSql = Util::em()->getRepository(UserCardTransaction::class)
                      ->createQueryBuilder('uct')
                      ->join('uct.userCard', 'usercard')
                      ->join('usercard.user', 'user')
                      ->andWhere(Util::expr()->gt('uct.txnTime', ':txnTime'))
                      ->select('user.id')
                      ->getDQL();
          $transferMembersSql = Util::em()->getRepository(Transfer::class)
                      ->createQueryBuilder('transfer')
                      ->join('transfer.sender', 'sender')
                      ->andWhere(Util::expr()->gt('transfer.sendAt', ':sendAt'))
                      ->select('sender.id')
                      ->getDQL();



          $query->andWhere(Util::expr()->orX(
            Util::expr()->eq('u.id', Util::expr()->any($transactionMembersSql)),
            Util::expr()->eq('u.id', Util::expr()->any($transferMembersSql)),
            ))
            ->setParameter('txnTime', $date)
            ->setParameter('sendAt', $date);
        }

        $res = $query->getQuery()
                     ->getResult();
        $this->line('Found ' . count($res) . ' members need to update record');

        foreach ($res as $item) {
            $this->getRecordListAndStore($item);
        }

        $this->done();
        return 0;
    }

    protected function getRecordListAndStore(User $user) {
      $expr = Util::expr();
      $list = Util::em()->getRepository(UserCardTransaction::class)
                      ->createQueryBuilder('uct')
                      ->join('uct.userCard', 'uc')
                      ->join('uc.user', 'u')
                      ->where('u.id = :userId')
                      ->andWhere($expr->in('uct.accountStatus', ':accountStatus'))
                      ->andWhere($expr->notLike('uct.tranDesc', ':tranDesc'))
                      ->setParameter('tranDesc', '%Fund Reversal -%')
                      ->setParameter('accountStatus', ['Executed', 'Completed'])
                      ->setParameter('userId', $user->getId())
                      ->select('uct.tranCode, uct.txnAmount, uct.actualTranCode, uct.txnTime, uct.tranDesc')
                      ->orderBy('uct.txnTime', 'desc')
                      ->getQuery()
                      ->getArrayResult();
      $deposit = [
        'total' => 0,
        'count' =>0,
        'avg' => 0,
        'last' => null
      ];
      $spend = [
        'total' => 0,
        'count' =>0,
        'avg' => 0,
        'last' => null
      ];
      $atm = [
        'total' => 0,
        'count' =>0,
        'avg' => 0,
        'last' => null
      ];
      // $this->line('Found ' . count($list) . ' transactions for the user ' . $user->getFullName());
      foreach($list as $item) {
          if ($item['tranCode'] == 'CREDIT') {
            if (strpos($item['tranDesc'], 'Maintenance Fee') === false) {
              $deposit['total'] += $item['txnAmount'];
              $deposit['count']++;
              if (!$deposit['last']) {
                $deposit['last'] = $item['txnTime'];
              }
            }
          } else if($item['tranCode'] == 'DEBIT') {
            if (in_array($item['actualTranCode'], [
              'POS_PURCHASE',
              'POS_PURCHASE_WITH_CASHBACK',
              'POS_ECOM_PURCHASE',
              'AUTH_COMPLETION_DEBIT'
            ])) {
              $spend['total'] += $item['txnAmount'];
              $spend['count']++;
              if (!$spend['last']) {
                $spend['last'] = $item['txnTime'];
              }
            } else if ( $item['actualTranCode'] == 'ATM_WITHDRAW') {
              $atm['total'] += $item['txnAmount'];
              $atm['count']++;
              if (!$atm['last']) {
                $atm['last'] = $item['txnTime'];
              }
            }
          }
      }
      $deposit['avg'] = Util::percent($deposit['total'], $deposit['count']);
      $spend['avg'] = Util::percent($spend['total'], $spend['count']);
      $atm['avg'] = Util::percent($atm['total'], $atm['count']);
      Data::setArray('member_report_records_info' . $user->getId(), [
        'deposit' => $deposit,
        'spend' => $spend,
        'atm' => $atm,
      ], 0, false);
      $userOtherInfo = UserOtherInfo::findByUser($user);
      if (!$userOtherInfo) {
        $iosLastLogin = Util::meta($user, 'iosLastLogin');
        $androidLastLogin = Util::meta($user, 'androidLastLogin');
        $webLastLogin = Util::meta($user, 'webLastLogin');
        $userOtherInfo = new UserOtherInfo();
        $userOtherInfo->setIosDeviceId(Util::meta($user, 'iosDeviceId'))
                      ->setLastIosLogin($iosLastLogin ?  new \DateTime($iosLastLogin) : null)
                      ->setAndroidDeviceId( Util::meta($user, 'androidDeviceId'))
                      ->setLastAndroidLogin($androidLastLogin ?  new \DateTime($androidLastLogin) : null)
                      ->setLastWebLogin($webLastLogin ?  new \DateTime($webLastLogin) : null);
      }
      $bank = $this->getTransferInfo($user, 'bank');
      $cash = $this->getTransferInfo($user, 'cash');
      $userOtherInfo->setUser($user)
                    ->setTotalDeposit($deposit['total'])
                    ->setCountDeposit($deposit['count'])
                    ->setAvgDeposit( $deposit['avg'])
                    ->setLastDepositAt($deposit['last'])
                    ->setTotalBankTransfer($bank['total'])
                    ->setCountBankTransfer($bank['count'])
                    ->setAvgBankTransfer($bank['avg'])
                    ->setBankTransferPercent(Util::percent($deposit['total'], $bank['total']) * 100)
                    ->setLastBankAt($bank['lastOne'])
                    ->setTotalCashPickup($cash['total'])
                    ->setCountCashPickup($cash['count'])
                    ->setAvgCashPickup($cash['avg'])
                    ->setCashCickupPercent(Util::percent($deposit['total'], $cash['total']) * 100)
                    ->setLastCashPickUpAt($cash['lastOne'])
                    ->setTotalSpend($spend['total'])
                    ->setCountSpend($spend['count'])
                    ->setAvgSpend($spend['avg'])
                    ->setSpendPercent(Util::percent($deposit['total'], $spend['total']) * 100)
                    ->setLastSpendAt($spend['last'])
                    ->setTotalAtm($atm['total'])
                    ->setCountAtm($atm['count'])
                    ->setAtmPercent(Util::percent($deposit['total'], $atm['total']) * 100)
                    ->setAvgAtm($atm['avg'])
                    ->setLastAtmAt($atm['last'])
                    ->persist();
      // Log::debug('deposit', $deposit);
      // Log::debug('spend', $spend);
      // Log::debug('atm', $atm);
    }

    protected function getTransferInfo(User $user, $type) {
      $query = $this->em->getRepository(Transfer::class)
                ->createQueryBuilder('t')
                ->join('t.sender', 'u')
                ->where(Util::expr()->eq('u.id', ':userId'))
                ->andWhere(Util::expr()->eq('t.payoutType', ':type'))
                ->andWhere(Util::expr()->in('t.status', ':statusList'))
                ->setParameter('userId', $user->getId())
                ->setParameter('type', $type)
                ->setParameter('statusList', [
                    Transfer::STATUS_CREATED,
                    Transfer::STATUS_COMPLETED,
                    Transfer::STATUS_HOLD,
                    Transfer::STATUS_QUEUED,
                ]);
      $lastOne = $query->orderBy('t.sendAt', 'desc')->select('t.sendAt')
                        ->setMaxResults(1)
                        ->getQuery()
                        ->getResult();
      $total = $query->select('sum(t.sendAmount + t.transferFee)')
              ->getQuery()
              ->getSingleScalarResult();

      $count = $query->select('count(distinct t)')
                      ->getQuery()
                      ->getSingleScalarResult();

      return [
          'total' => $total ?? 0,
          'count' => $count,
          'avg' => Util::percent($total, $count),
          'lastOne' => $lastOne ? $lastOne[0]['sendAt']: null,
      ];
    }
}
