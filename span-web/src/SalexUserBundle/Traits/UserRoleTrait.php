<?php
/**
 * Created by PhpStorm.
 * User: hans
 * Date: 2019/2/21
 * Time: 13:03
 */

namespace SalexUserBundle\Traits;


use ApiBundle\Services\ApiRunner;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\Config;
use CoreBundle\Entity\Email;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\Tag;
use CoreBundle\Services\RoleService;
use CoreBundle\Utils\Bundle;
use CoreBundle\Utils\Util;
use Doctrine\Common\Collections\ArrayCollection;
use PortalBundle\Exception\PortalException;
use SalexUserBundle\Entity\User;
use TransferMexBundle\Entity\EmployerSubCompany;

trait UserRoleTrait
{
    /**
     * @param string           $email
     * @param array|null       $roles
     * @param CardProgram|null $cp
     *
     * @return User|null
     */
    public static function findPlatformUserByEmail(string $email, array $roles = null, CardProgram $cp = null, $status = null)
    {
        if (!$email) {
            return null;
        }
        if ($roles === null) {
            $roles = Bundle::getAllRoles();
        }
        if (!$roles) {
            return null;
        }
        $expr = Util::expr();
        $q = Util::em()->getRepository(User::class)
            ->createQueryBuilder('u')
            ->join('u.teams', 't')
            ->where('u.email = :email')
            ->andWhere($expr->in('t.name', ':roles'))
            ->setParameter('email', $email)
            ->setParameter('roles', $roles);

        if ($cp) {
            $q->join('u.cards', 'uc')
                ->join('uc.card', 'c')
                ->andWhere('c.cardProgram = :cp')
                ->setParameter('cp', $cp);
        } else {
            self::queryFaasPlatformOwnUsers($q, $roles);
        }

        if ($status && in_array($status, [User::STATUS_ACTIVE, User::STATUS_INACTIVE])) {
            $q->andWhere('u.status = :status')
                ->setParameter('status', $status);
        }

        return $q->setMaxResults(1)
            ->distinct()
            ->getQuery()
            ->getOneOrNullResult();
    }

     /**
     * @param string           $phone
     * @param array|null       $roles
     * @param CardProgram|null $cp
     *
     * @return User|null
     */
    public static function findPlatformUserByPhone(string $phone, array $roles = null, CardProgram $cp = null, $status = null)
    {
        if (!$phone) {
            return null;
        }
        if ($roles === null) {
            $roles = Bundle::getAllRoles();
        }
        if (!$roles) {
            return null;
        }
        $expr = Util::expr();
        $q = Util::em()->getRepository(User::class)
            ->createQueryBuilder('u')
            ->join('u.teams', 't')
            ->where('u.mobilephone = :mobilephone')
            ->andWhere($expr->in('t.name', ':roles'))
            ->setParameter('mobilephone', $phone)
            ->setParameter('roles', $roles);

        if ($cp) {
            $q->join('u.cards', 'uc')
                ->join('uc.card', 'c')
                ->andWhere('c.cardProgram = :cp')
                ->setParameter('cp', $cp);
        } else {
            self::queryFaasPlatformOwnUsers($q, $roles);
        }

        if ($status && in_array($status, [User::STATUS_ACTIVE, User::STATUS_INACTIVE])) {
            $q->andWhere('u.status = :status')
                ->setParameter('status', $status);
        }

        return $q->setMaxResults(1)
            ->distinct()
            ->getQuery()
            ->getOneOrNullResult();
    }

    public function clearRoles($roles = null)
    {
        /** @var Role $role */
        foreach ($this->getTeams() as $role) {
            if (!$roles || in_array($role->getName(), $roles)) {
                $this->removeTeam($role);
            }
        }
        return $this;
    }

    public function ensureRole($roleName)
    {
        if (!$roleName) {
            throw PortalException::temp('Empty role name!');
        }
        if (is_array($roleName)) {
            $roleName = $roleName[0];
        }
        $role = Role::find($roleName);
        if (!$role) {
            return $this;
        }
        $find = $this->getTeams()->filter(function (Role $r) use ($role) {
            return Util::eq($r, $role);
        });
        if ($find->isEmpty()) {
            $this->addTeam($role)
                ->persist();
        }
        return $this;
    }

    public function hasNonConsumerRole()
    {
        $roleArr = $this->getTeams()->getValues();
        $consumerRoles = [
            Role::ROLE_CONSUMER,
            Role::ROLE_CASH_ON_WEB_MEMBER,
            Role::ROLE_TRANSFER_MEX_MEMBER,
            Role::ROLE_FAAS_MEMBER,
            Role::ROLE_SPENDR_CONSUMER,
            Role::ROLE_SKUX_CONSUMER,
            Role::ROLE_SKUX_APP_USER,
        ];
        /** @var Role $role */
        foreach ($roleArr as $role) {
            if (!in_array($role->getName(), $consumerRoles)) {
                return true;
            }
        }
        return false;
    }

    public function hasCashOnWebRole ($roleNames) {
      $roleArr = $this->getTeams()->getValues();
      /** @var Role $role */
      foreach ($roleArr as $role){
        if (in_array($role->getName(), $roleNames)) {
          return true;
        }
      }
      return false;
    }

    public function isCashOnWebMaster () {
      $roleArr = $this->getTeams()->getValues();

      /** @var Role $role */
      foreach ($roleArr as $role){
        if (in_array($role->getName(), [Role::ROLE_CASH_ON_WEB_PARTNER])) {
          return true;
        }
      }
      return false;
    }

    public function isAdmin()
    {
        $roleArr = $this->getTeams()->getValues();
        /** @var Role $role */
        foreach ($roleArr as $role) {
            if (in_array($role->getName(), [
                Role::ROLE_ADMIN,
                Role::ROLE_MASTER_ADMIN,
            ], true)) {
                return true;
            }
        }
        return false;
    }

    public function getTeamName($removePrefix = false)
    {
        $platform = Util::platform();
        $starts = RoleService::getPlatformPrefixes();
        $prefix = $starts[$platform->getName()] ?? null;
        if (is_bool($removePrefix) && $removePrefix) {
            $removePrefix = $prefix;
        }

        /** @var Role $role */
        foreach ($this->getTeams() as $role) {
            $name = $role->getName();
            if ($prefix && !Util::startsWith($name, $prefix)) {
                continue;
            }
            if ($removePrefix) {
                $name = str_replace_first($removePrefix, '', $name);
            }
            return $name;
        }
        return null;
    }

    public function inTeam($team)
    {
        $roleArr = $this->getTeams()->getValues();
        /** @var Role $role */
        foreach ($roleArr as $role) {
            if ($role->getName() === $team) {
                return true;
            }
        }
        return false;
    }

    public function inTeams($teams)
    {
        $roleArr = $this->getTeams()->getValues();
        /** @var Role $role */
        foreach ($roleArr as $role) {
            if (Util::includes($teams, $role->getName())) {
                return true;
            }
        }
        return false;
    }

    public function isMasterAdmin()
    {
        return $this->isSuperAdmin();
    }

    public function isSuperAdmin()
    {
        return $this->inTeam(Role::ROLE_MASTER_ADMIN);
    }

    public function isMasterAgentAdmin()
    {
        return $this->inTeams([
            Role::ROLE_MASTER_ADMIN,
            Role::ROLE_TRANSFER_MEX_ADMIN
        ]);
    }

    public function isPlatformOwner()
    {
        return $this->inTeam(Role::ROLE_PLATFORM_OWNER);
    }

    public function isFisKPIUser()
    {
        return $this->inTeam(Role::ROLE_KPI_USER);
    }

    public function isTernEmployee()
    {
        return Util::isTernEmployee($this);
    }

    public function isInternalTester(): string|bool
    {
        return Util::isInternalTester($this);
    }

    public function isProgramOwner()
    {
        return $this->isBrandPartner();
    }

    public function isCorporateUser()
    {
        return $this->inTeam(Role::ROLE_CORPORATE_USER);
    }

    public function isCowAdmin()
    {
        return $this->inTeams([
          Role::ROLE_CASH_ON_WEB_ADMIN,
          Role::ROLE_CASH_ON_WEB_PARTNER,
          Role::ROLE_CASH_ON_WEB_AGENT,
          Role::ROLE_CASH_ON_WEB_COMPLIANCE,
          Role::ROLE_CASH_ON_WEB_SUPERVISOR]);
    }

    public function isPlatformRelated()
    {
        return $this->inTeams([
            Role::ROLE_PLATFORM_OWNER,
            Role::ROLE_PROGRAM_OWNER,
            Role::ROLE_CORPORATE_USER,
        ]);
    }

    public function isEsSoloTestAdmin()
    {
        if ($this->isSuperAdmin()) {
            return true;
        }
        $email = $this->getEmail();
        return in_array($email, [
            '<EMAIL>',
            '<EMAIL>',
        ]);
    }

    public function isClfAdmin()
    {
        return $this->inTeams([
            Role::ROLE_CLF_DISPENSARY_ADMIN,
            Role::ROLE_CLF_VENDOR_ADMIN,
            Role::ROLE_CLF_BANK_ADMIN,
        ]);
    }

    public function isClfMerchantAdmin()
    {
        return $this->inTeams([
            Role::ROLE_CLF_DISPENSARY_ADMIN,
            Role::ROLE_CLF_VENDOR_ADMIN,
        ]);
    }

    public function isSpendrConsumer()
    {
        return $this->inTeams([
            Role::ROLE_SPENDR_CONSUMER,
        ]);
    }

    public function isSpendrMerchantBalanceAdmin()
    {
        return $this->inTeams([
            Role::ROLE_SPENDR_MERCHANT_ADMIN,
        ]);
    }

    public function isDemoUser()
    {
        return $this->isSuperAdmin() || Util::json($this, 'meta', 'forDemo');
    }

    public function isApiInvoker()
    {
        return $this->inTeam(Role::ROLE_API_INVOKER);
    }

    public function isApiRunner()
    {
        return $this->getEmail() === ApiRunner::INNER_INVOKER;
    }

    public function isPublicApiRunner()
    {
        return $this->getEmail() === ApiRunner::NONE_INVOKER;
    }

    public function isAffiliate()
    {
        return $this->inTeam(Role::ROLE_AFFILIATE);
    }

    public function isConsumerServiceAgent()
    {
        return $this->inTeam(Role::ROLE_CONSUMER_SERVICE_AGENT);
    }

    public function isBrandPartner()
    {
        return $this->inTeam(Role::ROLE_PROGRAM_OWNER);
    }

    public function getApiRoles()
    {
        $roleArr = $this->getTeams()->getValues();
        $result = [];
        /** @var Role $role */
        foreach ($roleArr as $role) {
            if (Util::startsWith($role->getName(), Role::ROLE_API_INVOKER)) {
                $result[] = $role;
            }
        }
        return $result;
    }

    public function getCurrentRole($adminOnly = true)
    {
        /** @var ArrayCollection $roles */
        $roles = $this->getTeams();
        $current = Util::json($this, 'meta', 'currentRole');
        $all = [];
        /** @var Role $role */
        foreach ($roles as $role) {
            $name = $role->getName();
            $all[$name] = $role;
            if ($adminOnly && Role::isMember($name)) {
                continue;
            }
            if ($current && $name === $current) {
                return $role;
            }
        }
        $current = null;
        foreach (Role::ORDERS as $name) {
            if (empty($all[$name])) {
                continue;
            }
            if ($adminOnly && Role::isMember($name)) {
                continue;
            }
            $current = $all[$name];
            break;
        }
        if (!$current) {
            return null;
        }
        Util::updateJson($this, 'meta', [
            'currentRole' => $current->getName(),
        ]);
        return $current;
    }

    public function getCurrentRoleName()
    {
        $role = $this->getCurrentRole();
        return $role ? $role->getName() : null;
    }

    public function getCurrentRoleCustomName()
    {
        $role = $this->getCurrentRole();
        return $role ? $role->getCustomName() : null;
    }

    public function setCurrentRole($roleName)
    {
        $roles = $this->getTeams();
        /** @var Role $role */
        foreach ($roles as $role) {
            if ($role->getName() === $roleName) {
                Util::updateJson($this, 'meta', [
                    'currentRole' => $roleName,
                ]);
                break;
            }
        }
    }

    public function setCurrentEmployer($employer)
    {
        $employers = EmployerSubCompany::getSubCompany($this);
        if (in_array($employer, $employers) || !$employer) {
          Util::updateMeta($this, [
            'currentEmployer' => $employer
          ]);
        }
    }

    public function isConsumer()
    {
        $roleArr = $this->getTeams()->getValues();
        if (!$roleArr) {
            return true;
        }
		$platform = Util::platform();
        /** @var Role $role */
        foreach ($roleArr as $role){
            if ($role->getName() === Role::ROLE_CONSUMER) {
                return true;
            }
            if ($platform && $platform->isSpendr() && $role->getName() === Role::ROLE_SPENDR_CONSUMER) {
            	return true;
			}
        }
        return false;
    }

    public function isDemoAccount()
    {
        return (bool)Util::json($this, 'meta', 'demoAccount')
               || $this->getEmail() === '<EMAIL>';
    }

    public function showPlatformRoles($platformName, $prefix)
    {
        if (Bundle::isRoot() && $this->isSuperAdmin()) {
            return true;
        }
        $platform = Util::platform();
        if (!$platform) {
            return false;
        }
        if ($platformName === Platform::NAME_FAAS && $prefix === 'Faas ') {
            return Bundle::isFaas();
        }
        if ($platform->getName() === $platformName) {
            return true;
        }
        return false;
    }
}
