<?php

namespace SalexUserBundle\Entity;

use CoreB<PERSON>le\Entity\UserGroup;
use CoreBundle\Entity\UserIdVerify;
use CoreBundle\Utils\Util;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\ORM\Mapping\Index;
use FaasBundle\Entity\UserConfigTrait;
use UsUnlockedBundle\Services\ReferService;

/**
 * UserConfig
 *
 * @ORM\Table(name="user_config", indexes={
 *    @Index(columns={"bank_account_id"}),
 *    @Index(columns={"rain_user_id"}),
 *    @Index(columns={"mautic_contact_id"}),
 *    @Index(columns={"rain_register_date"}),
 * })
 * @ORM\Entity(repositoryClass="SalexUserBundle\Repository\UserConfigRepository")
 */
class UserConfig
{
    use UserConfigTrait;

    // Platform name constants:
    public const PLATFORM_LEAFLINK = 'Leaflink';
    public const PLATFORM_USUNLOCKED = 'USUnlocked';
    public const PLATFORM_CASHONWEB = 'CashOnWeb';
    public const PLATFORM_SPENDR = 'Spendr';

    public static function findByBankAccountId($bankAccountId)
    {
        if (!$bankAccountId) {
            return null;
        }
        return Util::em()->getRepository(static::class)
            ->findOneBy([
                'bankAccountId' => $bankAccountId,
            ]);
    }

    public static function findByRainUserId(?string $rainUserId = null)
    {
        if (!$rainUserId) {
            return null;
        }
        return Util::em()->getRepository(static::class)
            ->findOneBy([
                'rainUserId' => $rainUserId,
            ]);
    }

    public static function findByBotmUserId(?string $rainUserId = null)
    {
        if (!$rainUserId) {
            return null;
        }
        return Util::em()->getRepository(static::class)
            ->findOneBy([
                'botmUserId' => $rainUserId,
            ]);
    }

    public function persist()
    {
        Util::persist($this);
    }

    public function ensureReferCode()
    {
        $code = $this->getReferCode();
        if (!$code) {
            $user = $this->getUser();
            if (!$user) {
                return $code;
            }
            if ($user->everLoaded()) {
                $code = Util::generate_key(10);
                $this->setReferCode($code)
                    ->persist();
            }
        }
        return $code;
    }

    public function getReferrals()
    {
        return ReferService::getReferrals($this->getUser());
    }

    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @ORM\OneToOne(targetEntity="SalexUserBundle\Entity\User", inversedBy="config", cascade={"persist", "remove"})
     * @ORM\JoinColumn(name="user_id", referencedColumnName="id", nullable=false)
     */
    private $user;

    /**
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\UserIdVerify")
     */
    private $idVerification;

    /**
     * The group this user manages.
     * Note: The groups this user belongs to are saved in the `user_group_users` table.
     *
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\UserGroup", inversedBy="adminConfigs")
     */
    private $group;

    /**
     * @var string
     *
     * @ORM\Column(name="company_id", type="string", length=255, nullable=true)
     */
    protected $companyId;

    /**
     * @var string
     *
     * @ORM\Column(name="bank_account_id", type="string", length=255, nullable=true, unique=true)
     */
    protected $bankAccountId;

    /**
     * @var string
     *
     * @ORM\Column(name="company_name", type="string", length=255, nullable=true)
     */
    protected $companyName;

    /**
     * @var string
     *
     * @ORM\Column(name="platform", type="string", length=255, nullable=true)
     */
    protected $platform;

    /**
     * @var boolean
     *
     * @ORM\Column(name="unsubscribed", type="boolean", nullable=true, options={"default" : false})"
     */
    protected $unsubscribed;

    /**
     * @var string
     *
     * @ORM\Column(name="refer_code", type="string", length=255, nullable=true)
     */
    protected $referCode;

    /**
     * @ORM\ManyToOne(targetEntity="SalexUserBundle\Entity\User")
     */
    private $referrer;

    /**
     * @var string
     * @ORM\Column(name="device_id", type="string", length=255, nullable=true)
     */
    private $deviceId;

    /**
     * @var integer
     *
     * @ORM\Column(name="botm_user_id", type="integer", nullable=true)
     */
    protected $botmUserId;

    /**
     * @var integer
     *
     * @ORM\Column(name="botm_user_account_id", type="integer", nullable=true, unique=true)
     */
    protected $botmUserAccountId;

    /**
     * @var string
     *
     * @ORM\Column(name="rain_user_id", type="string", length=63, nullable=true)
     */
    protected $rainUserId;

     /**
     * @var \DateTime
     *
     * @ORM\Column(name="rain_register_date", type="datetime", nullable=true)
     */
    protected $rainRegisterDate;


     /**
     * @var \DateTime
     *
     * @ORM\Column(name="rain_status", type="string", nullable=true)
     */
    protected $rainStatus;

    /**
     * @var integer|null
     *
     * @ORM\Column(name="mautic_contact_id", type="integer", nullable=true)
     */
    protected ?int $mauticContactId = null;

    /**
     * @var string|null
     *
     * @ORM\Column(name="mautic_hash", type="string", length=63, nullable=true)
     */
    protected ?string $mauticHash = null;

    /**
     * @var string|null
     *
     * @ORM\Column(name="mautic_data", type="text", nullable=true)
     */
    private ?string $mauticData = null;

    /**
     * Get id
     *
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set user
     *
     * @param \SalexUserBundle\Entity\User $user
     *
     * @return UserConfig
     */
    public function setUser(\SalexUserBundle\Entity\User $user)
    {
        $this->user = $user;

        return $this;
    }

    /**
     * Get user
     *
     * @return \SalexUserBundle\Entity\User
     */
    public function getUser()
    {
        return $this->user;
    }

    /**
     * Set idVerification
     *
     * @param \CoreBundle\Entity\UserIdVerify $idVerification
     *
     * @return UserConfig
     */
    public function setIdVerification(\CoreBundle\Entity\UserIdVerify $idVerification = null)
    {
        $this->idVerification = $idVerification;

        return $this;
    }

    /**
     * Get idVerification
     *
     * @return \CoreBundle\Entity\UserIdVerify
     */
    public function getIdVerification()
    {
        return $this->idVerification;
    }

    /**
     * Set companyId
     *
     * @param string $companyId
     *
     * @return UserConfig
     */
    public function setCompanyId($companyId)
    {
        $this->companyId = $companyId;

        return $this;
    }

    /**
     * Get companyId
     *
     * @return string
     */
    public function getCompanyId()
    {
        return $this->companyId;
    }

    /**

     * Set bankAccountId
     *
     * @param string $bankAccountId
     *
     * @return UserConfig
     */
    public function setBankAccountId(string $bankAccountId)
    {
        $this->bankAccountId = $bankAccountId;

        return $this;
    }

    /**
     * Get bankAccountId
     *
     * @return string
     */
    public function getBankAccountId()
    {
        return $this->bankAccountId;
    }

    /**
     * Set companyName
     *
     * @param string $companyName
     *
     * @return UserConfig
     */
    public function setCompanyName($companyName)
    {
        $this->companyName = $companyName;

        return $this;
    }

    /**
     * Get companyName
     *
     * @return string
     */
    public function getCompanyName()
    {
        return $this->companyName;
    }

    /**
     * Set platform
     *
     * @param string $platform
     *
     * @return UserConfig
     */
    public function setPlatform($platform)
    {
        $this->platform = $platform;

        return $this;
    }

    /**
     * Get platform
     *
     * @return string
     */
    public function getPlatform()
    {
        return $this->platform;
    }

    /**
     * Set unsubscribed
     *
     * @param boolean $unsubscribed
     *
     * @return UserConfig
     */
    public function setUnsubscribed(bool $unsubscribed)
    {
        $this->unsubscribed = $unsubscribed;

        return $this;
    }

    /**
     * Get unsubscribed
     *
     * @return string
     */
    public function getUnsubscribed()
    {
        return $this->unsubscribed;
    }

    /**
     * Set referCode
     *
     * @param string $referCode
     *
     * @return UserConfig
     */
    public function setReferCode($referCode)
    {
        $this->referCode = $referCode;

        return $this;
    }

    /**
     * Get referCode
     *
     * @return string
     */
    public function getReferCode()
    {
        return $this->referCode;
    }

    /**
     * Set referrer
     *
     * @param \SalexUserBundle\Entity\User $referrer
     *
     * @return UserConfig
     */
    public function setReferrer(\SalexUserBundle\Entity\User $referrer = null)
    {
        $this->referrer = $referrer;

        return $this;
    }

    /**
     * Get referrer
     *
     * @return \SalexUserBundle\Entity\User
     */
    public function getReferrer()
    {
        return $this->referrer;
    }

    /**
     * Set group
     *
     * @param \CoreBundle\Entity\UserGroup $group
     *
     * @return UserConfig
     */
    public function setGroup(\CoreBundle\Entity\UserGroup $group = null)
    {
        $this->group = $group;

        return $this;
    }

    /**
     * Get group
     *
     * @return \CoreBundle\Entity\UserGroup
     */
    public function getGroup()
    {
        return $this->group;
    }

    /**
     * Set deviceId
     *
     * @param string $deviceId
     *
     * @return UserConfig
     */
    public function setDeviceId($deviceId)
    {
        $this->deviceId = $deviceId;

        return $this;
    }

    /**
     * Get deviceId
     *
     * @return string
     */
    public function getDeviceId()
    {
        return $this->deviceId;
    }

    public function isUnsubscribed(): ?bool
    {
        return $this->unsubscribed;
    }

    public function getBotmUserId(): ?int
    {
        return $this->botmUserId;
    }

    public function setBotmUserId(?int $botmUserId): static
    {
        $this->botmUserId = $botmUserId;

        return $this;
    }

    public function getBotmUserAccountId(): ?int
    {
        return $this->botmUserAccountId;
    }

    public function setBotmUserAccountId(?int $botmUserAccountId): static
    {
        $this->botmUserAccountId = $botmUserAccountId;

        return $this;
    }

    public function getRainUserId(): ?string
    {
        return $this->rainUserId;
    }

    public function setRainUserId(?string $rainUserId): static
    {
        $this->rainUserId = $rainUserId;

        return $this;
    }

    public function getRainRegisterDate(): ?\DateTimeInterface
    {
        return $this->rainRegisterDate;
    }

    public function setRainRegisterDate(?\DateTimeInterface $rainRegisterDate): static
    {
        $this->rainRegisterDate = $rainRegisterDate;

        return $this;
    }

    public function getRainStatus(): ?string
    {
        return $this->rainStatus;
    }

    public function setRainStatus(?string $rainStatus): static
    {
        $this->rainStatus = $rainStatus;

        return $this;
    }

    public function getMauticContactId(): ?int
    {
        return $this->mauticContactId;
    }

    public function setMauticContactId(?int $mauticContactId): static
    {
        $this->mauticContactId = $mauticContactId;

        return $this;
    }

    public function getMauticData(): ?string
    {
        return $this->mauticData;
    }

    public function setMauticData(?string $mauticData): static
    {
        $this->mauticData = $mauticData;

        return $this;
    }

    public function getMauticHash(): ?string
    {
        return $this->mauticHash;
    }

    public function setMauticHash(?string $mauticHash): static
    {
        $this->mauticHash = $mauticHash;

        return $this;
    }
}
