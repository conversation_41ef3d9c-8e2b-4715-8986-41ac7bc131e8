<?php

namespace SalexUserBundle\Entity;

use ApiBundle\Entity\ApiEntityInterface;
use Carbon\Carbon;
use CashOnWebBundle\Entity\UserCashTrait;
use ClfBundle\Entity\Account;
use CoreBundle\Constant\IdentityType;
use CoreBundle\Entity\Affiliate;
use CoreBundle\Entity\BaseState;
use CoreBundle\Entity\CardProgramFeeItem;
use CoreBundle\Entity\Config;
use CoreBundle\Entity\Country;
use CoreBundle\Entity\Email;
use CoreBundle\Entity\FeeGlobalName;
use CoreBundle\Entity\IpUsage;
use CoreBundle\Entity\LoadLocator;
use CoreBundle\Entity\LoadPartner;
use CoreBundle\Entity\LoginAttempt;
use CoreBundle\Entity\Notes;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\TenantUser;
use CoreBundle\Entity\UserBillingAddress;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Entity\UserCardTransaction;
use CoreBundle\Entity\UserDiscount;
use CoreBundle\Entity\UserDiscountLoad;
use CoreBundle\Entity\UserIdVerify;
use CoreBundle\Utils\Bundle;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\S3Storage;
use CoreBundle\Utils\Security;
use CoreBundle\Utils\Service;
use CoreBundle\Utils\Traits\TraceableEntityTrait;
use CoreBundle\Utils\Util;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\EntityNotFoundException;
use Doctrine\ORM\Event\PreUpdateEventArgs;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\ORM\Mapping\AttributeOverride;
use Doctrine\ORM\Mapping\AttributeOverrides;
use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Index;
use FaasBundle\Entity\UserFaasTrait;
use FisBundle\Entity\UserFisTrait;
use FOS\UserBundle\Model\User as BaseUser;
use Gedmo\Mapping\Annotation as Gedmo;
use Gedmo\SoftDeleteable\Traits\SoftDeleteableEntity;
use JMS\Serializer\Annotation as Serializer;
use LeafLinkBundle\Entity\UserLeafLinkTrait;
use PortalBundle\Util\RegisterStep;
use SalexUserBundle\Traits\UserAuthTrait;
use SalexUserBundle\Traits\UserCardTrait;
use SalexUserBundle\Traits\UserClfTrait;
use SalexUserBundle\Traits\UserRoleTrait;
use SalexUserBundle\Traits\UserTagTrait;
use SalexUserBundle\Traits\UserSecurityTrait;
use SalexUserBundle\Traits\UserTenantTrait;
use SkuxBundle\Entity\UserSkuxTrait;
use SpendrBundle\Services\BrazeService;
use SpendrBundle\Services\LocationService;
use SpendrBundle\SpendrBundle;
use SpendrBundle\Traits\UserSpendrTrait;
use Symfony\Component\HttpFoundation\File\File;
use Symfony\Component\PasswordHasher\Hasher\PasswordHasherAwareInterface;
use Symfony\Component\Validator\Constraints as Assert;
use TransferMexBundle\Entity\UserTransferMexTrait;
use UsUnlockedBundle\Entity\PayPalSubscription;
use UsUnlockedBundle\Entity\UserTrait;
use Vich\UploaderBundle\Mapping\Annotation as Vich;
use WilenBundle\Entity\UserWilenTrait;

/**
 * User
 *
 * @ORM\Table(name="users", indexes={
 *     @Index(name="email_idx", columns={"email"}),
 *     @Index(name="email_canonical_idx", columns={"email_canonical"}),
 *     @Index(name="username_idx", columns={"username"}),
 *     @Index(name="first_name_idx", columns={"first_name"}),
 *     @Index(name="last_name_idx", columns={"last_name"}),
 *     @Index(name="status_idx", columns={"status"}),
 *     @Index(name="deleted_at_idx", columns={"deleted_at"}),
 *     @Index(name="created_at_idx", columns={"created_at"}),
 *     @Index(name="locked_at_idx", columns={"locked_at", "deleted_at"}),
 *     @Index(name="watch_idx", columns={"watch"}),
 *     @Index(name="register_step_idx", columns={"register_step"}),
 *     @Index(name="status_watch_idx", columns={"status", "watch"}),
 * })
 * @AttributeOverrides({
 *      @AttributeOverride(name="usernameCanonical",
 *          column=@Column(
 *              type     = "string",
 *              nullable = false,
 *              unique   = false,
 *              length   = 180
 *          )
 *      ),
 *      @AttributeOverride(name="emailCanonical",
 *          column=@Column(
 *              type     = "string",
 *              nullable = false,
 *              unique   = false,
 *              length   = 180
 *          )
 *      )
 * })
 * @ORM\Entity(repositoryClass="SalexUserBundle\Repository\UserRepository")
 * @ORM\HasLifecycleCallbacks
 * @Gedmo\SoftDeleteable()
 */
#[Vich\Uploadable]
class User extends BaseUser implements ApiEntityInterface, PasswordHasherAwareInterface
{
    use TraceableEntityTrait;
    use UserRoleTrait;
    use UserTagTrait;
    use UserClfTrait;
    use UserSecurityTrait;
    use UserTenantTrait;
    use UserCardTrait;
    use UserAuthTrait;
    use UserTrait;
    use UserLeafLinkTrait;
    use UserFisTrait;
    use UserTransferMexTrait;
    use UserCashTrait;
    use UserWilenTrait;
    use UserFaasTrait;
    use UserSpendrTrait;
    use UserSkuxTrait;

    const REGISTERING_CARD = 'registering_card';
    const LOADING_CARD = 'loading_card';

    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';
    const STATUS_CLOSED = 'closed';
    const STATUS_BANNED = 'banned';
    const STATUS_UNDER_REVIEW = 'under_review';

    const LOCK_STATUS_LOCK = 'Lock';
    const LOCK_STATUS_UNLOCK = 'Unlock';

    const FLAG_UNVALIDATED_ADDRESS = 'Unvalidated address';

    const FLAG_DISCOUNTED = 'Discounted';
    const FLAG_BAD_AP_ASSOCIATION = 'Bad IP Association';
    const FLAG_BANNED_USER = 'Banned User';
    const FLAG_FAVORED_STANDING = 'Favored Standing';
    const FLAG_BYPASS_OFAC = 'Bypass OFAC';

    const SOURCE_SELF = 'self';
    const SOURCE_API = 'api';
    const SOURCE_ADMIN = 'admin';

    const CLIENT_WEB = 'web';
    const CLIENT_DESKTOP = 'desktop';
    const CLIENT_MOBILE = 'mobile';

    const GENDER_MALE = 'Male';
    const GENDER_FEMALE = 'Female';

    const AVATAR_S3_PREFIX = 'secure/profile_image/';

    public function getRainApplicationStatus(): ?string {
        $status = Util::meta($this, 'rainApplicationStatus');
        $config = $this->ensureConfig();
        return $config->getRainStatus() ?? $status;
    }

    public function getPasswordHasherName(): ?string
    {
        return 'sha256salted';
    }

    public function getOldKey() {
        $oldKeyInfo = Util::meta($this, 'oldApiKey');
        $key = '';
        if ($oldKeyInfo && $oldKeyInfo['key']){
          $key = $oldKeyInfo['expireAt'] > Carbon::now() ? $oldKeyInfo['key'] : '';
        }
        return $key;
    }

    public function toFinalApiArray()
    {
        $data = $this->toApiArray(true);

        foreach (['emailInactive', 'lastLoginAt', 'teams', 'currentRole', 'passwordExpiryDisabled'] as $item) {
            unset($data[$item]);
        }

        return $data;
    }

    public function toApiArray(bool $extra = FALSE): array
    {
        $data = [
            'id' => $this->getId(),
            'username' => $this->getUsername(),
            'firstName' => $this->getFirstName(),
            'lastName' => $this->getLastName(),
            'status' => $this->getStatus(),
            'emailInactive' => $this->isEmailInactive(),
            'lastLoginAt' => Util::isoDate($this->getLastLogin()),
            'teams' => $this->getTeams()->map(function (Role $role) {
                return $role->getName();
            })->toArray(),
            'currentRole' => $this->getCurrentRoleName(),
        ];
        if (Bundle::isSpendr() && $this->inTeams(SpendrBundle::getConsumerRoles())) {
            $data['brazeExternalId'] = BrazeService::getExternalPrefix() . $this->getId();
        }

        if ($extra) {
            $birthday = $this->getBirthday();
            $country = $this->getCountry();
            $state = $this->getState();
            $data = array_merge($data, [
                'fullName' => $this->getFullName(),
                'email' => $this->getEmail(),
                'gender' => $this->getGender(),
                'locked' => $this->getLockedStatus(),
                'avatar' => $this->getAvatarUrl(),
                'birthday' => $birthday ? $birthday->format(Util::DATE_FORMAT_ISO_FULL_DATE) : null,
                'address' => trim($this->getAddress()),
                'address2' => trim($this->getAddressline()),
                'country' => $country ? $country->toApiArray() : null,
                'state' => $state ? $state->toApiArray() : null,
                'status' => $this->getStatus(),
                'city' => $this->getCity(),
                'zip' => $this->getZip(),
                'homePhone' => $this->getPhone(),
                'mobilePhone' => $this->getMobilephone(),
                'workPhone' => $this->getWorkphone(),
                'vip' => $this->getVip(),
                'flags' => $this->getFlagsname(),
                'passwordExpiryDisabled' => $this->isPasswordExpiryDisabled(),
            ]);

            if (Bundle::isClf()) {
                $account = Account::getByUser($this);
                $data['account'] = $account->toApiArray(true);
                $data['cpKey'] = null;
                $data['preference'] = $this->getPreferences();
                $data = Bundle::common('apiAdminUserProfile', [$data], $data);
            } else if (Bundle::isSpendr() && $this->inTeams(SpendrBundle::getMerchantEmployeeRoles())) {
                $spendrExtraArray = [
                    'role'     => $this->getRole(),
                    'pin'      => $this->getPin(),
                ];

				$location = LocationService::getCurrentLocation(true, false);
                if ($location) {
                    $spendrExtraArray['location'] = $location->toApiArray();
                }
                $data = array_merge($data, $spendrExtraArray);
            }
        }

        return $data;
    }

    public function toSpendrClerkArray()
    {
        $data = [
            'id' => $this->getId(),
            'firstName' => $this->getFirstName(),
            'lastName' => $this->getLastName(),
            'fullName' => $this->getFullName(),
            'email' => $this->getEmail(),
            'mobilePhone' => $this->getMobilephone(),
            'status' => $this->getStatus(),
            'role'     => $this->getRole(),
            'pin' => $this->getPin(),
//            'pin' => $this->getPin() ? SSLEncryptionService::encrypt($this->getPin()) : null
        ];
        $location = LocationService::getCurrentLocation(true, false);
        if ($location) {
            $data['location'] = $location->toApiArray();
        }
        return $data;
    }

    public function toPartnerAdminArray() {
        return [
          'Admin Name' => $this->getFullName(),
          'Admin ID' => $this->getId(),
          'Date & time Assigned' => Util::formatDateTime($this->getCreatedAt()),
          'Admin Email' => $this->getEmail(),
          'Admn Phone Number' => $this->getMobilephone(),
          'Status' => $this->getStatus(),
        ];
    }

    public function isEmailInactive()
    {
        return Config::isEmailInactive($this->getEmail());
    }

    /**
     * @ORM\PreUpdate()
     * @param PreUpdateEventArgs $event
     */
    public function preUpdate(PreUpdateEventArgs $event)
    {
        $fields = ['status', 'vip', 'affiliate', 'billingAddress'];
        $changed = false;
        foreach ($fields as $field) {
            if ($event->hasChangedField($field)) {
                $changed = true;
                break;
            }
        }
        if ($changed) {
            Service::identify($this);
        }
    }

    public static function getIdByUsername($username)
    {
        $entity = Util::em()->getRepository(\SalexUserBundle\Entity\User::class)
            ->findOneBy(['username' => $username]);
        if ($entity) {
            return $entity->getId();
        }
        return 0;
    }

    /**
     * @param $loadType
     * @return UserCardLoad|null
     */
    public function getLastLoad($loadType)
    {
        $all = new ArrayCollection();
        /** @var UserCard $uc */
        foreach ($this->getIssuedCards() as $uc) {
            $loads = $uc->getLoads($loadType);
            foreach ($loads as $load) {
                $all->add($load);
            }
        }
        return Util::last($all);
    }

    /**
     * @return null|UserCardTransaction
     */
    public function getLastPosTransaction() {
        $expr = Util::expr();
        $rs = Util::em()->getRepository(\CoreBundle\Entity\UserCardTransaction::class)
            ->createQueryBuilder('uct')
            ->join('uct.userCard', 'uc')
            ->where($expr->eq('uc.user', ':user'))
            ->andWhere($expr->in('uct.actualTranCode', ':codes'))
            ->setParameter('user', $this)
            ->setParameter('codes', [
                UserCardTransaction::CODE_POS_PURCHASE,
            ])
            ->orderBy('uct.txnTime', 'desc')
            ->setMaxResults(1)
            ->getQuery()
            ->getResult();
        if ($rs) {
            return $rs[0];
        }
        return null;
    }

    public function getPermissions($all = false)
    {
        $permissions = [[]];
        $currentRole = $this->getCurrentRole();
        /** @var Role $role */
        foreach ($this->getTeams() as $role) {
            if ($all || Util::eq($currentRole, $role)) {
                $permissions[] = $role->getSectionNames();
            }
        }
        return array_values(array_unique(array_merge(...$permissions)));
    }

    public function getEditableModules($all = false)
    {
        $permissions = [[]];
        $currentRole = $this->getCurrentRole();
        /** @var Role $role */
        foreach ($this->getTeams() as $role) {
            if ($all || Util::eq($currentRole, $role)) {
                $permissions[] = $role->getEditableModules();
            }
        }
        return array_values(array_unique(array_merge(...$permissions)));
    }

    public function logIP($type = 'login')
    {
        $request = Util::request();

        $iu = new IpUsage();
        $iu->setUsers($this);
        $iu->setType($type);
        $iu->setLoginIp(Security::getClientIp());
        $iu->setLoginInfromation($request->headers->get('User-Agent', ''));
        $iu->setLoginTime(new \DateTime());
        Util::persist($iu);
    }

    public function logLogin()
    {
        $this->logIP();

        // Update login fields
        $now = new \DateTime();
        if (!$this->getFirstLogin()) {
            $this->setFirstLogin($now);
        }
        $this->setLastLogin($now);

        $count = $this->getLoginCount() || 0;
        $this->setLoginCount($count + 1);

        Util::persist($this);
    }

    public function isLocked()
    {
        return $this->getLockedStatus() === self::LOCK_STATUS_LOCK;
    }

    public function isVIP()
    {
        return (boolean)$this->getVip();
    }

    public function isTestData()
    {
        return Util::isTestEmail($this->getEmail());
    }

    public function getDashboardType()
    {
        $type = Bundle::common('getUserDashboardType', [$this]);

        if ($type) {
            return $type;
        }

        if ($this->isAffiliate()) {
            return 'affiliate';
        }
        if ($this->isPlatformRelated()) {
            return 'analytics';
        }
        $platform = Util::platform();
        if ($platform && $platform->isTernCommerce()) {
            return 'usu';
        }
        return 'common';
    }

    public function getPreferences()
    {
        $all = Util::json($this, 'meta', 'preference');
        if ($all) {
            foreach ($all as $k => $v) {
                if (is_string($v)) {
                    $all[$k] = json_decode($v, true);
                }
            }
            return $all;
        }
        return [];
    }

    public function setPreference($k, $v = null)
    {
        $meta = Util::json($this, 'meta');
        $all = $meta['preference'] ?? [];
        if (is_array($k)) {
            foreach ($k as $i => $j) {
                $all[$i] = json_encode($j);
            }
        } else {
            $all[$k] = json_encode($v);
        }
        $meta['preference'] = $all;
        Util::updateJson($this, 'meta', $meta);
    }

    public function getFullName()
    {
        return $this->getName();
    }

    public function getSignature()
    {
        return $this->getId() . ', ' . $this->getFullName() . ', ' . $this->getEmail();
    }

    public function getIdEmail(): string
    {
        return $this->getId() . ' - ' . $this->getEmail();
    }

    public function getFullNameAbbr($max = 19)
    {
        $first = $this->getFirstNameWord() ?: '';
        $last = $this->getLastName() ?: '';
        $full = trim($first . ' ' . $last);
        if (mb_strlen($full) <= $max) {
            return $full;
        }
        $last = $last ? mb_substr($last, 0, 1) : $last;
        $full = trim($first . ' ' . $last);
        if (mb_strlen($full) <= $max) {
            return $full;
        }
        $first = $first ? mb_substr($first, 0, $max - 2) : $first;
        return trim($first . ' ' . $last);
    }

    public function getFirstNameWord()
    {
        $n = $this->getFirstName();
        $ns = explode(' ', $n);
        if (!$ns) {
            return $n;
        }
        return $ns[0];
    }

    public function setName($name)
    {
        $names = explode(' ', $name);
        $count = count($names);
        if ($count === 1) {
            $this->setFirstName($names[0]);
            $this->setLastName($names[0]);
        } else if ($count === 2) {
            $this->setFirstName($names[0]);
            $this->setLastName($names[1]);
        } else if ($count > 2) {
            $this->setFirstName(implode(' ', array_slice($names, 0, 2)));
            $this->setLastName(implode(' ', array_slice($names, 2)));
        }
    }

    public function getAddresses()
    {
        return trim($this->getAddress() . ' ' . $this->getAddressline());
    }

    public function getFullAddress($unicodeEncode = false)
    {
        $state = $this->getState();
        $country = $this->getCountry();
        $s = $this->getAddress() . ' ' . $this->getAddressline()
            . ', ' . $this->getCity()
            . ', ' . ($state ? $state->getAbbrOrName() : '') . ' ' . $this->getZip()
            . ', ' . ($country ? $country->getIsoCode() : '');
        if ($unicodeEncode) {
            $s = Util::unicodeString($s);
        }
        return $s;
    }

    public function getMemoForFirstView()
    {
        $memo = ' Customer Address: ' . $this->getFullAddress(true);
        if ($this->isIdVerified()) {
            $uiv = $this->getIdVerify();
            $params['DePpMemos'] = $uiv->getIdUrlString() . ' ' . $memo;
        }
        $memo = str_replace(["'", '&'], ' ', $memo);
        return trim($memo);
    }

    /*
     * Overwrote methods
     */
    public function getLastLogin($format = false)
    {
        $date = parent::getLastLogin();
        if (!$date) {
            return $format ? '' : null;
        }
        if ($format) {
            $date = $date->format(Util::DATE_TIME_FORMAT);
        }
        return $date;
    }

    /**
     * Remove this feature if anyone has optimized the countryid field.
     */
    public function getParsedCountry()
    {
        if (!$this->countryid) {
            return null;
        }
        return Util::em()->getRepository(\CoreBundle\Entity\Country::class)->find($this->countryid);
    }

    public function getParsedState()
    {
        if (!$this->stateid) {
            return null;
        }
        return Util::em()->getRepository(BaseState::class)->find($this->stateid);
    }

    /**
     * Get idVerifies
     *
     * @param bool $withAdminChanges
     * @return Collection
     */
    public function getIdVerifies($withAdminChanges = true)
    {
        return $this->idVerifies->filter(function (UserIdVerify $v) use ($withAdminChanges) {
            if (!$withAdminChanges && !$v->isCalledByUser()) {
                return false;
            }
            return $v->getCountry() && $v->getType() && $v->getProvider();
        });
    }

    public function getIdVerifyStatus($withAdminChanges = true)
    {
        $uiv = $this->getIdVerify();
        if ($uiv) {
            return $uiv->getStatus();
        }
        return UserIdVerify::STATUS_NONE;
    }

    public function isIdVerified()
    {
        return in_array($this->getIdVerifyStatus(), [
            UserIdVerify::STATUS_ACCEPTED,
            UserIdVerify::STATUS_MANUAL_ACCEPTED,
        ], true);
    }

    /**
     * Get idVerify
     *
     * @param bool $createNewIfFinished
     * @return UserIdVerify|null
     */
    public function getIdVerify($createNewIfFinished = false)
    {
        $uiv = null;
        $config = $this->getConfig();
        if ($config) {
            $uiv = $config->getIdVerification();
        }

        if (!$uiv) {
            $vs = $this->getIdVerifies();
            /** @var UserIdVerify $uiv */
            $uiv = Util::last($vs);
        }

        if ($createNewIfFinished) {
            if (!$uiv || $uiv->isFinished()) {
                $uiv = new UserIdVerify();
                $uiv->setUser($this);
                $uiv->setType(IdentityType::ID);
            }
        }
        return $uiv;
    }

    public function getValidPhone()
    {
        return $this->getMobilephone() ?: $this->getWorkphone() ?: $this->getPhone();
    }

    /**
     * @param UserCard $uc
     * @return UserBillingAddress|null
     */
    public function getBillingAddress (UserCard $uc = null)
    {
        if (!$uc) {
            $uc = $this->getOneCardInPlatform();
        }
        if ($uc) {
            $ba = $uc->getBillingAddress();
            if ($ba) {
                return $ba;
            }
        }
        /** @var UserCard $_uc */
        foreach($this->getCards(false) as $_uc) {
            $ba = $_uc->getBillingAddress();
            if ($ba) {
                return $ba;
            }
        }
        return null;
    }

    public function getCurrency()
    {
        $currency = null;
        /** @var UserCard $uc */
        foreach ($this->getCards() as $uc) {
            if (!$currency) {
                $currency = $uc->getCurrency();
            }
        }
        return $currency ?: 'USD';
    }

    public function getBalance()
    {
        $amount = 0;
        $currency = $this->getCurrency();
        /** @var UserCard $uc */
        foreach ($this->getActiveCards() as $uc) {
            $balance = $uc->getBalance();
            $c = $uc->getCurrency();
            if ($balance && $c) {
                $amount += Money::convert($balance, $c, $currency);
            }
        }
        return $amount;
    }

    public function getBalanceText()
    {
        $amount = $this->getBalance();
        $currency = $this->getCurrency();
        return Money::format($amount, $currency);
    }

    /**
     * Set status
     *
     * @param string $status
     *
     * @param null|boolean|string $note
     * @return User
     */
    public function setStatus($status, $note = null)
    {
        $oldStatus = $this->status;
        if ($this->status !== $status) {
            if ($status === self::STATUS_ACTIVE) {
                $this->setEnabled(true);
            }

            if ($note === false) {
                goto none;
            }

            $template = null;
            $cardTemplate = null;
            $cardNums = [];
            $uc = $this->getCards()->first();
            if ($status === self::STATUS_BANNED) {
                $this->addFlag(self::FLAG_BANNED_USER);
            }
            if ($status === self::STATUS_CLOSED || $status === self::STATUS_BANNED) {
                // Close user's cards.
                $cards = $this->getActiveCards();
                /** @var UserCard $card */
                foreach ($cards as $card) {
                    $uc = $card;
                    $card->setStatus(UserCard::STATUS_INACTIVE)
                        ->persist();
                    $cardNums[] = $card->getCard()->getFullName() . ' (' . $card->getPan('mask') . ') ';
                }
                $template = $status === self::STATUS_CLOSED
                    ? Email::TEMPLATE_USER_CLOSED
                    : Email::TEMPLATE_USER_BANNED;
                $cardTemplate = Email::TEMPLATE_CARD_CLOSED;
            } else if ($status === self::STATUS_UNDER_REVIEW) {
                $template = Email::TEMPLATE_USER_UNDER_REVIEW;

                $cards = $this->getIssuedCards();
                /** @var UserCard $card */
                foreach ($cards as $card) {
                    if ($card->getStatus() === UserCard::STATUS_ACTIVE && !$card->isDummy() && $card->isUsUnlocked()) {
                        $uc = $card;
                        $card->setStatus(UserCard::STATUS_INACTIVE)
                            ->setShippingMethod($status) // Temp field for USU
                            ->persist();
                        $cardNums[] = $card->getCard()->getFullName() . ' (' . $card->getPan('mask') . ') ';
                    }
                }
            } else if ($oldStatus === self::STATUS_UNDER_REVIEW && $status === self::STATUS_ACTIVE) {
                $cards = $this->getCardsInUsUnlocked();
                /** @var UserCard $card */
                foreach ($cards as $card) {
                    if ($card->getShippingMethod() === $oldStatus && $card->getStatus() === UserCard::STATUS_INACTIVE) {
                        $card->setStatus(UserCard::STATUS_ACTIVE)
                            ->setShippingMethod(null)
                            ->persist();
                    }
                }
            }
            if ($template) {
                Service::log('User ' . $this->getId() . ' is ' . $status, [
                    'note' => $note,
                ], 'warn');

                if (Bundle::isUsUnlocked()) {
                    $cp = $uc ? $uc->getCard()->getCardProgram() : null;
                    $cardNums = implode(', ', $cardNums);
                    Email::sendWithTemplateToUser($this, $template, [
                        'card' => $cardNums,
                    ], $cp);
                    if ($cardTemplate && $cardNums) {
                        Email::sendWithTemplateToUser($this, $cardTemplate, [
                            'card' => $cardNums,
                        ], $cp);
                    }
                }

                if ($note) {
                    Util::persist($this->addNote($note, false));
                }
            }

            $this->addNote('Updated status to ' . $status, false);
        }

        none:

        if ($status && $status !== self::STATUS_ACTIVE) {
            $this->setRegisterStep(RegisterStep::INACTIVE);
        }

        $this->status = $status;

        return $this;
    }

    public function getAvailableLocalBalance($currency)
    {
        $result = [
            'cards' => [],
            'sum' => 0,
        ];
        /** @var UserCard $uc */
        foreach ($this->getIssuedCards() as $uc) {
            $local = $uc->getLocalBalance();
            if ($local) {
                $id = $uc->getId();
                $localCurrency = $uc->getCurrency();
                $result['cards'][$id] = [
                    'amount'   => $local,
                    'currency' => $localCurrency,
                ];
                $result['sum'] += Money::convert($local, $localCurrency, $currency);
            }
        }

        return $result;
    }

    public function getDateTimeZone()
    {
        $tz = Util::timezone(null, $this);
        if ($tz) {
            return new \DateTimeZone($tz);
        }
        return null;
    }

    public function hasFlag($flag)
    {
        return in_array($flag, $this->getFlagsname(), true);
    }

    public function addFlag($flag)
    {
        if (!$flag) {
            return $this;
        }

        $this->addNote('Added flag ' . $flag, false);

        $flags = $this->getFlagsname();
        $flags[] = $flag;
        return $this->setFlagsname(implode(';', array_unique($flags)));
    }

    public function removeFlag($flag)
    {
        $this->addNote('Removed flag ' . $flag, false);

        $flags = $this->getFlagsname();
        return $this->setFlagsname(implode(';', array_diff($flags, [$flag])));
    }

    public function getBannedReasonText()
    {
        $rs = Util::s2j($this->getBannedReason());
        return implode('. ', $rs);
    }

    public function getBannedReasonHtml()
    {
        $rs = Util::s2j($this->getBannedReason());
        if ($rs) {
            return '<span class="label label-warning">'
                . implode('</span><span class="label label-warning">', $rs)
                . '</span>';
        }
        return '';
    }

    public function getClosureReasonText()
    {
        $rs = Util::s2j($this->getClosureReason());
        return implode('. ', $rs);
    }

    public function getClosureReasonHtml()
    {
        $rs = Util::s2j($this->getClosureReason());
        if ($rs) {
            return '<span class="label label-warning">'
                . implode('</span><span class="label label-warning">', $rs)
                . '</span>';
        }
        return '';
    }

    public function isImported()
    {
        $meta = Util::s2j($this->getMeta());
        return isset($meta['imported']) && $meta['imported'];
    }

    public function isActive()
    {
        return $this->isEnabled() && self::STATUS_ACTIVE === $this->getStatus();
    }

    public function isClosed()
    {
        return !$this->isEnabled() || self::STATUS_CLOSED === $this->getStatus();
    }

    public function isBanned()
    {
        return self::STATUS_BANNED === $this->getStatus();
    }

    public function isClosedOrBanned()
    {
        return in_array($this->getStatus(), [
            self::STATUS_BANNED,
            self::STATUS_CLOSED,
        ]);
    }

    public function isErased()
    {
        return $this->getEmail() === 'deleted';
    }

	public function isTwoFAEnabled()
	{
		$meta = Util::s2j($this->getMeta());
		return isset($meta['twoFAEnabled']) && $meta['twoFAEnabled'];
	}

	public function getNotes()
    {
        return Util::em()->getRepository(\CoreBundle\Entity\Notes::class)
            ->createQueryBuilder('n')
            ->where('n.toname = :id')
            ->andWhere('n.deletedAt is null')
            ->orderBy('n.createdtime', 'desc')
            ->setParameter('id', $this->getId())
            ->getQuery()
            ->getResult();
    }

    public function getEmployerNotes($employerId)
    {
        return Util::em()->getRepository(\CoreBundle\Entity\Notes::class)
            ->createQueryBuilder('n')
            ->where('n.toname = :id')
            ->andWhere('n.deletedAt is null')
            ->andWhere('n.fromname =:employerId')
            ->orderBy('n.createdtime', 'desc')
            ->setParameter('id', $this->getId())
            ->setParameter('employerId', $employerId)
            ->getQuery()
            ->getResult();
    }

    public function getCloseReason()
    {
        if ($this->isClosed()) {
            $notes = $this->getNotes();
            $prefix = 'Account closed because ';
            /** @var Notes $note */
            foreach ($notes as $note) {
                $msg = $note->getNotes();
                if (Util::startsWith($msg, $prefix)) {
                    $msg = ucfirst(str_replace_first($prefix, '', $msg));
                    if (!Util::endsWith($msg, '.')) {
                        $msg .= '.';
                    }
                    return $msg . ' Please contact support with any questions';
                }
            }
        }
        return $this->getClosureReasonText() ?: '';
    }

    public function getManagingAffiliates($all = false)
    {
        $mas = $this->managingAffiliates;
        if ($all) {
            $tenant = $this->getAffiliateTenant();
            if ($tenant) {
                $ta = $tenant->getAffiliate();
                if (!Util::includes($mas, $ta)) {
                    $mas->add($ta);
                }
            }
        }
        return $mas;
    }

    public function getManagingAffiliate()
    {
        $as = $this->getManagingAffiliates(true);
        if (!$as->isEmpty()) {
            return $as[0];
        }
        return null;
    }

    /**
     * @return \CoreBundle\Entity\Tenant|null
     */
    public function getTenant()
    {
        $tus = $this->getTenantUsers();
        if ($tus->isEmpty()) {
            return null;
        }
        /** @var TenantUser $tu */
        foreach ($tus as $tu) {
            if ($tu->getMain()) {
                return $tu->getTenant();
            }
        }
        return $tus->first()->getTenant();
    }

    /**
     * @return \CoreBundle\Entity\Tenant|null
     */
    public function getAffiliateTenant()
    {
        $tus = $this->getTenantUsers();
        /** @var TenantUser $tu */
        foreach ($tus as $tu) {
            $t = $tu->getTenant();
            if ($t->getAffiliate()) {
                return $t;
            }
        }
        return null;
    }

    public function getCountryName()
    {
        $country = $this->getCountry();
        return $country ? $country->getName() : '';
    }

    public function getCountryRegion()
    {
        $country = $this->getCountry();
        return $country ? $country->getRegion() : '';
    }

    public function getCountryCode(bool $iso3 = false)
    {
        $country = $this->getCountry();
        if (!$country) {
            return null;
        }
        return $iso3 ? $country->getIso3Code() : $country->getIsoCode();
    }

    public function getStateNameOrAbbr(bool $use3 = false)
    {
        $state = $this->getState();
        if (!$state) {
            return null;
        }
        return $use3 ? $state->getAbbr3OrName() : $state->getAbbrOrName();
    }

    public function getStateName()
    {
        $state = $this->getState();
        return $state ? $state->getName() : '';
    }

    public function getAffiliateName()
    {
        $aff = $this->getAffiliate();
        if ($aff) {
            return $aff->getAffName();
        }
        return '';
    }

    public function getAccessibleAffiliateTypes()
    {
        $types = [];
        /** @var Affiliate $affiliate */
        foreach ($this->getAccessibleAffiliates() as $affiliate) {
            $type = $affiliate->getAffType();
            $types[$type][] = $affiliate;
        }
        return $types;
    }

    /**
     * Get accessibleAffiliates
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getAccessibleAffiliates()
    {
        if ($this->isSuperAdmin()) {
            return new ArrayCollection(
                Util::em()->getRepository(\CoreBundle\Entity\Affiliate::class)->findAll()
            );
        }
        $aa = $this->accessibleAffiliates ? $this->accessibleAffiliates->toArray() : [];
        $as = $this->getLockedAccessibleAffiliates();
        foreach ($as as $a) {
            if (Util::includes($aa, $a)) {
                continue;
            }
            $aa[] = $a;
        }
        return new ArrayCollection($aa);
    }

    public function getLockedAccessibleAffiliates()
    {
        $tenants = Util::em()->getRepository(\CoreBundle\Entity\Affiliate::class)
            ->createQueryBuilder('a')
            ->join('a.tenant', 'tenant')
            ->join('tenant.contacts', 'contacts')
            ->where('contacts.user = :user')
            ->setParameter('user', $this)
            ->getQuery()
            ->getResult();
        $users = Util::em()->getRepository(\CoreBundle\Entity\Affiliate::class)
            ->createQueryBuilder('a')
            ->where('a.admin = :user')
            ->setParameter('user', $this)
            ->getQuery()
            ->getResult();
        return Util::uniqueEntities(array_merge($tenants, $users));
    }

    public function getDefaultUrl()
    {
        if (!$this->hasNonConsumerRole()) {
            return '/';
        }
        if ($this->isMasterAdmin()) {
            if (Bundle::isClf()) {
                return '/admin#/a/clf/sa';
            }
        }
        $roles = [];
        /** @var Role $role */
        foreach ($this->getTeams() as $role) {
            $url = $role->getDefaultUrl();
            if ($url) {
                $roles[$role->getName()] = $url;
            }
        }

        foreach (Role::ORDERS as $name) {
            if (!empty($roles[$name])) {
                return $roles[$name];
            }
        }
        return '/admin';
    }

    public function everLoaded(UserCardLoad $except = null, $return = false, $withRefunded = false)
    {
        $loadStatuses = UserCardLoad::RECEIVED_STATUS_ARRAY;
        if ($withRefunded) {
            $loadStatuses[] = UserCardLoad::LOAD_STATUS_REFUNDED;
        }

        $expr = Util::expr();
        $q = Util::em()->getRepository(UserCardLoad::class)
            ->createQueryBuilder('ucl')
            ->join('ucl.userCard', 'uc')
            ->where('uc.user = :user')
            ->andWhere($expr->in('ucl.loadStatus', ':loadStatuses'))
            ->andWhere($expr->orX(
                $expr->isNull('ucl.partner'),
                $expr->neq('ucl.partner', ':partner')
            ))
            ->setParameter('user', $this)
            ->setParameter('loadStatuses', $loadStatuses)
            ->setParameter('partner', LoadPartner::get(LoadPartner::SYSTEM));

        if ($except) {
            $q->andWhere('ucl <> :except')
                ->setParameter('except', $except);
        }

        if ($return) {
            return $q->select('ucl')
                ->getQuery()
                ->getResult();
        }

        $rs = $q->setMaxResults(1)
            ->select('ucl.id')
            ->getQuery()
            ->getResult()
        ;
        return count($rs) > 0;
    }

    /**
     * @return UserCardLoad|null
     * @throws \Doctrine\ORM\NonUniqueResultException
     */
    public function getFirstLoadedLoad()
    {
        $ucl = null;
        $id = Util::meta($this, 'firstLoadedLoad');
        if ($id) {
            $ucl = UserCardLoad::find($id);
        }
        if (!$ucl) {
            $expr = Util::expr();
            /** @var UserCardLoad $ucl */
            $ucl = Util::em()->getRepository(UserCardLoad::class)
                ->createQueryBuilder('ucl')
                ->join('ucl.userCard', 'uc')
                ->where('uc.user = :user')
                ->andWhere($expr->eq('ucl.loadStatus', ':loadStatus'))
                ->andWhere($expr->isNotNull('ucl.loadAt'))
                ->andWhere($expr->isNotNull('ucl.partner'))
                ->andWhere($expr->neq('ucl.partner', ':partner'))
                ->setParameter('user', $this)
                ->setParameter('loadStatus', UserCardLoad::LOAD_STATUS_LOADED)
                ->setParameter('partner', LoadPartner::get(LoadPartner::SYSTEM))
                ->orderBy('ucl.loadAt')
                ->setMaxResults(1)
                ->getQuery()
                ->getOneOrNullResult()
            ;
            if ($ucl) {
                Util::updateMeta($this, [
                    'firstLoadedLoad' => $ucl->getId(),
                ]);
            }
        }
        return $ucl;
    }

    public function isWatching()
    {
        return $this->getWatch();
    }

    /**
     * Set affiliate
     *
     * @param \CoreBundle\Entity\Affiliate $affiliate
     *
     * @return User
     */
    public function setAffiliate(\CoreBundle\Entity\Affiliate $affiliate = null)
    {
        if ($this->getId() && !Util::eq($this->affiliate, $affiliate)) {
            $this->addNote('Updated affiliate to '
                . ($affiliate ? $affiliate->getAffName() : 'null')
                . ' from '
                . ($this->affiliate ? $this->affiliate->getAffName() : 'null'));
        }
        $this->affiliate = $affiliate;

        if ($this->getId()) {
            $this->updateAffiliateDiscount();
        }

        return $this;
    }

    public function updateAffiliateDiscount($loadMethodName = null)
    {
        $affiliate = $this->getAffiliate();
        if (!$affiliate) {
            return;
        }
        $em = Util::em();
        $repo = $em->getRepository(\CoreBundle\Entity\CardProgram::class);
        $names = [
            FeeGlobalName::ONE_TIME_MEMBERSHIP_FEE,
        ];
        if ($loadMethodName) {
            $names[] = $loadMethodName;
        }
        /** @var UserCard $uc */
        foreach ($this->getActiveCards() as $uc) {
            $cp = $uc->getCard()->getCardProgram();
            foreach ($names as $name) {
                $feeItem = $repo->getFeeItem($cp, $name, $this);
                if ($feeItem) {
                    $this->updateAffiliateDiscountForFeeItem($affiliate, $feeItem);
                }
            }
        }
    }

    public function updateAffiliateDiscountForFeeItem(Affiliate $affiliate, CardProgramFeeItem $feeItem = null)
    {
        if (!$feeItem) {
            return;
        }
        $discountRule = UserDiscountLoad::find($affiliate, $feeItem);
        if (!$discountRule) {
            return;
        }
        $expireDate = trim($discountRule->getExpireDate());
        if ($expireDate && strtotime($expireDate) <= time()) {
            return;
        }
        $userDiscount = new UserDiscount();
        $minLoad = $discountRule->getMinLoad();
        //Mark previous discount setting as inactive
        $em = Util::em();
        $discounts = $em->getRepository(\CoreBundle\Entity\UserDiscount::class)
            ->findBy(array('user' => $this, 'fee' => $feeItem));
        foreach ($discounts as $discount) {
            $em->remove($discount);
        }
        $userDiscount->setUser($this)
            ->setFee($feeItem)
            ->setPreviousFeeFixed($discountRule->getPreviousFeeFixed())
            ->setPreviousFeeRatio($discountRule->getPreviousFeeRatio())
            ->setDiscountFeeFixed($discountRule->getDiscountFeeFixed())
            ->setDiscountFeeRatio($discountRule->getDiscountFeeRatio())
            ->setExpireDate($expireDate)
            ->setMinLoad($minLoad);
        $em->persist($userDiscount);
        $em->flush();
    }

    public function updateLoadDates($loadStatus, $persist = true)
    {
        $time = time();
        $update = [];
        if ($loadStatus === UserCardLoad::LOAD_STATUS_INITIATED) {
            $update['last_load_date_initiated'] = $time;
            $tm = Util::json($this, 'meta', 'first_load_date_initiated');
            if (!$tm) {
                $update['first_load_date_initiated'] = $time;
            }
        } else if ($loadStatus === UserCardLoad::LOAD_STATUS_RECEIVED) {
            $update['last_load_date'] = $time;
            $tm = Util::json($this, 'meta', 'first_load_date');
            if (!$tm) {
                $update['first_load_date'] = $time;
            }
        }
        Util::updateJson($this, 'meta', $update, $persist);
    }

    public function getGenderAbbr()
    {
        $g = $this->getGender();
        if (!$g) {
            return null;
        }
        $g = strtolower($g);
        if ($g === 'male' || $g === 'man') {
            return 'M';
        }
        if ($g === 'female' || $g === 'woman') {
            return 'F';
        }
        return $this->getGender();
    }


    /**
     * Hook SoftDeleteable behavior
     * updates deletedAt field
     */
    use SoftDeleteableEntity;

    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    protected $id;


    /**
     * @var string
     *
     * @ORM\Column(name="gender", type="string", length=255, nullable=true)
     */
    protected $gender;

    /**
     * @var \DateTime $birthday
     *
     * @ORM\Column(name="birthday", type="datetime", nullable = true)
     * @Assert\Type("\DateTime")
     *
     */
    protected $birthday;

    /**
     * @var string
     *
     * @ORM\Column(name="locked_status", type="string", length=255, nullable=true, options={"comment":"Lock or Unlock"})
     */
    protected $locked_status;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="locked_at", type="datetime", nullable=true)
     */
    protected $lockedAt;

    /**
     * @var string
     *
     * @ORM\Column(name="first_name", type="string", length=255, nullable=true)
     */
    protected $firstName;

    /**
     * @var string
     *
     * @ORM\Column(name="last_name", type="string", length=255, nullable=true)
     */
    protected $lastName;

    /**
     * @Assert\Image(
     *     maxSize="3M",
     *     mimeTypes={"image/png", "image/jpeg", "image/pjpeg"},
     *     maxWidth=250,
     *     maxHeight=250
     * )
     * @var [type]
     * @Serializer\Exclude()
     */
    #[Vich\UploadableField(mapping: 'profile_image', fileNameProperty: 'profile_picture')]
    private $profile_picture_file;

    /**
     * @ORM\Column(name="profile_picture", type="string", nullable=true, options={"comment":"Path to profile picture"})
     * @var string
     */
    private $profilePicture;

    /**
     * @ORM\ManyToMany(targetEntity="CoreBundle\Entity\Role",inversedBy="users")
     * @ORM\JoinTable(name="user_role",
     *     joinColumns={@ORM\JoinColumn(name="user_id", referencedColumnName="id", onDelete="cascade")},
     *     inverseJoinColumns={@ORM\JoinColumn(name="role_id", referencedColumnName="id", unique=false)}
     *     )
     * @Serializer\Exclude()
     */
    private $teams;

    /**
     * @ORM\ManyToMany(targetEntity="CoreBundle\Entity\Tag", inversedBy="users")
     * @ORM\JoinTable(name="user_tag",
     *      joinColumns={@ORM\JoinColumn(name="user_id", referencedColumnName="id", onDelete="cascade")},
     *      inverseJoinColumns={@ORM\JoinColumn(name="tag_id", referencedColumnName="id", unique=false)}
     *     )
     * @Serializer\Exclude()
     */
    private $tags;

    /**
     * @var string
     *
     * @ORM\Column(name="flagsname", type="string", length=1024, nullable=true, options={"comment":"Flags like Discounted', 'Unvalidated address', etc. Separated by comma."})
     */
    protected $flagsname;

    /**
     * @var string
     *
     * @ORM\Column(name="address", type="string", length=255, nullable=true)
     */
    protected $address;

    /**
     * @var string
     *
     * @ORM\Column(name="addressline", type="string", length=255, nullable=true, options={"comment":"2nd line of detailed address"})
     */
    protected $addressline;

    /**
     * @var int
     *
     * @ORM\Column(name="countryid", type="integer", nullable=true, options={"comment":"Duplicated with country_id"})
     */
    protected $countryid;

    /**
     * @var int
     *
     * @ORM\Column(name="stateid", type="integer", nullable=true, options={"comment":"Duplicated with state_id"})
     */
    protected $stateid;

    /**
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\Country")
     */
    protected $country;

    /**
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\BaseState")
     */
    protected $state;

    /**
     * @var string
     *
     * @ORM\Column(name="city", type="string", length=255, nullable=true)
     */
    protected $city;

    /**
     * @var string
     *
     * @ORM\Column(name="phone", type="string", length=255, nullable=true, options={"comment":"Home phone"})
     */

    protected $phone;
    /**
     * @var string
     *
     * @ORM\Column(name="mobilephone", type="string", length=255, nullable=true)
     */

    protected $mobilephone;

    /**
     * @var string
     *
     * @ORM\Column(name="workphone", type="string", length=255, nullable=true)
     */
    protected $workphone;

    /**
     * @var string
     *
     * @ORM\Column(name="title", type="string", length=255, nullable=true)
     */
    protected $title;

    /**
     * @var string
     *
     * @ORM\Column(name="zip", type="string", length=255, nullable=true)
     */
    protected $zip;

    /**
     * @var string
     *
     * @ORM\Column(name="token", type="string", length=255, nullable=true, options={"comment":"Used to verify email"})
     */
    protected $token;

    /**
     * @var integer
     *
     * @ORM\Column(name="email_verified", type="integer", nullable=true, options={"default"=1})
     */
    protected $emailVerified;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=255, nullable=true)
     */
    protected $status;

    /**
     * @var string
     *
     * @ORM\Column(name="banned_reason", type="string", length=255, nullable=true)
     */
    protected $bannedReason;

    /**
     * @var string
     *
     * @ORM\Column(name="closure_reason", type="text", nullable=true)
     */
    protected $closureReason;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="token_exptime", type="datetime", nullable=true)
     */
    protected $token_exptime;

    /**
     * @var string
     *
     * @ORM\Column(name="register_step", type="string", length=255, nullable=true)
     */
    protected $register_step;

    /**
     * @var \Doctrine\Common\Collections\Collection
     *
     * @ORM\OneToMany(targetEntity="CoreBundle\Entity\UserBillingAddress", mappedBy="user")
     * @Serializer\Exclude()
     */
    protected $reshipperAddresses;

    /**
     * @var \Doctrine\Common\Collections\Collection
     *
     * @ORM\OneToMany(targetEntity="CoreBundle\Entity\UserIdVerify", mappedBy="user")
     * @ORM\OrderBy({"id" = "DESC"})
     * @Serializer\Exclude()
     */
    protected $idVerifies;

    /**
     * @var \Doctrine\Common\Collections\Collection
     *
     * @ORM\OneToMany(targetEntity="CoreBundle\Entity\UserCard", mappedBy="user")
     * @Serializer\Exclude()
     */
    protected $cards;

    /**
     * @var \Doctrine\Common\Collections\Collection
     *
     * @ORM\OneToMany(targetEntity="CoreBundle\Entity\UserPin", mappedBy="user")
     * @Serializer\Exclude()
     */
    protected $pins;

    /**
     * @var \Doctrine\Common\Collections\Collection
     *
     * @ORM\OneToMany(targetEntity="CoreBundle\Entity\UserPinLog", mappedBy="user")
     * @Serializer\Exclude()
     */
    protected $pinLogs;

    /**
     * @var \Doctrine\Common\Collections\Collection
     *
     * @ORM\OneToMany(targetEntity="CoreBundle\Entity\Error", mappedBy="user")
     * @Serializer\Exclude()
     */
    protected $errors;

    /**
     * @var \Doctrine\Common\Collections\Collection
     *
     * @ORM\OneToMany(targetEntity="CoreBundle\Entity\UserExpenseCategory", mappedBy="user")
     * @Serializer\Exclude()
     */
    protected $expenseCategories;

    /**
     * @var boolean
     *
     * @ORM\Column(name="knownAddress", type="boolean", nullable=true)
     */
    protected $knownAddress;

    /**
     * @var Affiliate $affiliate
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\Affiliate", inversedBy="users")
     * @Serializer\Exclude()
     */
    private $affiliate;

    //require limits and attempt lock based on User
    /**
     * @var integer $phoneFail
     * @ORM\Column(name="pin_fail", type="integer", nullable=true, options={"comment":"Times when failed to validate pin. Max: 5"})
     */
    private $pinFail;

    /**
     * @var integer $phoneSms
     * @ORM\Column(name="phone_sms", type="integer", nullable=true, options={"comment":"Times when pin SMS was sent to phone"})
     */
    private $phoneSms;

    /**
     * @var integer $phoneVoice
     * @ORM\Column(name="phone_voice", type="integer", nullable=true)
     */
    private $phoneVoice;

    /**
     * @var integer $mobilePhoneSms
     * @ORM\Column(name="mobile_phone_sms", type="integer", nullable=true)
     */
    private $mobilePhoneSms;

    /**
     * @var integer $mobilePhoneVoice
     * @ORM\Column(name="mobile_phone_voice", type="integer", nullable=true)
     */
    private $mobilePhoneVoice;

    /**
     * @var integer $workPhoneSms
     * @ORM\Column(name="work_phone_sms", type="integer", nullable=true)
     */
    private $workPhoneSms;

    /**
     * @var integer $workPhoneVoice
     * @ORM\Column(name="work_phone_voice", type="integer", nullable=true)
     */
    private $workPhoneVoice;

    /**
     * @var integer $idVerifyLeftAttempts
     * @ORM\Column(name="id_verify_left_attempts", type="integer", nullable=true)
     */
    private $idVerifyLeftAttempts;

    /**
     * @var string $timezone
     * @ORM\Column(name="timezone", type="string", length=255, nullable=true)
     */
    private $timezone;

    /**
     * @var \DateTime
     * @ORM\Column(name="first_login", type="datetime", nullable=true)
     */
    private $firstLogin;

    /**
     * @var integer
     * @ORM\Column(name="login_count", type="integer", nullable=true, options={"default"=0})
     */
    private $loginCount;

    /**
     * @var string
     * @ORM\Column(name="last_request", type="text", nullable=true, options={"comment":"Last request URL and time info to avoid multiple same requests"})
     */
    private $lastRequest;

    /**
     * @var string
     * @ORM\Column(name="api_token", type="string", length=1024, nullable=true, options={"comment":"For API invoker(developer or 3rd party service) to call APIs."})
     */
    private $apiToken;

    /**
     * @var Collection
     *
     * @ORM\OneToMany(targetEntity="CoreBundle\Entity\UserToken", mappedBy="user")
     * @Serializer\Exclude()
     */
    private $tokens;

    /**
     * @var \Doctrine\Common\Collections\Collection
     *
     * @ORM\ManyToMany(targetEntity="CoreBundle\Entity\Affiliate")
     * @Serializer\Exclude()
     */
    private $accessibleAffiliates;

    /**
     * @var \Doctrine\Common\Collections\Collection
     *
     * @ORM\ManyToMany(targetEntity="CoreBundle\Entity\Platform")
     * @Serializer\Exclude()
     */
    private $accessiblePlatforms;

    /**
     * @var \Doctrine\Common\Collections\Collection
     *
     * @ORM\ManyToMany(targetEntity="CoreBundle\Entity\CardProgram")
     * @Serializer\Exclude()
     */
    private $accessibleCardPrograms;

    /**
     * @var string
     * @ORM\Column(name="accessible_api", type="text", nullable=true, options={"comment":"All APIs that the invoker can call."})
     */
    private $accessibleAPI;

    /**
     * Self registration, created by API, or added by admin
     *
     * self: Self registration
     * api: Created by API
     * admin: Added by Admin
     *
     * @var string
     * @ORM\Column(name="source", type="string", nullable=true, options={"comment":"enum: self, api, admin. Self registration, created by API, or added by admin"})
     */
    private $source;

    /**
     * web, desktop, or mobile
     * @var string
     * @ORM\Column(name="registration_client", type="string", nullable=true, options={"default"="web", "comment":"web, desktop, or mobile"})
     */
    private $registrationClient;

    /**
     * @var string
     * @ORM\Column(name="registration_ip", type="string", nullable=true)
     */
    private $registrationIp;

    /**
     * @var Country
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\Country")
     */
    private $registrationCountry;

    /**
     * @var string
     * @ORM\Column(name="registration_os", type="string", nullable=true)
     */
    private $registrationOS;

    /**
     * @var boolean
     * @ORM\Column(name="vip", type="boolean", nullable=true, options={"default"=false})
     */
    private $vip;

    /**
     * @var Collection
     * @ORM\OneToMany(targetEntity="CoreBundle\Entity\UserVelocity", mappedBy="user")
     * @Serializer\Exclude()
     */
    private $velocities;

    /**
     * @var Collection
     * @ORM\OneToMany(targetEntity="CoreBundle\Entity\UserPromotion", mappedBy="user")
     * @Serializer\Exclude()
     */
    private $promotions;

    /**
     * @var Collection
     * @ORM\OneToMany(targetEntity="CoreBundle\Entity\TenantUser", mappedBy="user")
     */
    private $tenantUsers;

    /**
     * @var Collection
     * @ORM\OneToMany(targetEntity="CoreBundle\Entity\Affiliate", mappedBy="admin")
     */
    private $managingAffiliates;

    /**
     * @var \Doctrine\Common\Collections\Collection
     *
     * @ORM\ManyToMany(targetEntity="CoreBundle\Entity\UserGroup", inversedBy="users")
     * @ORM\JoinTable(name="user_group_users",
     *      joinColumns={@ORM\JoinColumn(name="user_id", referencedColumnName="id", unique=false)},
     *      inverseJoinColumns={@ORM\JoinColumn(name="group_id", referencedColumnName="id", unique=false)}
     * )
     * @Serializer\Exclude()
     */
    private $userGroups;

    /**
     * @var string
     * @ORM\Column(name="meta", type="text", nullable=true)
     */
    private $meta;

    /**
     * @var string
     *
     * @ORM\Column(name="ip_whitelist", type="text", nullable=true)
     */
    private $ipWhitelist;

    /**
     * @ORM\OneToOne(targetEntity="SalexUserBundle\Entity\UserConfig", mappedBy="user", cascade={"persist", "remove"})
     */
    private $config;

    /**
     * @var array
     *
     * @ORM\Column(name="notifications", type="array", nullable=true)
     */
    private $notifications;

    /**
     * @var array
     *
     * @ORM\Column(name="viewed_notifications", type="array", nullable=true)
     */
    private $viewedNotifications;

    /**
     * @var boolean
     *
     * @ORM\Column(name="watch", type="boolean", nullable=true)
     */
    protected $watch;

    /**
     * @var \Doctrine\Common\Collections\Collection
     *
     * @ORM\OneToMany(targetEntity="ClfBundle\Entity\Merchant", mappedBy="adminUser")
     * @Serializer\Exclude()
     */
    protected $merchants;

    /**
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\LoadLocator")
     */
    protected $LoadLocator;

    /**
     * @ORM\ManyToMany(targetEntity="SpendrBundle\Entity\Location",inversedBy="admins")
     * @ORM\JoinTable(name="spendr_locations_admins",
     *     joinColumns={@ORM\JoinColumn(name="admin_id", referencedColumnName="id", onDelete="cascade")},
     *     inverseJoinColumns={@ORM\JoinColumn(name="location_id", referencedColumnName="id", unique=false)}
     *     )
     * @Serializer\Exclude()
     */
    private $locations;

    /**
     * @var \Doctrine\Common\Collections\Collection
     *
     * @ORM\OneToMany(targetEntity="UsUnlockedBundle\Entity\PayPalSubscription", mappedBy="user")
     * @ORM\OrderBy({"id" = "DESC"})
     * @Serializer\Exclude()
     */
    protected $payPalSubscriptions;

    public function __construct()
    {
        parent::__construct();
        $this->teams = new ArrayCollection();
        $this->tags = new ArrayCollection();
        $this->cards = new ArrayCollection();
        $this->idVerifies = new ArrayCollection();
        $this->reshipperAddresses = new ArrayCollection();
        $this->accessiblePlatforms = new ArrayCollection();
        $this->accessibleCardPrograms = new ArrayCollection();
        $this->accessibleAffiliates = new ArrayCollection();
        $this->userGroups = new ArrayCollection();
        $this->tokens = new ArrayCollection();
        $this->merchants = new ArrayCollection();
        $this->registrationClient = self::CLIENT_WEB;
        $this->vip = false;
        $this->locations = new ArrayCollection();
        $this->payPalSubscriptions = new ArrayCollection();
    }

    public function setIdForDebug ($id) {
        $this->id = $id;
    }

    /**
     * Set firstName
     *
     * @param string $firstName
     *
     * @return User
     */
    public function setFirstName($firstName)
    {
        $this->firstName = $firstName;

        return $this;
    }

    /**
     * Get firstName
     *
     * @return string
     */
    public function getFirstName()
    {
        return $this->firstName;
    }

    /**
     * Set lastName
     *
     * @param string $lastName
     *
     * @return User
     */
    public function setLastName($lastName)
    {
        $this->lastName = $lastName;

        return $this;
    }

    /**
     * Get lastName
     *
     * @return string
     */
    public function getLastName()
    {
        return $this->lastName;
    }

    /**
     * Get full name of the user
     *
     * @return string
     */
    public function getName()
    {
        try {
            $name = $this->getFirstName() . ' ' . $this->getLastName();
        } catch (EntityNotFoundException $e) {
            Log::error($e->getMessage(), $e->getTrace());
            $name = '';
        }
        return $name;
    }

    public function addNoteByCurrentUser(string $text, bool $persist = true)
    {
        $user = Util::user();
        return $this->addNote($text, $persist, $user?->getId(), $user);
    }

    public function addNote($string, $persist = true, $from = 500000001, User $createdBy = null)
    {
        if (!$this->getId()) {
            return null;
        }

        $note = new Notes();
        $note->setFromname($from);
        $note->setToname($this->getId());
        $note->setNotes($string)
            ->setCreatedBy($createdBy);

        Service::log('Added note to user', [
            'user' => $this->getId(),
            'note' => $string,
        ]);

        if ($persist) {
            Util::persist($note);
        } else {
            Util::em()->persist($note);
        }

        return $note;
    }

    /**
     * Sets the value of id.
     *
     * @param int $id the id
     *
     * @return self
     */
    protected function setId($id)
    {
        $this->id = $id;

        return $this;
    }

    /**
     * Gets the value of profile_picture_file.
     *
     * @return File|null
     */
    public function getProfilePictureFile()
    {
        return $this->profile_picture_file;
    }

    /**
     * Sets the value of profile_picture_file.
     *
     * @param File $profile_picture_file
     *
     * @return self
     */
    public function setProfilePictureFile(File $profile_picture_file = null)
    {
        $this->profile_picture_file = $profile_picture_file;

        if (null !== $profile_picture_file) {
            $this->setUpdatedAt(new \DateTime());
        }

        return $this;
    }

    /**
     * Gets the value of profilePicture.
     *
     * @return string
     */
    public function getProfilePicture()
    {
        return $this->profilePicture;
    }

    /**
     * Sets the value of profilePicture.
     *
     * @param string $profilePicture the profile picture
     *
     * @return self
     */
    public function setProfilePicture($profilePicture)
    {
        $this->profilePicture = $profilePicture;

        return $this;
    }

    /* Following functions are implemented for ThemeUser Interface  */

    public function getAvatar()
    {
        return $this->getProfilePicture();
    }

    public function getAvatarUrl()
    {
        $a = $this->getAvatar();
        if (!$a) {
            return null;
        }
        if (Util::startsWith($a, 'http://') || Util::startsWith($a, 'https://')) {
            return $a;
        }
        $s3Key = self::AVATAR_S3_PREFIX . $a;
        try {
            $platform = Util::platform();
            if ($platform && $platform->isSpendr()) {
                $url = S3Storage::getBackupPreSignedUrl($s3Key, '+3 days');
                if ($url) return $url;
            }
        } catch (\Exception $e) {
            Log::warn('Failed to get ' . $a . ' s3 url ' . $e->getMessage());
        }
        return Util::host() . '/files/profile_image/' . $a;
    }

    /**
     * upload avatar to S3
     * @param $body
     * @param $contentType
     * @return bool
     */
    public function uploadAvatarToS3($body, $contentType)
    {
        $uploaded = S3Storage::putBackupObject(
            self::AVATAR_S3_PREFIX . $this->getProfilePicture(),
            $body,
            $contentType
        );
        if ((int)$uploaded !== 200) {
            Log::warn('Spendr avatar upload failed to S3: ' . $this->getId());
            return false;
        }
        if (Util::isStaging()) {
            Log::debug('Spendr avatar upload success', [
                self::AVATAR_S3_PREFIX . $this->getProfilePicture(),
            ]);
        }
        return true;
    }

    public function getMemberSince()
    {
        return $this->createdAt;
    }

    public function isOnline()
    {
        return true;
    }

    public function getIdentifier()
    {
        return $this->getId();
    }

    public function setEmail($email)
    {
        if ($this->getId() && $this->getEmail()) {
            $user = Util::user();
            $this->addNote('Changed the email address to ' . $email, false, $user ? $user->getId() : 500000001, $user);
        }
        return parent::setEmail($email);
    }

     /**
     * Remove tag
     *
     * @param \CoreBundle\Entity\Tag $tag
     */
    public function removeTag(\CoreBundle\Entity\Tag $tag)
    {
        $this->tags->removeElement($tag);
    }


    /**
     * Get tags
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getTags()
    {
        return $this->tags;
    }


    /**
     * Add team
     *
     * @param mixed $tags
     *
     * @return User
     */
    public function setTags($tags)
    {
        $this->tags = $tags;

        return $this;
    }

     /**
     * Add team
     *
     * @param \CoreBundle\Entity\Tag $team
     *
     * @return User
     */
    public function addTag(\CoreBundle\Entity\Tag $tag)
    {
        $this->tags[] = $tag;

        return $this;
    }

    /**
     * Add team
     *
     * @param mixed $teams
     *
     * @return User
     */
    public function setTeams(  $teams)
    {
        $this->teams = $teams;

        return $this;
    }

    /**
     * Remove team
     *
     * @param \CoreBundle\Entity\Role $team
     */
    public function removeTeam(\CoreBundle\Entity\Role $team)
    {
        $this->teams->removeElement($team);
    }

    /**
     * Get teams
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getTeams()
    {
        return $this->teams;
    }

    /**
     * Add team
     *
     * @param \CoreBundle\Entity\Role $team
     *
     * @return User
     */
    public function addTeam(\CoreBundle\Entity\Role $team)
    {
        $this->teams[] = $team;

        return $this;
    }

    /**
     * Set flagsname
     *
     * @param string $flagsname
     *
     * @return User
     */
    public function setFlagsname($flagsname)
    {
        $this->flagsname = $flagsname;

        return $this;
    }

    /**
     * Get flagsname
     *
     * @return array
     */
    public function getFlagsname()
    {
        $all = array_merge(
            explode(';', $this->flagsname),
            explode(',', $this->flagsname),
        );
        $result = [];
        foreach ($all as $item) {
            if ($item && mb_strpos($item, ';') === false && mb_strpos($item, ',') === false) {
                $result[] = $item;
            }
        }
        return Util::unique($result);
    }

    /**
     * Set address
     *
     * @param string $address
     *
     * @return User
     */
    public function setAddress($address)
    {
        $this->address = $address;

        return $this;
    }

    /**
     * Get address
     *
     * @return string
     */
    public function getAddress()
    {
        return $this->address;
    }

    /**
     * Set addressline
     *
     * @param string $addressline
     *
     * @return User
     */
    public function setAddressline($addressline)
    {
        $this->addressline = $addressline;

        return $this;
    }

    /**
     * Get addressline
     *
     * @return string
     */
    public function getAddressline()
    {
        return $this->addressline;
    }

    /**
     * @deprecated Use `setCountry` instead
     * Set countryid
     *
     * @param integer $countryid
     *
     * @return User
     */
    public function setCountryid($countryid)
    {
        $this->countryid = $countryid;

        if ($countryid) {
            $country = Util::em()->getRepository(\CoreBundle\Entity\Country::class)->find($countryid);
            $this->setCountry($country);
        }

        return $this;
    }

    /**
     * @deprecated Use `getCountry` instead
     * Get countryid
     *
     * @return integer
     */
    public function getCountryid()
    {
        return $this->countryid;
    }

    /**
     * @deprecated Use `setState` instead
     * Set stateid
     *
     * @param integer $stateid
     *
     * @return User
     */
    public function setStateid($stateid)
    {
        $this->stateid = $stateid;

        if ($stateid) {
            $state = Util::em()->getRepository(BaseState::class)->find($stateid);
            $this->setState($state);
        } else {
            $this->setState(null);
        }

        return $this;
    }

    /**
     * @deprecated Use `getState` instead
     * Get stateid
     *
     * @return integer
     */
    public function getStateid()
    {
        return $this->stateid;
    }

    /**
     * Set phone
     *
     * @param string $phone
     *
     * @return User
     */
    public function setPhone($phone)
    {
        $this->phone = $phone;

        return $this;
    }

    /**
     * Get phone
     *
     * @return string
     */
    public function getPhone()
    {
        return $this->phone;
    }

    /**
     * Set mobilephone
     *
     * @param string $mobilephone
     *
     * @return User
     */
    public function setMobilephone($mobilephone)
    {
        $this->mobilephone = $mobilephone;

        return $this;
    }

    /**
     * Get mobilephone
     *
     * @return string
     */
    public function getMobilephone()
    {
        return $this->mobilephone;
    }

    /**
     * Set workphone
     *
     * @param string $workphone
     *
     * @return User
     */
    public function setWorkphone($workphone)
    {
        $this->workphone = $workphone;

        return $this;
    }

    /**
     * Get workphone
     *
     * @return string
     */
    public function getWorkphone()
    {
        return $this->workphone;
    }

    /**
     * Set zip
     *
     * @param string $zip
     *
     * @return User
     */
    public function setZip($zip)
    {
        $this->zip = $zip;

        return $this;
    }

    /**
     * Get zip
     *
     * @return string
     */
    public function getZip()
    {
        return $this->zip;
    }

    /**
     * Set token
     *
     * @param string $token
     *
     * @return User
     */
    public function setToken($token)
    {
        $this->token = $token;

        return $this;
    }

    /**
     * Get token
     *
     * @return string
     */
    public function getToken()
    {
        return $this->token;
    }


    /**
     * Set tokenExptime
     *
     * @param \DateTime $tokenExptime
     *
     * @return User
     */
    public function setTokenExptime($tokenExptime)
    {
        $this->token_exptime = $tokenExptime;

        return $this;
    }

    /**
     * Get tokenExptime
     *
     * @return \DateTime
     */
    public function getTokenExptime()
    {
        return $this->token_exptime;
    }

    /**
     * Set registerStep
     *
     * @param string $registerStep
     *
     * @return User
     */
    public function setRegisterStep($registerStep)
    {
        $this->register_step = $registerStep;

        return $this;
    }

    /**
     * Get registerStep
     *
     * @return string
     */
    public function getRegisterStep()
    {
        return $this->register_step;
    }

    /**
     * Set gender
     *
     * @param string $gender
     *
     * @return User
     */
    public function setGender($gender)
    {
        $this->gender = ucfirst($gender);

        return $this;
    }

    /**
     * Get gender
     *
     * @return string
     */
    public function getGender()
    {
        return $this->gender;
    }

    /**
     * Set birthday
     *
     * @param \DateTime $birthday
     *
     * @return User
     */
    public function setBirthday($birthday)
    {
        $this->birthday = $birthday;

        return $this;
    }

    /**
     * Get birthday
     *
     * @return \DateTime
     */
    public function getBirthday()
    {
        return $this->birthday;
    }

    /**
     * Set lockedStatus
     *
     * @param string $lockedStatus
     *
     * @return User
     */
    public function setLockedStatus($lockedStatus)
    {
        $this->locked_status = $lockedStatus;

        if ($lockedStatus === self::LOCK_STATUS_LOCK) {
            $this->setLockedAt(new \DateTime());
        } else {
            $this->setLockedAt(null);
            LoginAttempt::create($this, true, 'unlock');
        }

        return $this;
    }

    /**
     * Get lockedStatus
     *
     * @return string
     */
    public function getLockedStatus()
    {
        return $this->locked_status ?: self::LOCK_STATUS_UNLOCK;
    }

    /**
     * Get status
     *
     * @return string
     */
    public function getStatus()
    {
        return $this->status ?: self::STATUS_ACTIVE;
    }

    /**
     * Add card
     *
     * @param \CoreBundle\Entity\UserCard $card
     *
     * @return User
     */
    public function addCard(\CoreBundle\Entity\UserCard $card)
    {
        $this->cards[] = $card;

        return $this;
    }

    /**
     * Remove card
     *
     * @param \CoreBundle\Entity\UserCard $card
     */
    public function removeCard(\CoreBundle\Entity\UserCard $card)
    {
        $this->cards->removeElement($card);
    }

    public function getCardIds()
    {
        $ids = [];
        /** @var UserCard $uc */
        foreach ($this->getCards() as $uc) {
            $ids[] = $uc->getId();
        }
        return $ids;
    }

    /**
     * Add idVerify
     *
     * @param \CoreBundle\Entity\UserIdVerify $idVerify
     *
     * @return User
     */
    public function addIdVerify(\CoreBundle\Entity\UserIdVerify $idVerify)
    {
        $this->idVerifies[] = $idVerify;

        return $this;
    }

    /**
     * Remove idVerify
     *
     * @param \CoreBundle\Entity\UserIdVerify $idVerify
     */
    public function removeIdVerify(\CoreBundle\Entity\UserIdVerify $idVerify)
    {
        $this->idVerifies->removeElement($idVerify);
    }

    /**
     * Set bannedReason
     *
     * @param string $bannedReason
     *
     * @return User
     */
    public function setBannedReason($bannedReason)
    {
        $this->bannedReason = $bannedReason;

        return $this;
    }

    /**
     * Get bannedReason
     *
     * @return string
     */
    public function getBannedReason()
    {
        return $this->bannedReason;
    }

    /**
     * Set KnownAddress
     *
     * @param boolean $knownAddress
     *
     * @return User
     */
    public function setKnownAddress($knownAddress)
    {
        $this->knownAddress = $knownAddress;

        return $this;
    }

    /**
     * Get bannedReason
     *
     * @return boolean
     */
    public function getKnownAddress()
    {
        return $this->knownAddress;
    }

    /**
     * Set pinFail
     *
     * @param integer $pinFail
     *
     * @return User
     */
    public function setPinFail($pinFail)
    {
        $this->pinFail = $pinFail;

        return $this;
    }

    /**
     * Get pinFail
     *
     * @return integer
     */
    public function getPinFail()
    {
        return $this->pinFail;
    }

    /**
     * Set phoneSms
     *
     * @param integer $phoneSms
     *
     * @return User
     */
    public function setPhoneSms($phoneSms)
    {
        $this->phoneSms = $phoneSms;

        return $this;
    }

    /**
     * Get phoneSms
     *
     * @return integer
     */
    public function getPhoneSms()
    {
        return $this->phoneSms;
    }

    /**
     * Set phoneVoice
     *
     * @param integer $phoneVoice
     *
     * @return User
     */
    public function setPhoneVoice($phoneVoice)
    {
        $this->phoneVoice = $phoneVoice;

        return $this;
    }

    /**
     * Get phoneVoice
     *
     * @return integer
     */
    public function getPhoneVoice()
    {
        return $this->phoneVoice;
    }

    /**
     * Set mobilePhoneSms
     *
     * @param integer $mobilePhoneSms
     *
     * @return User
     */
    public function setMobilePhoneSms($mobilePhoneSms)
    {
        $this->mobilePhoneSms = $mobilePhoneSms;

        return $this;
    }

    /**
     * Get mobilePhoneSms
     *
     * @return integer
     */
    public function getMobilePhoneSms()
    {
        return $this->mobilePhoneSms;
    }

    /**
     * Set mobilePhoneVoice
     *
     * @param integer $mobilePhoneVoice
     *
     * @return User
     */
    public function setMobilePhoneVoice($mobilePhoneVoice)
    {
        $this->mobilePhoneVoice = $mobilePhoneVoice;

        return $this;
    }

    /**
     * Get mobilePhoneVoice
     *
     * @return integer
     */
    public function getMobilePhoneVoice()
    {
        return $this->mobilePhoneVoice;
    }

    /**
     * Set workPhoneSms
     *
     * @param integer $workPhoneSms
     *
     * @return User
     */
    public function setWorkPhoneSms($workPhoneSms)
    {
        $this->workPhoneSms = $workPhoneSms;

        return $this;
    }

    /**
     * Get workPhoneSms
     *
     * @return integer
     */
    public function getWorkPhoneSms()
    {
        return $this->workPhoneSms;
    }

    /**
     * Set workPhoneVoice
     *
     * @param integer $workPhoneVoice
     *
     * @return User
     */
    public function setWorkPhoneVoice($workPhoneVoice)
    {
        $this->workPhoneVoice = $workPhoneVoice;

        return $this;
    }

    /**
     * Get workPhoneVoice
     *
     * @return integer
     */
    public function getWorkPhoneVoice()
    {
        return $this->workPhoneVoice;
    }

    /**
     * Set city
     *
     * @param string $city
     *
     * @return User
     */
    public function setCity($city)
    {
        $this->city = $city;

        return $this;
    }

    /**
     * Get city
     *
     * @return string
     */
    public function getCity()
    {
        return $this->city;
    }

    /**
     * Set meta
     *
     * @param string $meta
     *
     * @return User
     */
    public function setMeta($meta)
    {
        $this->meta = $meta;

        return $this;
    }

    /**
     * Get meta
     *
     * @return string
     */
    public function getMeta()
    {
        return $this->meta;
    }

    /**
     * Set idVerifyAttempts
     *
     * @param integer $idVerifyLeftAttempts
     *
     * @return User
     */
    public function setIdVerifyLeftAttempts($idVerifyLeftAttempts)
    {
        $this->idVerifyLeftAttempts = $idVerifyLeftAttempts;

        return $this;
    }

    /**
     * Get idVerifyLeftAttempts
     *
     * @return integer
     */
    public function getIdVerifyLeftAttempts()
    {
        $left = $this->idVerifyLeftAttempts;
        if (null === $left) {
            $left = (int)Util::em()->getRepository(\CoreBundle\Entity\Config::class)
                ->getValue(Config::CONFIG_DEFAULT_ID_VERIFY_ATTEMPT_TIMES,
                    UserIdVerify::DEFAULT_ATTEMPT_TIMES);
        }
        return $left;
    }

    /**
     * Set emailVerified
     *
     * @param integer $emailVerified
     *
     * @return User
     */
    public function setEmailVerified($emailVerified)
    {
        $this->emailVerified = $emailVerified;

        return $this;
    }

    /**
     * Get emailVerified
     *
     * @return integer
     */
    public function getEmailVerified()
    {
        return $this->emailVerified;
    }

    /**
     * Set timezone
     *
     * @param string $timezone
     *
     * @return User
     */
    public function setTimezone($timezone)
    {
        $this->timezone = $timezone;

        return $this;
    }

    /**
     * Get timezone
     *
     * @param bool $default
     * @return string
     */
    public function getTimezone($default = false)
    {
        $tz = $this->timezone;
        if (null === $tz && $default) {
            return $default;
        }
        return $tz;
    }

    /**
     * @return Country
     */
    public function getCountry()
    {
        return $this->country;
    }

    /**
     * Set country
     *
     * @param \CoreBundle\Entity\Country $country
     *
     * @return User
     */
    public function setCountry(\CoreBundle\Entity\Country $country = null)
    {
        $this->country = $country;

        if ($country) {
            $this->countryid = $country->getId();
        }

        return $this;
    }

    /**
     * @return BaseState
     */
    public function getState()
    {
        return $this->state;
    }

    /**
     * Set state
     *
     * @param \CoreBundle\Entity\BaseState $state
     *
     * @return User
     */
    public function setState(\CoreBundle\Entity\BaseState $state = null)
    {
        $this->state = $state;

        if ($state) {
            $this->stateid = $state->getId();
        }

        return $this;
    }

    public function setStateIfExist(BaseState $state = null)
    {
        if (!$state || !$state->getId()) {
            return $this;
        }
        return $this->setState($state);
    }

    /**
     * Set firstLogin
     *
     * @param \DateTime $firstLogin
     *
     * @return User
     */
    public function setFirstLogin($firstLogin)
    {
        $this->firstLogin = $firstLogin;

        return $this;
    }

    /**
     * Get firstLogin
     *
     * @return \DateTime
     */
    public function getFirstLogin()
    {
        return $this->firstLogin;
    }

    /**
     * Set loginCount
     *
     * @param integer $loginCount
     *
     * @return User
     */
    public function setLoginCount($loginCount)
    {
        $this->loginCount = $loginCount;

        return $this;
    }

    public function getLoginCount()
    {
        return $this->loginCount;
    }

    /**
     * Set lastRequest
     *
     * @param string $lastRequest
     *
     * @return User
     */
    public function setLastRequest($lastRequest)
    {
        // $this->lastRequest = $lastRequest;
        Data::set('last_request_' . $this->id, $lastRequest);
        return $this;
    }

    /**
     * Get lastRequest
     *
     * @return string
     */
    public function getLastRequest()
    {
        $lastRequest = Data::get('last_request_' . $this->id);
        return $lastRequest;
    }

    /**
     * Add error
     *
     * @param \CoreBundle\Entity\Error $error
     *
     * @return User
     */
    public function addError(\CoreBundle\Entity\Error $error)
    {
        $this->errors[] = $error;

        return $this;
    }

    /**
     * Remove error
     *
     * @param \CoreBundle\Entity\Error $error
     */
    public function removeError(\CoreBundle\Entity\Error $error)
    {
        $this->errors->removeElement($error);
    }

    /**
     * Get errors
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getErrors()
    {
        return $this->errors;
    }

    /**
     * Get affiliate
     *
     * @return \CoreBundle\Entity\Affiliate
     */
    public function getAffiliate()
    {
        return $this->affiliate;
    }

    /**
     * Add pin
     *
     * @param \CoreBundle\Entity\UserPin $pin
     *
     * @return User
     */
    public function addPin(\CoreBundle\Entity\UserPin $pin)
    {
        $this->pins[] = $pin;

        return $this;
    }

    /**
     * Remove pin
     *
     * @param \CoreBundle\Entity\UserPin $pin
     */
    public function removePin(\CoreBundle\Entity\UserPin $pin)
    {
        $this->pins->removeElement($pin);
    }

    /**
     * Get pins
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getPins()
    {
        return $this->pins;
    }

    /**
     * Add pinLog
     *
     * @param \CoreBundle\Entity\UserPinLog $pinLog
     *
     * @return User
     */
    public function addPinLog(\CoreBundle\Entity\UserPinLog $pinLog)
    {
        $this->pinLogs[] = $pinLog;

        return $this;
    }

    /**
     * Remove pinLog
     *
     * @param \CoreBundle\Entity\UserPinLog $pinLog
     */
    public function removePinLog(\CoreBundle\Entity\UserPinLog $pinLog)
    {
        $this->pinLogs->removeElement($pinLog);
    }

    /**
     * Get pinLogs
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getPinLogs()
    {
        return $this->pinLogs;
    }

    /**
     * Set apiToken
     *
     * @param string $apiToken
     *
     * @return User
     */
    public function setApiToken($apiToken)
    {
        $this->apiToken = $apiToken;

        return $this;
    }

    /**
     * Get apiToken
     *
     * @return string
     */
    public function getApiToken()
    {
        return $this->apiToken;
    }

    /**
     * Add accessibleAffiliate
     *
     * @param \CoreBundle\Entity\Affiliate $accessibleAffiliate
     *
     * @return User
     */
    public function addAccessibleAffiliate(\CoreBundle\Entity\Affiliate $accessibleAffiliate)
    {
        $this->accessibleAffiliates[] = $accessibleAffiliate;

        return $this;
    }

    /**
     * Remove accessibleAffiliate
     *
     * @param \CoreBundle\Entity\Affiliate $accessibleAffiliate
     */
    public function removeAccessibleAffiliate(\CoreBundle\Entity\Affiliate $accessibleAffiliate)
    {
        $this->accessibleAffiliates->removeElement($accessibleAffiliate);
    }

    /**
     * Set registrationClient
     *
     * @param string $registrationClient
     *
     * @return User
     */
    public function setRegistrationClient($registrationClient)
    {
        $this->registrationClient = $registrationClient;

        return $this;
    }

    /**
     * Get registrationClient
     *
     * @return string
     */
    public function getRegistrationClient()
    {
        return $this->registrationClient;
    }

    /**
     * Set source
     *
     * @param string $source
     *
     * @return User
     */
    public function setSource($source)
    {
        $this->source = $source;

        return $this;
    }

    /**
     * Get source
     *
     * @return string
     */
    public function getSource()
    {
        return $this->source;
    }

    /**
     * Set registrationIp
     *
     * @param string $registrationIp
     *
     * @return User
     */
    public function setRegistrationIp($registrationIp)
    {
        $this->registrationIp = $registrationIp;

        return $this;
    }

    /**
     * Get registrationIp
     *
     * @return string
     */
    public function getRegistrationIp()
    {
        return $this->registrationIp;
    }

    /**
     * Set registrationOS
     *
     * @param string $registrationOS
     *
     * @return User
     */
    public function setRegistrationOS($registrationOS)
    {
        $this->registrationOS = $registrationOS;

        return $this;
    }

    /**
     * Get registrationOS
     *
     * @return string
     */
    public function getRegistrationOS()
    {
        return $this->registrationOS;
    }

    /**
     * Set registrationCountry
     *
     * @param \CoreBundle\Entity\Country $registrationCountry
     *
     * @return User
     */
    public function setRegistrationCountry(\CoreBundle\Entity\Country $registrationCountry = null)
    {
        $this->registrationCountry = $registrationCountry;

        return $this;
    }

    /**
     * Get registrationCountry
     *
     * @return \CoreBundle\Entity\Country
     */
    public function getRegistrationCountry()
    {
        return $this->registrationCountry;
    }

    /**
     * Set vip
     *
     * @param boolean $vip
     *
     * @return User
     */
    public function setVip($vip)
    {
        $this->vip = $vip;

        return $this;
    }

    /**
     * Get vip
     *
     * @return boolean
     */
    public function getVip()
    {
        return $this->vip;
    }

    /**
     * Set accessibleAPI
     *
     * @param string $accessibleAPI
     *
     * @return User
     */
    public function setAccessibleAPI($accessibleAPI)
    {
        $this->accessibleAPI = $accessibleAPI;

        return $this;
    }

    /**
     * Get accessibleAPI
     *
     * @return string
     */
    public function getAccessibleAPI()
    {
        return $this->accessibleAPI;
    }

    /**
     * Add accessibleCardProgram
     *
     * @param \CoreBundle\Entity\CardProgram $accessibleCardProgram
     *
     * @return User
     */
    public function addAccessibleCardProgram(\CoreBundle\Entity\CardProgram $accessibleCardProgram)
    {
        $this->accessibleCardPrograms[] = $accessibleCardProgram;

        return $this;
    }

    /**
     * Remove accessibleCardProgram
     *
     * @param \CoreBundle\Entity\CardProgram $accessibleCardProgram
     */
    public function removeAccessibleCardProgram(\CoreBundle\Entity\CardProgram $accessibleCardProgram)
    {
        $this->accessibleCardPrograms->removeElement($accessibleCardProgram);
    }

    /**
     * Set closureReason
     *
     * @param string $closureReason
     *
     * @return User
     */
    public function setClosureReason($closureReason)
    {
        $this->closureReason = $closureReason;

        return $this;
    }

    /**
     * Get closureReason
     *
     * @return string
     */
    public function getClosureReason()
    {
        return $this->closureReason;
    }

    public function getTitle()
    {
        return $this->title;
    }

    /**
     * Set title
     *
     * @param string $title
     *
     * @return User
     */
    public function setTitle($title)
    {
        $this->title = $title;

        return $this;
    }

    /**
     * Set ipWhitelist
     *
     * @param string $ipWhitelist
     *
     * @return User
     */
    public function setIpWhitelist($ipWhitelist)
    {
        $this->ipWhitelist = $ipWhitelist;

        return $this;
    }

    /**
     * Get ipWhitelist
     *
     * @return string
     */
    public function getIpWhitelist()
    {
        //$list = json_decode($this->ipWhitelist, true);
        //if($this->ipWhitelist) {
        //    $strings = implode('\n', $list);
        //    return $strings;
        //}

        return $this->ipWhitelist;
    }

    /**
     * Add velocity
     *
     * @param \CoreBundle\Entity\UserVelocity $velocity
     *
     * @return User
     */
    public function addVelocity(\CoreBundle\Entity\UserVelocity $velocity)
    {
        $this->velocities[] = $velocity;

        return $this;
    }

    /**
     * Remove velocity
     *
     * @param \CoreBundle\Entity\UserVelocity $velocity
     */
    public function removeVelocity(\CoreBundle\Entity\UserVelocity $velocity)
    {
        $this->velocities->removeElement($velocity);
    }

    /**
     * Get velocities
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getVelocities()
    {
        return $this->velocities;
    }

    /**
     * Add token
     *
     * @param \CoreBundle\Entity\UserToken $token
     *
     * @return User
     */
    public function addToken(\CoreBundle\Entity\UserToken $token)
    {
        $this->tokens[] = $token;

        return $this;
    }

    /**
     * Remove token
     *
     * @param \CoreBundle\Entity\UserToken $token
     */
    public function removeToken(\CoreBundle\Entity\UserToken $token)
    {
        $this->tokens->removeElement($token);
    }

    /**
     * Get tokens
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getTokens()
    {
        return $this->tokens;
    }

    /**
     * Add tenantUser
     *
     * @param \CoreBundle\Entity\TenantUser $tenantUser
     *
     * @return User
     */
    public function addTenantUser(\CoreBundle\Entity\TenantUser $tenantUser)
    {
        $this->tenantUsers[] = $tenantUser;

        return $this;
    }

    /**
     * Remove tenantUser
     *
     * @param \CoreBundle\Entity\TenantUser $tenantUser
     */
    public function removeTenantUser(\CoreBundle\Entity\TenantUser $tenantUser)
    {
        $this->tenantUsers->removeElement($tenantUser);
    }

    /**
     * Get tenantUsers
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getTenantUsers()
    {
        return $this->tenantUsers;
    }

    /**
     * Add managingAffiliate
     *
     * @param \CoreBundle\Entity\Affiliate $managingAffiliate
     *
     * @return User
     */
    public function addManagingAffiliate(\CoreBundle\Entity\Affiliate $managingAffiliate)
    {
        $this->managingAffiliates[] = $managingAffiliate;

        return $this;
    }

    /**
     * Remove managingAffiliate
     *
     * @param \CoreBundle\Entity\Affiliate $managingAffiliate
     */
    public function removeManagingAffiliate(\CoreBundle\Entity\Affiliate $managingAffiliate)
    {
        $this->managingAffiliates->removeElement($managingAffiliate);
    }

    /**
     * Add promotion
     *
     * @param \CoreBundle\Entity\UserPromotion $promotion
     *
     * @return User
     */
    public function addPromotion(\CoreBundle\Entity\UserPromotion $promotion)
    {
        $this->promotions[] = $promotion;

        return $this;
    }

    /**
     * Remove promotion
     *
     * @param \CoreBundle\Entity\UserPromotion $promotion
     */
    public function removePromotion(\CoreBundle\Entity\UserPromotion $promotion)
    {
        $this->promotions->removeElement($promotion);
    }

    /**
     * Get promotions
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getPromotions()
    {
        return $this->promotions;
    }

    /**
     * Set lockedAt
     *
     * @param \DateTime $lockedAt
     *
     * @return User
     */
    public function setLockedAt($lockedAt)
    {
        $this->lockedAt = $lockedAt;

        return $this;
    }

    /**
     * Get lockedAt
     *
     * @return \DateTime
     */
    public function getLockedAt()
    {
        return $this->lockedAt;
    }

    /**
     * Add expenseCategory
     *
     * @param \CoreBundle\Entity\UserExpenseCategory $expenseCategory
     *
     * @return User
     */
    public function addExpenseCategory(\CoreBundle\Entity\UserExpenseCategory $expenseCategory)
    {
        $this->expenseCategories[] = $expenseCategory;

        return $this;
    }

    /**
     * Remove expenseCategory
     *
     * @param \CoreBundle\Entity\UserExpenseCategory $expenseCategory
     */
    public function removeExpenseCategory(\CoreBundle\Entity\UserExpenseCategory $expenseCategory)
    {
        $this->expenseCategories->removeElement($expenseCategory);
    }

    /**
     * Get expenseCategories
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getExpenseCategories()
    {
        return $this->expenseCategories;
    }

    /**
     * Add accessiblePlatform
     *
     * @param \CoreBundle\Entity\Platform $accessiblePlatform
     *
     * @return User
     */
    public function addAccessiblePlatform(\CoreBundle\Entity\Platform $accessiblePlatform)
    {
        $this->accessiblePlatforms[] = $accessiblePlatform;

        return $this;
    }

    /**
     * Remove accessiblePlatform
     *
     * @param \CoreBundle\Entity\Platform $accessiblePlatform
     */
    public function removeAccessiblePlatform(\CoreBundle\Entity\Platform $accessiblePlatform)
    {
        $this->accessiblePlatforms->removeElement($accessiblePlatform);
    }

    /**
     * Set config
     *
     * @param \SalexUserBundle\Entity\UserConfig $config
     *
     * @return User
     */
    public function setConfig(\SalexUserBundle\Entity\UserConfig $config = null)
    {
        $this->config = $config;

        return $this;
    }

    /**
     * Get config
     *
     * @return \SalexUserBundle\Entity\UserConfig
     */
    public function getConfig()
    {
        return $this->config;
    }

    /**
     * Set notifications
     *
     * @param array $notifications
     *
     * @return User
     */
    public function setNotifications(array $notifications)
    {
        $this->notifications = $notifications;

        return $this;
    }

    /**
     * Get notifications
     *
     * @return array
     */
    public function getNotifications()
    {
        return $this->notifications;
    }

    /**
     * Set viewedNotifications
     *
     * @param array $viewedNotifications
     *
     * @return User
     */
    public function setViewedNotifications(array $viewedNotifications)
    {
        $this->viewedNotifications = $viewedNotifications;

        return $this;
    }

    /**
     * Get viewedNotifications
     *
     * @return array
     */
    public function getViewedNotifications()
    {
        return $this->viewedNotifications;
    }

    /**
     * Add reshipperAddress
     *
     * @param \CoreBundle\Entity\UserBillingAddress $reshipperAddress
     *
     * @return User
     */
    public function addReshipperAddress(\CoreBundle\Entity\UserBillingAddress $reshipperAddress)
    {
        $this->reshipperAddresses[] = $reshipperAddress;

        return $this;
    }

    /**
     * Remove reshipperAddress
     *
     * @param \CoreBundle\Entity\UserBillingAddress $reshipperAddress
     */
    public function removeReshipperAddress(\CoreBundle\Entity\UserBillingAddress $reshipperAddress)
    {
        $this->reshipperAddresses->removeElement($reshipperAddress);
    }

    /**
     * Get reshipperAddresses
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getReshipperAddresses()
    {
        return $this->reshipperAddresses;
    }

    /**
     * Add userGroup
     *
     * @param \CoreBundle\Entity\UserGroup $userGroup
     *
     * @return User
     */
    public function addUserGroup(\CoreBundle\Entity\UserGroup $userGroup)
    {
        $this->userGroups[] = $userGroup;

        return $this;
    }

    /**
     * Remove userGroup
     *
     * @param \CoreBundle\Entity\UserGroup $userGroup
     */
    public function removeUserGroup(\CoreBundle\Entity\UserGroup $userGroup)
    {
        $this->userGroups->removeElement($userGroup);
    }

    /**
     * Get userGroups
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getUserGroups()
    {
        return $this->userGroups;
    }

    /**
     * Set watch
     *
     * @param string $watch
     *
     * @return User
     */
    public function setWatch($watch)
    {
        $this->watch = $watch;

        return $this;
    }

    /**
     * Get watch
     *
     * @return string
     */
    public function getWatch()
    {
        return $this->watch;
    }

    /**
     * @return LoadLocator
     */
    public function getLoadLocator()
    {
        return $this->LoadLocator;
    }

    /**
     * Set LoadLocator
     *
     * @param \CoreBundle\Entity\LoadLocator $LoadLocator
     *
     * @return User
     */
    public function setLoadLocator(\CoreBundle\Entity\LoadLocator $LoadLocator = null)
    {
        $this->LoadLocator = $LoadLocator;

        return $this;
    }

    /**
     * Add merchant
     *
     * @param \ClfBundle\Entity\Merchant $merchant
     *
     * @return User
     */
    public function addMerchant(\ClfBundle\Entity\Merchant $merchant)
    {
        $this->merchants[] = $merchant;

        return $this;
    }

    /**
     * Remove merchant
     *
     * @param \ClfBundle\Entity\Merchant $merchant
     */
    public function removeMerchant(\ClfBundle\Entity\Merchant $merchant)
    {
        $this->merchants->removeElement($merchant);
    }

    /**
     * Get merchants
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getMerchants()
    {
        return $this->merchants;
    }

    /**
     * Add location
     *
     * @param \SpendrBundle\Entity\Location $location
     *
     * @return User
     */
    public function addLocation(\SpendrBundle\Entity\Location $location)
    {
        $this->locations[] = $location;

        return $this;
    }

    /**
     * Remove location
     *
     * @param \SpendrBundle\Entity\Location $location
     */
    public function removeLocation(\SpendrBundle\Entity\Location $location)
    {
        $this->locations->removeElement($location);
    }

    /**
     * Get locations
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getLocations()
    {
        return $this->locations;
    }
    /**
     * @return Collection<int, PayPalSubscription>
     */
    public function getPayPalSubscriptions(): Collection
    {
        return $this->payPalSubscriptions;
    }

    public function addPayPalSubscription(PayPalSubscription $payPalSubscription): static
    {
        if (!$this->payPalSubscriptions->contains($payPalSubscription)) {
            $this->payPalSubscriptions->add($payPalSubscription);
            $payPalSubscription->setUser($this);
        }

        return $this;
    }

    public function removePayPalSubscription(PayPalSubscription $payPalSubscription): static
    {
        if ($this->payPalSubscriptions->removeElement($payPalSubscription)) {
            // set the owning side to null (unless already changed)
            if ($payPalSubscription->getUser() === $this) {
                $payPalSubscription->setUser(null);
            }
        }

        return $this;
    }
}
