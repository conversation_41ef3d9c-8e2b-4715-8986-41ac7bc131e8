<?php

namespace FaasBundle\Services;

use Carbon\Carbon;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\Processor;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserCardBalance;
use CoreBundle\Entity\UserGroup;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Util;
use FaasBundle\Services\BOTM\BotmAPI;
use FaasBundle\Services\BOTM\BotmService;
use PortalBundle\Exception\PortalException;
use SalexUserBundle\Entity\User;
use TransferMexBundle\Services\MemberService;
use TransferMexBundle\Services\RapidAPI;
use TransferMexBundle\Services\RapidService;
use TransferMexBundle\Services\SlackService;
use TransferMexBundle\Services\Tern\TernService;

class ProcessorHub
{
    public static function getAllApis($platform = null)
    {
        $all = [
            BotmService::getAllApis($platform),
            RapidService::getAllApis($platform),
        ];
        $result = [];
        foreach ($all as $items) {
            foreach ($items as $key => $item) {
                $result[$key] = $item;
            }
        }
        return $result;
    }

    public static function getAllApisByGroup($platform = null)
    {
        $all = self::getAllApis($platform);
        $result = [];
        /* @var IProcessor $api */
        foreach ($all as $key => $api) {
            $pl = $api->getPlatform();
            $gid = Util::id($pl, 0);
            if ($api->entity instanceof UserGroup) {
                $gid .= '_' . $api->entity->getId();
            }
            if ( ! array_key_exists($gid, $result)) {
                $result[$gid] = [];
            }
            $result[$gid][$key] = $api;
        }
        return $result;
    }

    public static function getForUserGroupAdmin(User $user)
    {
        $group = $user->getAdminGroup();
        $class = $group->getProcessor()->getApiClass();
        if ($class) {
            return $class::getForUserGroupAdmin($user);
        }
        return null;
    }

    public static function ensureEmployerConfig(UserGroup $ug)
    { 
        $processor = $ug->getProcessor();
        if ($processor->isBOTM()) {
            BotmService::ensureUserGroupBusiness($ug);
            return BotmService::syncEmployerAccountNumbers($ug);
        }
        if ($processor->isTERN()) {
            TernService::ensureUserGroupBusiness($ug);
            return TernService::syncEmployerAccountNumbers($ug);
        }
        return null;
    }

    public static function updateAgentBalance(IProcessor $api)
    {
        $balance = $api->getAgentAvailableBalance();
        Data::set($api->getBalanceCacheKey(), $balance);
        return $balance;
    }

    public static function updateBalanceAndStatusSilently(UserCard $uc)
    {
        try {
            self::updateBalanceAndStatus($uc);
        } catch (\Exception $e) {
            Log::warn('ProcessorHub: Failed to updateBalanceAndStatus on card ' . $uc->getId() . ': ' . $e->getMessage());
        }
    }

    public static function updateBalanceAndStatus(UserCard $uc, $type = UserCardBalance::TYPE_LOOKUP, $comment = NULL, $meta = NULL)
    {
        $service = $uc->getProcessorApiService();
        $service::updateBalanceAndStatus($uc, $type, $comment, $meta);
    }

    public static function updateAllAgentsBalance($platform = false)
    {
        $result = [];
        $apis = self::getAllApis($platform);
        /** @var IProcessor $api */
        foreach ($apis as $api) {
            try {
                $balance = self::updateAgentBalance($api);
                $result[$api->agentNumber] = $balance;
            } catch (\Exception $exception) {
                if ($api instanceof RapidAPI && RapidAPI::isDisabled()) {
                    Log::debug('Skip updating agent balance for ' . $api->getName(true) . ' due to ' .
                               Util::exceptionBrief($exception));
                    continue;
                }
                $exKey = __METHOD__ . '_' . $api->agentNumber . '_' . date('Y-m-d');
                if (!Data::has($exKey)) {
                    SlackService::prepareForPlatform();
                    SlackService::alert('Failed to update agent balance: ' . $exception->getMessage(), [
                        'agent' => $api->getName(true),
                    ]);
                }
            }
        }
        return $result;
    }

    public static function getApiSummary(IProcessor $api)
    {
        $desc = [
            'name' => $api->getName(true),
        ];
        $entity = $api->entity;
        if ($entity instanceof UserGroup) {
            $desc['group'] = $entity->toApiArray();

            $dev = [
                'botmBusinessId' => $entity->getBotmBusinessId(),
                'botmBusinessAccountId' => $entity->getBotmBusinessAccountId(),
            ];
            $meta = Util::meta($entity);
            foreach ([
                'rapidAgentName', 'rapidAgentNumber', 'rapidAgentAni',
            ] as $field) {
                if (isset($meta[$field])) {
                    $dev[$field] = $meta[$field];
                }
            }
            $desc['meta'] = $dev;
        } else if ($entity instanceof UserCard) {
            $group = $entity->getUser()->getPrimaryGroup();
            $desc['group'] = $group ? $group->toApiArray() : null;
        } else if ($entity instanceof User) {
            $group = $entity->getAdminGroup();
            $desc['group'] = $group ? $group->toApiArray() : null;
        } else if ($api instanceof BotmAPI && (!$entity || $entity instanceof Platform)) {
            $platform = Util::$platform ?? Platform::transferMex();
            $desc['meta'] = [
                'botmBusinessId' => $platform->getBotmBusinessId(),
                'botmBusinessAccountId' => $platform->getBotmBusinessAccountId(),
            ];
        }
        return $desc;
    }

    public static function getTempUserCard(UserCard $uc, string $an, bool $force = false)
    {
        if ($force || $uc->getAccountNumber() !== $an) {
            return self::getForceTempUserCard($an);
        }
        return $uc;
    }

    public static function getForceTempUserCard(string $an)
    {
        $uc = new UserCard();
        $uc->setAccountNumber($an);
        return $uc;
    }

    public static function getForUserCardAccountNumber(UserCard $uc, $an)
    {
        if (!$an) {
            return $uc->getProcessorApiImpl();
        }
        if (BotmAPI::isAccountNumberValid($an)) {
            return BotmAPI::getForUserCardAccountNumber($uc, $an);
        }
        if (RapidAPI::isAccountNumberValid($an)) {
            return RapidAPI::getForUserCardAccountNumber($uc, $an);
        }
        return null;
    }

    public static function getForAgent($agentNumber)
    {
        if (str_starts_with($agentNumber, 'botm_')) {
            return BotmAPI::getForAgent($agentNumber);
        }
        return RapidAPI::getForAgent($agentNumber);
    }

    /**
     * @param UserCard $uc
     *
     * @return IProcessor|null
     */
    public static function getBaseAgentProcessor(UserCard $uc)
    {
        $an = $uc->getAccountNumber();
        return self::getBaseAgentProcessorForAccountNumber($an);
    }

    /**
     * @param string $an The account number
     *
     * @return BotmAPI|RapidAPI|null
     */
    public static function getBaseAgentProcessorForAccountNumber(string $an)
    {
        if (Util::startsWith($an, 'BOTM-') || BotmAPI::isAccountNumberValid($an)) {
            return new BotmAPI();
        }
        if (RapidAPI::isAccountNumberValid($an)) {
            return new RapidAPI();
        }
        return null;
    }

    public static function validateFullNumberPan($pan, $msg = 'Invalid card number!')
    {
        $ex = PortalException::temp($msg);
        if (!$pan) {
            throw $ex;
        }
        $matches = [];
        preg_match('|^\d{8}$|', $pan, $matches);
        if (!$matches) {
            preg_match('|^\d{16}$|', $pan, $matches);
            if (!$matches) {
                throw $ex;
            }
        }
    }

    public static function retireOldCard(UserCard $uc, $saveLegacy = true)
    {
        $old = $uc->getAccountNumber();
        if ($old && $saveLegacy) {
            $oan = Util::meta($uc, 'oldAccountNumbers') ?? [];
            $oan[] = $old;
            $meta = [
                'oldAccountNumbers' => array_values(array_unique($oan)),
            ];
            if (RapidAPI::isAccountNumberValid($old)) {
                $meta['retiredRapidAccount'] = $old;
            }
            Util::updateMeta($uc, $meta, false);
        }
        $uc->setAccountNumber(null)
            ->setIssued(false)
            ->setIssuedAt(null)
            ->persist();
        $uc->getUser()->addNote('Retired the old account ' . $old,
            TRUE, Util::id(Util::user()));
        // add flag to the season name of this user
        $user = $uc->getUser();
        $seaName = Util::meta($user, 'seasonName')??'';

        Util::updateMeta($user, ['seasonName' => trim('migration ' . $seaName)]);
    }

    public static function getConsumerTransactionKey(IProcessor $api, UserCard $uc)
    {
        $parts = [
            $api->getKey(),
        ];
        if ($api instanceof RapidAPI) {
            $parts[] = $api->getAgentNumber();
        }
        // BOTM transactions API is not agent-number sensitive.
        if ($uc->getId()) {
            $parts[] = $uc->getId();
        }
        return implode('_', $parts);
    }

    public static function updateConsumerTransactionsForCard(IProcessor $api, UserCard $inputUc,
                                                             Carbon $startAt, Carbon $endAt = null,
                                                             &$news = [], $msgPrefix = '',
                                                             &$pendings = [])
    {
        $service = $api::getProcessorService();
        if ($service === RapidService::class && RapidAPI::isDisabled()) {
            return 0;
        }

        /** @var IProcessorService $service */
        return $service::updateConsumerTransactionsForCard(
            $api, $inputUc, $startAt, $endAt,
            $news, $msgPrefix, $pendings
        );
    }

    public static function areSameProcessor(array $accountNumbers)
    {
        if ( ! $accountNumbers) {
            return false;
        }
        $first = $accountNumbers[0];
        $processors = [
            BotmAPI::class,
            RapidAPI::class,
        ];
        $processor = null;
        /** @var IProcessor $p */
        foreach ($processors as $p) {
            if ($p::isAccountNumberValid($first)) {
                $processor = $p;
                break;
            }
        }
        if (!$processor) {
            return false;
        }
        $count = count($accountNumbers);
        for ($i = 1; $i < $count; $i++) {
            $an = $accountNumbers[$i];
            if ( ! $processor::isAccountNumberValid($an)) {
                return false;
            }
        }
        return true;
    }

    public static function isFromRapidToBotm(string $oldAccountNumber, string $newAccountNumber)
    {
        return RapidAPI::isAccountNumberValid($oldAccountNumber) &&
               BotmAPI::isAccountNumberValid($newAccountNumber);
    }

    public static function getCurrentProcessor(UserCard|string $input = null)
    {
        if ( ! $input) {
            return null;
        }
        if (is_string($input)) {
            $uc = new UserCard();
            $uc->setAccountNumber($input);
        } else {
            $uc = $input;
        }
        return $uc->getCurrentProcessor();
    }

    public static function updateConsumerInfo(User $user)
    {
        $uc = self::verifyCardEnrolled($user);
        if (!$uc) {
            return;
        }
        $service = $uc->getProcessorApiService();
        if ($service) {
            $service::updateConsumerInfo($user);
        }
    }

    public static function verifyCardEnrolled(User|UserCard|null $input): UserCard|null
    {
        if ($input instanceof User) {
            $input = $input->getCurrentPlatformCard();
        }
        if (!$input) {
            return null;
        }
        if (!$input->getAccountNumber()) {
            return null;
        }
        return $input;
    }

    public static function validateBarcodeNumber($number, $deep = true): string
    {
        $processor = self::getCurrentProcessor($number);
        if ( ! $processor) {
            throw PortalException::temp('Unknown barcode number format. Please recheck.');
        }
        if ($deep) {
            if ($processor === Processor::NAME_BOTM) {
                $validate = BotmService::validateBarcodeNumber($number);
                if (is_string($validate)) {
                    throw PortalException::temp($validate);
                }
            }
        }
        return $processor;
    }

    public static function getProcessorUserId(User $member)
    {
        $uc = $member->getCurrentPlatformCard();
        $group = $member->getPrimaryGroup();
        if ($uc->isBotmCard() || $group->isBOTM()) {
            return $member->ensureConfig()->getBotmUserId();
        }
        return null;
    }

    public static function ensureProcessorUser(User $member)
    {
        $step = $member->getRegisterStep();
        if ( ! in_array($step, [
            MemberService::STEP_ONBOARDED,
            MemberService::STEP_ACTIVE,
        ])) {
            return false;
        }
        $uc = $member->getCurrentPlatformCard();
        $group = $member->getPrimaryGroup();
        if ($uc->isBotmCard() || $group->isBOTM()) {
            $userId = $member->ensureConfig()->getBotmUserId();
            if ( ! $userId) {
                $userId = BotmService::searchAndFillUser($member);
                if ( ! $userId) {
                    $api = BotmAPI::getForUserGroup($group);
                    $userId = $api->ensureBotmUserId($member);
                    try {
                        $api->ensureBotmUserAccountId($member);
                    } catch (\Throwable $t) {
                        Log::warn('Failed to ensure BOTM user ID', [
                            'member' => $member->getId(),
                            'error' => Util::getExceptionBrief($t),
                        ]);
                    }
                }
            }
            return $userId;
        }
        return null;
    }

    public static function ensureProcessorUserSilently(User $member)
    {
        try {
            return self::ensureProcessorUser($member);
        } catch (\Throwable $t) {
            Log::warn('Failed to ensure processor user', [
                'member' => $member->getId(),
                'error' => Util::getExceptionBrief($t),
            ]);
        }
        return null;
    }
}
