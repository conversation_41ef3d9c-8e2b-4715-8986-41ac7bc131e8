<?php

namespace FaasBundle\Services\BOTM;

use Carbon\Carbon;
use CoreBundle\Entity\Address;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\Country;
use CoreBundle\Entity\Email;
use CoreBundle\Entity\ExternalInvoke;
use CoreBundle\Entity\Merchant;
use CoreBundle\Entity\MerchantType;
use CoreBundle\Entity\NotificationEmails;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\State;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserCardBalance;
use CoreBundle\Entity\UserCardTransaction;
use CoreBundle\Entity\UserGroup;
use CoreBundle\Services\SSLEncryptionService;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Security;
use CoreBundle\Utils\Util;
use FaasBundle\Services\IProcessor;
use FaasBundle\Services\IProcessorService;
use FaasBundle\Services\ProcessorServiceTrait;
use PortalBundle\Exception\PortalException;
use SalexUserBundle\Entity\User;
use SalexUserBundle\Entity\UserConfig;
use TransferMexBundle\Services\MemberService;
use TransferMexBundle\Services\SlackService;
use TransferMexBundle\Entity\BotmKycExceptions;

class BotmService implements IProcessorService
{
    use ProcessorServiceTrait;

    public static function syncEmployerAccountNumbers(UserGroup $group)
    {
        $api = BotmAPI::getForUserGroup($group);
        $api->affirmBusinessId();
        $accountId = $api->affirmBusinessAccountId();
        $data = ExternalInvoke::host($api->retrieveBusinessAccount($accountId));

        $employer = $group->getPrimaryAdmin();
        Util::updateMeta($employer, [
            'Institution Name' => BotmAPI::BANK_NAME,
            'Account Number' => SSLEncryptionService::encrypt($data['internal_deposit_account']),
            'Routing Number' => SSLEncryptionService::encrypt($data['routing_number']),
        ]);

        return $data;
    }

    public static function ensureUserGroupBusiness(UserGroup $ug)
    {
        $businessId = $ug->getBotmBusinessId();
        $businessAccountId = $ug->getBotmBusinessAccountId();
        if ($businessId && $businessAccountId) {
            return $businessAccountId;
        }

        $_ids = Util::em()->getRepository(UserGroup::class)
            ->createQueryBuilder('ug')
            ->where('ug.botmBusinessAccountId is not null')
            ->select('ug.botmBusinessId')
            ->getQuery()
            ->getArrayResult();
        $ids = array_pluck($_ids, 'botmBusinessId');

        $ugName = strtolower($ug->getName());
        $api = new BotmAPI();
        $exists = ExternalInvoke::host($api->listBusiness());
        foreach ($exists as $e) {
            $name = strtolower($e['legal_business_name']);
            if ($name === $ugName && !in_array($e['id'], $ids)) {
                $ug->setBotmBusinessId($e['id'])
                    ->persist();
                break;
            }
        }

        $api = BotmAPI::getForUserGroup($ug);
        $api->ensureBusinessId($ug, true);
        return $api->ensureBusinessAccountId($ug, true);
    }

    public static function getUserGroupsWithCustomAccount($platform = null)
    {
        $q = Util::em()->getRepository(UserGroup::class)
            ->createQueryBuilder('ug')
            ->where('ug.botmBusinessAccountId is not null');

        if ($platform) {
            $q->andWhere('ug.platform = :platform')
                ->setParameter('platform', $platform);
        }

        return $q->getQuery()
            ->getResult();
    }

    public static function getAllApis($platform = null)
    {
        $platform = $platform ?? Util::platform();
        $staticKey = 'botm_' . ($platform ? $platform->getId() : 'all');
        if (array_key_exists($staticKey, self::$apisGroup)) {
            return self::$apisGroup[$staticKey];
        }

        $all = [];
        if ($platform) {
            $base = BotmAPI::getForPlatform($platform);
            $all['botm_' . $base->businessAccountId] = $base;
        } else {
            $base = new BotmAPI();
            $all['botm_' . $base->businessAccountId] = $base;

            $platforms = Util::em()->getRepository(Platform::class)->findAll();
            /** @var Platform $p */
            foreach ($platforms as $p) {
                if ($p->is(Platform::NAME_TRANSFER_MEX)) {
                    continue;
                }
                $agent = $p->getBotmBusinessAccountId();
                if ($agent) {
                    $base = BotmAPI::getForPlatform($platform);
                    $all['botm_' . $base->businessAccountId] = $base;
                }
            }
        }
        $groups = self::getUserGroupsWithCustomAccount($platform);
        foreach ($groups as $group) {
            $api = BotmAPI::getForUserGroup($group);
            $all['botm_' . $api->businessAccountId] = $api;
        }

        self::$apisGroup[$staticKey] = $all;
        return $all;
    }

    public static function createCard(UserCard $uc)
    {
        $user = $uc->getUser();
        $api = BotmAPI::getForUserCard($uc);
        $api->ensureBotmUserId($user);
        $api->ensureBotmUserAccountId($user);
        $data = ExternalInvoke::host($api->createCard($uc));

        self::onCreatedCard($uc, $data['proxy_value']);

        $uc->setBotmToken($data['token'])
            ->setAccountId($data['id'])
            ->setPan(Util::maskPan($data['last_four']))
            ->persist();

        if ($user->inTeam(Role::ROLE_TRANSFER_MEX_MEMBER)) {
            MemberService::updateOnboardStatus($user);
        }

        self::updateBalanceAndStatus($uc);
    }

    public static function enroll(UserCard $uc)
    {
        $group = self::checkBeforeEnrollingCard($uc);
        $user = $uc->getUser();

        $actions = [];
        $service = BotmAPI::getForUserCard($uc);
        if (!$uc->isIssued()) {
            if ($group) {
                $groupAgent = $group->getBotmBusinessAccountId();
                if (!$groupAgent) {
                    SlackService::$channel = 'client';
                    SlackService::alert('The employer has not configured the BOTM business info when attempting to enroll a card', [
                        'employer' => $group->getName(),
                        'member' => $user->getSignature(),
                        'account' => $uc->getAccountNumber(),
                    ], SlackService::GROUP_TRANSFER_MEX);
                    throw PortalException::temp('Failed to enroll the card since the employer is not well configured yet.');
                }
            }

            $data = ExternalInvoke::host($service->retrieveCard($uc));
            if (!empty($data['user_account_id'])) {
                $uaId = $service->getBotmUserAccountId($user);
                if ($uaId && $uaId !== $data['user_account_id']) {
                    $findUser = BotmUserService::findByBotmUserAccountId($data['user_account_id']);
                    if ($findUser) {
                      $linkedUser = $findUser->getSignature();
                      $message = !Util::isMexMobileRequest() ? 'The card assigned to this member is belong to the BOTM user account who has been linked to ' . $linkedUser . ', please use a new card!' : 'The card has been assigned to anouther member, please use a new card!';
                      throw PortalException::temp($message);
                    }
                    $config = $user->ensureConfig();
                    $matched = self::matchBotmName($user, $data);
                    $context = [
                        'user' => $user->getSignature(),
                        'name_matched' => $matched,
                        'old_botm_user' => self::getBotmUserSignature($config->getBotmUserId()),
                        'new_botm_user' => self::getBotmUserSignature($data['user_id']),
                        'old_botm_account_id' => $uaId,
                        'new_botm_account_id' => $data['user_account_id'],
                    ];
                    $config->setBotmUserId($data['user_id'])
                        ->replaceBotmUserAccountId($data['user_account_id'])
                        ->persist();

                    // TODO: Invoke a async job to check and move balance immediately.

                    SlackService::warning('Changed the user BOTM account ID when enrolling card', $context,
                        $matched ? [] : SlackService::GROUP_DEVS);
                }
                if (!$uaId) {
                    self::fillUserBotmIds($uc, $data['user_account_id']);
                }
            } else {
                self::searchAndFillUser($user);
                $service->ensureBotmUserId($user);
                /** @var ExternalInvoke $ei */
                [$ei, $data] = $service->enroll($uc);
                if ($ei && $ei->isFailed()) {
                    throw PortalException::temp($ei->getError());
                }
            }

            $uc->setBotmToken($data['card_token'] ?? $uc->getBotmToken())
                ->setAccountId($data['id'] ?? $uc->getAccountId())
                ->setPan(Util::maskPan($data['last_four'] ?? $uc->getLast4()))
                ->setIssued(true)
                ->persist();
            $actions[] = 'enroll';

            // Your card has been enrolled. Please change the PIN as soon as possible.
            $content = 'Su tarjeta ha sido registrada. Cambie el PIN lo antes posible.';
            MemberService::sendChangePinMessageToMembers($user, $content);
        }

        self::updateBalanceAndStatus($uc);

        if ($uc->getNativeStatus() !== BotmAPI::CARD_STATUS_ACTIVE) {
            [$ei, ] = $service->activate($uc);
            if ($ei && $ei->isFailed()) {
                throw PortalException::temp($ei->getError());
            }
        }

        if ( ! $uc->isActive()) {
            ExternalInvoke::host($service->updateUserAccountStatus($user, true));
        }

        self::updateBalanceAndStatus($uc);
        $actions[] = 'activate';

        if ($actions) {
            $user->addNote('Performed the ' . implode('/', $actions)
                                    . ' actions to the account ' . $uc->getAccountNumber(),
                TRUE, Util::user()->getId());
        }

        if ($uc->getNativeStatus() !== BotmAPI::CARD_STATUS_ACTIVE) {
            SlackService::alert('Failed to activate the card.', [
                'member' => $user->getSignature(),
                'status' => $uc->getNativeStatus(),
            ]);
            throw PortalException::temp('Your card is issued but not activated. Please try again a moment later.');
        }
    }

    public static function syncUserAccountStatus(UserCard $uc)
    {
        $api = BotmAPI::getForUserCard($uc);
        ExternalInvoke::host($api->updateUserAccountStatus($uc->getUser(), $uc->isActive()));
    }

    private static function fillUserBotmIds(UserCard $uc, $botmUserAccountId)
    {
        $api = BotmAPI::getForUserCard($uc);
        $data = ExternalInvoke::host($api->retrieveUserAccountById($botmUserAccountId));
        if (!empty($data['user_id'])) {
            $data['_user'] = ExternalInvoke::host($api->retrieveUserRaw($data['user_id']));
            $user = $uc->getUser();
            $_email = $data['_user']['email'] ?? '';
            if ($_email && $user->getEmail() !== $_email) {
                $fn = Util::matchable($user->getFirstName());
                $ln = Util::matchable($user->getLastName());
                $_fn = Util::matchable($data['_user']['first_name'] ?? '');
                $_ln = Util::matchable($data['_user']['last_name'] ?? '');
                if ($fn !== $_fn || $ln !== $_ln) {
                    SlackService::warning('Unmatched user info when filling BOTM user ID', [
                        'local' => [
                            'id' => $user->getId(),
                            'email' => $user->getEmail(),
                            'fn' => $fn,
                            'ln' => $ln,
                        ],
                        'botm' => [
                            'accountId' => $botmUserAccountId,
                            'email' => $_email,
                            'fn' => $_fn,
                            'ln' => $_ln,
                        ],
                    ], SlackService::GROUP_DEVS);
                }
            }
            $uc->getUser()
                ->ensureConfig()
                ->setBotmUserId($data['user_id'])
                ->replaceBotmUserAccountId($botmUserAccountId)
                ->persist();
        } else {
            throw PortalException::create('Invalid user ID from account ID');
        }
    }

    public static function updateBalanceAndStatus(UserCard $uc, $type = UserCardBalance::TYPE_LOOKUP, $comment = NULL, $meta = NULL)
    {
        $api = BotmAPI::getForUserCard($uc);

        $user = $uc->getUser();
        $accountId = $user->ensureConfig()->getBotmUserAccountId();
        if ($accountId) {
            /** @var ExternalInvoke $ei */
            [$ei, $details] = $api->retrieveUserAccount($user);
            if ($ei && $ei->isFailed()) {
                throw PortalException::createFromEi('Failed to retrieve user account data', $ei->getError());
            }
            if (!is_array($details) || !array_key_exists('available_balance', $details)) {
                throw PortalException::temp(
                    'Failed to get the account details (empty response) for user account ' . $accountId);
            }

            $ucActive = ($details['status_name'] ?? '') === 'Active' ||
                        ($details['active'] ?? 0) === 1 ||
                        ($details['active'] ?? 0) === true;
            $uc->setBalance($details['available_balance'], $type, $comment, $meta)
                ->setStatus($ucActive ? UserCard::STATUS_ACTIVE : UserCard::STATUS_INACTIVE);

            Util::updateMeta($uc, [
                'botmStatusName' => $details['status_name'] ?? 'Unset',
            ]);
        }

        if (BotmAPI::isAccountNumberValid($uc->getAccountNumber())) {
            $data = ExternalInvoke::host($api->retrieveCard($uc));
            $uc->setNativeStatus($data['card_status'])
                ->setBotmToken($data['card_token'] ?? $uc->getBotmToken())
                ->setAccountId($data['id'] ?? $uc->getAccountId())
                ->setPan(Util::maskPan($data['last_four'] ?? $uc->getLast4()))
                ->persist();
        }
    }

    public static function changePin(UserCard $uc, #[\SensitiveParameter] $newPin, #[\SensitiveParameter] $cardNumber): string
    {
        if ($uc->getLast4()) {
            $len = strlen($cardNumber ?? '');
            if ($len === 16) {
                $last = substr($cardNumber, -4);
                if ($last !== $uc->getLast4()) {
                    throw PortalException::temp('Invalid card number!!');
                }
            } else {
                $cardNumber .= $uc->getLast4();
            }
        }
        ExternalInvoke::host(
            $uc->getProcessorApiImpl()->changePin($uc, $newPin, $cardNumber)
        );
        return $cardNumber;
    }

    public static function moveFunds(int $amount, string $comment, ?UserGroup $fromGroup, ?UserGroup $toGroup, ?string $externalTransactionId = null)
    {
        if (($fromGroup && !BotmAPI::hasUserGroupAccount($fromGroup)) || ($toGroup && !BotmAPI::hasUserGroupAccount($toGroup))) {
            throw PortalException::temp('Only support transferring from a BOTM employer to another BOTM employer.');
        }
        if (!$fromGroup && !$toGroup) {
            throw PortalException::temp('Skip moving funds from base and to base.');
        }
        if (Util::eq($fromGroup, $toGroup)) {
            throw PortalException::temp('Skip moving funds from and to the same employer.');
        }
        $api = $fromGroup ? BotmAPI::getForUserGroup($fromGroup) : new BotmAPI();
        $businessAccountId = $api->affirmBusinessAccountId();
        $programAccountId = $api->getProgramAccountId();
        ExternalInvoke::throwIfFailed(
            $api->internalAccountTransfer(
                BotmAPI::TRANSFER_TYPE_BUSINESS_TO_PROGRAM, $businessAccountId, $programAccountId,
                $amount, 'b2p: ' . $comment, $externalTransactionId
            ),
            'Failed to transfer to the middle account'
        );

        $api = $toGroup ? BotmAPI::getForUserGroup($toGroup) : new BotmAPI();
        $businessAccountId = $api->affirmBusinessAccountId();
        /** @var ExternalInvoke $ei */
        [$ei] = $api->transferAccountBalance(
            BotmAPI::TRANSFER_TYPE_PROGRAM_TO_BUSINESS, $programAccountId, $businessAccountId,
            $amount, 'p2b: ' . $comment, $externalTransactionId
        );
        if ($ei && $ei->isFailed()) {
            SlackService::$channel = SlackService::CHANNEL_CLIENT;
            SlackService::alert('Failed to transfer the amount to the group. This needs to be fixed manually', [
                'error' => $ei->getErrorSignature(),
                'from' => $fromGroup ? $fromGroup->getSignature() : 'Base',
                'to' => $toGroup ? $toGroup->getSignature() : 'Base',
                'amount' => Money::formatUSD($amount),
                'comment' => $comment,
                'externalTransactionId' => $externalTransactionId,
            ], SlackService::MENTION_HANS);
            throw PortalException::createFromEi('Failed to transfer the amount to the group', $ei);
        }
    }

    public static function getCachedAccountIdByProxyValue($proxyValue)
    {
        $cacheKey = 'botm_account_id_from_proxy_value_' . $proxyValue;
        if (Data::has($cacheKey)) {
            return (int)Data::get($cacheKey);
        }
        $uc = new UserCard();
        $uc->setAccountNumber($proxyValue);
        $api = new BotmAPI();
        $card = ExternalInvoke::host($api->retrieveCard($uc));
        $accountId = $card['user_account_id'];
        Data::set($cacheKey, $accountId);
        return $accountId;
    }

    /**
     * @param BotmAPI  $api
     */
    public static function updateConsumerTransactionsForCard(IProcessor $api, UserCard $inputUc,
                                                             Carbon $startAt, Carbon $endAt = NULL,
                                                             &$news = [], $msgPrefix = '', &$pendings = []): int
    {
        $thisAccountId = null;
        if ($inputUc->getId()) {
            $thisAccountId = $inputUc->getUser()->ensureConfig()->getBotmUserAccountId();
            if (!$thisAccountId) {
                return 0;
            }
        } else if ($inputUc->getAccountNumber()) {
            $thisAccountId = self::getCachedAccountIdByProxyValue($inputUc->getAccountNumber());
        }

        if (!$thisAccountId) {
            return 0;
        }

        if ($msgPrefix) {
            $msgPrefix .= ' ' . $api->getName() . ' ---- ';
        }

        $newEntities = [];
        $total = 0;
        $page = 0;
        $pageSize = 100;
        $tz = Util::tzUTC();
        $now = Carbon::now();
        $endAt = $endAt ?? Carbon::now($tz)->endOfDay();

        $startAt->setTimezone($tz);
        $endAt->setTimezone($tz);

        $minDay = Carbon::now()->subDays(7);

        $em = Util::em();
        $uniqueTxns = [];
        $platform = $api->getPlatform();
        $cardTxnIds = [];
        do {
            $page++;
//            $pagePrefix = ' ' . Util::randTimeNumber() . ' ';
//            Util::consoleLine($msgPrefix . $pagePrefix . 'Fetching for page ' . $page . ' between '
//                              . $startAt->format(Util::DATE_FORMAT_SEARCH) . ' ~ ' . $endAt->format(Util::DATE_FORMAT_SEARCH));

            /** @var ExternalInvoke $ei */
            [$ei, $data] = $api->consumerTransactions($inputUc, $startAt, $endAt, $page, $pageSize, otherParams: [
                'order_direction' => 'ASC',
            ]);
//            Util::consoleLine($msgPrefix . $pagePrefix . 'Fetched for page ' . $page);

            if ($ei->isFailed()) {
                throw PortalException::fromEi($ei);
            }

            if (empty($data)) {
                break;
            }
            $countStatus = count($data);
//            Util::consoleLine($msgPrefix . $pagePrefix . 'Filling with ' . $countStatus . ' transactions');

            $total += $countStatus;
            foreach ($data as $item) {
                if (empty($item['user_account_id']) || empty($item['transaction_id'])) {
                    continue;
                }
                $txnId = 'botm_' . $item['transaction_id'];
                $accountId = (int)$item['user_account_id'];

                $user = null;
                if ($accountId === $thisAccountId && $inputUc->getId()) {
                    $uc = $inputUc;
                    $user = $uc->getUser();
                } else {
                    $user = static::findUserByAccountId($accountId);
                    if (!$user) {
                        Log::debug('BOTM transaction on an unknown account ' . $accountId, $item);
                        continue;
                    }
                    $uc = $user->getOneCardInPlatform($platform);
                }

                if ( ! ($uc instanceof UserCard)) {
                    Log::warn("Failed to find the BOTM user card for transaction $txnId of account $accountId", [
                        'user' => $user?->getId(),
                    ]);
                    continue;
                }

                // store the employer
                $group = $user->getPrimaryGroup();
                $employer =  $user->getPrimaryGroupAdmin();
                if ($item['mode'] == 'BUSINESS_TO_USER' &&  Util::startsWith($item['transfer_comment'], 'Payout TranID') && $group && $employer) {
                  $employerTransaction = UserCardTransaction::findForTransferMex($item['related_id']);
                  $employerCard = $employer->getOneCardInPlatform($platform);
                  if (!$employerTransaction && $employerCard) {
                    $employerTransaction= new UserCardTransaction();
                    $employerTransaction->setUserCard($employerCard)
                                        ->setTranId($item['related_id'])
                                        ->setTxnAmount(Money::normalizeAmount($item['amount'], 'USD') * -1)
                                        ->setActualTranCode(strtolower($item['mode']))
                                        ->setProductName(Util::$platform->getName())
                                        ->setTranCode('DEBIT')
                                        ->setTranDesc(Util::cleanUtf8String($item['card_holder_name'] ?? null))
                                        ->setAccountStatus(self::getTransactionStatus($item['status']))
                                        ->setOutstandingAuths('botm_'. $group->getBotmBusinessAccountId())
                                        ->setUserField2(Util::cleanUtf8String($item['card_holder_name'] ?? null))
                                        ->setTxnTime(Util::timeUTCNullable($item['balance_changed_date'] ?? null));
                    $em->persist($employerTransaction);
                  }
                }

                $t = $uniqueTxns[$txnId] ?? UserCardTransaction::findForTransferMex($txnId);
                $txnTime = Util::timeUTCNullable($item['card_transaction_date'] ?? $item['balance_changed_date'] ?? null);
                $txnMsg = $item['message'] ?? '';
                if (in_array($txnMsg, [
//                    'AUTHORIZATION to CLEARING', // Removed due to https://terncommerce.slack.com/archives/C017HLZ2U2Z/p1756935849222129
                    'AUTHORIZATION to AUTHORIZATION_REVERSAL',
                ])) {
                    $txnTime = Util::timeUTCNullable($item['balance_changed_date'] ?? null) ?? $txnTime;
                }

                if (!$t) {
                    $t = new UserCardTransaction();
                    $t->setUserCard($uc)
                        ->setTranId($txnId)
                        ->setProductName(UserCardTransaction::PRODUCT_TRANSFER_MEX);
                    $newEntities[] = $t;
                    $uniqueTxns[$txnId] = $t;

                    $viaApproved = strtolower($item['status'] ?? '') !== 'declined';
                    $isRecent = true;
                    if ($txnTime && $minDay->isAfter($txnTime)) {
                        $isRecent = false;
                    }
                    if ($viaApproved && $isRecent && $uc->getUser()) {
                        // new ATM and pos transaction
                        $messageInfo = [];
                        $locale = MemberService::getCurrentLanguage($uc->getUser()) ?: 'es';
                        $via = $item['via'] ?? '';
                        if (Util::startsWith($via, 'ATM')) {
                            $messageInfo = [
                                'title' =>  $locale === 'es' ? 'Notificación' : 'Notification',
                                'message' => ($locale === 'es' ? 'Usted tiene una nueva transacción ATM con una cantidad de ' : 'You have a new ATM transaction for the amount of ') . $item['amount'],
                            ];
                        } else if (Util::startsWith($via, 'POS')) {
                            $messageInfo = [
                                'title' => $locale === 'es' ? 'Notificación' : 'Notification',
                                'message' => ($locale === 'es' ? 'Usted tiene un nuevo consumo de la cantidad de ' : 'You have a new purchase, the amount is ') . $item['amount'],
                            ];
                        }
                        if ($messageInfo) {
                            MemberService::sentFirebase($uc->getUser(), $messageInfo, true);
                        }
                    }
                }

                $tranCode = $item['add_or_deduct'] ?? null;
                $tranCodeMap = [
                    'add' => 'CREDIT',
                    'deduct' => 'DEBIT',
                ];
                $tranCode = $tranCodeMap[$tranCode ?? ''] ?? $tranCode;

                $tcComment = Util::title($item['message'] ?? '');
                if (in_array($tcComment, [
                    'Authorization', 'Financial Authorization'
                ])) {
                    $tcComment = '';
                }

                $ctResult = Util::title($item['card_transaction_result'] ?? '');
                if ($ctResult === 'Approved') {
                    $ctResult = '';
                }

                $ctDesc = $item['description'] ?? '';
                if ($ctDesc === 'ach transactions') {
                    $ctDesc = 'ACH ' . Util::title($tranCode);
                }

                $fields = [
                    $tcComment,
                    $ctDesc,
                    $item['card_transaction_merchant_description'] ?? '',
                    $item['card_transaction_merchant_city'] ?? '',
                    $item['card_transaction_merchant_province'] ?? '',
                    $item['card_transaction_merchant_country'] ?? '',
                    $item['card_transaction_terminal_pos_type'] ?? '',
                    $item['card_transaction_merchant_mcc'] ?? '',
                    $item['card_transaction_decline_reason'] ?? '',
                    $item['via'] ?? '',
                    $ctResult,
                ];

                $sourceType = $item['transaction_source_type'] ?? null;
                if ($sourceType === 'card_transaction') {
                    $fields[] = $item['mode'] ?? '';
                    $cardTxnIds[] = $txnId;
                } else if ($sourceType === 'account_transfer') {
                    $fields = [
                        $item['description'] ?? '',
                        trim(($item['to_user_first_name'] ?? '' ) . ' ' . ($item['to_user_last_name'] ?? '')),
                        $item['transfer_comment'] ?? '',
                    ];
                } else if (in_array($sourceType, [
                    'ach_transactions',
                    'card_fee_transaction',
                    'external_transfer',
                    'load',
                    'program_load',
                    'withdraw',
                ])) {
                    $fields[] = $item['category'] ?? '';
                }

                $fields = array_values(array_unique(array_filter($fields)));

                $t->setTxnTime($txnTime)
                    ->setTranDesc(implode(' ~~ ', $fields))
                    ->setOutstandingAuths(implode(', ', $fields))
                    ->setTranCode($tranCode)
                    ->setActualTranCode($item['transaction_source_type'] ?? null)
                    ->setUserField2(Util::humanize($item['mode'] ?? $item['transaction_source_type'] ?? null))
                    ->setTxnAmount(Money::normalizeAmount($item['amount'], 'USD'))
                    ->setMerchant(self::findMerchantByTransaction($item, $platform));

                $status = 'Executed';
                if (!empty($item['card_transaction_type']) && $item['card_transaction_type'] === 'DECLINED') {
                    $status = 'Declined';
                    if (empty($item['amount'])) {
                        $amount = $item['card_transaction_amount'] ?? 0;
                        if (in_array($tranCode, ['deduct', 'DEBIT'])) {
                            $amount = -1 * abs($amount);
                        }
                        $t->setTxnAmount(Money::normalizeAmount($amount, 'USD'));
                    }
                }
                $t->setStatus($status);

                if (in_array($tranCode, ['add', 'CREDIT"'])) {
                    $t->setOverdraftProtected('Credit')
                        ->setUserField1('credit');
                } else if (in_array($tranCode, ['deduct', 'DEBIT'])) {
                    $t->setOverdraftProtected('Debit')
                        ->setUserField1('debit');
                } else {
                    $t->setOverdraftProtected(Util::title($tranCode))
                        ->setUserField1(strtolower($tranCode));
                }

                $meta = Util::meta($t) ?? [];
                self::appendTransactionChangeHistory($meta, $item);

                $meta['botmData'] = $item;
                if (empty($meta['botmBarcode'])) {
                    $meta['botmBarcode'] = $uc->getAccountNumber();
                }
                $t->setMeta(Util::j2s($meta));
                $em->persist($t);

                // update user card active time
                $uc->setActivityTime($now);
            }
            $em->flush();
            if ($page > 1000) { // Avoid infinite loop
                break;
            }
            if ($countStatus < $pageSize * 0.9) {
                break;
            }
        } while (true);
        $em->flush();

        /** @var UserCardTransaction $ne */
        foreach ($newEntities as $ne) {
            $neId = $ne->getId();
            if ($neId) {
                $news[] = $neId;
            }
        }

        if ($inputUc->getId()) {
            self::removeOutdatedCardTransaction($inputUc, $startAt, $endAt, $cardTxnIds);
        }

        return $total;
    }

    protected static function appendTransactionChangeHistory(array &$meta, array $item)
    {
        $old = $meta['botmData'] ?? null;
        if (!$old) {
            return;
        }
        $diff = Util::calculateArrayDiff($old, $item, [
            'current_balance',
            'current_balance_cents',
            'pre_balance',
            'new_balance',
            'icon_asset_url',
        ]);
        if (!$diff) {
            return;
        }
        if (!array_key_exists('botmDataHistory', $meta)) {
            $meta['botmDataHistory'] = [];
        }
        $when = time();
        $meta['botmDataHistory'][$when] = $diff;
    }

    protected static function removeOutdatedCardTransaction(UserCard $uc, Carbon $start, Carbon $end, array $includes)
    {
        $expr = Util::expr();
        $deleted = Util::em()->createQueryBuilder()
            ->delete(UserCardTransaction::class, 'uct')
            ->where('uct.userCard = :uc')
            ->andWhere($expr->notIn('uct.tranId', ':txn_ids'))
            ->andWhere($expr->like('uct.tranId', ':tran_id_like'))
            ->andWhere('uct.txnTime >= :start_time')
            ->andWhere('uct.txnTime <= :end_time')
            ->andWhere('uct.actualTranCode = :actual_tran_code')
            ->setParameter('uc', $uc)
            ->setParameter('txn_ids', $includes)
            ->setParameter('tran_id_like', 'botm_%')
            ->setParameter('start_time', $start)
            ->setParameter('end_time', $end)
            ->setParameter('actual_tran_code', 'card_transaction')
            ->getQuery()
            ->execute();
        Log::debug('Delete ' . $deleted . ' outdated BOTM card transactions during ' .
                   $start->format('c') . ' and ' . $end->format('c') .
                   ' for user ' . Util::id($uc->getUser()));
    }

    /**
     * @param $accountId
     *
     * @return User|null
     */
    public static function findUserByAccountId($accountId)
    {
        $rs = Util::em()->getRepository(User::class)
            ->createQueryBuilder('u')
            ->join('u.config', 'c')
            ->where('c.botmUserAccountId = :accountId')
            ->setParameter('accountId', $accountId)
            ->getQuery()
            ->getResult();
        return $rs ? $rs[0] : null;
    }

    public static function findMerchantByTransaction(array $detail, Platform $platform)
    {
        if (empty($detail['card_transaction_merchant_id'])) {
            return null;
        }
        $rs = Util::em()->getRepository(Merchant::class)
            ->findBy([
                'merchantId' => $detail['card_transaction_merchant_id'],
                'platform' => $platform,
            ]);
        if ($rs) {
            return $rs[0];
        }

        if (empty($detail['card_transaction_merchant_mcc'])) {
            return null;
        }

        // Create the address entity
        $address = new Address();
        if (!empty($detail['card_transaction_merchant_country'])) {
            $address->setCountry(Country::findByCode($detail['card_transaction_merchant_country']));
        }
        if (!empty($detail['card_transaction_merchant_province']) && $address->getCountry()) {
            $state = State::findByAbbrOrName($detail['card_transaction_merchant_province'], $address->getCountry());
            $address->setState($state);
        }
        $address->setCity($detail['card_transaction_merchant_city'] ?? null);
        Util::em()->persist($address);

        // Generate the name & location
        $name = implode(', ', array_filter([
            $detail['card_transaction_merchant_description'],
            $detail['card_transaction_merchant_city'] ?? '',
            $detail['card_transaction_merchant_province'] ?? '',
            $detail['card_transaction_merchant_country'] ?? '',
        ], function ($v) {
            return $v;
        }));

        // Create the merchant entity
        $entity = new Merchant();
        $entity->setMerchantType(MerchantType::find($detail['card_transaction_merchant_mcc']))
            ->setMerchantId($detail['card_transaction_merchant_id'])
            ->setMerchantNameAndLocation($name)
            ->setMerchantCustName($detail['card_transaction_merchant_description'])
            ->setPlatform($platform)
            ->setAddress($address)
        ;
        Util::updateMeta($entity, $detail, false);
        Util::em()->persist($entity);
        return $entity;
    }

    public static function getTransactionStatus ($status) {
      $statusArray = ['approved', 'complete', 'completed', 'executed'];
      if (in_array(strtolower($status), $statusArray)) {
        return 'Executed';
      }
      return $status;
    }

    public static function updateConsumerInfo(User $user)
    {
        $uc = $user->getCurrentPlatformCard();
        if ( ! $uc) {
            return;
        }
        $api = BotmAPI::getForUserCard($uc);
        if ( ! $api->getBotmUserId($user)) {
            return;
        }
        try {
            try {
                ExternalInvoke::host($api->updateUser($user));
            } catch (\Throwable $t) {
                if (BotmAPI::isEmailUndeliverableError($t->getMessage())) {
                    ExternalInvoke::host($api->updateUser($user, true));
                } else {
                    throw $t;
                }
            }
        } catch (\Throwable $e) {
            Log::error('Failed to update the BOTM user info for ' . $user->getId() . ': ' . $e->getMessage());
        }
    }

    public static function validateBarcodeNumber($number, $allowClosed = false): string|array
    {
        $uc = new UserCard();
        $uc->setAccountNumber($number);
        $api = BotmAPI::getForUserCardAccountNumber($uc, $number);
        /** @var ExternalInvoke $ei */
        [$ei, $cardData] = ExternalInvoke::mute(function () use ($api, $uc) {
            return $api->retrieveCard($uc);
        });
        if ($ei->isFailed()) {
            return $ei->getError() ?? 'Unknown card validation error';
        }
        if ( ! $allowClosed) {
            $status = $cardData['card_status'] ?? '';
            if ($status === BotmAPI::CARD_STATUS_CLOSED) {
                return 'The card had been closed!';
            }
        }
        return $cardData;
    }

    public static function sendEmailToBotmSupport(string $subject, string $body, $otherCc = [], $otherTo = [])
    {
        if (Util::isLive()) {
            $to = array_merge([
                '<EMAIL>',
            ], $otherTo);
            $cc = [
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
            ];
            if ($otherCc === 'TransferMex' && !str_contains($subject, 'Transfer to') && !str_contains($subject, 'settlement to')) {
                $otherCc = NotificationEmails::findByPlatform(Platform::transferMex());
            }
            if (is_array($otherCc) && $otherCc) {
                $cc = array_merge($cc, $otherCc);
            }
        } else {
            $to = '<EMAIL>';
            $cc = [
                '<EMAIL>',
                '<EMAIL>',
            ];
        }

        Email::sendWithTemplate($to, Email::TEMPLATE_SIMPLE_LAYOUT, [
            'name' => 'BOTM Support Team',
            'subject' => $subject,
            'body' => $body,
            '_cc' => $cc,
        ], null, CardProgram::transferMexUSD());
    }

    public static function decryptCachedPan(UserCard $uc): string
    {
        $an = $uc->getAccountNumber();
        if ( ! $an) {
            return '';
        }
        $cacheKey = 'botmPan_' . $an;
        $pan = null;
        if ($uc->getId()) {
            $cached = Util::meta($uc, $cacheKey);
            if ($cached) {
                $pan = SSLEncryptionService::cachedDecryptShort($cached);
            }
            if ($pan) {
              return $pan;
            }
            Log::debug('SSL Encrypt the pan failed.');
        }
        $api = BotmAPI::getForUserCard($uc);
        $data = ExternalInvoke::host($api->retrieveCardPan($uc));
        $pan = $data['pan'];
        if ($pan && $uc->getId()) {
            Util::removeMetaWithPrefix($uc, 'botmPan_', false);
            Util::updateMeta($uc, [
                $cacheKey => SSLEncryptionService::encrypt($pan),
            ]);
        }
        return $pan ?? '';
    }

    private static function cacheRoutingDepositNumber(UserCard $uc, User $user, string $accountId = null)
    {
        if ( ! $accountId) {
            throw PortalException::temp('The user has not enrolled the card yet!');
        }
        $api = BotmAPI::getForUserCard($uc);
        $data = ExternalInvoke::host($api->retrieveUserAccount($user));
        Util::removeMetaWithPrefix($uc, 'botmRoutingNumber_', false);
        Util::removeMetaWithPrefix($uc, 'botmDepositNumber_', false);
        Util::updateMeta($uc, [
            'botmRoutingNumber_' . $accountId => Security::rsaEncrypt($data['routing_number']),
            'botmDepositNumber_' . $accountId => SSLEncryptionService::encrypt($data['internal_deposit_account']),
        ]);
        return $data;
    }

    public static function decryptCachedRoutingDepositNumbers(UserCard $uc): array
    {
        if (!$uc->getId()) {
            return ['', ''];
        }
        $user = $uc->getUser();
        $api = BotmAPI::getForUserCard($uc);
        $accountId = $api->getBotmUserAccountId($user);
        if (!$accountId) {
            return ['', ''];
        }
        $routing = '';
        $deposit = '';
        $cached = Util::meta($uc, 'botmRoutingNumber_' . $accountId);
        if ($cached) {
            $decrypted = Security::rsaDecrypt($cached);
            if (is_string($decrypted)) {
                $routing = $decrypted;
            }
        }
        $cached = Util::meta($uc, 'botmDepositNumber_' . $accountId);
        if ($cached) {
            $decrypted = SSLEncryptionService::cachedDecryptShort($cached);
            if (is_string($decrypted)) {
                $deposit = $decrypted;
            }
        }
        if (!$routing || !$deposit) {
            $data = self::cacheRoutingDepositNumber($uc, $user, $accountId);
            $routing = $data['routing_number'];
            $deposit = $data['internal_deposit_account'];
        }
        return [$routing, $deposit];
    }

    public static function decryptCachedRoutingNumber(UserCard $uc): string
    {
        if (!$uc->getId()) {
            return '';
        }
        $user = $uc->getUser();
        $api = BotmAPI::getForUserCard($uc);
        $accountId = $api->getBotmUserAccountId($user);
        if (!$accountId) {
            return '';
        }
        $cached = Util::meta($uc, 'botmRoutingNumber_' . $accountId);
        if ($cached) {
            $decrypted = Security::rsaDecrypt($cached);
            if (is_string($decrypted)) {
                return $decrypted;
            }
        }
        $data = self::cacheRoutingDepositNumber($uc, $user, $accountId);
        return $data['routing_number'];
    }

    public static function decryptCachedDepositNumber(UserCard $uc): string
    {
        if (!$uc->getId()) {
            return '';
        }
        $user = $uc->getUser();
        $api = BotmAPI::getForUserCard($uc);
        $accountId = $api->getBotmUserAccountId($user);
        if (!$accountId) {
            return '';
        }
        $cached = Util::meta($uc, 'botmDepositNumber_' . $accountId);
        if ($cached) {
            $decrypted = SSLEncryptionService::cachedDecryptShort($cached);
            if (is_string($decrypted)) {
                return $decrypted;
            }
        }
        $data = self::cacheRoutingDepositNumber($uc, $user, $accountId);
        return $data['internal_deposit_account'];
    }

    public static function retrieveBotmUserAccount(User $user)
    {
        $accountId = $user->ensureConfig()->getBotmUserAccountId();
        if (!$accountId) {
            return null;
        }
        $uc = $user->getCurrentPlatformCard();
        $api = BotmAPI::getForUserCard($uc);
        return ExternalInvoke::host($api->retrieveUserAccountById($accountId));
    }

    public static function isBotmUserAccountActive(User $user)
    {
        $data = self::retrieveBotmUserAccount($user);
        if (!$data) {
            return false;
        }
        $active = $data['active'] ?? 0;
        return $active === 1;
    }

    public static function searchUserByEmail(string $email, User $verifyUser = null)
    {
        $api = new BotmAPI();
        $all = ExternalInvoke::host($api->listUsers([
            'email' => $email,
            'pageSize' => 100,
        ]));
        if (!$all) {
            return null;
        }
        $u = null;
        if ($verifyUser) {
            $fn = Util::matchable($verifyUser->getFirstName());
            $ln = Util::matchable($verifyUser->getLastName());
            foreach ($all as $item) {
                $_fn = Util::matchable($item['first_name'] ?? '');
                $_ln = Util::matchable($item['last_name'] ?? '');
                if ($fn === $_fn && $ln === $_ln) {
                    $u = $item;
                    break;
                }
            }
        } else {
            $u = $all[0];
        }
        if (!$u || !is_array($u)) {
            return null;
        }
        $u['user_id'] = $u['id'];
        return $u;
    }

    public static function searchAndFillUser(User $user)
    {
        $config = $user->ensureConfig();
        $fetched = false;
        if ( ! $config->getBotmUserId()) {
            $email = $user->getEmail();
            $result = self::searchUserByEmail($email, $user);
            if (!$result) {
                $email = BotmAPI::getDummyEmailOfUser($user);
                $result = self::searchUserByEmail($email, $user);
            }
            $uid = $result['user_id'] ?? null;
            if ($uid) {
                $config->setBotmUserId($uid);
                if (!empty($result['user_account_ids'][0])) {
                    $config->replaceBotmUserAccountId($result['user_account_ids'][0]);
                    $fetched = true;
                }
                $config->persist();
            }
        }
        if (!$fetched && $config->getBotmUserId() && !$config->getBotmUserAccountId()) {
            $api = new BotmAPI();
            $result = ExternalInvoke::host($api->retrieveUserRaw($config->getBotmUserId()));
            if (!empty($result['user_account_ids'][0])) {
                $config->replaceBotmUserAccountId($result['user_account_ids'][0])
                    ->persist();
            }
        }
        return $config->getBotmUserId();
    }

    public static function matchBotmName(User $user, array $data)
    {
        $fn = Util::matchable($user->getFirstName());
        $ln = Util::matchable($user->getLastName());
        $_fn = Util::matchable($data['first_name'] ?? '');
        $_ln = Util::matchable($data['last_name'] ?? '');
        return $fn === $_fn && $ln === $_ln;
    }

    public static function getBotmUserSignature($id)
    {
        if (!$id) {
            return '';
        }
        $api = new BotmAPI();
        $data = ExternalInvoke::host($api->retrieveUserRaw($id));
        return $data['id'] . ', ' . $data['first_name'] . ' ' . $data['last_name'] . ', ' . $data['email'];
    }

    public static function getBotmKycStatus(User $user)
    {
        $config = $user->ensureConfig();
        if ( ! $config->getBotmUserId()) {
            return 'Not created';
        }
        $uc = $user->getCurrentPlatformCard();
        if ( ! $uc) {
            return 'Not created';
        }
        $api = BotmAPI::getForUserCard($uc);
        $botmUser = ExternalInvoke::host($api->retrieveUser($user));
        return Util::title($botmUser['kyc_status'] ?? 'Unknown');
    }

    public static function getBotmKycInfo(User $user)
    {
        $config = $user->ensureConfig();
        if ( ! $config->getBotmUserId()) {
            return false;
        }
        $uc = $user->getCurrentPlatformCard();
        if ( ! $uc) {
            return false;
        }
        $api = BotmAPI::getForUserCard($uc);
        $botmUser = ExternalInvoke::host($api->retrieveUser($user));
        return [
                'status' => Util::title($botmUser['kyc_status'] ?? 'Unknown'),
                'approval_date' => isset($botmUser['kyc_approval_date']) ? $botmUser['kyc_approval_date'] : null
              ];
    }

    public static function isBotmKycAccepted(User $user, string $status = null)
    {
        $status = $status ?? self::getBotmKycStatus($user);
        return in_array($status, [
            'Accepted',
            'Approved',
            'Approval',
            'Manual Approval',
            'Manually Accepted',
            'Manually Approved',
            'Accepted Manually',
        ]);
    }

    public static function isProxyAssigned(string $proxy)
    {
        if (!BotmAPI::isAccountNumberValid($proxy)) {
            return null;
        }
        $api = new BotmAPI();
        $cr = ExternalInvoke::host($api->retrieveCardRaw($proxy));
        return !empty($cr['user_account_id']);
    }

    public static function sendEmailToBotmKYCSupport(User $user, $eiId)
    {
        $ei = ExternalInvoke::find($eiId);
        if (!$ei) {
            Log::debug('Unknown external invoke ID when sending BOTM KYC review request.');
            return;
        }

        $cacheKey = 'botm_kyc_review_request_' . $user->getId();
        if (Data::has($cacheKey)) {
            Log::debug('Skip sending BOTM KYC review request for ' . $user->getId());
            return;
        }
        // add kyc exception

        $kycException = BotmKycExceptions::findByMember($user->getId());
        if (!$kycException ) {
          $kycException = new BotmKycExceptions();
          $kycException->setEmployee($user)
                         ->setStatus('Pending')
                         ->setEmployer($user->getPrimaryGroupAdmin() ?? null)
                         ->persist();
        }

        Data::set($cacheKey, date('c'), true, 3600 * 12);

        /** @var ExternalInvoke $ei */
        $data = $ei->getRequest(true);

        $link = null;
        $config = $user->ensureConfig();
        $backup = BotmAPI::getUserDataForBotm($user);
        if ($config && $config->getBotmUserId()) {
            $link = 'https://admin.tern.to/detail.html?entity=Users&id=' . $config->getBotmUserId();
            $api = BotmAPI::getForUserCard($user->getCurrentPlatformCard());
            $botmUser = ExternalInvoke::host($api->retrieveUser($user));
            if ($botmUser) {
                $backup = array_merge($backup, $botmUser);
            }
        }

        $notifyLink = Util::host() . '/mex/notify/kyc-approved/' . $user->getId();
        $spanLink = Util::host() . '/admin/mex/member/' . $user->getId() . '/view-ofac-details';

        $params = [
            'First Name' => $data['first_name'] ?? $backup['first_name'],
            'Last Name' => $data['last_name'] ?? $backup['last_name'],
            'Email' => $data['email'] ?? $backup['email'],
            'DOB' => $data['dob'] ?? $backup['dob'],
            'Mobile Phone' => $data['mobile_phone'] ?? $backup['mobile_phone'],
            'Country' => $data['country'] ?? $backup['country'],
            'Province' => $data['province'] ?? $backup['province'],
            'City' => $data['city'] ?? $backup['city'],
            'Postal Code' => $data['postal_code'] ?? $backup['postal_code'],
            'Address 1' => $data['address_1'] ?? $backup['address_1'],
            'Address 2' => $data['address_2'] ?? $backup['address_2'],
            'Government ID' => Util::maskPan($data['government_id'] ?? $backup['government_id']),
            'Business ID' => $data['business_id'] ?? $backup['business_id'],
            'External ID' => $user->getId(),
        ];

        $subjectSuffix = $params['First Name'] . ' ' . $params['Last Name'];
        if ($config && $config->getBotmUserId()) {
            $subjectSuffix = '#' . $config->getBotmUserId() . ' ' . $subjectSuffix;
        }

        $subject = 'KYC Manual Review Request -- ' . $subjectSuffix;
        $body = Util::render('@Faas/BOTM/kyc_audit.html.twig', compact(
            'user', 'data', 'link', 'params', 'notifyLink', 'spanLink'
        ));
        $cc = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
        ];
        $otherTo = [
            '<EMAIL>',
            '<EMAIL>',
        ];
        if (Util::isStaging()) {
            $cc = [];
            $otherTo = [];
        }
        self::sendEmailToBotmSupport($subject, $body, $cc, $otherTo);
    }
}
