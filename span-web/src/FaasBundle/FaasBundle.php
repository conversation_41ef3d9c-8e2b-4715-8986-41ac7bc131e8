<?php

namespace FaasBundle;

use CoreBundle\Entity\Module;
use CoreBundle\Entity\Role;
use CoreBundle\Utils\Util;
use SalexUserBundle\Entity\User;
use Symfony\Component\HttpKernel\Bundle\Bundle;
use TransferMexBundle\TransferMexBundle;
use CoreBundle\Utils\Log;

class FaasBundle extends Bundle
{
    public static function getApiPrefix()
    {
        return '/api/faas/';
    }

    public static function getEnabledAPIs()
    {
        return [
            '/api/provinces',
        ];
    }

    public static function getDisabledAPIs()
    {
        return [
            '/api/faas/kyc/ofac',
        ];
    }

    public static function getUserDashboardType()
    {
        return 'faas';
    }

    public static function apiAdminUserProfile($data)
    {
        $data['cpKey'] = 'cp_faas';
        $data['adminLayout'] = 'h';
        return $data;
    }

    public static function getMemberRoles()
    {
        return [
            Role::ROLE_FAAS_MEMBER,
        ];
    }

    public static function getAdminRoles()
    {
        return [
            Role::ROLE_FAAS_ADMIN,
            Role::ROLE_FAAS_AGENT,
            Role::ROLE_FAAS_CLIENT,
            Role::ROLE_FAAS_CLIENT_ADMIN,
        ];
    }

    public static function isCorsAllowed($host)
    {
        $request = Util::request();
        $uri = $request->getRequestUri();
        if (Util::startsWith($uri, '/faas/widget/')) {
            return true;
        }
        return false;
    }

	public static function isConsumerRoleAllowed()
	{
		$platform = Util::platform();
		if ($platform && $platform->isFaasPlatforms()) {
			return true;
		}
		return false;
	}

    protected static function getMenusForCurrentClientUser()
    {
        return [
            [
                'id' => Module::ID_DASHBOARD,
                'name' => 'Dashboard',
                'route' => '',
                'mdRoute' => 'faas/base/client/dashboard',
                'icon' => 'fa fa-fw fa-dashboard',
                'mdIcon' => 'mdi-view-dashboard',
                'svgIcon' => 'dashboard',
                'children' => [],
            ],
            [
                'id' => Module::ID_FAAS_CLIENT_MEMBER,
                'name' => 'Members',
                'route' => '',
                'mdRoute' => 'faas/base/client/member',
                'icon' => 'fa fa-fw fa-dashboard',
                'mdIcon' => 'mdi-view-dashboard',
                'svgIcon' => 'mex_employees',
                'children' => [],
            ],
            [
                'id' => Module::ID_FAAS_CLIENT_PAYMENTS,
                'name' => 'Deposits & Payouts',
                'route' => '',
                'mdRoute' => 'faas/base/client/payouts',
                'icon' => 'fa fa-fw fa-dashboard',
                'mdIcon' => 'mdi-view-dashboard',
                'svgIcon' => 'mex_payment',
                'children' => [],
            ],
            [
                'id' => Module::ID_REPORTS,
                'name' => 'Reports',
                'route' => '',
                'mdRoute' => '',
                'icon' => 'fa fa-fw fa-file-text',
                'mdIcon' => 'mdi-file-chart-outline font-25',
                'children' => [
                    [
                        'id' => Module::ID_MEX_REPORT_REVENUE,
                        'name' => 'Daily Client Activity Report',
                        'route' => '',
                        'mdRoute' => 'faas/base/client/reports/daily',
                    ]
                ],
            ]
        ];
    }

    protected static function getMenusForCurrentClientAdminUser()
    {
        return [
            [
                'id' => Module::ID_DASHBOARD,
                'name' => 'Dashboard',
                'route' => '',
                'mdRoute' => 'faas/base/client/dashboard',
                'icon' => 'fa fa-fw fa-dashboard',
                'mdIcon' => 'mdi-view-dashboard',
                'svgIcon' => 'dashboard',
                'children' => [],
            ],
            [
                'id' => Module::ID_MEX_EMPLOYER_AGENT,
                'name' => 'Admins',
                'route' => '',
                'mdRoute' => 'faas/base/client/admins',
                'icon' => 'fa fa-fw fa-users',
                'mdIcon' => 'mdi-account-multiple',
                'svgIcon' => 'mex_employee_agent',
                'children' => [],
            ],
            [
                'id' => Module::ID_FAAS_CLIENT_MEMBER,
                'name' => 'Members',
                'route' => '',
                'mdRoute' => 'faas/base/client/member',
                'icon' => 'fa fa-fw fa-dashboard',
                'mdIcon' => 'mdi-view-dashboard',
                'svgIcon' => 'mex_employees',
                'children' => [],
            ],
            [
                'id' => Module::ID_FAAS_CLIENT_PAYMENTS,
                'name' => 'Deposits & Payouts',
                'route' => '',
                'mdRoute' => 'faas/base/client/payouts',
                'icon' => 'fa fa-fw fa-dashboard',
                'mdIcon' => 'mdi-view-dashboard',
                'svgIcon' => 'mex_payment',
                'children' => [],
            ],
            [
                'id' => Module::ID_REPORTS,
                'name' => 'Reports',
                'route' => '',
                'mdRoute' => '',
                'icon' => 'fa fa-fw fa-file-text',
                'mdIcon' => 'mdi-file-chart-outline font-25',
                'children' => [
                    [
                        'id' => Module::ID_MEX_REPORT_REVENUE,
                        'name' => 'Daily Client Activity Report',
                        'route' => '',
                        'mdRoute' => 'faas/base/client/reports/daily',
                    ]
                ],
            ]
        ];
    }

    protected static function getMenusForCurrentMemberUser()
    {
        return [
            [
                'id' => Module::ID_DASHBOARD,
                'name' => 'Dashboard',
                'route' => '',
                'mdRoute' => 'faas/base/member/dashboard',
                'icon' => 'fa fa-fw fa-dashboard',
                'mdIcon' => 'mdi-view-dashboard',
                'svgIcon' => 'dashboard',
                'children' => [],
            ],
            [
                'id' => Module::ID_FAAS_MEMBER_TRANSACTIONS,
                'name' => 'Transactions',
                'route' => '',
                'mdRoute' => 'faas/base/member/transactions',
                'icon' => '',
                'mdIcon' => 'mdi-bank-transfer',
                'svgIcon' => 'bank_transfer',
                'children' => [],
            ],
            [
                'id' => Module::ID_FAAS_MEMBER_SETTINGS,
                'name' => 'Settings',
                'route' => '',
                'mdRoute' => 'faas/base/member/settings',
                'icon' => '',
                'mdIcon' => 'mdi-settings',
                'svgIcon' => 'settings',
                'children' => [],
            ]
        ];
    }

    public static function getMenusForCurrentUser()
    {
    	 $platform = Util::platform();
        $user = Util::user();
        $masterAdmin = $user->isMasterAdmin();
        $admin = $user->inTeam(Role::ROLE_FAAS_ADMIN);
        $developer = $user->inTeam(Role::ROLE_API_INVOKER);
        $agent = $user->inTeam(Role::ROLE_FAAS_AGENT);
        $portal = $platform->isPortalsEnabled();
        $api = $platform->isApiEnabled();

        if ($portal && $user->inTeam(Role::ROLE_FAAS_CLIENT)) {
                return self::getMenusForCurrentClientAdminUser();
        }

        if ($portal && $user->inTeam(Role::ROLE_FAAS_CLIENT_ADMIN)) {
            return self::getMenusForCurrentClientUser();
        }


        if ($portal && $user->inTeam(Role::ROLE_FAAS_MEMBER)) {
            return self::getMenusForCurrentMemberUser();
        }

        if (!$masterAdmin && !$admin && !$agent && !$developer) {
            return [];
        }

        $all = [];
        if ($masterAdmin || $portal) {
            if ($masterAdmin || $admin || $agent) {
                $all[] = [
                    'id'  => Module::ID_DASHBOARD,
                    'name' => 'Dashboard',
                    'route' => '',
                    'mdRoute' => 'faas/base/dashboard',
                    'icon' => '',
                    'mdIcon' => '',
                    'svgIcon' => true,
                    'children' => [],
                ];
            }

            if ($masterAdmin || $admin) {
                $all[] = [
                  'id'  => Module::ID_FAAS_PROGRAM,
                  'name' => 'Program',
                  'route' => '',
                  'mdRoute' => 'faas/base/program',
                  'icon' => '',
                  'mdIcon' => '',
                  'svgIcon' => true,
                  'children' => [],
              ];
                $all[] = [
                    'id'  => Module::ID_FAAS_AGENT,
                    'name' => 'Agents',
                    'route' => '',
                    'mdRoute' => 'faas/base/agents',
                    'icon' => '',
                    'mdIcon' => '',
                    'svgIcon' => true,
                    'children' => [],
                ];
            }
            if ($masterAdmin || $admin || $agent) {
              if ($platform->isClientsRequired()) {
                  $all[] = [
                      'id'       => Module::ID_FAAS_CLIENT,
                      'name'     => 'Clients',
                      'route'    => '',
                      'mdRoute'  => 'faas/base/clients',
                      'icon'     => '',
                      'mdIcon'   => '',
                      'svgIcon'  => TRUE,
                      'children' => [],
                  ];
              }
              if ($platform->isIdScanRequired()) {
                $all[] = [
                    'id'  => Module::ID_FAAS_USERS,
                    'name' => 'Members',
                    'route' => '',
                    'mdRoute' => '',
                    'icon' => '',
                    'mdIcon' => '',
                    'svgIcon' => 'faas_users',
                    'children' => [
                        [
                            'id' => Module::ID_FAAS_USERS,
                            'name' => 'Members',
                            'route' => '',
                            'mdRoute' => 'faas/base/members'
                        ],
                        [
                            'id' => Module::ID_FAAS_USERS,
                            'name' => 'KYC Management',
                            'route' => '',
                            'mdRoute' => 'faas/base/kyc-management'
                        ]
                    ],
                ];
              } else {
                $all[] = [
                  'id'  => Module::ID_FAAS_USERS,
                  'name' => 'Members',
                  'route' => '',
                  'mdRoute' => 'faas/base/members',
                  'icon' => '',
                  'mdIcon' => '',
                  'svgIcon' => 'faas_users',
                  'children' => [
                  ],
                ];
              }
            }
        }

        if ($masterAdmin || ($api && $admin) || ($api && $developer)) {
            $developers =  [
                [
                    'id' => Module::ID_DEV_DOCUMENTATION,
                    'name' => 'Documentation',
                    'route' => '/developer',
                    'mdRoute' => ''
                ],
                [
                    'id' => Module::ID_DEVELOPER_RESOURCES,
                    'name' => 'API Config',
                    'route' => '/admin/developer',
                    'mdRoute' => ''
                ],
            ];
            if ($masterAdmin && !Util::isLive()) {
                $developers[] = [
                    'id' => 'developer_widgets',
                    'name' => 'Widgets Preview',
                    'route' => '',
                    'mdRoute' => 'faas/base/widgets',
                ];
            }
            $all[] = [
                'id' => Module::ID_DEVELOPER,
                'name' => 'Developer Tools',
                'route' => '',
                'icon' => 'fa fa-fw fa-code',
                'mdIcon' => 'mdi-code-tags',
                'svgIcon' => false,
                'children' => $developers,
            ];
        }

		if ($masterAdmin && !Util::isLive()) {
            $all[] = [
                'id'       => Module::ID_FAAS_SETTING,
                'name'     => 'Settings',
                'route'    => '',
                'mdRoute'  => 'faas/base/settings',
                'icon'     => '',
                'mdIcon'   => '',
                'svgIcon'  => TRUE,
                'children' => [],
            ];
		}

		return $all;
    }

    public static function getApiPermissionTree($default)
    {
        return [
            'Basic data' => [
                'Get Provinces' => 'api_basic_data_states',
            ],
            'Users' => [
                'Create User' => 'api_faas_create_user_profile',
                'Update User' => 'api_faas_update_user_profile',
                'Get User Profile' => 'api_faas_get_user_profile',
                'Get User Widget Token' => 'api_faas_get_user_widget_key'
            ],
            'Cards' => [
                'Create' => 'api_faas_card_create',
                'Enroll' => 'api_faas_card_enroll',
                'Activate' => 'api_faas_card_activate',
                'Load amount' => 'api_faas_card_load_amount',
                'Reverse funds' => 'api_faas_card_reverse_funds',
                'Get account balance' => 'api_faas_card_account_balance',
                'Get consumer details' => 'api_faas_card_consumer_details',
                'Get card details' => 'api_faas_card_card_details',
                'Get agent balance' => 'api_faas_card_agent_balance',
                'Get consumer transactions' => 'api_faas_card_consumer_transactions',
                'Get spending breakdown' => 'api_faas_card_spending_breakdown',
                'Get PIN' => 'api_faas_card_pin',
                'Update status' => 'api_faas_card_update_status',
                'Replace card' => 'api_faas_card_replace_card',
                'Assign card' => 'api_faas_card_assign_card',
                'Change PIN' => 'api_faas_card_change_pin',
            ],
            'KYC' => [
                'OFAC' => 'api_faas_kyc_ofac',
                'ID Scan' => 'api_faas_kyc_id_scan',
            ],
            'Commom' => [
              'Get Clients List' => 'api_faas_get_clients_list'
          ],
        ];
    }
}
