<?php

namespace FaasBundle\Controller\Generic\Member;

use ApiBundle\Services\ApiRunner;
use CoreBundle\Entity\ExternalInvoke;
use CoreBundle\Entity\Role;
use CoreBundle\Exception\FailedException;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Util;
use SalexUserBundle\Entity\User;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use TransferMexBundle\Controller\Mobile\UserControllerTrait;
use TransferMexBundle\Services\RapidAPI;

class MemberSettingController extends BaseController
{
	use UserControllerTrait;

	public function __construct()
	{
		parent::__construct();
		$this->authRoles($this->getAccessibleRoles());
	}

	protected function getAccessibleRoles()
	{
		return [
			Role::ROLE_FAAS_MEMBER
		];
	}

	/**
	 * @Route("/admin/faas/base/member/profile")
	 * @param Request $request
	 *
	 * @return SuccessResponse
	 * @throws FailedException
	 */
	public function profile(Request $request)
	{
		$data = $this->traitGetUserProfile($this->user);

		$cardDetails = [];
		$uc = $this->user->getCurrentPlatformCard();
		if ($uc->isIssued()) {
			$cardDetails = ApiRunner::run('/faas/card/card-details', 'GET', [
				'accountNumber' => $uc->getAccountNumber(),
			], true);
		}

		$data['cardHistory'] = $cardDetails;
		return new SuccessResponse($data);
	}

	/**
	 * @Route("/admin/faas/base/member/profile/update", methods={"POST"})
	 *
	 * @param Request $request
	 *
	 * @return FailedResponse|SuccessResponse
	 */
	public function update(Request $request)
	{
		return $this->traitUpdate($request, $this->user);
	}

	/**
	 * @Route("/admin/faas/base/member/close")
	 * @param Request $request
	 * @param User    $user
	 *
	 * @return SuccessResponse
	 */
	public function close(Request $request)
	{
		$this->user->setStatus(User::STATUS_CLOSED, 'The member closed his account')
			->persist();
		return new SuccessResponse();
	}

	/**
	 * @Route("/admin/faas/base/member/set2FA", methods={"POST"})
	 * @param Request $request
	 *
	 * @return FailedResponse|SuccessResponse
	 */
	public function set2FA(Request $request)
	{
		$meta = Util::s2j($this->user->getMeta());
		$meta['twoFAEnabled'] = $request->get('enable') ?? false;
		$this->user->setMeta(Util::j2s($meta))->persist();
		return new SuccessResponse();
	}

	// /**
	//  * @Route("/admin/faas/base/member/replace-card", methods={"POST"})
	//  */
	// public function replaceCard(Request $request)
	// {
	// 	return ApiRunner::run('/faas/card/replace-card', 'POST', [
	// 		'userId' => $this->user->getId()
	// 	]);
	// }
}
