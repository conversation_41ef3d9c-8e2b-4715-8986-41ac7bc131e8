<?php


namespace FaasBundle\Controller\API;

use ApiBundle\Services\ApiRunner;
use Carbon\Carbon;
use CoreBundle\Entity\ExternalInvoke;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserCardTransaction;
use CoreBundle\Entity\UserGroup;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Util;
use CoreBundle\Utils\Data;
use PortalBundle\Util\RegisterStep;
use OpenApi\Annotations as SWG;
use Symfony\Component\Routing\Annotation\Route;
use TransferMexBundle\Entity\EmployerPayout;
use TransferMexBundle\Services\RapidAPI;
use UsUnlockedBundle\Services\SpendingService;

class CardController extends BaseController
{
    public function syncCardBalanceAndStatus(UserCard $uc)
    {
        $msg = '';
        try {
            $processor = $uc->getProcessor();
            $processor->getApiService()::updateBalanceAndStatus($uc);
        } catch (\Exception $ex) {
            $errorId = Log::exception('Failed to sync the card balance and status from FaaS API.', $ex, [
                'uc' => $uc->getId(),
            ]);
            $msg .= ' Failed to sync the card balance and status. Error trace ID: ' . $errorId;
        }
        return $msg;
    }

    /**
     * @Route("/api/faas/card/create", methods={"POST"}, name="api_faas_card_create")
     * @SWG\Post(
     *   tags={"Cards"},
     *   summary="Create card",
     *   description="Create a card for the user initially",
     *   security={{"x-signature":{}, "x-salt":{}, "x-timestamp":{}, "x-access-key":{}}},
     *   @SWG\RequestBody(required=true, @SWG\MediaType(mediaType="application/x-www-form-urlencoded", schema=@SWG\Schema(required={"userId"}, properties={
     *     @SWG\Property(property="userId", description="User Id", type="integer"),
     *   }))),
     *   @SWG\Response(response=200, ref="#/components/schemas/FaasUserCardResponse"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     */
    public function create()
    {
        $request = $this->validateParameters(__METHOD__);

        $user = $this->validateUser($request->get('userId'), Role::ROLE_FAAS_MEMBER);
        $uc = $user->getOneCardInPlatform();

        if ($uc->getAccountNumber()) {
            return new SuccessResponse($uc->toApiArrayForFaaS());
            // return new FailedResponse('This member already has card assigned!', $uc->toApiArrayForFaaS());
        }

        $processor = $uc->getProcessor();

        Data::set('create_uc_' . $uc->getId(). '_by', $request->get('byId'));

        $processor->getApiService()::createCard($uc);

        RegisterStep::updateUser($user);

        return new SuccessResponse($uc->toApiArrayForFaaS());
    }

    /**
     * @Route("/api/faas/card/enroll", methods={"POST"}, name="api_faas_card_enroll")
     * @SWG\Post(
     *   tags={"Cards"},
     *   summary="Enroll",
     *   description="Enroll and activate the account. The response card status will be active if enrolled and activated successfully. Otherwise, error message will be returned.",
     *   security={{"x-signature":{}, "x-salt":{}, "x-timestamp":{}, "x-access-key":{}}},
     *   @SWG\RequestBody(required=true, @SWG\MediaType(mediaType="application/x-www-form-urlencoded", schema=@SWG\Schema(required={"userId", "accountNumber"}, properties={
     *     @SWG\Property(property="userId", description="User Id", type="integer"),
     *     @SWG\Property(property="accountNumber", description="Processor's account(barcode) number, not card number", type="string"),
     *   }))),
     *   security={{"x-signature":{}, "x-salt":{}, "x-timestamp":{}, "x-access-key":{}}},
     *   @SWG\Response(response=200, ref="#/components/schemas/FaasUserCardResponse"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     */
    public function enroll()
    {
        $request = $this->validateParameters(__METHOD__);

        $user = $this->validateUser($request->get('userId'), Role::ROLE_FAAS_MEMBER);
        $uc = $user->getOneCardInPlatform();

        $an = $request->get('accountNumber');
        $uc->setAccountNumber($an);

        $processor = $uc->getProcessor();
        $processor->getApiService()::enroll($uc);

        return new SuccessResponse($uc->toApiArrayForFaaS());
    }

    /**
     * @Route("/api/faas/card/activate", methods={"POST"}, name="api_faas_card_activate")
     * @SWG\Post(
     *   tags={"Cards"},
     *   summary="Activate",
     *   description="Activate the account if it's inactive. The account needs to be enrolled first. The enroll API usually activate the card directly.",
     *   security={{"x-signature":{}, "x-salt":{}, "x-timestamp":{}, "x-access-key":{}}},
     *   @SWG\RequestBody(required=true, @SWG\MediaType(mediaType="application/x-www-form-urlencoded", schema=@SWG\Schema(required={"accountNumber"}, properties={
     *     @SWG\Property(property="accountNumber", description="Processor's account(barcode) number, not card number", type="string"),
     *   }))),
     *   @SWG\Response(response=200, ref="#/components/schemas/FaasUserCardResponse"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     */
    public function activate()
    {
        $this->validateParameters(__METHOD__);
        $uc = $this->validateAccountNumber();

        $processor = $uc->getProcessor();
        $api = $processor->getApiImplForUserCard($uc);
        /** @var ExternalInvoke $ei */
        [$ei, ] = $api->activate($uc);
        if ($ei && $ei->isFailed()) {
            return new FailedResponse($ei->getError());
        }

        $processor->getApiService()::updateBalanceAndStatus($uc);

        return new SuccessResponse($uc->toApiArrayForFaaS());
    }

    /**
     * @Route("/api/faas/card/load-amount", methods={"POST"}, name="api_faas_card_load_amount")
     * @SWG\Post(
     *   tags={"Cards"},
     *   summary="Load amount",
     *   description="Load specific amount to the user's card",
     *   security={{"x-signature":{}, "x-salt":{}, "x-timestamp":{}, "x-access-key":{}}},
     *   @SWG\RequestBody(required=true, @SWG\MediaType(mediaType="application/x-www-form-urlencoded", schema=@SWG\Schema(required={"accountNumber", "amount", "description"}, properties={
     *     @SWG\Property(property="accountNumber", description="Processor's account(barcode) number, not card number", type="string"),
     *     @SWG\Property(property="amount", description="USD cents to load to this account", type="integer", minimum=1),
     *     @SWG\Property(property="description", description="Short description to the loading", type="string", maxLength=50),
     *   }))),
     *   @SWG\Response(response=200, ref="#/components/schemas/FaasUserCardResponse"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     */
    public function loadAmount()
    {
        $request = $this->validateParameters(__METHOD__);
        $uc = $this->validateAccountNumber();
        $amount = (int)$request->get('amount');
        $description = $request->get('description');

        $processor = $uc->getProcessor();
        $api = $processor->getApiImplForUserCard($uc);
        /** @var ExternalInvoke $ei */
        [$ei, ] = $api->loadAmount($uc, $amount, $description);
        if ($ei && $ei->isFailed()) {
            return new FailedResponse($ei->getError());
        }

        $msg = 'The load amount operation succeeded!'
               . $this->syncCardBalanceAndStatus($uc);
        return new SuccessResponse($uc->toApiArrayForFaaS(), $msg);
    }

    /**
     * @Route("/api/faas/card/reverse-funds", methods={"POST"}, name="api_faas_card_reverse_funds")
     * @SWG\Post(
     *   tags={"Cards"},
     *   summary="Reverse funds",
     *   description="Reverse/unload specific amount from the account",
     *   security={{"x-signature":{}, "x-salt":{}, "x-timestamp":{}, "x-access-key":{}}},
     *   @SWG\RequestBody(required=true, @SWG\MediaType(mediaType="application/x-www-form-urlencoded", schema=@SWG\Schema(required={"accountNumber", "amount", "description"}, properties={
     *     @SWG\Property(property="accountNumber", description="Processor's account(barcode) number, not card number", type="string"),
     *     @SWG\Property(property="amount", description="USD cents to reverse from this account", type="integer", minimum=1),
     *     @SWG\Property(property="description", description="Short description to the reversing", type="string", maxLength=50),
     *   }))),
     *   @SWG\Response(response=200, ref="#/components/schemas/FaasUserCardResponse"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     */
    public function reverseFunds()
    {
        $request = $this->validateParameters(__METHOD__);
        $uc = $this->validateAccountNumber();
        $amount = (int)$request->get('amount');
        $description = $request->get('description');

        $processor = $uc->getProcessor();
        $api = $processor->getApiImplForUserCard($uc);
        /** @var ExternalInvoke $ei */
        [$ei, ] = $api->reverseFunds($uc, $amount, $description);
        if ($ei && $ei->isFailed()) {
            return new FailedResponse($ei->getError());
        }

        $msg = 'The reverse funds operation succeeded!'
               . $this->syncCardBalanceAndStatus($uc);
        return new SuccessResponse($uc->toApiArrayForFaaS(), $msg);
    }

    /**
     * @Route("/api/faas/card/account-balance", methods={"GET"}, name="api_faas_card_account_balance")
     * @SWG\Get(
     *   tags={"Cards"},
     *   summary="Get account balance",
     *   description="Retrieve the accurate account balance",
     *   security={{"x-signature":{}, "x-salt":{}, "x-timestamp":{}, "x-access-key":{}}},
     *   @SWG\Parameter(name="accountNumber", description="Processor's account(barcode) number, not card number",
     *                                        in="query", schema=@SWG\Schema(type="string"), required=true),
     *   @SWG\Response(response=200, ref="#/components/schemas/FaasUserCardBalanceResponse"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     */
    public function accountBalance()
    {
        $this->validateParameters(__METHOD__);
        $uc = $this->validateAccountNumber();

        $processor = $uc->getProcessor();
        $api = $processor->getApiImplForUserCard($uc);
        $balance = $api->getBalance($uc);
        return new SuccessResponse($balance);
    }

    /**
     * @Route("/api/faas/card/consumer-details", methods={"GET"}, name="api_faas_card_consumer_details")
     * @SWG\Get(
     *   tags={"Cards"},
     *   summary="Get consumer details",
     *   description="Retrieve the consumer details",
     *   security={{"x-signature":{}, "x-salt":{}, "x-timestamp":{}, "x-access-key":{}}},
     *   @SWG\Parameter(name="accountNumber", description="Processor's account(barcode) number, not card number",
     *                                        in="query", schema=@SWG\Schema(type="string"), required=true),
     *   @SWG\Response(response=200, ref="#/components/schemas/FaasUserCardConsumerDetailsResponse"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     */
    public function consumerDetails()
    {
        $this->validateParameters(__METHOD__);
        $uc = $this->validateAccountNumber();

        $processor = $uc->getProcessor();
        $api = $processor->getApiImplForUserCard($uc);
        $details = $api->getConsumerDetailsData($uc);
        return new SuccessResponse($details);
    }

    /**
     * @Route("/api/faas/card/card-details", methods={"GET"}, name="api_faas_card_card_details")
     * @SWG\Get(
     *   tags={"Cards"},
     *   summary="Get card details",
     *   description="Retrieve the card details",
     *   security={{"x-signature":{}, "x-salt":{}, "x-timestamp":{}, "x-access-key":{}}},
     *   @SWG\Parameter(name="accountNumber", description="Processor's account(barcode) number, not card number",
     *                                        in="query", schema=@SWG\Schema(type="string"), required=true),
     *   @SWG\Response(response=200, ref="#/components/schemas/FaasUserCardCardDetailsResponse"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     */
    public function cardDetails()
    {
        $this->validateParameters(__METHOD__);
        $uc = $this->validateAccountNumber();

        $processor = $uc->getProcessor();
        $api = $processor->getApiImplForUserCard($uc);
        $details = $api->getCardDetailsData($uc);
        return new SuccessResponse($details);
    }

    /**
     * @Route("/api/faas/card/agent-balance", methods={"GET"}, name="api_faas_card_agent_balance")
     * @SWG\Get(
     *   tags={"Cards"},
     *   summary="Get agent balance",
     *   description="Retrieve the processor's agent available balance. Pass clientId or accountNumber to query the balance of the specific client if it's enabled, otherwise, query the balance of the entire platform.",
     *   security={{"x-signature":{}, "x-salt":{}, "x-timestamp":{}, "x-access-key":{}}},
     *   @SWG\Parameter(name="clientId", description="Client ID if it's enabled in the platform.",
     *                                        in="query", schema=@SWG\Schema(type="string")),
     *   @SWG\Parameter(name="accountNumber", description="Processor's account(barcode) number, not card number",
     *                                        in="query", schema=@SWG\Schema(type="string")),
     *   @SWG\Response(response=200, ref="#/components/schemas/FaasUserCardBalanceResponse"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     */
    public function agentBalance()
    {
        $request = $this->validateParameters(__METHOD__);
        $processor = $this->cardProgram->getProcessor();

        if ($request->get('clientId')) {
            $client = $this->validateClientId($request);
            $api = $processor->getApiImplForUserGroup($client->getAdminGroup());
        } else if ($request->get('accountNumber')) {
            $uc = $this->validateAccountNumber();
            $api = $processor->getApiImplForUserCard($uc);
        } else {
            $api = $processor->getApiImpl();
        }

        $balance = $api->getAgentAvailableBalance();
        return new SuccessResponse($balance);
    }

    /**
     * @Route("/api/faas/card/consumer-transactions", methods={"GET"}, name="api_faas_card_consumer_transactions")
     * @SWG\Get(
     *   tags={"Cards"},
     *   summary="Get consumer transactions",
     *   description="Retrieve the consumer transactions",
     *   security={{"x-signature":{}, "x-salt":{}, "x-timestamp":{}, "x-access-key":{}}},
     *   @SWG\Parameter(name="accountNumber", description="Processor's account(barcode) number, not card number",
     *                                        in="query", schema=@SWG\Schema(type="string"), required=true),
     *   @SWG\Parameter(name="fromDate", description="Start date filter. Format: YYYY-MM-DD", in="query",
     *                                   schema=@SWG\Schema(type="string", format="date"), required=true),
     *   @SWG\Parameter(name="toDate", description="End date filter. Format: YYYY-MM-DD", in="query", schema=@SWG\Schema(type="string", format="date"),
     *                                 required=true),
     *   @SWG\Parameter(name="force", description="The system will call processor's API to retrive the realtime transactions data when you pass 1 to this parameter, but this will be slow. The retrived data from processor in previous calls to this API will be cached, so you can pass `0` (which is the default behavior) to query the cached data only.",
     *                                in="query", schema=@SWG\Schema(type="integer", default=0)),
     *   @SWG\Response(response=200, ref="#/components/schemas/FaasUserCardTransactionsResponse"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     */
    public function consumerTransactions()
    {
        $request = $this->validateParameters(__METHOD__);
        $uc = $this->validateAccountNumber();

        $force = (int)$request->get('force') === 1;
        $maxDays = 200;
        if (!$force && $this->isInternalRequest($request)) {
            $maxDays = null;
        }
        [$fromDate, $toDate] = $this->validateTimeRange('fromDate', 'toDate', $maxDays);

        if ($force) {
            Util::executeCommand('span:mex:update-transactions', [
                '--uc' => $uc->getId(),
                '--from-date' => $fromDate->format(Util::DATE_FORMAT),
                '--to-date' => $toDate->format(Util::DATE_FORMAT),
            ]);
        }

        $rs = $this->em->getRepository(UserCardTransaction::class)
            ->createQueryBuilder('uct')
            ->where('uct.userCard = :uc')
            ->andWhere('uct.txnTime >= :fromDate')
            ->andWhere('uct.txnTime < :toDate')
            ->setParameter('uc', $uc)
            ->setParameter('fromDate', Util::toUTC($fromDate))
            ->setParameter('toDate', Util::toUTC($toDate->addDay()))
            ->getQuery()
            ->getResult()
        ;
        $result = [];
        /** @var UserCardTransaction $r */
        foreach ($rs as $r) {
            $result[] = $r->toApiArrayForFaas();
        }

        return new SuccessResponse($result);
    }

    /**
     * @Route("/api/faas/card/spending-breakdown", methods={"GET"}, name="api_faas_card_spending_breakdown")
     * @SWG\Get(
     *   tags={"Cards"},
     *   summary="Get spending breakdown",
     *   description="Get the spending breakdown for all the transactions with merchant info",
     *   security={{"x-signature":{}, "x-salt":{}, "x-timestamp":{}, "x-access-key":{}}},
     *   @SWG\Parameter(name="accountNumber", description="Processor's account(barcode) number, not card number",
     *                                        in="query", schema=@SWG\Schema(type="string"), required=true),
     *   @SWG\Parameter(name="fromDate", description="Start date filter. Format: YYYY-MM-DD", in="query",
     *                                  schema=@SWG\Schema(type="string", format="date"), required=true),
     *   @SWG\Parameter(name="toDate", description="End date filter. Format: YYYY-MM-DD", in="query", schema=@SWG\Schema(type="string", format="date"),
     *                                 required=true),
     *   @SWG\Response(response=200, ref="#/components/schemas/FaasUserCardSpendingBreakdownResponse"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     */
    public function spendingBreakdown()
    {
        $this->validateParameters(__METHOD__);
        $uc = $this->validateAccountNumber();
        [$fromDate, $toDate] = $this->validateTimeRange('fromDate', 'toDate');

        $rs = $this->em->getRepository(UserCardTransaction::class)
            ->createQueryBuilder('uct')
            ->where('uct.userCard = :uc')
            ->andWhere('uct.merchant is not null')
            ->andWhere('uct.tranCode = :tranCode')
            ->andWhere('uct.txnTime >= :fromDate')
            ->andWhere('uct.txnTime < :toDate')
            ->setParameter('uc', $uc)
            ->setParameter('tranCode', 'DEBIT')
            ->setParameter('fromDate', Util::toUTC($fromDate))
            ->setParameter('toDate', Util::toUTC($toDate->addDay()))
            ->getQuery()
            ->getResult()
        ;
        $result = SpendingService::calculate($rs);
        return new SuccessResponse($result);
    }

    /**
     * @Route("/api/faas/card/update-status", methods={"POST"}, name="api_faas_card_update_status")
     * @SWG\Post(
     *   tags={"Cards"},
     *   summary="Update status",
     *   description="Update card's status",
     *   security={{"x-signature":{}, "x-salt":{}, "x-timestamp":{}, "x-access-key":{}}},
     *   @SWG\RequestBody(required=true, @SWG\MediaType(mediaType="application/x-www-form-urlencoded", schema=@SWG\Schema(required={"accountNumber", "lastFourDigits", "status"}, properties={
     *     @SWG\Property(property="accountNumber", description="Processor's account(barcode) number, not card number", type="string"),
     *     @SWG\Property(property="lastFourDigits", description="Last 4 digits of Card Number", type="string"),
     *     @SWG\Property(property="status", description="The new status", type="string", enum={"Active", "OnHold", "Lost", "Suspended", "Block", "Stolen", "SuspectedFraud", "Damaged"}),
     *   }))),
     *   @SWG\Response(response=200, ref="#/components/schemas/FaasUserCardResponse"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     */
    public function updateStatus()
    {
        $request = $this->validateParameters(__METHOD__);
        $uc = $this->validateAccountNumber();

        $status = $request->get('status');
        $lastFourDigits = $request->get('lastFourDigits');

        $processor = $uc->getProcessor();
        $api = $processor->getApiImplForUserCard($uc);
        /** @var ExternalInvoke $ei */
        [$ei, ] = $api->updateStatus($uc, $status, $lastFourDigits);
        if ($ei && $ei->isFailed()) {
            return new FailedResponse($ei->getError());
        }

        $user = $uc->getUser();
        $user->addNote('Changed the card (ends with ' . $lastFourDigits .')\'s status to `' . $status . '`.',
            true, ApiRunner::getNoteById($user));


        $msg = 'The card status has been updated!'
               . $this->syncCardBalanceAndStatus($uc);
        return new SuccessResponse($uc->toApiArrayForFaaS(), $msg);
    }

    /**
     * @Route("/api/faas/card/replace-card", methods={"POST"}, name="api_faas_card_replace_card")
     * @SWG\Post(
     *   tags={"Cards"},
     *   summary="Replace card",
     *   description="Create a new account & card for the user. The old account & card will be suspended. The balance on the old card will be transferred to the new card.",
     *   security={{"x-signature":{}, "x-salt":{}, "x-timestamp":{}, "x-access-key":{}}},
     *   @SWG\RequestBody(required=true, @SWG\MediaType(mediaType="application/x-www-form-urlencoded", schema=@SWG\Schema(required={"userId"}, properties={
     *     @SWG\Property(property="userId", description="User Id", type="integer"),
     *   }))),
     *   @SWG\Response(response=200, ref="#/components/schemas/FaasUserCardResponse"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     */
    public function replaceCard()
    {
        $request = $this->validateParameters(__METHOD__);
        $user = $this->validateUser($request->get('userId'), Role::ROLE_FAAS_MEMBER);
        $uc = $user->getCurrentPlatformCard();
        if (!$uc || !$uc->getAccountNumber() || !$uc->isIssued()) {
            return new FailedResponse('The old account is not created or enrolled yet!');
        }

        $processor = $uc->getProcessor();
        $api = $processor->getApiImplForUserCard($uc);
        $service = $processor->getApiService();
        $user = $uc->getUser();
        $oldAccountNumber = $uc->getAccountNumber();
        $me = $request->get('byId') ?? ApiRunner::getNoteById($user);

        /** @var ExternalInvoke $ei */
        [$ei] = $api->updateStatus($uc, RapidAPI::ACCOUNT_STATUS_SUSPENDED, null, true);
        if ($ei && $ei->isFailed()) {
            return new FailedResponse('Failed to suspend the old account: ' . $ei->getError());
        }
        $user->addNote('Suspended the old account ' . $oldAccountNumber . ' when replacing card', true, $me);

        // Unload the balance
        $balance = $api->getBalance($uc);
        if ($balance > 0) {
            [$ei] = $api->reverseFunds($uc, $balance, 'Replace account');
            if ($ei && $ei->isFailed()) {
                return new FailedResponse('Failed to unload the balance from the old account: ' . $ei->getError());
            }
            $user->addNote('Unloaded ' . Money::formatUSD($balance) . ' from the old account ' . $oldAccountNumber
                           . ' when replacing card', TRUE, $me);
        } else {
            $user->addNote('Skip migrating the balance ' . Money::formatUSD($balance) . ' from the old account '
                           . $oldAccountNumber . ' when replacing card', true, $me);
        }

        // The real replace card operation
        Data::set('create_uc_' . $uc->getId(). '_by', $me);
        $service::createCard($uc);

        // Load the balance to the new account
        if ($balance > 0) {
            [$ei] = $api->loadAmount($uc, $balance, 'Replace account from ' . $oldAccountNumber);
            if ($ei && $ei->isFailed()) {
                // add new record for graph https://app.asana.com/0/****************/****************/f
                  $client = $user->getPrimaryGroupAdmin();
                  $userGroup = $user->getPrimaryGroup();
                  $employerFundingType = ($userGroup ? $userGroup->getFundingType() : null) ?? UserGroup::FUNDING_TYPE_ACH;
                  if ($client && $employerFundingType === UserGroup::FUNDING_TYPE_PREFUNDED) {
                    $clientUc = $client->getCurrentPlatformCard();
                    $newRecord = new UserCardTransaction();
                    $newRecord->setUserCard($clientUc)
                              ->setTxnAmount($balance)
                              ->setTxnTime(Carbon::now())
                              ->setTranCode('Pay_employee')
                              ->setProductName(UserCardTransaction::PRODUCT_TRANSFER_MEX)
                              ->setStatus(EmployerPayout::PAYOUT_COMPLETE)
                              ->setTranDesc('Member:' . $user->getId() . ';Change account from ' . $oldAccountNumber)
                              ->persist();
                  }
                return new FailedResponse('Failed to load the balance to the new account: ' . $ei->getError());
            }
            $user->addNote('Loaded ' . Money::formatUSD($balance) . ' to the new account ' . $uc->getAccountNumber()
                           . ' when replacing card', true, $me);
        }


        $msg = 'The replace card operation succeeded!'
               . $this->syncCardBalanceAndStatus($uc);
        return new SuccessResponse($uc->toApiArrayForFaaS(), $msg);
    }

    /**
     * @Route("/api/faas/card/assign-card", methods={"POST"}, name="api_faas_card_assign_card")
     * @SWG\Post(
     *   tags={"Cards"},
     *   summary="Assign card",
     *   description="Assign a new card to an account. The balance on the old card will be moved to the new card automatically.",
     *   security={{"x-signature":{}, "x-salt":{}, "x-timestamp":{}, "x-access-key":{}}},
     *   @SWG\RequestBody(required=true, @SWG\MediaType(mediaType="application/x-www-form-urlencoded", schema=@SWG\Schema(required={"accountNumber", "cardNumber"}, properties={
     *     @SWG\Property(property="accountNumber", description="Processor's account(barcode) number, not card number. Don't assign a new account number to the same member, just use the old account number.", type="string"),
     *     @SWG\Property(property="cardNumber", description="Full Card Number of the new card", type="string"),
     *   }))),
     *   @SWG\Response(response=200, ref="#/components/schemas/FaasUserCardResponse"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     */
    public function assignCard()
    {
        $request = $this->validateParameters(__METHOD__);
        $cardNumber = $request->get('cardNumber');
        $uc = $this->validateAccountNumber();

        if (!$uc->isIssued()) {
            return new FailedResponse('The account is not activated/enrolled yet!');
        }

        $processor = $uc->getProcessor();
        $api = $processor->getApiImplForUserCard($uc);
        /** @var ExternalInvoke $ei */
        [$ei, ] = $api->assignCard($uc, $cardNumber);
        if ($ei && $ei->isFailed()) {
            return new FailedResponse($ei->getError());
        }

        $uc->setIssued(false)
            ->setIssuedAt(null)
            ->setStatus(UserCard::STATUS_INACTIVE, false, false)
            ->persist();

        $uc->saveMaskedCardNumber($cardNumber);

        $user = $uc->getUser();
        $user->addNote('Changed the full card number to ' . Util::maskPan($cardNumber),
            TRUE, $this->user->getId());

        RegisterStep::updateUser($user);

        $msg = 'The assign card operation succeeded!'
               . $this->syncCardBalanceAndStatus($uc);
        return new SuccessResponse($uc->toApiArrayForFaaS(), $msg);
    }

    /**
     * @Route("/api/faas/card/pin", methods={"GET"}, name="api_faas_card_pin")
     * @SWG\Get(
     *   tags={"Cards"},
     *   summary="Get PIN",
     *   description="Get the PIN of the card. For security reason, it's not suggested to use this API. You can reset the PIN if you do not know it.",
     *   security={{"x-signature":{}, "x-salt":{}, "x-timestamp":{}, "x-access-key":{}}},
     *   @SWG\Parameter(name="accountNumber", description="Processor's account(barcode) number, not card number",
     *                                        in="query", schema=@SWG\Schema(type="string"), required=true),
     *   @SWG\Response(response=200, ref="#/components/schemas/StringResponse"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     */
    public function getPin()
    {
        $this->validateParameters(__METHOD__);
        $uc = $this->validateAccountNumber();
        return new SuccessResponse($uc->getUpdatedOrApiPIN());
    }

    /**
     * @Route("/api/faas/card/change-pin", methods={"POST"}, name="api_faas_card_change_pin")
     * @SWG\Post(
     *   tags={"Cards"},
     *   summary="Change PIN",
     *   description="Change the PIN of the card",
     *   security={{"x-signature":{}, "x-salt":{}, "x-timestamp":{}, "x-access-key":{}}},
     *   @SWG\RequestBody(required=true, @SWG\MediaType(mediaType="application/x-www-form-urlencoded", schema=@SWG\Schema(required={"accountNumber", "cardNumber", "pin"}, properties={
     *     @SWG\Property(property="accountNumber", description="Processor's account(barcode) number, not card number", type="string"),
     *     @SWG\Property(property="cardNumber", description="Full Card Number", type="string"),
     *     @SWG\Property(property="pin", description="The new PIN", type="string"),
     *   }))),
     *   @SWG\Response(response=200, ref="#/components/schemas/FaasUserCardResponse"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     */
    public function changePin()
    {
        $request = $this->validateParameters(__METHOD__);
        $uc = $this->validateAccountNumber();

        $cardNumber = $request->get('cardNumber');
        $pin = $request->get('pin');

        $processor = $uc->getProcessor();
        $api = $processor->getApiImplForUserCard($uc);
        /** @var ExternalInvoke $ei */
        [$ei, ] = $api->changePin($uc, $pin, $cardNumber);
        if ($ei && $ei->isFailed()) {
            return new FailedResponse($ei->getError());
        }

        $user = $uc->getUser();
        $user->addNote('Updated the PIN to the card ' . Util::maskPan($cardNumber),
            true, ApiRunner::getNoteById($user));

        $uc->setUpdatedPIN($pin);
        return new SuccessResponse($uc->toApiArrayForFaaS());
    }
}
