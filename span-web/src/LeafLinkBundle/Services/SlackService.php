<?php


namespace LeafLinkBundle\Services;


use CoreBundle\Utils\Util;

class SlackService extends \UsUnlockedBundle\Services\SlackService
{
    /**
     * @return string
     */
    public static function getWebHookUrl()
    {
        // #fis_alerts
        $url = Util::getParameter('slack_webhook_ll');
        if (Util::isStaging() || Util::isDev()) {
            $url = Util::getParameter('slack_webhook_test');
        }
        return $url;
    }
}