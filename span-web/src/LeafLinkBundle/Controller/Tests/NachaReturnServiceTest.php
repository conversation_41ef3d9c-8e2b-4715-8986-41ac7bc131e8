<?php


namespace LeafLinkBundle\Controller\Tests;

use CoreBundle\Controller\Cron\NakedProtectedController;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Util;
use LeafLinkBundle\Services\NachaReturnService;
use LeafLinkBundle\Services\SlackService;
use phpseclib\Net\SFTP;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;


class NachaReturnServiceTest extends NakedProtectedController
{
    /** @var SFTP */
    public $sftp;

    // Login for sFTP server. Called in submitACHBatch().
    public function login()
    {
        $this->sftp = new SFTP(Util::getParameter('sl_ftp_host_2'), Util::getParameter('sl_ftp_port_2'), 90);
        if ($this->sftp === FALSE) {
            SlackService::alert('Leaflink FTP connection error!');
            return;
        }
        if (!$this->sftp->login(
            Util::getParameter('sl_ftp_username_2'),
            Util::getParameter('sl_ftp_password_2')
        )) {
            SlackService::alert('Failed to login the LeafLink ftp server (002)...', [
                'error' => $this->sftp->getLastSFTPError(),
            ]);
            return;
        }
    }

    /**
     * @Route("/t/cron/leaflink/test/nacha-return-test")
     * @param Request $request
     * @param $retry
     * @return Response
     * @throws \LogicException
     */
    public function testNachaReturnService(Request $request, $retry = false)
    {
        // Get list of all files in sFTP
        $this->login();
        $dir = "./";
        $files = $this->sftp->nlist($dir);

        // Process all files.
        foreach ($files as $file)
        {
            if (strpos($file, "nacha-return-test") === 0)
            {
                $path = $dir . $file;
                $dlFIle = $this->sftp->get($path);

                $result = NachaReturnService::getTraceNumsFromFile($dlFIle);

                SlackService::info('Nacha return file detected and processed.', [
                    'Result' => $result
                ]);

                return new SuccessResponse($result, 'Success');
            }
        }

        return new FailedResponse('Failure');
    }

    /**
     * @Route("/t/cron/leaflink/test/isStage")
     * @param Request $request
     * @param $retry
     * @return Response
     * @throws \LogicException
     */
    public function testUtilIsStage(Request $request, $retry = false)
    {
        SlackService::info('starting test', [
            'isStage' => Util::isStage()
        ]);
        return new SuccessResponse(Util::isStage());
    }

    /**
     * @Route("/t/cron/spendr/test/nacha-return-formatting-test")
     * @param Request $request
     * @param $retry
     * @return Response
     * @throws \LogicException
     */
    public function testNachaFormatService(Request $request, $retry = false)
    {
        // Get list of all files in sFTP
        $this->login();
        $dir = "./";
        $files = $this->sftp->nlist($dir);

        // Process all files.
        foreach ($files as $file)
        {
            if (strpos($file, "nacha-return-formatting-test") === 0)
            {
                $path = $dir . $file;
                $dlFIle = $this->sftp->get($path);

                $result = NachaReturnService::normalizeReturnFile($dlFIle);

                SlackService::info('Nacha return file detected and processed.', [
                    'Result' => $result
                ]);

                $this->sftp->put($dir . 'nacha-return-formatting-result.txt', $result);

                return new SuccessResponse($result, 'Success');
            }
        }

        return new FailedResponse('Failure');
    }

    /**
     * @Route("/t/cron/spendr/test/nacha-return-test")
     * @param Request $request
     * @param $retry
     * @return Response
     * @throws \LogicException
     */
    public function testSpendrReturnService(Request $request, $retry = false)
    {
        // Get list of all files in sFTP
        $this->login();
        $dir = "./";
        $files = $this->sftp->nlist($dir);

        // Process all files.
        foreach ($files as $file)
        {
            if (strpos($file, "nacha-return-formatting-test") === 0)
            {
                $path = $dir . $file;
                $dlFIle = $this->sftp->get($path);

                $result = NachaReturnService::normalizeReturnFile($dlFIle);

                SlackService::info('Nacha return file detected and processed.', [
                    'Result' => $result
                ]);

                $this->sftp->put($dir . 'nacha-return-formatting-result.txt', $result);

                $result = NachaReturnService::parseReturnFile($result);

                SlackService::info('Nacha return file detected and processed.', [
                    'Result' => $result
                ]);

                return new SuccessResponse($result, 'Success');
            }
        }

        return new FailedResponse('Failure');
    }
}
