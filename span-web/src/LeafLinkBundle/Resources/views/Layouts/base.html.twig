<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.4.1/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@5.12.1/css/all.min.css" integrity="sha256-mmgLkCYLUQbXn0B1SRqzHar6dCnv9oZFPEC1g1cwlkk=" crossorigin="anonymous">
    <link href="https://fonts.googleapis.com/css?family=Rubik:300,300i,400,500,500i,700,900&display=swap" rel="stylesheet">

    <link rel="stylesheet" href="/static/helper.css?v={{ _v() }}">
    <link rel="stylesheet" href="/static/leaflink/css/main.css?v={{ _v() }}">

    <title>{% block title %}{{ _cpp.name | default }}{% endblock %}</title>

    <link rel="icon" type="image/png" href="{{ _cp_icon | url }}" />
    <link rel="apple-touch-icon" href="{{ _cp_icon_touch | url }}" />

    {% block head %}{% endblock %}
</head>
<body>
{% block body %}{% endblock %}

<script src="https://cdn.jsdelivr.net/npm/jquery@3.4.1/dist/jquery.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.0/dist/umd/popper.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.4.1/dist/js/bootstrap.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert@2.1.2/dist/sweetalert.min.js"></script>
<script type='text/javascript' src='https://cdn.yodlee.com/fastlink/v3/initialize.js'></script>
{% if dev() %}
    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.11/dist/vue.js"></script>
{% else %}
    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.11/dist/vue.min.js"></script>
{% endif %}
<script src="https://cdn.jsdelivr.net/npm/vuex@3.1.2/dist/vuex.min.js"></script>
<script src="/static/leaflink/js/main.js?v={{ _v() }}"></script>

{% block foot %}{% endblock %}
</body>
</html>