<?php
/**
 * Created by PhpStorm.
 * User: hans
 * Date: 2017/4/1
 * Time: 下午2:22
 */

namespace CoreBundle\Utils;


use CoreBundle\Constant\GlobalCollectSupported;
use CoreBundle\Entity\Currency as CurrencyEntity;
use Money\Currencies\ISOCurrencies;
use Money\Currency;
use Money\Exception\UnknownCurrencyException;
use Money\Formatter\DecimalMoneyFormatter;
use Money\Formatter\IntlMoneyFormatter;
use Money\Money as MoneyLib;
use Money\Parser\IntlMoneyParser;
use PortalBundle\Exception\PortalException;
use Symfony\Polyfill\Intl\Icu\Exception\MethodArgumentValueNotImplementedException;

class Money
{
    public const FACTOR_NORMAL = 1;
    public const FACTOR_EXTRA = 1.02;

    public const CONVERTER_GLOBAL_COLLECT = 'GlobalCollect';
    public const CONVERTER_LOCAL = 'Local';

    protected static $exchange;
    protected static $converter;
    protected static $formatter;
    protected static $parser;
    protected static $decimalFormatter;

    /**
     * Get the currency number, for instance, 840 for USD
     *
     * @param string $code Currency Code
     * @return int
     */
    public static function getNum($code)
    {
        return (new ISOCurrencies())->numericCodeFor(new Currency($code));
    }

    public static function getMinorUnit($code)
    {
        return (new ISOCurrencies())->subunitFor(new Currency($code));
    }

    private static function getFormatter()
    {
        if (self::$formatter) {
            return self::$formatter;
        }
        $numberFormatter = null;
        try {
            $numberFormatter = new \NumberFormatter('en_US', \NumberFormatter::CURRENCY);
        } catch (MethodArgumentValueNotImplementedException $exception) {
            $numberFormatter = new \NumberFormatter('en', \NumberFormatter::CURRENCY);
        }
        self::$formatter = new IntlMoneyFormatter($numberFormatter, new ISOCurrencies());
        return self::$formatter;
    }

    private static function getParser()
    {
        if (self::$parser) {
            return self::$parser;
        }
        try {
            $numberFormatter = new \NumberFormatter('en_US', \NumberFormatter::CURRENCY);
        } catch (MethodArgumentValueNotImplementedException $exception) {
            $numberFormatter = new \NumberFormatter('en', \NumberFormatter::CURRENCY);
        }
        self::$parser = new IntlMoneyParser($numberFormatter, new ISOCurrencies());
        return self::$parser;
    }

    private static function getDecimalFormatter()
    {
        if (self::$decimalFormatter) {
            return self::$decimalFormatter;
        }
        self::$decimalFormatter = new DecimalMoneyFormatter(new ISOCurrencies());
        return self::$decimalFormatter;
    }

    public static function getDefaultConverterMethod()
    {
        return self::CONVERTER_LOCAL;
    }

    /**
     * Convert money amount from one currency to another (Will add 2% in the convert result by default)
     * 不同货币类型之间转换数值（应客户要求，默认结果增加2%）
     *
     * @param string|int|float $amount Always use minor currency units(exponent), such as Cent
     *                    始终传入最小单位的该货币数量，比如美分、人民币分，不同货币的最小单位不同，比如日元的最小单位就是日元
     * @param string $fromCurrency
     * @param string $toCurrency
     * @param bool $format
     * @param float|int $factor
     * @param string $converter
     * @return float|int
     */
    public static function convert($amount, $fromCurrency, $toCurrency, $format = false, $factor = self::FACTOR_NORMAL, $converter = null)
    {
        if (!$amount) {
            return 0;
        }

        if ($fromCurrency === $toCurrency) {
            if (!$format) {
                return $amount;
            }
            $newMoney = new MoneyLib((int)round($amount), new Currency($toCurrency));
        } else {
            if (!$converter) {
                $converter = self::getDefaultConverterMethod();
            }

            $method = 'convertBy' . $converter;
            // NOTE: We multiply 100 in all the methods to ensure the precise because of the next multiply 0.01 operation.
            /** @var MoneyLib $newMoney */
            $newMoney = self::$method($amount * 100, $fromCurrency, $toCurrency);

            // NOTE: We added 2% to the convert result.
            $_factor = $fromCurrency === $toCurrency ? 1 : $factor;
            $newMoney = $newMoney->multiply((string)(0.01 * $_factor));
        }

        if ($format) {
            return (float)self::getDecimalFormatter()->format($newMoney);
        }

        return (int)round((float)$newMoney->getAmount());
    }

    public static function convertWithExtra($amount, $fromCurrency, $toCurrency, $format = false, $converter = null)
    {
        return self::convert($amount, $fromCurrency, $toCurrency, $format, self::FACTOR_EXTRA, $converter);
    }

    public static function convertWithReversedExtra($amount, $fromCurrency, $toCurrency, $format = false)
    {
        return self::convert($amount, $fromCurrency, $toCurrency, $format,  1 / self::FACTOR_EXTRA);
    }

    public static function roundUpIfRequired($method, $amount, $currency)
    {
        if ($method !== GlobalCollectSupported::PAYNAME_WESTERNUNION) {
            return $amount;
        }
        $formatted = (int)ceil((float)self::formatAmount($amount, $currency, ''));
        return self::normalizeAmount($formatted, $currency);
    }

    public static function fixUnit ($amount, $fromCurrency, $toCurrency)
    {
        $from = new Currency($fromCurrency);
        $to = new Currency($toCurrency);
        $iso = new ISOCurrencies();
        try {
            $ratio = $iso->subunitFor($to) - $iso->subunitFor($from);
        } catch (UnknownCurrencyException $exception) {
            throw new PortalException($exception->getMessage());
        }
        return $amount * (10 ** $ratio);
    }

    public static function getConvertRateFromUSD($toCurrency)
    {
        if ($toCurrency === 'USD') {
            return 1;
        }
        $entity = CurrencyEntity::find($toCurrency);
        if (!$entity) {
            throw new PortalException('Unknown currency code: ' . $toCurrency);
        }
        $v = $entity->getOneUsd();
        // MIN: 1 USD = 0.30838 KWD
        // MAX: 1 USD = 89377 LBP
        if ($v === null || $v < 0.05 || $v > 200000) {
            throw new PortalException('Invalid currency rate from ' . $toCurrency . ': ' . $v);
        }
        return $v;
    }

    public static function getConvertRate($fromCurrency, $toCurrency)
    {
        $fromRate = self::getConvertRateFromUSD($fromCurrency);
        $toRate = self::getConvertRateFromUSD($toCurrency);
        return $toRate / $fromRate;
    }

    /**
     * @param $amount
     * @param $fromCurrency
     * @param $toCurrency
     * @return MoneyLib
     * @throws \Money\Exception\UnknownCurrencyException
     * @throws \InvalidArgumentException
     */
    private static function convertByLocal($amount, $fromCurrency, $toCurrency)
    {
        $from = Util::em()->getRepository(\CoreBundle\Entity\Currency::class)->findOneBy([
            'curCode' => $fromCurrency,
        ]);
        if (!$from) {
            throw new PortalException('Unknown currency ' . $fromCurrency . ' in local db!');
        }

        $to = Util::em()->getRepository(\CoreBundle\Entity\Currency::class)->findOneBy([
            'curCode' => $toCurrency,
        ]);
        if (!$to) {
            throw new PortalException('Unknown currency ' . $toCurrency . ' in local db!');
        }

        $value = $amount * $to->getOneUsd() / $from->getOneUsd();
        $value = self::fixUnit($value, $fromCurrency, $toCurrency);
        return new MoneyLib((int)round($value), new Currency($toCurrency));
    }

    /**
     * Get formatted money string, such as USD $3.52
     * 获取格式化过的金额（含货币类型），比如 USD $3.52
     *
     * @param string|int|float|MoneyLib $moneyLibObjectOrAmount Always use minor currency units(exponent), such as Cent
     *                    始终传入最小单位的该货币数量，比如美分、人民币分，不同货币的最小单位不同，比如日元的最小单位就是日元
     * @param string|null $currencyCode
     * @param bool $withCurrency
     * @param bool $fixGC
     * @return string
     */
    public static function format($moneyLibObjectOrAmount, $currencyCode = null, $withCurrency = true, $fixGC = false)
    {
        if (!($moneyLibObjectOrAmount instanceof MoneyLib)) {
            if (is_string($moneyLibObjectOrAmount)) {
                $moneyLibObjectOrAmount = (float)$moneyLibObjectOrAmount;
            }
            $moneyLibObjectOrAmount = new MoneyLib((int)round($moneyLibObjectOrAmount), new Currency($currencyCode));
        }

        // https://epayments-api.developer-ingenico.com/s2sapi/v1/en_US/java/payments/create.html#payments-create-payload
        // Amount in cents and always having 2 decimals
        if ($fixGC) {
            $_amount = $moneyLibObjectOrAmount->getAmount();
            $ratio = self::getMinorUnit($currencyCode) - 2;
            $_amount = (int)round($_amount * 10 ** $ratio);
            $moneyLibObjectOrAmount = new MoneyLib($_amount, new Currency($currencyCode));
        }

        $amount = self::getFormatter()->format($moneyLibObjectOrAmount);

        if ($withCurrency && !Util::startsWith($amount, $currencyCode)) {
            $amount = $currencyCode . ' ' . $amount;
        }
        return $amount;
    }

    public static function formatUSD($amount, $withCurrency = true)
    {
        return self::format($amount, 'USD', $withCurrency);
    }

    public static function usdFormat($amount)
    {
        return self::formatUSD($amount, false);
    }

    /**
     * Get formatted amount number, such as 3.52 in $3.52
     * 获取格式化过的金额数值，比如 $3.52 里面的 3.52
     *
     * @param int $amount Always use minor currency units(exponent), such as Cent
     *                    始终传入最小单位的该货币数量，比如美分、人民币分，不同货币的最小单位不同，比如日元的最小单位就是日元
     * @param string $currencyCode Currency code
     * @param string $thousands_sep
     * @return string
     */
    public static function formatAmount($amount, $currencyCode = 'USD', $thousands_sep = ',')
    {
        $minorUnit = self::getMinorUnit($currencyCode);
        $cent = $amount * (0.1 ** $minorUnit);
        return number_format(round($cent, 2), $minorUnit, '.', $thousands_sep);
    }

    public static function formatAmountToNumber($amount, $currencyCode = 'USD', $thousands_sep = '')
    {
        return (float)static::formatAmount($amount, $currencyCode, $thousands_sep);
    }

    /**
     * Get amount in minor currency units(exponent)
     * 获取金额对应的最小货币单位的数量
     *
     * @param float|int $amount The formatted currency values, such as the 3.52 in $3.52
     *                          格式化过的金额，单位是主货币（如美元），例如 $3.52 中的 3.52
     * @param $currencyCode
     * @return int
     */
    public static function normalizeAmount($amount, $currencyCode)
    {
        if (is_string($amount)) {
            $amount = (float)str_replace(',', '', $amount);
        }
        return (int)round($amount * (10 ** self::getMinorUnit($currencyCode)));
    }

    public static function normalizeAmountOrNull($amount, $currencyCode = 'USD')
    {
        if ($amount === null) {
            return null;
        }
        return static::normalizeAmount($amount, $currencyCode);
    }

    public static function normalizeAmountFromUsdFormat($format)
    {
        $format = str_replace([
            'USD ', '$', ',',
        ], '', trim($format));
        return self::normalizeAmount($format, 'USD');
    }

    /**
     * Get amount and formatted string for provided values with keys.
     *
     * @param string $fromCurrency
     * @param string $toCurrency
     * @param array $values ["key" => amount]
     * @param bool $withExtra
     * @return array
     */
    public static function convertWithText($fromCurrency, $toCurrency, array $values, $withExtra = false)
    {
        $data = [];
        $data['code'] = $toCurrency;

        foreach ($values as $key => $amount) {
            $data[$key] = self::convert($amount, $fromCurrency, $toCurrency, true,
                $withExtra ? self::FACTOR_EXTRA : self::FACTOR_NORMAL);
            $data[$key . '_text'] = self::format(self::normalizeAmount($data[$key], $toCurrency), $toCurrency);
        }

        return $data;
    }

    public static function formatToHtml($text)
    {
        $matches = [];
        preg_match('|(.*?)([\d., ]+)(.*?)|', $text, $matches);
        return '<span class="super">' . $matches[1] . '</span><span>'
               . $matches[2] . '</span><span class="super">'
               . $matches[3] . '</span>';
    }

    public static function parse($money, $currency = null)
    {
        return static::getParser()->parse($money, $currency);
    }

    public static function formatWhen($amount, $currency = 'USD', $withCurrency = false)
    {
        if ($amount === null) {
            return '';
        }
        return self::format($amount, $currency, $withCurrency);
    }
}
