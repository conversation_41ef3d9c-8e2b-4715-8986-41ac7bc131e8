<?php


namespace CoreBundle\Utils;


use Enqueue\Client\Producer;

class Queue
{
    /**
     * @param string|null $name
     *
     * @return Producer|object
     */
    protected static function producer(string $name = null)
    {
        $name = $name ?? 'default';
        return Util::$container->get('enqueue.client.' . $name . '.producer');
    }

    public static function send($topic, $message, $name = null)
    {
        if (!$name && in_array($topic, ['privacy', 'rapyd', 'coinflow', 'spendr', 'spendr_promo', 'spendr_bank_ledger'])) {
            $name = $topic;
        }
        $producer = self::producer($name);
        $producer->sendEvent($topic, $message);
    }

    public static function sendSpendr($topic, $message, $name = 'spendr')
    {
        self::send($topic, $message, $name);
    }

    public static function sendSpendrPromo($topic, $message, $name = 'spendr_promo')
    {
        self::send($topic, $message, $name);
    }

	public static function sendSpendrBankLedger($topic, $message, $name = 'spendr_bank_ledger')
    {
        self::send($topic, $message, $name);
    }

    public static function updatePrivacyCard($ucId)
    {
        self::send('privacy', [
            'type' => 'updateCard',
            'ucId' => $ucId,
        ], 'privacy');
    }

    public static function spendrPayTransaction($txnToken, $appId, $timezone)
	{
		self::sendSpendr(
			'spendrTransaction',
			[
				'type' => 'payment',
				'txnToken' => $txnToken,
				'appId' => $appId,
				'timezone' => $timezone
			]
		);
	}

	public static function spendrRetrictAreaPayTransaction($txnToken, $loadId, $appId, $timezone)
	{
		self::sendSpendr(
			'spendrTransaction',
			[
				'type' => 'restrictAreaPayment',
				'txnToken' => $txnToken,
				'loadId' => $loadId,
				'appId' => $appId,
				'timezone' => $timezone
			]
		);
	}

    public static function spendrUpdateTransaction($txnToken, $action)
	{
		self::sendSpendr(
			'spendrTransaction',
			[
				'type' => 'update',
				'txnToken' => $txnToken,
				'action' => $action
			]
		);
	}

	public static function spendrCancelExpiredTransactions()
    {
        self::sendSpendr(
            'spendrTransaction',
            [
                'type' => 'cancelExpiredTransactions'
            ]
        );
    }

    public static function spendrRefundTransaction(
    	$txnToken,
		$refundAmount,
		$employeeId,
		$locationId,
		$terminalId,
		$timezone
	){
		self::sendSpendr(
			'spendrTransaction',
			[
				'type' => 'refund',
				'txnToken' => $txnToken,
				'refundAmount' => $refundAmount,
				'employeeId' => $employeeId,
				'locationId' => $locationId,
				'terminalId' => $terminalId,
				'timezone' => $timezone
			]
		);
	}

	public static function spendrLoad($loadId)
	{
		self::sendSpendr('spendrTransaction', [
			'type' => 'loadCard',
			'loadId' => $loadId
		]);
	}

	public static function spendrCancelLoad($loadId, $cancelReason)
	{
		self::sendSpendr('spendrTransaction', [
			'type' => 'cancelLoad',
			'loadId' => $loadId,
			'cancelReason' => $cancelReason
		]);
	}

	public static function spendrSettleLoad($loadId)
	{
		self::sendSpendrBankLedger('spendrBankLedger', [
			'type' => 'settleLoad',
			'loadId' => $loadId,
		]);
	}

	public static function spendrReturnLoad($tranId, $returnCode)
	{
		self::sendSpendr('spendrTransaction', [
			'type' => 'returnLoad',
			'tranId' => $tranId,
			'returnCode' => $returnCode
		]);
	}

	public static function spendrBatchFile($processType, $batchForTest = false)
	{
		self::sendSpendr('spendrTransaction', [
			'type' => 'batchFile',
			'processType' => $processType,
			'batchForTest' => $batchForTest
		]);
	}

	public static function spendrProcessBankLedger($spendrTransactionId, $processType)
	{
		self::sendSpendrBankLedger('spendrBankLedger', [
			'type' => 'bankLedger',
			'spendrTransactionId' => $spendrTransactionId,
			'processType' => $processType
		]);
	}

	public static function spendrChargeConsumerInactivityFee()
	{
		self::sendSpendr('spendrTransaction', [
			'type' => 'chargeConsumerInactivityFee',
		]);
	}

	public static function spendrChargeConsumerInactivityFeeAction($userId, $feeName, $fee)
	{
		self::sendSpendr('spendrTransaction', [
			'type' => 'chargeConsumerInactivityFeeAction',
			'userId' => $userId,
			'feeName' => $feeName,
			'fee' => $fee
		]);
	}

	public static function spendrCorrectData($funName)
	{
		self::sendSpendr('spendrTransaction', [
			'type' => 'correctData',
			'funName' => $funName
		]);
	}

	public static function spendrCorrectConsumerReturnFee($funName, $loadId, $type, $amount)
	{
		self::sendSpendr('spendrTransaction', [
			'type' => 'correctData',
			'funName' => $funName,
			'processingType' => $type,
			'loadId' => $loadId,
			'amount' => $amount,
		]);
	}

	public static function spendrPromoCodeRedemption($loadId)
	{
		self::sendSpendrPromo('spendrPromoCode', [
			'type' => 'redemption',
			'loadId' => $loadId
		]);
	}

	public static function spendrBankLedgerLoad($loadId)
	{
		self::sendSpendrBankLedger('spendrBankLedger', [
			'type' => 'loadCard',
			'loadId' => $loadId
		]);
	}

	public static function spendrBankLedgerAddBatchAmountToPartner($txnId)
	{
		self::sendSpendrBankLedger('spendrBankLedger', [
			'type' => 'addBatchAmountToPartner',
			'spendrTransactionId' => $txnId
		]);
	}
}
