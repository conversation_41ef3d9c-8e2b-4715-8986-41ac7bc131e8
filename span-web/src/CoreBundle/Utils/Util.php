<?php

namespace CoreBundle\Utils;

use Adbar\Dot;
use Carbon\Carbon;
use CoreBundle\Command\BaseCommand;
use CoreBundle\Entity\Affiliate;
use CoreBundle\Entity\Attachment;
use CoreBundle\Entity\CardProgram;
use CoreB<PERSON>le\Entity\Config;
use CoreBundle\Entity\Country;
use CoreBundle\Entity\EntitySignature;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserToken;
use CoreBundle\Exception\RedirectException;
use CoreBundle\Services\LocalizeService;
use CoreBundle\Services\SSLEncryptionService;
use DateTime;
use DateTimeZone;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\Common\Util\ClassUtils;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\Query\Parameter;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;
use drupol\phpermutations\Generators\Combinations;
use FaasBundle\Services\ClientService;
use Faker\Factory;
use Faker\Generator;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use hisorange\BrowserDetect\Parser;
use JMS\Serializer\SerializerBuilder;
use Knp\Component\Pager\PaginatorInterface;
use libphonenumber\PhoneNumberFormat;
use libphonenumber\PhoneNumberUtil;
use malkusch\lock\mutex\PHPRedisMutex;
use malkusch\lock\mutex\PredisMutex;
use malkusch\lock\mutex\RedisMutex;
use Monolog\Formatter\LineFormatter;
use Monolog\Handler\StreamHandler;
use Monolog\Logger;
use Nacmartin\PhpExecJs\PhpExecJs;
use Normalizer;
use PortalBundle\Exception\DeniedException;
use PortalBundle\Exception\PortalException;
use SalexUserBundle\Entity\User;
use Stringy\Stringy;
use Symfony\Bundle\FrameworkBundle\Console\Application;
use Symfony\Component\Console\Input\ArrayInput;
use Symfony\Component\Console\Output\BufferedOutput;
use Symfony\Component\DependencyInjection\Container;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\HeaderBag;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\Lock\LockFactory;
use Symfony\Component\Security\Core\Authentication\Token\SwitchUserToken;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Component\Yaml\Yaml;
use TransferMexBundle\Services\SlackService;
use Twig\Environment;
use UsUnlockedBundle\Services\UserRefererService;
use Vich\UploaderBundle\Storage\StorageInterface;
use function Functional\pluck;
use function Functional\some;

class Util
{
    public const READONLY = false;
    public const FRAME_OPTIONS = 'SAMEORIGIN';

    public const US_HOLIDAYS = [
        // 2023
        '2023-01-01', '2023-01-02', '2023-01-16', '2023-02-20', '2023-05-29',
        '2023-06-19', '2023-07-04', '2023-09-04', '2023-10-09', '2023-11-10',
        '2023-11-11', '2023-11-23', '2023-12-25',

        // 2024
        '2024-01-01', '2024-01-15', '2024-02-19', '2024-05-27',
        '2024-06-19', '2024-07-04', '2024-09-02', '2024-10-14',
        '2024-11-11', '2024-11-28', '2024-12-25',

        // 2025
        '2025-01-01', '2025-01-20', '2025-02-17', '2025-05-26',
        '2025-06-19', '2025-07-04', '2025-09-01', '2025-10-13',
        '2025-11-11', '2025-11-27', '2025-12-25',

        // 2026
        '2026-01-01', '2026-01-19', '2026-02-16', '2026-05-25',
        '2026-06-19', '2026-07-04', '2026-09-07', '2026-10-12',
        '2026-11-11', '2026-11-26', '2026-12-25',

        // 2027
        '2027-01-01', '2027-01-18', '2027-02-15', '2027-05-31',
        '2027-06-19', '2027-07-04', '2027-09-06', '2027-10-11',
        '2027-11-11', '2027-11-25', '2027-12-25',
    ];

    const DATE_FORMAT = 'm/d/Y';
    const DATE_FORMAT_MONTH = 'Ym';
    const DATE_FORMAT_MONTH_DASH = 'Y-m';
    const DATE_FORMAT_LONG = 'F d, Y';
    const DATE_FORMAT_PORTAL = 'M d, Y';
    const DATE_TIME_FORMAT = 'm/d/Y g:i A';
    const DATE_TIME_FORMAT_LC = 'm/d/Y g:i a';
    const DATE_TIME_TZ_FORMAT = 'm/d/Y g:i A e';
    const DATE_TIME_FILE_FORMAT = 'YmdHis'; // 20191220180312
    const TIME_FORMAT = 'g:i A';

    const DATE_FORMAT_WEEK_DASH = 'Y-W';
    const DATE_FORMAT_YEAR_DASH = 'Y';
    const DATE_FORMAT_SEARCH = 'Y-m-d';
    const DATE_TIME_FORMAT_SEARCH = 'Y-m-d H:i';
    const DATE_TIME_FORMAT_SEARCH_FULL = 'Y-m-d H:i:s';
    const DATE_TIME_FORMAT_FULL = 'Y-m-d H:i:s';

    const DATE_FORMAT_ISO_FULL_DATE = 'Y-m-d';
    const DATE_FORMAT_ISO_DATE_TIME = 'c';

    const SYSTEM_NAME = 'US Unlocked';
    const DEFAULT_HELP_DESK_MAIL = '<EMAIL>';
    const USER_AGENT = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_12_5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.115 Safari/537.36';

    const FROM = "From";
    const TO = "To";
    const CC = "Cc";
    const BCC = "Bcc";
    const SUBJECT = "Subject";
    const TAG = 'Tag';
    const HTMLBODY = "HtmlBody";
    const TEXTBODY = "TextBody";
    const REPLYTO = "ReplyTo";

    const ADMIN_ID = 500000001;

    public const RESERVED_ADMIN_IDS = [
        500000001,
        1,
    ];

    const COOKIE_CARD_PROGRAM = '__card_program__';
    const HEADER_CARD_PROGRAM = 'x-card-program';

    const COOKIE_PLATFORM = '__platform__';
    const HEADER_PLATFORM = 'x-platform';

    const TRANSFERMEX_APP_OPTIONS_URL = '_security.transfer_mex_options_url';


    /**
     * @var Container $container
     */
    public static $container = null;
    public static $serializer = null;

    /**
     * @var null|Request
     */
    public static $request = null;

    public static $emReconnect = false;

    /**
     * @var null|User $user
     * @deprecated Use the method user() instead
     */
    public static $user = null;

    /** @var null|User $user */
    public static $cronUser = null;

    /** @var null|User $user */
    public static $appUser = null;

    /** @var null|User $user */
    public static $devUser = null;

    /** @var null|User $apiInvoker */
    public static $apiInvoker = null;

    /** @var null|UserToken  */
    public static $apiToken = null;

    /**
     * @var null|Platform
     */
    public static $platform = null;

    /**
     * @var null|CardProgram
     */
    public static $cardProgram = null;

    /** @var BaseCommand */
    public static $console;

    public static $runInConsole = false;

    public static ?\Symfony\Bundle\SecurityBundle\Security $security = null;

    public static ?ManagerRegistry $doctrine = null;

    public static ?Environment $twig = null;

    public static ?PaginatorInterface $paginator = null;

    public static ?ValidatorInterface $validator = null;

    public static ?EventDispatcherInterface $eventDispatcher = null;

    public static ?LockFactory $lockFactory = null;

    protected static $configKeys;

    /**
     * @param bool $reconnect
     * @return EntityManager
     */
    public static function em($reconnect = false, $manager = 'default')
    {
        $doctrine = self::$doctrine;

        /** @var EntityManager $em */
        $em = $doctrine->getManager($manager);
        if (!$em->isOpen()) {
            $doctrine->resetManager($manager);
            $em = $doctrine->getManager($manager);
        }
        if ($reconnect || self::$emReconnect) {
            self::reconnect($em);
        }
        return $em;
    }

    public static function aem($manager = 'analytics')
    {
        return self::em(false, $manager);
    }

    public static function expr()
    {
        return self::em()->getExpressionBuilder();
    }

    public static function flush()
    {
        self::em()->flush();
    }

    public static function refresh($entity)
    {
        self::em()->refresh($entity);
        return $entity;
    }

    public static function refreshAndReQuery($entity)
    {
        $entity = self::refresh($entity);
        if (method_exists($entity, 'getId')) {
            return self::em()->getRepository($entity::class)
                ->find($entity->getId());
        }
        return $entity;
    }

    public static function detach($entity)
    {
        $em = self::em();
        $em->detach($entity);

        if (method_exists($entity, 'getId')) {
            return $em->getRepository(get_class($entity))->find($entity->getId());
        }
        return $entity;
    }

    public static function rawQuery($query = '', $em = null, $manager = 'default')
    {
        try {
            if (!$em) {
                $em = self::em($manager);
            }
            $conn = $em->getConnection();
            $stmt = $conn->prepare($query);
            $stmt->execute();
            Log::info('Raw Query successfully executed');
            return true;
        } catch (\Exception $error) {
            Log::debug('Raw Query Error: ' . $error->getMessage());
            Log::debug($query);
            return null;
        }
    }

    // Reconnect to database if it's timed out
    public static function reconnect($em = null, $manager = 'default')
    {
        if (!$em) {
            $em = self::em($manager);
        }
        $connection = $em->getConnection();
        if ($connection->isConnected() === false) {
            $connection->close();
            $connection->connect();
        }

        return $connection;
    }

    public static function persist($object)
    {
        $em = self::em();
        $em->persist($object);
        $em->flush();

        return $object;
    }

    public static function resetQuery(QueryBuilder $query)
    {
        return (clone $query)->setMaxResults(null)
            ->setFirstResult(null)
            ->resetDQLPart('orderBy');
    }

    public static function twig()
    {
        return self::$twig;
    }

    /**
     * New York Time Zone (Timezone also used in FirstView's report)
     * @return DateTimeZone
     */
    public static function tzNewYork()
    {
        return new DateTimeZone('America/New_York');
    }

    /**
     * US Central Time Zone (Timezone also used in Rapid's API)
     * @return DateTimeZone
     */
    public static function tzCentral()
    {
        return new DateTimeZone('US/Central'); // 'America/Mexico_City'
    }

    /**
     * UTC timezone
     * @return DateTimeZone
     */
    public static function tzUTC()
    {
        return new DateTimeZone('UTC');
    }

    /**
     * EST timezone
     * @return DateTimeZone
     */
    public static function tzEST()
    {
        return new DateTimeZone('EST');
    }

    /**
     * Shanghai timezone
     * @return DateTimeZone
     */
    public static function tzShanghai()
    {
        return new DateTimeZone('Asia/Shanghai');
    }

    public static function formatBirthday(\DateTime $date = null, $format = 'c', $tz = null)
    {
        if (!$date) {
            return null;
        }
        $d = Carbon::instance($date);
        $d->setTimezone($tz ?: self::tzUTC());
        return $d->startOfDay()->addHours(11)->format($format);
    }

    public static function formatUtcDate($date, $format = 'Y-m-d')
    {
        return self::formatDateTime($date, $format, self::tzUTC());
    }

    public static function utc($requestDate = null)
    {
        return self::timeUTC($requestDate, true);
    }

    public static function timeUTC($requestDate = null, $utcInput = false)
    {
        $time = self::timeLocal($requestDate, $utcInput);
        $time->setTimezone(self::tzUTC());
        return $time;
    }

    public static function timeUTCNullable($requestDate = null, $utcInput = true)
    {
        if (!$requestDate) {
            return null;
        }
        return self::timeUTC($requestDate, $utcInput);
    }

    public static function timeLocal($requestDate = null, $utcInput = false)
    {
        $tz = $utcInput ? self::tzUTC() : self::timezone();
        return new Carbon($requestDate, $tz);
    }

    public static function timeLocalNullable($requestDate = null, $utcInput = false)
    {
        if (!$requestDate) {
            return null;
        }
        return static::timeLocal($requestDate, $utcInput);
    }

    public static function timezone(Request $request = NULL, $user = null)
    {
        if (!$request) {
            $request = self::$request;
        }
        if ($request->headers->has('x-timezone')) {
            $tz = $request->headers->get('x-timezone');
            if ($tz) {
                return $tz;
            }
        }
        if ($request->headers->has('Timezone')) {
            $tz = $request->headers->get('Timezone');
            if ($tz) {
                return $tz;
            }
        }
        if (!$user) {
            $user = self::user();
        }
        if ($user && is_object($user) && $user->getTimezone()) {
            return $user->getTimezone();
        }
        return 'America/New_York';
    }

    /**
     * @param DateTime $dateTime
     * @return Carbon
     */
    public static function toUTC(\DateTime $dateTime)
    {
        return Carbon::instance($dateTime->setTimezone(self::tzUTC()));
    }

    /**
     * @param DateTime      $dateTime
     * @param DateTimeZone $zone
     *
     * @return Carbon
     */
    public static function toTimezone(\DateTime $dateTime, DateTimeZone $zone)
    {
        return Carbon::instance($dateTime->setTimezone($zone));
    }

    public static function timestampToLocalDate($timestamp, $format = self::DATE_FORMAT_ISO_FULL_DATE)
    {
        return Carbon::createFromTimestamp($timestamp)
            ->setTimezone(new DateTimeZone(self::timezone()))
            ->format($format);
    }

    public static function localDateToTimestamp($date)
    {
        return (new Carbon($date, self::timezone()))->getTimestamp();
    }

    public static function microtime()
    {
        return (int)floor(microtime(true) * 1000);
    }

    public static function correctTimezone(?string $input): ?string
    {
        $all = Data::callback('timezones', [], static function () {
            return \DateTimeZone::listIdentifiers();
        }, false);
        if (in_array($input, $all, true)) {
            return $input;
        }
        return null;
    }

    /**
     * Fix PHP bug of \DateTime::modify. See:
     * https://stackoverflow.com/a/31977411/6622926
     *
     * @param DateTime $before
     * @param DateTime $after
     *
     * @return Carbon
     */
    public static function fixDST(\DateTime $before, \DateTime $after)
    {
        $d1 = $before->format('I');
        $d2 = $after->format('I');
        if ($d1 === '1' && $d2 === '0') {
            $after->modify('+1 hour');
        } else if ($d1 === '0' && $d2 === '1') {
            $after->modify('-1 hour');
        }
        return Carbon::instance($after);
    }

    public static function modify(Carbon $date, $express, $unit)
    {
        $day = $date->day;
        $date->modify($express);

        if ($unit === 'month' || $unit === 'year') {
            if ($day !== $date->day) {
                $date->modify('last day of previous month');
            }
        }

        return $date;
    }

    public static function setWeekStartAtMonday()
    {
        Carbon::setWeekStartsAt(Carbon::MONDAY);
        Carbon::setWeekEndsAt(Carbon::SUNDAY);
    }

    /**
     * @param string $week format like '2022-W03'
     */
    public static function getStartOfWeek($week, $tz = null)
    {
        $tz = $tz ?? self::timezone();

        $year = substr($week, 0, 4);
        $weekNum = (int)substr($week, -2);

        $start = Carbon::create($year, 1, 1, 0, 0, 0, $tz);
        $startNum = (int)$start->format('W');
        if ($startNum === 1) {
            $weekNum--;
        }

        return $start->addWeeks($weekNum)->startOfWeek();
    }

    /**
     * @param Carbon $carbon
     * @return string format like '2022-W03'
     */
    public static function formatWeek(Carbon $carbon)
    {
        $year = (int)$carbon->format('Y');
        $month = $carbon->format('m');
        $week = $carbon->format('W');
        if ($month === '12' && $week === '01') {
            $year++;
        } else if ($month === '01' && (int)$week >= 50) {
            $year--;
        }
        return $year . '-W' . $week;
    }

    /**
     * @return Request
     */
    public static function request($resetForce = false)
    {
        if (null === self::$request || $resetForce) {
            $requestStack = self::$container->get('request_stack');
            if ($requestStack) {
                self::$request = $requestStack->getMainRequest();
            }

            if (!self::$request) {
                self::$request = Request::createFromGlobals();
            }
        }
        return self::$request;
    }

    public static function jsonSimpleRequest(Request $request = null)
    {
        $request = $request ?? self::request();
        $user = self::appUser();
        $data = [
            'host' => self::getParameter('host'),
            'domain' => $request->getSchemeAndHttpHost(),
            'path' => $request->getRequestUri(),
            'method' => $request->getMethod(),
            'user' => $user?->getId(),
            'ip' => Security::getClientIps(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
        ];

        $headers = $request->headers->all();
        $keys = ['x-location', 'x-terminal', 'x-version', 'x-environment'];
        foreach ($keys as $key) {
            if (isset($headers[$key])) {
                $data[$key] = $headers[$key];
            }
        }
        return $data;
    }

    public static function jsonRequest(Request $request = null, $clean = true, $callback = null)
    {
        if (!$request) {
            $request = self::request();
        }
        $user = self::appUser();

        $server = $_SERVER;
        unset($server['HTTP_COOKIE']);

        $headers = $request->headers->all();
        unset(
            $headers['cookie'], $headers['authorization'],
            $headers['x-token'], $headers['x-api-key'],
            $headers['signature'],
        );

        try {
            $xKeys = [
                'x-token', 'x-api-key', 'x-access-key',
                'x-signature', 'x-salt',
            ];
            $maskKeys = [
                'newPin', 'newCvc', 'newCvv',
                'pinCode', 'cvcCode', 'cvvCode',
                'cardNumber', 'fullCardNumber',
                'passWord', 'pwd', 'passCode',
                'token', 'pin', 'pan', 'cvc', 'cvv',
            ];

            self::maskArrayField($headers, $xKeys, false);
            self::maskArrayField($headers, $maskKeys);

            $query = $request->query->all();
            self::maskArrayField($query, $xKeys, false);
            self::maskArrayField($query, $maskKeys);

            $post = $request->request->all();
            self::maskArrayField($post, $xKeys, false);
            self::maskArrayField($post, $maskKeys);

            if ($callback && is_callable($callback)) {
                $callback($headers, $query, $post, $request);
            }

            if (isset($query['card']) && is_array($query['card'])) {
                foreach (['pan', 'cvv', 'exp_month', 'exp_year'] as $item) {
                    unset($query['card'][$item], $post['card'][$item]);
                }
            }

            $server = array_filter($server, function ($k) {
                if ($k === 'REQUEST_URI') {
                    return false;
                }
                foreach (['HTTP_', 'REQUEST_', 'REMOTE_'] as $prefix) {
                    if (Util::startsWith($k, $prefix)) {
                        return true;
                    }
                }
                return false;
            }, ARRAY_FILTER_USE_KEY);
            self::maskArrayField($server, [
                'HTTP_X_TOKEN', 'HTTP_X_API_KEY', 'HTTP_X_ACCESS_KEY',
                'HTTP_X_SIGNATURE', 'HTTP_X_SALT', 'HTTP_AUTHORIZATION',
                'HTTP_X_AUTHORIZATION', 'HTTP_SIGNATURE',
            ], false);
        } catch (\Exception $ex) {
            $headers = [];
            $query = [];
            $post = [];
            $server = [];
        }

        return [
            'host' => self::getParameter('host'),
            'path' => $request->getPathInfo(),
            'user' => $user?->getId(),
            'query' => $query,
            'request' => $post,
            'files' => $request->files->keys(),
            'headers' => $headers,
            'server' => $server,
        ];
    }

    public static function jsonResponse(Response $response)
    {
        $headers = $response->headers->all();
        unset($headers['cookie']);

        return [
            'statusCode' => $response->getStatusCode(),
            'headers' => $headers,
            'content' => $response->getContent(),
        ];
    }

    public static function getRequestSignature(Request $request = null, $others = [], $user = null)
    {
        $request = $request ?? self::request();
        $all = array_merge(
            [
                '_path_info' => $request->getPathInfo(),
            ],
            $request->query->all(),
            $request->request->all(),
            $others
        );
        unset($all['_t'], $all['_'], $all['_mode_']);
        ksort($all);

        $prefix = 'req_sig_';
        /*
        $user = $user ?? self::user();
        if ($user) {
            $prefix .= '_' . $user->getId() . '_';
        }
        */

        return $prefix . md5(http_build_query($all));
    }

    public static function clearRequestSignatureCache(User $user = null, $all = false)
    {
        $prefix = 'req_sig_';
        Data::delAllWith($prefix);
    }

    public static function clearDoctrineCache()
    {
        $keys = array_keys($GLOBALS);
        foreach ($keys as $key) {
            if (str_starts_with($key, '__cache__')) {
                unset($GLOBALS[$key]);
            }
        }
        self::em()->clear();
    }

    public static function completeDoctrineBatch()
    {
        $fields = [
            'user' => User::class,
            'cronUser' => User::class,
            'apiInvoker' => User::class,
            'platform' => Platform::class,
            'cardProgram' => CardProgram::class,
        ];
        $ids = [];
        foreach ($fields as $field => $class) {
            $entity = self::$$field ?? null;
            if ($entity && method_exists($entity, 'getId')) {
                $ids[$field] = $entity->getId();
            }
        }

        $em = self::em();
        $em->flush();
        self::clearDoctrineCache();

        foreach ($ids as $field => $id) {
            self::$$field = $em->getRepository($fields[$field])->find($id);
        }
    }

    public static function env()
    {
        return self::getParameter('kernel.environment');
    }

    public static function isDebug()
    {
        try {
            $request = self::request();
        } catch (\Exception $exception) {
            return false;
        }

        if (!defined('__DEV_KEY__')) {
            return false;
        }

        return (bool)$request->cookies->get(__DEV_KEY__) && self::isTrustedDevIp();
    }

    public static function isTrustedDevIp()
    {
        $ips = Security::getClientIps();
        $trusted = [
            '127.0.0.1',

            // Amazon Lightsail - Oregon
            '**************',
            '2600:1f14:253f:4900:4d74:ff47:b705:2d3d',

            // Amazon Lightsail - Singapore
            '**************',
            '2406:da18:c15:7400:21b0:c1a5:14e9:2ed5',

            // Amazon Lightsail - Tokyo
            '************',
            '2406:da14:ca8:7d00:5890:80f3:3f94:8dba',
        ];
        if (array_intersect($ips, $trusted)) {
            return true;
        }
        foreach ($ips as $ip) {
            // Private network: 192.168.x.x
            if (self::startsWith($ip, '192.168.')) {
                return true;
            }
            // Private network: 172.16.x.x to 172.31.x.x
            if (self::startsWith($ip, '172.')) {
                $parts = explode('.', $ip);
                $second = (int)($parts[1] ?? '0');
                if ($second >= 16 && $second <= 31) {
                    return true;
                }
            }
        }
        return false;
    }

    public static function isDemo()
    {
        try {
            $request = self::request();
        } catch (\Exception $exception) {
            return false;
        }

        $param = self::$container->getParameter('demo_mode');
        if ((bool)$request->cookies->get($param)) {
            return true;
        }

        $user = static::user();
        return $user && $user->isDemoAccount();
    }

    public static function logCallStack()
    {
        try {
            throw new \RuntimeException('logging callstack...');
        } catch (\Exception $exception) {
            Log::exception($exception->getMessage(), $exception);
        }
    }

    public static function getCaller($returnArray = false)
    {
        $caller = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 3);
        $c = $caller[2] ?? [];
        if ($returnArray) {
            return $c;
        }
        $d = $caller[1] ?? [];
        return ($c['class'] ?? '') . '::' . ($c['function'] ?? '') . '_' . ($d['line'] ?? '');
    }

    public static function demoLabel()
    {
        return '<a href="/admin/demo/off" title="Click to turn off" style="position: fixed; bottom: 0; left: 0; z-index: ************; text-transform: uppercase; transform: translate(-30%, -25%) rotate(45deg); background: red; color: white !important; text-decoration: none; padding: 4px 30px; font-weight: bold; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);">demo</a>';
    }

    public static function adjustTimeRangeForDemo(Request $request)
    {
        if (Util::isDemo()) {
            $req = $request->request;

            $start = '2018-03-01T16:00:00.000Z';
            $end = '2018-03-31T16:00:00.000Z';
            $newRange = $req->all('range');
            if (isset($newRange['ucl.loadAt'])) {
                $newRange['ucl.loadAt']['start'] = $start;
                $newRange['ucl.loadAt']['end'] = $end;
            }

            if (isset($newRange['start'])) {
                $newRange['start'] = $start;
                $newRange['end'] = $end;
            }

            $req->set('range', $newRange);

            if ($req->has('start')) {
                $req->set('start', $start);
                $req->set('end', $end);
            }

            if ($req->has('RegistrationdateA')) {
                $req->set('RegistrationdateA', $start);
                $req->set('RegistrationdateB', $start);
            }
        }
    }

    public static function isTernitupTesting(): string|bool
    {
        $user = self::user();
        if (!$user) {
            return false;
        }
        return $user->isInternalTester();
    }

    public static function isTernEmployee(string|User $input): bool
    {
        if ($input instanceof User) {
            $input = $input->getEmail();
        }
        return self::hasSuffix($input, [
            '@ternitup.com',
            '@terncommerce.com',
        ]);
    }

    public static function isInternalTester(string|User $input): string|bool
    {
        if ($input instanceof User) {
            $input = $input->getEmail();
        }
        $input = trim($input);
        if (!$input) {
            return false;
        }
        if (self::isTernEmployee($input)) {
            return 'always';
        }
        return in_array($input, array_filter(Config::array(
            Config::CONFIG_TESTERS_MAIL,
        )));
    }

    public static function isCommand()
    {
        return PHP_SAPI === 'cli';
    }

    public static function isDev($absolute = false)
    {
        if ('dev' === self::env()) {
            return true;
        }
        if ($absolute) {
            return false;
        }
        return self::isDebug();
    }

    public static function isCRSFAttack($referer, $method) {
        if (!self::isLive() && !self::isStaging()) {
            return true;
        }

        if (self::isDev()) {
            return false;
        }

        if (static::endsWith($referer, '.virtualcards.us/' . $method)) {
            return false;
        }

        $liveDomains = self::getLiveDomains();
        foreach ($liveDomains as $domain) {
            if ($referer === $domain . '/' . $method) {
                return false;
            }
        }
        return true;
    }

    public static function getLiveDomains()
    {
        // TODO: Make the below list dynamic by reading the platform setting cache
        return [
            'https://prepaidprogrammanagement.com',
            'https://www.prepaidprogrammanagement.com',
            'https://account.usunlocked.com',
            'https://admin.transfermex.com',
            'https://app.transfermex.com',
            'https://admin.cashonweb.net',
            'https://secureaccounts.leaflink.com',
            'https://admin.spendr.com',
            'https://app.spendr.com',
            'https://apos.d2finservices.com',
            'https://welldyne.d2finservices.com'
        ];
    }

    public static function isLive()
    {
        if (self::isCommand()) {
            return self::getParameter('host') === 'https://www.virtualcards.us';
        }

        $host = self::host();
        if (static::isStaging()) {
            return false;
        }
        if (static::endsWith($host, '.virtualcards.us')) {
            return true;
        }
        $platform = self::platform();
        if ($platform) {
          $liveDomains = $platform->getLiveDomains();
          if ($liveDomains && strpos($liveDomains, '"' . static::request()->getHost() . '"') !== false) {
              return true;
          }
        }
        return in_array($host, self::getLiveDomains());
    }

    public static function isStaging($withLocal = false)
    {
        if (self::isCommand()) {
            return self::getParameter('host') === 'https://staging.virtualcards.us';
        }

        $host = self::host();
        if (static::endsWith($host, 'staging.virtualcards.us') || static::endsWith($host, '-test.virtualcards.us')) {
            return true;
        }
        if ($withLocal && static::endsWith($host, 'staging.span.hans')) {
            return true;
        }
        if ($withLocal && static::endsWith($host, 'staging.span.local')) {
            return true;
        }

        if (self::isStage()) {
            return true;
        }

        return in_array($host, [
            'https://test.spendr.com',
        ]);
    }

    public static function isStage()
    {
        if (self::isCommand()) {
            return self::getParameter('host') === 'https://demo.virtualcards.us';
        }

        $host = self::host();
        if (static::endsWith($host, 'demo.virtualcards.us') || static::endsWith($host, '-stage.virtualcards.us')) {
            return true;
        }

        return false;
    }

    public static function isServer()
    {
        return self::isLive() || self::isStaging();
    }

    public static function isServerHosts()
    {
        return in_array(gethostname(), [
            'ip-172-31-90-202', // EC2 production PHP8
            'SPAN-Test', // EC2 staging PHP8
        ]);
    }

    public static function isDevDevice()
    {
        return in_array(gethostname(), [
            // Hans
            'hans-mac', // Hans local
            'hans-mac2', // Hans local 2
            'Hanss-MacBook-Pro.local', // Hans' new Macbook

            // Abel
            'Abel.local', // Abel's Macbook
            'abeldeMac-mini.local'
        ]) || self::isDevDocker();
    }

    public static function isDevDocker()
    {
        return in_array(gethostname(), [
            'd93917c05a7a', // Hans' Docker
            '9f04a978e11f', // Hans' Docker 2
            '22dc8d799a2e', // Hans' Docker 3
            '8aadba68829a', // Hans' Docker 4
            'hans-mac2', // Hans' Docker 5
        ]);
    }

    public static function isLocal()
    {
        if (self::isDevDevice()) {
            return true;
        }
        $host = self::host();
        return in_array($host, [
                   'http://span.hans',
                   'https://span.hans',
                   'http://span.local',
                   'https://span.local',
               ]) ||
               static::endsWith($host, '.span.hans') ||
               static::endsWith($host, '.span.local');
    }

    public static function isHansUser()
    {
        $user = self::user();
        return $user && $user->getEmail() === '<EMAIL>';
    }

    public static function isTracyUser()
    {
        $user = self::user();
        return $user && $user->getEmail() === '<EMAIL>';
    }

    public static function isCowTestUser()
    {
        $user = self::user();
        return $user && ($user->getEmail() === '<EMAIL>' || $user->getEmail() === '<EMAIL>');
    }

    public static function isTestUser()
    {
        $user = self::user();
        return $user && $user->isTestData();
    }

    public static function isTestEnv()
    {
        return self::env() === 'test';
    }

    public static function isDevTesting()
    {
        $uri = static::request()->getRequestUri();
        return static::startsWith($uri, '/dev/');
    }

    public static function isReservedHosts()
    {
        $host = self::request()->getHttpHost();
        if (in_array($host, [
            'localhost',
            'span.hans',
            'span.local',
            'midea.ltd',
        ])) { /* || self::isDevDevice()*/
            return true;
        }
        $parts = explode('.', $host);
        return isset($parts[0]) &&
            in_array($parts[0], CardProgram::RESERVED_DOMAINS);
    }

    public static function isSelfRequest()
    {
        $ips = Security::getClientIps();
        $all = self::getServerIps();
        return !empty(array_intersect($all, $ips));
//        Log::debug('Checking self request by IPs', [
//            'url' => self::request()->getRequestUri(),
//            'ip' => $ip,
//            'ips' => Security::getClientIps(),
//            'servers' => $all,
//        ]);

        // return in_array($ip, $all);
    }

    /**
     * @param string|null $env prod|staging|all
     */
    public static function getServerIps($env = null)
    {
        $live = [
            '************',
            '*************', // The new temporary production server
            '127.0.0.1',
        ];
        if (in_array($env, [
            'live',
            'pro',
            'prod',
            'production',
        ])) {
            return $live;
        }
        $staging = [
            '**************',
            '127.0.0.1',
        ];
        if (in_array($env, [
            'dev',
            'stag',
            'stage',
            'staging',
        ])) {
            return $staging;
        }
        return array_merge($live, $staging);
    }

    public static function isTestEmail($email)
    {
        $matches = [];
        preg_match('/tcspan\.at\.(\d{13})@yopmail\.com/', $email, $matches);
        return isset($matches[1]) || $email === '<EMAIL>';
    }

    public static function isUUID($v)
    {
        $v = strtolower($v);
        $len = strlen($v);
        if ($len !== 32 && $len !== 36) {
            return false;
        }
        $matches = [];
        preg_match('|^[a-z0-9\-]+$|', $v, $matches);
        return $matches;
    }

    public static function isApiPath()
    {
        return self::isPathStartedWith('/api/');
    }

    public static function isPathStartedWith($prefix)
    {
        $request = self::request();
        $path = $request->getPathInfo();
        return self::startsWith($path, $prefix);
    }

    public static function isAPI()
    {
        $request = self::request();
        $path = $request->getPathInfo();
        if (
            self::startsWith($path, '/api/')
            || self::startsWith($path, '/mex/m/')
            || self::startsWith($path, '/spendr/m/')
            || self::startsWith($path, '/skux/m/')
        ) {
            return true;
        }

        if ($request->headers->get('x-api-key')) {
            if (Bundle::isClf()) {
                if (some(['dispensary', 'vendor', 'merchant', 'bank'], static function ($role) use ($path) {
                    return self::startsWith($path, '/clf/' . $role);
                })) {
                    return true;
                }
            }
            if ($path === '/attachments' && $request->getMethod() === 'POST') {
                return true;
            }
        }

        try {
            $spendr = Bundle::isSpendr();
        } catch (\Throwable $t) {
            $spendr = false;
        }

        if ($spendr) {
            if (
            	($request->headers->get('x-token') || $request->get('x-token'))
				&& self::startsWith($path, '/attachments')
				&& $request->getMethod() === 'POST'
			) {
                return true;
            }
        }

        return false;
    }

    public static function isPrivacyRequest()
    {
        $request = self::request();
        $path = $request->getPathInfo();
        return self::startsWith($path, '/p/');
    }

    public static function isMexMobileRequest()
    {
        $request = self::request();
        $path = $request->getPathInfo();
        return self::startsWith($path, '/mex/m');
    }

    public static function isMdRequest()
    {
        $request = self::request();
        return !!$request->headers->get('x-api-key');
    }

    public static function isWebRequest()
    {
        $userAgent = (string)self::request()->headers->get('user-agent');
        return $userAgent && self::containsSubString($userAgent, [
                'Mozilla', 'Chrome', 'Chromium', 'Opera', 'Safari', 'Firefox', 'Edge', 'MSIE', 'Trident',
                'OPR', 'UCBrowser', 'UCWEB', 'CriOS', 'Vivaldi', 'SamsungBrowser',
                'Baidu', 'QQ', 'Weibo', 'Weixin', 'Wechat', 'MicroMessenger',
            ]);
    }

    public static function isCronJob()
    {
        $request = self::request();
        return self::startsWith($request->getPathInfo(), '/t/cron/');
    }

    public static function isPidRunning($pid, $debug = false)
    {
        $pid .= ' ';
        $output = [];
        exec('ps -ax 2>&1', $output);
        foreach ($output as $line) {
            if ($debug) {
                self::consoleLine('Has pid running line: ' . $line, null, [], false);
            }
            $line = ltrim($line);
            if (str_starts_with($line, $pid)) {
                return $line;
            }
        }
        return false;
    }

    public static function hasProcessMatches($str, $exceptPid = null, $debug = false)
    {
        if ($exceptPid) {
            $exceptPid .= ' ';
        }
        $output = [];
        $matches = [];
        exec('ps -ax 2>&1', $output);
        foreach ($output as $line) {
            if ($debug) {
                self::consoleLine('Has process matches line: ' . $line, null, [], false);
            }
            $line = ltrim($line);
            if ($exceptPid && str_starts_with($line, $exceptPid)) {
                continue;
            }
            if (str_contains($line, $str)) {
                $matches[] = $line;
            }
        }
        return $matches;
    }

    /**
     * @return User|null
     */
    public static function getDefaultMasterAdmin()
    {
        $repo = self::em()->getRepository(\SalexUserBundle\Entity\User::class);
        foreach (self::RESERVED_ADMIN_IDS as $id) {
            $user = $repo->find($id);
            if ($user) {
                return $user;
            }
        }
        return null;
    }

    public static function getDevConsumer()
    {
        return User::find(500000004);
    }

    public static function isSimilarText(?string $a, ?string $b,
                                         float $minPercent = 80, ?int $minSimilarLength = null,
                                         bool $caseSensitive = false): bool
    {
        $a = trim($a ?? '');
        $b = trim($b ?? '');
        if ( ! $caseSensitive) {
            $a = strtolower($a);
            $b = strtolower($b);
        }
        if ($a === $b) {
            return true;
        }
        $percent = 0;
        $similarity = similar_text($a, $b, $percent);
        if ($percent < $minPercent) {
            return false;
        }
        if ($minSimilarLength !== null && $similarity < $minSimilarLength) {
            return false;
        }
        return true;
    }

    public static function e2a($entity)
    {
        if (null === $entity) {
            return null;
        }
        if (!self::$serializer) {
            self::$serializer = SerializerBuilder::create()->build();
        }
        $jsonObject = self::$serializer->serialize($entity, 'json');

        return json_decode($jsonObject, true);
    }

    public static function p2a($object)
    {
        return self::object2ArrayPrivate($object);
    }

    public static function object2ArrayPrivate($object)
    {
        $public = array();
        $reflection = new \ReflectionClass($object);
        foreach ($reflection->getProperties() as $property) {
            $key = $property->getName();
            $value = $property->getValue($object);
            if (is_object($value) || is_array($value)) {
                $public[$key] = self::object2ArrayPrivate($value);
            } else {
                $public[$key] = $value;
            }
        }
        return $public;
    }

    public static function object2ArrayPrivateNoNulls($object)
    {
        $public = self::object2ArrayPrivate($object);
        return self::removeNulls($public);
    }

    public static function removeNulls(array $array)
    {
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $array[$key] = self::removeNulls($value);
            } elseif ($value === null || $value === '') {
                unset($array[$key]);
            } else {
                $array[$key] = $value;
            }
        }
        return $array;
    }

    public static function isEmail($str)
    {
        $p = '/^[a-z0-9!#$%&*+-=?^_`{|}~]+(\.[a-z0-9!#$%&*+-=?^_`{|}~]+)*';
        $p .= '@([-a-z0-9]+\.)+([a-z]{2,3}';
        $p .= '|info|arpa|aero|coop|name|museum)$/ix';
        return preg_match($p, $str);
    }

    public static function getUserWithDeleted($entity)
    {
        Util::disableSoftDeletable();
        /** @var UserCard $entity */
        $u = Util::e2a($entity->getUser());
        Util::enableSoftDeletable();

        return $u;
    }

    public static function googleFrontendKey()
    {
        return self::getConfigKey('google_frontend_key');
    }

    public static function generate_key($length = 32)
    {
        $b63 = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ_';
        if (function_exists('openssl_random_pseudo_bytes')) {
            $pass = str_replace(array('+', '/', '==', '='), array('_', self::randString(1, $b63), '', ''), base64_encode(openssl_random_pseudo_bytes($length, $strong)));
        } else {
            $pass = '';
            while (strlen($pass) < $length) {
                $pass .= self::randString(rand(1, 6), $b63);
            }
        }
        return substr($pass, 0, $length);
    }

    /**
     * 18 characters
     *
     * @param int $extra
     * @return string
     */
    public static function unique_id($extra = 2)
    {
        return base_convert(microtime(false), 10, 36)
            . self::generate_key($extra);
    }

    public static function unique(array $array)
    {
        return array_keys(array_count_values($array));
    }

    public static function uniqueEntities($array)
    {
        $result = [];
        foreach ($array as $item) {
        	if ($item) {
				$result[$item->getId()] = $item;
			}
        }
        $news = array_values($result);
        if (is_array($array)) {
            return $news;
        }
        return new ArrayCollection($news);
    }

    public static function web64_decode($str)
    {
        //undo character substitutions
        $str = str_replace(array('-', '_', '!', '.'), array('+', '/', '==', '='), $str);
        return base64_decode($str);
    }

    public static function get_reward_urid($userID)
    {
        return Util::web64_encode(Util::rw_10to256($userID * 11 + 9));
    }

    public static function web64_encode($str)
    {
        $str = base64_encode($str);
        //replace bad url characters and reduce the ending terminators ('==' -> '!' && '=' -> '.')
        return str_replace(array('+', '/', '==', '='), array('-', '_', '!', '.'), $str);
    }

    public static function rw_10to256($number)
    {
        $result = "";
        $n = $number;
        do {
            $remainder = bcmod($n, 256);
            $n = bcdiv($n, 256);
            $result .= chr($remainder);
        } while ($n > 0);
        return $result;
    }

    public static function checkHashPassword($previousPassword, $plainPassword)
    {
        $plainPassword = trim($plainPassword);

        // Pull the salts from the db hashed password
        $salt1 = substr($previousPassword, 0, 3);
        $salt2 = substr($previousPassword, -3);
        return ($previousPassword == Util::hashPassword($plainPassword, $salt1, $salt2));
    }

    public static function checkAnyHashPasswords(array $previousPasswords, $plainPassword)
    {
        foreach ($previousPasswords as $previousPassword) {
            if (static::checkHashPassword($previousPassword, $plainPassword)) {
                return true;
            }
        }
        return false;
    }

    public static function hashPassword($plainPassword, $salt1 = null, $salt2 = null)
    {
        $plainPassword = trim($plainPassword);
        // Generate the salts randomly (if needed), different each time
        if ($salt1 == null) $salt1 = Util::randString(3, 'abcdefghijklmnopqrstuvwxyz0123456789');
        if ($salt2 == null) $salt2 = Util::randString(3, 'abcdefghijklmnopqrstuvwxyz0123456789');
        return $salt1 . Util::hash($salt1 . $plainPassword . $salt2) . $salt2;
    }

    public static function hash($input, $algo = 'sha256', bool $base64 = false)
    {
        if (is_array($input)) {
            $input = self::j2se($input);
        }
        $hash = hash($algo, $input, $base64);
        if ($base64) {
            $hash = base64_encode($hash);
        }
        return $hash;
    }

    public static function base64Hash($input, $algo = 'sha256')
    {
        return self::hash($input, $algo, true);
    }

    public static function shortHash($str, $length = 16)
    {
        $code = hash('sha1', 'TCSPAN_cKss3kid7kPWx0sdYyJ_' . $str);
        return substr($code, 0, $length);
    }

    public static function randString($len = 10, $choices = '0123456789abcdefghijklmnopqrstuvwxyz')
    {
        $nChoices = strlen($choices);
        $str = '';

        if ($nChoices < 1 || $len < 1) {
            return 'SPLAT!';
        }

        //seeding mt_srand is not necessary: in fact it's bad (was creating duplicate keys from util_str_seed)
        //mt_srand((double)microtime()*1000);

        for ($i = 0; $i < $len; $i++) {
            $str .= $choices[mt_rand(0, $nChoices - 1)] . '';
        }

        return $str;
    }

    public static function randNumber($len = 10)
    {
        return self::randString($len, '0123456789');
    }

    /**
     * Total 4 + 14
     *
     * @param int $len
     *
     * @return string
     */
    public static function randTimeNumber($len = 4)
    {
        $time = microtime(true) * 10000;
        return round($time) . static::randNumber($len);
    }

    public static function guid($hyphen = '-')
    {
        $char = strtoupper(md5(uniqid(mt_rand(), true)));
        return substr($char, 0, 8) . $hyphen
            . substr($char, 8, 4) . $hyphen
            . substr($char, 12, 4) . $hyphen
            . substr($char, 16, 4) . $hyphen
            . substr($char, 20, 12);
    }

    /**
     * convert xml object to array
     * @param  $xmlObj
     * @return array
     */
    public static function xml2array($xmlObj)
    {
        if (is_string($xmlObj)) {
            $xmlObj = self::getXmlObject($xmlObj);
        }
        $ret = array();
        foreach ((array)$xmlObj as $k => $v) {
            if ($v instanceof \SimpleXMLElement) {
                if (!$v->count() && !$v->attributes()->count()) {
                    $s = (string)$v;
                    if ($s) {
                        $v = trim($s);
                    }
                }
            }
            $ret[$k] = (is_array($v) || is_object($v)) ? self::xml2array($v) : $v;
        }
        return $ret;
    }

    /**
     * get the xml object
     * @param $xml
     * @return object
     */
    public static function getXmlObject($xml)
    {
        $result = @preg_replace_callback('/(<[^>]*:[^>]*>)/', function ($m) {
            return str_replace(':', '_COL_', $m[1]);
        }, $xml);
        return @simplexml_load_string($result);
    }

    public static function printXml($xml)
    {
        $dom = new \DOMDocument();
        $dom->preserveWhiteSpace = true;
        $dom->formatOutput = true;

        $dom->loadXML($xml);
        return $dom->saveXML();
    }

    public static function getParameter($name, $returnNullIfNotExist = false)
    {
        if ($returnNullIfNotExist) {
            if (!self::$container->hasParameter($name)) {
                return null;
            }
        }
        return self::$container->getParameter($name);
    }

    public static function getConfigKeys()
    {
        if (!self::$configKeys) {
            $all = Yaml::parse(file_get_contents(__DIR__ . '/../../../config/keys_rDLf4peHHQZz.yml')) ?? [];
            self::$configKeys = $all['parameters'] ?? [];
        }
        return self::$configKeys;
    }

    public static function getConfigKey($name)
    {
        $all = self::getConfigKeys();
        return $all[$name] ?? null;
    }

    public static function getDecryptedParameter($name, $fromKey = true)
    {
        if ($fromKey) {
            $value = self::getConfigKey($name);
        } else {
            $value = self::getParameter($name, true);
        }
        return Security::rsaDecrypt($value);
    }

    public static function getKmsParameter($name)
    {
        $cacheKey = 'config_key_' . $name;
        if (Data::has($cacheKey)) {
            $cached = Data::get($cacheKey);
            return Security::rsaDecrypt($cached);
        }
        $value = self::getConfigKey($name);
        $decrypted = SSLEncryptionService::tryToDecrypt($value);
        if ($decrypted) {
            Data::set($cacheKey, Security::rsaEncrypt($decrypted), false, 604800); // = 3600 * 24 * 7
        }
        return $decrypted;
    }

    public static function get($id)
    {
        return self::$container->get($id);
    }

    public static function find($entityName, $id)
    {
        return self::em()->getRepository($entityName)->find($id);
    }

    public static function getBooleanRequest($key)
    {
        $request = self::request();
        $v = $request->get($key);
        return $v === true || $v === 'true';
    }

    public static function v($array, $key)
    {
        if (isset($array[$key])) {
            return $array[$key];
        }
        return NULL;
    }

    public static function longRequest($max_execution_time = 30000, $memory_limit = '1024M')
    {
        ini_set('max_execution_time', $max_execution_time);
        ini_set('memory_limit', $memory_limit);

        self::em()->getConnection()->executeStatement('set sort_buffer_size=1073741824');

        $GLOBALS['BATCH'] = [
            'time' => time(),
        ];
    }

    public static function shortRequest()
    {
        self::longRequest(300, '512M');
    }

    public static function longerRequest()
    {
        self::longRequest(120000, '4096M');
    }

    public static function unlimitedRequest()
    {
        self::longRequest(0, '-1');
    }

    public static function dd()
    {
        $args = func_get_args();
        if ($args) {
            var_dump(...$args);
        }
        die;
    }

    public static function de()
    {
        $args = func_get_args();
        if ($args) {
            $args = Util::e2a($args);
            if ($args) {
                var_dump(...$args);
            }
        }
        die;
    }

    public static function dje()
    {
        header('Content-Type: application/json; charset=UTF-8');
        $args = func_get_args();
        if ($args) {
            echo json_encode(...Util::e2a($args));
        }
        die;
    }

    public static function dj()
    {
        header('Content-Type: application/json; charset=UTF-8');
        $args = func_get_args();
        if ($args) {
            echo json_encode(...$args);
        }
        die;
    }

    public static function dump($d)
    {
        dump(...func_get_args());
    }

    public static function detect()
    {
        return new \Mobile_Detect();
    }

    public static function acceptLanguage()
    {
        $code = self::request()->getPreferredLanguage() ?? 'en';
        return substr($code, 0, 2);
    }

    public static function host($configured = false)
    {
        if ($configured || self::isCommand()) {
            $host = self::getParameter('host', true);
            if ($host) {
                return $host;
            }
        }
        return self::request()->getSchemeAndHttpHost();
    }

    public static function hostRoot()
    {
        $host = static::host(true);
        return str_replace([
            'https://www.',
            'https://staging.',
            'http://',
        ], '', $host);
    }

    public static function hostOnly($host)
    {
        $host = mb_strtolower($host);
        $host = self::removePrefix($host, 'http://');
        $host = self::removePrefix($host, 'https://');
        return self::removeSuffix($host, '/');
    }

    public static function hostSchema()
    {
        return static::getParameter('host_schema', true) ?: 'https';
    }

    public static function getRequests(Request $request, array $keys)
    {
        $r = [];
        foreach ($keys as $key) {
            $r[$key] = $request->get($key);
        }
        return $r;
    }

    public static function s2j($string, $default = [], bool $throw = false)
    {
        if (!$string) {
            return $default;
        }
        $flags = $throw ? JSON_THROW_ON_ERROR : 0;
        /** @noinspection JsonEncodingApiUsageInspection */
        return json_decode($string, true, flags: $flags);
    }

    public static function s2je($string, $default = [])
    {
        return self::s2j($string, $default, true);
    }

    public static function j2s($json, $default = [], $options = 0)
    {
        if (!$json) {
            $json = $default;
        }
        /** @noinspection JsonEncodingApiUsageInspection */
        return json_encode($json, $options);
    }

    public static function j2se($json, $default = [], $options = 0)
    {
        return self::j2s($json, $default, $options | JSON_THROW_ON_ERROR);
    }

    public static function o2j($object)
    {
        return self::s2j(self::j2s($object));
    }

    public static function j2o($object)
    {
        return json_decode(self::j2s($object), false);
    }

    public static function s2implode($string, $glue = ', ', $default = [])
    {
        return implode($glue, self::s2j($string, $default));
    }

    public static function mergeMixedDoctrineResult(array &$rs)
    {
        foreach ($rs as $i => $r) {
            $rs[$i] = [];
            foreach ($r as $k => $v) {
                if (is_int($k) && is_array($v)) {
                    $rs[$i] = array_merge($rs[$i], $v);
                } else {
                    $rs[$i][$k] = $v;
                }
            }
        }
        return $rs;
    }

    public static function executePdfCpu($path, $outPath) {

        $command = sprintf('PATH=\$PATH:/usr/local/bin:/usr/bin:/opt/homebrew/bin pdfcpu form export %s %s', escapeshellarg($path), $outPath);

        exec($command, $output, $returnCode);
        if (Util::isStaging() || Util::isLocal()) {
            Log::debug($path);
            Log::debug($command);
            Log::debug($returnCode);
            Log::debug($output);
        }
        if ($returnCode !== 0) {
            Log::debug('Failed to obtain the form from the file.');
            return null;
        }

        $jsonData = json_decode(file_get_contents($outPath), true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            Log::debug('Failed to parse the json when obtain the form from the file.');
            return null;
        }

        return $jsonData;
    }
    public static function executeORC($path, $newPath) {
      exec("PATH=\$PATH:/usr/local/bin:/usr/bin ocrmypdf $path $newPath 2>&1", $output, $code);
      // Log::debug($output);
      if (Util::isStaging() || Util::isLocal()) {
        Log::debug('11111111');
        Log::debug($code);
      }
      if ($code === 0) {
        return true;
      }
      return false;
    }
    public static function executeShell(string $cmd, bool $asString = true, string $separator = "\n"): string|array
    {
        $output = [];
        $command = "PATH=\$PATH:/usr/local/bin:/usr/bin $cmd 2>&1";
        exec($command, $output);
        if ($asString) {
            $output = implode($separator, $output);
        }
        return $output;
    }

    public static function executeNodeCommand($cmd)
    {
        $output = [];
        $command = "PATH=\$PATH:/usr/local/bin:/usr/bin NODE_PATH=\$NODE_PATH:/usr/local/lib/node_modules/ node $cmd 2>&1";
        exec($command, $output);
//        if (self::isDev() && self::isHansUser()) {
//            Log::debug('Executing node command', compact('command', 'output'));
//        }
        return $output;
    }

    /**
     * For value-optional args, pass `true` or `null` as the value in the `args`.
     * @param string $command
     * @param array $args
     * @param $returnCode
     *
     * @return string
     * @throws \Exception
     */
    public static function executeCommand(string $command, $args = [], &$returnCode = null)
    {
        $kernel = static::$container->get('kernel');
        $application = new Application($kernel);
        $application->setAutoExit(false);

        $args = array_merge([
            'command' => $command,
        ], $args);

        self::$runInConsole = true;
        $previousConsole = self::$console;

        $input = new ArrayInput($args);
        $output = new BufferedOutput();
        $returnCode = $application->run($input, $output);

        $result = $output->fetch();
        self::$runInConsole = false;

        self::$console = $previousConsole;
        if ($previousConsole) {
            $po = $previousConsole->getOutput();
            $po->writeln('<<<<<<<<<<<<<< ' . $command . ' <<<<<<<<<<<<<<<');
            $po->writeln($result);
            $po->writeln('>>>>>>>>>>>>>> ' . $command . ' >>>>>>>>>>>>>>>');
        }

        return $result;
    }

    public static function field($entity, $field = 'name', $default = null, $args = [])
    {
        if (!$entity) {
            return $default;
        }
        $field = 'get' . ucfirst($field);
        if (!method_exists($entity, $field)) {
            return $default;
        }
        return call_user_func_array([$entity, $field], $args);
    }

    public static function id($entity, $default = null, $field = 'id')
    {
        return self::field($entity, $field, $default);
    }

    public static function checkRoutingNumber($routingNumber = 0)
    {
        $routingNumber = preg_replace('[\D]', '', $routingNumber);
        //only digits
        if (strlen($routingNumber) !== 9) {
            return false;
        }

        $checkSum = 0;
        for ($i = 0, $j = strlen($routingNumber); $i < $j; $i += 3) {
            //loop through routingNumber character by character
            $checkSum += ($routingNumber[$i] * 3);
            $checkSum += ($routingNumber[$i + 1] * 7);
            $checkSum += ($routingNumber[$i + 2]);
        }

        if ($checkSum !== 0 and ($checkSum % 10) === 0) {
            return true;
        }

        return false;
    }

    /**
     * @param $object
     * @param $field
     * @param $array array|string if you want to delete the field
     * @param bool $persist
     * @return mixed
     */
    public static function updateJson($object, $field, $array, $persist = true)
    {
        $getMethod = 'get' . studly_case($field);
        $all = self::s2j($object->$getMethod()) ?? [];
        if (is_string($array)) {
            unset($all[$array]);
        } else if (is_array($array)) {
            $all = array_replace_recursive($all, $array);
        }

        $setMethod = 'set' . studly_case($field);
        $object->$setMethod(self::j2s($all));

        if ($persist) {
            self::persist($object);
        }

        return $object;
    }

    public static function updateMeta($object, $array, $persist = true)
    {
        return self::updateJson($object, 'meta', $array, $persist);
    }

    public static function updateMetaArrayField($object, string $field, array $value, bool $persist = true)
    {
        self::updateMeta($object, $field, false);
        return self::updateMeta($object, [
            $field => $value,
        ], $persist);
    }

    public static function removeMetaWithPrefix($object, $prefix, $persist = true)
    {
        $meta = self::meta($object) ?? [];
        foreach ($meta as $k => $v) {
            if (str_starts_with($k, $prefix)) {
                unset($meta[$k]);
            }
        }

        $object->setMeta(self::j2s($meta));

        if ($persist) {
            self::persist($object);
        }

        return $object;
    }

    /**
     * @param $object
     * @param $field
     * @param null $key
     * @return array|mixed|null
     */
    public static function json($object, $field, $key = null)
    {
        if (!$object) {
            return null;
        }
        $getMethod = 'get' . studly_case($field);
        $all = self::s2j($object->$getMethod());
        if (!$all) {
            return null;
        }

        if ($key) {
            if (!isset($all[$key])) {
                return null;
            }

            return $all[$key];
        }

        return $all;
    }

    public static function meta($object, $key = null)
    {
        return self::json($object, 'meta', $key);
    }

    public static function fixJsonBeforeEncoding($value)
    {
        if (is_array($value)) {
            foreach ($value as $k => $v) {
                $value[$k] = static::fixJsonBeforeEncoding($v);
            }
        } else if (is_float($value)) {
            if (is_nan($value) || is_infinite($value)) {
                $value = 0;
            }
        }
        return $value;
    }

    public static function trimArray($arr)
    {
        return array_map(function ($col) {
            return trim($col);
        }, $arr);
    }

    public static function filterArray($arr, $preserveKey = false)
    {
        $rs = array_filter($arr, function ($col) {
            return (bool)$col;
        });
        if ($preserveKey) {
            return $rs;
        }
        return array_values($rs);
    }

    public static function filterParamsArray(array $params)
    {
        return array_filter($params, function ($v) {
            return $v !== null && $v !== '';
        });
    }

    public static function cleanUnicodeStringExtra(?string $str)
    {
        $str = self::cleanUtf8String($str);
        $str = preg_replace('/\\\\u[0-9a-zA-Z]{4}/u', '', $str);
        return $str;
    }

    public static function cleanUtf8String($str)
    {
        if (!$str) {
            return $str;
        }
        $str = mb_convert_encoding($str, 'UTF-8', 'UTF-8');
        $str = iconv("UTF-8", "UTF-8//IGNORE", $str);
        $str = preg_replace('/[\x{FFFd}-\x{FFFe}]/u', '', $str);
        $str = preg_replace('/\p{Cf}/u', '', $str);
        return self::removeEmoji($str);
    }

    public static function removeEmoji($string)
    {
        // Match Enclosed Alphanumeric Supplement
        $regex_alphanumeric = '/[\x{1F100}-\x{1F1FF}]/u';
        $clear_string = preg_replace($regex_alphanumeric, '', $string);

        // Match Miscellaneous Symbols and Pictographs
        $regex_symbols = '/[\x{1F300}-\x{1F5FF}]/u';
        $clear_string = preg_replace($regex_symbols, '', $clear_string);

        // Match Emoticons
        $regex_emoticons = '/[\x{1F600}-\x{1F64F}]/u';
        $clear_string = preg_replace($regex_emoticons, '', $clear_string);

        // Match Transport And Map Symbols
        $regex_transport = '/[\x{1F680}-\x{1F6FF}]/u';
        $clear_string = preg_replace($regex_transport, '', $clear_string);

        // Match Supplemental Symbols and Pictographs
        $regex_supplemental = '/[\x{1F900}-\x{1F9FF}]/u';
        $clear_string = preg_replace($regex_supplemental, '', $clear_string);

        // Match Miscellaneous Symbols
        $regex_misc = '/[\x{2600}-\x{26FF}]/u';
        $clear_string = preg_replace($regex_misc, '', $clear_string);

        // Match Dingbats
        $regex_dingbats = '/[\x{2700}-\x{27BF}]/u';
        $clear_string = preg_replace($regex_dingbats, '', $clear_string);

        return $clear_string;
    }

    public static function isoDate($dateTime)
    {
        if (!$dateTime) {
            return null;
        }
        return self::formatDateTime($dateTime, self::DATE_FORMAT_ISO_DATE_TIME);
    }

    public static function formatLocalDate(?DateTime $dateTime, DateTimeZone $timezone, $format = 'Y-m-d H:i:s e'): string
    {
        if (!$dateTime) {
            return '';
        }
        $local = self::toTimezone($dateTime, $timezone);
        return self::formatDateTime($local, $format, $timezone);
    }

    public static function formatDate($dateTime = null, $timezone = null, $format = self::DATE_FORMAT)
    {
        return self::formatDateTime($dateTime, $format, $timezone);
    }

    public static function formatDateTime($date = null, $format = self::DATE_TIME_FORMAT, $timezone = null)
    {
        $date = self::dateObject($date);
        if (!$date) {
            return '';
        }
        if ($date instanceof \DateTime) {
            if (!$format) {
                $format = self::DATE_TIME_FORMAT;
            }
            if (!$timezone) {
                $user = self::user();
                if ($user) {
                    $timezone = $user->getTimezone();
                }
                if (!$timezone) {
                    $timezone = date_default_timezone_get();
                }
            }
            if (is_string($timezone)) {
                $timezone = new DateTimeZone($timezone);
            }
            return $date->setTimezone($timezone)
                ->format($format);
        }
        return '' . $date;
    }

    public static function formatApiDateTime($date = null)
    {
        return self::formatDateTime($date, self::DATE_FORMAT_ISO_DATE_TIME) ?: null;
    }

    public static function formatFullDateTime($date = null)
    {
        return self::formatDateTime($date, self::DATE_TIME_FORMAT_FULL) ?: null;
    }

    public static function dateObject($date)
    {
        if (!$date) {
            return null;
        }
        if ($date instanceof \DateTime) {
            return $date;
        }

        if (is_numeric($date)) {
            $date = Carbon::createFromTimestamp($date);
        } else if (is_string($date)) {
            $date = new \DateTime($date);
        }

        return $date;
    }

    public static function carbonObject($date): ?Carbon
    {
        $date = self::dateObject($date);
        if (!$date) {
            return null;
        }
        if ($date instanceof Carbon) {
            return $date;
        }
        if ($date instanceof DateTime) {
            return Carbon::instance($date);
        }
        return new Carbon($date);
    }

    public static function usort(&$data, array $criteria, callable $callback = null)
    {
        foreach ($criteria as $k => $v) {
            if (is_bool($v)) {
                $criteria[$k] = $v ? 'asc' : 'desc';
            }
        }
        usort($data, function ($a, $b) use ($criteria, $callback) {
            foreach ($criteria as $what => $order) {
                if (is_callable($callback)) {
                    $av = $callback($a, $what);
                    $bv = $callback($b, $what);
                } else {
                    $av = $a[$what] ?? '';
                    $bv = $b[$what] ?? '';
                    if (is_string($a[$what])) {
                        $av = strtolower($a[$what]);
                    }
                    if (is_string($b[$what])) {
                        $bv = strtolower($b[$what]);
                    }
                }
                if ($av === $bv) {
                    continue;
                }
                return ((strtolower($order) === 'desc') ? -1 : 1) * (($av < $bv) ? -1 : 1);
            }
            return 0;
        });
    }

    public static function last(Collection $collection)
    {
        $max = 0;
        $maxObj = null;
        foreach ($collection as $obj) {
            $id = $obj->getId();
            if ($id > $max) {
                $max = $id;
                $maxObj = $obj;
            }
        }
        return $maxObj;
    }

    public static function asc(Collection $collection, $field = 'id', $asc = true)
    {
        $method = 'get' . ucfirst($field);

        $items = [];
        foreach ($collection as $obj) {
            $id = $obj->$method();
            $items[$id] = $obj;
        }
        if ($asc) {
            ksort($items);
        } else {
            krsort($items);
        }
        return new ArrayCollection(array_values($items));
    }

    public static function desc(Collection $collection, $field = 'id')
    {
        return self::asc($collection, $field, false);
    }

    public static function ascName(Collection $collection, $field = 'name')
    {
        return self::asc($collection, $field);
    }

    public static function getEntityName($class)
    {
        $name = ClassUtils::getRealClass($class);
        return str_replace("\\Entity\\", ':', $name);
    }

    public static function uploadLimit()
    {
        $file = (int)ini_get('max_file_uploads');
        $post = (int)ini_get('post_max_size');

        return min($file, $post) . 'MB';
    }

    public static function logTime($tag = null)
    {
        global $logStart;
        global $logTimes;

        if ($tag === null) {
            $logStart = microtime(true);
            $logTimes = [];
            $tag = 'start';
        }

        $logTimes[] = [$tag, microtime(true) - $logStart];

        return $logTimes;
    }

    public static function line($message, $type = null, $context = [])
    {
        self::consoleLine($message, $type, $context);
    }

    public static function consoleLine($message, $type = null, $context = [], $log = true)
    {
        if (!self::$console) {
            Log::debug($message, $context);
            return;
        }
        self::$console->line($message, $type, $context, $log);
    }

    public static function suffixSlash($str, $ch = '/')
    {
        if (!Util::endsWith($str, $ch)) {
            $str .= $ch;
        }
        return $str;
    }

    public static function cacheDir($subDir = '')
    {
        $dir = self::suffixSlash(self::getParameter('cache_root'));
        if ($subDir) {
            $subDir = self::suffixSlash($subDir);
            $dir = self::ensureFile($dir . $subDir, true);
        }
        return $dir;
    }

    public static function logDir()
    {
        $dir = self::suffixSlash(self::getParameter('log_root'));
        return self::ensureFile($dir, true);
    }

    /**
     * Public directory
     *
     * @param string $subDir
     *
     * @return mixed|string
     * @throws PortalException
     */
    public static function uploadDir($subDir = '')
    {
        $dir = self::suffixSlash(self::getParameter('upload_root'));
        $dir = self::ensureFile($dir, true);
        if ($subDir) {
            $subDir = self::suffixSlash($subDir);
            $dir = self::ensureFile($dir . $subDir, true);
        }
        return $dir;
    }

    /**
     * Protected directory
     *
     * @param string $subDir
     *
     * @return mixed|string
     * @throws PortalException
     */
    public static function secureDir($subDir = '')
    {
        $dir = self::suffixSlash(self::getParameter('secure_root'));
        $dir = self::ensureFile($dir, true);
        if ($subDir) {
            $subDir = self::suffixSlash($subDir);
            $dir = self::ensureFile($dir . $subDir, true);
        }
        return $dir;
    }

    public static function rootDir()
    {
        $root = self::getParameter('kernel.project_dir');
        return self::ensureSuffix($root, '/');
    }

    public static function ensureFile($path, $dir = false)
    {
        if (!file_exists($path)) {
            if ($dir) {
                if (!mkdir($path) || !is_dir($path)) {
                    throw new PortalException('Failed to create folder.');
                }
            } else {
                touch($path);
            }
        }
        @chmod($path, 0777);
        return $path;
    }

    public static function verifyPathSecurity($path, $checkExistence = true, $throw = true)
    {
        $origin = $path;
        $error = false;
        do {
            if ($checkExistence) {
                $path = strtolower(realpath($path));
            } else {
                if (file_exists($path)) {
                    $path = strtolower(realpath($path));
                } else {
                    $path = strtolower($path);
                }
            }
            $cacheDir = self::cacheDir();
            if (self::containsSubString($path . '/', [
                '..',
//                '/var/', '/mnt/', '/secure/',
                '/config/', '/etc/', '/log/',
                '/logs/', '/session/', '/sessions/',
                '/conf/',
                '/db/', '/mysql/', '/nginx/',
                '/php/', '/redis/', '/bin/',
                '/src/', '/tests/', '/cache/',
                '/sql/', '/env/', '/database/',
                '/.git/',
            ]) && !self::startsWith($path, $cacheDir . 'id-verify/')) {
                $error = 1;
                break;
            }
            if (self::hasSuffix($path, [
                'composer.json',
                'package.json',
                'package-lock.json',
                'config.js',
                '.node-version',
                '.lock', '.sample', '.build', '.sh',
                '.config', '.conf', '.ini', '.yml',
                '.xml', '.log', '.dist', '.php',
                '.inc', '.md', '.sql', '.back',
                '.db', '.pem', '.p12', '.cnf',
                '.frm', '.ibd', '.myd', 'Dockerfile',
                '.rdb', '.gitignore', '.git', '.gitkeep',
                '.gitmodules', '.twig', '.jar', '.phar',
                '.gz', '.tgz', '.out', '.cert', '.cer',
                '.py', '.local', '.yaml', '.test',
            ])) {
                $error = 5;
                break;
            }
            $ext = pathinfo($path, PATHINFO_EXTENSION);
            if (!$ext) {
                $error = 2;
                break;
            }
            if (!in_array($ext, [
                'csv', 'xls', 'xlsx',
                'doc', 'docx', 'txt',
                'ach', 'pdf', 'png',
                'jpg', 'jpeg', 'gif',
                'zip', 'dat', 'mf',
                'html',
            ])) {
                $error = 3;
                break;
            }
            $secure = self::secureDir();
            if (!self::hasPrefix($path, [
                strtolower(self::uploadDir()),
                strtolower($secure . 'idology_file/'),
                strtolower($secure . 'id_verification/'),
                strtolower($secure . 'splash_page_file/'),
                strtolower($secure . 'mex_dd_pdf/parsed/'),
                strtolower($secure . 'mex_dd_pdf/manual/'),
                strtolower($secure . 'mex_w2s_pdf/parsed/'),
                strtolower($secure . 'mex_w2s_pdf/manual/'),
                strtolower($secure . 'mex_receip_pdf/'),
                strtolower($secure . 'mex_intermex_form_1025/'),
                strtolower($secure . 'public/'),
                strtolower($secure . 'spendr_merchant_rep/'),
                strtolower($secure . 'spendr_merchant_doc/'),
                strtolower($secure . 'spendr_consumer_doc/'),
                strtolower($secure . 'spendr_idology_file/'),
                strtolower($secure . 'spendr_reward_targets/'),
                strtolower($secure . 'spendr_transactions/'),
                strtolower($secure . 'Spendr ACH Returns/'),
                strtolower(self::rootDir() . 'web/'),
                strtolower($cacheDir . 'id-verify/'),
                strtolower($secure . 'transfer/')
            ])) {
                $error = 4;
                break;
            }
            if ($checkExistence) {
                if (!file_exists($origin)) {
                    $error = 199;
                    break;
                }
            }
        } while (false);

        if (!$error) {
            return true;
        }

        Log::warn('Suspicious path: ' . $error . ' - ' . $origin, [
            'path' => $path,
//            'allow' => [
//                strtolower(self::uploadDir()),
//                strtolower(self::secureDir('public')),
//                strtolower(self::rootDir() . 'web/'),
//            ],
        ]);
        if ($throw) {
            throw PortalException::temp('Invalid path! (' . $error . ')');
        }
        return false;
    }

    public static function verifyPathExistence($path, $throw = true)
    {
        if (file_exists($path)) {
            return true;
        }

        Log::warn('Not found path: ' . $path);
        if ($throw) {
            throw PortalException::temp('File not found!');
        }
        return false;
    }

    /**
     * @return Generator
     */
    public static function faker()
    {
        $faker = Factory::create();
        $faker->seed(NULL);
        return $faker;
    }

    public static function fakeImage($number = null)
    {
        $number = $number ?: time();
        return 'http://gravatar.com/avatar/' . $number . '?size=128&d=identicon';
    }

    public static function fakePicsum($width = null, $height = null, $number = null)
    {
        $faker = static::faker();

        global $picsums;
        if (empty($picsums)) {
            $client = new Client();
            $response = $client->get('https://picsum.photos/v2/list?page=' . $faker->numberBetween(0, 9) . '&limit=100');
            $content = $response->getBody()->getContents();
            $picsums = json_decode($content, true);
        }

        if (!$number) {
            $number = $faker->numberBetween(0, count($picsums) - 1);
            $number = $picsums[$number]['id'];
        }
        if (!$width) {
            $width = $faker->numberBetween(200, 400);
        }
        if (!$height) {
            $height = $faker->numberBetween(200, 400);
        }
        return 'https://picsum.photos/' . $width . '/' . $height . '?image=' . $number;
    }

    public static function inputFilter($str)
    {
        $str = str_replace('\'', '\'\'', $str);
        return trim($str);
    }

    public static function startsWith($haystack, $needle)
    {
        return str_starts_with($haystack, $needle);
    }

    public static function hasPrefix($str, $prefixes)
    {
        if (!is_array($prefixes)) {
            $prefixes = [$prefixes];
        }
        foreach ($prefixes as $prefix) {
            if (self::startsWith($str, $prefix)) {
                return true;
            }
        }
        return false;
    }

    public static function ensurePrefix($str, $prefix)
    {
        if (self::hasPrefix($str, [$prefix])) {
            return $str;
        }
        return $prefix . $str;
    }

    public static function removePrefix($str, $prefix)
    {
        if (self::startsWith($str, $prefix)) {
            $str = mb_substr($str, mb_strlen($prefix));
        }
        return $str;
    }

    public static function endsWith($haystack, $needle)
    {
        return str_ends_with($haystack, $needle);
    }

    public static function hasSuffix($str, $suffixes)
    {
        if (!is_array($suffixes)) {
            $suffixes = [$suffixes];
        }
        foreach ($suffixes as $suffix) {
            if (self::endsWith($str, $suffix)) {
                return true;
            }
        }
        return false;
    }

    public static function ensureSuffix($str, $suffix)
    {
        if (self::hasSuffix($str, [$suffix])) {
            return $str;
        }
        return $str . $suffix;
    }

    public static function removeSuffix($str, $suffix)
    {
        if (self::endsWith($str, $suffix)) {
            $str = mb_substr($str, 0, mb_strlen($str) - mb_strlen($suffix));
        }
        return $str;
    }

    public static function appendQueryParam($url, $name, $value)
    {
        $sep = '?';
        if (strpos($url, '?') !== false) {
            $sep = '&';
        }
        $url .= $sep . $name . '=' . $value;
        return $url;
    }

    public static function mergeWhitespaces($str)
    {
        return preg_replace('!\s+!', ' ', $str);
    }

    public static function keyAbstract($key, $length = 2)
    {
        if (!$key || strlen($key) <= $length * 5) {
            return '***';
        }
        return substr($key, 0, $length) . '...' . substr($key, strlen($key) - $length);
    }

    public static function maskArrayField(array &$arr, $key, $otherFormat = true, $type = 'right')
    {
        if (is_array($key)) {
            foreach ($key as $k) {
                self::maskArrayField($arr, $k, $type);
            }
            return;
        }

        $s = Stringy::create($key);
        $newKeys = [
            $key,
        ];
        if ($otherFormat) {
            $newKeys = array_merge($newKeys, [
                ucfirst($key),
                ''. $s->toLowerCase(),
                ''. $s->toUpperCase(),
                ''. $s->toTitleCase(),
                ''. $s->dasherize(),
                ''. $s->underscored(),
            ]);
        }
        $newKeys = array_values(array_unique($newKeys));
        foreach ($newKeys as $newKey) {
            if (array_key_exists($newKey, $arr)) {
                if (is_array($arr[$newKey])) {
                    foreach ($arr[$newKey] as $j => $value) {
                        $value = (string)($value ?: '');
                        $arr[$newKey][$j] = self::maskString($value);
                    }
                } else {
                    $value = (string)($arr[$newKey] ?: '');
                    $arr[$newKey] = self::maskString($value);
                }
            }
        }
    }

    public static function maskString($value, $type = 'right', $leave = null)
    {
        $length = strlen($value);
        $leave = $leave ?? ($length > 12 ? 4 : 0);
        if ($type === 'right') {
            return '****' . substr($value, $length - $leave);
        }

        return substr($value, 0, $leave) . '****';
    }

    public static function formatJSON($data)
    {
        if (is_string($data)) {
            $data = json_decode($data);
        }
        return json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    }

    public static function intPhone($phone, $removeExtra = false, $length = null)
    {
        if (false !== $removeExtra) {
            if (is_numeric($removeExtra)) {
                $phone = str_replace($removeExtra . '+ ', '', $phone);
            }
            $phone = preg_replace('/\sx\d+$/', '', $phone);
        }
        $number = (int)str_replace(['-', ' ', '(', ')', '.', 'x', '+'], '', $phone);

        if ($length !== null) {
            $number = str_pad($number,  $length, '0', STR_PAD_LEFT);
            $number = substr($number, strlen($number) - $length, $length);
        }

        return $number;
    }

    public static function e164Phone($phone)
    {
        return '+' . self::intPhone($phone);
    }

    public static function maxLength($str, $max)
    {
        if ($str === false || $str === null) {
            return '';
        }
        $len = mb_strlen('' . $str);
        if ($len > $max) {
            return mb_substr($str, 0, $max);
        }
        return $str;
    }

    public static function intPostalCode(?string $code): int
    {
        return (int)preg_replace('/\D/', '', $code ?? '');
    }

    public static function cleanPostalCode(?string $code, ?Country $country = null): string
    {
        $cleaned = str_replace([
            ' ', '-', '_', ',', '.', '(', ')', 'x', '+', 'X',
            "\n", "\r", "\t", '/', '\\', 'n/a', 'N/A',
        ], '', $code ?? '');
        if ($country && $country->getIsoCode() === 'SA') {
            $cleaned = substr($cleaned, 0, 5);
        }
        return $cleaned;
    }

    public static function humanize($str)
    {
        return (string)Stringy::create($str)->humanize();
    }

    public static function title($str, $keepCase = false)
    {
        if (!$keepCase) {
            $str = mb_strtolower($str);
        }
        return (string)Stringy::create($str)->delimit(' ')->toTitleCase();
    }

    public static function ascii($str, $toLowerCase = false)
    {
        if (!$str) {
            return '';
        }
        $s = Stringy::create($str)->toAscii();
        if ($toLowerCase) {
            $s = $s->toLowerCase();
        }
        return (string)$s;
    }

    public static function matchable($str)
    {
        return self::asciiLower(trim($str));
    }

    public static function asciiLower($str)
    {
        return self::ascii($str, true);
    }

    public static function inputPhone($number, $country = null)
    {
        if ($country && $country instanceof Country) {
            $country = $country->getIsoCode();
        }
        try {
            return self::formatFullPhone($number, $country);
        } catch (\Exception $e) {
            Log::debug($e->getMessage(), [
                'number' => $number,
                'country' => $country,
            ]);
            return $number;
        }
    }

    public static function checkPhone($number, $country = null) {
        if ($country && $country instanceof Country) {
          $country = $country->getIsoCode();
        }
        if ($country === 'AN') {
          $country = 'CW';
        }
        $phoneUtil = PhoneNumberUtil::getInstance();
        return $phoneUtil->isValidNumber($phoneUtil->parse($number, $country));
    }

    public static function parsePhoneNumber(string $number, ?Country $country = null): ?\libphonenumber\PhoneNumber
    {
        $number = trim($number);
        if (!$number || $number === '+') {
            return null;
        }

        $phoneUtil = PhoneNumberUtil::getInstance();
        try {
            return $phoneUtil->parse($number, $country?->getIsoCode());
        } catch (\Throwable) {
            if ($country) {
                try {
                    return $phoneUtil->parse($number);
                } catch (\Throwable) {
                    return null;
                }
            }
            return null;
        }
    }

    public static function getNationalPhoneNumber(?string $number, ?Country $country = null): string
    {
        return self::getPhoneDigitsWithOutCountryIso($number, $country, auto: true);
    }

    public static function getPhoneDigitsWithOutCountryIso($number, ?Country $country = null,
                                                           bool $auto = false): string
    {
        $number = trim($number);
        if (!$number || $number === '+') {
            return '';
        }

        if ($auto) {
            $pn = self::parsePhoneNumber($number, $country);
            if ($pn) {
                return $pn->getNationalNumber() ?? '';
            }
        }

        if (self::startsWith($number, '+')) {
            $list = explode(' ', $number);
            $res = count($list) ? str_replace(' ', '', str_replace($list[0], '', $number)) : str_replace('+', '', $number);
            return str_replace('-', '', $res);
        } else {
            return str_replace('+', '', $number);
        }
    }

    public static function formatFullPhone($number, $countryCode = null, $format = PhoneNumberFormat::INTERNATIONAL)
    {
        if (!$number || $number === '+') {
            return '';
        }
        try {
            $nb = self::cleanUnicodeStringExtra($number);
            if (self::startsWith($nb, '+')) {
                $nb = '+' . ltrim(substr($nb, 1), '0');
            }

            if ($countryCode === 'AN') {
                $countryCode = 'CW';
            } else if ($countryCode instanceof Country) {
                $countryCode = $countryCode->getIsoCode();
            }

            $phoneUtil = PhoneNumberUtil::getInstance();
            $pn = $phoneUtil->parse($nb, $countryCode);
            return $phoneUtil->format($pn, $format);
        } catch (\Exception $ex) {
            $msg = 'Failed to format phone number `' . $number . '`: ' . $ex->getMessage();
            Log::warn($msg);
            throw PortalException::tempPage($msg);
        }
    }

    public static function formatFullPhoneDigits($number, $countryCode = null)
    {
        $s = self::formatFullPhone($number, $countryCode, PhoneNumberFormat::E164);
        if ($s) {
            $s = str_replace_first('+', '', $s);
        }
        return $s;
    }

    public static function parseCountryFromPhoneNumber(string|null $phone): Country|null
    {
        $code = self::parseCountryCodeFromPhoneNumber($phone);
        if (!$code) {
            return null;
        }
        return Country::findByCode($code);
    }

    public static function parseCountryCodeFromPhoneNumber(string|null $phone): string|null
    {
        if (!$phone) {
            return null;
        }
        $phone = self::ensurePrefix($phone, '+');
        $phoneUtil = PhoneNumberUtil::getInstance();

        try {
            $phoneNumber = $phoneUtil->parse($phone);
            if (!$phoneNumber) {
                return null;
            }
            return $phoneUtil->getRegionCodeForNumber($phoneNumber);
        } catch (\Exception $ex) {
            Log::warn('Failed to parse the country from phone number ' . $phone . ': ' . $ex->getMessage());
            return null;
        }
    }

    public static function setUser(?User $user)
    {
        /** @noinspection PhpDeprecationInspection */
        self::$user = $user;
    }

    /**
     * @return User|null
     * @noinspection PhpDeprecationInspection
     */
    public static function user()
    {
        if (self::isCronJob()) {
            return self::$cronUser ?? self::getDefaultMasterAdmin();
        }

        if (self::$user) {
            return self::$user;
        }

        if (!self::$security) {
            return null;
        }

        if (!is_object($user = self::$security->getUser())) {
            return null;
        }

        return $user;
    }

    public static function invoker()
    {
        if (self::$apiInvoker) {
            return self::$apiInvoker;
        }
        return self::user();
    }

    public static function appUser()
    {
        if (self::$appUser) {
            return self::$appUser;
        }

        $u = self::invoker();
        if ($u) {
            self::$appUser = $u;
            return $u;
        }

        $request = self::request();
        $key = 'x-token';
        $token = $request->headers->get($key);
        if (!$token) {
            $token = $request->get($key);
        }
        if (!$token) {
            return null;
        }
        $token = str_replace('Bearer ', '', $token);
        $rs = self::em()->getRepository(UserToken::class)->findBy([
            'token' => $token,
        ], null, 1);
        if (!$rs) {
            return null;
        }
        /** @var UserToken $ut */
        $ut = $rs[0];
        self::$appUser = $ut->getUser();
        return self::$appUser;
    }

    public static function clearCookie(Request $request = null, $response = '/login')
    {
        if (is_string($response)) {
            $response = new RedirectResponse($response);
        }

        if (!$request) {
            $request = self::request();
        }

        $session = $request->getSession();
        if ($session) {
            $session->invalidate();
        }

        foreach ($request->cookies as $cookieName => $cookieData) {
            $response->headers->clearCookie($cookieName);
        }

        return $response;
    }

    public static function auth(User $user, #[\SensitiveParameter] $pwd)
    {
        return Util::encodePassword($user, $pwd) === $user->getPassword();
    }

    public static function encodePassword(User $user, #[\SensitiveParameter] $pwd)
    {
        $encoder = Util::getPasswordEncoder();
        return $encoder->hashPassword($user, $pwd);
    }

    public static function getPasswordEncoder()
    {
        return Util::$container->get('sha256salted_encoder');
    }

    public static function generatePassword(#[\SensitiveParameter] $prefix = '')
    {
        $A = self::randString(3, 'ABCDEFGHIJKLMNOPQRSTUVWXYZ');
        $a = self::randString(3, 'abcdefghijklmnopqrstuvwxyz');
        $d = self::randString(3, '0123456789');
        $c = self::randString(3, '!@#$%^&*()-=+_');
        $p = str_split($A . $a . $d . $c);
        shuffle($p);
        return $prefix . implode('', $p);
    }

    public static function generateBackupPassword($fileName)
    {
       $pwd = self::getKmsParameter('backup_pwd');
       $fileName .= $pwd;
       return hash_hmac('md5', $fileName, $pwd);
    }

    /**
     * @return User|null
     */
    public static function getImpersonatingUser()
    {
        $impersonatingUser = null;
        $em = self::em();
        try {
            if (self::$security->isGranted('IS_IMPERSONATOR')) {
                $token = self::$security->getToken();
                if ($token instanceof SwitchUserToken) {
                    $impersonatingUser = $token->getOriginalToken()->getUser();
                }
            }

            if ($impersonatingUser === 'anon.') {
                $impersonatingUser = self::user();
            }

            if ($impersonatingUser) {
                $impersonatingUser = $em->getRepository(\SalexUserBundle\Entity\User::class)
                    ->find($impersonatingUser->getId());
            }
        } catch (\Exception $e) {
        }

        if (!$impersonatingUser) {
            $xi = self::request()->headers->get('x-impersonating');
            if ($xi) {
                $impersonatingUser = $em->getRepository(\SalexUserBundle\Entity\User::class)
                    ->find($xi);
            }
        }

        return $impersonatingUser;
    }

    public static function isMasterAdminLoggedInAs()
    {
        $m = self::getImpersonatingUser();
        if (!$m) {
            return false;
        }
        return $m->isMasterAdmin();
    }

    /**
     * @param $timeString
     * @return Carbon
     */
    public static function tm($timeString)
    {
        $time = strtotime(trim($timeString));
        if ($time <= -62135596800) { // 0001-01-01 00:00:00+00
            return null;
        }
        return Carbon::createFromTimestamp($time);
    }

    public static function version($force = false)
    {
        if (isset($GLOBALS['_v'])) {
            return $GLOBALS['_v'];
        }
        $v = time();
        if (self::$container && self::isLocal()) {
            return $v;
        }
        if ($force || !self::isDev()) {
            $git = __DIR__ . '/../../../../.git/index';
            if (file_exists($git)) {
                $v = filemtime($git);
            } else {
                 $revision = __DIR__ . '/../../../../.revision';
                 if (file_exists($revision)) {
                     $v = file_get_contents($revision);
                 }
            }
        }
        $v = trim('' . $v);
        $vs = explode("\n", $v);
        if ($vs) {
            $v = $vs[0];
        }
        $GLOBALS['_v'] = $v;
        return $v;
    }

    public static function filenameFilter($name)
    {
        $str = str_replace([
            '/',
            "\\",
        ], ' ', $name);
        return trim($str);
    }

    public static function markEntityToUpload($entity, $file)
    {
        $uploadableManager = Util::$container->get('stof_doctrine_extensions.uploadable.manager');
        $uploadableManager->markEntityToUpload($entity, $file);

        return $entity;
    }

    public static function affirmArray($data)
    {
        return is_array($data) ? $data : [$data];
    }

    public static function ensureArray($data)
    {
        if (is_array($data) && array_key_exists(0, $data)) {
            return $data;
        }
        if (is_array($data) && !$data) {
            return $data;
        }
        return [
            $data,
        ];
    }

    public static function ensureArrayField($data, $routes, $field, $error, $index = 0)
    {
        $routes = $routes ?: [];
        $count = count($routes);
        for ($i = 0; $i <= $count; $i++) {
            if ($i > 0) {
                $key = $routes[$i - 1];
                $data = $data[$key] ?? null;
            }
            if (!isset($data)) {
                throw new PortalException('Invalid ' . $error);
            }
        }
        $rows = static::ensureArray($data);
        if (!isset($rows[$index][$field])) {
            throw new PortalException('Invalid format of ' . $error);
        }
        return $rows[$index][$field];
    }

    /**
     * Remove the special characters in string to be a part of file path
     *
     * @param $str
     *
     * @return mixed
     */
    public static function normalizePath($str)
    {
        return preg_replace('/[^a-z0-9_]+/', '-', strtolower($str));
    }

    public static function disableSoftDeletable()
    {
        $filters = self::em()->getFilters();
        if ($filters->isEnabled('softdeleteable')) {
            $filters->disable('softdeleteable');
        }
    }

    public static function enableSoftDeletable()
    {
        $filters = self::em()->getFilters();
        if (!$filters->isEnabled('softdeleteable')) {
            $filters->enable('softdeleteable');
        }
    }

    public static function bypassSoftDeletable(callable $callback)
    {
        $em = static::em();

        // initiate an array for the removed listeners
        $originalEventListeners = [];

        // cycle through all registered event listeners
        foreach ($em->getEventManager()->getListeners() as $eventName => $listeners) {
            foreach ($listeners as $listener) {
                if ($listener instanceof \Gedmo\SoftDeleteable\SoftDeleteableListener) {

                    // store the event listener, that gets removed
                    $originalEventListeners[$eventName] = $listener;

                    // remove the SoftDeletableSubscriber event listener
                    $em->getEventManager()->removeEventListener($eventName, $listener);
                }
            }
        }

        $callback();

        // re-add the removed listener back to the event-manager
        foreach ($originalEventListeners as $eventName => $listener) {
            $em->getEventManager()->addEventListener($eventName, $listener);
        }
    }

    public static function in_array($item, $array)
    {
        if (is_object($item) && method_exists($item, 'getId')) {
            $id = $item->getId();
            foreach ($array as $a) {
                if (self::methodExists($a, 'getId')) {
                    if ($a->getId() === $id) {
                        return true;
                    }
                }
            }
            return false;
        }
        return in_array($item, $array, true);
    }

    public static function vichFullPath($entity, $field)
    {
        /** @var StorageInterface $storage */
        $storage = self::$container->get('vich_uploader.storage');
        return $storage->resolveUri($entity, $field);
    }

    public static function isUrl($url)
    {
        return self::hasPrefix(strtolower($url), [
            'http://',
            'https://',
        ]);
    }

    public static function isPathOrUrl($url)
    {
        if (!$url) {
            return false;
        }
        $url = strtolower($url);
        return self::hasPrefix($url, [
            'http://',
            'https://',
            '/',
        ]);
    }

    public static function fixFilePath($url, $withHost = false)
    {
        if (!$url || $url === 'Error when upload image, please try later!') {
            return '';
        }
        if ($url instanceof Attachment) {
            $url = $url->getPath();
        }
        $_url = strtolower($url);
        if (
            !$url
            || self::startsWith($_url, 'http://')
            || self::startsWith($_url, 'https://')
        ) {
            return $url;
        }
        $roots = [
            self::uploadDir(),
            self::secureDir(),
            '/mnt/secure/span/',
        ];

        if (self::isDevDevice()) {
            $roots[] = '/var/upload/span/';
            $roots[] = '/var/secure/span/';
            $roots[] = '/Volumes/ExtendDisk/Library/var/upload/span/';
            $roots[] = '/Volumes/ExtendDisk/Library/var/secure/span/';
        }

        foreach ($roots as $root) {
            if (self::startsWith($url, $root)) {
                $url = str_replace($root, 'files/', $url);
                break;
            }
        }
        if (!self::startsWith($url, '/')) {
            $url = '/' . $url;
        }
        if ($withHost) {
            $url = self::host() . $url;
        }
        return $url;
    }

    public static function isUrlExists($url)
    {
        $file_headers = @get_headers($url);
        $row = $file_headers[0] ?? '';
        $redirect = $file_headers[7] ?? '';
        $errors = [
            '400 Bad Request',
            '404 Not Found',
            '403 Forbidden',
            '500 Internal Server Error',
            '503 Service Unavailable',
        ];
        if (self::containsSubString($row, $errors)) {
            return false;
        }
        if (self::containsSubString($row, [
                '301 Moved Permanently',
                '302 Found',
            ]) && self::containsSubString($redirect, $errors)) {
            return false;
        }
        return true;
    }

    public static function fileExtension($path)
    {
        return pathinfo($path, PATHINFO_EXTENSION);
    }

    public static function fileBaseName($path)
    {
        return pathinfo($path, PATHINFO_BASENAME);
    }

    public static function getFileBase64($path, $withCheck = true)
    {
        if ($withCheck) {
            self::verifyPathSecurity($path);
        }
        $content = file_get_contents($path);
        return base64_encode($content);
    }

    public static function stringMeta($string)
    {
        return [
            'type' => gettype($string),
            'length' => strlen($string),
        ];
    }

    public static function eq($a, $b)
    {
        if (is_object($a) && is_object($b)) {
            if ($a instanceof \DateTime && $b instanceof \DateTime) {
                return $a->getTimestamp() === $b->getTimestamp();
            }
            if (method_exists($a, 'getId') && method_exists($b, 'getId')) {
                return $a->getId() === $b->getId();
            }
        }
        if (is_numeric($b) && is_object($a) && method_exists($a, 'getId')) {
            return $a->getId() === (int)$b;
        }
        if (is_numeric($a) && is_object($b) && method_exists($b, 'getId')) {
            return $b->getId() === (int)$a;
        }
        return $a === $b;
    }

    public static function neq($a, $b)
    {
        return !static::eq($a, $b);
    }

    public static function eqNum($a, $b)
    {
        if (is_numeric($a) && is_numeric($b)) {
            return number_format($a, 6) === number_format($b, 6);
        }
        return $a === $b;
    }

    public static function isZeroNum($v)
    {
        $str = (string)$v;
        $str = trim($str, '0.,');
        if ($str === '') {
            return true;
        }
        return self::eqNum($v, 0.0);
    }

    public static function includes($array, $o)
    {
        /** @var array|Collection $array */
        foreach ($array as $c) {
            if (self::eq($c, $o)) {
                return true;
            }
        }
        return false;
    }

    public static function containsSubString($full, $subs)
    {
        foreach ($subs as $item) {
            if (str_contains($full, $item)) {
                return true;
            }
        }
        return false;
    }

    public static function flattenArray(array $array, $key)
    {
        return array_map(function ($item) use ($key) {
            return $item[$key] ?? null;
        }, $array);
    }

    public static function toKeyedArray(array $indexedArray)
    {
        $result = [];
        foreach ($indexedArray as $i => $v) {
            $result[$v] = $i;
        }
        return $result;
    }

    public static function keyArrayBy(array $array, $field = 'name')
    {
        $result = [];
        foreach ($array as $item) {
            $v = $item[$field];
            $result[$v] = $item;
        }
        return $result;
    }

    public static function intersect($array1, $array2)
    {
        $result = [];
        foreach ($array1 as $item) {
            if (self::includes($array2, $item)) {
                $result[] = $item;
            }
        }
        return $result;
    }

    public static function getBoolText($v)
    {
        if ($v === true) {
            return 'TRUE';
        }
        if ($v === false) {
            return 'FALSE';
        }
        return '';
    }

    public static function getServerKey()
    {
        $dir = self::uploadDir();
        $file = $dir . 'SERVER_KEY';
        if (!file_exists($file)) {
            self::ensureFile($file);
            file_put_contents($file, gethostname() . '_' . self::randString());
        }
        return file_get_contents($file);
    }

    public static function implodeHtml($arr, $prefix, $suffix)
    {
        if (!$arr) {
            return '';
        }

        return $prefix . implode($suffix . $prefix, $arr) . $suffix;
    }

    public static function implode($arr, $separator = ', ')
    {
        return implode($separator, array_map(function ($a) {
            return Stringy::create($a)->humanize();
        }, $arr));
    }

    public static function newLocalRedis()
    {
        $redis = new \Redis();
//        $host = self::isDevDocker() ? 'redis' : 'localhost';
//        $redis->pconnect($host, 6379);
        $redis->pconnect('localhost', 6379);
        return $redis;
    }

    public static function formatZip($zip)
    {
        return str_replace('P.O.BOX ', '', $zip);
    }

    public static function formatPercent($float, $decimals = 2, $suffix = '%')
    {
        return number_format($float * 100, $decimals) . $suffix;
    }

    public static function delta($new, $old)
    {
        $new = (float)$new;
        $old = (float)$old;
        if (self::isZero($old)) {
            if (self::isZero($new)) {
                return 0;
            }
            return 1;
        }
        $multiple = 1;
        if ($new > 0 && $old < 0) {
            $multiple = -1;
        }
        return $multiple * ($new - $old) / $old;
    }

    public static function percent($some, $all)
    {
        $some = (float)$some;
        $all = (float)$all;
        if (self::isZero($all)) {
            return 0;
        }
        return $some / $all;
    }

    /**
     * Actually same as the `delta` method, isn't it?
     *
     * @param $some
     * @param $all
     *
     * @return float|int
     */
    public static function trendPercent($some, $all)
    {
        $some = (float)$some;
        $all = (float)$all;
        if (self::isZero($all)) {
            return self::isZero($some) ? 0 : 1;
        }
        $multiple = 1;
        if ($some > 0 && $all < 0) {
            $multiple = -1;
        }
        return $multiple * ($some / $all - 1);
    }

    public static function isZero($v)
    {
        $v = (float)$v;
        return $v === 0.0 || abs($v) < 10e-6;
    }

    public static function isTrue($input)
    {
        return $input === 'true' || $input === true || $input === '1' || $input === 1;
    }

    public static function isFalse($input)
    {
        return $input === 'false' || $input === false || $input === '0' || $input === 0;
    }

    public static function method($field)
    {
        return 'get' . ucfirst(str_replace('$', '', $field));
    }

    public static function explode($string, $char = ';', array $default = [])
    {
        if ($string) {
            $css = explode($char, trim($string));
            return array_filter($css, static function ($cs) {
                return $cs;
            });
        }
        return $default;
    }

    public static function mapArrayKeyValue(array $array)
    {
        $result = [];
        foreach ($array as $k => $v) {
            $result[] = [$k, $v];
        }
        return $result;
    }

    public static function unicodeString($str)
    {
        return substr(json_encode($str, JSON_UNESCAPED_SLASHES), 1, -1);
    }

    public static function testLatinName($str)
    {
        return preg_match('/^[\.\-_\s#\(\)\/\'"+:;,a-zA-Z0-9]*$/', $str);
    }

    public static function monoLog($name)
    {
        $log = new Logger($name);
        try {
            $logPath = self::logDir() . $name . '-' . date('Y-m-d') . '.log';
            if (!file_exists($logPath)) {
                touch($logPath);
                chmod($logPath, 0777);
            }
            $handler = new StreamHandler($logPath);
            $log->pushHandler($handler);

            // the default date format is "Y-m-d\TH:i:sP"
            $dateFormat = "Y-m-d H:i:s.u";

            // the default output format is "[%datetime%] %channel%.%level_name%: %message% %context% %extra%\n"
            $output = "%datetime% %level_name%: %message% %context% %extra%\n";

            $handler->setFormatter(new LineFormatter($output, $dateFormat));
        } catch (\Throwable) {}
        return $log;
    }

    public static function parseMonoLogFile($name, $file = null)
    {
        if (!$file || $file === 'latest' || $file === 'today') {
            $file = $name . '-' . date('Y-m-d');
        } else if ($file === 'yesterday' || $file === 'last') {
            $file = $name . '-' . Carbon::yesterday()->format('Y-m-d');
        } else if (preg_match('|^\d{4}-\d{2}-\d{2}$|', $file)) {
            $file = $name . '-' . $file;
        }
        $file .= '.log';

        return $file;
    }


    public static function readFileLinesReversely(string $filePath, $lineCount = null, $filter = null): array {
        $file = fopen($filePath, 'rb');
        if (!$file) {
            return [];
        }

        $lines = [];
        $line = '';
        $position = -1;

        // Move to the end of the file
        fseek($file, $position, SEEK_END);

        // Read backwards until the start of the file
        while (ftell($file) > 0) {
            $char = fgetc($file);

            if ($char === "\n" && $line !== '') {
                // We've completed a line, so store it in the array
                $rev = strrev($line);
                if (!$filter || $filter($rev)) {
                    $lines[] = $rev;
                    if ($lineCount !== null && count($lines) >= $lineCount) {
                        break;
                    }
                }
                $line = '';
            } else {
                // Add character to the line (reversed since we're reading backwards)
                $line .= $char;
            }

            // Move back one position
            $position--;
            fseek($file, $position, SEEK_END);
        }

        if ($lineCount === null || count($lines) < $lineCount) {
            $line .= fgetc($file);

            // Add the last line if it's not empty
            if ($line !== '') {
                $rev = strrev($line);
                if (!$filter || $filter($rev)) {
                    $lines[] = $rev;
                }
            }
        }

        if ($lines && $lines[0]) {
            if ($lines[0] === "\n") {
                unset($lines[0]);
            } else if (str_ends_with($lines[0], "\n")) {
                $lines[0] = substr($lines[0], 0, -1);
            }
        }

        fclose($file);
        return $lines;
    }

    public static function readMonoLog($name, $file = null, $lineCount = 100, $filter = null, $return = false, &$logPath = null)
    {
        self::longerRequest();
        $file = self::parseMonoLogFile($name, $file);
        $logPath = self::logDir() . $file;
        if (!file_exists($logPath)) {
            return new Response('Log is not created yet: ' . $logPath);
        }
        $log = file_get_contents($logPath);

        $lines = [];
        if (self::startsWith($file, $name)) {
            $lines = explode("\n", $log);
            $lines = array_reverse($lines);
            if ($lines && !$lines[0]) {
                unset($lines[0]);
            }

            if ($filter) {
                $lines = array_filter($lines, $filter);
            }

            $lineCount = (int)$lineCount;
            if ($lineCount > 0) {
                $lines = array_slice($lines, 0, $lineCount);
            }

            if (!$return) {
                $log = implode('<br/>', $lines);
            }
        }

        if ($return) {
            return $lines;
        }

        return new Response('<pre>' . $log . '</pre>');
    }

    public static function getBaseHost($fixStart = true)
    {
        $origin = Util::getParameter('host_path');
        $parts = self::explode($origin, '.');
        if (isset($parts[0]) && in_array($parts[0], ['www', 'staging'])) {
            $parts = array_slice($parts, 1);
        }
        $host = join('.', $parts);
        if ($fixStart) {
            if (Util::startsWith($origin, 'staging.')) {
                $host = '-staging.' . $host;
            } else {
                $host = '.' . $host;
            }
        }
        return $host;
    }

    public static function fillUserVariablesToRender(array &$parameters = [])
    {
        $user = self::user();
        if (empty($parameters['user'])) {
            if ($user) {
                $parameters['user'] = $user;
            }
        }
        $parameters['_user'] = $user;
        $parameters['_impersonatingUser'] = self::getImpersonatingUser();

        return $parameters;
    }

    /**
     * @param array            $parameters
     * @param Platform|null    $p
     *
     * @return array
     */
    public static function fillPlatformVariablesToRender(array &$parameters = [], Platform $p = null)
    {
        $p = $p ?: self::platform();
        $cp = self::cardProgram();
        $user = self::user();
        if (!$cp && $user && !self::isReservedHosts()) {
            $uc = $user->getOneCardInPlatform();
            if ($uc) {
                $cp = $uc->getCard()->getCardProgram();
            }
        }

        $request = self::$request;

        $parameters['__cp'] = $p ?: Platform::get(Platform::NAME_TERN_COMMERCE);
        $parameters['_cp'] = [];
        $parameters['_cpp'] = [];
        $parameters['_cpc'] = [];
        $parameters['_cp_name'] = Platform::NAME_TERN_COMMERCE;
        $parameters['_cp_logo'] = CardProgram::DEFAULT_LOGO;
        $parameters['_cp_logo_tern'] = '/static/img/tern_logo_new.svg';
        $parameters['_cp_lang'] = 'en';
        $parameters['_cp_languages'] = Config::activeLanguages();
        $parameters['_cp_icon'] = CardProgram::DEFAULT_ICON;
        $parameters['_cp_icon_touch'] = CardProgram::DEFAULT_ICON_TOUCH;
        if ($p) {
            $parameters['_cp'] = $p;
            $parameters['_cpp'] = $cp;
            $parameters['_cpc'] = self::json($p, 'customize') ?: [];
            $parameters['_cp_name'] = $p->getName();
            $parameters['_cp_logo'] = ClientService::getLogoByDomainOrReferer() ?? $p->getLogo();
            $parameters['_cp_lang'] = $p->getDefaultLanguage();
            $parameters['_cp_languages'] = $cp ? $cp->getSupportedLanguages() : [];


            $icon = $p->getIcon();
            if ($icon) {
                $parameters['_cp_icon'] = $icon->getAssetUrl();
                $parameters['_cp_icon_touch'] = $p->getTouchIconUrl();
            }

            $preview = $request->get('preview');
            if ($preview) {
                if (!$request->get('logo')) {
                    die;
                }
                $parameters['_cp_logo'] = Attachment::find($request->get('logo'));
                $parameters['_cp_lang'] = $request->get('default_language');
                $parameters['_cp_preview_page'] = $request->get('preview_page');
            }
            $parameters['_cp_logo_tern'] = $parameters['_cp_logo'];
        }

        $parameters['_cp_deplay_subtitle_2'] = ClientService::getTitleByDomainOrReferer();

        if (!$p || $p->isTernCommerce()) {
            if ($cp && ($cp->isUsUnlocked() || $cp->isUsUnlockedLegacy())) {
                $parameters['_site_description'] = 'The US Unlocked virtual prepaid cards and debit card is accepted by many US online stores allowing you to shop and ship internationally.';
            } else {
                $parameters['_site_description'] = 'A comprehensive API-driven platform providing a turnkey solution for leading brands to launch innovative payment solutions.';
            }
        } else {
            $parameters['_site_description'] = $p->getDescription() ?? '';
        }

        $parameters['_site_csp'] = self::secureContentPolicy();

        return $parameters;
    }

    public static function secureContentPolicy()
    {
        $all = "script-src 'self' 'unsafe-inline' 'unsafe-eval' blob:
apis.google.com
www.google-analytics.com
www.googletagmanager.com
tools.luckyorange.com
*.googleapis.com
*.gstatic.com
*.gstatic.cn
at.alicdn.com
*.cloudflare.com
*.dialogflow.com
cdn.jsdelivr.net
cdn.segment.com
api.privacy.com
js-agent.newrelic.com
hexagon-analytics.com
www.shipito.com
global.localizecdn.com
cdn.yodlee.com
cdn.plaid.com
cdn.redoc.ly
unpkg.com
fpnpmcdn.net
*.nsureapi.com
*.paypal.com
*.usunlocked.com
www.recaptcha.net;
base-uri 'self';";

        return str_replace("\n", ' ', $all);
    }

    public static function frameOptions()
    {
        return Bundle::common('frameOptions', [], self::FRAME_OPTIONS);
    }

    public static function pageResponseForPreview(Response $response)
    {
        $csp = self::secureContentPolicy();
        $fo = self::frameOptions();

        $request = Util::request();
        if ($request->get('preview') && $request->get('preview_page')) {
            $path = Util::getParameter('host_path');
            if (Util::startsWith($path, 'www.')) {
                $path = str_replace('www.', '.', $path);
            } else if (Util::startsWith($path, 'staging.')) {
                $path = '-' . $path;
            } else {
                $path = '.' . $path;
            }
            $host = Util::getParameter('host_schema') . '://*' . $path;

            $csp .= 'frame-ancestors ' . $host . ';';
            $fo = 'ALLOW-FROM ' . $host;
        }

        $response->headers->set('Content-Security-Policy', $csp);
        $response->headers->set('X-Frame-Options', $fo);
        return $response;
    }

    public static function fillAffiliateVariablesToRender(array &$parameters = [])
    {
        $affId = Affiliate::getAffIdFromRequest(self::request());
        $affiliate = null;
        if ($affId) {
            $affiliate = self::em()->getRepository(\CoreBundle\Entity\Affiliate::class)->findOneBy([
                'affId' => $affId,
            ]);
        }
        if (!$affiliate) {
            $user = self::user();
            if ($user) {
                $affiliate = $user->getAffiliate();
            }
        }
        $parameters['_af_lang'] = $affiliate ? $affiliate->getLanguage() : 'en';

        return $parameters;
    }

    public static function getTwigSource($view)
    {
        return self::$twig->getLoader()->getSourceContext($view);
    }

    public static function accessible($module)
    {
        $user = self::user();
        if (!$user) {
            return false;
        }
        return $user->authPermission($module);
    }

    public static function editable($module)
    {
        $user = self::user();
        if (!$user) {
            return false;
        }
        return $user->authEditable($module);
    }

    public static function removeNA($s)
    {
        if ($s === 'N/A') {
            return '';
        }
        return $s ?: '';
    }

    public static function methodExists($object_or_class, string $method)
    {
        if (is_object($object_or_class) || is_string($object_or_class)) {
            return method_exists($object_or_class, $method);
        }
        if (is_array($object_or_class) && isset($object_or_class['class'], $object_or_class['id'])) {
            try {
                $es = new EntitySignature($object_or_class);
                $entity = $es->toEntity();
                if ($entity) {
                    return method_exists($entity, $method);
                }
            } catch (\Throwable $t) {}
        }
        return false;
    }

    public static function toFilterOptions($items, $maps = null, $humanize = true)
    {
        if (is_string($maps)) {
            $maps = [
                'value' => 'id',
                'label' => $maps,
            ];
        } else if (!$maps) {
            $maps = [];
        }

        if (!isset($maps['value'])) {
            $maps['value'] = 'id';
        }

        if (!isset($maps['label'])) {
            $maps['label'] = 'name';
        }

        if ($items instanceof Collection) {
            $items = $items->toArray();
        }

        return array_map(function ($item) use (&$maps, $humanize) {
            $result = [];
            foreach ($maps as $key => $field) {
                $method = 'get' . ucfirst($field);
                if (is_array($item) && isset($item[$field])) {
                    $result[$key] = $item[$field];
                } else if (self::methodExists($item, $method)) {
                    $result[$key] = $item->$method();
                } else if (self::methodExists($item, $field)) {
                    $result[$key] = $item->$field();
                } else if (is_string($item)) {
                    $result[$key] = $item;
                    if ($key === 'label' && $humanize) {
                        $result[$key] = static::humanize($item);
                    }
                } else {
                    $result[$key] = null;
                }
            }

            if (isset($result['label'])) {
                if ($result['label'] === '________') {
                    $result['label'] = '';
                    $result['className'] = 'select-separator';
                }
            }

            return $result;
        }, $items);
    }

    public static function allToFilterOptions(array $all, array $fields = null, $humanize = true)
    {
        if ($fields === null) {
            $fields = array_map(static function () {
                return null;
            }, $all);
        }

        $result = [];
        foreach ($fields as $field => $maps) {
            if (isset($all[$field])) {
                $result[$field] = static::toFilterOptions($all[$field], $maps, $humanize);
            }
        }
        return $result;
    }

    public static function createRequestForPredicateNot(Request $origin)
    {
        $not = $origin->get('__not');
        if (!$not) {
            return null;
        }

        $filters = [];
        foreach ($not as $i => $n) {
            if (mb_strpos($i, 'filter[') === 0 && !static::endsWith($i, ']')) {
                $name = mb_substr($i, 7);
                $filters[$name] = $n;
            }
        }

        return new Request([], $filters ? [
            'filter' => $filters,
        ] : $not);
    }

    public static function moveQueryParameters(QueryBuilder $query, QueryBuilder $sub)
    {
        /** @var Parameter $parameter */
        foreach ($sub->getParameters() as $parameter) {
            $query->setParameter($parameter->getName(), $parameter->getValue());
        }
        $sub->setParameters([]);
        return $query;
    }

    public static function parseDQLForPredicateNot(
        QueryBuilder $searchQuery,
        QueryBuilder $notSearchQuery,
        $mapKey
    ) {
        $dql = $notSearchQuery->getDQL();
        $searches = [];
        $replaces = [];

        $matches = [];
        preg_match_all('| [a-z][a-z0-9_]* |u', $dql, $matches);
        if (!empty($matches[0])) {
            $names = array_unique($matches[0]);
            foreach ($names as $name) {
                $name = trim($name);
                $searches[] = ' ' . $name . ' ';
                $searches[] = ' ' . trim($name) . '.';
                $searches[] = '(' . trim($name) . '.';

                $new = 'not_' . $name;
                $replaces[] = ' ' . $new . ' ';
                $replaces[] = ' ' . trim($new) . '.';
                $replaces[] = '(' . trim($new) . '.';
            }
        }

        $paramCount = $searchQuery->getParameters()->count();

        $matches = [];
        preg_match_all('|[ (]\?[1-9][0-9]*[ )]|u', $dql, $matches);
        if (!empty($matches[0])) {
            $numbers = array_unique($matches[0]);
            foreach ($numbers as $number) {
                $bracket = self::startsWith($number, '(');
                $prefix = $bracket ? '(?' : ' ?';
                $suffix = $bracket ? ')' : ' ';

                $number = (int)trim($number, ' ()?');
                $searches[] = $prefix . $number . $suffix;

                $new = $paramCount + $number;
                $replaces[] = $prefix . $new . $suffix;
            }
        }

        $dql = str_replace($searches, $replaces, $dql);

        $expr = Util::expr();
        $searchQuery->andWhere(
            $expr->neq($mapKey, $expr->all($dql))
        );

        /**
         * @var Parameter $v
         */
        foreach ($notSearchQuery->getParameters() as $v) {
            $key = $v->getName();
            if (is_numeric($key)) {
                $key = $paramCount + (int)$key;
            }
            $searchQuery->setParameter($key, $v->getValue());
        }
    }

    public static function render($view, $parameters = [])
    {
        $view = Bundle::replaceRenderView($view);
        return static::$twig->render($view, $parameters);
    }

    public static function renderData($url, $params = [], $execJs = true)
    {
        $js = self::twig()->render($url, $params);

        if ($execJs) {
            $js = "JSON.stringify(JSON.parse(`$js`));";
            $js = static::execJs($js);
        }

        return json_decode($js, true);
    }

    public static function execJs($js)
    {
        if (self::isDevDevice()) {
            $env = getenv('PATH');
            if ($env === false || gethostname() === 'abeldeMac-mini.local') {
                putenv('PATH=/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/Users/<USER>/.nvm/versions/node/v12.6.0/bin');
            }
        }

        $runner = new PhpExecJs();
        return $runner->evalJs($js);
    }

    /**
     * @param $start Carbon
     * @param $end Carbon
     * @return string
     */
    public static function getRangeType($start, $end)
    {
        $rangeType = 'Y';
        if ($start && $end) {
            if ($start->diffInYears($end) < 3) {
                $rangeType = 'Y-m';
                if ($start->diffInMonths($end) < 3) {
                    $rangeType = 'Y-W\W';
                    if ($start->diffInWeeks($end) < 3) {
                        $rangeType = 'Y-m-d';
                    }
                }
            }
        }

        return $rangeType;
    }

    public static function t($phrase, $args = [], $dictName = 'localize-es-phrases')
    {
        return LocalizeService::t($phrase, $args, $dictName);
    }

    public static function toApiArray($data, $extra = false)
    {
        $result = [];
        if (is_array($data) || $data instanceof Collection) {
            foreach ($data as $entity) {
                $result[] = static::toApiArray($entity, $extra);
            }
        } else if (is_object($data)) {
            if (method_exists($data, 'toApiArray')) {
                return $data->toApiArray($extra);
            }
            return Util::e2a($data);
        } else {
            $result = $data;
        }
        return $result;
    }

    public static function mergeArrayTo(array $data, &$to)
    {
        foreach ($data as $k => $v) {
            $to[$k] = $v;
        }
    }

    public static function nameAbbr($name)
    {
        $parts = explode(' ', $name);
        if (count($parts) <= 1) {
            return $name;
        }
        $last = trim($parts[1] ?: '');
        if (mb_strlen($last) <= 0) {
            return $name;
        }
        $suffix = mb_strlen($last) > 1 ? '.' : '';
        return $parts[0] . ' ' . mb_substr($last, 0, 1) . $suffix;
    }

    public static function getQrCodeUrl($value, $size = 200, $ecc = 'M')
    {
        $value = urlencode($value);
        return "https://api.qrserver.com/v1/create-qr-code/?size={$size}x{$size}&data={$value}&ecc=$ecc";
    }

    public static function platform()
    {
        if (static::$platform) {
            return static::$platform;
        }
        $host = static::request()->getHost();
        $em = static::em();
        $repo = $em->getRepository(Platform::class);
        if ($host) {
            static::$platform = $repo->findOneBy([
                'domain' => $host,
            ]);
            if (!static::$platform) {
                static::$platform = $repo->createQueryBuilder('p')
                    ->where('p.otherDomains like :domain')
                    ->setParameter('domain', '%"' . $host . '"%')
                    ->setMaxResults(1)
                    ->getQuery()
                    ->getOneOrNullResult();
            }
            if (!static::$platform) {
                if (static::startsWith($host, 'www.')) {
                    static::$platform = $repo->findOneBy([
                        'domain' => substr($host, 4),
                    ]);
                } else {
                    static::$platform = $repo->findOneBy([
                        'domain' => 'www.' . $host,
                    ]);
                }
            }
            if (!static::$platform) {
                $parts = explode('.', $host);
                if (isset($parts[0]) && $parts[0] !== 'staging') {
                    $sub = $parts[0];
                    if (static::endsWith($sub, '-staging')) {
                        $sub = str_replace('-staging', '', $sub);
                    }
                    static::$platform = $repo->findOneBy([
                        'subDomain' => $sub,
                    ]);
                }
            }
        }
        if (!static::$platform && static::isStaging(true)) {
            $cpId = static::$request->cookies->get(static::COOKIE_PLATFORM);
            if ($cpId) {
                static::$platform = $repo->find($cpId);
            }

            if (!static::$platform) {
                $cpId = static::$request->headers->get(static::HEADER_PLATFORM);
                if ($cpId) {
                    static::$platform = $repo->find($cpId);
                }
            }
        }
        if (!static::$platform && !static::isReservedHosts() && !static::isCommand()) {
            throw new RedirectException(static::getParameter('host'));
        }
        return static::$platform;
    }

    public static function cardProgram()
    {
        if (static::$cardProgram) {
            return static::$cardProgram;
        }
        $platform = static::platform();
        if (!$platform) {
            return null;
        }
        $cps = $platform->getCardPrograms();
        if (!$cps || $cps->isEmpty()) {
            return null;
        }
        if ($platform->isTernCommerce()) {
            static::$cardProgram = $cps->filter(function (CardProgram $cp) {
                return $cp->getName() === CardProgram::NAME_US_UNLOCKED;
            })->first();
        } else {
            static::$cardProgram = $cps->first();
        }
        return static::$cardProgram;
    }

    public static function browserNameFromUserAgent($userAgent)
    {
        if (!$userAgent) {
            return '';
        }

        $parser = new Parser();
        $detector = $parser->parse($userAgent);
        $name = $detector->browserFamily();
        if ($name) {
            return $name;
        }
        if (strpos($userAgent, 'CFNetwork') !== false) {
            return 'iOS App';
        }
        return '';
    }

    public static function getFileResponse($path, $disposition = ResponseHeaderBag::DISPOSITION_ATTACHMENT)
    {
        self::verifyPathSecurity($path);
        $response = new BinaryFileResponse($path);
        $response->setContentDisposition($disposition);
        return $response;
    }

    public static function getFilePreviewResponse($path, $disposition = ResponseHeaderBag::DISPOSITION_INLINE)
    {
        return self::getFileResponse($path, $disposition);
    }

    public static function createStandResp($data = null, $success = true, $message = '')
    {
        return compact('success', 'message', 'data');
    }

    public static function last4(#[\SensitiveParameter] $pan)
    {
        return self::maskPan($pan, 4, true);
    }

    public static function last2(#[\SensitiveParameter] $pan)
    {
        return self::maskPan($pan, 2, true);
    }

    public static function maskPan(#[\SensitiveParameter] $pan, $visible = 4, $noStar = false)
    {
        if (!$pan) {
            return '';
        }
        $pan = str_replace(['_', '-', ' ', 'x'], ['', '', '', '*'], $pan);
        $len = strlen($pan);
        if ($len > $visible) {
            $pan = substr($pan, -$visible);
        }
        if ($noStar) {
            return $pan;
        }
        return str_pad($pan, 16, '*', STR_PAD_LEFT);
    }

    public static function maskPanWithBin(#[\SensitiveParameter] $pan, $binLength = 6)
    {
        if (!$pan) {
            return '';
        }
        $pan = str_replace(['_', '-', ' ', 'x'], ['', '', '', '*'], $pan);
        $len = strlen($pan);
        if ($len <= 14) {
            return static::maskPan($pan);
        }
        $start = substr($pan, 0, $binLength);
        $end = substr($pan, -4);
        $middle = str_repeat('*', $len - 4 - $binLength);
        return $start . $middle . $end;
    }

    public static function formatPan(#[\SensitiveParameter] $pan)
    {
        $pan = $pan ?: '****************';
        $items = str_split($pan, 4);
        return implode(' ', $items);
    }

    public static function isPanMasked($pan)
    {
        if (strlen($pan) <= 8) {
            return true;
        }
        $middle = substr($pan, 6, 6);
        return $middle === '******';
    }

    public static function unsetCHD(&$res)
    {
        unset($res['cvv'], $res['exp_year'], $res['exp_month'], $res['pan']);
        return $res;
    }

    public static function utf8ize($mixed)
    {
        if (is_array($mixed)) {
            foreach ($mixed as $key => $value) {
                $mixed[$key] = self::utf8ize($value);
            }
        } elseif (is_string($mixed)) {
            return mb_convert_encoding($mixed, 'UTF-8', 'UTF-8');
        }

        return $mixed;
    }

    public static function headerValues(HeaderBag $bag, array $keys)
    {
        $map = [];
        foreach ($keys as $key) {
            $map[strtolower($key)] = $key;
        }

        $result = [];
        $all = $bag->keys();
        foreach ($all as $item) {
            $lower = strtolower($item);
            if (isset($map[$lower])) {
                $result[$map[$lower]] = $bag->get($item);
            }
        }
        return $result;
    }

    public static function feeName($name)
    {
        if (self::endsWith(strtolower($name), ' fee')) {
            return $name;
        }
        return self::title($name . ' Fee');
    }

    public static function queryAdminsInCurrentPlatform(QueryBuilder $q, $alias = 'u')
    {
        $platform = self::platform();
        if (!$platform) {
            return $q;
        }
        return $q->join($alias . '.accessiblePlatforms', '__ps__')
            ->andWhere('__ps__ = :__platform__')
            ->setParameter('__platform__', $platform);
    }

    public static function queryMembersInCurrentPlatform(QueryBuilder $q, $alias = 'u')
    {
        $cp = self::cardProgram();
        if (!$cp) {
            return $q;
        }
        return $q->join($alias . '.cards', '__uc__')
            ->join('__uc__.card', '__card__')
            ->andWhere('__card__.cardProgram = :__cardProgram__')
            ->setParameter('__cardProgram__', $cp);
    }

    public static function queryRealConsumers(QueryBuilder $query, $prefix = 'u')
    {
        if (!self::isServer()) {
            return;
        }
        $expr = self::expr();
        foreach ([
            '%@shinetechchina.com',
            '%@shinetechsoftware.com',
            '%@ternitup.com',
            '%@terncommerce.com',
            '%@yopmail.com',
            '<EMAIL>',
            '<EMAIL>',
        ] as $i => $item) {
            $query->andWhere($expr->notLike($prefix . '.email', ':email_' . $i))
                ->setParameter('email_' . $i, $item);
        }
    }

    public static function maskIP($ip)
    {
        if (!$ip) {
            return $ip;
        }
        if (strpos($ip, ':') !== false) {
            $ips = explode(':', $ip);
            if ($ips) {
                $ips[count($ips) - 1] = '****';
            }
            return implode(':', $ips);
        }
        $ips = explode('.', $ip);
        if ($ips) {
            $ips[count($ips) - 1] = '***';
        }
        return implode('.', $ips);
    }

    public static function queryArray(QueryBuilder $q, $field = 'name')
    {
        $rs = $q->distinct()
            ->orderBy($field, 'asc')
            ->getQuery()
            ->getArrayResult();
        return pluck($rs, $field);
    }

    public static function kSortRecursive(&$array, $sort_flags = SORT_REGULAR)
    {
        if (!is_array($array)) {
            return false;
        }
        ksort($array, $sort_flags);
        foreach ($array as &$arr) {
            self::kSortRecursive($arr, $sort_flags);
        }
        unset($arr);
        return true;
    }

    public static function compareArrays(array $a, array $b)
    {
        self::kSortRecursive($a);
        self::kSortRecursive($b);
        return json_encode($a) === json_encode($b);
    }

    public static function calculateArrayDiff(array $a, array $b, array $ignoreKeys = [], ?array $onlyKeys = null)
    {
        $result = [];
        foreach ($a as $k => $v) {
            if (in_array($k, $ignoreKeys)) {
                continue;
            }
            if (is_array($onlyKeys) && !in_array($k, $onlyKeys)) {
                continue;
            }
            if (array_key_exists($k, $b)) {
                if ($v === $b[$k]) {
                    continue;
                }
                if (is_array($v) && is_array($b[$k]) && self::compareArrays($v, $b[$k])) {
                    continue;
                }
            }
            $result[$k] = [
                $v,
                $b[$k] ?? null,
            ];
        }
        foreach ($b as $k => $v) {
            if (in_array($k, $ignoreKeys)) {
                continue;
            }
            if (is_array($onlyKeys) && !in_array($k, $onlyKeys)) {
                continue;
            }
            if (array_key_exists($k, $a)) {
                if ($v === $a[$k]) {
                    continue;
                }
                if (is_array($v) && is_array($a[$k]) && self::compareArrays($v, $a[$k])) {
                    continue;
                }
            }
            $result[$k] = [
                $a[$k] ?? null,
                $v,
            ];
        }
        return $result;
    }

    public static function calculateArrayDiffWithDecryption(array $a, array $b, array $paths)
    {
        $oldValues = [];
        $items = [$a, $b];
        foreach ($items as $i => $item) {
            $oldValues[$i] = [];
            $updated = false;
            $dot = new Dot($item);
            foreach ($paths as $path) {
                if (!$dot->isEmpty($path)) {
                    $value = $dot->get($path);
                    if (SSLEncryptionService::isEncrypted($value)) {
                        $oldValues[$i][$path] = $value;
                        $value = SSLEncryptionService::tryToDecrypt($value);
                        $dot->set($path, $value);
                        $updated = true;
                    }
                }
            }
            if ($updated) {
                $items[$i] = $dot->all();
            }
        }
        $diff = self::calculateArrayDiff($items[0], $items[1]);
        if ($diff && $oldValues) {
            foreach ($diff as $k => $values) {
                foreach ($values as $i => $v) {
                    $old = $oldValues[$i] ?? [];
                    $dot = new Dot($v);
                    $updated = false;
                    foreach ($old as $path => $o) {
                        if ($dot->has($path)) {
                            $dot->set($path, $o);
                            $updated = true;
                        }
                    }
                    if ($updated) {
                        $diff[$k][$i] = $dot->all();
                    }
                }
            }
        }
        return $diff;
    }

    public static function arrayColumns(array $arr, array $keys)
    {
        $result = [];
        foreach ($keys as $k) {
            if (array_key_exists($k, $arr)) {
                $result[$k] = $arr[$k];
            }
        }
        return $result;
    }

    public static function resolveSum(array $numbers, int $sum, int $size = null)
    {
        $numbers = array_filter($numbers, function ($item) use ($sum) {
            return $item <= $sum;
        });
        if ($size === null) {
            $size = count($numbers);
            $all = [];
            for ($i = 1; $i < $size; $i++) {
                $all[] = self::resolveSum($numbers, $sum, $i);
            }
            return array_merge(...$all);
        }
        $p = new Combinations($numbers, $size);
        return array_filter($p->toArray(), function (array $items) use ($sum) {
            return array_sum($items) === $sum;
        });
    }

    public static function getExceptionBrief(\Throwable $e)
    {
        $message = $e->getMessage() ?? '';
        [$file, $func, $line] = array_values(self::getExceptionFileLine($e, false));
        $ei = '';

        if (method_exists($e, 'getExternalInvokeId')) {
            $eiId = $e->getExternalInvokeId();
            if ($eiId) {
                $ei = ' (EI: ' . $eiId . ')';
            }
        }

        return sprintf(
            '%s. %s@%s#%d%s',
            $message,
            $file,
            $func,
            $line,
            $ei,
        );
    }

    public static function exceptionBrief(\Throwable $e)
    {
        return self::getExceptionBrief($e);
    }

    public static function dumpException(\Throwable $ex)
    {
        $data = [
            'type' => get_class($ex),
            'msg' => $ex->getMessage(),
            'file'  => $ex->getFile(),
            'line'  => $ex->getLine(),
            'code'  => $ex->getCode(),
            'trace' => $ex->getTrace(),
        ];
        $previous = $ex->getPrevious();
        if ($previous) {
            $data['previous'] = [
                'type' => get_class($previous),
                'msg' => $previous->getMessage(),
                'file'  => $previous->getFile(),
                'line'  => $previous->getLine(),
                'code'  => $previous->getCode(),
                'trace' => $previous->getTrace(),
            ];
        }
        return $data;
    }

    public static function getExceptionFileLine(\Throwable $e, bool $asString = true): string|array
    {
        $file = $e->getFile();
        $line = $e->getLine();
        $trace = $e->getTrace();
        $func = $trace[1]['function'] ?? '';
        $i = 0;
        do {
            if ( ! self::containsSubString($file, [
                'PortalException',
                'FailedException',
            ])) {
                break;
            }
            $tr = $trace[$i] ?? null;
            if (!$tr) {
                break;
            }
            $file = $tr['file'] ?? '';
            $line = $tr['line'] ?? 0;
            $func = $trace[$i + 1]['function'] ?? '';
            $i++;
        } while ($i < 10);
        if ($asString) {
            if ($func) {
                $func = '@' . $func;
            }
            return "$file$func#$line";
        }
        return compact('file', 'func', 'line');
    }

    public static function getCallstackSource()
    {
        try {
            throw new \RuntimeException();
        } catch (\Throwable $t) {
            try {
                $trace = $t->getTrace();
                if ($trace) {
                    $first = $trace[0] ?? [];
                    $parts = explode('/', $first['file'] ?? '');
                    $file = implode('/', array_slice($parts, -3));
                    $file = self::removeSuffix($file, '.php');
                    $line = $first['line'] ?? '';
                    $func = $trace[1]['function'] ?? '';
                    return "$file@$func#$line";
                }
            } catch (\Throwable $t) {
                return '';
            }
        }
        return '';
    }

    /**
     * @param int $min milliseconds
     * @param int $max milliseconds
     *
     * @return void
     */
    public static function usleep($min, $max = null)
    {
        $v = $min;
        if ($max !== null) {
            $v = self::faker()->numberBetween($min, $max);
        }
        usleep($v * 1000);
    }

    /**
     * @param     $name
     * @param int $timeout seconds
     *
     * @return RedisMutex
     */
    public static function mutex($name, $timeout = 10)
    {
        $redis = Data::instance();
        $connector = self::getParameter('redis_connector', true);
        if ($connector === 'predis') {
            return new PredisMutex([$redis], $name, $timeout);
        }
        return new PHPRedisMutex([$redis], $name, $timeout);
    }

    public static function mute(Callable $callback, array $args = [], string $summary = null, array $context = [])
    {
        try {
            return $callback(...$args);
        } catch (\Throwable $t) {
            $context['__code__'] = $t->getCode();
            $context['__file__'] = $t->getFile();
            $context['__line__'] = $t->getLine();
            $context['__trace__'] = $t->getTraceAsString();

            if (!$summary) {
                if (is_array($callback)) {
                    $summary = implode('::', $callback);
                } else {
                    $summary = 'undefined action';
                }
            }
            Log::warn('Failed to perform "' . $summary . '": ' . $t->getMessage(), $context);
            return null;
        }
    }

    public static function silent(callable $callback, $default = null)
    {
        try {
            return $callback();
        } catch (\Throwable) {
            return $default;
        }
    }

    public static function getCorsHeaders()
    {
        return [
            'Access-Control-Allow-Origin' => '*',
            'Access-Control-Allow-Credentials' => 'true',
            'Access-Control-Allow-Methods' => 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers' => 'DNT, X-User-Token, Keep-Alive, User-Agent, X-Requested-With, If-Modified-Since, Cache-Control, Content-Type, x-timezone, x-api-key, x-token, x-i18n, x-version, x-environment, x-location, x-terminal, x-impersonating',
            'Access-Control-Max-Age' => 1728000,
        ];
    }

    public static function createCorsResponse(Response $response)
    {
        $response->headers->add(self::getCorsHeaders());
        $response->headers->set('X-Frame-Options', '');
        return $response;
    }

    public static function GetNextBusinessDay($tmpDate = null)
    {
        // get today date
        $tmpDate = $tmpDate ?? date('Y-m-d');
        // 1 day
        $i = 1;
        // for the next business day from today
        $nextBusinessDay = date('Y-m-d', strtotime($tmpDate .
            ' +' . $i .
            ' Weekday'));
        // check whether the date of the business day is in public holiday
        while (in_array($nextBusinessDay, self::US_HOLIDAYS)) {
            $i++;
            // move the business date forward since its a public holiday
            $nextBusinessDay = date('Y-m-d', strtotime($tmpDate .
                ' +' . $i .
                ' Weekday'));
        }

        return $nextBusinessDay;
    }

    public static function GetNextBusinessDayNacha($tmpDate = null)
    {
        $next = self::GetNextBusinessDay($tmpDate);
        return date('ymd', strtotime($next));
    }

    public static function isDateHoliday($date = null)
    {
        if (!$date)
        {
            $date = date('Y-m-d');
        }

        return in_array($date, self::US_HOLIDAYS);
    }

    public static function isTodayBusinessDay()
    {
        $currentWeekDay = date( "w" );

        switch ($currentWeekDay) {
            case "0": {  // sunday
                return false;
                break;
            }
            case "6": {  // saturday
                return false;
                break;
            }
            default: {  //all other days
                return !self::isDateHoliday();
                break;
            }
        }
    }
    /**
     * @param $type false: before true: after
     * @param $count
     */
    public static function getBusinessDay($count, $type = false) {
        $i = 0;
        $now = Carbon::now();
        do {
          if ($type) {
            $now->addDay();
          } else {
            $now->subDay();
          }
          if (!self::checkIsHoliday($now) && $now->dayOfWeek != 0 &&  $now->dayOfWeek != 6) {
            $i++;
          }
        } while ($i != $count);

        return $now;
    }

    public static function isBusinessDay(Carbon $date = null)
    {
        if (!$date) {
          $date = Carbon::now();
        }
        $currentWeekDay = $date->dayOfWeek;
        switch ($currentWeekDay) {
            case "0": {  // sunday
                return false;
                break;
            }
            case "6": {  // saturday
                return false;
                break;
            }
            default: {  //all other days
                return !self::checkIsHoliday($date);
                break;
            }
        }
    }

    public static function getHolidaysByYear($year) {
      $holidays = [];
      $client = new Client();
      try{
        $response = $client->get('https://date.nager.at/api/v3/PublicHolidays/' . $year . '/us');
        if ($response) {
          $content = $response->getBody()->getContents() ?: '{}';
          $content = Util::s2j($content) ?? [];
          foreach($content as $item) {
            if ($item['global'] || $item['name'] == 'Columbus Day'){
              $holidays[] = $item['date'];
            }
          }
        }
      } catch (RequestException $exception) {
        $holidays = self::US_HOLIDAYS;
      }
      return  $holidays;
    }

    public static function checkIsHoliday($date = null) {
        if (!$date) {
          $date = date('Y-m-d');
        }
        $year = date('Y');
        $holidays = Data::getArray('holidy-' . $year);
        if (!$holidays) {
          $holidays = self::getHolidaysByYear($year);
        }
        $isHoliday = in_array($date, $holidays);
        return $isHoliday;
    }

    public static function getCurrentRoles()
    {
        $platform = self::platform();
        $role = null;
        if ($platform->isFaasPlatforms()) {
            $role = [
                'agentAndAdmin' => [
                    Role::ROLE_FAAS_ADMIN,
                    Role::ROLE_FAAS_AGENT
                ],
                'member' => Role::ROLE_FAAS_MEMBER,
                'employer' => Role::ROLE_FAAS_CLIENT,
            ];
        } else if ($platform->isTransferMex()) {
            $role = [
                'agentAndAdmin' => [
                    Role::ROLE_TRANSFER_MEX_ADMIN,
                    Role::ROLE_TRANSFER_MEX_AGENT,
                ],
                'member' => Role::ROLE_TRANSFER_MEX_MEMBER,
                'employer' => Role::ROLE_TRANSFER_MEX_EMPLOYER,
            ];
        }
        return $role;
    }

    public static function formatManualInputPhoneNumber($phoneNumber)
    {
        $phoneNumber = preg_replace('/[\(\)\-\s]/', '', $phoneNumber);
        if (!Util::startsWith($phoneNumber, '+')) {
            if (Util::startsWith($phoneNumber, '86')) {
                $suffix = trim(substr($phoneNumber, 2));
                if (strlen($suffix) === 11) {
                    return '+86 ' . $suffix;
                }
            }
            if (Util::startsWith($phoneNumber, '1')) {
                $suffix = trim(substr($phoneNumber, 1));
                if (strlen($suffix) === 10) {
                    return '+1 ' . $suffix;
                }
            }
            if (strlen($phoneNumber) === 10) {
                return '+1 ' . $phoneNumber;
            }
        } else {
            $usPre = trim(substr($phoneNumber, 1, 1));
            $cnPre = trim(substr($phoneNumber, 1, 2));
            if ($usPre === '1') {
                $pre = trim(substr($phoneNumber, 0, 2));
                $suffix = trim(substr($phoneNumber, 2));
                return $pre . ' ' . $suffix;
            }
            if ($cnPre === '86') {
                $pre = trim(substr($phoneNumber, 0, 3));
                $suffix = trim(substr($phoneNumber, 3));
                return $pre . ' ' . $suffix;
            }
        }

        return $phoneNumber;
    }

    public static function verifyUSPhoneNumber($phone)
	{
		$pattern = "/^(1\s?)?(\(\d{3}\)|\d{3})\s?-?\d{3}-?\s?\d{4}$/";
		if (preg_match($pattern, $phone)) {
			return true;
		}
		return false;
	}

  public static function maskedAccountNumber($account)
  {
      $length = strlen($account);
      if (!$length) {
          return '';
      }
      if ($length <= 4) {
          return '***' . substr($account, -1);
      }
      if ($length <= 8) {
          return '****' . substr($account, -4);
      }
      return substr($account, 0, 4) . str_repeat('*', $length - 8) . substr($account, -4);
  }

    public static function authDevOrLocal()
    {
        if (self::isDev() || self::isSelfRequest()) {
            return;
        }
        throw new DeniedException();
    }

    public static function authApiOrDevOrLocal()
    {
        $path = self::request()->getPathInfo();
        if (self::startsWith($path, '/api/')) {
            return;
        }
        self::authDevOrLocal();
    }

    public static function InitSpecialEmail ($email) {
      $res = preg_replace('/[+$\`*#=\-,;\[\]\{\}\(\)\\^]/', '', $email);
      return $res ? $res : $email;
    }

    public static function getWeek($number = 0) {
        $weeks = [
          'SUN',
          'MON',
          'TUE',
          'WED',
          'THUR',
          'FRI',
          'SAT'
        ];

        return $weeks[$number];
    }
    public static function getMonth($number = 0) {
      $monthes = [
        'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sept', 'Oct', 'Nov', 'Dec'
      ];

      return $monthes[$number];
  }

  public static function checkCardPin(User $user, #[\SensitiveParameter] $pin) {
      $bod = $user->getBirthday();
      $error = null;
      if ($bod) {
        $year = $bod->format('Y');
        $month = $bod->format('m');
        $day = $bod->format('d');
        if (in_array($pin, [
            $year,
            $month . $day,
            $day . $month,
        ])) {
          $error = 'Please do not use your birthday as your PIN.';
        }
      }
      if (preg_match('/(\d)\1{3}/', $pin)) {
        $error = 'Please do not use the same digits as your PIN.';
      }
      if (preg_match('/((?:0(?=1)|1(?=2)|2(?=3)|3(?=4)|4(?=5)|5(?=6)|6(?=7)|7(?=8)|8(?=9)|9(?=0)){3}\d)/', $pin)) {
        $error = 'Please do not use consecutive digits as your PIN.';
      }
      $oldCardPin = self::meta($user, 'old_card_pin');
      if ($oldCardPin) {
          if (SSLEncryptionService::isEncrypted($oldCardPin)) {
              $oldCardPin = SSLEncryptionService::tryToDecrypt($oldCardPin);
          }
          if ($oldCardPin) {
              $oldPinList = explode(',', $oldCardPin);
              if (in_array(sha1($pin), $oldPinList)) {
                  $error = 'Please use a PIN that had not been used recently.';
              }
          }
      }
      return $error;
  }

  public static function storeCardPin(User $user, #[\SensitiveParameter] $pin) {

      $oldCardPin = self::meta($user, 'old_card_pin');
      if (SSLEncryptionService::isEncrypted($oldCardPin)) {
          $oldCardPin = SSLEncryptionService::tryToDecrypt($oldCardPin);
      }
      $oldPinList = $oldCardPin ? explode(',', $oldCardPin) : [];
      if (count($oldPinList) >=10 ) {
          $oldPinList = array_slice($oldPinList, count($oldPinList) - 9);
      }
      $oldPinList[] = sha1($pin);

      self::updateMeta($user, [
        'old_card_pin' => SSLEncryptionService::encrypt(implode(',', $oldPinList)),
      ]);
  }
  public static function removeAccents($string) {
    $normalized = Normalizer::normalize($string, Normalizer::FORM_D);
    $noAccents = preg_replace('/[\x{0300}-\x{036f}]/u', '', $normalized);
    return $noAccents;
  }
}
