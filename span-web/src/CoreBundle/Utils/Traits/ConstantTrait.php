<?php
/**
 * Created by PhpStorm.
 * User: hans
 * Date: 2017/12/26
 * Time: 11:18
 */

namespace CoreBundle\Utils\Traits;


use CoreBundle\Utils\Util;
use Stringy\Stringy;

trait ConstantTrait
{
    public static function getConstantsValuesStartWith($prefix)
    {
        return array_values(self::getConstantsStartWith($prefix));
    }

    public static function getConstantsStartWith($prefix, $removePrefix = false, Callable $filter = null)
    {
        $prefix = strtoupper($prefix);
        if (!Util::endsWith($prefix, '_')) {
            $prefix .= '_';
        }
        $rc = new \ReflectionClass(self::class);
        $cs = $rc->getConstants();
        $result = [];
        foreach ($cs as $k => $v) {
            if (Util::startsWith($k, $prefix)) {
                if (is_callable($filter) && !$filter($k, $v)) {
                    continue;
                }
                if ($removePrefix) {
                    $k = substr($k, strlen($prefix));
                }
                $result[$k] = $v;
            }
        }
        return $result;
    }

    public static function getConstantsKeyedStartWith($prefix, Callable $filter = null)
    {
        $items = self::getConstantsStartWith($prefix, false, $filter);
        $result = [];
        foreach ($items as $k) {
            $result[$k] = (string)Stringy::create($k)->humanize();
        }
        return $result;
    }

    public static function getConstantsKeyedStartWithArray($prefix, Callable $filter = null)
    {
        $items = self::getConstantsStartWith($prefix, false, $filter);
        $result = [];
        foreach ($items as $k) {
            $result[] = [
              'id' => $k,
              'name' => (string)Stringy::create($k)->humanize()
            ];
        }
        return $result;
    }
}
