<?php

namespace CoreBundle\Utils\Traits;


use Carbon\Carbon;
use CoreBundle\Utils\Excel;
use CoreBundle\Utils\Util;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Writer\Csv;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\HttpFoundation\StreamedResponse;

trait ExcelTrait
{
    /**
     * @var Worksheet
     */
    public $sheet;

    /**
     * @var integer
     */
    public $row;

    protected $textColumns;

    protected $rowCallback;

    protected $rowCache;

    public static $monoFields = [
        'Avg. Deposits', 'Amount of Deposits', 'Employee Count', 'Avg count of Transfers',
        'Total $ of Bank Transfers', 'Fee Credits', 'Count of Transfers', 'Avg. Transfer per Employee #',
        'Transfer Exchange Rate', 'UniTeller Revenue Fix', 'Total $ of Spend', 'Members with Loads',
        'Total $ of Other POS Spend', 'Avg USD/MXN Rate', 'Rapyd Balance', 'Platform Net Revenue Avg',
        'Avg $ of Spend', 'Total $ of PIN POS Spend', 'Bank Transfer $', 'Rapyd Fee', 'Platform Gross Revenue',
        'KYC Costs', 'Avg Bank Transfer $', 'Total count of Transfers', 'Cash Pickup $', 'Platform Net Revenue',
        'Cash Pickup #', 'Deposit Amount', 'Number of Requests', 'Amount', 'Transfer Amount', 'Members with Spend',
        'Total Count Of Transfers', 'Total Count Of Loads', 'Active Employees', 'Avg. Deposits per Employee #',
        'Total Transfer $', 'Revenue', 'Total count of Spend', 'Avg count of Spend', 'UniTeller Net Revenue Growth',
        'Avg $ of Loads', 'Total $ of ATM Spend', 'Total count of Loads',
        'Platform Net Revenue Growth', 'Count of Deposits',
        'Avg $ of Transfers', 'Partner Net Revenue AVG', 'Partner Net Revenue Avg', 'Transfer', 'Partner Net Revenue',
        'Partner Revenue Fix', 'Total Count Of Bank Transfers', 'Total Net Revenue', 'Total $ of Loads', 'FX Income',
        'Member Count', 'Avg. Transfer per Recipient #', 'Transfers Income', 'Avg Cash Pickup $', 'Revenue to settle',
        'Total Profit',
        'Recipient Count', 'Partner Gross Revenue', 'Members with Transfers', 'Balance',
        'Total $ of Cash Pickup Transfers',
        'Total $ of Transfers', 'Bank Transfer #', 'Avg count of Loads', 'Total Count Of Cash Pickup Transfers',
        'Rapyd Net Revenue Growth', 'Partner Net Revenue Growth', 'Platform Revenue Fix', 'Employees',
    ];

    public function cellValue($col) {
        $v = $this->sheet->getCell($col . $this->row)->getCalculatedValue();
        if (is_string($v)) {
            $v = trim($v);
        }
        return $v;
    }

    public function setCellValue($col, $row, $value) {
        $cell = $this->sheet->getCell([$col, $row]);
        if (in_array($col, $this->textColumns, true)) {
//            Log::debug('Set excel explicit value', compact('col', 'row', 'value'));
            $cell->setValueExplicit($value);
//                ->getStyle()
//                ->setFont($cell->getStyle()->getFont()->setBold(true)->setItalic(true)->setUnderline(true))
//                ->setQuotePrefix(true);
        } else {
            $cell->setValue($value);
        }
    }

    /**
     * @deprecated Use Excel::openExcel instead
     * @param $file
     * @return Spreadsheet
     */
    public function openExcel($file)
    {
        return Excel::openExcel($file);
    }

    public function generateExcel($title, $headers, $data, $callback, array $textColumns = [], $freeze = 'A2')
    {
        $excel = $this->createXSLObject($title);
        return $this->writeDataToExcelSheet($excel, 0, $headers, $data, $callback, $textColumns, $freeze);
    }

    public function generateSheetAppendToExcel(Spreadsheet $excel, $sheetTitle, $headers, $data, $callback, array $textColumns = [], $freeze = 'A2')
    {
        $active = $excel->getActiveSheet();
        $remove = $active->getHighestRow() === 1 && $active->getHighestColumn() === 'A';
        $removeIndex = $excel->getActiveSheetIndex();

        $sheet = new Worksheet();
        $sheet->setTitle($sheetTitle);
        $excel->addSheet($sheet);

        if ($remove) {
            $excel->removeSheetByIndex($removeIndex);
        }

        return $this->writeDataToExcelSheet($excel, $excel->getSheetCount() - 1, $headers, $data, $callback, $textColumns, $freeze);
    }

    public function generateExcelMultipleSheets($title, $headers, callable $sheetDataSource, $cellCallback,
                                                array $textColumns = [], $freeze = 'A2') {
        $excel = $this->createXSLObject($title);
        for ($i = 0; true; $i++) {
            $data = $sheetDataSource($i);
            if (!$data) {
                break;
            }
            $this->writeDataToExcelSheet($excel, $i, $headers, $data, $cellCallback, $textColumns, $freeze);
        }
        return $excel;
    }

    public function writeDataToExcelSheet(Spreadsheet $excel, $sheetIndex, $headers, $data, $callback,
                                          array $textColumns = [], $freeze = 'A2')
    {
        $excel->setActiveSheetIndex($sheetIndex);
        $this->sheet = $excel->getActiveSheet();

        $keys = array_flip(array_keys($headers));
        foreach ($textColumns as $i => $textColumn) {
            if (is_string($textColumn)) {
                $textColumns[$i] = $keys[$textColumn] + 1;
            }
        }
        $this->textColumns = $textColumns;

        $i = 1;
        foreach ($headers as $t => $width) {
            $this->setCellValue($i, 1, $t);
            $this->sheet->getColumnDimensionByColumn($i)->setWidth($width);
            $i++;
        }
        $this->sheet->getStyle('A1:ZZ1')->getFont()->setBold(true);
        $this->sheet->getStyle('A1:ZZ1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $this->sheet->freezePane($freeze);

        $count = count($data);
        $rowCallback = $this->rowCallback;
        for ($i = 0; $i < $count; $i++) {
            $row = $i + 2;
            $item = $data[$i];
            $j = 1;

            if (is_callable($rowCallback)) {
                $this->rowCache = [];
                $rowCallback($item, $this->rowCache, $row, $excel);
            }

            foreach ($headers as $t => $width) {
                $value = $callback($j, $item, $excel, $t) ?? '';
                if (
                    str_starts_with($value, "USD$") ||
                    str_starts_with($value, "$") ||
                    str_starts_with($value, "-$") ||
                    str_starts_with($value, "MX$") ||
                    str_starts_with($value, "USD $") ||
                    in_array($t, self::$monoFields)
                ) {
                    $keys = array_keys($headers);
                    $index = 1 + array_search($t, array_values($keys));
                    $columnLetter = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($index);
                    $cell = $columnLetter . $row;
                    $style = $this->sheet->getStyle($cell);
                    $style->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
                    $style->getFont()->setName('Courier New')->setBold(true);
                }

                $this->setCellValue($j, $row, $value);
                $j++;
            }
        }

        return $excel;
    }

    public function loadExcel($path)
    {
        return IOFactory::load($path);
    }

    public function createXSLObject($title = Util::SYSTEM_NAME)
    {
        $title = Util::filenameFilter($title);

        $spreadsheet = new Spreadsheet();
        $spreadsheet->getProperties()->setCreator(Util::SYSTEM_NAME)
            ->setLastModifiedBy(Util::SYSTEM_NAME)
            ->setTitle($title)
            ->setSubject($title)
            ->setDescription("Exported from " . Util::SYSTEM_NAME);
        $spreadsheet->setActiveSheetIndex(0);
        return $spreadsheet;
    }

    public function loadOrCreateExcel($path, $title)
    {
        if (file_exists($path)) {
            return $this->loadExcel($path);
        }
        return $this->createXSLObject($title);
    }

    public function fileNameFromTimestamp(Request $request, $prefix)
    {
        $time = ceil($request->get('query_timestamp', time() * 1000) / 1000);
        return $prefix . date(Util::DATE_TIME_FILE_FORMAT, $time);
    }

    public function getReportFileSuffix(Request $request)
    {
        $time = $request->get('query_timestamp', microtime(true) * 1000);
        return Carbon::createFromTimestamp((int)($time / 1000))
            ->format(Util::DATE_TIME_FILE_FORMAT);
    }

    public function responseExcel(Spreadsheet $spreadsheet)
    {
        $writer = new Xlsx($spreadsheet);
        $response = new StreamedResponse(
            function () use ($writer) {
                $writer->save('php://output');
            }
        );

        $dispositionHeader = $response->headers->makeDisposition(
            ResponseHeaderBag::DISPOSITION_ATTACHMENT,
            $spreadsheet->getProperties()->getTitle() . '.xlsx'
        );
        $response->headers->set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        $response->headers->set('Pragma', 'public');
        $response->headers->set('Cache-Control', 'maxage=1');
        $response->headers->set('Content-Disposition', $dispositionHeader);

        return $response;
    }

    public function flipSheet($origin)
    {
        $data = [];
        foreach ($origin as $i => $fields) {
            if ($i <= 0) {
                continue;
            }
            $row = [];
            foreach ($fields as $j => $field) {
                $key = $origin[0][$j];
                if (!$key) {
                    continue;
                }
                $row[$key] = $field;
            }
            $data[] = $row;
        }
        return $data;
    }

    public function getColumnSelector($column, $count)
    {
        return $column . '2:' . $column . ($count + 1);
    }

    public function getColumnStyle($column, $count)
    {
        $selector = $this->getColumnSelector($column, $count);
        return $this->sheet->getStyle($selector);
    }

    public function setColumnNumberPercent($column, $count)
    {
        $style = $this->getColumnStyle($column, $count);
        $style->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_PERCENTAGE_00);
    }

    public function setColumnNumberMoney($column, $count)
    {
        $style = $this->getColumnStyle($column, $count);
        $style->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_CURRENCY_USD);
    }

    public function setColumnMoneyFont($column, $count)
    {
        $style = $this->getColumnStyle($column, $count);
        $style->getFont()->setName('Courier New');
    }

    public function setColumnsNumberMoneyByTitles(array $headers, array $titles, $count)
    {
        $headers = array_keys($headers);
        $columns = [];
        foreach ($titles as $title) {
            $index = array_search($title, $headers);
            if ($index !== false) {
                $columns[] = Coordinate::stringFromColumnIndex($index);
            }
        }
        $this->setColumnsNumberMoney($columns, $count);
    }

    public function setColumnsNumberMoney(array $columns, $count)
    {
        foreach ($columns as $column) {
            $this->setColumnNumberMoney($column, $count);
            $this->setColumnMoneyFont($column, $count);
        }
    }

    public function setCellRangeMoney($range)
    {
        $this->sheet->getStyle($range)
            ->getNumberFormat()
            ->setFormatCode(NumberFormat::FORMAT_CURRENCY_USD);
    }

    public function setColumnWrapText($column, $count)
    {
        $style = $this->getColumnStyle($column, $count);
        $style->getAlignment()->setWrapText(TRUE);
        $style->getAlignment()->setVertical(Alignment::VERTICAL_TOP);
    }

    public function saveExcelTo(Spreadsheet $excel, $path)
    {
        $excel->setActiveSheetIndex(0); // var_dump($excel);
        $writer = new Xlsx($excel); // IOFactory::createWriter($excel, Xlsx::class);

        $writer->save($path);
    }

    public function saveCsvTo(Spreadsheet $csv, $path, $columnCount)
    {
        $csv->setActiveSheetIndex(0);
        $writer = new Csv($csv);
        $writer->save($path);
    }

    // Index starts from 1
    public function getIndexFromColumnCode($code)
    {
        $index = 0;
        foreach (str_split($code) as $letter) {
            $index *= 26;
            $index += ord($letter) - ord('A') + 1;
        }
        return $index;
    }

    // Index starts from 1
    public function getColumnCodeFromIndex($index)
    {
        $code = '';
        while ($index > 0) {
            $code = 'A' . $code;
            $rem = $index % 26;
            if ($rem === 0) {
                $code[0] = 'Z';
                $index = (int)($index / 26 - 1);
            } else {
                $code[0] = chr($rem - 1 + ord('A'));
                $index = (int)($index / 26);
            }
        }
        return $code;
    }

    public function getUpdatedFormula($formula, $newColumn, $newRow, $firstColumn, $firstRow)
    {
        return preg_replace_callback(
            '/(?:(?<!\$))(?:(?<!\$[A-Z]))(?:(?<!\$\d))(([A-Z]*)(\d*))/',
            function ($matches) use ($newColumn, $newRow, $firstColumn, $firstRow) {
                if (!$matches[0]) {
                    return '';
                }
                $column = $matches[2];
                if ($column !== '') {
                    $delta = $this->getIndexFromColumnCode($column) - $firstColumn;
                    $column = $this->getColumnCodeFromIndex($newColumn + $delta);
                }

                $row = $matches[3];
                if ($row !== '') {
                    $delta = (int)$row - $firstRow;
                    $row = $newRow + $delta;
                }

                return $column . $row;
            }, $formula);
    }

    public function createHeaderArray($titles, $width = 20)
    {
        $result = [];
        foreach ($titles as $title) {
            $result[$title] = $width;
        }
        return $result;
    }

    public function protectSheet(Spreadsheet $excel, #[\SensitiveParameter] string $password)
    {
        foreach ($excel->getAllSheets() as $sheet) {
            $sheet->getProtection()
                ->setSheet(true)
                ->setPassword($password);
        }
        return $excel->getSecurity()
            ->setLockWindows(true)
            ->setLockStructure(true)
            ->setWorkbookPassword($password);
    }
}
