<?php
/**
 * Created by PhpStorm.
 * User: hans
 * Date: 16/5/9
 * Time: 下午2:23
 */

namespace CoreBundle\Utils\Traits;


use Carbon\Carbon;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\QueryListParams;
use CoreBundle\Utils\Util;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\QueryBuilder;
use Symfony\Component\HttpFoundation\Request;
use CoreBundle\Utils\Log;

trait DbTrait
{
    /** @var QueryListParams */
    protected $queryParams;

    public function getResetParameterizedQuery()
    {
        $query = $this->queryParams->query;
        $query->setMaxResults(null)
            ->setFirstResult(null);
        return $query;
    }

    public function hasCompositeName()
    {
        return true;
    }

    public function getHasStringPartExpr(QueryBuilder $query, $key, $str, $separator = ',')
    {
        $expr = $query->expr();
        $name = '__' . Util::randString() . '__';
        $orx = $expr->orX(
            $expr->eq($key, ':eq' . $name),
            $expr->like($key, ':left' . $name),
            $expr->like($key, ':right' . $name),
            $expr->like($key, ':both' . $name)
        );
        $query->setParameter(':eq' . $name, $str)
            ->setParameter(':left' . $name, $str . $separator . '%')
            ->setParameter(':right' . $name, '%' . $separator . $str)
            ->setParameter(':both' . $name, '%' . $separator . $str . $separator . '%');
        return $orx;
    }

    /**
     * @return EntityManager
     */
    public function em(): EntityManagerInterface
    {
        $method = 'getDoctrine';
        return $this->$method()->getManager();
    }

    public function persist($entity)
    {
        $em = $this->em();
        $em->persist($entity);
        $em->flush();
    }

    public function prepareKeyword($keyword, $full = true)
    {
        $keyword = Util::inputFilter($keyword);
        if ($full) {
            $keyword = str_replace(' ', ' %', $keyword);
        }
        return strtolower("'%" . $keyword . "%'");
    }

    /**
     * @param QueryBuilder  $query
     * @param Request       $request
     * @param array         $fields
     * @param callable|null $cb
     */
    public function queryLike(QueryBuilder $query, Request $request, $fields = [], Callable $cb = null)
    {
        if ($request->get('keyword')) {
            $expr = $query->expr();
            $like = $this->prepareKeyword($request->get('keyword'));

            if ($cb) {
                $cb($query, $like);
            } else if ($fields) {
                $likes = [];
                foreach ($fields as $key => $field) {
                    if (is_string($key) && Util::endsWith($key, ' in')) {
                        $likes[] = $expr->in(str_replace(' in', '', $key), $field);
                    } else if ($field === 'u.name' && $this->hasCompositeName()) {
                        $likes[] = $expr->orX(
                            $expr->like('u.firstName', $like),
                            $expr->like('u.lastName', $like),
                            $expr->like(
                                $expr->concat(
                                    'u.firstName',
                                    $expr->concat($expr->literal(' '), 'u.lastName')
                                ),
                                $like
                            )
                        );
                    } else {
                        $likes[] = $expr->like('lower(' . $field . ')', $like);
                    }
                }

                $or = call_user_func_array([
                    $expr,
                    'orX'
                ], $likes);
                $query->andWhere($or);
            }
        }
    }

    /**
     * @param QueryBuilder $query
     * @param Request      $request
     */
    public function queryFilter(QueryBuilder $query, Request $request)
    {
        $filters = $request->get('filter');
        if ($filters) {
            if (is_string($filters)) {
                $filters = (array)json_decode($filters, TRUE);
            }
            foreach ($filters as $key => $value) {
                if ($value === '' || $value === NULL) {
                    continue;
                }
                $and = false;
                $andValue = null;
                if (Util::endsWith($key, '`')) {
                    $key = substr($key, 0, -1);
                    $value = json_decode($value, true);
                } else {
                    $andPos = strpos($key, '+');
                    if ($andPos !== false) {
                        $and = substr($key, $andPos + 1);
                        $key = substr($key, 0, $andPos);
                        [$value, $andValue] = json_decode($value, true);
                    }
                }
                $this->queryFilterDoQuery($query, $key, $value);

                if ($and && $andValue !== null && $andValue !== '') {
                    $this->queryFilterDoQuery($query, $and, $andValue);
                }
            }
        }
        $filterRanges = $request->get('range');
        if (is_array($filterRanges)) {
          $expr = $query->expr();
          foreach($filterRanges as $key => $item) {
            if ($key != 'start' && $key !='end') {
              if(isset($item['min'])) {
                $v = $item['min'];
                if (stripos($key, 'balance') !== false || stripos($key, 'amount') !== false) {
                  $v *= 100;
                }
                $query->andWhere($expr->gte($key, $v));
              }
              if (isset($item['max'])) {
                $v = $item['max'];
                if (stripos($key, 'balance') !== false || stripos($key, 'amount') !== false) {
                  $v *= 100;
                }
                $query->andWhere($expr->lte($key,  $v));
              }
            }
          }
        }

    }

    protected function queryFilterDoQuery(QueryBuilder $query, $key, $value)
    {
        $expr = $query->expr();

        $eq = false;
        $and = false;
        $andEq = false;
        $andPos = strpos($key, '+');
        if ($andPos !== false) {
            $and = substr($key, $andPos + 1);
            if (Util::endsWith($and, '=')) {
                $and = substr($and, 0, -1);
                $andEq = true;
            }

            $key = substr($key, 0, $andPos);
            $value = array_map(function ($v) {
                return json_decode($v, true);
            }, $value);
        }
        if (Util::endsWith($key, '=')) {
            $key = substr($key, 0, -1);
            $eq = true;
        }

        if (is_string($value)) {
            $value = Util::inputFilter(strtolower($value));
            if ($value === 'is null') {
                $query->andWhere($expr->isNull($key));
            } else if ($value === 'is not null') {
                $query->andWhere($expr->isNotNull($key));
            } else if ($eq) {
                $query->andWhere($key . '=\'' . $value . '\'');
            } else if ($key === 'u.name' && $this->hasCompositeName()) {
                $value = "'%" . $value . "%'";
                $query->andWhere($expr->orX(
                    $expr->like('u.firstName', $value),
                    $expr->like('u.lastName', $value),
                    $expr->like(
                        $expr->concat(
                            'u.firstName',
                            $expr->concat($expr->literal(' '), 'u.lastName')
                        ),
                        $value
                    )
                ));
            } else {
                $key = 'lower(' . $key . ')';
                $value = "'%" . $value . "%'";

                $query->andWhere($expr->like($key, $value));
            }
        } else if (is_array($value)) {
            $orx = [];
            foreach ($value as $v) {
                $andX = [];
                if ($and) {
                    $andValue = $v[1];
                    $v = $v[0];
                    if ($andValue !== null && $andValue !== '') {
                        $andX[] = $this->queryLikeSimple($expr, $and, $andValue, $andEq);
                    }
                }
                $andX[] = $this->queryLikeSimple($expr, $key, $v, $eq);
                $orx[] = $expr->andX(...$andX);
            }
            $query->andWhere($expr->orX(...$orx));
        } else {
            $query->andWhere($expr->eq($key, $value));
        }
    }

    protected function queryLikeSimple($expr, $key, $value, $eq = false)
    {
        if (is_string($value)) {
            return $expr->like($key, "'" . ($eq ? '' : '%') . $value . ($eq ? '' : '%') . "'");
        }
        return $expr->eq($key, $value);
    }

    /**
     * @param QueryBuilder $query
     * @param Request      $request
     */
    public function queryNotFilter(QueryBuilder $query, Request $request)
    {
        $not = $request->get('__not');
        if ($not) {
            if (is_string($not)) {
              $not = (array)json_decode($not, TRUE);
            }
            $filters = [];
            foreach ($not as $i => $n) {
              if (Util::startsWith($i, 'otherInfo')) {
                continue;
              }
              if (is_string($n)) {
                if (mb_strpos($i, 'filter[') === 0 && !Util::endsWith($i, ']')) {
                    $name = mb_substr($i, 7);
                    $filters[$name] = $n;
                }
              } else if (is_array($n)) {
                $name = mb_substr($i, 6);
                $filters[$name] = $n;
              }
            }

            $expr = $query->expr();
            foreach ($filters as $key => $value) {
                if ($value === '' || $value === NULL) {
                    continue;
                }
                if (Util::endsWith($key, '`')) {
                    $key = substr($key, 0, -1);
                    $value = json_decode($value, true);
                }
                $neq = false;
                if (Util::endsWith($key, '=')) {
                    $key = substr($key, 0, -1);
                    $neq = true;
                }
                if (is_string($value)) {
                    $value = Util::inputFilter(strtolower($value));
                    if ($value === 'is null') {
                        $query->andWhere($expr->isNotNull($key));
                    } else if ($value === 'is not null') {
                        $query->andWhere($expr->isNull($key));
                    } else if ($neq) {
                        $query->andWhere($key . '<>\'' . $value . '\'');
                    } else if ($key === 'u.name' && $this->hasCompositeName()) {
                        $value = "'%" . $value . "%'";
                        $query->andWhere($expr->orX(
                            $expr->notLike('u.firstName', $value),
                            $expr->notLike('u.lastName', $value),
                            $expr->notLike(
                                $expr->concat(
                                    'u.firstName',
                                    $expr->concat($expr->literal(' '), 'u.lastName')
                                ),
                                $value
                            )
                        ));
                    } else {
                        if ($key === 'id' || Util::endsWith($key, '.id')) {
                            $key = 'cast(' . $key . ' as string)';
                        }
                        $key = 'lower(' . $key . ')';
                        $value = "'%" . $value . "%'";

                        $query->andWhere($expr->notLike($key, $value));
                    }
                } else if (is_array($value)) {
                    if ($neq) {
                        $query->andWhere($expr->notIn($key, $value));
                    } else {
                        $orx = [];
                        foreach ($value as $k => $v) {
                            if ($k == 'min') {
                              if (stripos($key, 'balance') !== false || stripos($key, 'amount') !== false) {
                                $v *= 100;
                              }
                              $orx[] = $expr->lt($key, $v);
                            } else if ($k == 'max') {
                              if (stripos($key, 'balance') !== false || stripos($key, 'amount') !== false) {
                                $v *= 100;
                              }
                              $orx[] = $expr->gt($key, $v);
                            } else {
                              $orx[] = $expr->notLike($key, "'%" . $v . "%'");
                            }
                        }
                        $query->andWhere(call_user_func_array([$expr, 'orX'], $orx));
                    }
                } else {
                    $query->andWhere($expr->neq($key, $value));
                }
            }
        }
    }

    /**
     * @param QueryBuilder    $query
     * @param Request         $request
     * @param QueryListParams $params
     */
    public function queryDateRange(QueryBuilder $query, Request $request, QueryListParams $params)
    {
        $ranges = $request->get('range');
        if ($ranges) {
            if (is_string($ranges)) {
                $ranges = (array)json_decode($ranges, TRUE);
            }
            foreach ($ranges as $field => $dates) {
                // Date Range
                $start = $dates['start'] ?? null;
                $end = $dates['end'] ?? null;
                $expr = $query->expr();
                if ($start) {
                    $start = Util::timeUTC($start);
                    $query->andWhere($expr->gte($field, ':pDateStart'))
                        ->setParameter('pDateStart', $start);
                }
                if ($end) {
                    $end = Util::timeUTC($end);
                    $query->andWhere($expr->lt($field, ':pDateEnd'))
                        ->setParameter('pDateEnd', $end->addDay());
                }

                // Value Range
                $min = $dates['min'] ?? null;
                $max = $dates['max'] ?? null;
                if ($min) {
                    if ($this->isRangeMoneyNeedsToBeFix($params, $field)) {
                        $min = Money::normalizeAmount($min, 'USD');
                    }
                    $query->andWhere($expr->gte($field, ':pValueMin'))
                        ->setParameter('pValueMin', $min);
                }
                if ($max) {
                    if ($this->isRangeMoneyNeedsToBeFix($params, $field)) {
                        $max = Money::normalizeAmount($max, 'USD');
                    }
                    $query->andWhere($expr->lte($field, ':pValueMax'))
                        ->setParameter('pValueMax', $max);
                }
            }
        }
    }

    protected function isRangeMoneyNeedsToBeFix(QueryListParams $params, $field)
    {
        if (!$params->fixRangeMoney) {
            return false;
        }
        foreach ([
            '.balance',
            '.amount',
            '.txnAmount',
        ] as $item) {
            if (Util::endsWith($field, $item)) {
                return true;
            }
        }
        return false;
    }

    protected function removeOrderByFromRequest(Request $request, array $keys, QueryListParams $params = null)
    {
        $result = [];
        foreach ($keys as $key) {
            $found = $this->getSortOrderFromRequest($request, $key);
            if ($found !== null) {
                $result[$key] = $found;
            }
        }
        if ($result) {
            $request->query->remove('orderBy');
            $request->request->remove('orderBy');

            if ($params) {
                $params->pagination = null;
            }
        }
        return $result;
    }

    protected function sliceQueryArrayByOrderByRemoved(&$result, $removed, $page, $limit)
    {
        if ($removed) {
            Util::usort($result['data'], $removed);
            $result['data'] = $this->sliceQueryArrayBy($result['data'], $page, $limit);
        }

        return $result;
    }

    protected function sortArrayByOrderByRemoved(&$data, $removed)
    {
        if ($removed) {
            Util::usort($data, $removed);
        }

        return $data;
    }

    protected function getSortOrderFromRequest(Request $request, $queryKey)
    {
        $orderBy = $request->get('orderBy');
        if (!$orderBy) {
            return null;
        }
        $ob = explode(', ', $orderBy);
        $order = 'ASC';
        foreach ($ob as $i => $o) {
            if ($i === 0) {
                $order = $o[0] === '-' ? 'DESC' : 'ASC';
                $field = substr($o, 1);
            } else {
                $field = $o;
            }
            if ($field === '___' . $queryKey) {
                return $order;
            }
        }
        return null;
    }

    protected function queryAddOrderGroup(QueryListParams $params) {
        $query = $params->query;
        $request = $params->request;

        if ($params->groupBy) {
            $query->groupBy($params->groupBy);
        }

        $orderBy = $request->get('orderBy');
        if ($orderBy) {
            if (($orderBy === '+u.name' || $orderBy === '-u.name') && $this->hasCompositeName()) {
                $orderBy = str_replace('u.name', 'u.firstName, u.lastName', $orderBy);
            }
            $ob = explode(', ', $orderBy);
            $order = 'ASC';
            foreach ($ob as $i => $o) {
                if (strpos($o, '___') !== false) {
                    continue;
                }
                if ($i === 0) {
                    $order = $o[0] === '-' ? 'DESC' : 'ASC';
                    $field = substr($o, 1);
                    $query->orderBy($field, $order);
                } else {
                    $field = $o;
                    $query->addOrderBy($field, $order);
                }
            }
        } else if ($params->orderBy) {
            $firstKey = array_keys($params->orderBy)[0];
            foreach ($params->orderBy as $key => $order) {
                if ($key === $firstKey) {
                    $query->orderBy($key, $order);
                } else {
                    $query->addOrderBy($key, $order);
                }
            }
        }

        if ($params->distinct) {
            $query->distinct();
        }
    }

    public function getPageSizeFromParams(QueryListParams $params): int
    {
        $request = $params->request;
        $pageSize = $request->get('pageSize') ?: 10;
        if (isset($params->pagination['size'])) {
            $pageSize = $params->pagination['size'];
        }

        if ($pageSize <= 0) {
            $pageSize = 2147483647; // MySQL max int
        }

        return (int)$pageSize;
    }

    public function getPageStartFromParams(QueryListParams $params, int|string $pageSize, int|string $count = null)
    {
        $request = $params->request;
        $page = 1;
        if ($request->get('pageIndex')) {
            $page = $request->get('pageIndex');
        } else if ($request->get('page')) {
            $page = $request->get('page');
        } else if (isset($params->pagination['page'])) {
            $page = $params->pagination['page'];
        }

        $pageSize = (int)$pageSize;
        $start = ($page - 1) * $pageSize;
        if ($count !== null && $start >= ((int)$count + $pageSize)) {
            $start = 0;
        }
        return $start;
    }

    public function queryJoin(QueryListParams $params) {
        $query = $params->query;
        $request = $params->request;
        $joins = $params->joins ?: [];
        $optionals = $params->optionalJoins ?: [];
        foreach ($joins as $name => $field) {
            $query->join($field, $name);
        }

        $allJoins = array_merge($joins, $optionals);
        $filters = $request->get('filter', []);
        foreach ($optionals as $name => $field) {
            if (isset($joins[$name])) {
                continue;
            }
            $find = $name . '.';
            $do = false;
            foreach ($filters as $filter => $_) {
                if (Util::startsWith($filter, $find) && $_) {
                    $do = true;
                    break;
                }
            }
            if ($do) {
                $temps = [];
                $last = $name;
                do {
                    $dependency =  $allJoins[$last];
                    $temps[$last] = $dependency;
                    $last = explode('.', $dependency)[0];
                } while (!isset($joins[$last]) && isset($allJoins[$last]));

                $temps = array_reverse($temps);
                foreach ($temps as $key => $value) {
                    $query->join($value, $key);
                    $joins[$key] = $value;
                }
            }
        }
    }

    /**
     * @param QueryListParams $params
     * @param                 $count
     *
     * @return array
     */
    public function queryAll(QueryListParams $params, $count = null) {
        $query = $params->query;
        $this->queryAddOrderGroup($params);

        if ($params->pagination) {
            $pageSize = $this->getPageSizeFromParams($params);
            $start = $this->getPageStartFromParams($params, $pageSize, $count);

            $query->select($params->dataSelect)
                ->setFirstResult($start)
                ->setMaxResults($pageSize);
        } else {
            $query->select($params->dataSelect);
        }

        return $query->getQuery()->getResult($params->hydrationMode);
    }

    public function prepareDataForPaginator($rs, QueryListParams $params, $count)
    {
        $pageSize = $this->getPageSizeFromParams($params);
        $start = $this->getPageStartFromParams($params, $pageSize, $count);
        $prefix = [];
        $suffix = [];
        for ($i = 0; $i < $start; $i++) {
            $prefix[] = [];
        }
        for ($i = $start + $pageSize; $i < $count; $i++) {
            $suffix[] = [];
        }
        return array_merge($prefix, $rs, $suffix);
    }

    public function sliceQueryArray($array, Request $request) {
        $pageSize = $request->get('pageSize') ?: 10;
        $page = 1;
        if ($request->get('pageIndex')) {
            $page = $request->get('pageIndex');
        } else if ($request->get('page')) {
            $page = $request->get('page');
        }
        return $this->sliceQueryArrayBy($array, $page, $pageSize);
    }

    public function sliceQueryArrayBy($array, $page, $limit)
    {
        return array_slice($array, $limit * ($page - 1), $limit);
    }

    public function inOrders(Request $request, array $names)
    {
        $key = $request->get('orderBy');
        if (!$key) {
            return FALSE;
        }
        $key = str_replace(['+', '-'], '', $key);
        if (in_array($key, $names, true)) {
            $request->query->remove('orderBy');
            return TRUE;
        }
        return FALSE;
    }

    /**
     * @param QueryBuilder $query
     * @param              $select
     *
     * @return mixed
     */
    public function queryCount(QueryBuilder $query, $select)
    {
        $countQuery = clone $query;
        $count = 0;
        try {
            $count = $countQuery->distinct()
                ->select($select)
                ->getQuery()
                ->getSingleScalarResult();
        } catch (NonUniqueResultException $e) {
            Log::warn('no unique result: ' . $e->getMessage(), [
                $e->getTrace()
            ]);
        } catch (NoResultException $e) {
            Log::warn('no result: ' . $e->getMessage());
        }
        return (int)$count;
    }

    public function queryListForPaginator(QueryListParams $params, $addOrderGroup = true)
    {
        $query = $params->query;
        $request = $params->request;

        $this->queryJoin($params);

        if ($params->searchFields) {
            $this->queryLike($query, $request, $params->searchFields);
        }
        $this->queryFilter($query, $request);
        $this->queryDateRange($query, $request, $params);

        if ($addOrderGroup) {
            $this->queryAddOrderGroup($params);
        }

        if (is_callable($params->extraQueryCallback)) {
            $method = $params->extraQueryCallback;
            $method($query, $request);
        }

        return $query;
    }

    public function queryListForAPI(QueryListParams $params, Callable $eachCallback = null, Callable $dataCallback = null)
    {
        $result = $this->queryList($params);

        if ($dataCallback) {
            $result['data'] = $dataCallback($result['data']);
        }

        if (!$eachCallback) {
            $result['data'] = Util::toApiArray($result['data']);
        } else {
            $result['data'] = array_map($eachCallback, $result['data']);
        }
        return $result;
    }

    public function queryListForAPIWithOrderByKeys(QueryListParams $params, Request $request, $page, $limit, array $keys = [], Callable $eachCallback = null, Callable $dataCallback = null)
    {
        $removed = $this->removeOrderByFromRequest($request, $keys, $params);
        $result = $this->queryListForAPI($params, $eachCallback, $dataCallback);
        return $this->sliceQueryArrayByOrderByRemoved($result, $removed, $page, $limit);
    }

    /**
     * @param QueryListParams $params
     * @param callable        $callback
     *
     * @return array
     */
    public function queryList(QueryListParams $params, $callback = NULL)
    {
        $query = $params->query;
        $request = $params->request;

        $this->queryJoin($params);

        if ($params->searchFields || $params->searchQueryCb) {
            $this->queryLike($query, $request, $params->searchFields, $params->searchQueryCb);
        }
        $this->queryFilter($query, $request);
        $this->queryNotFilter($query, $request);
        $this->queryDateRange($query, $request, $params);
        $count = $this->queryCount($query, $params->countSelect);

        if (is_callable($params->extraQueryCallback)) {
            $method = $params->extraQueryCallback;
            $method($query, $request);
        }
        $data = $this->queryAll($params, $count);

        if ($params->fixArray) {
            $oldData = $data;
            $data = [];
            foreach ($oldData as $oldItem) {
                $i = 0;
                $item = [];
                foreach ((array)$oldItem as $k => $v) {
                    if ($i === 0) {
                        $item = $v;
                        $i++;
                    } else {
                        $item[$k] = $v;
                    }
                }
                $data[] = $item;
            }
        }

        if ($params->splitBy) {
            $oldData = $data;
            $data = [];
            $by = $params->splitBy;
            foreach ($oldData as $item) {
                if (isset($item[$by]) && count($item[$by]) > 1) {
                    foreach ((array)$item[$by] as $_) {
                        $_item = $item;
                        $_item[$by] = [
                            $_
                        ];
                        $data[] = $_item;
                    }
                } else {
                    $data[] = $item;
                }
            }
        }

        if (is_callable($callback)) {
            $_data = [];
            foreach ($data as $item) {
                $arr = Util::e2a($item);
                $callback($arr, $item);
                $_data[] = $arr;
            }
            $data = $_data;
        }

        return [
            'count' => $count,
            'data' => $data,
        ];
    }

    public function queryCountForExport(QueryListParams $params)
    {
        $query = $this->queryListForPaginator($params, false);
        return $this->queryCount($query, $params->countSelect);
    }

    public function queryListForExport(QueryListParams $params, $from = 0, $limit = 5000, Callable $rowCallback = null)
    {
        $query = $this->queryListForPaginator($params);
        $data = $query->select($params->dataSelect)
            ->setFirstResult($from)
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult($params->hydrationMode);
        if ($rowCallback) {
            return array_map($rowCallback, $data);
        }
        return $data;
    }

    public function queryListForDataOnly(QueryListParams $params, Callable $rowCallback = null)
    {
        $query = $this->queryListForPaginator($params);

        if ($params->pagination) {
            $pageSize = $this->getPageSizeFromParams($params);
            $start = $this->getPageStartFromParams($params, $pageSize);

            $query->setFirstResult($start)
                ->setMaxResults($pageSize);
        }

        $data = $query->select($params->dataSelect)
            ->getQuery()
            ->getResult($params->hydrationMode);
        if ($rowCallback) {
            return array_map($rowCallback, $data);
        }
        return $data;
    }

    public function queryListForExportWithOrderByKeys(QueryListParams $params, Request $request, $from = 0, $limit = 5000, array $keys = [], Callable $rowCallback = null)
    {
        $removed = $this->removeOrderByFromRequest($request, $keys);
        $data = $this->queryListForExport($params, $from, $limit, $rowCallback);
        return $this->sortArrayByOrderByRemoved($data, $removed);
    }

    public function translateDrPicker(Request $request, $toField)
    {
        $range = $request->get('range');
        if (isset($range['dr_picker']['start'], $range['dr_picker']['end'])) {
            $range[$toField] = [
                'start' => Carbon::createFromTimestamp($range['dr_picker']['start'])->format(Util::DATE_FORMAT),
                'end' => Carbon::createFromTimestamp($range['dr_picker']['end'])->format(Util::DATE_FORMAT),
            ];
            unset($range['dr_picker']);
            $request->request->set('range', $range);
        }
    }
}
