<?php
/**
 * Created by PhpStorm.
 * User: hans
 * Date: 2017/3/29
 * Time: 下午4:01
 */

namespace CoreBundle\Twig;


use CoreBundle\Entity\Config;
use CoreBundle\Entity\Module;
use CoreBundle\Entity\Platform;
use CoreBundle\Utils\Bundle;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Util;
use Doctrine\Common\Collections\Collection;
use Parsedown;
use Symfony\Component\HttpFoundation\Response;
use Twig\Extension\AbstractExtension;
use Twig\Extension\GlobalsInterface;
use Twig\TwigFilter;
use Twig\TwigFunction;
use UsUnlockedBundle\Services\Coinflow\CoinflowAPI;
use function Stringy\create as s;

class AppTwigExtension extends AbstractExtension implements GlobalsInterface
{
    public function getFilters()
    {
        return [
            new TwigFilter('url', [$this, 'url']),
            new TwigFilter('bool', [$this, 'bool']),
            new TwigFilter('humanize', [$this, 'humanize']),
            new TwigFilter('spaced', [$this, 'spaced']),
            new TwigFilter('implode', [$this, 'implode']),
            new TwigFilter('money_format', [$this, 'moneyFormat']),
            new TwigFilter('money_format_amount', [$this, 'moneyFormatAmount']),
            new TwigFilter('money_normalize_amount', [$this, 'moneyNormalizeAmount']),
            new TwigFilter('s2j', [$this, 's2j']),
            new TwigFilter('date', [$this, 'date']),
            new TwigFilter('json_format', [$this, 'jsonFormat']),
            new TwigFilter('extract', [$this, 'extract']),
            new TwigFilter('comma', [$this, 'comma']),
            new TwigFilter('slugify', [$this, 'slugify']),
            new TwigFilter('defaultEx', [$this, 'defaultEx']),
        ];
    }

    public function getFunctions()
    {
        return [
            new TwigFunction('dev', [$this, 'dev']),
            new TwigFunction('staging', [$this, 'staging']),
            new TwigFunction('demo', [$this, 'demo']),
            new TwigFunction('demoLabel', [$this, 'demoLabel']),
            new TwigFunction('eqv', [$this, 'eqv']),
            new TwigFunction('_v', [$this, '_v']),
            new TwigFunction('_customize_v', [$this, '_customize_v']),
            new TwigFunction('_t', [$this, '_t']),
            new TwigFunction('var_dump', [$this, 'var_dump']),
            new TwigFunction('userWithDeleted', [$this, 'userWithDeleted']),
            new TwigFunction('util_const', [$this, 'utilConst']),
            new TwigFunction('host', [$this, 'host']),
            new TwigFunction('isStagingHost', [$this, 'isStagingHost']),
            new TwigFunction('util', [$this, 'util']),
            new TwigFunction('thisUrl', [$this, 'thisUrl']),
            new TwigFunction('query_string', [$this, 'queryString']),
            new TwigFunction('cookie', [$this, 'cookie']),
            new TwigFunction('includes', [$this, 'includes']),
            new TwigFunction('type', [$this, 'type']),
            new TwigFunction('detect', [$this, 'detect']),
            new TwigFunction('i18n', [$this, 'i18n']),
            new TwigFunction('overwrite_chain', [$this, 'overwriteChain']),
            new TwigFunction('accessible', [$this, 'accessible']),
            new TwigFunction('editable', [$this, 'editable']),
            new TwigFunction('session_key', [$this, 'sessionKey']),
            new TwigFunction('meta', [$this, 'meta']),
            new TwigFunction('count', [$this, 'count']),
            new TwigFunction('cp', [$this, 'cp']),
            new TwigFunction('platform', [$this, 'platform']),
            new TwigFunction('layoutPath', [$this, 'layoutPath']),
            new TwigFunction('cpVariables', [$this, 'cpVariables']),
            new TwigFunction('esSolo', [$this, 'esSolo']),
            new TwigFunction('usu', [$this, 'usu']),
            new TwigFunction('transferMex', [$this, 'transferMex']),
            new TwigFunction('spendr', [$this, 'spendr']),
            new TwigFunction('otherPlatforms', [$this, 'otherPlatforms']),
            new TwigFunction('isRootOrUsu', [$this, 'isRootOrUsu']),
            new TwigFunction('supportReshipper', [$this, 'supportReshipper']),
            new TwigFunction('markdown', [$this, 'markdown']),
            new TwigFunction('statusText', [$this, 'statusText']),
            new TwigFunction('str_replace', [$this, 'str_replace']),
            new TwigFunction('isNewUsuEnabled', [$this, 'isNewUsuEnabled']),
            new TwigFunction('localizeJsOptions', [$this, 'localizeJsOptions']),
            new TwigFunction('cashOnWeb', [$this, 'cashOnWeb']),
            new TwigFunction('nSurePartnerId', [$this, 'nSurePartnerId']),
        ];
    }

    public function getGlobals() : array
    {
        return array(
            'Module' => Module::getConstantsStartWith('ID_'),
        );
    }

    public function date($date, $format = null, $timezone = null)
    {
        return Util::formatDateTime($date, $format, $timezone);
    }

    public function url($url)
    {
        return Util::fixFilePath($url);
    }

    public function dev($absolute = false)
    {
        return Util::isDev($absolute);
    }

    public function staging($withLocal = false)
    {
        return Util::isStaging($withLocal);
    }

    public function demo()
    {
        return Util::isDemo();
    }

    public function demoLabel()
    {
        if (Util::isDemo()) {
            return Util::demoLabel();
        }
        return '';
    }

    public function eqv($a, $b, $v = 'active')
    {
        return $a === $b ? $v : '';
    }

    public function _v()
    {
        return Util::version();
    }

    public function _customize_v()
    {
        $version = Util::version();

        $platform = Util::platform();
        if ($platform) {
            return $version . '_' . $platform->getCustomizeVersion();
        }

        return $version;
    }

    public function var_dump($v)
    {
        /** @noinspection ForgottenDebugOutputInspection */
        var_dump(Util::e2a($v));
    }

    public function bool($v)
    {
        return $v ? 'Yes' : 'No';
    }

    public function moneyFormat($amount, $currency, $withCurrency = true, $noPrefix = false)
    {
        if (null === $amount || '' === $amount || !$currency) {
            return '';
        }
        $s = Money::format($amount, $currency, $withCurrency);
        if ($noPrefix) {
            $s = str_replace('$', '', $s);
        }
        return $s;
    }

    public function moneyFormatAmount($amount, $currency, $thousands_sep = ',')
    {
        if (null === $amount || '' === $amount || !$currency) {
            return '';
        }
        return Money::formatAmount($amount, $currency, $thousands_sep);
    }

    public function moneyNormalizeAmount($amount, $currency)
    {
        if (null === $amount || '' === $amount || !$currency) {
            return '';
        }
        return Money::normalizeAmount($amount, $currency);
    }

    public function userWithDeleted($entity)
    {
        return Util::getUserWithDeleted($entity);
    }

    public function humanize($v)
    {
        return s($v)->humanize();
    }

    /**
     * Added whitespaces to humanized string
     *
     * @param $v
     *
     * @return string
     */
    public function spaced($v)
    {
        $chars = str_split($v);
        for ($i = count($chars) - 1; $i >= 0; $i--) {
            $char = $chars[$i];
            $upper = mb_strtoupper($char);
            if ($upper === $char && $i > 0) {
                array_splice($chars, $i, 0, ' ');
            }
        }
        return implode('', $chars);
    }

    public function slugify($v)
    {
        return s($v)->slugify();
    }

    public function implode($arr, $separator = ', ')
    {
        return Util::implode($arr, $separator);
    }

    public function s2j($s, $key = null)
    {
        $v = Util::s2j($s);
        if (!$key) {
            return $v;
        }
        if (isset($v[$key])) {
            return $v[$key];
        }
        return null;
    }

    public function utilConst($name)
    {
        $cls = new \ReflectionClass(Util::class);
        return $cls->getConstant($name);
    }

    public function util($method, $args = [])
    {
        if (!is_array($args)) {
            $args = [$args];
        }
        return Util::$method(...$args);
    }

    public function _t(string $text, array $args = [], string $dict = 'localize-es-phrases')
    {
        return Util::t($text, $args, $dict);
    }

    public function jsonFormat($value)
    {
        return json_encode($value, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    }

    public function host($configured = false)
    {
        return Util::host($configured);
    }

    public function isStagingHost($host)
    {
        return Util::startsWith($host, 'staging.');
    }

    public function queryString()
    {
        $request = Util::request();
        return $request->getQueryString();
    }

    public function cookie($name)
    {
        $request = Util::request();
        return $request->cookies->get($name);
    }

    public function extract($entity, $keys = ['id', 'name'])
    {
        $all = Util::e2a($entity);
        $result = [];
        foreach ((array)$all as $item) {
            $r = [];
            foreach ((array)$item as $k => $v) {
                if (in_array($k, $keys, true)) {
                    $r[$k] = $v;
                }
            }
            $result[] = $r;
        }
        return $result;
    }

    public function includes($array, $v)
    {
        return Util::includes($array, $v);
    }

    public function comma($v)
    {
        return str_replace(',', ', ', $v);
    }

    public function type($v)
    {
        return gettype($v);
    }

    public function detect()
    {
        return Util::detect();
    }

    public function i18n()
    {
        $i18n = Util::request()->cookies->get('i18n');
        if (!$i18n) {
            return 'en';
        }
        if ($i18n === 'false') {
            return false;
        }
        return $i18n;
    }

    public function defaultEx($v, $d = '')
    {
        return $v ?: $d;
    }

    public function overwriteChain(array $values, $valueToOverwrite = '')
    {
        foreach ($values as $value) {
            if ($value && $value !== $valueToOverwrite) {
                return $value;
            }
        }
        if ($values) {
            return $values[0];
        }
        return $valueToOverwrite;
    }

    public function accessible($module)
    {
        return Util::accessible($module);
    }

    public function editable($module)
    {
        return Util::editable($module);
    }

    public function sessionKey()
    {
        $id = Util::request()->cookies->get('PHPSESSID') ?: (string)time();
        return md5($id);
    }

    public function thisUrl()
    {
        return Util::request()->getRequestUri();
    }

    public function meta($entity, $key = null, $field = 'meta')
    {
        return Util::json($entity, $field, $key);
    }

    public function count($arr)
    {
        if ($arr instanceof Collection) {
            return $arr->count();
        }
        return count($arr);
    }

    public function cp()
    {
        return Util::cardProgram();
    }

    public function platform()
    {
        return Util::platform();
    }

    public function layoutPath()
    {
        return Platform::getLayoutPathOf(Util::platform());
    }

    public function cpVariables()
    {
        $params = [];
        Util::fillUserVariablesToRender($params);
        Util::fillPlatformVariablesToRender($params);
        Util::fillAffiliateVariablesToRender($params);
        return $params;
    }

    public function esSolo()
    {
        $platform = Util::platform();
        return $platform && $platform->isEsSolo();
    }

    public function usu()
    {
        return Bundle::isUsUnlocked();
    }

    public function transferMex()
    {
        return Bundle::isTransferMex();
    }

    public function cashOnWeb()
    {
        return Bundle::isCashOnWeb();
    }

    public function spendr()
    {
        return Bundle::isSpendr();
    }

    public function otherPlatforms()
    {
        $platform = Util::platform();
        return $platform && !$platform->isTernCommerce();
    }

    public function isRootOrUsu()
    {
        return Bundle::isRootOrUsu();
    }

    public function nSurePartnerId()
    {
        return CoinflowAPI::getNSurePartnerId();
    }

    public function supportReshipper()
    {
        if ($this->esSolo()) {
            return false;
        }
        return true;
    }

    public function markdown($v)
    {
        $provider = new Parsedown();
        return $provider->line($v);
    }

    public function statusText($code)
    {
        return Response::$statusTexts[$code] ?? $code;
    }

    public function str_replace($search, $replaceWith, $str)
    {
        return str_replace($search, $replaceWith, $str);
    }

    public function isNewUsuEnabled()
    {
        return Config::isNewUsuEnabled();
    }

    public function localizeJsOptions($others = [])
    {
        $options = Bundle::common('getLocalizeJsOptions', [], [
            'key' => 'GQxmjushEep5N',
            'rememberLanguage' => true,
            'saveNewPhrasesFromSource' => true,
        ]);
        return array_merge($options, $others ?: []);
    }
}
