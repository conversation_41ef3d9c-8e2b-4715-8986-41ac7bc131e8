<?php
/**
 * User: Bob
 * Date: 2017/4/25
 * Time: 14:23
 */

namespace CoreBundle\Entity;

use CoreBundle\Utils\Money;
use CoreBundle\Utils\Util;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\ORM\Mapping\Index;

/**
 * Class UserCardBalance
 * @ORM\Table(name="user_card_balance", indexes={
 *     @Index(name="type_idx", columns={"type"}),
 *     @Index(columns={"user_card_id", "created_at"}),
 * }, options={"comment":"User card balance history"})
 * @ORM\Entity
 * @package CoreBundle\Entity
 */
class UserCardBalance extends BaseEntity
{

    const TYPE_LOAD_CARD = 'load_card';
    const TYPE_UNLOAD_CARD = 'unload_card';
    const TYPE_MANUAL_REFRESH = 'manual_refresh';
    const TYPE_TRANSACTION = 'transaction';
    const TYPE_LOOKUP = 'lookup';
    const TYPE_DAILY_LOOKUP = 'daily_lookup';
    const TYPE_UNLOAD_FEE = 'unload_fee';
    const TYPE_MONTHLY_FEE = 'monthly_fee';
    const TYPE_MEMBER_LOAD = 'member_load';
    const TYPE_MEMBER_UNLOAD = 'member_unload';
    const TYPE_MEMBER_SHIP = 'member_ship_fee';
    const TYPE_PLATFORM_ADJUSTMENT = 'platform_adjustment';
    const TYPE_LOAD_FEE = 'load_fee';
    const TYPE_PAY_EMPLOYEE = 'pay_employee';
    const TYPE_PAY_EMPLOYEE_FAILED = 'pay_employee_failed_return';
    const TYPE_REVERSE_EMPLOYEE_FAILED = 'reverse_employee_failed_return';
    const TYPE_EMPLOYER_PAY = 'employer_pay';
    const TYPE_EMPLOYER_REVERSE = 'employer_reverse';
    const TYPE_REVERSE_PAYOUT = 'reverse_employee_payout';
    const TYPE_TIP = 'tip';
    /**
     * @deprecated
     */
    const TYPE_REPLACE_CARD_LOAD = 'replace_card_load';
    const TYPE_REPLACE_CARD_UNLOAD = 'replace_card_unload';
    const TYPE_REPLACE_CARD_FEE = 'replace_card_unload_fee';
    const TYPE_LOCAL_SETTLEMENT = 'local_settlement';
    const TYPE_IMPORT = 'import';

    const TYPE_REFUND = 'refund';
    const TYPE_MANUAL_LOAD = 'manual_load'; // will remove
    const TYPE_INSTANT_LOAD = 'instant_load';

    const SOURCE_CARD = 'card';
    const SOURCE_LOCAL_BALANCE = 'local_balance';

    public function parseUsuData()
    {
        $uc = $this->getUserCard();
        $result = [
            'tranId' => '',
            'merchant' => '',
            'amount' => Money::format($this->getCurrentBalance() - $this->getPreviousBalance(), 'USD', false),
        ];

        $meta = Util::meta($this, 'entity');
        if ($meta) {
            $es = new EntitySignature($meta);
            $entity = $es->toEntity();
            if ($entity instanceof UserCardTransaction || $entity instanceof UserCardDecline) {
                $uc = $entity->getUserCard();
                $result['tranId'] = $entity->getId();
                $result['merchant'] = $entity->getMerchantName();
            }
        } else {
            $comment = $this->getComment();
            $matches = [];
            preg_match('|[a-z0-9\-]{36}|', $comment, $matches);
            if (!empty($matches[0])) {
                $token = $matches[0];
                $type = $this->getType();
                $class = $type === 'Declined Transaction Fee' ? UserCardDecline::class : UserCardTransaction::class;
                $field = $class === UserCardTransaction::class ? 'tranId' : 'txnId';
                $rs = Util::em()->getRepository($class)->findBy([
                    $field => $token,
                ]);
                if ($rs) {
                    /** @var UserCardDecline|UserCardTransaction $entity */
                    $entity = $rs[0];
                    $uc = $entity->getUserCard();
                    $result['tranId'] = $entity->getId();
                    $result['merchant'] = $entity->getMerchantName();
                }
            }
        }

        $result['cardId'] = $uc->getId();
        $result['token'] = $uc->getToken();
        $result['last4'] = $uc->getLast4();
        $result['cardName'] = $uc->getNickName();

        $ct = $uc->getType();
        $result['cardType'] = $ct === 'DUMMY' ? '' : $ct;

        return $result;
    }

    /**
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\UserCard", inversedBy="balances")
     * @ORM\JoinColumn(name="user_card_id", referencedColumnName="id", onDelete="cascade")
     */
    private $userCard;

    /**
     * @var string $type
     * @ORM\Column(name="pan", type="string", length=255, nullable=true)
     */
    private $pan;

    /**
     * @var string $type
     * @ORM\Column(name="type", type="string", options={"comment":"load_card, unload_card, manual_refresh, etc."})
     */
    private $type;

    /**
     * @var string $source
     * @ORM\Column(name="source", type="string", length=255, options={"default"="card", "comment":"card or local_balance"})
     */
    private $source;

    /**
     * @var float $currentBalance
     * @ORM\Column(name="cur_balance", type="float")
     */
    private $currentBalance;

    /**
     * @var float $previousBalance
     * @ORM\Column(name="pre_balance", type="float")
     */
    private $previousBalance;

    /**
     * @var string $comment
     * @ORM\Column(name="comment", type="text", nullable=true)
     */
    private $comment;

    /**
     * @var string $meta
     * @ORM\Column(name="meta", type="text", nullable=true)
     */
    private $meta;

    /**
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\UserCardLoad")
     * @ORM\JoinColumn(name="user_card_load_id", referencedColumnName="id", onDelete="cascade")
     */
    private $userCardLoad;

    /**
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\UserCardTransaction")
     * @ORM\JoinColumn(name="user_card_transaction_id", referencedColumnName="id", onDelete="cascade")
     */
    private $userCardTransaction;

    /**
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\Transfer")
     * @ORM\JoinColumn(name="transfer_id", referencedColumnName="id", onDelete="cascade")
     */
    private $transfer;


    /**
     * Set type
     *
     * @param string $type
     *
     * @return UserCardBalance
     */
    public function setType($type)
    {
        $this->type = $type;

        return $this;
    }

    /**
     * Get type
     *
     * @return string
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * Set currentBalance
     *
     * @param float $currentBalance
     *
     * @return UserCardBalance
     */
    public function setCurrentBalance($currentBalance)
    {
        $this->currentBalance = $currentBalance;

        return $this;
    }

    /**
     * Get currentBalance
     *
     * @return float
     */
    public function getCurrentBalance()
    {
        return $this->currentBalance;
    }

    /**
     * Set previousBalance
     *
     * @param float $previousBalance
     *
     * @return UserCardBalance
     */
    public function setPreviousBalance($previousBalance)
    {
        $this->previousBalance = $previousBalance;

        return $this;
    }

    /**
     * Get previousBalance
     *
     * @return float
     */
    public function getPreviousBalance()
    {
        return $this->previousBalance;
    }

    /**
     * Set userCard
     *
     * @param \CoreBundle\Entity\UserCard $userCard
     *
     * @return UserCardBalance
     */
    public function setUserCard(\CoreBundle\Entity\UserCard $userCard = null)
    {
        $this->userCard = $userCard;

        return $this;
    }

    /**
     * Get userCard
     *
     * @return \CoreBundle\Entity\UserCard
     */
    public function getUserCard()
    {
        return $this->userCard;
    }



    /**
     * Set pan
     *
     * @param string $pan
     *
     * @return UserCardBalance
     */
    public function setPan($pan)
    {
        $this->pan = $pan;

        return $this;
    }

    /**
     * Get pan
     *
     * @return string
     */
    public function getPan()
    {
        return $this->pan;
    }

    /**
     * Set source
     *
     * @param string $source
     *
     * @return UserCardBalance
     */
    public function setSource($source)
    {
        $this->source = $source;

        return $this;
    }

    /**
     * Get source
     *
     * @return string
     */
    public function getSource()
    {
        return $this->source;
    }

    /**
     * Set comment
     *
     * @param string $comment
     *
     * @return UserCardBalance
     */
    public function setComment($comment)
    {
        $this->comment = $comment;

        return $this;
    }

    /**
     * Get comment
     *
     * @return string
     */
    public function getComment()
    {
        return $this->comment;
    }

    /**
     * Set meta
     *
     * @param string $meta
     *
     * @return UserCardBalance
     */
    public function setMeta($meta)
    {
        $this->meta = $meta;

        return $this;
    }

    /**
     * Get meta
     *
     * @return string
     */
    public function getMeta()
    {
        return $this->meta;
    }

    public function getUserCardLoad(): ?UserCardLoad
    {
        return $this->userCardLoad;
    }

    public function setUserCardLoad(?UserCardLoad $userCardLoad): static
    {
        $this->userCardLoad = $userCardLoad;

        return $this;
    }

    public function getUserCardTransaction(): ?UserCardTransaction
    {
        return $this->userCardTransaction;
    }

    public function setUserCardTransaction(?UserCardTransaction $userCardTransaction): static
    {
        $this->userCardTransaction = $userCardTransaction;

        return $this;
    }

    public function getTransfer(): ?Transfer
    {
        return $this->transfer;
    }

    public function setTransfer(?Transfer $transfer): static
    {
        $this->transfer = $transfer;

        return $this;
    }
}
