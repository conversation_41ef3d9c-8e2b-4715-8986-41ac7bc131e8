<?php

namespace CoreBundle\Entity;

use Carbon\Carbon;
use CoreBundle\Utils\Util;
use DateTime;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\ORM\Mapping\Index;
use FisBundle\Entity\AccountBalance;
use FisBundle\Entity\Authorization;
use FisBundle\Entity\Dispute;
use FisBundle\Entity\MonetaryNew;
use FisBundle\Entity\NonMonetary;
use JMS\Serializer\Annotation as Serializer;
use SalexUserBundle\Entity\User;

/**
 * UserVelocity
 *
 * @ORM\Table(name="user_velocity", indexes={
 *     @Index(name="velocity_id_idx", columns={"velocity_id"}),
 *     @Index(name="user_id_idx", columns={"user_id"}),
 *     @Index(name="work_of_date_idx", columns={"work_of_date"}),
 *     @Index(name="pan_idx", columns={"pan"}),
 *     @Index(name="status_idx", columns={"status"}),
 * }, options={"comment":"History of users that triggered any velocity"})
 * @ORM\Entity(repositoryClass="CoreBundle\Repository\UserVelocityRepository")
 */
class UserVelocity extends BaseEntity
{
    const STATUS_UNKNOWN = null;
    const STATUS_REVIEWED = 'reviewed';
    const STATUS_CLEARED = 'cleared';

    /**
     * @param Velocity $velocity
     * @param User $user
     * @return Carbon
     */
    public static function getLatestResetTime(Velocity $velocity, User $user)
    {
        $rs = Util::em()->getRepository(\CoreBundle\Entity\UserVelocity::class)
            ->createQueryBuilder('uv')
            ->where('uv.velocity = :velocity')
            ->andWhere('uv.user = :user')
            ->andWhere('uv.reset = :reset')
            ->andWhere('uv.resetAt is not null')
            ->setParameter('velocity', $velocity)
            ->setParameter('user', $user)
            ->setParameter('reset', true)
            ->orderBy('uv.resetAt', 'DESC')
            ->setMaxResults(1)
            ->getQuery()
            ->getResult();
        if (!$rs) {
            return Carbon::now()->subYearsWithoutOverflow(100);
        }
        /** @var UserVelocity $uv */
        $uv = $rs[0];
        return Carbon::instance($uv->getResetAt());
    }

    public function getFilteredDetails()
    {
        $trigger = (string)$this->getTrigger();
        $ds = $this->getDetails() ?: [];
        if ($ds) {
            $ds = array_values(array_filter($ds, function ($ds) use ($trigger) {
                return (string)$ds !== $trigger;
            }));
        }
        return $ds;
    }

    public function liftReview()
    {
        $this->setStatus(self::STATUS_REVIEWED);
        if ($this->getReset()) {
            $this->setResetAt(new \DateTime());
        }
        Util::em()->persist($this);
    }

    /**
     * @var User $user
     * @ORM\ManyToOne(targetEntity="SalexUserBundle\Entity\User")
     * @Serializer\Exclude()
     */
    private $user;

    /**
     * @var UserCard $userCard
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\UserCard")
     * @Serializer\Exclude()
     */
    private $userCard;

    /**
     * @var Velocity $velocity
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\Velocity")
     */
    private $velocity;

    /**
     * @var array|null $trigger
     * @ORM\Column(name="trigger_data", type="text", nullable=true, options={"comment":"Details of data that triggered the velocity"})
     */
    private $trigger;

    /**
     * @var array|null $details
     * @ORM\Column(name="details", type="text", nullable=true, options={"comment":"Related all data that triggered the velocity"})
     */
    private $details;

    /**
     * @var string $details
     * @ORM\Column(name="status", type="string", nullable=true, options={"comment":"null or reviewed"})
     */
    private $status;

    /**
     * @var boolean $reset
     * @ORM\Column(name="reset", type="boolean", nullable=true, options={"comment":"Reset statistics or not"})
     */
    private $reset;

    /**
     * @var \DateTime $resetAt
     * @ORM\Column(name="reset_at", type="datetime", nullable=true)
     */
    private $resetAt;

    /**
     * @var string
     * @ORM\Column(name="pan", type="string", length=30, nullable=true)
     */
    private $pan;

    /**
     * @ORM\ManyToOne(targetEntity="FisBundle\Entity\AccountBalance")
     * @Serializer\Exclude()
     */
    private $accountBalance;

    /**
     * @ORM\ManyToOne(targetEntity="FisBundle\Entity\Authorization")
     * @Serializer\Exclude()
     */
    private $authorization;

    /**
     * @ORM\ManyToOne(targetEntity="FisBundle\Entity\MonetaryNew")
     * @Serializer\Exclude()
     */
    private $monetary;

    /**
     * @ORM\ManyToOne(targetEntity="FisBundle\Entity\Dispute")
     * @Serializer\Exclude()
     */
    private $dispute;

    /**
     * @ORM\ManyToOne(targetEntity="FisBundle\Entity\NonMonetary")
     * @Serializer\Exclude()
     */
    private $nonMonetary;

    /**
     * @var DateTime
     *
     * @ORM\Column(name="work_of_date", type="date", nullable=true)
     */
    private $workOfDate;

    /**
     * @var integer
     *
     * @ORM\Column(name="count", type="integer", nullable=true)
     */
    private $count;

    /**
     * @var integer
     *
     * @ORM\Column(name="amount", type="integer", nullable=true)
     */
    private $amount;

    /**
     * @var string
     * @ORM\Column(name="meta", type="text", nullable=true)
     */
    private $meta;

    /**
     * Set trigger
     *
     * @param EntitySignature|null $trigger
     *
     * @return UserVelocity
     */
    public function setTrigger($trigger)
    {
        $this->trigger = $trigger ? (string)$trigger : null;

        return $this;
    }

    /**
     * Get trigger
     *
     * @return EntitySignature|null
     */
    public function getTrigger()
    {
        $t = $this->trigger;
        if ($t) {
            $sig = new EntitySignature($t);
            if ($sig->getClass()) {
                return $sig;
            }
        }
        return null;
    }

    /**
     * Set details
     *
     * @param array|EntitySignature[]|null $details
     *
     * @return UserVelocity
     */
    public function setDetails($details)
    {
        if ($details) {
            foreach ($details as $i => $detail) {
                if ($detail && !($detail instanceof EntitySignature)) {
                    $detail = new EntitySignature($detail);
                }
                $details[$i] = $detail ? (array)$detail : null;
            }
            $this->details = json_encode($details);
        } else {
            $this->details = $details;
        }

        return $this;
    }

    /**
     * Get details
     *
     * @return array|EntitySignature[]|null
     */
    public function getDetails()
    {
        $ds = json_decode($this->details, true);
        if (is_array($ds)) {
            foreach ($ds as $i => $d) {
                $ds[$i] = new EntitySignature($d);
            }
        }
        return $ds;
    }

    /**
     * Set user
     *
     * @param \SalexUserBundle\Entity\User $user
     *
     * @return UserVelocity
     */
    public function setUser(\SalexUserBundle\Entity\User $user = null)
    {
        $this->user = $user;

        return $this;
    }

    /**
     * Get user
     *
     * @return \SalexUserBundle\Entity\User
     */
    public function getUser()
    {
        return $this->user;
    }

    /**
     * Set velocity
     *
     * @param \CoreBundle\Entity\Velocity $velocity
     *
     * @return UserVelocity
     */
    public function setVelocity(\CoreBundle\Entity\Velocity $velocity = null)
    {
        $this->velocity = $velocity;

        return $this;
    }

    /**
     * Get velocity
     *
     * @return \CoreBundle\Entity\Velocity
     */
    public function getVelocity()
    {
        return $this->velocity;
    }

    /**
     * Set userCard
     *
     * @param \CoreBundle\Entity\UserCard $userCard
     *
     * @return UserVelocity
     */
    public function setUserCard(\CoreBundle\Entity\UserCard $userCard = null)
    {
        $this->userCard = $userCard;

        return $this;
    }

    /**
     * Get userCard
     *
     * @return \CoreBundle\Entity\UserCard
     */
    public function getUserCard()
    {
        return $this->userCard;
    }

    /**
     * Set status
     *
     * @param string $status
     *
     * @return UserVelocity
     */
    public function setStatus($status)
    {
        $this->status = $status;

        return $this;
    }

    /**
     * Get status
     *
     * @return string
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * Set reset
     *
     * @param boolean $reset
     *
     * @return UserVelocity
     */
    public function setReset($reset)
    {
        $this->reset = $reset;

        return $this;
    }

    /**
     * Get reset
     *
     * @return boolean
     */
    public function getReset()
    {
        return $this->reset;
    }

    /**
     * Set resetAt
     *
     * @param \DateTime $resetAt
     *
     * @return UserVelocity
     */
    public function setResetAt($resetAt)
    {
        $this->resetAt = $resetAt;

        return $this;
    }

    /**
     * Get resetAt
     *
     * @return \DateTime
     */
    public function getResetAt()
    {
        return $this->resetAt;
    }

    /**
     * Set pan
     *
     * @param string $pan
     *
     * @return UserVelocity
     */
    public function setPan($pan)
    {
        $this->pan = $pan;

        return $this;
    }

    /**
     * Get pan
     *
     * @return string
     */
    public function getPan()
    {
        return $this->pan;
    }

    /**
     * Set meta
     *
     * @param string $meta
     *
     * @return UserVelocity
     */
    public function setMeta($meta)
    {
        $this->meta = $meta;

        return $this;
    }

    /**
     * Get meta
     *
     * @return string
     */
    public function getMeta()
    {
        return $this->meta;
    }

    /**
     * Set accountBalance
     *
     * @param AccountBalance $accountBalance
     *
     * @return UserVelocity
     */
    public function setAccountBalance(AccountBalance $accountBalance = null)
    {
        $this->accountBalance = $accountBalance;

        return $this;
    }

    /**
     * Get accountBalance
     *
     * @return AccountBalance
     */
    public function getAccountBalance()
    {
        return $this->accountBalance;
    }

    /**
     * Set authorization
     *
     * @param Authorization $authorization
     *
     * @return UserVelocity
     */
    public function setAuthorization(Authorization $authorization = null)
    {
        $this->authorization = $authorization;

        return $this;
    }

    /**
     * Get authorization
     *
     * @return Authorization
     */
    public function getAuthorization()
    {
        return $this->authorization;
    }

    /**
     * Set monetary
     *
     * @param MonetaryNew $monetary
     *
     * @return UserVelocity
     */
    public function setMonetary(MonetaryNew $monetary = null)
    {
        $this->monetary = $monetary;

        return $this;
    }

    /**
     * Get monetary
     *
     * @return Monetary
     */
    public function getMonetary()
    {
        return $this->monetary;
    }

    /**
     * Set workOfDate
     *
     * @param \DateTime $workOfDate
     *
     * @return UserVelocity
     */
    public function setWorkOfDate($workOfDate)
    {
        $this->workOfDate = $workOfDate;

        return $this;
    }

    /**
     * Get workOfDate
     *
     * @return \DateTime
     */
    public function getWorkOfDate()
    {
        return $this->workOfDate;
    }

    /**
     * Set count
     *
     * @param integer $count
     *
     * @return UserVelocity
     */
    public function setCount($count)
    {
        $this->count = $count;

        return $this;
    }

    /**
     * Get count
     *
     * @return integer
     */
    public function getCount()
    {
        return $this->count;
    }

    /**
     * Set amount
     *
     * @param integer $amount
     *
     * @return UserVelocity
     */
    public function setAmount($amount)
    {
        $this->amount = $amount;

        return $this;
    }

    /**
     * Get amount
     *
     * @return integer
     */
    public function getAmount()
    {
        return $this->amount;
    }

    /**
     * Set dispute
     *
     * @param Dispute $dispute
     *
     * @return UserVelocity
     */
    public function setDispute(Dispute $dispute = null)
    {
        $this->dispute = $dispute;

        return $this;
    }

    /**
     * Get dispute
     *
     * @return Dispute
     */
    public function getDispute()
    {
        return $this->dispute;
    }

    /**
     * Set nonMonetary
     *
     * @param NonMonetary $nonMonetary
     *
     * @return UserVelocity
     */
    public function setNonMonetary(NonMonetary $nonMonetary = null)
    {
        $this->nonMonetary = $nonMonetary;

        return $this;
    }

    /**
     * Get nonMonetary
     *
     * @return NonMonetary
     */
    public function getNonMonetary()
    {
        return $this->nonMonetary;
    }
}
