<?php

namespace CoreBundle\Entity;

use ApiBundle\Entity\ApiEntityInterface;
use CoreBundle\Entity\Traits\EntitySignatureTrait;
use CoreBundle\Entity\Traits\NamedEntityTrait;
use CoreBundle\Utils\Util;
use Doctrine\Common\Collections\Collection;
use Doctrine\Common\Util\ClassUtils;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\ORM\Mapping\Index;
use J<PERSON>\Serializer\Annotation as Serializer;
use SalexUserBundle\Entity\User;

/**
 * Tenant
 *
 * @ORM\Table(name="tenant", indexes={
 *     @Index(name="name_index", columns={"name"}),
 * }, options={"comment":"Base info of all tenant types: brand_partner, issuing_bank, kyc_provider, load_partner, marketing_partner, processor, program_manager, reshipper, platform and service_provider."})
 * @ORM\Entity(repositoryClass="CoreBundle\Repository\TenantRepository")
 * @ORM\InheritanceType("JOINED")
 * @ORM\DiscriminatorColumn(name="discriminator", type="string")
 */
abstract class Tenant implements ApiEntityInterface
{
    use EntitySignatureTrait;
    use NamedEntityTrait;

    const TYPE_BRAND_PARTNER = 'brand_partner';
    const TYPE_ISSUING_BANK = 'issuing_bank';
    const TYPE_KYC_PROVIDER = 'kyc_provider';
    const TYPE_LOAD_PARTNER = 'load_partner';
    const TYPE_MARKETING_PARTNER = 'marketing_partner';
    const TYPE_PROCESSOR = 'processor';
    const TYPE_PROGRAM_MANAGER = 'program_manager';
    const TYPE_RESHIPPER = 'reshipper';
    const TYPE_PLATFORM = 'platform';
    const TYPE_SERVICE_PROVIDER = 'service_provider';

    const ClASS_TYPE_MAP = [
        self::TYPE_BRAND_PARTNER     => BrandPartner::class,
        self::TYPE_LOAD_PARTNER      => LoadPartner::class,
        self::TYPE_KYC_PROVIDER      => KycProvider::class,
        self::TYPE_MARKETING_PARTNER => MarketingPartner::class,
        self::TYPE_RESHIPPER         => Reshipper::class,
        self::TYPE_ISSUING_BANK      => IssuingBank::class,
        self::TYPE_PROCESSOR         => Processor::class,
        self::TYPE_PROGRAM_MANAGER   => ProgramManager::class,
        self::TYPE_PLATFORM          => Platform::class,
        self::TYPE_SERVICE_PROVIDER  => ServiceManager::class,
    ];

    const MODULE_MAP = [
        self::TYPE_PROCESSOR         => Module::ID_PROCESSOR_MANAGEMENT,
        self::TYPE_PROGRAM_MANAGER   => Module::ID_PROGRAM_MANAGEMENT,
        self::TYPE_LOAD_PARTNER      => Module::ID_LOAD_PARTNER_MANAGEMENT,
        self::TYPE_KYC_PROVIDER      => Module::ID_KYC_PROVIDER_MANAGEMENT,
        self::TYPE_ISSUING_BANK      => Module::ID_ISSUING_BANK_MANAGEMENT,
        self::TYPE_BRAND_PARTNER     => Module::ID_BRAND_PARTNER_MANAGEMENT,
        self::TYPE_MARKETING_PARTNER => Module::ID_MARKETING_PARTNER_MANAGEMENT,
        self::TYPE_RESHIPPER         => Module::ID_RESHIPPER_MANAGEMENT,
        self::TYPE_SERVICE_PROVIDER  => Module::ID_SERVICE_MANAGEMENT,
        self::TYPE_PLATFORM          => Module::ID_PLATFORM_MANAGEMENT,
    ];

    public static function getClassOfType($type)
    {
        return self::ClASS_TYPE_MAP[$type] ?? null;
    }

    public static function findAll() {
        $result = [];
        foreach (self::ClASS_TYPE_MAP as $type => $class) {
            $result[$type] = Util::em()->getRepository($class)->findBy([], ['name' => 'asc']);
        }
        return $result;
    }

    public static function find($name)
    {
        return Util::em()->getRepository(static::class)->findOneBy(compact('name'));
    }

    public static function findById($id)
    {
        return Util::em()->getRepository(static::class)->find($id);
    }

    public function toApiArray(bool $extra = false) : array
    {
        $r = [
            'id' => $this->getId(),
            'name' => $this->getName(),
            'address' => $this->getAddress(),
        ];
        if ($extra) {
            $r = array_merge($r, [
                'type' => $this->getType(),
            ]);
        }
        return $r;
    }

    protected function getStringifyFields()
    {
        return [
            'name' => $this->getName(),
        ];
    }

    public function getType()
    {
        $map = array_flip(self::ClASS_TYPE_MAP);
        $class = ClassUtils::getRealClass(static::class);
        return $map[$class] ?? '';
    }

    /**
     * @return Affiliate|null
     */
    public function getAffiliate()
    {
        $as = $this->getAffiliates();
        if (!$as || $as->isEmpty()) {
            return null;
        }
        return $as->first();
    }

    public function getContact()
    {
        $tc = $this->getTenantContact();
        $user = $tc ? $tc->getUser() : new User();
        $user->setSource($user->getSource() ?? 'tenant_contact');
        return $user;
    }

    /**
     * @return TenantUser
     */
    public function getTenantContact()
    {
        $all = $this->getContacts();
        $mains = $all->filter(function (TenantUser $tenantUser) {
            return $tenantUser->getMain();
        });
        if (!$mains->isEmpty()) {
            return $mains->first();
        }
        return $all->first();
    }

    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @var string
     *
     * @ORM\Column(name="name", type="string", length=255)
     */
    private $name;

    /**
     * @var string
     *
     * @ORM\Column(name="address", type="text", nullable=true)
     */
    private $address;

    /**
     * @var Collection
     * @ORM\OneToMany(targetEntity="CoreBundle\Entity\TenantUser", mappedBy="tenant")
     * @Serializer\Exclude()
     */
    private $contacts;

    /**
     * @var Collection
     * @ORM\OneToMany(targetEntity="CoreBundle\Entity\Affiliate", mappedBy="tenant")
     * @Serializer\Exclude()
     */
    private $affiliates;

    /**
     * @var Collection
     * @ORM\OneToMany(targetEntity="CoreBundle\Entity\TenantFeeOption", mappedBy="tenant")
     * @Serializer\Exclude()
     */
    private $feeOptions;

    /**
     * @var string
     *
     * @ORM\Column(name="meta", type="text", nullable=true)
     */
    private $meta;

    /**
     * Get id
     *
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->contacts = new \Doctrine\Common\Collections\ArrayCollection();
    }

    /**
     * Add contact
     *
     * @param \CoreBundle\Entity\TenantUser $contact
     *
     * @return Tenant
     */
    public function addContact(\CoreBundle\Entity\TenantUser $contact)
    {
        $this->contacts[] = $contact;

        return $this;
    }

    /**
     * Remove contact
     *
     * @param \CoreBundle\Entity\TenantUser $contact
     */
    public function removeContact(\CoreBundle\Entity\TenantUser $contact)
    {
        $this->contacts->removeElement($contact);
    }

    /**
     * Get contacts
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getContacts()
    {
        return $this->contacts;
    }

    /**
     * Set name
     *
     * @param string $name
     *
     * @return Tenant
     */
    public function setName($name)
    {
        $this->name = $name;

        return $this;
    }

    /**
     * Get name
     *
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * Set address
     *
     * @param string $address
     *
     * @return Tenant
     */
    public function setAddress($address)
    {
        $this->address = $address;

        return $this;
    }

    /**
     * Get address
     *
     * @return string
     */
    public function getAddress()
    {
        return $this->address;
    }

    /**
     * Add affiliate
     *
     * @param \CoreBundle\Entity\Affiliate $affiliate
     *
     * @return Tenant
     */
    public function addAffiliate(\CoreBundle\Entity\Affiliate $affiliate)
    {
        $this->affiliates[] = $affiliate;

        return $this;
    }

    /**
     * Remove affiliate
     *
     * @param \CoreBundle\Entity\Affiliate $affiliate
     */
    public function removeAffiliate(\CoreBundle\Entity\Affiliate $affiliate)
    {
        $this->affiliates->removeElement($affiliate);
    }

    /**
     * Get affiliates
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getAffiliates()
    {
        return $this->affiliates;
    }

    /**
     * Add feeOption
     *
     * @param \CoreBundle\Entity\TenantFeeOption $feeOption
     *
     * @return Tenant
     */
    public function addFeeOption(\CoreBundle\Entity\TenantFeeOption $feeOption)
    {
        $this->feeOptions[] = $feeOption;

        return $this;
    }

    /**
     * Remove feeOption
     *
     * @param \CoreBundle\Entity\TenantFeeOption $feeOption
     */
    public function removeFeeOption(\CoreBundle\Entity\TenantFeeOption $feeOption)
    {
        $this->feeOptions->removeElement($feeOption);
    }

    /**
     * Get feeOptions
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getFeeOptions()
    {
        return $this->feeOptions;
    }

    /**
     * Set meta
     *
     * @param string $meta
     *
     * @return Tenant
     */
    public function setMeta($meta)
    {
        $this->meta = $meta;

        return $this;
    }

    /**
     * Get meta
     *
     * @return string
     */
    public function getMeta()
    {
        return $this->meta;
    }
}
