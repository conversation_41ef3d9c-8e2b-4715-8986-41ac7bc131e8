<?php

namespace CoreBundle\Entity;

use CoreBundle\Utils\Util;
use DateTime;
use Doctrine\ORM\Mapping as ORM;

/**
 * UserCardPurse
 *
 * @ORM\Table(name="user_card_purse")
 * @ORM\Entity(repositoryClass="CoreBundle\Repository\UserCardPurseRepository")
 */
class UserCardPurse
{
    public function toApiArrayForSkux()
    {
        return [
            'CardStatus' => $this->getCardStatus(),
            'PurseEffectiveDate' => Util::formatApiDateTime($this->getEffectiveDate()),
            'PurseExpirationDate' => Util::formatApiDateTime($this->getExpirationDate()),
            'PurseNumber' => $this->getNumber(),
            'PurseType' => $this->getType(),
            'PurseStatus' => $this->getStatus(),
            'PurseBalance' => $this->getBalance(),
        ];
    }

    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\UserCard", inversedBy="purses")
     * @ORM\JoinColumn(name="user_card_id", referencedColumnName="id", onDelete="cascade")
     */
    private $userCard;

    /**
     * @var string
     *
     * @ORM\Column(name="card_status", type="string", length=255, nullable=true)
     */
    private $cardStatus;

    /**
     * @var DateTime
     *
     * @ORM\Column(name="effective_date", type="datetime", nullable=true)
     */
    private $effectiveDate;

    /**
     * @var DateTime
     *
     * @ORM\Column(name="expiration_date", type="datetime", nullable=true)
     */
    private $expirationDate;

    /**
     * @var string
     *
     * @ORM\Column(name="number", type="string", length=255)
     */
    private $number;

    /**
     * @var string
     *
     * @ORM\Column(name="type", type="string", length=255, nullable=true)
     */
    private $type;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=255, nullable=true)
     */
    private $status;

    /**
     * @var int
     *
     * @ORM\Column(name="balance", type="integer", nullable=true)
     */
    private $balance;

    /**
     * @var string $meta
     * @ORM\Column(name="meta", type="text", nullable=true)
     */
    private $meta;


    /**
     * Get id
     *
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set cardStatus
     *
     * @param string $cardStatus
     *
     * @return UserCardPurse
     */
    public function setCardStatus($cardStatus)
    {
        $this->cardStatus = $cardStatus;

        return $this;
    }

    /**
     * Get cardStatus
     *
     * @return string
     */
    public function getCardStatus()
    {
        return $this->cardStatus;
    }

    /**
     * Set effectiveDate
     *
     * @param DateTime $effectiveDate
     *
     * @return UserCardPurse
     */
    public function setEffectiveDate($effectiveDate)
    {
        $this->effectiveDate = $effectiveDate;

        return $this;
    }

    /**
     * Get effectiveDate
     *
     * @return DateTime
     */
    public function getEffectiveDate()
    {
        return $this->effectiveDate;
    }

    /**
     * Set expirationDate
     *
     * @param DateTime $expirationDate
     *
     * @return UserCardPurse
     */
    public function setExpirationDate($expirationDate)
    {
        $this->expirationDate = $expirationDate;

        return $this;
    }

    /**
     * Get expirationDate
     *
     * @return DateTime
     */
    public function getExpirationDate()
    {
        return $this->expirationDate;
    }

    /**
     * Set number
     *
     * @param string $number
     *
     * @return UserCardPurse
     */
    public function setNumber($number)
    {
        $this->number = $number;

        return $this;
    }

    /**
     * Get number
     *
     * @return string
     */
    public function getNumber()
    {
        return $this->number;
    }

    /**
     * Set type
     *
     * @param string $type
     *
     * @return UserCardPurse
     */
    public function setType($type)
    {
        $this->type = $type;

        return $this;
    }

    /**
     * Get type
     *
     * @return string
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * Set status
     *
     * @param string $status
     *
     * @return UserCardPurse
     */
    public function setStatus($status)
    {
        $this->status = $status;

        return $this;
    }

    /**
     * Get status
     *
     * @return string
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * Set balance
     *
     * @param integer $balance
     *
     * @return UserCardPurse
     */
    public function setBalance($balance)
    {
        $this->balance = $balance;

        return $this;
    }

    /**
     * Get balance
     *
     * @return int
     */
    public function getBalance()
    {
        return $this->balance;
    }

    /**
     * Set meta
     *
     * @param string $meta
     *
     * @return UserCardPurse
     */
    public function setMeta($meta)
    {
        $this->meta = $meta;

        return $this;
    }

    /**
     * Get meta
     *
     * @return string
     */
    public function getMeta()
    {
        return $this->meta;
    }

    /**
     * Set userCard
     *
     * @param \CoreBundle\Entity\UserCard $userCard
     *
     * @return UserCardPurse
     */
    public function setUserCard(\CoreBundle\Entity\UserCard $userCard = null)
    {
        $this->userCard = $userCard;

        return $this;
    }

    /**
     * Get userCard
     *
     * @return \CoreBundle\Entity\UserCard
     */
    public function getUserCard()
    {
        return $this->userCard;
    }
}
