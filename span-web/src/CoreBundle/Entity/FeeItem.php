<?php
/**
 * User: Bob
 * Date: 2017/2/24
 * Time: 13:14
 * This is used to record the Fee Item information.
 */

namespace CoreBundle\Entity;

use CoreBundle\Type\Measure;
use CoreBundle\Utils\Traits\ConstantTrait;
use CoreBundle\Utils\Util;
use CoreBundle\Utils\Money;
use Doctrine\ORM\Mapping as ORM;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;
use Symfony\Component\Validator\Constraints as Assert;

/**
 * Class FeeItem
 * @ORM\Entity(repositoryClass="CoreBundle\Repository\FeeItemRepository")
 * @ORM\Table(name="fee_item")
 * @package CoreBundle\Entity
 */
class FeeItem extends BaseEntity
{
    use ConstantTrait;

    const TRAN_CODE_N_A = 'N/A';

    const WAY_REPORTED_BY_FTP = 'reported_by_ftp';
    const WAY_REPORTED_BY_API = 'reported_by_api';
    const WAY_REPORTED_OFFLINE_BY_TENANT = 'reported_offline_by_tenant';
    const WAY_PROGRAM_CHARGE_AND_CONFIGURATION = 'program_charge_and_configuration';

    const ENTITY_MAP = [
        'Program'      => ProgramManager::class,
        'Platform'     => Platform::class,
        'KycProvider'  => KycProvider::class,
        'Service'      => ServiceManager::class,
        'LoadPartner'  => LoadPartner::class,
        'Processor'    => Processor::class,
        'IssuingBank'  => IssuingBank::class,
        'BrandPartner' => BrandPartner::class,
    ];

    const ENTITY_TYPES = [
        'Processor'    => 'Processor',
        'Program'      => 'Program Manager',
        'LoadPartner'  => 'Load Partner',
        'KycProvider'  => 'KYC Provider',
        'IssuingBank'  => 'Issuing Bank',
        'BrandPartner' => 'Brand Partner',
        'Service'      => 'Service Provider',
        'Platform'     => 'Platform',
    ];

    const TENANT_TYPE_MAP = [
        'Processor'    => Tenant::TYPE_PROCESSOR,
        'Program'      => Tenant::TYPE_PROGRAM_MANAGER,
        'LoadPartner'  => Tenant::TYPE_LOAD_PARTNER,
        'KycProvider'  => Tenant::TYPE_KYC_PROVIDER,
        'IssuingBank'  => Tenant::TYPE_ISSUING_BANK,
        'BrandPartner' => Tenant::TYPE_BRAND_PARTNER,
        'Service'      => Tenant::TYPE_SERVICE_PROVIDER,
        'Platform'     => Tenant::TYPE_PLATFORM,
    ];

    const TENANT_TYPE_MAP_REVERSE = [
        Tenant::TYPE_PROCESSOR        => 'Processor',
        Tenant::TYPE_PROGRAM_MANAGER  => 'Program',
        Tenant::TYPE_LOAD_PARTNER     => 'LoadPartner',
        Tenant::TYPE_KYC_PROVIDER     => 'KycProvider',
        Tenant::TYPE_ISSUING_BANK     => 'IssuingBank',
        Tenant::TYPE_BRAND_PARTNER    => 'BrandPartner',
        Tenant::TYPE_SERVICE_PROVIDER => 'Service',
        Tenant::TYPE_PLATFORM         => 'Platform',
    ];

    public function setWay($way)
    {
        return $this->setAppliedBy($way);
    }

    public function getWay()
    {
        return $this->getAppliedBy();
    }

    /**
     * @return null|Tenant|object
     */
    public function getEntity()
    {
        $type = $this->getFeeEntityType();
        $id = $this->getFeeEntityId();
        if (!$type || !$id) {
            return null;
        }
        $classes = self::ENTITY_MAP;
        if (!isset($classes[$type])) {
            return null;
        }
        return Util::em()->getRepository($classes[$type])
            ->find($id);
    }

    public function getEntityType()
    {
        return $this->getFeeEntityType();
    }

    public function getTenant()
    {
        return $this->getEntity();
    }

    public function getTenantType()
    {
        return $this->getEntityType();
    }

    public function getName()
    {
        return $this->getFeeName();
    }

    public function getFullName()
    {
        return $this->getId() . ' - ' . $this->getTenantType()
            . ' ' . $this->getTenant()->getName() . ' ' . $this->getName();
    }

    /**
     * @var string $feeName
     *
     * @ORM\Column(name="fee_name", type="string")
     * @Assert\NotBlank()
     */
    private $feeName;

    //Begin Add by Bob Wen Bao on 2017-03-07 use transaction code to track the specific fee items according to the report
    /**
     * @var string $tranCode
     *
     * @ORM\Column(name="tran_code", type="string", options={"default"="N/A","comment":"Transaction code according to transaction type"})
     */
    private $tranCode;
    //End

    /**
     * If tranCode is 'N/A', use transactionType.
     * @var string $transactionType
     *
     * @ORM\Column(name="transaction_type", type="string", nullable=true)
     */
    private $transactionType;

    /**
     * @var string $transactionStatus
     *
     * @ORM\Column(name="transaction_status", type="string", nullable=true)
     */
    private $transactionStatus;

    /**
     * @var string $function
     *
     * @ORM\Column(name="function", type="string", nullable=true, options={"comment":"API related to this fee item. Charge when this API is called and transaction status is same"})
     */
    private $function;

    /**
     * @var string $feeEntityType
     *
     * @ORM\Column(name="fee_entity_type", type="string", nullable = true)
     */
    private $feeEntityType;

    /**
     * @var integer $feeEntityId
     *
     * @ORM\Column(name="fee_entity_id", type="integer", nullable = true)
     */
    private $feeEntityId;

    /**
     * @var string $appliedBy
     *
     * @ORM\Column(name="applied_by", type="string", nullable=true)
     */
    private $appliedBy;

    //Modify by Bob Wen Bao on 2017-03-29 to update fee item
    //remove max fee fixed, max fee ratio, cost fee fixed and cost fee ratio.
    //add cost to tern ratio and cost to tern fixed
    //add currency with each fixed column
    //separate measure string to 3 parts : number + (currency / times) + per (days/weeks/months/years)
    /**
     * @var Measure $measure
     * @ORM\Column(name="measure", type="object", nullable=true)
     * @deprecated Measure field is moved to CardProgramFeeItem
     */
    private $measure;

    /**
     * @var integer $costTernFixed
     * @ORM\Column(name="cost_tern_fixed", type="float", nullable=true)
     */
    private $costTernFixed;

    /**
     * @var string $costTernCurrency
     * @ORM\Column(name="cost_tern_fixed_cur", type="string", nullable=true)
     */
    private $costTernFixedCurrency;

    /**
     * @var integer $costTernRatio
     * @ORM\Column(name="cost_tern_ratio", type="decimal", precision=4, scale=2, nullable=true)
     */
    private $costTernRatio;
    //End

    /**
     * @ORM\ManyToOne(targetEntity="FeeGlobalName", inversedBy="items")
     * @Serializer\Exclude()
     */
    private $feeGlobalName;

    //Begin Add by Bob Wen Bao on 2017-03-08 to record the relationships between Card Program and Fee Item
    /**
     * @ORM\OneToMany(targetEntity="CardProgramFeeItem" , mappedBy="feeItem" , cascade={"all"} , orphanRemoval=true)
     */
    private $cardProgramFeeItem;

    /**
     * @ORM\OneToMany(targetEntity="CardProgramFeeRevenueShare" , mappedBy="feeItem" , cascade={"all"} , orphanRemoval=true)
     */
    private $cardProgramFeeRevenueShare;
    //End

    /**
     * Set feeName
     *
     * @param string $feeName
     *
     * @return FeeItem
     */
    public function setFeeName($feeName)
    {
        $this->feeName = $feeName;

        return $this;
    }

    /**
     * Get feeName
     *
     * @return string
     */
    public function getFeeName()
    {
        return $this->feeName;
    }

    /**
     * Set feeEntityType
     *
     * @param string $feeEntityType
     *
     * @return FeeItem
     */
    public function setFeeEntityType($feeEntityType)
    {
        $this->feeEntityType = $feeEntityType;

        return $this;
    }

    /**
     * Get feeEntityType
     *
     * @return string
     */
    public function getFeeEntityType()
    {
        return $this->feeEntityType;
    }

    /**
     * Set appliedBy
     *
     * @param string $appliedBy
     *
     * @return FeeItem
     */
    public function setAppliedBy($appliedBy)
    {
        $this->appliedBy = $appliedBy;

        return $this;
    }

    /**
     * Get appliedBy
     *
     * @return string
     */
    public function getAppliedBy()
    {
        return $this->appliedBy;
    }


    /**
     * Set feeGlobalName
     *
     * @param \CoreBundle\Entity\FeeGlobalName $feeGlobalName
     *
     * @return FeeItem
     */
    public function setFeeGlobalName(\CoreBundle\Entity\FeeGlobalName $feeGlobalName = null)
    {
        $this->feeGlobalName = $feeGlobalName;

        return $this;
    }

    /**
     * Get feeGlobalName
     *
     * @return \CoreBundle\Entity\FeeGlobalName
     */
    public function getFeeGlobalName()
    {
        return $this->feeGlobalName;
    }

    /**
     * Set feeEntityId
     *
     * @param integer $feeEntityId
     *
     * @return FeeItem
     */
    public function setFeeEntityId($feeEntityId)
    {
        $this->feeEntityId = $feeEntityId;

        return $this;
    }

    /**
     * Get feeEntityId
     *
     * @return integer
     */
    public function getFeeEntityId()
    {
        return $this->feeEntityId;
    }

    /**
     * Set tranCode
     *
     * @param string $tranCode
     *
     * @return FeeItem
     */
    public function setTranCode($tranCode)
    {
        $this->tranCode = $tranCode;

        return $this;
    }

    /**
     * Get tranCode
     *
     * @return string
     */
    public function getTranCode()
    {
        return $this->tranCode ?: self::TRAN_CODE_N_A;
    }
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->cardProgramFeeItem = new \Doctrine\Common\Collections\ArrayCollection();
        $this->measure = new Measure();
    }

    /**
     * Add cardProgramFeeItem
     *
     * @param \CoreBundle\Entity\CardProgramFeeItem $cardProgramFeeItem
     *
     * @return FeeItem
     */
    public function addCardProgramFeeItem(\CoreBundle\Entity\CardProgramFeeItem $cardProgramFeeItem)
    {
        $this->cardProgramFeeItem[] = $cardProgramFeeItem;

        return $this;
    }

    /**
     * Remove cardProgramFeeItem
     *
     * @param \CoreBundle\Entity\CardProgramFeeItem $cardProgramFeeItem
     */
    public function removeCardProgramFeeItem(\CoreBundle\Entity\CardProgramFeeItem $cardProgramFeeItem)
    {
        $this->cardProgramFeeItem->removeElement($cardProgramFeeItem);
    }

    /**
     * Get cardProgramFeeItem
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getCardProgramFeeItem()
    {
        return $this->cardProgramFeeItem;
    }


    /**
     * Get specific cardProgramFeeItem
     *
     * @param integer $cardProgramId
     *
     * @return \CoreBundle\Entity\CardProgramFeeItem
     */
    public function getCardProgramFeeItemByCardProgramId($cardProgramId)
    {
        $cardProgram = Util::em()->getRepository(\CoreBundle\Entity\CardProgram::class)->find($cardProgramId);
        $cardProgramFeeItem = Util::em()
            ->getRepository(\CoreBundle\Entity\CardProgramFeeItem::class)
            ->findOneBy(array('feeItem' => $this, 'cardProgram' => $cardProgram));
        if($cardProgramFeeItem){
            return $cardProgramFeeItem;
        }else{
            return new CardProgramFeeItem();
        }
    }

    /**
     * Add cardProgramFeeRevenueShare
     *
     * @param \CoreBundle\Entity\CardProgramFeeRevenueShare $cardProgramFeeRevenueShare
     *
     * @return FeeItem
     */
    public function addCardProgramFeeRevenueShare(\CoreBundle\Entity\CardProgramFeeRevenueShare $cardProgramFeeRevenueShare)
    {
        $this->cardProgramFeeRevenueShare[] = $cardProgramFeeRevenueShare;

        return $this;
    }

    /**
     * Remove cardProgramFeeRevenueShare
     *
     * @param \CoreBundle\Entity\CardProgramFeeRevenueShare $cardProgramFeeRevenueShare
     */
    public function removeCardProgramFeeRevenueShare(\CoreBundle\Entity\CardProgramFeeRevenueShare $cardProgramFeeRevenueShare)
    {
        $this->cardProgramFeeRevenueShare->removeElement($cardProgramFeeRevenueShare);
    }

    /**
     * Get cardProgramFeeRevenueShare
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getCardProgramFeeRevenueShare()
    {
        return $this->cardProgramFeeRevenueShare;
    }

    /**
     * Set measure
     *
     * @param \stdClass $measure
     * @deprecated Measure field is moved to CardProgramFeeItem
     * @return FeeItem
     */
    public function setMeasure($measure)
    {
        $this->measure = $measure;

        return $this;
    }

    /**
     * Get measure
     * @deprecated Measure field is moved to CardProgramFeeItem
     * @return Measure
     */
    public function getMeasure()
    {
        return $this->measure;
    }

    //Begin Add by Bob Wen Bao on 2017-03-29 to separate measure string to 3 parts, using object instead
    public function getMeasureNumber()
    {
        return $this->measure->getNumber();
    }

    public function setMeasureNumber($measureNumber)
    {
        $this->measure->setNumber($measureNumber);
        return $this;
    }
    public function getMeasureMeasureStr()
    {
        return $this->measure->getMeasureStr();
    }

    public function setMeasureMeasureStr($measureMeasureStr)
    {
        $this->measure->setMeasureStr($measureMeasureStr);
        return $this;
    }
    public function getMeasureUnit()
    {
        return $this->measure->getUnit();
    }

    public function setMeasureUnit($measureUnit)
    {
        $this->measure->setUnit($measureUnit);
        return $this;
    }
    //End

    /**
     * Set costTernFixed
     *
     * @param integer $costTernFixed
     *
     * @return FeeItem
     */
    public function setCostTernFixed($costTernFixed)
    {
        $this->costTernFixed = $costTernFixed;

        return $this;
    }

    /**
     * Get costTernFixed
     *
     * @return integer
     */
    public function getCostTernFixed()
    {
        return $this->costTernFixed;
    }


    /**
     * Set costTernRatio
     *
     * @param string $costTernRatio
     *
     * @return FeeItem
     */
    public function setCostTernRatio($costTernRatio)
    {
        $this->costTernRatio =(double)$costTernRatio;

        return $this;
    }

    /**
     * Get costTernRatio
     *
     * @return string
     */
    public function getCostTernRatio()
    {
        return $this->costTernRatio;
    }

    /**
     * Set costTernFixedCurrency
     *
     * @param string $costTernFixedCurrency
     *
     * @return FeeItem
     */
    public function setCostTernFixedCurrency($costTernFixedCurrency)
    {
        $this->costTernFixedCurrency = $costTernFixedCurrency;

        return $this;
    }

    /**
     * Get costTernFixedCurrency
     *
     * @return string
     */
    public function getCostTernFixedCurrency()
    {
        return $this->costTernFixedCurrency;
    }

    /**
     * Set transactionType
     *
     * @param string $transactionType
     *
     * @return FeeItem
     */
    public function setTransactionType($transactionType)
    {
        $this->transactionType = $transactionType;

        return $this;
    }

    /**
     * Get transactionType
     *
     * @return string
     */
    public function getTransactionType()
    {
        return $this->transactionType;
    }

    /**
     * Set transactionStatus
     *
     * @param string $transactionStatus
     *
     * @return FeeItem
     */
    public function setTransactionStatus($transactionStatus)
    {
        $this->transactionStatus = $transactionStatus;

        return $this;
    }

    /**
     * Get transactionStatus
     *
     * @return string
     */
    public function getTransactionStatus()
    {
        return $this->transactionStatus;
    }

    /**
     * Set function
     *
     * @param string $function
     *
     * @return FeeItem
     */
    public function setFunction($function)
    {
        $this->function = $function;

        return $this;
    }

    /**
     * Get function
     *
     * @return string
     */
    public function getFunction()
    {
        return $this->function;
    }
}
