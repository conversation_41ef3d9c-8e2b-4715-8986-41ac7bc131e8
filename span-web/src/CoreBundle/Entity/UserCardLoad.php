<?php

namespace CoreBundle\Entity;

use Api<PERSON><PERSON>le\Entity\ApiEntityInterface;
use Api<PERSON><PERSON>le\Entity\Coin;
use Carbon\Carbon;
use CoreBundle\Entity\Traits\UserCardLoadRefundTrait;
use CoreBundle\Services\APIServices\GlobalCollectService;
use CoreBundle\Services\FirstView\FirstViewAPI;
use CoreBundle\Services\RevenueService;
use CoreBundle\Services\SSLEncryptionService;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Service;
use CoreBundle\Utils\Util;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\ORM\Mapping\Index;
use JMS\Serializer\Annotation as Serializer;
use phpDocumentor\Reflection\Types\Null_;
use SpendrBundle\Entity\PromotionCode;
use SpendrBundle\Entity\SpendrReferConfig;
use Spendr<PERSON><PERSON>le\Entity\SpendrReward;
use SpendrBundle\Services\LoadService;
use function Stringy\create as s;
use UsUnlockedBundle\Entity\UserCardLoadTrait;
use UsUnlockedBundle\Services\SlackService;
use CashOnWebBundle\Services\PartnerService;
use CoreBundle\Entity\UserCardBalance;
use CoreBundle\Entity\UserCardTransaction;

/**
 * UserCardLoad
 *
 * @ORM\Table(name="user_card_load", indexes={
 *     @Index(name="transaction_no_idx", columns={"transaction_no"}),
 *     @Index(name="fv_no_idx", columns={"fv_no"}),
 *     @Index(name="payment_id_idx", columns={"payment_id"}),
 *     @Index(name="payment_reference_idx", columns={"payment_reference"}),
 *     @Index(name="user_card_id_idx", columns={"user_card_id"}),
 *     @Index(name="created_at_idx", columns={"created_at"}),
 *     @Index(name="load_status_idx", columns={"load_status"}),
 *     @Index(name="type_idx", columns={"type"}),
 *     @Index(name="load_at_idx", columns={"load_at"}),
 *     @Index(name="load_status_type_at_idx", columns={"load_status", "type", "load_at"}),
 *     @Index(name="load_status_type_idx", columns={"load_status", "type"}),
 *     @Index(name="load_status_create_at_idx", columns={"load_status", "created_at"}),
 * }, options={"comment":"Load / unload history of all user cards."})
 * @ORM\Entity(repositoryClass="CoreBundle\Repository\UserCardLoadRepository")
 * @ORM\HasLifecycleCallbacks()
 */
class UserCardLoad extends BaseEntity implements ApiEntityInterface
{
    use UserCardLoadTrait;
    use UserCardLoadRefundTrait;

    const TYPE_LOAD_TEST = 'load_test';
    const TYPE_LOAD_CARD = 'load_card';
    /** @deprecated */
    const TYPE_LOAD_REPLACEMENT = 'load_replacement';
    const TYPE_LOAD_LOCAL_BALANCE = 'load_local_balance';
    const TYPE_LOAD_PENDING_MAX_BALANCE = 'load_pending_max_balance';
    const TYPE_LOAD_COMMISSION = 'load_commission';
    const TYPE_LOAD_TRANSFER = 'load_transfer';

    // When adding an unload type, please update method "needToSaveUnloadAmountToLocalBalance"
    const TYPE_UNLOAD_TEST = 'unload_test';
    const TYPE_UNLOAD = 'unload';
    const TYPE_UNLOAD_ACCOUNT_CLOSED = 'unload_account_closed';
    const TYPE_UNLOAD_CARD_DEACTIVATED = 'unload_card_deactivated';
    const TYPE_UNLOAD_CARD_CLOSED = 'unload_card_closed';
    const TYPE_UNLOAD_REPLACEMENT = 'unload_replacement';
    const TYPE_UNLOAD_BY_ADMIN = 'unload_by_admin';
    const TYPE_UNLOAD_UPGRADE = 'unload_upgrade';
    const TYPE_UNLOAD_FIX = 'upload_fix';
    const TYPE_UNLOAD_ISSUE_ANOTHER_NON_RELOADABLE = 'unload_issue_another_non_reloadable';
    const TYPE_UNLOAD_TRANSFER = 'unload_transfer';

    // Spendr Earn Type
    const EARN_TYPE_BANK_LINK = 'Bank Link';
    const EARN_TYPE_SPEND_OVER = 'First $100 Spend';
    const EARN_TYPE_SPEND_REWARD = 'Spend Reward';
    const EARN_TYPE_REFERRAL = 'Member Referral';

    const ALL_TYPES = [
        self::TYPE_LOAD_CARD,
        self::TYPE_LOAD_LOCAL_BALANCE,
        self::TYPE_LOAD_REPLACEMENT,
        self::TYPE_LOAD_TEST,

        self::TYPE_UNLOAD,
        self::TYPE_UNLOAD_ACCOUNT_CLOSED,
        self::TYPE_UNLOAD_CARD_CLOSED,
        self::TYPE_UNLOAD_CARD_DEACTIVATED,
        self::TYPE_UNLOAD_REPLACEMENT,
        self::TYPE_UNLOAD_UPGRADE,
        self::TYPE_UNLOAD_BY_ADMIN,
        self::TYPE_UNLOAD_FIX,
        self::TYPE_UNLOAD_TEST,
    ];

    const STATUS_UNKNOWN = null;
    const STATUS_PENDING = 'pending';
    const STATUS_COMPLETED = 'completed';
    const STATUS_EXPIRED = 'expired';

    // Load card status, ref to https://shinetechchina.atlassian.net/browse/TCSPAN-143
    const LOAD_STATUS_UNKNOWN   = null;
    const LOAD_STATUS_INITIATED = 'initiated';
    const LOAD_STATUS_PENDING   = 'pending';
    const LOAD_STATUS_CONFIRMED = 'confirmed'; // Captured or something else...
    const LOAD_STATUS_RECEIVED  = 'received';
    const LOAD_STATUS_LOADED    = 'loaded';
    const LOAD_STATUS_REFUNDED  = 'refunded';
    const LOAD_STATUS_ERROR     = 'error'; // Remove from the load queue
    const LOAD_STATUS_SKIPPED   = 'skipped'; // Remove from the load queue

    const RECEIVED_STATUS_ARRAY = [
        self::LOAD_STATUS_LOADED,
        self::LOAD_STATUS_RECEIVED,
    ];

    const ALL_LOAD_STATUS = [
        self::LOAD_STATUS_UNKNOWN,
        self::LOAD_STATUS_INITIATED,
        self::LOAD_STATUS_PENDING,
        self::LOAD_STATUS_CONFIRMED,
        self::LOAD_STATUS_RECEIVED,
        self::LOAD_STATUS_LOADED,
        self::LOAD_STATUS_REFUNDED,
        self::LOAD_STATUS_ERROR,
        self::LOAD_STATUS_SKIPPED,
    ];

    public function toApiArrayWithNegative()
    {
        $all = $this->toApiArray(true);

        if ($this->isUnload()) {
            $all['initialAmount'] = Money::format(-$this->getInitialAmount(), $this->getInitialCurrency());
            $all['payAmount'] = Money::format(-$this->getPayAmount(), $this->getPayCurrency() ?: 'USD');
            $all['payAmountUSD'] = Money::format(-$this->getPayAmountUSD(), 'USD');
            $all['receivedLocalAmount'] = Money::format(-$this->getReceivedLocalAmount(), $this->getReceivedLocalCurrency() ?: 'USD');
            $all['receivedAmountUSD'] = Money::format(-$this->getReceivedAmountUSD(), 'USD');
            $all['loadAmount'] = Money::format(-$this->getLoadAmount(), $this->getInitialCurrency());
            $all['loadAmountUSD'] = Money::format(-$this->getLoadAmountUSD(), 'USD');
        }

        $cost = $this->getCost();
        $all['cost'] = $cost ? Money::format($cost, 'USD') : '';

        $revenue = $this->getRevenue();
        $all['revenue'] = $revenue ? Money::format($revenue, 'USD') : '';

        return $all;
    }

    public function toApiArray(bool $extra = false): array
    {
        $initializeAt = $this->getInitializedAt();

        $data = [
            'id' => $this->getId(),
            'partner' => $this->getPartnerName(),
            'method' => $this->getMethodName(),
            'transactionId' => $this->getTransactionNo(),
            'totalAmount' => $this->getInitialCoin()->toApiArray(),
            'totalCost' => $this->getPayCoin()->toApiArray(),
            'totalReceived' => $this->getReceivedCoin()->toApiArray(),
            'loadAmount' => $this->getLoadCoin()->toApiArray(),
            'loadFee' => $this->getLoadFeeCoin()->toApiArray(),
            'membershipFee' => $this->getMembershipFeeCoin()->toApiArray(),
            'replacementFee' => $this->getReplacementFeeCoin()->toApiArray(),
            'discount' => $this->getDiscountCoin()->toApiArray(),
            'createdAt' => $initializeAt ? $initializeAt->format('c') : $this->getCreatedAt()->format('c'),
            'loadAt' => $this->getLoadAt() ? $this->getLoadAt()->format('c') : null,
            'status' => $this->getLoadStatus(),
            'order' => $this->getUserCardOrder(),
            'paymentId' => $this->getPaymentId(),
        ];

        if ($extra) {
            $other = Util::renderData('@Admin/LoadTransaction/data.html.twig', [
                'item' => $this,
            ]);
            $data = array_merge($data, $other);
        }

        return $data;
    }

    public function toAppApiArray(bool $extra = false, bool $withWaiting = false): array
    {
        $initializeAt = $this->getInitializedAt();

        $data = [
            'id' => $this->getId(),
            'totalAmount' => $this->getInitialCoin()->toApiArray(),
            'loadAmount' => $this->getLoadCoin()->toApiArray(),
            'createdAt' => $initializeAt ? $initializeAt->format('c') : $this->getCreatedAt()->format('c'),
            'loadAt' => $this->getLoadAt() ? $this->getLoadAt()->format('c') : null,
            'status' => $this->getLoadStatus(),
            'order' => $this->getUserCardOrder(),
            'reason' => null,
            'transactionNo' => $this->getTransactionNo(),
            'type' => null,
            'typeDisplayName' => null,
			'title' => null,
			'subtitle' => null,
			'note' => null,
			'notifyMsg' => null,
        ];
        // user card transaction
		Util::disableSoftDeletable();
        $transaction = UserCardTransaction::findByTranId($this->getTransactionNo());
        if ($transaction) {
            if ($this->getType() === self::TYPE_UNLOAD) {
                $data['loadAt'] = $data['status'] === self::LOAD_STATUS_ERROR
					? $data['loadAt'] :
					($transaction->getStatus() !== UserCardTransaction::STATUS_LL_SETTLED ? null : $data['loadAt']);
            }
            $data['bankId'] = $transaction->getUserCard()->getId();
            $data['bankName'] = $transaction->getUserCard()->getBankName();
            $bankNumber = '***' . substr(SSLEncryptionService::tryToDecrypt($transaction->getUserCard()->getAccountNumber()), -3);
            $meta = Util::meta($transaction->getUserCard());
            if ($transaction->getUserCard()->getType() === UserCard::LL_TYPE_PLAID && $meta && isset($meta['mask'])) {
                $bankNumber = '***' . $meta['mask'];
            }
            $data['bankNumber'] = $bankNumber;
			$data['paymentSource'] = $data['bankName'] . ($data['bankNumber'] ? '(' . $data['bankNumber'] . ')' : null);
        } else {
            $data['bankName'] = 'Spendr';
            $data['bankNumber'] = '***';
            $data['bankId'] = null;
			$data['paymentSource'] = $data['bankName'];
        }
        Util::enableSoftDeletable();

		$data['reason'] = '';
		$data['type'] = 'Deposit';
		$data['typeDisplayName'] = 'Deposit';
		$data['title'] = $data['bankName'];
		if ($this->getType() === UserCardLoad::TYPE_LOAD_CARD) {
			if (LoadService::isPromotionLoad($this)) {
				if (Util::meta($this, 'isPromotion')) {
					$data['type'] = 'Reward';
					$data['typeDisplayName'] = 'Reward';
				}
				if (Util::meta($this, 'rewardTitle')) {
				    $data['title'] = Util::meta($this, 'rewardTitle');
                } else {
                    $data['title'] = Util::meta($this, 'customMessage') ?? $data['type'];
                }
			}

			if (LoadService::isPrefundLoad($this)) {
				$data['reason'] = 'Your funds will arrive in 1-3 business days. For instant deposits please link another bank account.';
				if ($this->getLoadStatus() === UserCardLoad::LOAD_STATUS_LOADED) {
				    $data['reason'] = 'Your funds have been added to your balance.';
                }
			} else {
				if ($this->getStatus() == UserCardLoad::LOAD_STATUS_RECEIVED) {
				    $data['reason'] = 'Deposit complete. Please go back to view the results.';
                } else {
                    $data['reason'] = 'Your ' . strtolower($data['type']) . ' has been added to your account balance.';
                }
			}

			if (LoadService::isLoadForUnloadFailed($this)) {
				$data['title'] = 'Unload Return';
				$data['paymentSource'] = 'Spendr Balance';
			}
		} else if ($this->getType() === UserCardLoad::TYPE_UNLOAD) {
			$data['type'] = 'Unload';
			$data['typeDisplayName'] = 'Unload';
			$data['paymentSource'] = 'Spendr Balance';
			$data['reason'] = '';
			if (Util::meta($this, 'promoType')) {
			    $data['title'] = 'Reward Unload';
			    $data['note'] = self::revokeNote();
            } elseif (LoadService::isUnloadForLoadFailed($this)) {
				$data['title'] = 'Deposit Return';
				$data['note'] = self::unloadNote();
			}
		}

		$data['subtitle'] = $data['typeDisplayName'];
		if ($this->getLoadStatus() === self::LOAD_STATUS_ERROR) {
			$data['notifyMsg'] = $data['type'] . ' Failed';
		} else if ($this->getLoadStatus() === self::LOAD_STATUS_LOADED) {
			$data['notifyMsg'] = $data['type'] . ' Successful';
		} else {
			$data['notifyMsg'] = $data['type'] . ' Pending';
			$data['subtitle'] .= ' (Pending)';
		}

		$symbol = '';
		if ($this->getType() === UserCardLoad::TYPE_LOAD_CARD) {
			$symbol = '+';
		} else if ($this->getType() === UserCardLoad::TYPE_UNLOAD) {
			$symbol = '-';
		}
		$amount = $data['totalAmount'] && $data['totalAmount']['formatted']
			? $data['totalAmount']['formatted']: null;
		$data['amount'] = $amount;
		$data['amountText'] = $amount ? $symbol . $amount : null;

		if ($this->getLoadStatus() === UserCardLoad::LOAD_STATUS_ERROR) {
			$data['reason'] = $this->getError() !== '[]' ? $this->getError() : '';
		}

		// Do not change the order and structure unless necessary
		$data['displayList'] = [
			[
				'title' => 'Payment Source',
				'value' => $data['paymentSource'],
			],
			[
				'title' => $data['type'] . ' Total',
				'value' => $this->getInitialAmountText(false),
			],
			[
				'title' => 'Time of ' . $data['type'],
				'value' => Util::formatDateTime(
					$this->getInitializedAt(),
					Util::DATE_TIME_FORMAT,
					Util::timezone()
				),
			],
		];

		if ($withWaiting) {
            $data['waiting'] = true;
            if ($this->getLoadStatus() === UserCardLoad::LOAD_STATUS_ERROR) {
                $data['waiting'] = false;
            }
            if (
                LoadService::isPrefundLoad($this)
                && $this->getLoadStatus() === UserCardLoad::LOAD_STATUS_INITIATED
            ) {
                $data['waiting'] = false;
            }
            if (
                LoadService::isInstantLoad($this)
                && $this->getLoadStatus() === UserCardLoad::LOAD_STATUS_LOADED
            ) {
                $data['waiting'] = false;
            }
        }

        return $data;
    }

    public static function unloadNote()
	{
		$platform = Util::platform();
		$platformMeta = Util::meta($platform);
		$supportPhone = $platformMeta['customerSupportPhone'] ?? null;
		$supportEmail = $platformMeta['customerSupportEmail'] ?? null;
		$description = "Your bank declined your request to load funds to your Spendr Wallet. " .
			"The load amount you requested has been removed from your Spendr wallet.\r\n" .
			"If you have already spent funds, this may leave your account with a negative balance. " .
			"Please load good funds to your Spendr wallet as soon as possible and ensure the Bank account " .
			"you’re loading with has sufficient funds available.\r\n";
		if ($supportPhone) {
			$description .= 'If you have questions, please contact Support at ' . $supportPhone;
		}
		if ($supportEmail) {
			$description .= ' or email us at ' . $supportEmail;
		}
		return $description;
	}

	public static function revokeNote()
    {
        $platform = Util::platform();
        $platformMeta = Util::meta($platform);
        $supportPhone = $platformMeta['customerSupportPhone'] ?? null;
        $supportEmail = $platformMeta['customerSupportEmail'] ?? null;
        $description = "Your reward has been revoked." .
            " Please view the initial reward transaction for explanation.\r\n";
        if ($supportPhone) {
            $description .= 'If you have questions, please contact Support at ' . $supportPhone;
        }
        if ($supportEmail) {
            $description .= ' or email us at ' . $supportEmail;
        }
        return $description;
    }

    public static function getLoadTypePrefix($type)
    {
        $prefixes = [
            'load_',
            'unload_',
        ];
        foreach ($prefixes as $prefix) {
            if (Util::startsWith($type, $prefix)) {
                return $prefix;
            }
        }
        return '';
    }

    public static function getTransactionNoFromFv($fvNo)
    {
        $query = Util::em()->getRepository(\CoreBundle\Entity\UserCardLoad::class)
            ->createQueryBuilder('load');
        $expr = $query->expr();
        $rs = $query->where($expr->eq('load.fvNo', ':fvNo'))
            ->setParameter('fvNo', $fvNo)
            ->getQuery()
            ->getResult();
        if ($rs) {
            /** @var UserCardLoad $load */
            $load = $rs[0];
            return $load->getTransactionNo();
        }
        return $fvNo;
    }

    /**
     * @param $id
     * @return UserCardLoad|null
     */
    public static function find($id)
    {
        if (!$id) {
            return null;
        }
        return Util::em()->getRepository(self::class)->find($id);
    }

    /**
     * @param $no
     *
     * @return UserCardLoad|null
     */
    public static function findByTransactionNo($no)
    {
        if (!$no) {
            return null;
        }
        $rs = Util::em()->getRepository(self::class)->findBy([
            'transactionNo' => $no,
        ]);
        return $rs ? $rs[0] : null;
    }

    /**
     * @param $paymentId
     * @return UserCardLoad|null
     */
    public static function findByPaymentId($paymentId)
    {
        if (!$paymentId) {
            return null;
        }
        $repo = Util::em()->getRepository(\CoreBundle\Entity\UserCardLoad::class);
        $r = $repo->findOneBy(['paymentId' => $paymentId]);
        if ($r) {
            return $r;
        }
        $rs = $repo->createQueryBuilder('load')
            ->join('load.transaction', 't')
            ->where('t.requestKey = \'' . $paymentId . '\'')
            ->getQuery()
            ->getResult();
        if ($rs) {
            return $rs[0];
        }
        return null;
    }

    /**
     * @param $no
     *
     * @return UserCardLoad|null
     */
    public static function findByPaymentReference($no)
    {
        if (!$no) {
            return null;
        }
        $rs = Util::em()->getRepository(self::class)->findBy([
            'paymentReference' => $no,
        ]);
        return $rs ? $rs[0] : null;
    }

    public static function isNewStatus($newStatus, $oldStatus)
    {
        if ($newStatus === self::LOAD_STATUS_RECEIVED && $oldStatus === self::LOAD_STATUS_ERROR) {
            return true;
        }
        $newIndex = array_search($newStatus, self::ALL_LOAD_STATUS, true);
        $oldIndex = array_search($oldStatus, self::ALL_LOAD_STATUS, true);
        return $newIndex > $oldIndex;
    }

    public function isValid()
    {
        return in_array($this->getLoadStatus(), [
            self::LOAD_STATUS_INITIATED,
            self::LOAD_STATUS_PENDING,
            self::LOAD_STATUS_CONFIRMED,
            self::LOAD_STATUS_RECEIVED,
            self::LOAD_STATUS_LOADED,
            self::LOAD_STATUS_REFUNDED,
            self::LOAD_STATUS_ERROR,
            self::LOAD_STATUS_SKIPPED,
        ], true);
    }

    public function isReceived()
    {
        return in_array($this->getLoadStatus(), [
            self::LOAD_STATUS_RECEIVED,
            self::LOAD_STATUS_LOADED,
        ], true);
    }

    public function isLoaded()
    {
        return $this->getLoadStatus() === self::LOAD_STATUS_LOADED;
    }

    public function isExpired()
    {
        return $this->getLoadStatus() === self::LOAD_STATUS_ERROR &&
               $this->getStatus() === self::STATUS_EXPIRED;
    }

    public function getFilter()
    {
        return Util::startsWith($this->getType(), 'load_')
            ? FirstViewAPI::FILTER_LOAD
            : FirstViewAPI::FILTER_UNLOAD;
    }

    public function isUnload()
    {
        return $this->getFilter() === FirstViewAPI::FILTER_UNLOAD;
    }

    public function needToChargeUnloadFee()
    {
        $type = $this->getType();
        if (!Util::startsWith($type, 'unload')) {
            return false;
        }
        return !in_array($type, [
            self::TYPE_UNLOAD_UPGRADE,
            self::TYPE_UNLOAD_REPLACEMENT,
            self::TYPE_UNLOAD_FIX,
            self::TYPE_UNLOAD_ISSUE_ANOTHER_NON_RELOADABLE,
        ], true);
    }

    public function needToChargeReplacementFee()
    {
        $uc = $this->getUserCard();
        $user = $uc->getUser();
        $closed = $user->getCards()->filter(function (UserCard $_uc) use ($uc) {
            return Util::eq($uc->getCard(), $_uc->getCard())
                && !Util::eq($uc, $_uc)
                && !$_uc->isActive();
        });
        if ($closed->isEmpty()) {
            return false;
        }
        $loads = $uc->getLoads(self::TYPE_LOAD_CARD)->filter(function (UserCardLoad $ucl) {
            return !Util::eq($this, $ucl)
                && in_array($ucl->getLoadStatus(), self::RECEIVED_STATUS_ARRAY, true);
        });
        return $loads->isEmpty();
    }

    public function needToSaveUnloadAmountToLocalBalance()
    {
        // In other platforms, we don't need the local balance logic.
        if (!$this->getUserCard()->isUsUnlockedLegacy()) {
            return false;
        }

        return !in_array($this->getType(), [
            // Replacement's unload amount is just the replacement fee, so needn't to be added to local balance.
            self::TYPE_UNLOAD_REPLACEMENT,

            // Balance needs to load to another new non-reloadable card in load card script.
            self::TYPE_UNLOAD_ISSUE_ANOTHER_NON_RELOADABLE,

            // Transferred to another user's card
            self::TYPE_UNLOAD_TRANSFER,
        ], true);
    }

    public function getLoadAmountText()
    {
        return Money::format($this->getLoadAmount(), $this->getInitialCurrency());
    }

    public function getInitialCoin()
    {
        return Coin::create($this->getInitialAmount(), $this->getInitialCurrency());
    }

    public function getReceivedCoin()
    {
        return Coin::create($this->getReceivedAmount(), $this->getReceivedCurrency());
    }

    public function getPayCoin()
    {
        return Coin::create($this->getPayAmount(), $this->getPayCurrency());
    }

    public function getLoadCoin()
    {
        return Coin::create($this->getLoadAmount(), $this->getUserCard()->getCurrency());
    }

    public function getLoadFeeCoin()
    {
        return Coin::create($this->getLoadFeeUSD(), 'USD');
    }

    public function getUnloadFeeCoin()
    {
        return Coin::create($this->getUnloadFee(), $this->getInitialCurrency());
    }

    public function getMembershipFeeCoin()
    {
        return Coin::create($this->getMembershipFeeUSD(), 'USD');
    }

    public function getReplacementFeeCoin()
    {
        return Coin::create($this->getReplacementFee(), $this->getInitialCurrency());
    }

    public function getDiscountCoin()
    {
        return Coin::create($this->getDiscount(), $this->getInitialCurrency());
    }

    public function getStep()
    {
        if (!$this->initialAmount) {
            return 1;
        }
        if (!$this->partner || !$this->method || !$this->payCurrency) {
            return 2;
        }
        if (!$this->payAmount || !$this->feeStructure || !$this->transaction) {
            return 3;
        }
        if ($this->loadStatus === self::LOAD_STATUS_PENDING) {
            return 4;
        }
        return 5;
    }

    public function getFeeStructureText($key, $format = true)
    {
        $items = Util::s2j($this->getFeeStructure());
        $amount = 0;
        if (isset($items[$key])) {
            $amount = $items[$key];
        } else if (isset($items['otherFees'][$key])) {
            $amount = $items['otherFees'][$key];
        }
        if ('discount' === $key) {
            return $amount ?: '';
        }
        if (!$format) {
            return $amount;
        }
        return Money::format($amount, $this->getInitialCurrency());
    }

    public function getTransactionNo($update = false, $direct = false)
    {
        $no = $this->transactionNo;
        if ($direct) {
            return $no;
        }
        if ($update) {
            // Save the old transaction no.
            if ($no) {
                Util::updateJson($this, 'meta', [
                    'history_txn_no_' . $no => time(),
                ], false);
            }

            $no = Util::unique_id();
            $this->setTransactionNo($no);
            Util::persist($this);
        }
        return $no;
    }

    public function getParsedPaymentId()
    {
        $meta = Util::s2j($this->getMeta());
        if (!empty($meta['paymentReference'])) {
            return $meta['paymentReference'];
        }
        $transaction = $this->getTransaction();
        if ($transaction) {
            return $transaction->getPaymentId();
        }
        return '';
    }

    public function updateFeeStructure($array)
    {
        $all = Util::s2j($this->getFeeStructure());
        $all = array_replace_recursive($all, $array);
        $this->setFeeStructure(Util::j2s($all));

        return $this;
    }

    public function getExpectReceiveAmount($receiveCurrency)
    {
        return (int)Money::convert($this->getPayAmount(), $this->getPayCurrency(), $receiveCurrency);
    }

    // NOTE: TCSPAN-766
    public function updateLoadAmountWhenReceived($force = false)
    {
        $received = (int)$this->getReceivedAmount();
        if (!$received) {
            return;
        }
        $initialCurrency = $this->getInitialCurrency();
        $receivedCurrency = $this->getReceivedCurrency();
        $expectReceive = $this->getExpectReceiveAmount($receivedCurrency);

        /** @var UserCard $uc */
        $uc = $this->getUserCard();
        if ($received === $expectReceive && !$force) {
            Service::log('Received amount is same as expected: ' . $received, [
                'currency' => $receivedCurrency,
                'load'     => $this->getId(),
            ]);

            $loadAmount = $this->getInitialAmount();

            Util::updateJson($this, 'meta', 'updatedLoadAmount', false);
        } else {
            Service::log('Received amount is different from expected receive amount or force update!', [
                'load'     => $this->getId(),
                'force'    => $force,
                'received' => $received,
                'expect'   => $expectReceive,
                'currency' => $receivedCurrency,
            ], 'warn');

            /** @var LoadMethod $method */
            $method = $this->getMethod();
            $methodName = $method ? $method->getName() : 'Bank Transfer';

            // Membership fee
            $membershipFee = $this->getMembershipFeeUSD();
            if ($membershipFee) {
                $membershipFee = Money::convertWithExtra($membershipFee, 'USD', $receivedCurrency);
            }

            // Other fees
            $otherFee = 0;
            foreach ((array)Util::s2j($uc->getPendingFees()) as $of) {
                if ($of['type'] === UserCard::PENDING_FEE_MEMBERSHIP_OR_LOAD) {
                    continue;
                }
                $otherFee += Money::convertWithExtra($of['amount'], $initialCurrency, $receivedCurrency);
            }

            // Load fee
            $from = $received - $membershipFee - $otherFee;
            $loadAmount = $uc->calculateReversedLoadAmount($methodName, $from, $receivedCurrency, $initialCurrency, $this);

            Util::updateJson($this, 'meta', [
                'updatedLoadAmount' => [
                    'receivedAmount'   => $received,
                    'receivedCurrency' => $receivedCurrency,
                    'membershipFee'    => $membershipFee,
                    'loadAmount'       => $loadAmount,
                    'time'             => time(),
                ],
            ], false);

            $loadFee = $uc->calculateFinalFee($methodName, $loadAmount);
            if (($p = Promotion::applyUsuLoadFeePromotion($uc, $this, $loadFee)) !== false) {
                $loadFee = $p;
            }

            $this->updateFeeStructure([
                'loadFee' => $loadFee,
            ]);
            $this->fillCommission(true);
            RevenueService::updateCostAndRevenue($this);

            Service::log('Updated load amount to ' . $loadAmount, [
                'load'     => $this->getId(),
            ]);
        }

        // Add load amount to pending fees if it's less than 0
        if ($loadAmount < 0) {
            if ($uc->getCardProgram()->isUsUnlockedLegacy()) {
                $uc->updatePendingFee(
                    UserCard::PENDING_FEE_MEMBERSHIP_OR_LOAD,
                    'load',
                    $this->getId(),
                    Money::convert(-$loadAmount, $initialCurrency, $uc->getCurrency())
                );
            } else {
                $receivedUSD = Money::convertWithReversedExtra($received, $receivedCurrency, $initialCurrency);

                // All received amount as the membership fee. Only charged partial membership fee.
                $this->setMembershipFeeUSD($receivedUSD);
            }

            Service::log('Updated load amount to 0 due to less than 0: ' . $loadAmount, [], 'error');

            $loadAmount = 0;
        }

        // Deduct the membership fee if it's already charged in other load requests
        $membershipFee = $this->getMembershipFeeUSD();
        if ($membershipFee) {
            $user = $uc->getUser();
            $unpaid = $user->getUnpaidMembershipFee($this);
            if ($unpaid < $membershipFee) {
                $this->updateFeeStructure([
                    'membershipFee' => $unpaid,
                ]);
                $oldLoadAmount = $loadAmount;
                $loadAmount += $membershipFee - $unpaid;

                Util::updateJson($this, 'meta', [
                    'updatedLoadAmountForMembershipFee' => [
                        'membershipFee'    => $membershipFee,
                        'oldLoadAmount'    => $oldLoadAmount,
                        'newLoadAmount'    => $loadAmount,
                        'time'             => time(),
                    ],
                ], false);

                SlackService::info('Removed the membership fee from the load because the fee was already charged before.', [
                    'load' => $this->id,
                    'oldLoadAmount' => Money::format($oldLoadAmount, $this->getInitialCurrency()),
                    'newLoadAmount' => Money::format($loadAmount, $this->getInitialCurrency()),
                ]);
            }
        }

        $this->setLoadAmount($loadAmount)
            ->persist();
    }

    public function getRealLoadAmount()
    {
        // Fix load amount
        if (null === $this->getLoadAmount()) {
            $this->setLoadAmount($this->getInitialAmount())
                ->persist();
        }

        $amount = $this->getLoadAmount();

        $unloadFee = $this->getFeeStructureElement('unloadFee');
        if ($unloadFee) {
            $amount += $unloadFee;
        }

        return $amount;
    }

    public function getInitialAmountText($withCurrency = true)
    {
        $amount = $this->getInitialAmount();
        if ($amount === null) {
            return '';
        }
        return Money::format($amount, $this->getInitialCurrency(), $withCurrency);
    }

    public function getReceivedAmountText()
    {
        $amount = $this->getReceivedAmount();
        if ($amount === null) {
            return '';
        }
        return Money::format($amount, $this->getReceivedCurrency());
    }

    /**
     * @ORM\PrePersist()
     */
    public function fillServerKey()
    {
        if (!$this->getServerKey()) {
            $this->setServerKey(Util::getServerKey());
        }
    }

    public function getPartnerName()
    {
        $partner = $this->getPartner();
        if ($partner) {
            return $partner->getName();
        }
        $meta = Util::s2j($this->getMeta());
        if (!empty($meta['partner'])) {
            return $meta['partner'];
        }
        return '';
    }

    public function getMethodName(bool $short = false)
    {
        $method = $this->getMethod();
        if ($method) {
            if ($short) {
                return $method->getShortName();
            }
            return $method->getName();
        }
        $meta = Util::s2j($this->getMeta());
        if (!empty($meta['merchant'])) {
            return $meta['merchant'];
        }
        return '';
    }

    public function isImported()
    {
        $meta = Util::s2j($this->getMeta());
        return !empty($meta['imported']);
    }

    public function getUserDiscounts() {
        $discount = $this->getFeeStructureElement('discount');
        $result = [];
        if (!$discount) {
            return $result;
        }
        $repo = Util::em()->getRepository(\CoreBundle\Entity\CardProgram::class);
        $uc = $this->getUserCard();
        $user = $uc->getUser();
        $cp = $uc->getCard()->getCardProgram();
        if (!empty($discount['loadFee'])) {
            $cpFeeItem = $repo->getFeeItem($cp, $this->getMethodName(), $user);
            if ($cpFeeItem) {
                $ud = UserDiscount::find($user, $cpFeeItem);
                if ($ud) {
                    $result[] = $ud;
                }
            }
        }
        if (!empty($discount['membershipFee'])) {
            $cpFeeItem = $repo->getFeeItem($cp, FeeGlobalName::ONE_TIME_MEMBERSHIP_FEE, $user);
            if ($cpFeeItem) {
                $ud = UserDiscount::find($user, $cpFeeItem);
                if ($ud) {
                    $result[] = $ud;
                }
            }
        }
        return $result;
    }

    public function fillCommission($force = false)
    {
        if (self::TYPE_LOAD_CARD !== $this->getType()) {
            return;
        }
        if ($this->getAffiliatePayment()) {
            return;
        }

        $loadStatus = $this->getLoadStatus();
        if (in_array($loadStatus, [
            self::LOAD_STATUS_REFUNDED,
            self::LOAD_STATUS_ERROR,
        ])) {
            $this->setMembershipCommissionUSD(null)
                ->setLoadCommissionUSD(null);
            return;
        }

        if (!in_array($loadStatus, self::RECEIVED_STATUS_ARRAY, true)) {
            return;
        }
        if ($this->getMethodName() === LoadMethod::PAYNAME_SYSTEM) {
            return;
        }
        $uc = $this->getUserCard();
        $user = $uc->getUser();
        $affiliate = $user->getAffiliate();
        if (!$affiliate) {
            return;
        }
        if ($force) {
            $this->setLoadCommissionUSD(null)
                ->setMembershipCommissionUSD(null);
        }
        $shares = $affiliate->getAffiliateRevenueShare();
        /** @var AffiliateRevenueShare $share */
        foreach ($shares as $share) {
            $type = $share->getType();
            if (AffiliateRevenueShare::TYPE_MEMBERSHIP_FEE === $type) {
                if (!$force && $this->getMembershipCommissionUSD() !== null) {
                    continue;
                }
                $origin = (int)$this->getMembershipFeeUSD();
                if (!$origin || $origin <= 0) {
                    $this->setMembershipCommissionUSD(null);
                }
                if ($origin && $origin > 0) {
                    $commission = $share->calculate($origin);

                    $start = $share->getStart() ?: 0;
                    if ($start) {
                        $q = Util::em()->getRepository(\CoreBundle\Entity\UserCardLoad::class)
                            ->createQueryBuilder('ucl')
                            ->join('ucl.userCard', 'uc')
                            ->join('uc.user', 'u');
                        $created = $q->where('u.affiliate = :affiliate')
                            ->andWhere($q->expr()->in('ucl.loadStatus', ':loadStatus'))
                            ->andWhere('ucl.membershipFeeUSD is not null')
                            ->andWhere('ucl.membershipFeeUSD > 0')
                            ->andWhere('ucl.receivedAt < :receivedAt')
                            ->andWhere('ucl.type = :type')
                            ->setParameters([
                                'affiliate' => $affiliate,
                                'type' => self::TYPE_LOAD_CARD,
                                'loadStatus' => self::RECEIVED_STATUS_ARRAY,
                                'receivedAt' => $this->getReceivedAt(),
                            ])
                            ->select('count(distinct ucl)')
                            ->getQuery()
                            ->getSingleScalarResult();
                        if ((int)$created < $start) {
                            $commission = null;
                        }
                    }
                    $this->setMembershipCommissionUSD($commission);

                    if ($commission) {
                        Util::updateJson($this, 'meta', [
                            'membershipCommissionDetail' => $share->toArray(),
                        ], false);
                    }
                } else if (AffiliateRevenueShare::MEMBERSHIP_FEE_COMMISION_BASE && $uc->isUsUnlocked() && $this->getReceivedAt()) {
                    // https://app.asana.com/0/1198894128947639/1201802042815418
                    // Affiliates are paid $5 per linked member (Member who loads)
                    $newStart = Carbon::create(2022, 2, 11, 10, 0, 0, Util::tzUTC());
                    if ($newStart->lt($this->getReceivedAt())) {
                        $metaKey = 'paidNewAffiliateMembershipFeeCommission';
                        $cacheKey = $metaKey . '_' . $user->getId();
                        if (!Data::has($cacheKey) && !Util::meta($user, $metaKey)) {
                            Log::debug('Adding membership fee commission for the user ' . $user->getId());
                            $commission = $share->calculate(AffiliateRevenueShare::MEMBERSHIP_FEE_COMMISION_BASE);
                            $this->setMembershipCommissionUSD($commission);
                            Util::updateMeta($user, [
                                $metaKey => true,
                            ], false);
                            Data::set($cacheKey, true);
                        }
                    }
                }
            } else if (AffiliateRevenueShare::TYPE_LOAD_AMOUNT === $type) {
                if (!$force && $this->getLoadCommissionUSD() !== null) {
                    continue;
                }

                $origin = (int)$this->getLoadAmountUSD();
                $commission = $share->calculate($origin);

                $loadFee = (int)($this->getLoadFeeUSD() ?? 0);
                if ($commission && $commission > $loadFee) {
                    $commission = $loadFee;
                }

                $start = $share->getStart() ?: 0;
                if ($start) {
                    $q = Util::em()->getRepository(\CoreBundle\Entity\UserCardLoad::class)
                        ->createQueryBuilder('ucl')
                        ->join('ucl.userCard', 'uc')
                        ->join('uc.user', 'u');
                    $created = $q->where('u.affiliate = :affiliate')
                        ->andWhere($q->expr()->in('ucl.loadStatus', ':loadStatus'))
                        ->andWhere('ucl.receivedAt < :receivedAt')
                        ->andWhere('ucl.type = :type')
                        ->setParameters([
                            'affiliate' => $affiliate,
                            'type' => self::TYPE_LOAD_CARD,
                            'loadStatus' => self::RECEIVED_STATUS_ARRAY,
                            'receivedAt' => $this->getReceivedAt(),
                        ])
                        ->select('count(distinct ucl)')
                        ->getQuery()
                        ->getSingleScalarResult();
                    if ((int)$created < $start) {
                        $commission = null;
                    }
                }
                $this->setLoadCommissionUSD($commission);

                if ($commission) {
                    Util::updateJson($this, 'meta', [
                        'loadAmountCommissionDetail' => $share->toArray(),
                    ], false);
                }
            }
        }
    }

    public function getCommissionUSD()
    {
        $membership = $this->getMembershipCommissionUSD() ?: 0;
        $load = $this->getLoadCommissionUSD() ?: 0;
        if ($membership || $load) {
            return $membership + $load;
        }
        return null;
    }

    public function updateOrder()
    {
        if ($this->getLoadStatus() === self::LOAD_STATUS_UNKNOWN || $this->getUserCardOrder()) {
            return;
        }
        $uct = Util::em()->getRepository(\CoreBundle\Entity\UserCardLoad::class)
            ->createQueryBuilder('load')
            ->where('load.userCard = :userCard')
            ->andWhere('load.type = :type')
            ->andWhere('load.loadStatus is not null')
            ->andWhere('load.createdAt < :createdAt')
            ->setParameter('userCard', $this->getUserCard())
            ->setParameter('type', self::TYPE_LOAD_CARD)
            ->setParameter('createdAt', $this->getCreatedAt())
            ->setMaxResults(1)
            ->orderBy('load.createdAt', 'desc')
            ->getQuery()
            ->getResult();
        $order = 1;
        if ($uct) {
            /** @var UserCardTransaction $uct */
            $uct = $uct[0];
            $order = $uct->getUserCardOrder() + 1;
        }
        $this->setUserCardOrder($order);
    }

    public function saveFeeHistory()
    {
        $user = $this->getUserCard()->getUser();
        $role = $this->getUserCard()->getUser()->getTeams()->map(function (Role $role) {
          return $role->getName();
        })->toArray();
        if ($this->getUserCard()->isCashOnWeb() && !in_array(Role::ROLE_CASH_ON_WEB_PARTNER,$role) ) {
          $cp = $this->getUserCard()->getCard()->getCardProgram();
          // membership fee
          $feeName = FeeGlobalName::ONE_TIME_MEMBERSHIP_FEE;
          $feeItem = Util::em()->getRepository(\CoreBundle\Entity\CardProgram::class)->getFeeItem($cp, $feeName);
          $shipCost =   $feeItem ? $feeItem->calculateCost($this->getLoadAmountUSD(), 'USD') : 0;
          UserFeeHistory::createCow($user, $this->getMembershipFeeUSD(), $this->getMembershipFeeUSD(), FeeGlobalName::ONE_TIME_MEMBERSHIP_FEE, $shipCost, $this);
          PartnerService::updateBalance($cp->getPlatform()->getName(), $this->getMembershipFeeUSD(), $this->getMembershipFeeUSD(), $feeName, $shipCost, UserCardBalance::TYPE_MEMBER_SHIP, 'Load id: '.$this->getId(), $this);

          // load fee
          $feeName = FeeGlobalName::COW_LOAD_FEE;
          UserFeeHistory::createCow($user, $this->getLoadFeeUSD(), $this->getLoadFeeUSD(), FeeGlobalName::COW_LOAD_FEE, 100, $this);
          PartnerService::updateBalance($cp->getPlatform()->getName(), $this->getLoadFeeUSD(), $this->getLoadFeeUSD(), $feeName, 100, UserCardBalance::TYPE_LOAD_FEE,'load id: '.$this->getId(), $this);
          if ($this->getType() === self::TYPE_LOAD_CARD) {
            PartnerService::updateProgramBalance($user, $cp->getPlatform()->getName(), -$this->getPayAmount(), $this->getMembershipFeeUSD(), UserCardBalance::TYPE_MEMBER_SHIP, 'Load id: '.$this->getId(), $this, $this->getLoadFeeUSD());
          }
          // unload fee
          $feeName = FeeGlobalName::UNLOAD_FEE;
          $feeItem = Util::em()->getRepository(\CoreBundle\Entity\CardProgram::class)->getFeeItem($cp, $feeName);
          $unLoadCost =   $feeItem ? $feeItem->calculateCost($this->getUnloadFeeUSD(), 'USD') : 0;
          UserFeeHistory::createCow($user, $this->getUnloadFeeUSD(), $this->getUnloadFeeUSD(), FeeGlobalName::UNLOAD_FEE, $unLoadCost, $this);
          PartnerService::updateBalance($cp->getPlatform()->getName(), $this->getUnloadFeeUSD(), $this->getUnloadFeeUSD(), $feeName, $unLoadCost, UserCardBalance::TYPE_UNLOAD_FEE,'Unload id: '.$this->getId(), $this);
          if ($this->getType() === self::TYPE_UNLOAD) {
            PartnerService::updateProgramBalance($user, $cp->getPlatform()->getName(), $this->getLoadAmountUSD(), $this->getUnloadFeeUSD(), UserCardBalance::TYPE_UNLOAD_FEE, 'Unload id: '.$this->getId(), $this);
          }
        } else {
          UserFeeHistory::create($user, $this->getLoadFeeUSD(), $this->getMethodName(), $this);
          UserFeeHistory::create($user, $this->getMembershipFeeUSD(), FeeGlobalName::ONE_TIME_MEMBERSHIP_FEE, $this);
          UserFeeHistory::create($user, $this->getReplacementFeeUSD(), FeeGlobalName::REPLACEMENT_FEE_CLOSED_CARD, $this);
          UserFeeHistory::create($user, $this->getUnloadFeeUSD(), FeeGlobalName::UNLOAD_FEE, $this);
        }
        Util::em()->flush();
    }

    /**
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\UserCard", inversedBy="loads")
     * @ORM\JoinColumn(name="user_card_id", referencedColumnName="id", onDelete="cascade")
     * @Serializer\Exclude()
     */
    private $userCard;

    /**
     * @ORM\ManyToOne(targetEntity="SpendrBundle\Entity\PromotionCode", inversedBy="loads")
     * @ORM\JoinColumn(name="promo_code_id", referencedColumnName="id", onDelete="cascade")
     * @Serializer\Exclude()
     */
    private $promoCode;

    /**
     * @ORM\ManyToOne(targetEntity="SpendrBundle\Entity\SpendrReward")
     * @ORM\JoinColumn(name="reward_id", referencedColumnName="id")
     */
    private $reward;

    /**
     * @ORM\ManyToOne(targetEntity="SpendrBundle\Entity\SpendrReferConfig")
     * @ORM\JoinColumn(name="refer_config_id", referencedColumnName="id")
     */
    private $referConfig;

    /**
     * @var string
     *
     * @ORM\Column(name="type", type="string", length=255, options={"default":"load_card","comment":"load_card, load_local_balance, load_commission, unload, unload_card_closed, unload_account_closed, unload_upgrade, etc."})
     */
    private $type;

    /**
     * // NOTE: TCSPAN-620: Prevent from loading the card for same load attempt on different servers
     * @var string
     *
     * @ORM\Column(name="server_key", type="string", length=255, nullable=true, options={"comment":"Unique key on each server. Prevent from loading the card for same load attempt on different servers"})
     */
    private $serverKey;

    /**
     * @var int
     *
     * @ORM\Column(name="initial_amount", type="bigint", options={"comment":"Amount when user started the load request"})
     */
    private $initialAmount;

    /**
     * @var string
     *
     * @ORM\Column(name="initial_currency", type="string", length=255, options={"comment":"Currency when user started the load request"})
     */
    private $initialCurrency;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="initialized_at", type="datetime", nullable=true)
     */
    private $initializedAt;

    /**
     * @var string
     *
     * @ORM\Column(name="fee_structure", type="text", nullable=true, options={"comment":"JSON. Fee structure details"})
     */
    private $feeStructure;

    /**
     * @var integer
     *
     * @ORM\Column(name="membership_fee_usd", type="bigint", nullable=true)
     */
    private $membershipFeeUSD;

    /**
     * @var integer
     *
     * @ORM\Column(name="replacement_fee_usd", type="bigint", nullable=true)
     */
    private $replacementFeeUSD;

    /**
     * @var integer
     *
     * @ORM\Column(name="load_fee_usd", type="bigint", nullable=true)
     */
    private $loadFeeUSD;

    /**
     * @var integer
     *
     * @ORM\Column(name="unload_fee_usd", type="bigint", nullable=true)
     */
    private $unloadFeeUSD;

    /**
     * @var int
     *
     * @ORM\Column(name="pay_amount", type="bigint", nullable=true, options={"comment":"Amount when money is paid"})
     */
    private $payAmount;

    /**
     * @var int
     *
     * @ORM\Column(name="pay_amount_usd", type="bigint", nullable=true)
     */
    private $payAmountUSD;

    /**
     * @var string
     *
     * @ORM\Column(name="pay_currency", type="string", length=255, nullable=true, options={"comment":"Currency of the payment"})
     */
    private $payCurrency;

    /**
     * Currency rate of initial currency to foreign(pay) currency when in step 3,
     * for instance 6.8499 when convert from USD to CNY
     * @var string
     *
     * @ORM\Column(name="currency_rate", type="string", length=20, nullable=true, options={"comment":"Currency rate of initial currency to foreign(pay) currency when in step 3,"})
     */
    private $currencyRate;

    /**
     * @var string Internal Transaction No -> ***** Merchant Reference *****
     *
     * @ORM\Column(name="transaction_no", type="string", length=20, nullable=true, options={"comment":"Internal Transaction No(Merchant Reference) generated by our system"})
     */
    private $transactionNo;

    /**
     * @var string PaymentID
     *
     * @ORM\Column(name="payment_id", type="string", length=40, nullable=true, options={"comment":"Payment ID(useless) returned from GlobalCollect or Alternative Payments"})
     */
    private $paymentId;

    /**
     * @var string Payment Reference
     *
     * @ORM\Column(name="payment_reference", type="string", length=63, nullable=true, options={"comment":"Payment ID(important to track order) returned from GlobalCollect or Alternative Payments"})
     */
    private $paymentReference;

    /**
     * @var string FirstView Transaction No
     *
     * @ORM\Column(name="fv_no", type="string", length=20, nullable=true, options={"comment":"FirstView transaction number"})
     */
    private $fvNo;

    /**
     * @var LoadPartner
     *
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\LoadPartner")
     * @Serializer\Exclude()
     */
    private $partner;

    /**
     * @var LoadMethod
     *
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\LoadMethod")
     * @Serializer\Exclude()
     */
    private $method;

    /**
     * @var Transaction
     *
     * @ORM\OneToOne(targetEntity="CoreBundle\Entity\Transaction")
     * @ORM\JoinColumn(name="transaction_id", referencedColumnName="id", onDelete="SET NULL")
     * @Serializer\Exclude()
     */
    private $transaction;

    /**
     * @var integer
     *
     * @ORM\Column(name="received_amount", type="bigint", nullable=true, options={"comment":"Received amount. Parsed from GlobalCollect's reports or Alternative Payments' callback."})
     */
    private $receivedAmount;

    /**
     * @var integer
     *
     * @ORM\Column(name="received_local_amount", type="bigint", nullable=true, options={"comment":"Received local amount. Especially for GlobalCollect. Parsed from their reports."})
     */
    private $receivedLocalAmount;

    /**
     * @var integer
     *
     * @ORM\Column(name="received_amount_usd", type="bigint", nullable=true)
     */
    private $receivedAmountUSD;

    /**
     * @var string
     *
     * @ORM\Column(name="received_currency", type="string", length=16, nullable=true)
     */
    private $receivedCurrency;

    /**
     * @var string
     *
     * @ORM\Column(name="received_local_currency", type="string", nullable=true)
     */
    private $receivedLocalCurrency;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="received_at", type="datetime", nullable=true)
     */
    private $receivedAt;

    /**
     * The external_invoke for this load/unload request.
     *
     * @var ExternalInvoke
     *
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\ExternalInvoke")
     * @Serializer\Exclude()
     */
    private $loadInvoke;

    /**
     * @var integer
     *
     * @ORM\Column(name="load_amount", type="bigint", nullable=true, options={"comment":"Final load amount to the card"})
     */
    private $loadAmount;

    /**
     * @var integer
     *
     * @ORM\Column(name="load_amount_usd", type="bigint", nullable=true)
     */
    private $loadAmountUSD;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="load_at", type="datetime", nullable=true)
     */
    private $loadAt;

    /**
     * @var string
     *
     * @ORM\Column(name="load_status", type="string", length=255, nullable=true, options={"comment":"Important status to load/unload request. unknown(as null), initiated, pending, confirmed, received, loaded or refunded"})
     */
    private $loadStatus;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="completed_at", type="datetime", nullable=true)
     */
    private $completedAt;

    /**
     * @var string
     *
     * @ORM\Column(name="error", type="text", nullable=true)
     */
    private $error;

    /**
     * @var string data in wx file
     *
     * @ORM\Column(name="wx", type="text", nullable=true, options={"comment":"string data in wx file"})
     */
    private $wx;

    /**
     * @var \DateTime Time when wx file was parsed
     *
     * @ORM\Column(name="wx_parsed_at", type="datetime", nullable=true, options={"comment":"When GlobalCollect's wx report is parsed which includes this transaction"})
     */
    private $wxParsedAt;

    /**
     * @var boolean First loading or reload
     *
     * @ORM\Column(name="reload", type="boolean", nullable=true, options={"default": true,"comment":"Whether this is the first load to this user card or not"})
     */
    private $reload;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=255, nullable=true, options={"comment":"unknown(as null), pending, completed"})
     */
    private $status;

    /**
     * @var integer
     *
     * @ORM\Column(name="membership_commission_usd", type="bigint", nullable=true, options={"comment":"Membership commission amount for affiliate"})
     */
    private $membershipCommissionUSD;

    /**
     * @var integer
     *
     * @ORM\Column(name="load_commission_usd", type="bigint", nullable=true, options={"comment":"Load commission amount for affiliate"})
     */
    private $loadCommissionUSD;

    /**
     * @var AffiliatePayment $affiliatePayment
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\AffiliatePayment", inversedBy="loads")
     * @Serializer\Exclude()
     */
    private $affiliatePayment;

    /**
     * @var string
     * @ORM\Column(name="meta", type="text", nullable=true)
     */
    private $meta;

    /**
     * @var integer $order
     * @ORM\Column(name="user_card_order", type="integer", nullable=true, options={"comment":"Load order of the user card. Start from 1. Only count for `load_card` type"})
     */
    private $userCardOrder; // Load order of the user card. Start from 1. Only count for `load_card` type

    /**
     * @ORM\OneToMany(targetEntity="CoreBundle\Entity\UserCardRefund", mappedBy="userCardLoad")
     * @Serializer\Exclude()
     */
    private $refunds;

    /**
     * Load cost for the load partners
     *
     * @var integer
     *
     * @ORM\Column(name="cost", type="bigint", nullable=true)
     */
    private $cost;

    /**
     * Load revenue = Load fee - cost
     *
     * @var integer
     *
     * @ORM\Column(name="revenue", type="bigint", nullable=true)
     */
    private $revenue;

    /**
     * @var string
     *
     * @ORM\Column(name="promo_type", type="string", length=64, nullable=true, options={"comment":"Earn, Promo Code, Manual"})
     */
    private $promoType;

    /**
     * @var boolean
     *
     * @ORM\Column(name="invalid_data", type="boolean", nullable=true, options={"comment":"Whether the record is invalid data or not"})
     */
    private $invalidData;

    /**
     * @var string
     *
     * @ORM\Column(name="load_type", type="string", length=64, nullable=true, options={"comment":"instantLoad, prefundLoad, promotionLoad"})
     */
    private $loadType;

    /**
     * @var string
     *
     * @ORM\Column(name="earn_type", type="string", length=64, nullable=true, options={"comment":"Bank Link, First $100 Spend, Spend Reward, Member Referral"})
     */
    private $earnType;

    /**
     * Set userCard
     *
     * @param UserCard $userCard
     *
     * @return UserCardLoad
     */
    public function setUserCard($userCard)
    {
        $this->userCard = $userCard;

        return $this;
    }

    /**
     * Get userCard
     *
     * @return UserCard
     */
    public function getUserCard()
    {
        return $this->userCard;
    }

    /**
     * Get feeStructure Element
     * @param string $elementName
     *
     * @return int
     */
    private function getFeeStructureElement(string $elementName)
    {
        $feeStructure = json_decode($this->getFeeStructure(), true);
        if (!$feeStructure) {
            return 0;
        }
        return array_key_exists($elementName, $feeStructure) ? $feeStructure[$elementName] : 0 ;
    }

    public function getDiscount()
    {
        $feeStructureArr = json_decode($this->getFeeStructure(), true);
        if (!$feeStructureArr) {
            return 0;
        }
        $discountArr = $feeStructureArr['discount'] ?? [];
        return $discountArr['totalFee'] ?? 0;
    }

    public function getDiscountText()
    {
        $discountArr = $this->getFeeStructureText('discount');
        $discount = '';
        if (is_array($discountArr)){
            if (array_key_exists('loadFeeText', $discountArr)
                && ($discountArr['loadFeeText'] !== '') ){
                $discount = 'Load fee : ' . $discountArr['loadFeeText'];
            }
            if (array_key_exists('membershipFeeText', $discountArr)
                && ($discountArr['membershipFeeText'] !== '') ){
                $discount = $discount . ' <br/>' . 'Membership fee : ' . $discountArr['membershipFeeText'];
            }
        }

        return $discount;
    }

    public function getTotalAmountUSD()
    {
        $totalAmount = (float)$this->getTotalAmount();
        $currency = $this->getUserCard()->getCurrency();
        return $totalAmount === 0 ? 0 : Money::convert($totalAmount, $currency, 'USD');
    }

    public function getTotalAmount()
    {
        return $this->getFeeStructureElement('totalAmount');
    }

    public function getReplacementFee()
    {
        return $this->getFeeStructureElement('replacementFee');
    }

    public function getUnloadFee()
    {
        return $this->getFeeStructureElement('unloadFee');
    }

    /**
     * Get loadAmount
     *
     * @param bool $returnInitialIfNull
     * @return int
     */
    public function getLoadAmount($returnInitialIfNull = false)
    {
        $loadAmount = $this->loadAmount;
        if ($returnInitialIfNull && null === $loadAmount) {
            return $this->getInitialAmount();
        }
        return $loadAmount;
    }

    public function getInitialAmountUSD()
    {
        return Money::convert($this->getInitialAmount(), $this->getInitialCurrency(), 'USD');
    }

    public function getDiscountUSD()
    {
        return Money::convert($this->getDiscount(), $this->getInitialCurrency(), 'USD');
    }

    /**
     * Set transaction
     *
     * @param Transaction $transaction
     *
     * @return UserCardLoad
     */
    public function setTransaction($transaction)
    {
        $this->transaction = $transaction;

        return $this;
    }

    /**
     * Get transaction
     *
     * @return Transaction
     */
    public function getTransaction()
    {
        return $this->transaction;
    }

    /**
     * Set status
     *
     * @param string $status
     *
     * @return UserCardLoad
     */
    public function setStatus($status)
    {
        $this->status = $status;

        return $this;
    }

    /**
     * Get status
     *
     * @return string
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * Set initialAmount
     *
     * @param integer $initialAmount
     *
     * @return UserCardLoad
     */
    public function setInitialAmount($initialAmount)
    {
        $this->initialAmount = (int)$initialAmount;

        return $this;
    }

    /**
     * Get initialAmount
     *
     * @return integer
     */
    public function getInitialAmount()
    {
        return $this->initialAmount;
    }

    /**
     * Set initialCurrency
     *
     * @param string $initialCurrency
     *
     * @return UserCardLoad
     */
    public function setInitialCurrency($initialCurrency)
    {
        $this->initialCurrency = $initialCurrency;

        return $this;
    }

    /**
     * Get initialCurrency
     *
     * @return string
     */
    public function getInitialCurrency()
    {
        return $this->initialCurrency;
    }

    /**
     * Set payCurrency
     *
     * @param string $payCurrency
     *
     * @return UserCardLoad
     */
    public function setPayCurrency($payCurrency)
    {
        $this->payCurrency = $payCurrency;

        return $this;
    }

    /**
     * Get payCurrency
     *
     * @return string
     */
    public function getPayCurrency()
    {
        if (!$this->payCurrency && $this->isImported()) {
            return $this->getInitialCurrency();
        }
        return $this->payCurrency;
    }

    /**
     * Set partner
     *
     * @param \CoreBundle\Entity\LoadPartner $partner
     *
     * @return UserCardLoad
     */
    public function setPartner(\CoreBundle\Entity\LoadPartner $partner = null)
    {
        $this->partner = $partner;

        return $this;
    }

    /**
     * Get partner
     *
     * @return \CoreBundle\Entity\LoadPartner
     */
    public function getPartner()
    {
        return $this->partner;
    }

    /**
     * Set method
     *
     * @param \CoreBundle\Entity\LoadMethod $method
     *
     * @return UserCardLoad
     */
    public function setMethod(\CoreBundle\Entity\LoadMethod $method = null)
    {
        $this->method = $method;

        return $this;
    }

    /**
     * Get method
     *
     * @return \CoreBundle\Entity\LoadMethod
     */
    public function getMethod()
    {
        return $this->method;
    }

    /**
     * Set error
     *
     * @param string $error
     *
     * @return UserCardLoad
     */
    public function setError($error)
    {
        $this->error = $error;

        return $this;
    }

    /**
     * Get error
     *
     * @return string
     */
    public function getError()
    {
        return $this->error;
    }

    /**
     * Set feeStructure
     *
     * @param string|array $feeStructure
     *
     * @return UserCardLoad
     */
    public function setFeeStructure($feeStructure)
    {
        if (is_string($feeStructure)) {
            $feeStructure = json_decode($feeStructure, true);
        }

        $fees = ['loadFee', 'membershipFee', 'replacementFee', 'unloadFee'];
        foreach ($fees as $fee) {
            if (isset($feeStructure[$fee])) {
                $amount = $feeStructure[$fee];
                $amount = Money::convert($amount, $this->getInitialCurrency() ?: 'USD', 'USD');
                $method = 'set' . ucfirst($fee) . 'USD';
                $this->$method($amount);

                // For searching:
                //
                // setLoadFeeUSD
                // setMembershipFeeUSD
                // setReplacementFeeUSD
                // setUnloadFeeUSD
            }
        }

        $this->feeStructure = json_encode($feeStructure);

        return $this;
    }

    /**
     * Get feeStructure
     *
     * @return string
     */
    public function getFeeStructure()
    {
        return $this->feeStructure;
    }

    /**
     * Set payAmount
     *
     * @param integer $payAmount
     *
     * @return UserCardLoad
     */
    public function setPayAmount($payAmount)
    {
        $this->payAmount = (int)$payAmount;

        $this->setPayAmountUSD(Money::convert($this->payAmount, $this->getPayCurrency(), 'USD'));

        return $this;
    }

    /**
     * Get payAmount
     *
     * @return integer
     */
    public function getPayAmount()
    {
        if (!$this->payAmount && $this->isImported()) {
            return $this->getInitialAmount();
        }
        return $this->payAmount;
    }

    /**
     * Set completedAt
     *
     * @param \DateTime $completedAt
     *
     * @return UserCardLoad
     */
    public function setCompletedAt($completedAt)
    {
        $this->completedAt = $completedAt;

        return $this;
    }

    /**
     * Get completedAt
     *
     * @return \DateTime
     */
    public function getCompletedAt()
    {
        $completedAt = $this->completedAt;
        if (!$completedAt && $this->getStatus() === self::STATUS_COMPLETED) {
            $completedAt = $this->getUpdatedAt();
            $this->setCompletedAt($completedAt)
                ->persist();
        }
        return $completedAt;
    }

    /**
     * Set loadInvoke
     *
     * @param \CoreBundle\Entity\ExternalInvoke $loadInvoke
     *
     * @return UserCardLoad
     */
    public function setLoadInvoke(\CoreBundle\Entity\ExternalInvoke $loadInvoke = null)
    {
        $this->loadInvoke = $loadInvoke;

        return $this;
    }

    /**
     * Get loadInvoke
     *
     * @return \CoreBundle\Entity\ExternalInvoke
     */
    public function getLoadInvoke()
    {
        return $this->loadInvoke;
    }

    /**
     * Set loadAmount
     *
     * @param integer $loadAmount
     *
     * @return UserCardLoad
     */
    public function setLoadAmount($loadAmount)
    {
        $this->loadAmount = $loadAmount;

        $this->setLoadAmountUSD(Money::convert($this->loadAmount, $this->getInitialCurrency() ?: 'USD', 'USD'));

        return $this;
    }

    /**
     * Set type
     *
     * @param string $type
     *
     * @return UserCardLoad
     */
    public function setType($type)
    {
        $this->type = $type;

        return $this;
    }

    /**
     * Get type
     *
     * @param bool $human
     * @return string
     */
    public function getType($human = false)
    {
        $type = $this->type;
        if ($human) {
            $type = s($type)->humanize();
        }
        return $type;
    }

    /**
     * Set loadStatus
     *
     * @param string $loadStatus
     *
     * @param bool $sendEmail
     * @return UserCardLoad
     * @throws \Exception
     */
    public function setLoadStatus($loadStatus, $sendEmail = true)
    {
        if ($sendEmail && $this->loadStatus !== $loadStatus && $loadStatus) {
            $const = Email::class . '::TEMPLATE_LOAD_CARD_PAYMENT_' . strtoupper($loadStatus);
            if (!defined($const) || in_array($loadStatus, [
                self::LOAD_STATUS_PENDING,
                self::LOAD_STATUS_CONFIRMED,
                self::LOAD_STATUS_ERROR,
            ])) {
                goto operation;
            }
            if (Util::meta($this, 'silentLoad')) {
                goto operation;
            }
            $template = constant($const);
            $method = $this->getMethod();
            if ($template && $method) {
                $platform = $this->getUserCard()->getCardProgram()->getPlatform();
                $url = $platform->host();
                if (null !== $this->getReceivedAmount()) {
                    $amount = Money::format($this->getReceivedAmount(), $this->getReceivedCurrency());
                } else if (null !== $this->getPayAmount()) {
                    $amount = Money::format($this->getPayAmount(), $this->getPayCurrency());
                } else {
                    $amount = Money::format($this->getInitialAmount(), $this->getInitialCurrency());
                }

                if ($loadStatus !== self::LOAD_STATUS_LOADED && $this->getType() === self::TYPE_LOAD_CARD) {
                    $url .= '/consumer-register/card-load?step=5&load=' . $this->getId();
                }

                $loadAmount = $this->getLoadAmount();
                if ($loadStatus === self::LOAD_STATUS_RECEIVED && !$loadAmount) {
                    Service::log('Empty load amount sent in load received email! Now recalculate!', [
                        'load' => $this->getId(),
                        'load_status' => $this->getLoadStatus(),
                    ], 'error');
                    $this->updateLoadAmountWhenReceived();
                    $loadAmount = $this->getLoadAmount();
                }
                $loadAmount = Money::format($loadAmount, $this->getInitialCurrency());

                $uc = $this->getUserCard();
                $params = [
                    'card' => $uc->getCardTypeName(),
                    'amount' => $amount,
                    'load_amount' => $loadAmount,
                    'method' => $this->getMethod()->getName(),
                    'action_url' => $url,
                ];
                if ($loadStatus === self::LOAD_STATUS_INITIATED) {
                    $transaction = $this->getTransaction();
                    $partner = $this->getPartner();
                    if ($transaction && $partner && $partner->is(LoadPartner::GLOBAL_COLLECT)) {
                        $response = json_decode($transaction->getResponse());
                        $merchantAction = $response->merchantAction;
                        if ($merchantAction->actionType === 'SHOW_INSTRUCTIONS') {
                            $renderData = [];
                            $renderData['method'] = $this->getMethod();
                            $renderData['instruction'] = GlobalCollectService::formatShowData($merchantAction->showData);
                            $params['payment_detail'] = Util::twig()->render('@Portal/Register/load-card-step4-instruction.html.twig', $renderData);
                        }
                    }
                }

                if ($loadStatus !== self::LOAD_STATUS_INITIATED) {
                    // It's possible that the user card had been deleted.
                    try {
                        Email::sendWithTemplateToUser($uc->getUser(), $template, $params, $uc->getCard()->getCardProgram());
                    } catch(\Exception $exception) {
                        Service::log('Send email when load status changes' . $exception->getMessage()
                            , [], 'error');
                    }
                }
            }
        }

        operation:
        $this->loadStatus = $loadStatus;

        if ($loadStatus && !$this->getInitializedAt()) {
            $this->setInitializedAt(new \DateTime());
        }

        $this->updateOrder();
        $this->fillCommission();
        RevenueService::updateCostAndRevenue($this);

        $this->getUserCard()->getUser()->updateLoadDates($loadStatus, false);

        return $this;
    }

    /**
     * Get loadStatus
     *
     * @return string
     */
    public function getLoadStatus()
    {
        return $this->loadStatus;
    }

    /**
     * Set wx
     *
     * @param string $wx
     *
     * @return UserCardLoad
     */
    public function setWx($wx)
    {
        $this->wx = $wx;

        $this->setWxParsedAt(new \DateTime());

        return $this;
    }

    /**
     * Get wx
     *
     * @return string
     */
    public function getWx()
    {
        return $this->wx;
    }

    /**
     * Set meta
     *
     * @param string $meta
     *
     * @return UserCardLoad
     */
    public function setMeta($meta)
    {
        $this->meta = $meta;

        return $this;
    }

    /**
     * Get meta
     *
     * @return string
     */
    public function getMeta()
    {
        return $this->meta;
    }

    /**
     * Set transactionNo
     *
     * @param string $transactionNo
     *
     * @return UserCardLoad
     */
    public function setTransactionNo($transactionNo)
    {
        $this->transactionNo = $transactionNo;

        return $this;
    }

    /**
     * Set fvNo
     *
     * @param string $fvNo
     *
     * @return UserCardLoad
     */
    public function setFvNo($fvNo)
    {
        $this->fvNo = $fvNo;

        return $this;
    }

    /**
     * Get fvNo
     *
     * @return string
     */
    public function getFvNo()
    {
        return $this->fvNo;
    }

    /**
     * Set receivedAmount
     *
     * @param integer $receivedAmount
     *
     * @return UserCardLoad
     */
    public function setReceivedAmount($receivedAmount)
    {
        $this->receivedAmount = (int)$receivedAmount;

        $this->setReceivedAmountUSD(Money::convert($this->receivedAmount, $this->getReceivedCurrency(), 'USD'));

        if ( !$this->getReceivedAt() ) {
            $this->setReceivedAt(new \DateTime());
        }

        if (null === $this->receivedLocalAmount) {
            $this->setReceivedLocalAmount($this->receivedAmount);
        }

        return $this;
    }

    /**
     * Get receivedAmount
     *
     * @return integer
     */
    public function getReceivedAmount()
    {
        return $this->receivedAmount;
    }

    /**
     * Set receivedCurrency
     *
     * @param string $receivedCurrency
     *
     * @return UserCardLoad
     */
    public function setReceivedCurrency($receivedCurrency)
    {
        $this->receivedCurrency = $receivedCurrency;

        if (null === $this->receivedLocalCurrency) {
            $this->setReceivedLocalCurrency($receivedCurrency);
        }

        return $this;
    }

    /**
     * Get receivedCurrency
     *
     * @return string
     */
    public function getReceivedCurrency()
    {
        return $this->receivedCurrency;
    }

    /**
     * Set loadAt
     *
     * @param \DateTime $loadAt
     *
     * @return UserCardLoad
     */
    public function setLoadAt($loadAt)
    {
        $this->loadAt = $loadAt;

        if ($loadAt) {
            $uc = $this->getUserCard();
            $uc->setBalanceReminded(null);
            Util::em()->persist($uc);

            $receivedAt = $this->getReceivedAt();
            if (!$receivedAt) {
                $parsedAt = $this->getWxParsedAt();
                if ($parsedAt) {
                    $this->setReceivedAt($parsedAt);
                } else {
                    $this->setReceivedAt($loadAt);
                }
            }
        }

        return $this;
    }

    /**
     * Get loadAt
     *
     * @return \DateTime
     */
    public function getLoadAt()
    {
        return $this->loadAt;
    }

    /**
     * Set serverKey
     *
     * @param string $serverKey
     *
     * @return UserCardLoad
     */
    public function setServerKey($serverKey)
    {
        $this->serverKey = $serverKey;

        return $this;
    }

    /**
     * Get serverKey
     *
     * @return string
     */
    public function getServerKey()
    {
        return $this->serverKey;
    }

    /**
     * Get payment id
     *
     * @return string
     */
    public function getPaymentId()
    {
        return $this->paymentId;
    }

    /**
     * Set paymentId
     *
     * @param string $paymentId
     *
     * @return UserCardLoad
     */
    public function setPaymentId($paymentId)
    {
        $this->paymentId = $paymentId;

        return $this;
    }

    /**
     * Set paymentReference
     *
     * @param string $paymentReference
     *
     * @return UserCardLoad
     */
    public function setPaymentReference($paymentReference)
    {
        $this->paymentReference = $paymentReference;

        return $this;
    }

    /**
     * Get paymentReference
     *
     * @return string
     */
    public function getPaymentReference()
    {
        return $this->paymentReference;
    }

    /**
     * Set currencyRate
     *
     * @param string $currencyRate
     *
     * @return UserCardLoad
     */
    public function setCurrencyRate($currencyRate)
    {
        $this->currencyRate = $currencyRate;

        return $this;
    }

    /**
     * Get currencyRate
     *
     * @return string
     */
    public function getCurrencyRate()
    {
        return $this->currencyRate;
    }

    /**
     * Set reload
     *
     * @param boolean $reload
     *
     * @return UserCardLoad
     */
    public function setReload($reload)
    {
        $this->reload = $reload;

        return $this;
    }

    /**
     * Get reload
     *
     * @return boolean
     */
    public function getReload()
    {
        return $this->reload;
    }

    public function getMembershipFeeUSD()
    {
        return $this->membershipFeeUSD;
    }

    /**
     * Set membershipFeeUSD
     *
     * @param integer $membershipFeeUSD
     *
     * @return UserCardLoad
     */
    public function setMembershipFeeUSD($membershipFeeUSD)
    {
        $this->membershipFeeUSD = $membershipFeeUSD;

        return $this;
    }

    /**
     * @return float|int
     */
    public function getPayAmountUSD()
    {
        return $this->payAmountUSD;
    }

    /**
     * Set payAmountUSD
     *
     * @param integer $payAmountUSD
     *
     * @return UserCardLoad
     */
    public function setPayAmountUSD($payAmountUSD)
    {
        $this->payAmountUSD = $payAmountUSD;

        return $this;
    }

    public function getReceivedAmountUSD()
    {
        return $this->receivedAmountUSD;
    }

    /**
     * Set receivedAmountUSD
     *
     * @param integer $receivedAmountUSD
     *
     * @return UserCardLoad
     */
    public function setReceivedAmountUSD($receivedAmountUSD)
    {
        $this->receivedAmountUSD = $receivedAmountUSD;

        return $this;
    }

    public function getLoadAmountUSD()
    {
        return $this->loadAmountUSD;
    }

    /**
     * Set loadAmountUSD
     *
     * @param integer $loadAmountUSD
     *
     * @return UserCardLoad
     */
    public function setLoadAmountUSD($loadAmountUSD)
    {
        $this->loadAmountUSD = $loadAmountUSD;

        return $this;
    }

    /**
     * Set wxParsedAt
     *
     * @param \DateTime $wxParsedAt
     *
     * @return UserCardLoad
     */
    public function setWxParsedAt($wxParsedAt)
    {
        $this->wxParsedAt = $wxParsedAt;

        return $this;
    }

    /**
     * Get wxParsedAt
     *
     * @return \DateTime
     */
    public function getWxParsedAt()
    {
        return $this->wxParsedAt;
    }

    /**
     * Set membershipCommissionUSD
     *
     * @param integer $membershipCommissionUSD
     *
     * @return UserCardLoad
     */
    public function setMembershipCommissionUSD($membershipCommissionUSD)
    {
        $this->membershipCommissionUSD = $membershipCommissionUSD;

        return $this;
    }

    /**
     * Get membershipCommissionUSD
     *
     * @return integer
     */
    public function getMembershipCommissionUSD()
    {
        return $this->membershipCommissionUSD;
    }

    /**
     * Set loadCommissionUSD
     *
     * @param integer $loadCommissionUSD
     *
     * @return UserCardLoad
     */
    public function setLoadCommissionUSD($loadCommissionUSD)
    {
        $this->loadCommissionUSD = $loadCommissionUSD;

        return $this;
    }

    /**
     * Get loadCommissionUSD
     *
     * @return integer
     */
    public function getLoadCommissionUSD()
    {
        return $this->loadCommissionUSD;
    }

    /**
     * Set receivedAt
     *
     * @param \DateTime $receivedAt
     *
     * @return UserCardLoad
     */
    public function setReceivedAt($receivedAt)
    {
        $this->receivedAt = $receivedAt;

        return $this;
    }

    /**
     * Get receivedAt
     *
     * @return \DateTime
     */
    public function getReceivedAt()
    {
        return $this->receivedAt;
    }

    /**
     * Set userCardOrder
     *
     * @param integer $userCardOrder
     *
     * @return UserCardLoad
     */
    public function setUserCardOrder($userCardOrder)
    {
        $this->userCardOrder = $userCardOrder;

        return $this;
    }

    /**
     * Get userCardOrder
     *
     * @return integer
     */
    public function getUserCardOrder()
    {
        return $this->userCardOrder;
    }

    /**
     * Set replacementFeeUSD
     *
     * @param integer $replacementFeeUSD
     *
     * @return UserCardLoad
     */
    public function setReplacementFeeUSD($replacementFeeUSD)
    {
        $this->replacementFeeUSD = $replacementFeeUSD;

        return $this;
    }

    /**
     * Get replacementFeeUSD
     *
     * @return integer
     */
    public function getReplacementFeeUSD()
    {
        return $this->replacementFeeUSD;
    }

    /**
     * Set loadFeeUSD
     *
     * @param integer $loadFeeUSD
     *
     * @return UserCardLoad
     */
    public function setLoadFeeUSD($loadFeeUSD)
    {
        $this->loadFeeUSD = $loadFeeUSD;

        return $this;
    }

    /**
     * Get loadFeeUSD
     *
     * @return integer
     */
    public function getLoadFeeUSD()
    {
        return $this->loadFeeUSD;
    }

    /**
     * Set unloadFeeUSD
     *
     * @param integer $unloadFeeUSD
     *
     * @return UserCardLoad
     */
    public function setUnloadFeeUSD($unloadFeeUSD)
    {
        $this->unloadFeeUSD = $unloadFeeUSD;

        return $this;
    }

    /**
     * Get unloadFeeUSD
     *
     * @return integer
     */
    public function getUnloadFeeUSD()
    {
        return $this->unloadFeeUSD;
    }

    /**
     * Set affiliatePayment
     *
     * @param \CoreBundle\Entity\AffiliatePayment $affiliatePayment
     *
     * @return UserCardLoad
     */
    public function setAffiliatePayment(\CoreBundle\Entity\AffiliatePayment $affiliatePayment = null)
    {
        $this->affiliatePayment = $affiliatePayment;

        return $this;
    }

    /**
     * Get affiliatePayment
     *
     * @return \CoreBundle\Entity\AffiliatePayment
     */
    public function getAffiliatePayment()
    {
        return $this->affiliatePayment;
    }

    /**
     * Set initializedAt
     *
     * @param \DateTime $initializedAt
     *
     * @return UserCardLoad
     */
    public function setInitializedAt($initializedAt)
    {
        $this->initializedAt = $initializedAt;

        return $this;
    }

    /**
     * Get initializedAt
     *
     * @return \DateTime
     */
    public function getInitializedAt()
    {
        return $this->initializedAt;
    }

    /**
     * Set receivedLocalAmount
     *
     * @param integer $receivedLocalAmount
     *
     * @return UserCardLoad
     */
    public function setReceivedLocalAmount($receivedLocalAmount)
    {
        $this->receivedLocalAmount = $receivedLocalAmount;

        return $this;
    }

    /**
     * Get receivedLocalAmount
     *
     * @return integer
     */
    public function getReceivedLocalAmount()
    {
        return $this->receivedLocalAmount;
    }

    /**
     * Set receivedLocalCurrency
     *
     * @param string $receivedLocalCurrency
     *
     * @return UserCardLoad
     */
    public function setReceivedLocalCurrency($receivedLocalCurrency)
    {
        $this->receivedLocalCurrency = $receivedLocalCurrency;

        return $this;
    }

    /**
     * Get receivedLocalCurrency
     *
     * @return string
     */
    public function getReceivedLocalCurrency()
    {
        return $this->receivedLocalCurrency;
    }
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->refunds = new ArrayCollection();
    }

    /**
     * Add refund
     *
     * @param \CoreBundle\Entity\UserCardRefund $refund
     *
     * @return UserCardLoad
     */
    public function addRefund(\CoreBundle\Entity\UserCardRefund $refund)
    {
        $this->refunds[] = $refund;

        return $this;
    }

    /**
     * Remove refund
     *
     * @param \CoreBundle\Entity\UserCardRefund $refund
     */
    public function removeRefund(\CoreBundle\Entity\UserCardRefund $refund)
    {
        $this->refunds->removeElement($refund);
    }

    /**
     * Get refunds
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getRefunds()
    {
        return $this->refunds;
    }

    /**
     * Set cost
     *
     * @param integer $cost
     *
     * @return UserCardLoad
     */
    public function setCost($cost)
    {
        $this->cost = $cost;

        return $this;
    }

    /**
     * Get cost
     *
     * @return integer
     */
    public function getCost()
    {
        return $this->cost;
    }

    /**
     * Set revenue
     *
     * @param integer $revenue
     *
     * @return UserCardLoad
     */
    public function setRevenue($revenue)
    {
        $this->revenue = $revenue;

        return $this;
    }

    /**
     * Get revenue
     *
     * @return integer
     */
    public function getRevenue()
    {
        return $this->revenue;
    }

    /**
     * Set promoCode
     *
     * @param PromotionCode $promoCode
     *
     * @return UserCardLoad
     */
    public function setPromoCode($promoCode)
    {
        $this->promoCode = $promoCode;

        return $this;
    }

    /**
     * Get promoCode
     *
     * @return PromotionCode
     */
    public function getPromoCode()
    {
        return $this->promoCode;
    }

    /**
     * Set reward
     * @param SpendrReward $reward
     * @return UserCardLoad
     */
    public function setReward($reward)
    {
        $this->reward = $reward;

        return $this;
    }

    /**
     * Get reward
     * @return SpendrReward
     */
    public function getReward()
    {
        return $this->reward;
    }

    /**
     * Set referConfig
     * @param SpendrReferConfig $config
     * @return UserCardLoad
     */
    public function setReferConfig($config)
    {
        $this->referConfig = $config;

        return $this;
    }

    /**
     * Get referConfig
     * @return SpendrReferConfig
     */
    public function getReferConfig()
    {
        return $this->referConfig;
    }

    /**
     * Set promoType
     * @param string $value
     * @return UserCardLoad
     */
    public function setPromoType($value)
    {
        $this->promoType = $value;

        return $this;
    }

    /**
     * Get promoType
     * @return string|null
     */
    public function getPromoType()
    {
        return $this->promoType;
    }

    /**
     * Set invalidData
     * @param boolean $value
     * @return UserCardLoad
     */
    public function setInvalidData($value)
    {
        $this->invalidData = $value;

        return $this;
    }

    /**
     * Get invalidData
     * @return boolean|null
     */
    public function getInvalidData()
    {
        return $this->invalidData;
    }

    /**
     * Set loadType
     * @param string $value
     * @return UserCardLoad
     */
    public function setLoadType($value)
    {
        $this->loadType = $value;

        return $this;
    }

    /**
     * Get loadType
     * @return string|null
     */
    public function getLoadType()
    {
        return $this->loadType;
    }

    /**
     * Set earnType
     * @param string $value
     * @return UserCardLoad
     */
    public function setEarnType($value)
    {
        $this->earnType = $value;

        return $this;
    }

    /**
     * Get earnType
     * @return string|null
     */
    public function getEarnType()
    {
        return $this->earnType;
    }
}
