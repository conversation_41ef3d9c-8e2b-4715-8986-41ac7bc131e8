<?php

namespace CoreBundle\Entity;

use ApiB<PERSON>le\Entity\ApiEntityInterface;
use Carbon\Carbon;
use CoreBundle\Utils\Util;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\ORM\NonUniqueResultException;
use FaasBundle\Services\BOTM\BotmAPI;
use FaasBundle\Services\ProcessorHub;
use J<PERSON>\Serializer\Annotation as Serializer;
use SalexUserBundle\Entity\User;
use TransferMexBundle\Services\IntermexRemittanceService;
use TransferMexBundle\Services\MemberService;
use TransferMexBundle\Services\RapydDisburseService;
use TransferMexBundle\Services\UniTellerAPI;
use TransferMexBundle\Services\UniTellerRemittanceService;
use TransferMexBundle\TransferMexBundle;

/**
 * Transfer
 *
 * @ORM\Table(name="transfer", indexes={
 *     @ORM\Index(name="partner_idx", columns={"partner", "partner_id"}),
 *     @ORM\Index(columns={"status"}),
 *     @ORM\Index(columns={"source_agent_number"}),
 *     @ORM\Index(columns={"payout_to_base"}),
 * })
 * @ORM\Entity(repositoryClass="CoreBundle\Repository\TransferRepository")
 */
class Transfer extends BaseEntity implements ApiEntityInterface
{
    public const PARTNER_RAPYD = 'rapyd';
    public const PARTNER_UNITELLER = 'uniteller';
    public const PARTNER_BOTM = 'botm';
    public const PARTNER_INTERMEX = 'intermex';

    public const PARTNERS = [
        self::PARTNER_RAPYD,
        self::PARTNER_UNITELLER,
        self::PARTNER_BOTM,
        self::PARTNER_INTERMEX
    ];

    public const STATUS_PENDING = 'Pending'; // draft
    public const STATUS_CONFIRMATION = 'Confirmation'; // need to confirm the FX rate
    public const STATUS_QUEUED = 'Queued'; // queued due to insufficient balance in Rapyd Wallet
    public const STATUS_PROCESSING = 'Processing'; // queued and cannot be canceled
    public const STATUS_CREATED = 'Created'; // created payout but not confirmed received, for instance, Cash Payout
    public const STATUS_COMPLETED = 'Completed'; // received
    public const STATUS_CANCELED = 'Canceled';
    public const STATUS_ERROR = 'Error';
    public const STATUS_EXPIRED = 'Expired';
    public const STATUS_HOLD = 'Hold';
    public const STATUS_DECLINE = 'Decline';
    public const STATUS_DECLINED = 'Declined';
    public const STATUS_RETURNED = 'Returned';
    public const STATUS_SEND = 'Send'; // send pickup code

    // UniTeller transfer status in
    public const UNITELLER_STATUS_HOLD = 'HOLD'; // create and waite uniteller confirm, we can't get the cash pickup code
    public const UNITELLER_STATUS_PAYABLE = 'PAYABLE'; // UniTeller confirm and we can withdraw cash
    public const UNITELLER_STATUS_PAID = 'PAID'; // The transfer is completed
    public const UNITELLER_STATUS_CANCELED = 'CANCEL';
    public const UNITELLER_STATUS_CANCELPENDING = 'CANCELPENDING';
    public const UNITELLER_STATUS_REFUND = 'REFUND';

    // Intermex status ID and desc
    // 1 In Process
    // 2 Ready To Send
    // 3 Sent
    // 4 paid
    // 5 On Hold By Payer
    // 6 Canceled
    // 7 On Hold By Compliance
    public const INTERMEX_STATUS_TEST = [
      1 => 'Created',
      2 => 'Created',
      3 => 'Created',
      4 => 'Completed',
      5 => 'Created',
      6 => 'Canceled',
      7 => 'Created'
    ];
    public const INTERMEX_STATUS_LIST = [
      1 => 'Created',
      2 => 'Created',
      3 => 'Created',
      4 => 'Created',
      5 => 'Completed',
      6 => 'Canceled',
      7 => 'Canceled',
      8 => 'Canceled',
      9 => 'Canceled',
      10 => 'Canceled',
      11 => 'Created'
    ];

    public const INTERMEX_INTERNAL_STATUS_LIST = [
      1 => 'In process',
      2 => 'Info Pending',
      3 => 'Waiting to be charged to sender',
      4 => 'Available to pick up',
      5 => 'Paid',
      6 => 'Canceled. Pending refund',
      7 => 'Canceled and refunded',
      8 => 'Cancellation in progress',
      9 => 'Waiting original wire to be cancelled',
      10 => 'Void not charged',
      11 => 'In process no info pending'
    ];

    public const STATUSES_SENT = [
        self::STATUS_HOLD,
        self::STATUS_CREATED,
        self::STATUS_COMPLETED,
    ];

    public const STATUSES_ERROR = [
        self::STATUS_ERROR,
        self::STATUS_RETURNED,
        self::STATUS_DECLINE,
        self::STATUS_DECLINED,
    ];

    public const STATUSES_RAW_ERROR = [
        self::STATUS_ERROR,
        self::STATUS_RETURNED,
    ];

    public const PAYOUT_TYPE_BANK = 'bank';
    public const PAYOUT_TYPE_CASH = 'cash';

    public static function getPartnerName($key)
    {
        if ($key === 'rapyd') {
            return 'Rapyd';
        }
        if ($key === 'uniteller') {
            return 'UniTeller';
        }
        if ($key === self::PARTNER_BOTM) {
            return 'BOTM';
        }
        return ucfirst($key);
    }

    public static function isSentStatus($status)
    {
        return in_array($status, self::STATUSES_SENT);
    }

    /**
     * @param $id
     *
     * @return Transfer|null
     */
    public static function find($id)
    {
        return Util::em()->getRepository(self::class)->find($id);
    }

    public static function findByRapydId($id)
    {
        return self::findByPartnerId($id, self::PARTNER_RAPYD);
    }

    public static function findByUniTellerId($id)
    {
        return self::findByPartnerId($id, self::PARTNER_UNITELLER);
    }

    public static function findByIntermexId($id)
    {
        return self::findByPartnerId($id, self::PARTNER_INTERMEX);
    }

    /**
     * @param string $id
     * @param string|null $partner
     *
     * @return Transfer|null
     */
    public static function findByPartnerId($id, string $partner = null)
    {
        if (!$id) {
            return null;
        }
        if (!$partner) {
            if (Util::startsWith($id, 'payout_')) {
                $partner = self::PARTNER_RAPYD;
            }
        }
        $where = [
            'partnerId' => $id,
        ];
        if ($partner) {
            $where['partner'] = $partner;
        }
        $all = Util::em()->getRepository(self::class)->findBy($where, limit: 2);
        if (count($all) > 1) {
            throw new NonUniqueResultException('Found more than one transfer with the partner ID ' . $id);
        }
        return $all[0] ?? null;
    }

    /**
     * @param string $partner
     * @param string $transferStatus
     *
     * @return String|null
     */
    public static function getTransfersByPartnerForNoSettle($partner, $transferStatus, $total = true)
    {
        $q = Util::em()->getRepository(self::class)
                         ->createQueryBuilder('t')
                         ->where(Util::expr()->eq('t.partner', ':partner'))
                         ->andWhere(Util::expr()->isNull('t.settlement'))
                         ->andWhere(Util::expr()->eq('t.status', ':status'))
                         ->setParameter('partner', $partner)
                         ->setParameter('status', $transferStatus);
        if ($total) {
            $res = $q->select('sum(t.sendAmount)')
                     ->getQuery()
                     ->getSingleScalarResult();
        } else {
            $res = $q->select('count(t)')
                    ->getQuery()
                     ->getSingleScalarResult();
        }

        return $res;
    }

    /**
     * @param string $control
     * @param string $partner
     *
     * @return Transfer|null
     */
    public static function findByIntermexControl($control, string $partner = self::PARTNER_INTERMEX)
    {
        $res = Util::em()->getRepository(self::class)
                         ->createQueryBuilder('t')
                         ->where(Util::expr()->eq('t.partner', ':partner'))
                         ->andWhere(Util::expr()->like('t.meta', ':control'))
                         ->setParameter('partner', $partner)
                         ->setParameter('control', '%"control":' . $control .'%')
                         ->getQuery()
                         ->getResult();
        return !empty($res) ? $res[0] : null;
    }


    public static function parsePayoutType($type)
    {
        if (Util::startsWith($type, 'Bank Transfer')) {
            return 'bank';
        }
        if (Util::startsWith($type, 'Cash Pickup')) {
            return 'cash';
        }
        return $type;
    }

    public static function isLaterStatus($newStatus, $oldStatus)
    {
        $statuses = [
            self::STATUS_PENDING => 0,
            self::STATUS_CONFIRMATION => 10,
            self::STATUS_HOLD => 15,
            self::STATUS_CREATED => 20,
            self::STATUS_CANCELED => 30,
            self::STATUS_ERROR => 30,
            self::STATUS_EXPIRED => 30,
            self::STATUS_DECLINE => 30,
            self::STATUS_DECLINED => 30,
            self::STATUS_COMPLETED => 100,
            self::STATUS_RETURNED => 110,
        ];
        $new = $statuses[$newStatus] ?? -1;
        $old = $statuses[$oldStatus] ?? -1;
        return $new > $old;
    }

    public function getParsedPartnerName()
    {
        return self::getPartnerName($this->getPartner());
    }

    public function isSent()
    {
        return self::isSentStatus($this->getStatus());
    }

    public function getPayoutTypeName()
    {
        $name = $this->getPayoutType();
        if ($name === 'bank') {
            return 'Bank Transfer';
        }
        if ($name === 'cash') {
            return 'Cash Pickup';
        }
        return $name;
    }

    public function getPayoutMethodTypeName()
    {
        $type = $this->getPayoutMethodType();
        $cache = RapydDisburseService::getCachedMethodType($type);
        if (!empty($cache['name'])) {
            return $cache['name'];
        }
        if ( $this->isUniTeller()) {
          $nameList = UniTellerRemittanceService::CASH_PICKUP_NAME_LIST;
          return isset($nameList[$type]) ? $nameList[$type] : $type;
        }
        if ($this->isIntermex()) {
          $nameList = IntermexRemittanceService::getCachedFilteredMethodTypes();
          $key = $this->getPayoutType() . '-' . $type ;
          return isset($nameList[$key]) ? $nameList[$key]['name'] : $type;
        }
        return $type;
    }

    public function isRapyd()
    {
        return $this->getPartner() === self::PARTNER_RAPYD;
    }

    public function isUniTeller()
    {
        return $this->getPartner() === self::PARTNER_UNITELLER;
    }

    public function isIntermex()
    {
        return $this->getPartner() === self::PARTNER_INTERMEX;
    }

    public function isCashPickup()
    {
        return $this->getPayoutType() === self::PAYOUT_TYPE_CASH;
    }

    public function isSandbox()
    {
        return Util::meta($this, '__sandbox') === true;
    }

    public function isDeducted()
    {
        return Util::meta($this, 'deductedFromMember') === true;
    }

    public function setDeducted($deducted = true)
    {
        return Util::updateMeta($this, [
            'deductedFromMember' => $deducted,
        ]);
    }

    public function isRefunded()
    {
        return Util::meta($this, 'refundedToMember') === true;
    }

    public function setRefunded($refunded = true)
    {
        Util::updateMeta($this, [
            'refundedToMember' => $refunded,
        ]);
    }

    public function isPendingToRecheck()
    {
        return Util::meta($this, 'unhandledRapydError');
    }

    public function isError()
    {
        return in_array($this->getStatus(), self::STATUSES_RAW_ERROR);
    }

    public function isUnderReviewByRapyd()
    {
        if ( ! $this->isError()) {
            return false;
        }
        $error = $this->isPendingToRecheck();
        if (!$error) {
            return false;
        }
        return str_contains($error, 'REQUEST_UNDER_REVIEW');
    }

    public function isFailedByRapydAndDidNotRefundDirectly()
    {
        if ( ! $this->isError()) {
            return false;
        }
        $error = $this->isPendingToRecheck();
        if (!$error) {
            return false;
        }
        return Util::containsSubString($error, [
            'ERROR_GET_USER',
            'REQUEST_DECLINED',
            'REQUEST_TIMED_OUT',
            'REQUEST_UNDER_REVIEW',
            'SERVICE_TEMPORARILY_UNAVAILABLE',
            '502 Bad Gateway',
            '504 Gateway Timeout',
            'SSL connection timeout',
        ]);
    }

    public function isFailedToDeductBalance()
    {
        return Util::startsWith($this->getError(), 'Failed to deduct transfer amount from the card');
    }

    public function getTotalAmount()
    {
        return ($this->getSendAmount() ?? 0) + ($this->getTransferFee() ?? 0);
    }

    public function getFilteredError()
    {
        $common = 'Failed to create payout. ';
        $err = $this->getError();
        if (!$err) {
            return '';
        }
        if (strpos($err, 'GENERAL_ERROR') !== false) {
            return $common . 'Please check the recipient\'s account number and other info, and try again later.';
        }
        if (strpos($err, 'ERROR_BANK_IDENTIFIER_MISMATCH') !== false) {
            return 'The bank portion of the account number does not match the bank identifier. The request was rejected. Corrective action: Verify the account number and bank identifier and retry.';
        }
        if (strpos($err, 'ERROR_CREATE_PAYOUT') !== false) {
            return 'A required field was missing or a field had an incorrect value. The request was rejected. Corrective action: Verify the account number and other info and retry.';
        }
        if (strpos($err, 'ERROR_PAYOUT_AMOUNT_NOT_WITHIN_RANGE_OF_PAYOUT_METHOD_TYPE') !== false) {
            return 'The amount provided was not within the range of the payout method type. The request was rejected. Corrective action: Ensure that the amount provided is within the range of the payout method type.';
        }
        if (strpos($err, 'ERROR_REQUEST_TIMED_OUT') !== false) {
            return 'The request have timed out. Please check the payout status and try again later if it failed.';
        }
        if (strpos($err, 'NOT_ENOUGH_FUNDS') !== false) {
            return 'No sufficient funds. The request was rejected. Corrective action: Contact support.';
        }
        if (strpos($err, 'ERROR_UNSUPPORTED_PAYOUT_CURRENCY_OR_COUNTRY') !== false) {
            return 'The request attempted a payout operation, but there was a problem with the country or currency, or a mismatch in entity types. The request was rejected. Corrective action: Contact support.';
        }
        if (strpos($err, 'ERROR_CHECK_DIGIT_NOT_VALID') !== false) {
            return 'The request attempted an operation with an alphanumeric identifier, but the check digit was not valid. The request was rejected. Corrective action: Verify the identifier and retry.';
        }
        return str_replace('Error occurred when call the rapyd API: ', $common, $err);
    }

    public function getRealSendAmount()
    {
        $amount = $this->getSendAmount() ?? 0;
        if ($this->getPartner() === self::PARTNER_UNITELLER) {
            return $amount;
//          $markUps = Util::meta($this, 'fxRateMarkup') ?? [
//            UniTellerRemittanceService::RATE_MARK_UP_PARTNER,
//            UniTellerRemittanceService::RATE_MARK_UP_PLATFORM,
//          ];
        }
        if ($this->getPartner() === self::PARTNER_INTERMEX) {
          $markUps = Util::meta($this, 'fxRateMarkup') ?? [
            IntermexRemittanceService::getPartnerRateMarkup(),
            IntermexRemittanceService::getPlatformRateMarkup(),
          ];
        } else {
          $markUps = Util::meta($this, 'fxRateMarkup') ?? [
            RapydDisburseService::getPartnerRateMarkup(),
            RapydDisburseService::getPlatformRateMarkup(),
          ];
        }
        $rate = 1;
        foreach ($markUps as $markUp) {
            $rate -= $markUp;
        }
        return (int)round($amount * $rate);
    }

    public function getSettleAmount()
    {
        return $this->getRealSendAmount() + ($this->getCost() ?? 0);
    }

    public function getActualRecipientDetails()
    {
        $recipient = $this->getRecipient();
        $rapyd = $this->isRapyd();
        $uniTeller = $this->isUniTeller();
        $intermex = $this->isIntermex();
        if (($rapyd || $uniTeller || $intermex) && $recipient->inTeam(Role::ROLE_TRANSFER_MEX_RECIPIENT)) {
            return $recipient->toTransferMexApiArray('recipient', $this);
        }
        return $recipient->toApiArray();
    }

    /**
     * @param bool $extra
     *
     * @return array
     */
    public function toApiArray(bool $extra = FALSE): array
    {
        $payoutOptions = Util::meta($this, 'payoutOptions');
        // change error for UniTeller transfer
        $errorMessage = $this->getFilteredError();
        if (UniTellerAPI::isOopsError($errorMessage)) {
          $errorMessage = 'Sorry that the operation failed due to an error (code: 06). We are investigating. Please reach out to our support if it failed constantly.';
        }
        $status = $this->getStatus();
        if ($status == Transfer::STATUS_CANCELED && Util::meta($this, 'cancelByIntermex')) {
          $status = Transfer::STATUS_ERROR;
          $statusInfo = Util::meta($this, 'IntermexStatus');
          $errorMessage = 'Canceled by the bank';
          $errorMessage .= $statusInfo && isset($statusInfo['cancelationReasonDescription']) ? ' with the reason: ' . $statusInfo['cancelationReasonDescription'] : '';
        }
        $intermexTransaction = Util::meta($this, 'intermexTransaction');
        $all = [
            'id' => $this->getId(),
            'status' => $status,
            'payoutType' => $this->getPayoutType(),
            'payoutMethodType' => $this->getPayoutMethodType(),
            'sendAmount' => $this->getSendAmount(),
            'sendCurrency' => $this->getSendCurrency(),
            'createdAt' => Util::formatApiDateTime($this->getCreatedAt()),
            'transferFee' => $this->getTransferFee(),
            'payoutAmount' => $this->getPayoutAmount(),
            'payoutCurrency' => $this->getPayoutCurrency(),
            'sendAt' => Util::formatApiDateTime($this->getSendAt()),
            'receiveAmount' => $this->getReceiveAmount(),
            'receiveCurrency' => $this->getReceiveCurrency(),
            'receiveAt' => Util::formatApiDateTime($this->getReceiveAt()),
            'error' => $errorMessage,
            'partner' => $this->getPartner(),
            'totalCost' => $this->getTotalAmount(),
            'cashFee'   => $payoutOptions ? (isset($payoutOptions['stateFee']) ? $this->getTransferFee() - $payoutOptions['stateFee'] : $this->getTransferFee()) : $this->getTransferFee(),
            'stateFee'   => $payoutOptions && isset($payoutOptions['stateFee']) ? $payoutOptions['stateFee'] : 0,
            'rate'     => round(1/$this->getFxRate(), 8),
            'unitellerStatus' => '',
            'branchId' => Util::meta($this, 'branchId'),
            'needAddComplianceInfo' => Util::meta($this, 'needAddComplianceInfo'),
            'needAddComplianceFile' => Util::meta($this, 'needAddComplianceFile'),
        ];
        if ($intermexTransaction && ($all['needAddComplianceInfo'] || (!empty($intermexTransaction['print1025']) && $intermexTransaction['print1025']))) {
          if (!empty($intermexTransaction['stsComplianceOk']) && $intermexTransaction['stsComplianceOk'] == true) {
            $all['needAddComplianceInfo'] = false;
          } else {
            $all['complianceInfoFields'] = [
              [
                'label'     => 'Social Security Number',
                'name'      =>  'ssValue',
                'isRequire' => ((!empty($intermexTransaction['requireSS']) && $intermexTransaction['requireSS']) || (!empty($intermexTransaction['print1025']) && $intermexTransaction['print1025'])) && !Util::meta($this->getSender(), 'ssValue'),
                'type'      => 'text'
              ],
              [
                'label'     => 'Transfer Purpose',
                'name'      =>  'wirePurpose',
                'isRequire' => (!empty($intermexTransaction['requireWireAddInf']) && $intermexTransaction['requireWireAddInf']) || (!empty($intermexTransaction['print1025']) && $intermexTransaction['print1025']),
                'type'      => 'text'
              ],
              [
                'label'     => 'Fund Source',
                'name'      =>  'fundSource',
                'isRequire' => (!empty($intermexTransaction['requireWireAddInf']) && $intermexTransaction['requireWireAddInf']) || (!empty($intermexTransaction['print1025']) && $intermexTransaction['print1025']),
                'type'      => 'text'
              ],
              [
                'label'     => 'Nationality information',
                'name'      =>  'citizenship',
                'isRequire' => !empty($intermexTransaction['requiredNationality']) && $intermexTransaction['requiredNationality'] && !Util::meta($this->getSender(), 'citizenshipValue'),
                'type'      => 'text'
              ],
              [
                'label'     => 'Relationship to Recipient',
                'name'      => 'relationship',
                'isRequire' => !empty($intermexTransaction['print1025']) && $intermexTransaction['print1025'] && !Util::meta($this->getRecipient(), 'relationship'),
                'type'      => 'text'
              ],
              [
                'label'     => 'Sender Occupation',
                'name'      =>  'occupation',
                'isRequire' => !empty($intermexTransaction['print1025']) && $intermexTransaction['print1025'],
                'type'      => 'text'
              ]
            ];
          }
        }
        if ($intermexTransaction && $all['needAddComplianceFile']) {
          $all['complianceInfoFile'] = [];
          if (!empty($intermexTransaction['requirePhotoID']) && $intermexTransaction['requirePhotoID']){
            $all['complianceInfoFile'][] = [
              [
                'label'     => "Sender Identification Document",
                'name'      =>  'COMP_SNDID',
                'type'      => 'file',
                'isRequire' => true
              ],
              [
                'label'     => 'ID Type',
                'name'      => 'idType',
                'type'      => 'select',
                'isRequire' => true,
                'source'    => IntermexRemittanceService::ID_TYPE_LIST
              ],
              [
                'label'     => 'ID Number',
                'name'      =>  'idNumber',
                'type'      => 'text',
                'isRequire' => true
              ],
              [
                'label'     => 'ID Expiration Date',
                'name'      =>  'idExpirationDate',
                'type'      => 'text',
                'isRequire' => true
              ]
            ];
          }
          if (!empty($intermexTransaction['requireIncomeVerif']) && $intermexTransaction['requireIncomeVerif']) {
            $all['complianceInfoFile'][] = [
              [
                'label'     => 'Income Proof Document',
                'name'      =>  'COMP_SND_INCOME',
                'type'      => 'file',
                'isRequire' => true
              ],
              [
                'label'     => 'Income Type',
                'name'      => 'incomeTypeId',
                'type'      => 'select',
                'isRequire' => true,
                'source'    => IntermexRemittanceService::ID_INCOME_TYPE_LIST
              ],
            ];
          }
        }
        if ($extra) {
            if (Util::isAPI() && in_array($this->getStatus(), [
                self::STATUS_CREATED,
                self::STATUS_COMPLETED,
                self::STATUS_HOLD,
                self::STATUS_PROCESSING,
                self::STATUS_QUEUED,
            ])) {
                $all['error'] = null;
            }

            $recipient = $this->getRecipient();
            $rapyd = $this->isRapyd();
            $uniTeller = $this->isUniTeller();
            $intermex = $this->isIntermex();
            $all['canCancel'] = false;
            $all['canCancelByAdmin'] = false;
            if (($uniTeller || $rapyd || $intermex) && $recipient->inTeam(Role::ROLE_TRANSFER_MEX_RECIPIENT)) {
                $all['recipient'] = $recipient->toTransferMexApiArray('recipient', $this);
                $all['canCancel'] = $intermex ? false : $this->getCanCancelFlag();
                $all['canCancelByAdmin'] = $this->getCanCancelFlag();
            } else {
                $all['recipient'] = $recipient->toApiArray();
            }

            $all['cancancelAfter'] = null;
            if ($uniTeller) {
              $sendAt = $this->getSendAt();
              $all['cancancelAfter'] =  $all['canCancel'] && $sendAt ? Util::formatApiDateTime(Carbon::parse($sendAt)->addMinutes(Config::get('cancel_uniteller_transfer_time', 90))) : null;
            }
            $status = $this->getStatus();
            if ($rapyd && $status === self::STATUS_CREATED && $this->getPartnerId()) {
                $meta = Util::meta($this, 'rapydPayout');
                if (!empty($meta['instructions']['steps'])) {
                    $steps = [];
                    foreach ($meta['instructions']['steps'] as $ss) {
                        $steps = array_merge($steps, array_values($ss));
                    }
                    if ($steps) {
                        $all['instructions'] = $steps;
                    }
                }
                if (!empty($meta['instructions_value'])) {
                    $all['instructionValues'] = $meta['instructions_value'];
                }
            }

            if ($status === self::STATUS_CREATED && $uniTeller && $this->getPartnerId()) {
                $meta = Util::meta($this, 'UniTellerSendMoney');
                if (
                    isset($meta['transactionDetail']) &&
                    (
                        (isset($meta['transactionStatus']) && $meta['transactionStatus'] !== self::UNITELLER_STATUS_HOLD) ||
                        $meta['transactionDetail']['transactionStatus'] !== self::UNITELLER_STATUS_HOLD
                    )
                    && $this->getPayoutType() == 'cash'
                ) {
                  $all['instructions'] = ['Please provide the below pickup code to the recipient.'];
                  $beneficiarySummary = $meta['transactionDetail']['beneficiarySummary'];
                  $recipient = $beneficiarySummary['firstName'] . ' ' . $beneficiarySummary['lastName'];
                  $address =  $beneficiarySummary['address']['address1'] . ' ' . $beneficiarySummary['address']['city'] . ' ' . $beneficiarySummary['address']['stateName'] . ' ' . $beneficiarySummary['address']['country'];
                    $all['instructionValues'] = [
                        'Recipient'         => $recipient,
                        'Recipient Address' => $address,
                        'Pickup Code'       => $this->getPartnerId(),
                        'Notice' => 'Some merchants may ask for a copy of the Gov ID.',
                    ];
                }
                $all['unitellerStatus'] = isset($meta['transactionStatus']) ? $meta['transactionStatus'] : (isset($meta['transactionDetail']) ? $meta['transactionDetail']['transactionStatus'] : '');
            }

            if ($status === self::STATUS_CREATED && $intermex && $this->getPartnerId()) {
              $IntermexStatusInfo = Util::meta($this, 'IntermexStatus');
              if ($IntermexStatusInfo && isset($IntermexStatusInfo['wireStatusId']) && $IntermexStatusInfo['wireStatusId'] == 4 && $this->getPayoutType() == 'cash') {
                $all['instructions'] = ['Please provide the below pickup code to the recipient.'];
                $address =  $recipient->getAddress() . ' ' . $recipient->getCity() . ' ' . $recipient->getState()->getName() . ' ' . $recipient->getCountry()->getName();
                $all['instructionValues'] = [
                    'Recipient'         => $recipient->getFirstName() . ' ' . $recipient->getLastName(),
                    'Recipient Address' => $address,
                    'Pickup Code'       => $this->getPartnerId(),
                    'Notice' => 'Some merchants may ask for a copy of the Gov ID.',
                ];
              } else if (isset(IntermexRemittanceService::BANK_TIME_LIMIT[$this->getPayoutMethodType()])) {
                if (MemberService::getCurrentLanguage($this->getSender()) != 'es') {
                  $all['instructions'] = ["Your transfer is being processed and will be reflected in approximately 10–15 minutes during bank hours. \n\nThank you for trusting us!"];
                } else {
                  $all['instructions'] = ["Tu transferencia está en proceso y se reflejará en aproximadamente 10–15 minutos durante las horas de banco. \n\n¡Gracias por confiar en nosotros!"];
                }
              }
              $all['intermexStatus'] = isset($IntermexStatusInfo['wireStatusId']) ? self::INTERMEX_INTERNAL_STATUS_LIST[$IntermexStatusInfo['wireStatusId']] : '';
            }
            if ($rapyd && Util::isAPI()) {
                $completedStatuses = [
                    self::STATUS_CREATED,
                    self::STATUS_PROCESSING,
                ];
                if (in_array($status, $completedStatuses) || Util::meta($this, 'markAsCompleted')) {
                    $all['status'] = self::STATUS_COMPLETED;
                }
                if ($status === self::STATUS_HOLD) {
                    $all['status'] = self::STATUS_PROCESSING;
                } else if (in_array($status, [
                    self::STATUS_DECLINE,
                    self::STATUS_DECLINED,
                ])) {
                    $all['status'] = self::STATUS_ERROR;
                }
            }

            $appFields = [];
            if ($rapyd && $this->isPendingToRecheck()) {
                $appFields['Status'] = 'Your transfer is currently being processed. The status will be updated in 1~3 days.';
            }
            if (! TransferMexBundle::isSecureApp()) {
                // Attention: You are using an old version. Please upgrade your app to achieve better security and performance. We will deprecate the old version on August 8th or even earlier.
                $appFields['Atención'] = 'Estás usando una versión antigua. Actualice su aplicación para lograr una mejor seguridad y rendimiento. Desactivaremos la versión anterior el 8 de agosto o incluso antes.';
            }
            if ($appFields) {
                $all['appFields'] = $appFields;
            }
        }

        if ($all['error'] === '') {
            $all['error'] = null;
        }

        return $all;
    }

    public function getCanCancelFlag() {
        if (in_array($this->getStatus(), [self::STATUS_CREATED, self::STATUS_CONFIRMATION]) && in_array($this->getPartner(), [self::PARTNER_UNITELLER, self::PARTNER_INTERMEX])) {
          return true;
        }
        return false;
    }

    public function fillEiFromException(\Throwable $t)
    {
        if ($this->getEi()) {
            return $this;
        }
        if (method_exists($t, 'getExternalInvokeId')) {
            $eiId = $t->getExternalInvokeId();
            if ($eiId) {
                $ei = ExternalInvoke::find($eiId);
                if ($ei) {
                    $this->setEi($ei);
                }
            }
        }
        return $this;
    }

    public function getFromCardBaseProcessor()
    {
        $from = $this->getFromAccountNumber();
        if ( ! $from) {
            if ($this->getCreatedAt() && Carbon::parse('2025-01-01')->lte($this->getCreatedAt())) {
                return new BotmAPI();
            }
            return null;
        }
        return ProcessorHub::getBaseAgentProcessorForAccountNumber($from);
    }

    /**
     * @ORM\ManyToOne(targetEntity="SalexUserBundle\Entity\User")
     * @Serializer\Exclude()
     */
    private $sender;

    /**
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\UserCard")
     * @Serializer\Exclude()
     */
    private $senderCard;

    /**
     * @ORM\ManyToOne(targetEntity="SalexUserBundle\Entity\User")
     * @Serializer\Exclude()
     */
    private $recipient;

    /**
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\UserCard")
     * @Serializer\Exclude()
     */
    private $recipientCard;

    /**
     * @var string
     *
     * @ORM\Column(name="partner", type="string", length=255, nullable=true)
     */
    private $partner;

    /**
     * @var string
     *
     * @ORM\Column(name="partner_id", type="string", length=255, nullable=true)
     */
    private $partnerId;

    /**
     * @var string
     *
     * @ORM\Column(name="description", type="text", nullable=true)
     */
    private $description;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=255, options={"default": "pending"})
     */
    private $status;

    /**
     * @var string
     *
     * @ORM\Column(name="source_agent_number", type="string", length=15, nullable=true)
     */
    private $sourceAgentNumber;

    /**
     * @var string
     *
     * @ORM\Column(name="from_account_number", type="string", length=16, nullable=true)
     */
    private $fromAccountNumber;

    /**
     * @var string
     *
     * @ORM\Column(name="payout_type", type="string", length=255, nullable=true)
     */
    private $payoutType;

    /**
     * @var string
     *
     * @ORM\Column(name="payout_method_type", type="string", length=255, nullable=true)
     */
    private $payoutMethodType;

    /**
     * @var int
     *
     * @ORM\Column(name="send_amount", type="integer")
     */
    private $sendAmount;

    /**
     * @var string
     *
     * @ORM\Column(name="send_currency", type="string", length=10)
     */
    private $sendCurrency;

    /**
     * @var double
     *
     * @ORM\Column(name="fx_rate", type="decimal", precision=16, scale=8, options={"default": 1})
     */
    private $fxRate;

    /**
     * @var int
     *
     * @ORM\Column(name="cost", type="integer", nullable=true)
     */
    private $cost;

    /**
     * @var int
     *
     * @ORM\Column(name="revenue", type="integer", nullable=true)
     */
    private $revenue;

    /**
     * @var int
     *
     * @ORM\Column(name="platform_revenue", type="integer", nullable=true)
     */
    private $platformRevenue;

    /**
     * @var int
     *
     * @ORM\Column(name="payout_amount", type="integer")
     */
    private $payoutAmount;

    /**
     * @var string
     *
     * @ORM\Column(name="payout_currency", type="string", length=10)
     */
    private $payoutCurrency;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="send_at", type="datetime", nullable=true)
     */
    private $sendAt;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="sync_at", type="datetime", nullable=true)
     */
    private $syncAt;

    /**
     * @var int
     *
     * @ORM\Column(name="receive_amount", type="integer", nullable=true, options={"comment": "the final amount the recipient received"})
     */
    private $receiveAmount;

    /**
     * @var string
     *
     * @ORM\Column(name="receive_currency", type="string", length=10, nullable=true)
     */
    private $receiveCurrency;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="receive_at", type="datetime", nullable=true)
     */
    private $receiveAt;

    /**
     * @var int
     *
     * @ORM\Column(name="transfer_fee", type="integer")
     */
    private $transferFee;

    /**
     * @var string
     * @ORM\Column(name="error", type="text", nullable=true)
     */
    private $error;

    /**
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\ExternalInvoke")
     * @Serializer\Exclude()
     */
    private $ei;

    /**
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\TransferSettlement")
     * @Serializer\Exclude()
     */
    private $settlement;

    /**
     * @var string
     * @ORM\Column(name="meta", type="text", nullable=true)
     */
    private $meta;

     /**
     * @var \DateTime
     *
     * @ORM\Column(name="cancel_at", type="datetime", nullable=true)
     */
    private $cancelAt;

       /**
     * @var \DateTime
     *
     * @ORM\Column(name="cash_pick_code_at", type="datetime", nullable=true)
     */
    private $cashPickCodeAt;

    /**
     * @var string
     * @ORM\Column(name="force_payout_status", type="string", nullable=true)
     */
    private $forcePayoutStatus;

    /**
     * @var int
     * @ORM\Column(name="payout_to_base", type="integer", nullable=true)
     */
    private $payoutToBase;

    /**
     * @var string
     * @ORM\Column(name="transfer_region", length=100, type="string", nullable=true)
     */
    private $transferRegion;


    /**
     * Transfer constructor.
     */
    public function __construct()
    {
        $this->status = self::STATUS_PENDING;
        $this->partner = self::PARTNER_RAPYD;
    }


    /**
     * Set status
     *
     * @param string $status
     *
     * @return Transfer
     */
    public function setStatus($status)
    {
        $this->status = $status;

        return $this;
    }

    /**
     * Get status
     *
     * @return string
     */
    public function getStatus()
    {
        return $this->status;
    }

    public function logChangeHistory($diff = [])
    {
        $his = compact('diff');
        $his['when'] = Util::formatApiDateTime(Carbon::now());

        $history = Util::meta($this, 'changeHistory');
        $history[] = $his;
        Util::updateMeta($this, [
            'changeHistory' => $history,
        ]);
    }

    /**
     * Get Partner status
     *
     * @return string
     */
    public function getPartnerStatus()
    {
        $status = $this->status;

        if ($this->getPartner() == self::PARTNER_UNITELLER) {
          if ($status == self::STATUS_CREATED) {
            $meta = $this->getMeta();
            if (strpos($meta, "PAYABLE") !== false) {
              $hash = Util::meta($this, 'hash');
              if (strlen($hash) < 10){
                $status = 'Created';
              } else {
                $created = $this->getSendAt();
                $now = Carbon::now();
                $status = $created < $now->subDays(7) ? 'Expired' : 'Sent sms, email, or called';
              }
            } else {
              $status = 'Pending';
            }
          }
        }
        return $status;
    }

    /**
     * Set sendAmount
     *
     * @param integer $sendAmount
     *
     * @return Transfer
     */
    public function setSendAmount($sendAmount)
    {
        $this->sendAmount = $sendAmount;

        return $this;
    }

    /**
     * Get sendAmount
     *
     * @return int
     */
    public function getSendAmount()
    {
        return $this->sendAmount;
    }

    /**
     * Set sendCurrency
     *
     * @param string $sendCurrency
     *
     * @return Transfer
     */
    public function setSendCurrency($sendCurrency)
    {
        $this->sendCurrency = $sendCurrency;

        return $this;
    }

    /**
     * Get sendCurrency
     *
     * @return string
     */
    public function getSendCurrency()
    {
        return $this->sendCurrency;
    }

    /**
     * Set receiveAmount
     *
     * @param integer $receiveAmount
     *
     * @return Transfer
     */
    public function setReceiveAmount($receiveAmount)
    {
        $this->receiveAmount = $receiveAmount;

        return $this;
    }

    /**
     * Get receiveAmount
     *
     * @return int
     */
    public function getReceiveAmount()
    {
        return $this->receiveAmount;
    }

    /**
     * Set receiveCurrency
     *
     * @param string $receiveCurrency
     *
     * @return Transfer
     */
    public function setReceiveCurrency($receiveCurrency)
    {
        $this->receiveCurrency = $receiveCurrency;

        return $this;
    }

    /**
     * Get receiveCurrency
     *
     * @return string
     */
    public function getReceiveCurrency()
    {
        return $this->receiveCurrency;
    }

    /**
     * Set sendAt
     *
     * @param \DateTime $sendAt
     *
     * @return Transfer
     */
    public function setSendAt($sendAt)
    {
        $this->sendAt = $sendAt;

        return $this;
    }

    /**
     * Get sendAt
     *
     * @return \DateTime
     */
    public function getSendAt()
    {
        return $this->sendAt;
    }

    /**
     * Set receiveAt
     *
     * @param \DateTime $receiveAt
     *
     * @return Transfer
     */
    public function setReceiveAt($receiveAt)
    {
        $this->receiveAt = $receiveAt;

        return $this;
    }

    /**
     * Get receiveAt
     *
     * @return \DateTime
     */
    public function getReceiveAt()
    {
        return $this->receiveAt;
    }

    /**
     * Set payoutType
     *
     * @param string $payoutType
     *
     * @return Transfer
     */
    public function setPayoutType($payoutType)
    {
        $this->payoutType = $payoutType;

        return $this;
    }

    /**
     * Get payoutType
     *
     * @return string
     */
    public function getPayoutType()
    {
        return $this->payoutType;
    }

    /**
     * Set transferFee
     *
     * @param integer $transferFee
     *
     * @return Transfer
     */
    public function setTransferFee($transferFee)
    {
        $this->transferFee = $transferFee;

        return $this;
    }

    /**
     * Get transferFee
     *
     * @return integer
     */
    public function getTransferFee()
    {
        return $this->transferFee;
    }

    /**
     * Set meta
     *
     * @param string $meta
     *
     * @return Transfer
     */
    public function setMeta($meta)
    {
        $this->meta = $meta;

        return $this;
    }

    /**
     * Get meta
     *
     * @return string
     */
    public function getMeta()
    {
        return $this->meta;
    }

    /**
     * Set sender
     *
     * @param \SalexUserBundle\Entity\User $sender
     *
     * @return Transfer
     */
    public function setSender(\SalexUserBundle\Entity\User $sender = null)
    {
        $this->sender = $sender;

        return $this;
    }

    /**
     * Get sender
     *
     * @return \SalexUserBundle\Entity\User
     */
    public function getSender()
    {
        return $this->sender;
    }

    /**
     * Set senderCard
     *
     * @param \CoreBundle\Entity\UserCard $senderCard
     *
     * @return Transfer
     */
    public function setSenderCard(\CoreBundle\Entity\UserCard $senderCard = null)
    {
        $this->senderCard = $senderCard;

        return $this;
    }

    /**
     * Get senderCard
     *
     * @return \CoreBundle\Entity\UserCard
     */
    public function getSenderCard()
    {
        return $this->senderCard;
    }

    /**
     * Set recipient
     *
     * @param \SalexUserBundle\Entity\User $recipient
     *
     * @return Transfer
     */
    public function setRecipient(\SalexUserBundle\Entity\User $recipient = null)
    {
        $this->recipient = $recipient;

        return $this;
    }

    /**
     * Get recipient
     *
     * @return \SalexUserBundle\Entity\User
     */
    public function getRecipient()
    {
        return $this->recipient;
    }

    /**
     * Set recipientCard
     *
     * @param \CoreBundle\Entity\UserCard $recipientCard
     *
     * @return Transfer
     */
    public function setRecipientCard(\CoreBundle\Entity\UserCard $recipientCard = null)
    {
        $this->recipientCard = $recipientCard;

        return $this;
    }

    /**
     * Get recipientCard
     *
     * @return \CoreBundle\Entity\UserCard
     */
    public function getRecipientCard()
    {
        return $this->recipientCard;
    }

    /**
     * Set payoutMethodType
     *
     * @param string $payoutMethodType
     *
     * @return Transfer
     */
    public function setPayoutMethodType($payoutMethodType)
    {
        $this->payoutMethodType = $payoutMethodType;

        return $this;
    }

    /**
     * Get payoutMethodType
     *
     * @return string
     */
    public function getPayoutMethodType()
    {
        return $this->payoutMethodType;
    }

    /**
     * Set description
     *
     * @param string $description
     *
     * @return Transfer
     */
    public function setDescription($description)
    {
        $this->description = $description;

        return $this;
    }

    /**
     * Get description
     *
     * @return string
     */
    public function getDescription()
    {
        return $this->description;
    }

    /**
     * Set error
     *
     * @param string $error
     *
     * @return Transfer
     */
    public function setError($error)
    {
        $this->error = $error;

        return $this;
    }

    /**
     * Get error
     *
     * @return string
     */
    public function getError()
    {
        return $this->error;
    }

    /**
     * Set ei
     *
     * @param \CoreBundle\Entity\ExternalInvoke $ei
     *
     * @return Transfer
     */
    public function setEi(\CoreBundle\Entity\ExternalInvoke $ei = null)
    {
        $this->ei = $ei;

        return $this;
    }

    /**
     * Get ei
     *
     * @return \CoreBundle\Entity\ExternalInvoke
     */
    public function getEi()
    {
        return $this->ei;
    }

    /**
     * Set partnerId
     *
     * @param string $partnerId
     *
     * @return Transfer
     */
    public function setPartnerId($partnerId)
    {
        $this->partnerId = $partnerId;

        return $this;
    }

    /**
     * Get partnerId
     *
     * @return string
     */
    public function getPartnerId()
    {
        return $this->partnerId;
    }

    /**
     * Set partner
     *
     * @param string $partner
     *
     * @return Transfer
     */
    public function setPartner($partner)
    {
        $this->partner = $partner;

        return $this;
    }

    /**
     * Get partner
     *
     * @return string
     */
    public function getPartner()
    {
        return $this->partner;
    }

    /**
     * Set payoutAmount
     *
     * @param integer $payoutAmount
     *
     * @return Transfer
     */
    public function setPayoutAmount($payoutAmount)
    {
        $this->payoutAmount = $payoutAmount;

        return $this;
    }

    /**
     * Get payoutAmount
     *
     * @return integer
     */
    public function getPayoutAmount()
    {
        return $this->payoutAmount;
    }

    /**
     * Set payoutCurrency
     *
     * @param string $payoutCurrency
     *
     * @return Transfer
     */
    public function setPayoutCurrency($payoutCurrency)
    {
        $this->payoutCurrency = $payoutCurrency;

        return $this;
    }

    /**
     * Get payoutCurrency
     *
     * @return string
     */
    public function getPayoutCurrency()
    {
        return $this->payoutCurrency;
    }

    /**
     * Set fxRate
     *
     * @param double $fxRate
     *
     * @return Transfer
     */
    public function setFxRate($fxRate)
    {
        $this->fxRate = $fxRate;

        return $this;
    }

    /**
     * Get fxRate
     *
     * @return double
     */
    public function getFxRate()
    {
        return $this->fxRate;
    }

    /**
     * Set cost
     *
     * @param integer $cost
     *
     * @return Transfer
     */
    public function setCost($cost)
    {
        $this->cost = $cost;

        return $this;
    }

    /**
     * Get cost
     *
     * @return integer
     */
    public function getCost()
    {
        return $this->cost;
    }

    /**
     * Set revenue
     *
     * @param integer $revenue
     *
     * @return Transfer
     */
    public function setRevenue($revenue)
    {
        $this->revenue = $revenue;

        return $this;
    }

    /**
     * Get revenue
     *
     * @return integer
     */
    public function getRevenue()
    {
        return $this->revenue;
    }

    /**
     * Set platformRevenue
     *
     * @param integer $platformRevenue
     *
     * @return Transfer
     */
    public function setPlatformRevenue($platformRevenue)
    {
        $this->platformRevenue = $platformRevenue;

        return $this;
    }

    /**
     * Get platformRevenue
     *
     * @return integer
     */
    public function getPlatformRevenue()
    {
        return $this->platformRevenue;
    }

    /**
     * Set settlement
     *
     * @param \CoreBundle\Entity\TransferSettlement $settlement
     *
     * @return Transfer
     */
    public function setSettlement(\CoreBundle\Entity\TransferSettlement $settlement = null)
    {
        $this->settlement = $settlement;

        return $this;
    }

    /**
     * Get settlement
     *
     * @return \CoreBundle\Entity\TransferSettlement
     */
    public function getSettlement()
    {
        return $this->settlement;
    }

    /**
     * Set syncAt
     *
     * @param \DateTime $syncAt
     *
     * @return Transfer
     */
    public function setSyncAt($syncAt)
    {
        $this->syncAt = $syncAt;

        return $this;
    }

    /**
     * Get syncAt
     *
     * @return \DateTime
     */
    public function getSyncAt()
    {
        return $this->syncAt;
    }

    /**
     * Set sourceAgentNumber
     *
     * @param string $sourceAgentNumber
     *
     * @return Transfer
     */
    public function setSourceAgentNumber($sourceAgentNumber)
    {
        $this->sourceAgentNumber = $sourceAgentNumber;

        return $this;
    }

    /**
     * Get sourceAgentNumber
     *
     * @return string
     */
    public function getSourceAgentNumber()
    {
        return $this->sourceAgentNumber;
    }

     /**
     * Set cancelAt
     *
     * @param \DateTime $cancelAt
     *
     * @return Transfer
     */
    public function setCancelAt($cancelAt)
    {
        $this->cancelAt = $cancelAt;

        return $this;
    }

    /**
     * Get cancelAt
     *
     * @return \DateTime
     */
    public function getCancelAt()
    {
        return $this->cancelAt;
    }

     /**
     * Set cashPickCodeAt
     *
     * @param \DateTime $cashPickCodeAt
     *
     * @return Transfer
     */
    public function setCashPickCodeAt($cashPickCodeAt)
    {
        $this->cashPickCodeAt = $cashPickCodeAt;

        return $this;
    }

    /**
     * Get cashPickCodeAt
     *
     * @return \DateTime
     */
    public function getCashPickCodeAt()
    {
        return $this->cashPickCodeAt;
    }

    public function getFromAccountNumber(): ?string
    {
        return $this->fromAccountNumber;
    }

    public function setFromAccountNumber(?string $fromAccountNumber): static
    {
        $this->fromAccountNumber = $fromAccountNumber;

        return $this;
    }

     /**
     * Set forcePayoutStatus
     *
     * @param String $forcePayoutStatus
     *
     * @return Transfer
     */
    public function setForcePayoutStatus($forcePayoutStatus)
    {
        $this->forcePayoutStatus = $forcePayoutStatus;

        return $this;
    }

    /**
     * Get forcePayoutStatus
     *
     * @return string
     */
    public function getForcePayoutStatus()
    {
        return $this->forcePayoutStatus;
    }

     /**
     * Set payoutToBase
     *
     * @param int $payoutToBase
     *
     * @return Transfer
     */
    public function setPayoutToBase($payoutToBase)
    {
        $this->payoutToBase = $payoutToBase;

        return $this;
    }

    /**
     * Get payoutToBase
     *
     * @return int
     */
    public function getPayoutToBase()
    {
        return $this->payoutToBase;
    }

    public function getTransferRegion(): ?string
    {
        return $this->transferRegion;
    }

    public function setTransferRegion(?string $transferRegion): static
    {
        $this->transferRegion = $transferRegion;

        return $this;
    }
}
