<?php

namespace CoreBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use J<PERSON>\Serializer\Annotation as Serializer;

/**
 * UniTellerTransaction
 *
 * @ORM\Table(name="uniteller_transaction", indexes={
 *     @ORM\Index(name="platform_txn_id", columns={"platform_id", "txn_id"}),
 *     @ORM\Index(name="platform_created_at_idx", columns={"platform_id", "created_at"}),
 *     @ORM\Index(name="created_at_idx", columns={"created_at"}),
 * })
 * @ORM\Entity(repositoryClass="CoreBundle\Repository\RapydTransactionRepository")
 */
class UniTellerTransaction
{
    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\Platform")
     * @Serializer\Exclude()
     */
    private $platform;

    /**
     * @var string
     *
     * @ORM\Column(name="txn_id", type="string", length=255, unique=true)
     */
    private $txnId;

    /**
     * @var string
     *
     * @ORM\Column(name="currency", type="string", length=10, nullable=true)
     */
    private $currency;

    /**
     * @var int
     *
     * @ORM\Column(name="amount", type="integer", nullable=true)
     */
    private $amount;

    /**
     * @var string
     *
     * @ORM\Column(name="type", type="string", length=255, nullable=true)
     */
    private $type;

    /**
     * @var string
     *
     * @ORM\Column(name="balance_type", type="string", length=255, nullable=true)
     */
    private $balanceType;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="created_at", type="datetime", nullable=true)
     */
    private $createdAt;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=50, nullable=true)
     */
    private $status;


    /**
     * @var string
     *
     * @ORM\Column(name="remitter", type="string", length=100, nullable=true)
     */
    private $remitter;

    /**
     * @var string
     *
     * @ORM\Column(name="reason", type="string", length=255, nullable=true)
     */
    private $reason;

    /**
     * @var string
     *
     * @ORM\Column(name="meta", type="text", nullable=true)
     */
    private $meta;

    /**
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\Transfer")
     * @Serializer\Exclude()
     */
    private $transfer;

    /**
     * Get id
     *
     * @return integer
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set txnId
     *
     * @param string $txnId
     *
     * @return UniTellerTransaction
     */
    public function setTxnId($txnId)
    {
        $this->txnId = $txnId;

        return $this;
    }

    /**
     * Get txnId
     *
     * @return string
     */
    public function getTxnId()
    {
        return $this->txnId;
    }

    /**
     * Set currency
     *
     * @param string $currency
     *
     * @return UniTellerTransaction
     */
    public function setCurrency($currency)
    {
        $this->currency = $currency;

        return $this;
    }

    /**
     * Get currency
     *
     * @return string
     */
    public function getCurrency()
    {
        return $this->currency;
    }

    /**
     * Set amount
     *
     * @param integer $amount
     *
     * @return UniTellerTransaction
     */
    public function setAmount($amount)
    {
        $this->amount = $amount;

        return $this;
    }

    /**
     * Get amount
     *
     * @return integer
     */
    public function getAmount()
    {
        return $this->amount;
    }

    /**
     * Set type
     *
     * @param string $type
     *
     * @return UniTellerTransaction
     */
    public function setType($type)
    {
        $this->type = $type;

        return $this;
    }

    /**
     * Get type
     *
     * @return string
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * Set balanceType
     *
     * @param string $balanceType
     *
     * @return UniTellerTransaction
     */
    public function setBalanceType($balanceType)
    {
        $this->balanceType = $balanceType;

        return $this;
    }

    /**
     * Get balanceType
     *
     * @return string
     */
    public function getBalanceType()
    {
        return $this->balanceType;
    }

    /**
     * Set createdAt
     *
     * @param \DateTime $createdAt
     *
     * @return UniTellerTransaction
     */
    public function setCreatedAt($createdAt)
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    /**
     * Get createdAt
     *
     * @return \DateTime
     */
    public function getCreatedAt()
    {
        return $this->createdAt;
    }

    /**
     * Set status
     *
     * @param string $status
     *
     * @return UniTellerTransaction
     */
    public function setStatus($status)
    {
        $this->status = $status;

        return $this;
    }

    /**
     * Get status
     *
     * @return string
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * Set reason
     *
     * @param string $reason
     *
     * @return UniTellerTransaction
     */
    public function setReason($reason)
    {
        $this->reason = $reason;

        return $this;
    }

    /**
     * Get reason
     *
     * @return string
     */
    public function getReason()
    {
        return $this->reason;
    }

    /**
     * Set meta
     *
     * @param string $meta
     *
     * @return UniTellerTransaction
     */
    public function setMeta($meta)
    {
        $this->meta = $meta;

        return $this;
    }

    /**
     * Get meta
     *
     * @return string
     */
    public function getMeta()
    {
        return $this->meta;
    }

    /**
     * Set platform
     *
     * @param \CoreBundle\Entity\Platform $platform
     *
     * @return UniTellerTransaction
     */
    public function setPlatform(\CoreBundle\Entity\Platform $platform = null)
    {
        $this->platform = $platform;

        return $this;
    }

    /**
     * Get platform
     *
     * @return \CoreBundle\Entity\Platform
     */
    public function getPlatform()
    {
        return $this->platform;
    }

    /**
     * Set transfer
     *
     * @param \CoreBundle\Entity\Transfer $transfer
     *
     * @return UniTellerTransaction
     */
    public function setTransfer(\CoreBundle\Entity\Transfer $transfer = null)
    {
        $this->transfer = $transfer;

        return $this;
    }

    /**
     * Get transfer
     *
     * @return \CoreBundle\Entity\Transfer
     */
    public function getTransfer()
    {
        return $this->transfer;
    }
}