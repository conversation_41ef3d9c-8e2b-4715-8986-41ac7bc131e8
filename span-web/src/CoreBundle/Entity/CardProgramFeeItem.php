<?php
/**
 * User: Bob
 * Date: 2017/3/9
 * Time: 12:29
 */

namespace CoreBundle\Entity;

use Carbon\Carbon;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Util;
use CoreBundle\Utils\Traits\ConstantTrait;
use Doctrine\ORM\Mapping as ORM;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;
use PortalB<PERSON>le\Exception\PortalException;
use SalexUserBundle\Entity\User;

/**
 * Class CardProgramFeeItem
 * @ORM\Entity
 * @ORM\Table(name="card_program_fee_item", options={"comment":"Fee settings for each card program"})
 * @package CoreBundle\Entity
 */
class CardProgramFeeItem extends BaseEntity
{
    use ConstantTrait;

    const APPLY_TO_ACCOUNT_HOLDER = 'account_holder';
    const APPLY_TO_CARD = 'card';
    const APPLY_TO_BALANCE = 'balance';
    const APPLY_TO_BRAND_PARTNER = 'brand_partner';

    const MEASURE_UNIT_PER_TIMES = 'per_times';
    const MEASURE_UNIT_FIRST_TIME = 'first_time';
    const MEASURE_UNIT_FIRST_TIME_RECURRING = 'first_time_then_recurring';
    const MEASURE_UNIT_RECURRING = 'recurring';

    const RECURRING_TYPE_PER_DAY = 'per_day';
    const RECURRING_TYPE_PER_WEEK = 'per_week';
    const RECURRING_TYPE_PER_MONTH = 'per_month';
    const RECURRING_TYPE_PER_YEAR = 'per_year';

    const RECURRING_TIME_FIRST_DAY_IN_WEEK = 'on_first_day_of_week';
    const RECURRING_TIME_FIRST_DAY_IN_MONTH = 'on_first_day_of_month';
    const RECURRING_TIME_FIRST_DAY_IN_YEAR = 'on_first_day_of_year';
    const RECURRING_TIME_LAST_DAY_IN_WEEK = 'on_last_day_of_week';
    const RECURRING_TIME_LAST_DAY_IN_MONTH = 'on_last_day_of_month';
    const RECURRING_TIME_LAST_DAY_IN_YEAR = 'on_last_day_of_year';
    const RECURRING_TIME_15TH_IN_MONTH = 'on_15_th_of_month';
    const RECURRING_TIME_ANNIVERSARY_OF_CARD_CREATE = 'on_anniversary_of_card_account_creation';
    const RECURRING_TIME_ANNIVERSARY_OF_USER_CREATE = 'on_anniversary_of_user_account_creation';

    public static function getFor(CardProgram $cp, $name, User $user, ?int $amount = null)
    {
        return Util::em()->getRepository(CardProgram::class)
            ->getFeeItem($cp, $name, $user, $amount);
    }

    public function hasFee()
    {
        return $this->getFeeToCusFixed() || $this->getFeeToCusRatio();
    }

    protected function mergeDiscountSummary(array &$old, array $new)
    {
        $old['fixed'] += $new['fixed'];
        $old['ratio'] += $new['ratio'];
        if ($old['ratio'] > 100) {
            $old['ratio'] = 100;
        }
    }

    public function calculate($amount, $currency = 'USD', User $subtractOnUser = null, &$comments = null)
    {
        $discount = [
            'fixed' => 0,
            'ratio' => 0,
        ];
        if ($subtractOnUser) {
            $ud = UserDiscount::find($subtractOnUser, $this);
            if ($ud) {
                $this->mergeDiscountSummary($discount, $ud->getValidSummary($this, $amount, $currency));
            }

            $now = Carbon::now(Util::tzUTC());
            $promotions = Util::em()->getRepository(\CoreBundle\Entity\Promotion::class)
                ->createQueryBuilder('p')
                ->join('p.fees', 'f')
                ->where('f = :fee')
                ->andWhere('p.status is null')
                ->andWhere('p.startAt <= :startAt')
                ->andWhere('p.endAt >= :endAt')
                ->setParameter('fee', $this)
                ->setParameter('startAt', $now)
                ->setParameter('endAt', $now)
                ->distinct()
                ->getQuery()
                ->getResult();
            /** @var Promotion $p */
            foreach ($promotions as $p) {
                if ($p->matchUser($subtractOnUser) && $p->isValid($subtractOnUser)) {
                    $this->mergeDiscountSummary($discount, $p->getDiscountSummary($this, $amount, $currency));
                    if (is_array($comments)) {
                        $comments[] = 'promotion_' . $p->getId();
                    }
                }
            }
        }

        $fixed = $this->getFeeToCusFixed();
        $fee = 0;
        if ($fixed) {
            $fromCurrency = $this->getFeeItem()->getCostTernFixedCurrency();
            if (!$fromCurrency) {
                throw new PortalException('Fee Item "' . $this->getFeeItem()->getFeeName() . '"\'s currency is not configured! Please contact Administrator!');
            }
            $fee += Money::convertWithExtra($fixed, $fromCurrency, $currency);
            $fee -= $discount['fixed'];
            if ($fee < 0) {
                $fee = 0;
            }
        }
        $ratio = $this->getFeeToCusRatio();
        if ($ratio) {
            $ratio -= $discount['ratio'];
            if ($ratio < 0) {
                $ratio = 0;
            }
            $fee += ($amount + $fee) * $ratio / 100;
        }

        return (int)round($fee);
    }

    public function calculateCost($amount, $currency = 'USD')
    {
        $fixed = $this->getFeeItem()->getCostTernFixed();
        $fee = 0;
        if ($fixed) {
            $fromCurrency = $this->getFeeItem()->getCostTernFixedCurrency();
            if (!$fromCurrency) {
                throw new PortalException('Fee Item "' . $this->getFeeItem()->getFeeName() . '"\'s currency is not configured! Please contact Administrator!');
            }
            $fee += Money::convertWithExtra($fixed, $fromCurrency, $currency);
            if ($fee < 0) {
                $fee = 0;
            }
        }
        $ratio = $this->getFeeItem()->getCostTernRatio();
        if ($ratio) {
            if ($ratio < 0) {
                $ratio = 0;
            }
            $fee += ($amount + $fee) * $ratio / 100;
        }

        return (int)round($fee);
    }

    // TCSPAN-766
    public function calculateReversed($receivedAmount, $currency)
    {
        $ratio = $this->getFeeToCusRatio();
        if ($ratio) {
            $receivedAmount /= (1 + $ratio / 100);
        }
        $fixed = $this->getFeeToCusFixed();
        if ($fixed) {
            $fromCurrency = $this->getFeeItem()->getCostTernFixedCurrency();
            if (!$fromCurrency) {
                throw new PortalException('Fee Item "' . $this->getFeeItem()->getFeeName() . '"\'s currency is
                    not configured when calculate reversed load amount! Please contact Administrator!');
            }
            $receivedAmount -= Money::convertWithExtra($fixed, $fromCurrency, $currency);
        }
        return round($receivedAmount);
    }

    public function getDescription($currency = null) {
        $s = [];

        $fixed = $this->getFeeToCusFixed();
        if ($fixed) {
            $defaultCurrency = $this->getFeeItem()->getCostTernFixedCurrency();
            if ($currency) {
                $fixed = Money::convertWithExtra($fixed, $defaultCurrency, $currency);
            } else {
                $currency = $defaultCurrency;
            }
            $s[] = Money::format($fixed, $currency);
        }

        $ratio = $this->getFeeToCusRatio();
        if ($ratio && !Util::isZero($ratio)) {
            $s[] = $ratio . '%';
        }

        return implode(' & ', $s);
    }

    //Relationships
    /**
     * @ORM\ManyToOne(targetEntity="FeeItem", inversedBy="cardProgramFeeItem")
     * @ORM\JoinColumn(name="fee_item_id", referencedColumnName="id", onDelete="CASCADE")
     */
    private $feeItem;

    /**
     * @ORM\ManyToOne(targetEntity="CardProgram", inversedBy="cardProgramFeeItem")
     * @ORM\JoinColumn(name="card_program_id", referencedColumnName="id", onDelete="CASCADE")
     * @Serializer\Exclude()
     */
    private $cardProgram;

    //Begin Add by Bob Wen Bao on 2017-03-13 to handle new property
    /**
     * @var integer $feeToCusFixed
     * @ORM\Column(name="fee_to_customer_fixed", type="float", nullable=true, options={"comment":"Fixed fee amount(USD) charged to customer."})
     */
    private $feeToCusFixed;

    /**
     * @var integer $feeToCusRatio
     * @ORM\Column(name="fee_to_customer_ratio", type="decimal", precision=4, scale=2, nullable=true, options={"comment":"Fee percent(0~100) of each order charged to customer."})
     */
    private $feeToCusRatio;

    /**
     * @var integer $feeToBPFixed
     * @ORM\Column(name="fee_to_bp_fixed", type="float", nullable=true, options={"comment":"Fixed fee amount(USD) charged to brand partner."})
     */
    private $feeToBPFixed;

    /**
     * @var integer $feeToBPRatio
     * @ORM\Column(name="fee_to_bp_ratio", type="decimal", precision=4, scale=2, nullable=true, options={"comment":"Fee percent(0~100) of each order charged to brand partner."})
     */
    private $feeToBPRatio;
    //End

    /**
     * @var string
     * @ORM\Column(name="apply_to", type="string", nullable=true)
     */
    private $applyTo;

    /**
     * @var float
     * @ORM\Column(name="measure", type="float", nullable=true)
     */
    private $measure;

    /**
     * @var string
     * @ORM\Column(name="measure_unit", type="string", nullable=true)
     */
    private $measureUnit;

    /**
     * @var string
     * @ORM\Column(name="recurring_type", type="string", nullable=true)
     */
    private $recurringType;

    /**
     * @var string
     * @ORM\Column(name="recurring_time", type="string", nullable=true)
     */
    private $recurringTime;

    /**
     * @var string
     * @ORM\Column(name="price_currencies", type="string", nullable=true)
     */
    private $priceCurrencies;

    /**
     * @var string
     * @ORM\Column(name="statement_display_name", type="string", nullable=true)
     */
    private $statementDisplayName;

    /**
     * Set feeItem
     *
     * @param \CoreBundle\Entity\FeeItem $feeItem
     *
     * @return CardProgramFeeItem
     */
    public function setFeeItem(\CoreBundle\Entity\FeeItem $feeItem = null)
    {
        $this->feeItem = $feeItem;

        return $this;
    }

    /**
     * Get feeItem
     *
     * @return \CoreBundle\Entity\FeeItem
     */
    public function getFeeItem()
    {
        return $this->feeItem;
    }

    /**
     * Set cardProgram
     *
     * @param \CoreBundle\Entity\CardProgram $cardProgram
     *
     * @return CardProgramFeeItem
     */
    public function setCardProgram(\CoreBundle\Entity\CardProgram $cardProgram = null)
    {
        $this->cardProgram = $cardProgram;

        return $this;
    }

    /**
     * Get cardProgram
     *
     * @return \CoreBundle\Entity\CardProgram
     */
    public function getCardProgram()
    {
        return $this->cardProgram;
    }

    /**
     * Set feeToCusFixed
     *
     * @param integer $feeToCusFixed
     *
     * @return CardProgramFeeItem
     */
    public function setFeeToCusFixed($feeToCusFixed)
    {
        $this->feeToCusFixed = $feeToCusFixed;

        return $this;
    }

    /**
     * Get feeToCusFixed
     *
     * @return integer
     */
    public function getFeeToCusFixed()
    {
        return $this->feeToCusFixed;
    }

    /**
     * Set feeToCusRatio
     *
     * @param string $feeToCusRatio
     *
     * @return CardProgramFeeItem
     */
    public function setFeeToCusRatio($feeToCusRatio)
    {
        $this->feeToCusRatio = $feeToCusRatio;

        return $this;
    }

    /**
     * Get feeToCusRatio
     *
     * @return string
     */
    public function getFeeToCusRatio()
    {
        return $this->feeToCusRatio;
    }

    /**
     * Set feeToBPFixed
     *
     * @param integer $feeToBPFixed
     *
     * @return CardProgramFeeItem
     */
    public function setFeeToBPFixed($feeToBPFixed)
    {
        $this->feeToBPFixed = $feeToBPFixed;

        return $this;
    }

    /**
     * Get feeToBPFixed
     *
     * @return integer
     */
    public function getFeeToBPFixed()
    {
        return $this->feeToBPFixed;
    }

    /**
     * Set feeToBPRatio
     *
     * @param string $feeToBPRatio
     *
     * @return CardProgramFeeItem
     */
    public function setFeeToBPRatio($feeToBPRatio)
    {
        $this->feeToBPRatio = $feeToBPRatio;

        return $this;
    }

    /**
     * Get feeToBPRatio
     *
     * @return string
     */
    public function getFeeToBPRatio()
    {
        return $this->feeToBPRatio;
    }

    /**
     * Set applyTo
     *
     * @param string $applyTo
     *
     * @return CardProgramFeeItem
     */
    public function setApplyTo($applyTo)
    {
        $this->applyTo = $applyTo;

        return $this;
    }

    /**
     * Get applyTo
     *
     * @return string
     */
    public function getApplyTo()
    {
        return $this->applyTo;
    }

    /**
     * Set measure
     *
     * @param float $measure
     *
     * @return CardProgramFeeItem
     */
    public function setMeasure($measure)
    {
        $this->measure = $measure;

        return $this;
    }

    /**
     * Get measure
     *
     * @return float
     */
    public function getMeasure()
    {
        return $this->measure;
    }

    /**
     * Set measureUnit
     *
     * @param string $measureUnit
     *
     * @return CardProgramFeeItem
     */
    public function setMeasureUnit($measureUnit)
    {
        $this->measureUnit = $measureUnit;

        return $this;
    }

    /**
     * Get measureUnit
     *
     * @return string
     */
    public function getMeasureUnit()
    {
        return $this->measureUnit;
    }

    /**
     * Set recurringType
     *
     * @param string $recurringType
     *
     * @return CardProgramFeeItem
     */
    public function setRecurringType($recurringType)
    {
        $this->recurringType = $recurringType;

        return $this;
    }

    /**
     * Get recurringType
     *
     * @return string
     */
    public function getRecurringType()
    {
        return $this->recurringType;
    }

    /**
     * Set recurringTime
     *
     * @param string $recurringTime
     *
     * @return CardProgramFeeItem
     */
    public function setRecurringTime($recurringTime)
    {
        $this->recurringTime = $recurringTime;

        return $this;
    }

    /**
     * Get recurringTime
     *
     * @return string
     */
    public function getRecurringTime()
    {
        return $this->recurringTime;
    }

    /**
     * Set priceCurrencies
     *
     * @param string $priceCurrencies
     *
     * @return CardProgramFeeItem
     */
    public function setPriceCurrencies($priceCurrencies)
    {
        $this->priceCurrencies = $priceCurrencies;

        return $this;
    }

    /**
     * Get priceCurrencies
     *
     * @return string
     */
    public function getPriceCurrencies()
    {
        return $this->priceCurrencies;
    }

    /**
     * Set statementDisplayName
     *
     * @param string $statementDisplayName
     *
     * @return CardProgramFeeItem
     */
    public function setStatementDisplayName($statementDisplayName)
    {
        $this->statementDisplayName = $statementDisplayName;

        return $this;
    }

    /**
     * Get statementDisplayName
     *
     * @return string
     */
    public function getStatementDisplayName()
    {
        return $this->statementDisplayName;
    }
}
