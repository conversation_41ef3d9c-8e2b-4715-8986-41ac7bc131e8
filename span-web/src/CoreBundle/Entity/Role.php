<?php
namespace CoreBundle\Entity;
/**
 * Created by PhpStorm.
 * User: dell
 * Date: 2017/2/11
 * Time: 14:22
 */

use CoreBundle\Utils\Bundle;
use CoreBundle\Utils\Util;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\Mapping as ORM;
use FisBundle\FisBundle;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;
use Symfony\Component\Validator\Constraints as Assert;
use SalexUserBundle\Entity\User;
/**
 * Class Role
 * @ORM\Table(name="role")
 * @ORM\Entity(repositoryClass="CoreBundle\Repository\RoleRepository")
 * @package CoreBundle\Entity
 */
class Role
{
    const ROLE_CONSUMER               = 'Consumer';
    const ROLE_ADMIN                  = 'Admin';
    const ROLE_MASTER_ADMIN           = 'MasterAdmin';
    const ROLE_API_INVOKER            = 'API Invoker';
    const ROLE_AFFILIATE              = 'Affiliate';
    const ROLE_CONSUMER_SERVICE_AGENT = 'Customer Service Agent';
    const ROLE_PROGRAM_OWNER          = 'Program Owner';
    const ROLE_PLATFORM_OWNER         = 'Platform Owner';
    const ROLE_KPI_USER               = 'Fis KPI User';
    const ROLE_CORPORATE_USER         = 'Corporate User';
    const ROLE_MARKETING_PARTNER      = 'Marketing Partner';
    const ROLE_MARKETING              = 'Marketing';

    const ROLE_CLF_DISPENSARY_ADMIN = 'UTC Dispensary Admin';
    const ROLE_CLF_BANK_ADMIN = 'UTC Bank Admin';
    const ROLE_CLF_VENDOR_ADMIN = 'UTC Vendor Admin';

    const ROLE_UTC_DISPENSARY_ADMIN = 'UTC Dispensary Admin';
    const ROLE_UTC_BANK_ADMIN = 'UTC Bank Admin';
    const ROLE_UTC_VENDOR_ADMIN = 'UTC Vendor Admin';

    const ROLE_TRANSFER_MEX_ADMIN = 'TransferMex Admin';
    const ROLE_TRANSFER_MEX_DEPARTMENT= 'TransferMex Department';
    const ROLE_TRANSFER_MEX_AGENT = 'TransferMex Agent';
    const ROLE_TRANSFER_MEX_CAPTURE = 'TransferMex Capture';
    const ROLE_TRANSFER_MEX_EMPLOYER = 'TransferMex Employer';
    const ROLE_TRANSFER_MEX_MEMBER = 'TransferMex Member';
    const ROLE_TRANSFER_MEX_RECIPIENT = 'TransferMex Recipient';
    const ROLE_TRANSFER_MEX_EMPLOYER_ADMIN = 'TransferMex Employer Admin';
    const ROLE_TRANSFER_MEX_EMPLOYER_ADMIN_ACH = 'TransferMex ACH Employer Admin';


    const ROLE_CASH_ON_WEB_ADMIN = 'CashOnWeb Admin';
    const ROLE_CASH_ON_WEB_AGENT = 'CashOnWeb Agent';
    const ROLE_CASH_ON_WEB_PARTNER = 'CashOnWeb Platform Partner';
    const ROLE_CASH_ON_WEB_MEMBER = 'CashOnWeb Member';
    const ROLE_CASH_ON_WEB_COMPLIANCE = 'CashOnWeb Compliance';
    const ROLE_CASH_ON_WEB_SUPERVISOR = 'CashOnWeb Supervisor';

    const ROLE_WILEN_AGENT = 'Wilen Agent';
    const ROLE_WILEN_ADMIN = 'Wilen Admin';

    // FAAS
    const ROLE_FAAS_MEMBER = 'Faas Member';
    const ROLE_FAAS_ADMIN = 'Faas Admin';
    const ROLE_FAAS_AGENT = 'Faas Agent';
    const ROLE_FAAS_CLIENT = 'Faas Client';
    const ROLE_FAAS_CLIENT_ADMIN = 'Faas Client Admin';

    // Leaflink Role
    const ROLE_LEAF_LINK_EMPLOYER = 'LeafLink Employer';

    // SKUx Roles
    const ROLE_SKUX_CONSUMER = 'SKUx Consumer';
    const ROLE_SKUX_APP_USER = 'SKUx App User';

    // Spendr Role
	// bank admin
	const ROLE_SPENDR_BANK = 'Spendr Bank';
	// spendr admin
	const ROLE_SPENDR_PROGRAM_OWNER = 'Spendr Program Owner';
	// spendr employee
	const ROLE_SPENDR_CUSTOMER_SUPPORT = 'Spendr Customer Support';
	const ROLE_SPENDR_COMPLIANCE = 'Spendr Compliance';
	const ROLE_SPENDR_ACCOUNTANT = 'Spendr Accountant';
	// merchant admin
	const ROLE_SPENDR_MERCHANT_ADMIN = 'Spendr Merchant Admin';
	const ROLE_SPENDR_MERCHANT_MASTER_ADMIN = 'Spendr Merchant Master Admin';
	const ROLE_SPENDR_MERCHANT_OPERATOR_ADMIN = 'Spendr Merchant Operator Admin';
	const ROLE_SPENDR_MERCHANT_MANAGER_ADMIN = 'Spendr Merchant Manager Admin';
	const ROLE_SPENDR_MERCHANT_ACCOUNTANT_ADMIN = 'Spendr Merchant Accountant Admin';
	const ROLE_SPENDR_MERCHANT_OTHER_ADMIN = 'Spendr Merchant Other Admin';
    const ROLE_SPENDR_LOCATION_BALANCE_ADMIN = 'Spendr Location Balance Admin';
	// merchant employee
	const ROLE_SPENDR_MERCHANT_MANAGER = 'Spendr Merchant Manager';
	const ROLE_SPENDR_MERCHANT_ASSISTANT_MANAGER = 'Spendr Assistant Manager';
	const ROLE_SPENDR_CLERKS = 'Spendr Clerks';
	// consumer
	const ROLE_SPENDR_CONSUMER = 'Spendr Consumer';
    const ROLE_SPENDR_GROUP_ADMIN = 'Spendr Group Admin';

    const ORDERS = [
        self::ROLE_MASTER_ADMIN,
        self::ROLE_ADMIN,
        self::ROLE_PLATFORM_OWNER,
        self::ROLE_PROGRAM_OWNER,
        self::ROLE_CONSUMER_SERVICE_AGENT,
        self::ROLE_AFFILIATE,
        self::ROLE_MARKETING,

        self::ROLE_UTC_BANK_ADMIN,
        self::ROLE_UTC_DISPENSARY_ADMIN,
        self::ROLE_UTC_VENDOR_ADMIN,

        self::ROLE_TRANSFER_MEX_ADMIN,
        self::ROLE_TRANSFER_MEX_AGENT,
        self::ROLE_TRANSFER_MEX_EMPLOYER,
        self::ROLE_TRANSFER_MEX_EMPLOYER_ADMIN,
        self::ROLE_TRANSFER_MEX_MEMBER,
        self::ROLE_TRANSFER_MEX_RECIPIENT,

        self::ROLE_FAAS_ADMIN,
        self::ROLE_FAAS_AGENT,
        self::ROLE_FAAS_CLIENT,
        self::ROLE_FAAS_CLIENT_ADMIN,
        self::ROLE_FAAS_MEMBER,

        self::ROLE_SPENDR_PROGRAM_OWNER,
        self::ROLE_SPENDR_BANK,
        self::ROLE_SPENDR_GROUP_ADMIN,
        self::ROLE_SPENDR_MERCHANT_ADMIN,
        self::ROLE_SPENDR_MERCHANT_MANAGER,
        self::ROLE_SPENDR_MERCHANT_ASSISTANT_MANAGER,
        self::ROLE_SPENDR_CLERKS,
		self::ROLE_SPENDR_CUSTOMER_SUPPORT,
		self::ROLE_SPENDR_COMPLIANCE,
		self::ROLE_SPENDR_ACCOUNTANT,
		self::ROLE_SPENDR_CONSUMER,
		self::ROLE_SPENDR_MERCHANT_MASTER_ADMIN,
		self::ROLE_SPENDR_MERCHANT_OPERATOR_ADMIN,
		self::ROLE_SPENDR_MERCHANT_MANAGER_ADMIN,
		self::ROLE_SPENDR_MERCHANT_ACCOUNTANT_ADMIN,
		self::ROLE_SPENDR_MERCHANT_OTHER_ADMIN,
        self::ROLE_SPENDR_LOCATION_BALANCE_ADMIN,

        self::ROLE_SKUX_CONSUMER,
        self::ROLE_SKUX_APP_USER,

        self::ROLE_API_INVOKER,
        self::ROLE_MARKETING_PARTNER,
        self::ROLE_CONSUMER,
    ];

    const VIEWER = 'VIEWER';
    const EDITOR = 'EDITOR';

    /**
     * @param $idOrName
     *
     * @return Role|null
     */
    public static function find($idOrName)
    {
        if (is_numeric($idOrName)) {
            $where = ['id' => $idOrName];
        } else {
            $where = ['name' => $idOrName];
        }
        $rs = Util::em()->getRepository(\CoreBundle\Entity\Role::class)->findBy($where);
        return $rs ? $rs[0] : null;
    }

    public static function isMember($name)
    {
        if (in_array($name, [
            self::ROLE_CONSUMER,
            self::ROLE_SPENDR_CONSUMER,
        ])) {
            return true;
        }
        if (Util::hasSuffix($name, [
            ' Consumer',
            ' Member',
        ])) {
            return true;
        }
        return false;
    }

    public static function filterRolesInPlatform(array $roleNames, Platform $platform = null)
    {
        $platform = $platform ?? Util::platform();
        if (!$platform) {
            return $roleNames;
        }

        $roles = [
            self::ROLE_MASTER_ADMIN,
            self::ROLE_ADMIN,
            self::ROLE_PLATFORM_OWNER,
            self::ROLE_PROGRAM_OWNER,
            self::ROLE_CONSUMER_SERVICE_AGENT,
            self::ROLE_AFFILIATE,
            self::ROLE_API_INVOKER,
            self::ROLE_CONSUMER,
        ];
        $pRoles = Bundle::getAllRoles();
        foreach ($pRoles as $pRole) {
            if (!in_array($pRole, $roles)) {
                $roles[] = $pRole;
            }
        }

        return array_values(array_intersect($roleNames, $roles));
    }

    public function is($name)
    {
        return $this->getName() === $name;
    }

    public function in($names)
    {
        return in_array($this->getName(), $names);
    }

    public function getSection()
    {
        $sections = $this->getSections();
        return $sections->isEmpty() ? null : $sections[0];
    }

    public function getSectionNames()
    {
        $names = [[]];
        /** @var Section $section */
        foreach ($this->getSections() as $section) {
            $sns = explode(';', $section->getSectionName());
            foreach ($sns as $sn) {
                if (!$sn) {
                    continue;
                }
                $matches = [];
                preg_match('/^(.+?)(@\d+)?$/', $sn, $matches);
                if (isset($matches[1])) {
                    $names[] = [$matches[1]];
                }
            }
            $names[] = $section->getEditableModules();
        }
        return array_merge(...$names);
    }

    public function getEditableModules()
    {
        $names = [[]];
        /** @var Section $section */
        foreach ($this->getSections() as $section) {
            $names[] = $section->getEditableModules();
        }
        return array_merge(...$names);
    }

    public function getCustomName()
    {
        $name = $this->getName();
        if (Bundle::isFIS()) {
            $map = FisBundle::getInternalRoleNamesMap();
            return $map[$name] ?? $name;
        }
        return $name;
    }

    /**
     * @var integer $id
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @var string $name
     *
     * @ORM\Column(name="name", type="string", length=32, unique=true)
     * @Assert\NotBlank()
     */
    private $name;

    /**
     * @ORM\ManyToMany(targetEntity="SalexUserBundle\Entity\User", mappedBy="teams")
     * @ORM\JoinTable(name="user_role",
     *      joinColumns={@ORM\JoinColumn(name="role_id", referencedColumnName="id", onDelete="cascade")},
     *      inverseJoinColumns={@ORM\JoinColumn(name="user_id", referencedColumnName="id", unique=false)}
     *     )
     * @Serializer\Exclude()
     */
    private $users;

    /**
     * @ORM\OneToMany(targetEntity="Section",mappedBy="role")
     * @Serializer\Exclude()
     */
    private $sections;

    /**
     * @var string $name
     *
     * @ORM\Column(name="authname", type="string", length=2555, nullable=true)
     */
    private $authname;

    /**
     * @var string $name
     *
     * @ORM\Column(name="authsname", type="string", length=2555, nullable=true)
     */
    private $authsname;

    //Begin Add by Bob Wen Bao on 2017-04-25 to handle authority on action level
    /**
     * @var string $authority
     * @ORM\Column(name="authority", type="string", nullable=true)
     */
    private $authority;
    //End

    /**
     * @var string $defaultUrl
     * @ORM\Column(name="default_url", type="string", nullable=true, options={"comment":"Default page after logging in"})
     */
    private $defaultUrl;

    public function __construct()
    {
        $this->users = new ArrayCollection();
        $this->sections = new ArrayCollection();
    }


    /**
     * Get id
     *
     * @return integer
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set name
     *
     * @param string $name
     *
     * @return Role
     */
    public function setName($name)
    {
        $this->name = $name;

        return $this;
    }

    /**
     * Get name
     *
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * Add user
     *
     * @param \SalexUserBundle\Entity\User $user
     *
     * @return Role
     */
    public function addUser(\SalexUserBundle\Entity\User $user)
    {
        $this->users[] = $user;

        return $this;
    }

    /**
     * Remove user
     *
     * @param \SalexUserBundle\Entity\User $user
     */
    public function removeUser(\SalexUserBundle\Entity\User $user)
    {
        $this->users->removeElement($user);
    }

    /**
     * Get users
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getUsers()
    {
        return $this->users;
    }

    /**
     * Add section
     *
     * @param \CoreBundle\Entity\Section $section
     *
     * @return Role
     */
    public function addSection(\CoreBundle\Entity\Section $section)
    {
        $this->sections[] = $section;

        return $this;
    }

    /**
     * Remove section
     *
     * @param \CoreBundle\Entity\Section $section
     */
    public function removeSection(\CoreBundle\Entity\Section $section)
    {
        $this->sections->removeElement($section);
    }

    /**
     * Get sections
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getSections()
    {
        return $this->sections;
    }

    /**
     * Set authname
     *
     * @param string $authname
     *
     * @return Role
     */
    public function setAuthname($authname)
    {
        $this->authname = $authname;

        return $this;
    }

    /**
     * Get authname
     *
     * @return string
     */
    public function getAuthname()
    {
        return $this->authname;
//        return explode('@',$this->authname);
    }

    /**
     * Set authsname
     *
     * @param string $authsname
     *
     * @return Role
     */
    public function setAuthsname($authsname)
    {
        $this->authsname = $authsname;

        return $this;
    }

    /**
     * Get authsname
     *
     * @return string
     */
    public function getAuthsname()
    {
        return $this->authsname;
    }

    /**
     * Set authority
     *
     * @param string $authority
     *
     * @return Role
     */
    public function setAuthority($authority)
    {
        $this->authority = $authority;

        return $this;
    }

    /**
     * Get authority
     *
     * @return string
     */
    public function getAuthority()
    {
        return $this->authority;
    }

    /**
     * Set defaultUrl
     *
     * @param string $defaultUrl
     *
     * @return Role
     */
    public function setDefaultUrl($defaultUrl)
    {
        $this->defaultUrl = $defaultUrl;

        return $this;
    }

    /**
     * Get defaultUrl
     *
     * @return string
     */
    public function getDefaultUrl()
    {
        return $this->defaultUrl;
    }
}
