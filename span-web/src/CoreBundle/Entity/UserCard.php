<?php

namespace CoreBundle\Entity;

use Api<PERSON><PERSON>le\Entity\ApiEntityInterface;
use ApiB<PERSON>le\Entity\Coin;
use Carbon\Carbon;
use CoreBundle\Entity\FakerTrait\UserCardFakerTrait;
use CoreBundle\Services\CardAccountActivity;
use CoreBundle\Services\Common\CardService;
use CoreBundle\Services\PdoService;
use CoreBundle\Utils\Bundle;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Service;
use CoreBundle\Utils\Util;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\ORM\Mapping\Index;
use FaasBundle\Entity\UserCardFaasTrait;
use Gedmo\Mapping\Annotation as Gedmo;
use Gedmo\SoftDeleteable\Traits\SoftDeleteableEntity;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;
use LeafLinkB<PERSON>le\Entity\UserCardLeafLinkTrait;
use SpendrBundle\Entity\Location;
use SpendrBundle\Entity\PlaidIdentity;
use malkusch\lock\exception\LockReleaseException;
use PortalBundle\Exception\PortalException;
use SalexUserBundle\Entity\User;
use SkuxBundle\Entity\UserCardSkuxTrait;
use Stringy\Stringy;
use TransferMexBundle\Entity\UserCardTransferMexTrait;
use UsUnlockedBundle\Entity\UserCardPrivacyTrait;
use UsUnlockedBundle\Services\Privacy\PrivacyAPI;

/**
 * UserCard
 *
 * @ORM\Table(name="user_card", indexes={
 *     @Index(name="account_number_idx", columns={"account_number"}),
 *     @Index(name="user_id_idx", columns={"user_id"}),
 *     @Index(name="card_id_idx", columns={"card_id"}),
 *     @Index(name="nick_name", columns={"nick_name"}),
 *     @Index(name="card_provider", columns={"card_provider"}),
 *     @Index(name="deactivating_idx", columns={"card_id", "status", "deactivating_at"}),
 *     @Index(name="user_card_idx", columns={"card_id", "user_id"}),
 * }, options={"comment":"User's all cards"})
 * @ORM\Entity(repositoryClass="CoreBundle\Repository\UserCardRepository")
 * @Gedmo\SoftDeleteable()
 */
class UserCard extends BaseEntity implements ApiEntityInterface
{
    use UserCardFakerTrait;
    use UserCardPrivacyTrait;
    use UserCardLeafLinkTrait;
    use UserCardTransferMexTrait;
    use UserCardFaasTrait;
    use UserCardSkuxTrait;

    public const CARD_PROVIDER_RAIN = 'RAIN';
    public const CARD_PROVIDER_PRIVACY = 'Privacy';

    public const STATUS_ACTIVE = 'active';
    public const STATUS_PENDING = 'pending';
    public const STATUS_INACTIVE = 'inactive';
    public const STATUS_CLOSED = 'closed';

    public const REPLACE_STATUS_NONE = null;
    public const REPLACE_STATUS_UNLOADING = 'unloading';
    public const REPLACE_STATUS_REPLACING = 'replacing';
    public const REPLACE_STATUS_LOADING = 'loading';

    public const PENDING_FEE_UNLOAD = 'Pending unload fee';
    public const PENDING_FEE_REPLACEMENT = 'Pending replace card fee';
    public const PENDING_FEE_MEMBERSHIP_OR_LOAD = 'Pending membership or load fee';

    // spendr
    public const PENDING_FEE_BACK_TO_PARTNER = 'Pending back to partner fee';
    public const PENDING_ACH_RETURN_FEE_BACK_TO_PARTNER = 'Pending back to partner ach return fee';
    public const OFFSET_PENDING_FEE_BY_PREFUND_LOAD = 'Prefund';
    public const OFFSET_PENDING_FEE_BY_PROMOTION_LOAD = 'Promotion';
    public const OFFSET_PENDING_FEE_BY_REFUND_TXN = 'Refund';

    public const LL_TYPE_MANUAL = "Manual";
    public const LL_TYPE_YODLEE = "Yodlee";
    public const LL_TYPE_PLAID = "Plaid";

    public const TYPE_CHECKING = 'checking';
    public const TYPE_SAVING = 'savings';

    public const DEFAULT_BALANCE_REMIND_THRESHOLD = 1500; // USD

    public const SHIPPING_METHOD_STANDARD = 1;
    public const SHIPPING_METHOD_PRIORITY = 2;
    public const SHIPPING_METHOD_RUSH_DELIVERY = 3;
    public const SHIPPING_METHOD_REGISTERED_MAIL = 4;

    public const NATIVE_STATUS_LL_UNVERIFIED = null;
    public const NATIVE_STATUS_LL_VERIFIED = 'verified';

    // Spendr
    public const CONSUMER_DAILY_MAX_SPEND_LIMIT = 100000;
    // manually linked card has total instant load limit $200, more than $200 will be prefund load
    public const CONSUMER_MANUAL_LINK_INSTANT_LIMIT = 20000;
    // non-manual card instant load limit
    public const CONSUMER_AUTH_INSTANT_LIMIT = 100000;

    public function getName()
    {
        $s = '';
        $card = $this->getCard();
        if ($card) {
            $s = $card->getFullName();

            $pan = $this->getPan('mask');
            if ($pan) {
                $s .= ' (' . $pan . ')';
            }
        }
        return $s;
    }

    public function getAccountName()
    {
        $s = '';
        $card = $this->getCard();
        if ($card) {
            $s = $card->getFullName();

            $an = $this->getAccountNumber();
            if ($an) {
                $s .= ' (' . $an . ')';
            }
        }
        return $s;
    }

    public function getFullName()
    {
        $s = '';
        $card = $this->getCard();
        if ($card) {
            $s = $card->getFullName();

            $rs = [];
            $pan = $this->getPan('mask');
            if ($pan) {
                $rs[] = $pan;
            }

            $an = $this->getAccountNumber();
            if ($an) {
                $rs[] = $an;
            }

            if ($rs) {
                $s .= ' (' . implode(', ', $rs) . ')';
            }
        }
        return $s;
    }

    public function getCardTypeName()
    {
        $cp = Util::field($this->getCardProgram());
        $type = $this->getType();
        if ($type) {
            if ($type === PrivacyAPI::CARD_TYPE_SINGLE_USE) {
                $type = 'One-Time';
            } else if ($type === PrivacyAPI::CARD_TYPE_MERCHANT_LOCKED) {
                $type = 'Store Locked';
            } else if ($type === PrivacyAPI::CARD_TYPE_UNLOCKED) {
                $type = '';
            } else if ($type === PrivacyAPI::CARD_TYPE_DUMMY) {
                $type = 'Account';
            } else {
                $type = Stringy::create($type)->toTitleCase()->replace('_', ' ')->toString();
            }
        }
        return  $type === 'Account' ? ($cp . ' ' . $type) : implode(' ', array_filter([
            $cp,
            $type,
            'Card',
        ]));
    }

    public function getSnap($time)
    {
        return $this->getSnaps()->filter(function (UserCardSnap $snap) use ($time) {
            return $snap->getTime() === $time;
        })->first();
    }

    public function getLast4()
    {
        $pan = $this->getPan();
        if (!$pan) {
            return '';
        }
        if (strlen($pan) <= 4) {
            return $pan;
        }
        return substr($pan, -4);
    }

    public function everInitiated()
    {
        return $this->hasLoadWithStatus([
            UserCardLoad::LOAD_STATUS_INITIATED,
            UserCardLoad::LOAD_STATUS_PENDING,
            UserCardLoad::LOAD_STATUS_REFUNDED,
            UserCardLoad::LOAD_STATUS_CONFIRMED,
            UserCardLoad::LOAD_STATUS_RECEIVED,
            UserCardLoad::LOAD_STATUS_LOADED,
            UserCardLoad::LOAD_STATUS_ERROR,
        ]);
    }

    public function everPaid()
    {
        return $this->hasLoadWithStatus([
            UserCardLoad::LOAD_STATUS_REFUNDED,
            UserCardLoad::LOAD_STATUS_CONFIRMED,
            UserCardLoad::LOAD_STATUS_RECEIVED,
            UserCardLoad::LOAD_STATUS_LOADED,
            UserCardLoad::LOAD_STATUS_ERROR,
        ]);
    }

    public function everLoaded()
    {
        $meta = Util::s2j($this->getMeta());
        if (isset($meta['everLoaded']) && $meta['everLoaded']) {
            return true;
        }
        return $this->hasLoadWithStatus([
            UserCardLoad::LOAD_STATUS_RECEIVED,
            UserCardLoad::LOAD_STATUS_LOADED,
        ]);
    }

    public function toApiArray(bool $extra = false): array
    {
        $r = [
            'id' => $this->getId(),
            'hash' => $this->getHash(),
            'accountNumber' => $this->getAccountNumber(),
            'pan' => $this->getPan('mask'),
            'holder' => $this->getHolder(),
            'balance' => $this->getBalance(),
            'currency' => $this->getCurrency(),
            'coin' => Coin::create($this->getBalance(), $this->getCurrency())->toApiArray(),
            'status' => $this->getStatus(),
            'issued' => (boolean)$this->getIssued(),
        ];

        $card = $this->getCard();
        if ($extra) {
            $r['card'] = $card->toApiArray();

            /** @var UserCardTransaction $lastTransaction */
            $lastTransaction = $this->getLastTransaction();
            $r['lastTransaction'] = $lastTransaction ? $lastTransaction->toApiArray() : null;

            if (!Util::isAPI()) {
                $request = Util::request();
                $other = Util::renderData('@Admin/Report/cardAccountActivity/data.html.twig', [
                    'item' => $this,
                    'point'=> $request->get('time-point')
                ]);
                $r = array_merge($r, $other);
            }
        }

        $r['status'] = $this->getStatus();

        $cp = $card->getCardProgram();
        if ($cp->isEsSolo()) {
            $r['nativeStatus'] = $this->getNativeStatus();
        } else if ($this->isUsUnlocked() || $this->isCashOnWeb()) {
            $r['sandbox'] = $this->isSandbox();
            $r['type'] = $this->getType();
            $r['spendLimitDuration'] = $this->getSpendLimitDuration();

            $sl = $this->getSpendLimit();
            $r['spendLimit'] = Money::format($sl ?? 1, 'USD', false);

            $slActual = Util::meta($this, 'privacySpendLimit');
            if ($slActual !== null && (int)$slActual !== (int)$sl) {
                $slDuration = Util::meta($this, 'privacySpendLimitDuration') ?: $this->getSpendLimitDuration();
                $slDuration = PrivacyAPI::getSpendLimitDurationName($slDuration);
                $r['spendLimit'] .= ' (Actual: ' . Money::format($slActual, 'USD', false)
                                    . '/' . $slDuration . ')';
            }

            $merchant = $this->getMerchant();
            if ($merchant) {
                $r['merchant'] = $merchant->toApiArray();
            }
        }

        return $r;
    }

    public function getActivityTypes()
    {
        return CardAccountActivity::check($this);
    }

    public function calculateBalanceChangedAt()
    {
        $loadAt = null;
        $load = $this->getLastCompletedLoad(null);
        if ($load) {
            $loadAt = $load->getLoadAt();
            $loadAt = $loadAt ? Carbon::instance($loadAt) : null;
        }
        $txnAt = null;
        $usage = $this->getLastTransaction();
        if ($usage) {
            $txnAt = $usage->getTxnTime();
            $txnAt = $txnAt ? Carbon::instance($txnAt) : null;
        }
        if ($loadAt && $txnAt) {
            return $loadAt->gt($txnAt) ? $loadAt : $txnAt;
        }
        if ($loadAt && !$txnAt) {
            return $loadAt;
        }
        if (!$loadAt && $txnAt) {
            return $txnAt;
        }
        return null;
    }

    public function isApiActive()
    {
        if ($this->isUsUnlockedLegacy()) {
            return $this->isMasterCard();
        }
        return true;
    }

    public function isVisa()
    {
        return $this->getCard()->getCardType()->isVisa();
    }

    public function isMasterCard()
    {
        return $this->getCard()->getCardType()->isMasterCard();
    }

    public function isReloadable()
    {
        return $this->getCard()->getCardType()->isReloadable();
    }

    public function isUsUnlocked()
    {
        return $this->getCard()->getCardProgram()->isUsUnlocked();
    }

    public function isUsUnlockedLegacy()
    {
        return $this->getCard()->getCardProgram()->isUsUnlockedLegacy();
    }

    public function isCashOnWeb()
    {
        return $this->getCard()->getCardProgram()->isCashOnWeb();
    }

    public function isSpendr()
    {
        return $this->getCard()->getCardProgram()->isSpendr();
    }

    public function isTransferMex()
    {
        return $this->getCard()->getCardProgram()->isTransferMex();
    }

    public function isEsSolo()
    {
        return $this->getCard()->getCardProgram()->isEsSolo();
    }

    public function isEsSoloUba()
    {
        return $this->getCard()->getCardProgram()->isEsSoloUba();
    }

    public function isEsSoloGtBank()
    {
        return $this->getCard()->getCardProgram()->isEsSoloGtBank();
    }

    /**
     * @return UserCardTransaction|null
     */
    public function getLastTransaction()
    {
        if ($this->isUsUnlocked()) {
            return $this->getUsages()->last();
        }

        $transactions = Util::em()->createQuery( 'SELECT uct FROM CoreBundle:UserCardTransaction uct
                WHERE (uct.userCard = :userCard) AND (uct.tranDesc LIKE :tranCode)order by uct.txnTime desc')
            ->setParameter('userCard', $this)
            ->setParameter('tranCode', Transaction::POS_PURCHASE.'%')
            ->getResult();
        return array_first($transactions);
    }

    public function hasLoadWithStatus(array $statuses)
    {
        $loads = $this->getLoads(UserCardLoad::TYPE_LOAD_CARD);
        /** @var UserCardLoad $load */
        foreach ($loads as $load) {
            if (in_array($load->getLoadStatus(), $statuses, true)) {
                return true;
            }
        }
        return false;
    }

    public function ensureLoad()
    {
        if (Util::isAPI()) {
            $loadId = Util::request()->get('load');
            if (!$loadId) {
                $loadId = Util::request()->get('loadId');
            }
            if ($loadId) {
                $load = Util::em()->getRepository(\CoreBundle\Entity\UserCardLoad::class)->find($loadId);
                $uc = $load->getUserCard();
                if (!Util::eq($uc, $this)) {
                    throw new PortalException('Invalid load parameter!');
                }
                return $load;
            }
        }

        $load = $this->getLatestValidLoad();
        if ($load && $load->getStatus() === UserCardLoad::STATUS_UNKNOWN && !$load->getInitializedAt()) {
            return $load;
        }

        $load = new UserCardLoad();
        $load->setType(UserCardLoad::TYPE_LOAD_CARD);
        $load->setUserCard($this);
        $load->setInitialAmount(0);
        $load->setInitialCurrency('USD');
        $load->setReload($this->getUser()->everLoaded());
        Util::persist($load);

        return $load;
    }

    public function ensureUnload()
    {
        if (Util::isAPI()) {
            $loadId = Util::request()->get('unload');
            if (!$loadId) {
                $loadId = Util::request()->get('loadId');
            }
            if ($loadId) {
                $load = Util::em()->getRepository(\CoreBundle\Entity\UserCardLoad::class)->find($loadId);
                $uc = $load->getUserCard();
                if (!Util::eq($uc, $this)) {
                    throw new PortalException('Invalid unload parameter!');
                }
                return $load;
            }
        }

        $load = $this->getLatestValidUnLoad();
        if ($load && $load->getStatus() === UserCardLoad::STATUS_UNKNOWN && !$load->getInitializedAt()) {
            return $load;
        }

        $load = new UserCardLoad();
        $load->setType(UserCardLoad::TYPE_UNLOAD);
        $load->setUserCard($this);
        $load->setInitialAmount(0);
        $load->setInitialCurrency('USD');
        if (!$this->isSpendr()) {
			$load->setReload($this->getUser()->everLoaded());
		}
        Util::persist($load);

        return $load;
    }

    public function getLatestValidUnLoad()
    {
        $loads = Util::asc($this->getLoads(UserCardLoad::TYPE_UNLOAD));
        for ($i = $loads->count() - 1; $i >= 0; $i--) {
            /** @var UserCardLoad $load */
            $load = $loads->get($i);
            if ($load->getServerKey() !== 'Generated-from-wx') {
                return $load;
            }
        }
        return null;
    }


    public function getLatestValidLoad()
    {
        $loads = Util::asc($this->getLoads(UserCardLoad::TYPE_LOAD_CARD));
        for ($i = $loads->count() - 1; $i >= 0; $i--) {
            /** @var UserCardLoad $load */
            $load = $loads->get($i);
            if ($load->getServerKey() !== 'Generated-from-wx') {
                return $load;
            }
        }
        return null;
    }

    /**
     * @param Transaction $transaction
     * @return UserCardLoad|null
     */
    public function getLoadByTransaction(Transaction $transaction)
    {
        $loads = $this->getLoads()->filter(function (UserCardLoad $load) use ($transaction) {
            $trans = $load->getTransaction();
            return $trans && $trans->getId() === $transaction->getId();
        });
        if ($loads->isEmpty()) {
            return null;
        }
        return $loads->last();
    }

    public function isImported()
    {
        $meta = Util::s2j($this->getMeta());
        return !empty($meta['everLoaded']);
    }

    /**
     * @param string $fromPage
     * @param bool   $loadLocalBalance
     * @param array  $params
     *
     * @return ExternalInvoke
     * @throws PortalException
     */
    public function createCard($fromPage = '', $loadLocalBalance = true, $params = [])
    {
        if ($this->getAccountNumber()) {
            if ($this->isImported()) {
                $ei = new ExternalInvoke();
                $ei->setStatus(ExternalInvoke::STATUS_SUCCESS);
                return $ei;
            }
            $type = null;
            if ($this->isEsSolo()) {
                $type = ExternalInvoke::TYPE_GTP_REGISTER;
            } else if ($this->isUsUnlockedLegacy()) {
                $type = ExternalInvoke::TYPE_FIRST_VIEW_CREATE_CARD;
            }

            if ($type) {
                return ExternalInvoke::findByEntity(self::class, $this->getId(), $type);
            }
            return null;
        }

        $user = $this->getUser();
        if ($this->isEsSolo() && !$user->isIdVerified()) {
            return ExternalInvoke::createTempFailed('User\'s id is not verified yet!');
        }

        try {
            $ei = CardService::createCard($this, $fromPage, $params);
        } catch (PortalException $exception) {
            return ExternalInvoke::createTempFailed($exception->getMessage());
        }

        if ($ei) {
            if ($ei->isFailed()) {
                Util::updateJson($this, 'meta', [
                    'issueError' => $ei->getError(),
                ]);
            } else if ($this->isIssued() && !$this->isDummy()) {
                Service::identify($user);

                if ($loadLocalBalance && $this->isReloadable()) {
                    $this->loadLocalBalance();
                }

                Email::sendWithTemplateToUser($user, Email::TEMPLATE_CARD_ISSUED, [
                    'uc' => $this->getId(),
                    'card' => $this->getCardTypeName(),
                    'action_url' => Util::host() . '/card-management',
                ], $this->getCard()->getCardProgram());
            }
        }
        return $ei;
    }

    public function isDummy()
    {
        return $this->getType() === PrivacyAPI::CARD_TYPE_DUMMY;
    }

    public function isRainCard () {
        if ( ! $this->getAccountNumber() && ! $this->isDummy()) {
            $this->setCardProvider(self::CARD_PROVIDER_RAIN);
            return true;
        }
        return $this->getCardProvider() === self::CARD_PROVIDER_RAIN;
    }

    public function isPrivacyCard() {
        return in_array($this->getCardProvider(), [
            null,
            self::CARD_PROVIDER_PRIVACY,
        ]);
    }

    public function getParsedCardProvider() {
        if ($this->isRainCard()) {
            return self::CARD_PROVIDER_RAIN;
        }
        if ($this->isPrivacyCard()) {
            return self::CARD_PROVIDER_PRIVACY;
        }
        return $this->getCardProvider();
    }

    public function loadLocalBalance()
    {
        Service::sendAsync('/t/cron/load-local-balance/' . $this->getId());
    }

    /**
     * Add load amount to card's balance
     *
     * @param UserCardLoad $load
     * @param string $fromPage
     * @param bool $throwException
     * @return ExternalInvoke
     * @throws \Exception
     */
    public function completeLoad(UserCardLoad $load, $fromPage = '', $throwException = true)
    {
        try {
            $ei = $this->createCard($fromPage);
        } catch (\Exception $exception) {
            Util::updateJson($load->getUserCard(), 'meta', [
                'issueError' => $exception->getMessage(),
            ]);
            if ($throwException) {
                throw $exception;
            }
            Log::exception($exception->getMessage(), $exception);
            $ei = ExternalInvoke::create(ExternalInvoke::TYPE_COMPLETE_LOAD_CARD, [
                'load' => $load->getId(),
            ]);
            $ei->fail($exception->getMessage());
        }

        $this->setCurrency($load->getInitialCurrency())
            ->setPendingFees(null);

        $load->setStatus(UserCardLoad::STATUS_COMPLETED)
            ->setCompletedAt(new \DateTime())
            ->persist();

        return $ei;
    }

    public function getBalanceText()
    {
        $balance = $this->getBalance() ?: 0;
        $currency = $this->getCurrency() ?: 'USD';
        return Money::format($balance, $currency);
    }

    public function getLocalBalanceText()
    {
        $balance = $this->getLocalBalance() ?: 0;
        $currency = $this->getCurrency() ?: 'USD';
        return Money::format($balance, $currency);
    }

    public function getExpireAtText()
    {
        $expireAt = $this->getExpireAt();
        if (!$expireAt) {
            return '';
        }
        return $expireAt->format('m/y');
    }

    /**
     * Get cardNumber. Fetch from firstView API if the card is already issued but card number is still null
     *
     * @return string
     */
    public function getCardNumber()
    {
        if ($this->getIssued()) {
            return CardService::getCardNumber($this);
        }
        return '';
    }

    /**
     * @return null|string
     */
    public function getCvc()
    {
        if ($this->getIssued()) {
            return CardService::getCVC($this);
        }
        return '';
    }

    /**
     * Get expireAt
     *
     * @return \DateTime
     * @throws \InvalidArgumentException
     */
    public function getExpireAt()
    {
        $expireAt = $this->expireAt;
        if (!$expireAt && $this->getIssued()) {
            $expireAt = CardService::getExpireDate($this);
            $this->setExpireAt($expireAt);
            Util::persist($this);
        }

        return $expireAt;
    }

    /**
     * Get holder
     *
     * @param bool $updateIfEmpty
     *
     * @return string
     */
    public function getHolder($updateIfEmpty = false)
    {
        $holder = $this->holder;
        if (!$holder && $this->getIssued() && $updateIfEmpty) {
            $holder = CardService::getHolder($this);
            $this->setHolder($holder);
            Util::persist($this);
        }

        $user = $this->getUser();
        if ($user) {
            return $user->getName();
        }

        return $holder;
    }

    public function isActive() {
        $status = $this->getStatus();
        return !$status || $status === self::STATUS_ACTIVE;
    }

    public function isActiveStatus() {
        return $this->getStatus() === self::STATUS_ACTIVE;
    }

    public function isReallyActive() {
        return CardService::isReallyActive($this);
    }

    public function isClosed() {
        if ($this->getStatus() === self::STATUS_CLOSED) {
            return true;
        }
        $closed = Util::json($this, 'meta', 'closed');
        return !$this->isActive() && $closed === true;
    }

    public function isIssued() {
        return $this->getIssued();
    }

    public function isExpired() {
        $expiredAt = $this->getExpireAt();
        return $expiredAt && Carbon::instance($expiredAt)->isPast();
    }

    public function getExpireReminderArray() {
        $reminders = json_decode($this->getExpireReminder(), true);
        return $reminders ?: [];
    }

    public function getCardProgram()
    {
        $card = $this->getCard();
        if (!$card) {
            return null;
        }
        return $card->getCardProgram();
    }

    public function getPlatform()
    {
        $cp = $this->getCardProgram();
        return $cp ? $cp->getPlatform() : null;
    }

    public function getBalanceOnly()
    {
        return $this->balance;
    }

    /**
     * Get balance
     *
     * @param bool $updateType
     * @return int
     */
    public function getBalance($updateType = false)
    {
        $v = $this->balance ?: 0;
        if ($updateType && $this->getIssued()) {
            $v = CardService::getBalance($this);
            if (null === $v) {
                return null;
            }
            $this->setBalance($v, is_string($updateType) ? $updateType : UserCardBalance::TYPE_MANUAL_REFRESH)
                ->persist();
        }
        return (int)$v;
    }

    public function getBalanceUSD()
    {
        return Money::convert($this->getBalance(), $this->getCurrency(), 'USD');
    }

    public function getBalanceWithPendingPayments(UserCardLoad $except = null)
    {
        $loads = $this->getPendingToLoadLoads(10);
        $balance = $this->getBalanceUSD();
        $id = $except ? $except->getId() : 0;
        /** @var UserCardLoad $l */
        foreach ($loads as $l) {
            if ($l->getId() !== $id) {
                $la = $l->getLoadAmountUSD();
                if ($la) {
                    $balance += $la;
                }
            }
        }
        return $balance;
    }

    /*
     * TODO: Don't forget to update spending limit every time this method is called.
     */
    public function updateBalanceBy($delta, $type, $comment = null, $meta = null, $skipIfNotEnough = false)
    {
        $id = $this->getId();
        $mutex = Util::mutex('user_card_' . $id, 60);
        try {
            return $mutex->synchronized(function () use ($id, $delta, $type, $comment, $meta, $skipIfNotEnough) {
                $oldBalance = PdoService::queryUserCardBalance($id, $this->getBalance());
                $this->setBalanceOnly($oldBalance);

                $balance = $oldBalance + $delta;

                if ($delta < 0 && $balance < 0 && $skipIfNotEnough) {
                    if ($oldBalance <= 0) {
                        return 0;
                    }
                    $delta = -$oldBalance;
                    $balance = 0;
                }

                $this->setBalance($balance, $type, $comment, $meta);
                Util::persist($this);
                return $delta;
            });
        } catch (LockReleaseException $unlockException) {
            $exception = $unlockException->getCodeException();
            if ($exception) {
                throw $exception;
            }
            // ignore other unlock exceptions
            Log::error('Unlock error: ' . $unlockException->getMessage());
            return $delta;
        }
    }

    public function setBalanceOnly($balance)
    {
        $this->balance = $balance;
        return $this;
    }

    /**
     * Set balance
     *
     * @param integer $balance
     *
     * @param string  $type
     * @param null    $comment
     * @param null    $meta
     *
     * @return UserCard
     * @throws \Exception
     */
    public function setBalance($balance, $type = UserCardBalance::TYPE_LOAD_CARD, $comment = null, $meta = null)
    {
        if ($this->id && (int)($this->balance) !== (int)$balance && ($this->balance || $balance)) {
            $log = new UserCardBalance();
            $log->setUserCard($this);
            $log->setSource(UserCardBalance::SOURCE_CARD);
            $log->setType($type);
            $log->setComment(Util::cleanUtf8String($comment));
            $log->setPan($this->getPan());
            $log->setCurrentBalance($balance ?: 0);
            $log->setPreviousBalance($this->balance ?: 0);
            if ($meta) {
                try {
                    if (is_object($meta)) {
                        if ($meta instanceof UserCardLoad && $meta->getId()) {
                            $log->setUserCardLoad($meta);
                        } else if ($meta instanceof UserCardTransaction && $meta->getId()) {
                            $log->setUserCardTransaction($meta);
                        } else if ($meta instanceof Transfer && $meta->getId()) {
                            $log->setTransfer($meta);
                        }
                        $meta = [
                            'entity' => (array)(new EntitySignature($meta)),
                        ];
                    }
                    if (is_array($meta)) {
                        Util::updateMeta($log, $meta, false);
                    }
                } catch (\Exception $ex) {
                    Log::debug('Failed to fill balance change meta: ' . $ex->getMessage(), [
                        'uc' => $this->getId(),
                        'balance' => $balance,
                        'type' => $type,
                        'comment' => $comment,
                    ]);
                }
            }
            Util::em()->persist($log);
        }

        if ((int)($this->balance) !== (int)$balance) {
            $this->setBalanceChangedAt(new \DateTime());
        }

        $this->balance = $balance;

        try {
            if ($this->getIsWarning()) {
                $warn = $this->getWarningValue();
                if ($warn !== null && $warn < $balance) {
                    $this->setBalanceReminded(0);
                }
            }
        } catch (\Exception $e) {
            Log::warn('Failed to clear the low balance remind flag for uc ' . $this->getId() . ': ' . $e->getMessage());
        }

        return $this;
    }

    /**
     * Set status
     *
     * @param string $status
     * @param bool $updatePartnerStatus
     * @param bool $unload
     * @return UserCard
     */
    public function setStatus($status, bool $updatePartnerStatus = true, bool $unload = false)
    {
        $old = $this->status;
        if ($this->id && $this->getIssued()) {
            if ($this->status === self::STATUS_ACTIVE && $status === self::STATUS_INACTIVE) {
                if ($unload) {
                    $cp = $this->getCardProgram();
                    if ($cp && $cp->needUnloadCardWhenDeactivated()) {
                        CardService::unload($this, null, UserCardLoad::TYPE_UNLOAD_CARD_DEACTIVATED);
                    }
                }
                if ($updatePartnerStatus) {
                    CardService::deactivate($this);
                }
            } else if ($this->status === self::STATUS_INACTIVE && $status === self::STATUS_ACTIVE) {
                if ($updatePartnerStatus) {
                    CardService::activate($this);
                }
                Util::updateJson($this, 'meta', [
                    'closed' => false,
                ], false);
            }
        }
        $this->status = $status;

        if ($old !== $status) {
            UserCardStatus::create($this, $old, $status);
        }

        return $this;
    }

    public function getLastCompletedLoad($loadType = UserCardLoad::TYPE_LOAD_CARD)
    {
        $loads = $this->getLoads($loadType)->toArray();
        /** @var UserCardLoad $load */
        foreach (array_reverse($loads) as $load) {
            if (in_array($load->getLoadStatus(), [
                UserCardLoad::LOAD_STATUS_RECEIVED,
                UserCardLoad::LOAD_STATUS_LOADED,
            ], true)) {
                return $load;
            }
        }
        return null;
    }

    public function getPendingToLoadLoads($days = null)
    {
        return $this->getLoads(UserCardLoad::TYPE_LOAD_CARD)->filter(function (UserCardLoad $load) use ($days) {
            $status = $load->getLoadStatus();
            if (UserCardLoad::LOAD_STATUS_RECEIVED === $status) {
                if (null === $days) {
                    return true;
                }
                $createdAt = Carbon::instance($load->getCreatedAt());
                if ($createdAt->addDays($days)->isFuture()) {
                    return true;
                }
            }
            return false;
        });
    }

    /**
     * Pending to be load
     * @return ArrayCollection
     */
    public function getReceivedLocalBalanceLoad()
    {
        $loads = $this->getLoads(UserCardLoad::TYPE_LOAD_LOCAL_BALANCE)->filter(function (UserCardLoad $load) {
            return UserCardLoad::LOAD_STATUS_RECEIVED === $load->getLoadStatus();
        });
        return Util::desc($loads);
    }

    /**
     * Get loads
     *
     * @param null $loadType
     * @return Collection
     */
    public function getLoads($loadType = null)
    {
        return $this->loads->filter(function (UserCardLoad $load) use ($loadType) {
            return !$loadType || $loadType === $load->getType();
        });
    }

    /**
     * @param $data array amount, type
     */
    public function addPendingFee($data) {
        $pf = Util::s2j($this->getPendingFees());
        $pf[] = $data;
        $this->setPendingFees(json_encode($pf))
            ->persist();

        $text = Money::format($data['amount'], $this->getCurrency());
        $note = "Add pending fee ({$data['type']}): $text";
        $this->getUser()->addNote($note);
    }

    public function updatePendingFee($type, $searchBy, $searchValue, $amount)
    {
        $pf = Util::s2j($this->getPendingFees());
        $data = [
            'type' => $type,
            'amount' => $amount,
            $searchBy => $searchValue,
        ];
        foreach ($pf as $i => $item) {
            if ($item['type'] === $type) {
                if (isset($item[$searchBy]) && $item[$searchBy] === $searchValue) {
                    $pf[$i] = $data;
                    $this->setPendingFees(json_encode($pf))
                        ->persist();

                    $text = Money::format($amount, $this->getCurrency());
                    $note = "Updated pending fee ($type) of $searchBy $searchValue to $text";
                    $this->getUser()->addNote($note);
                    return;
                }
            }
        }

        // Add if not found
        $this->addPendingFee($data);
    }

    public function getPendingFeeApiArray()
    {
        $pf = Util::s2j($this->getPendingFees());
        foreach ($pf as $i => $p) {
            $pf[$i]['currency'] = $this->getCurrency();
            $pf[$i]['coin'] = Coin::create($p['amount'], $this->getCurrency())->toApiArray();
        }
        return $pf;
    }

    /**
     * https://shinetechchina.atlassian.net/browse/TCSPAN-789
     * @throws \PortalBundle\Exception\PortalException
     */
    public function upgrade() {
        Util::longRequest();

        $user = $this->getUser();
        $user->addNote('UPGRADE: Start to upgrade from ' . $this->getAccountNumber());

        // 1. Unload the non-reloadable card. Saved the amount to local balance. Don't charge unload fee.
        $balance = $this->getBalance(true);
        if ($balance && $balance > 0) {
            $ret = CardService::unload($this, $balance, UserCardLoad::TYPE_UNLOAD_UPGRADE, true);
            if (!$ret || is_string($ret)) {
                $exception = 'Failed to upgrade card: Unload operation failed. ' . ($ret ?: '');
                $user->addNote('UPGRADE: ' . $exception);
                throw new PortalException($exception);
            }
        }
        $user->addNote('UPGRADE: Unloaded '  . $balance . ' from old card with ' . $this->getAccountNumber());

        // 2. Inactivated the non-reloadable card.
        $this->setStatus(self::STATUS_INACTIVE, false, false)
            ->persist();
        $inactive = CardService::updateStatus($this);
        if (!$inactive) {
            $exception = 'Failed to upgrade card: Inactivate old card failed!';
            $user->addNote('UPGRADE: ' . $exception);
            throw new PortalException($exception);
        }
        $user->addNote('UPGRADE: Inactivated old card ' . $this->getAccountNumber());

        // 3. Create a new reloadable card. The local balance should be loaded to this card.
        $oldCard = $this->getCard();
        $newCard = $oldCard->getUpgradeTargetCard();
        $newCardId = $newCard->getId();

        // 3.1 Find old valid card first(Created in last failed upgrade operation)
        $_ucs = $user->getCards()->filter(function (UserCard $_uc) use ($newCardId) {
            return $_uc->getCard()->getId() === $newCardId && !$_uc->getIssued() && !$_uc->isDeleted();
        });
        $newUc = null;
        if ($_ucs) {
            $newUc = $_ucs[0];
        }
        if (!$newUc) {
            $newUc = $this->copy();
            $newUc->setCard($newCard);
            $newUc->persist();
        }
        $newUc->setUpgradeFrom($this)
            ->persist();

        // 3.2 Now issue the card
        $ei = $newUc->createCard('/upgrade-card');
        if ($ei->isFailed()) {
            $exception = 'Failed to upgrade card: Unable to create new card! ' . $ei->getError();
            $user->addNote('UPGRADE: ' . $exception);
            throw new PortalException($exception);
        }
        $user->addNote('UPGRADE: Issued new card ' . $newUc->getAccountNumber());

        Util::updateJson($this, 'meta', [
            'upgradedTo' => $newUc->getId(),
            'upgradedAt' => Carbon::now()->getTimestamp(),
        ]);

        Email::sendWithTemplateToUser($user, Email::TEMPLATE_CARD_UPGRADED, [
            'card' => $newCard->getFullName(),
            'action_url' => Util::host() . '/card-management',
        ], $this->getCard()->getCardProgram());

        $user->addNote('UPGRADE: Upgraded to "' . $newCard->getFullName() . '" card');
    }

    public function getUpgradedToUserCard()
    {
        return Util::em()->getRepository(\CoreBundle\Entity\UserCard::class)->findOneBy([
            'upgradeFrom' => $this->getId(),
        ]);
    }

    /**
     * @return UserCard
     */
    public function copy() {
        $new = new self();
        $new->setUser($this->getUser());
        $new->setCard($this->getCard());
        $new->setCurrency($this->getCurrency());
        $new->setStatus(self::STATUS_ACTIVE, false, false);
        $new->setPan('');
        $new->setWarningValue($this->getWarningValue());
        $new->setIsWarning($this->getIsWarning());
        $new->setInitializedAt($this->getInitializedAt());
        Util::em()->persist($new);

        $ba = $this->getBillingAddress();
        if ($ba) {
            $nba = $ba->copy($new);
            $new->setBillingAddress($nba);
        }

        return $new;
    }

    public function calculateFee($name, $amount = null, &$feeItem = null)
    {
        $repo = Util::em()->getRepository(\CoreBundle\Entity\CardProgram::class);
        $cp = $this->getCard()->getCardProgram();
        $currency = $this->getCurrency();

        if ($amount === null) {
            $amount = $this->getBalance();
        }

        $feeItem = $repo->getFeeItem($cp, $name, $this->getUser(), $amount);
        if ($feeItem) {
            $origin = $feeItem->calculate($amount, $currency);
            if (!$origin) {
                return null;
            }
            $comments = [];
            $final = $feeItem->calculate($amount, $currency, $this->getUser(), $comments);
            return [
                'origin' => $origin,
                'discount' => $origin - $final,
                'final' => $final,
                'comments' => $comments,
            ];
        }
        return null;
    }

    // Set rules for currency conversion takes place and storing of conversion formula (exchange rate used)
    // converted load amount and how to reported load amount was recalculated.
    // TCSPAN-766
    public function calculateReversedLoadAmount($methodOrFeeName, $receivedAmount, $receivedCurrency, $initialCurrency, UserCardLoad $ucl = null)
    {
        $repo = Util::em()->getRepository(\CoreBundle\Entity\CardProgram::class);
        $cp = $this->getCard()->getCardProgram();

        $feeItem = $repo->getFeeItem($cp, $methodOrFeeName, $this->getUser(),
            $receivedAmount);
        $calculated = $receivedAmount;
        if ($feeItem) {
            $applied = Promotion::applyUsuLoadFeePromotion($this, $ucl, $receivedAmount);
            if ($applied === false) {
                $calculated = $feeItem->calculateReversed($receivedAmount, $receivedCurrency);
            }
        }
        return Money::convertWithReversedExtra($calculated, $receivedCurrency, $initialCurrency);
    }

    public function calculateFinalFee($name, $amount = null, &$feeItem = null)
    {
        $result = $this->calculateFee($name, $amount, $feeItem);
        if (!$result) {
            return 0;
        }
        return $result['final'];
    }

    public function calculateFeeArray($name, $amount = null)
    {
        $result = $this->calculateFee($name, $amount);
        if (!$result) {
            return null;
        }
        $key = FeeGlobalName::getKey($name);
        return [
            'origin' => [
                $key => $result['origin'],
            ],
            'discount' => [
                $key => $result['discount'],
            ],
            $key => $result['final'],
        ];
    }

    public function addLocalBalance($delta, $type)
    {
        $old = (int)$this->getLocalBalance();
        return $this->setLocalBalance($old + $delta, $type);
    }

    public function getBeginBalance($date)
    {
        $startAt = Carbon::createFromFormat(Util::DATE_FORMAT_MONTH,
            Util::formatDateTime($date, Util::DATE_FORMAT_MONTH))->startOfMonth()->startOfDay();
        $endAt = Carbon::createFromFormat(Util::DATE_FORMAT_MONTH,
            Util::formatDateTime($date, Util::DATE_FORMAT_MONTH))->endOfMonth()->endOfDay();
        $balanceQuery = Util::em()->getRepository(\CoreBundle\Entity\UserCardBalance::class)
            ->createQueryBuilder('ub')
            ->where('ub.userCard = :userCard')
            ->andWhere('ub.createdAt >= :startAt')
            ->andWhere('ub.createdAt <= :endAt')
            ->setParameter('userCard', $this)
            ->setParameter('startAt', $startAt)
            ->setParameter('endAt', $endAt)
            ->orderBy('ub.createdAt', 'asc');
        $balances = $balanceQuery->getQuery()->getResult();
        return current($balances)? current($balances)->getCurrentBalance() : 0;
    }

    public function getEndBalance($date)
    {
        $startAt = Carbon::createFromFormat(Util::DATE_FORMAT_MONTH,
            Util::formatDateTime($date, Util::DATE_FORMAT_MONTH))->startOfMonth()->startOfDay();
        $endAt = Carbon::createFromFormat(Util::DATE_FORMAT_MONTH,
            Util::formatDateTime($date, Util::DATE_FORMAT_MONTH))->endOfMonth()->endOfDay();
        $balanceQuery = Util::em()->getRepository(\CoreBundle\Entity\UserCardBalance::class)
            ->createQueryBuilder('ub')
            ->where('ub.userCard = :userCard')
            ->andWhere('ub.createdAt >= :startAt')
            ->andWhere('ub.createdAt <= :endAt')
            ->setParameter('userCard', $this)
            ->setParameter('startAt', $startAt)
            ->setParameter('endAt', $endAt)
            ->orderBy('ub.createdAt', 'asc');
        $balances = $balanceQuery->getQuery()->getResult();
        return end($balances)? end($balances)->getCurrentBalance() : 0;
    }

    public function isLoadDisabled()
    {
        $meta = Util::s2j($this->getMeta());
        return $meta && isset($meta['disableLoad']) && $meta['disableLoad'];
    }

    public function statisticAnnualLoad()
    {
        $q = Util::em()->getRepository(\CoreBundle\Entity\UserCardLoad::class)
            ->createQueryBuilder('ucl');
        $expr = $q->expr();
        $result = $q->where($expr->eq('ucl.userCard', ':uc'))
            ->andWhere($expr->eq('ucl.loadStatus', ':status'))
            ->andWhere($expr->gte('ucl.loadAt', ':loadAt'))
            ->setParameters([
                'uc'     => $this->getId(),
                'status' => UserCardLoad::LOAD_STATUS_LOADED,
                'loadAt' => Carbon::now()->subYearWithoutOverflow(),
            ])
            ->select('count(ucl) _count', 'sum(ucl.loadAmountUSD) _sum')
            ->getQuery()
            ->getScalarResult();
        return $result[0];
    }

    public function statisticUsage()
    {
        $q = Util::em()->getRepository(\CoreBundle\Entity\UserCardTransaction::class)
            ->createQueryBuilder('uct');
        $expr = $q->expr();
        $result = $q->where($expr->eq('uct.userCard', ':uc'))
            ->andWhere($expr->in('uct.actualTranCode', ':actualTranCode'))
            ->setParameters([
                'uc'      => $this->getId(),
                'actualTranCode' => [
                    UserCardTransaction::CODE_POS_PURCHASE,
                    UserCardTransaction::CODE_PURCHASE_RETURN,
                ],
            ])
            ->select('count(uct) _count', 'sum(uct.txnAmount) _sum')
            ->getQuery()
            ->getScalarResult();
        return $result[0];
    }

    public function allMCC()
    {
        $q = Util::em()->getRepository(\CoreBundle\Entity\UserCardTransaction::class)
            ->createQueryBuilder('uct');
        $expr = $q->expr();
        $rs = $q->innerJoin('uct.merchant', 'm')
            ->innerJoin('m.merchantType', 'mt')
            ->where($expr->eq('uct.userCard', ':uc'))
            ->andWhere($expr->in('uct.actualTranCode', ':actualTranCode'))
            ->setParameters([
                'uc'      => $this->getId(),
                'actualTranCode' => [
                    UserCardTransaction::CODE_POS_PURCHASE,
                    UserCardTransaction::CODE_PURCHASE_RETURN,
                ],
            ])
            ->select('mt.mcc')
            ->distinct()
            ->getQuery()
            ->getArrayResult();
        return array_map(function ($r) {
            return $r['mcc'];
        }, $rs);
    }

    /**
     * Get status
     *
     * @return string
     */
    public function getStatus()
    {
        if (!$this->status) {
            $this->status = self::STATUS_ACTIVE;
        }
        return $this->status;
    }

    /**
     * @param $idOrHash
     * @return null|UserCard
     */
    public static function find($idOrHash)
    {
        $q = Util::em()->getRepository(\CoreBundle\Entity\UserCard::class)
            ->createQueryBuilder('uc');
        $e = $q->expr();

        $field = is_numeric($idOrHash) ? 'uc.id' : 'uc.hash';
        $rs = $q->where($e->eq($field, ':v'))
            ->setParameter('v', $idOrHash)
            ->getQuery()
            ->getResult();
        if ($rs) {
            return $rs[0];
        }
        return null;
    }

    public function ensureBillingAddress()
    {
        $ba = $this->getBillingAddress();
        if (!$ba) {
            $ba = new UserBillingAddress();
            $ba->setUserCard($this)
                ->setCreatedAt(new \DateTime('now'));
        }
        return $ba;
    }

    /**
     * Get hash
     *
     * @return string
     */
    public function getHash()
    {
        $hash = $this->hash;
        if (!$hash) {
            $hash = Util::guid();
            $this->setHash($hash)
                ->persist();
        }
        return $hash;
    }

    /**
     * Get hash
     *
     * @return string
     */
    public function getHashWithoutAutoSet()
    {
        return $this->hash;
    }

    public function getUserDiscountOfFee($feeGlobalName) {
        $user = $this->getUser();
        $cp = $this->getCard()->getCardProgram();
        $cpFeeItem = Util::em()->getRepository(\CoreBundle\Entity\CardProgram::class)
            ->getFeeItem($cp, $feeGlobalName, $this->getUser());
        if (!$cpFeeItem) {
            return null;
        }
        return UserDiscount::find($user, $cpFeeItem);
    }

    /**
     * Set issued
     *
     * @param integer $issued
     *
     * @return UserCard
     */
    public function setIssued($issued)
    {
        $this->issued = $issued;

        if ($issued && null === $this->getIssuedAt()) {
            $this->setIssuedAt(new \DateTime());
        }

        return $this;
    }

    public function getPosUsages()
    {
        return $this->getUsages()->filter(function (UserCardTransaction $transaction) {
            return UserCardTransaction::CODE_POS_PURCHASE === $transaction->getActualTranCode();
        });
    }

    /**
     * Hook SoftDeleteable behavior
     * updates deletedAt field
     */
    use SoftDeleteableEntity;

    /**
     * @ORM\ManyToOne(targetEntity="SalexUserBundle\Entity\User")
     * @ORM\JoinColumn(name="user_id", referencedColumnName="id", onDelete="cascade")
     * @Serializer\Exclude()
     */
    private $user;

    /**
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\CardProgramCardType")
     * @ORM\JoinColumn(name="card_id", referencedColumnName="id", onDelete="cascade")
     * @Serializer\Exclude()
     */
    private $card;

    /**
     * First time when the load payment has been created at - Load process step 4.
     *
     * @var \DateTime
     *
     * @ORM\Column(name="initialized_at", type="datetime", nullable=true, options={"comment":"First time when the load payment has been created at - Load process step 4."})
     */
    private $initializedAt;

    /**
     * @ORM\OneToMany(targetEntity="CoreBundle\Entity\UserCardLoad", mappedBy="userCard")
     * @Serializer\Exclude()
     */
    private $loads;

    //Begin Add by Bob Wen Bao on 2017-04-25 to handle card balance
    /**
     * @var Collection $balances
     * @ORM\OneToMany(targetEntity="CoreBundle\Entity\UserCardBalance", mappedBy="userCard")
     * @Serializer\Exclude()
     */
    private $balances;

    /**
     * @var Collection $statuses
     * @ORM\OneToMany(targetEntity="CoreBundle\Entity\UserCardStatus", mappedBy="userCard")
     * @Serializer\Exclude()
     */
    private $statuses;
    //End

    /**
     * @var Collection $purses
     * @ORM\OneToMany(targetEntity="CoreBundle\Entity\UserCardPurse", mappedBy="userCard")
     * @Serializer\Exclude()
     */
    private $purses;

    /**
     * @var Collection $identities
     * @ORM\OneToMany(targetEntity="SpendrBundle\Entity\PlaidIdentity", mappedBy="card")
     * @Serializer\Exclude()
     */
    private $identities;

    /**
     * @var string
     *
     * @ORM\Column(name="nick_name", type="string", length=255, nullable=true, options={"comment":"Privacy's friendly card name, etc."})
     */
    private $nickName;

    /**
     * @var string
     *
     * @ORM\Column(name="type", type="string", length=255, nullable=true, options={"comment":"Privacy's card type, etc."})
     */
    private $type;

    /**
     * @var integer
     *
     * @ORM\Column(name="spend_limit", type="integer", nullable=true, options={"comment":"Privacy's amount (in cents) to limit approved authorizations, etc."})
     */
    private $spendLimit;

    /**
     * @var string
     *
     * @ORM\Column(name="spend_limit_duration", type="string", length=255, nullable=true, options={"comment":"Privacy's amount (in cents) to limit approved authorizations, etc."})
     */
    private $spendLimitDuration;

    /**
     * @var string System generated Account number
     *             In Privacy, accountNumber = token
     *
     * @ORM\Column(name="account_number", type="string", length=1000, nullable=true, options={"comment":"FirstView generated Account number"})
     */
    private $accountNumber;

    /**
     * @var string System Generated Direct Deposit Account Number
     *             In TransferMex, dbaNo = card number
     *
     * @ORM\Column(name="dba_no", type="string", length=1000, nullable=true, options={"comment":"FirstView generated Direct Deposit Account Number"})
     */
    private $dbaNo;

    /**
     * @var \DateTime Card will be expired at
     *
     * @ORM\Column(name="expire_at", type="datetime", nullable=true, options={"comment":"Card will be expired at"})
     */
    private $expireAt;

    /**
     * @var string Remind flags when the card is expiring
     *
     * @ORM\Column(name="expire_reminder", type="text", nullable=true, options={"comment":"Remind(send email) flags when the card is expiring"})
     */
    private $expireReminder;

    /**
     * @var string Card holder name
     *
     * @ORM\Column(name="holder", type="string", length=255, nullable=true, options={"comment":"Card holder name"})
     */
    private $holder;

    /**
     * @var string Last 4 digits of the card number
     *
     * @ORM\Column(name="pan", type="string", length=255, nullable=true, options={"comment":"Last 4 digits of the card number"})
     */
    private $pan;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="account_created_at", type="datetime", nullable=true)
     */
    private $accountCreatedAt;

    /**
     * Bound merchant, for example, in Privacy
     *
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\Merchant")
     * @ORM\JoinColumn(onDelete="SET NULL")
     * @Serializer\Exclude()
     */
    private $merchant;

    /**
     * @var int
     *
     * @ORM\Column(name="balance", type="bigint", nullable=true)
     */
    private $balance;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="balance_updated_at", type="datetime", nullable=true)
     */
    private $balanceUpdatedAt;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="balance_changed_at", type="datetime", nullable=true)
     */
    private $balanceChangedAt;

    /**
     * @var int
     *
     * @ORM\Column(name="local_balance", type="bigint", nullable=true, options={"comment":"Local saved balance. This is also the money of consumer, but not in the card(3rd partner)"})
     */
    private $localBalance;

    /**
     * @var string
     *
     * @ORM\Column(name="currency", type="string", length=255, nullable=true)
     */
    private $currency;

    /**
     * @var int
     *
     * @ORM\Column(name="issued", type="integer", nullable=true)
     */
    private $issued;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="issued_at", type="datetime", nullable=true)
     */
    private $issuedAt;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=255, nullable=true)
     */
    private $status;

    /**
     * @var string
     *
     * @ORM\Column(name="native_status", type="string", length=255, nullable=true)
     */
    private $nativeStatus;

    /**
     * @var string
     *
     * @ORM\Column(name="replace_status", type="string", length=255, nullable=true, options={"comment":"Status of replacing card process"})
     */
    private $replaceStatus;

    /**
     * @var string
     *
     * @ORM\Column(name="pending_fees", type="text", nullable=true, options={"comment":"Pending fees which should charge from consumer"})
     */
    private $pendingFees;

    /**
     * @var Collection $pins
     * @ORM\OneToMany(targetEntity="CoreBundle\Entity\UserPin", mappedBy="card")
     * @Serializer\Exclude()
     */
    private $pins;

    /**
     * @var Collection $snaps
     * @ORM\OneToMany(targetEntity="CoreBundle\Entity\UserCardSnap", mappedBy="userCard")
     * @Serializer\Exclude()
     */
    private $snaps;

    /**
     * @var string
     * @ORM\Column(name="warning_value", type="string",nullable=true, options={"comment":"The minimum balance amount to send low balance warning."})
     */
    private $warningValue;

    /**
     * @var string
     * @ORM\Column(name="is_warning", type="string",nullable=true, options={"comment":"Whether to send low balance warning when balance is lower than warning_value"})
     */
    private $isWarning;

    /**
     * @var integer
     * @ORM\Column(name="balance_reminded", type="bigint",nullable=true, options={"comment":"Flag whether consumer is reminded for low balance"})
     */
    private $balanceReminded;

    /**
     * From which card this card was upgraded
     *
     * @var UserCard Upgrade from card
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\UserCard")
     * @Serializer\Exclude()
     */
    private $upgradeFrom;

    /**
     * @var Collection
     * @ORM\OneToMany(targetEntity="CoreBundle\Entity\UserCardLocalBalanceSettlement", mappedBy="userCard")
     * @Serializer\Exclude()
     */
    private $localBalanceSettlements;

    /**
     * @var Collection
     * @ORM\OneToMany(targetEntity="CoreBundle\Entity\UserCardTransaction", mappedBy="userCard")
     * @Serializer\Exclude()
     */
    private $usages;

    /**
     * @var Collection
     * @ORM\OneToMany(targetEntity="CoreBundle\Entity\UserCardDecline", mappedBy="userCard")
     * @Serializer\Exclude()
     */
    private $declines;

    /**
     * @var string
     * @ORM\Column(name="hash", type="string", nullable=true, unique=true, options={"comment":"User card hash used in consumer portal to work as id to avoid attack"})
     */
    private $hash;

    /**
     * @var CardInventory
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\CardInventory")
     * @Serializer\Exclude()
     */
    private $inventory;

    /**
     * @var UserBillingAddress
     *
     * @ORM\OneToOne(targetEntity="CoreBundle\Entity\UserBillingAddress", mappedBy="userCard")
     * @Serializer\Exclude()
     */
    private $billingAddress;

    /**
     * @var string
     * @ORM\Column(name="shipping_method", type="string", length=255, nullable=true)
     */
    private $shippingMethod;

    /**
     * @var string
     * @ORM\Column(name="meta", type="text", nullable=true)
     */
    private $meta;

    /**
     * @var int
     *
     * @ORM\Column(name="accountId", type="bigint", nullable=true)
     */
    private $accountId;

    /**
     * @var int
     *
     * @ORM\Column(name="providerAccountId", type="bigint", nullable=true)
     */
    private $providerAccountId;

    /**
     * @var string
     *
     * @ORM\Column(name="plaid_access_token", type="string", nullable=true)
     */
    private $plaidAccessToken;

     /**
     * @var \DateTime
     *
     * @ORM\Column(name="activity_time", type="datetime", nullable=true)
     */
    private $activityTime;

    /**
     * @var Collection
     * @ORM\OneToMany(targetEntity="SpendrBundle\Entity\Location", mappedBy="bankCard")
     * @Serializer\Exclude()
     */
    private $spendrLocations;

      /**
     * @var string
     *
     * @ORM\Column(name="card_provider", type="string", length=255, nullable=true)
     */
    private $cardProvider;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="deactivating_at", type="datetime", nullable=true)
     */
    private $deactivatingAt;

    /**
     * Set user
     *
     * @param User $user
     *
     * @return UserCard
     */
    public function setUser($user)
    {
        $this->user = $user;

        return $this;
    }

    /**
     * Get user
     *
     * @return User
     */
    public function getUser()
    {
        return $this->user;
    }

    /**
     * Set card
     *
     * @param CardProgramCardType $card
     *
     * @return UserCard
     */
    public function setCard($card)
    {
        $this->card = $card;

        return $this;
    }

    /**
     * Get card
     *
     * @return CardProgramCardType
     */
    public function getCard()
    {
        return $this->card;
    }

    /**
     * Get issued
     *
     * @return int
     */
    public function getIssued()
    {
        return $this->issued;
    }

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->balances = new ArrayCollection();
        $this->statuses = new ArrayCollection();
        $this->loads = new ArrayCollection();
        $this->usages = new ArrayCollection();
        $this->declines = new ArrayCollection();
        $this->purses = new ArrayCollection();
        $this->spendrLocations = new ArrayCollection();
        $this->identities = new ArrayCollection();
        $this->pins = new ArrayCollection();
        $this->snaps = new ArrayCollection();
        $this->localBalanceSettlements = new ArrayCollection();
    }

    /**
     * Add load
     *
     * @param \CoreBundle\Entity\UserCardLoad $load
     *
     * @return UserCard
     */
    public function addLoad(\CoreBundle\Entity\UserCardLoad $load)
    {
        $this->loads[] = $load;

        return $this;
    }

    /**
     * Remove load
     *
     * @param \CoreBundle\Entity\UserCardLoad $load
     */
    public function removeLoad(\CoreBundle\Entity\UserCardLoad $load)
    {
        $this->loads->removeElement($load);
    }

    /**
     * Set currency
     *
     * @param string $currency
     *
     * @return UserCard
     */
    public function setCurrency($currency)
    {
        $this->currency = $currency;

        return $this;
    }

    /**
     * Get currency
     *
     * @return string
     */
    public function getCurrency()
    {
        return $this->currency;
    }

    /**
     * Set dbaNo
     *
     * @param string $dbaNo
     *
     * @return UserCard
     */
    public function setDbaNo(#[\SensitiveParameter] $dbaNo)
    {
        $this->dbaNo = $dbaNo;

        return $this;
    }

    /**
     * Get dbaNo
     *
     * @return string
     */
    public function getDbaNo()
    {
        return $this->dbaNo;
    }

    /**
     * Set accountCreatedAt
     *
     * @param \DateTime $accountCreatedAt
     *
     * @return UserCard
     */
    public function setAccountCreatedAt($accountCreatedAt)
    {
        $this->accountCreatedAt = $accountCreatedAt;

        return $this;
    }

    /**
     * Get accountCreatedAt
     *
     * @return \DateTime
     */
    public function getAccountCreatedAt()
    {
        return $this->accountCreatedAt;
    }

    /**
     * Add balance
     *
     * @param \CoreBundle\Entity\UserCardBalance $balance
     *
     * @return UserCard
     */
    public function addBalance(\CoreBundle\Entity\UserCardBalance $balance)
    {
        $this->balances[] = $balance;

        return $this;
    }

    /**
     * Remove balance
     *
     * @param \CoreBundle\Entity\UserCardBalance $balance
     */
    public function removeBalance(\CoreBundle\Entity\UserCardBalance $balance)
    {
        $this->balances->removeElement($balance);
    }

    /**
     * Get balances
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getBalances()
    {
        return $this->balances;
    }

    /**
     * Set accountNumber
     *
     * @param string $accountNumber
     *
     * @return UserCard
     */
    public function setAccountNumber(#[\SensitiveParameter] $accountNumber)
    {
        $this->accountNumber = $accountNumber;

        return $this;
    }

    /**
     * Get accountNumber
     *
     * @return string
     */
    public function getAccountNumber()
    {
        return $this->accountNumber;
    }

    /**
     * Set pan
     *
     * @param string $pan
     *
     * @return UserCard
     */
    public function setPan(#[\SensitiveParameter] $pan)
    {
        $this->pan = $pan;

        return $this;
    }

    /**
     * Get pan
     *
     * @param bool|string $removeStar
     * @return string
     */
    public function getPan($removeStar = false)
    {
        if ($this->pan) {
            if ($removeStar === 'mask') {
                return Util::maskPan($this->pan);
            }
            if ($removeStar) {
                return substr($this->pan, -4);
            }
            if (false === strpos($this->pan, '*')) {
                return str_pad($this->pan, 16, '*', STR_PAD_LEFT);
            }
        }
        return $this->pan;
    }


    /**
     * Add pin
     *
     * @param \CoreBundle\Entity\UserPin $pin
     *
     * @return UserCard
     */
    public function addPin(\CoreBundle\Entity\UserPin $pin)
    {
        $this->pins[] = $pin;

        return $this;
    }

    /**
     * Remove pin
     *
     * @param \CoreBundle\Entity\UserPin $pin
     */
    public function removePin(\CoreBundle\Entity\UserPin $pin)
    {
        $this->pins->removeElement($pin);
    }

    /**
     * Get pins
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getPins()
    {
        return $this->pins;
    }

    /**
     * Set expireAt
     *
     * @param \DateTime $expireAt
     *
     * @return UserCard
     */
    public function setExpireAt($expireAt)
    {
        if (($this->expireAt && !$expireAt)
            || (!$this->expireAt && $expireAt)
            || ($this->expireAt && $expireAt && $expireAt->getTimestamp() !== $this->expireAt->getTimestamp()))
        {
            $this->setExpireReminder(null);
        }

        $this->expireAt = $expireAt;

        return $this;
    }

    /**
     * Set holder
     *
     * @param string $holder
     *
     * @return UserCard
     */
    public function setHolder($holder)
    {
        $this->holder = $holder;

        return $this;
    }

    /**
     * Set expireReminder
     *
     * @param string $expireReminder
     *
     * @return UserCard
     */
    public function setExpireReminder($expireReminder)
    {
        $this->expireReminder = $expireReminder;

        return $this;
    }

    /**
     * Get expireReminder
     *
     * @return string
     */
    public function getExpireReminder()
    {
        return $this->expireReminder;
    }

    /**
     * Set localBalance
     *
     * @param integer $localBalance
     *
     * @param string $type
     * @return UserCard
     */
    public function setLocalBalance($localBalance, $type = UserCardBalance::TYPE_UNLOAD_CARD)
    {
        if ($type && (int)($this->localBalance) !== (int)$localBalance && ($this->localBalance || $localBalance) && $this->getId()) {
            $log = new UserCardBalance();
            $log->setUserCard($this);
            $log->setSource(UserCardBalance::SOURCE_LOCAL_BALANCE);
            $log->setType($type);
            $log->setPan($this->getPan());
            $log->setCurrentBalance($localBalance ?: 0);
            $log->setPreviousBalance($this->localBalance ?: 0);
            $log->persist();
        }
        $this->localBalance = $localBalance;

        return $this;
    }

    /**
     * Get localBalance
     *
     * @return integer
     */
    public function getLocalBalance()
    {
        return $this->localBalance;
    }

    /**
     * Set pendingFees
     *
     * @param string $pendingFees
     *
     * @return UserCard
     */
    public function setPendingFees($pendingFees)
    {
        $this->pendingFees = $pendingFees;

        return $this;
    }

    /**
     * Get pendingFees
     *
     * @return string
     */
    public function getPendingFees()
    {
        return $this->pendingFees;
    }

    /**
     * Set warningValue
     *
     * @param string $warningValue
     *
     * @return UserCard
     */
    public function setWarningValue($warningValue)
    {
        $this->warningValue = $warningValue;

        return $this;
    }

    /**
     * Get warningValue
     *
     * @return string
     */
    public function getWarningValue()
    {
//        if (!$this->warningValue) {
            //Have changed related logic in consumer portal CardController and BalanceLookupHandler
            //No need to do convert here any more
//            $currency = $this->getCurrency() ?: 'USD';
//            $threshold = Money::convert(self::DEFAULT_BALANCE_REMIND_THRESHOLD, 'USD', $currency);
//            /*$this->setWarningValue(Money::formatAmount($threshold, $currency))
//                ->persist();*/
//            $this->setWarningValue($threshold)->persist();
//        }
        return $this->warningValue;
    }

    /**
     * Set isWarning
     *
     * @param string $isWarning
     *
     * @return UserCard
     */
    public function setIsWarning($isWarning)
    {
        $this->isWarning = $isWarning;

        return $this;
    }

    /**
     * Get isWarning
     *
     * @return string
     */
    public function getIsWarning()
    {
        return $this->isWarning; // '1'; // TCSPAN-396
    }

    /**
     * Set upgradeFrom
     *
     * @param UserCard $upgradeFrom
     *
     * @return UserCard
     */
    public function setUpgradeFrom($upgradeFrom)
    {
        $this->upgradeFrom = $upgradeFrom;

        return $this;
    }

    /**
     * Get upgradeFrom
     *
     * @return UserCard
     */
    public function getUpgradeFrom()
    {
        return $this->upgradeFrom;
    }

    /**
     * Add localBalanceSettlement
     *
     * @param \CoreBundle\Entity\UserCardLocalBalanceSettlement $localBalanceSettlement
     *
     * @return UserCard
     */
    public function addLocalBalanceSettlement(\CoreBundle\Entity\UserCardLocalBalanceSettlement $localBalanceSettlement)
    {
        $this->localBalanceSettlements[] = $localBalanceSettlement;

        return $this;
    }

    /**
     * Remove localBalanceSettlement
     *
     * @param \CoreBundle\Entity\UserCardLocalBalanceSettlement $localBalanceSettlement
     */
    public function removeLocalBalanceSettlement(\CoreBundle\Entity\UserCardLocalBalanceSettlement $localBalanceSettlement)
    {
        $this->localBalanceSettlements->removeElement($localBalanceSettlement);
    }

    /**
     * Get localBalanceSettlements
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getLocalBalanceSettlements()
    {
        return $this->localBalanceSettlements;
    }

    /**
     * Set balanceReminded
     *
     * @param integer $balanceReminded
     *
     * @return UserCard
     */
    public function setBalanceReminded($balanceReminded)
    {
        $this->balanceReminded = $balanceReminded;

        return $this;
    }

    /**
     * Get balanceReminded
     *
     * @return integer
     */
    public function getBalanceReminded()
    {
        return $this->balanceReminded;
    }

    /**
     * Set meta
     *
     * @param string $meta
     *
     * @return UserCard
     */
    public function setMeta($meta)
    {
        $this->meta = $meta;

        return $this;
    }

    /**
     * Get meta
     *
     * @return string
     */
    public function getMeta()
    {
        return $this->meta;
    }

    /**
     * Add usage
     *
     * @param \CoreBundle\Entity\UserCardTransaction $usage
     *
     * @return UserCard
     */
    public function addUsage(\CoreBundle\Entity\UserCardTransaction $usage)
    {
        $this->usages[] = $usage;

        return $this;
    }

    /**
     * Remove usage
     *
     * @param \CoreBundle\Entity\UserCardTransaction $usage
     */
    public function removeUsage(\CoreBundle\Entity\UserCardTransaction $usage)
    {
        $this->usages->removeElement($usage);
    }

    /**
     * Get usages
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getUsages()
    {
        return $this->usages;
    }

    /**
     * Set replaceStatus
     *
     * @param string $replaceStatus
     *
     * @param null $message
     * @return UserCard
     */
    public function setReplaceStatus($replaceStatus, $message = null)
    {
        if ($this->replaceStatus !== $replaceStatus) {
            $this->getUser()->addNote(
                'Replace card(' . $this->getAccountNumber(). ') status: '
                . ($replaceStatus ? ucfirst($replaceStatus) : 'Finished')
                . ($message ? (' - ' . $message) : ''));
        }

        $this->replaceStatus = $replaceStatus;

        return $this;
    }

    /**
     * Get replaceStatus
     *
     * @return string
     */
    public function getReplaceStatus()
    {
        return $this->replaceStatus;
    }

    /**
     * Set balanceUpdatedAt
     *
     * @param \DateTime $balanceUpdatedAt
     *
     * @return UserCard
     */
    public function setBalanceUpdatedAt($balanceUpdatedAt)
    {
        $this->balanceUpdatedAt = $balanceUpdatedAt;

        return $this;
    }

    /**
     * Get balanceUpdatedAt
     *
     * @return \DateTime
     */
    public function getBalanceUpdatedAt()
    {
        return $this->balanceUpdatedAt;
    }

    /**
     * Set hash
     *
     * @param string $hash
     *
     * @return UserCard
     */
    public function setHash($hash)
    {
        $this->hash = $hash;

        return $this;
    }

    /**
     * Set issuedAt
     *
     * @param \DateTime $issuedAt
     *
     * @return UserCard
     */
    public function setIssuedAt($issuedAt)
    {
        $this->issuedAt = $issuedAt;

        return $this;
    }

    /**
     * Get issuedAt
     *
     * @return \DateTime
     */
    public function getIssuedAt()
    {
        return $this->issuedAt;
    }

    /**
     * Set balanceChangedAt
     *
     * @param \DateTime $balanceChangedAt
     *
     * @return UserCard
     */
    public function setBalanceChangedAt($balanceChangedAt)
    {
        $this->balanceChangedAt = $balanceChangedAt;

        return $this;
    }

    /**
     * Get balanceChangedAt
     *
     * @return \DateTime
     */
    public function getBalanceChangedAt()
    {
        return $this->balanceChangedAt;
    }

    /**
     * Add snap
     *
     * @param \CoreBundle\Entity\UserCardSnap $snap
     *
     * @return UserCard
     */
    public function addSnap(\CoreBundle\Entity\UserCardSnap $snap)
    {
        $this->snaps[] = $snap;

        return $this;
    }

    /**
     * Remove snap
     *
     * @param \CoreBundle\Entity\UserCardSnap $snap
     */
    public function removeSnap(\CoreBundle\Entity\UserCardSnap $snap)
    {
        $this->snaps->removeElement($snap);
    }

    /**
     * Get snaps
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getSnaps()
    {
        return $this->snaps;
    }

    /**
     * Set initializedAt
     *
     * @param \DateTime $initializedAt
     *
     * @return UserCard
     */
    public function setInitializedAt($initializedAt)
    {
        $this->initializedAt = $initializedAt;

        return $this;
    }

    /**
     * Get initializedAt
     *
     * @return \DateTime
     */
    public function getInitializedAt()
    {
        return $this->initializedAt;
    }

    /**
     * Set billingAddress
     *
     * @param \CoreBundle\Entity\UserBillingAddress $billingAddress
     *
     * @return UserCard
     */
    public function setBillingAddress(\CoreBundle\Entity\UserBillingAddress $billingAddress = null)
    {
        $this->billingAddress = $billingAddress;

        return $this;
    }

    /**
     * Get billingAddress
     *
     * @return \CoreBundle\Entity\UserBillingAddress
     */
    public function getBillingAddress()
    {
        return $this->billingAddress;
    }

    /**
     * Set nativeStatus
     *
     * @param string $nativeStatus
     *
     * @return UserCard
     */
    public function setNativeStatus($nativeStatus)
    {
        $old = $this->nativeStatus;
        $this->nativeStatus = $nativeStatus;

        if ($old !== $nativeStatus) {
            UserCardStatus::create($this, $old, $nativeStatus, UserCardStatus::TYPE_NATIVE_STATUS);
        }

        return $this;
    }

    /**
     * Get nativeStatus
     *
     * @return string
     */
    public function getNativeStatus()
    {
        return $this->nativeStatus;
    }

    /**
     * Set inventory
     *
     * @param \CoreBundle\Entity\CardInventory $inventory
     *
     * @return UserCard
     */
    public function setInventory(\CoreBundle\Entity\CardInventory $inventory = null)
    {
        $this->inventory = $inventory;

        return $this;
    }

    /**
     * Get inventory
     *
     * @return \CoreBundle\Entity\CardInventory
     */
    public function getInventory()
    {
        return $this->inventory;
    }

    /**
     * Set shippingMethod
     *
     * @param string $shippingMethod
     *
     * @return UserCard
     */
    public function setShippingMethod($shippingMethod)
    {
        $this->shippingMethod = $shippingMethod;

        return $this;
    }

    /**
     * Get shippingMethod
     *
     * @return string
     */
    public function getShippingMethod()
    {
        return $this->shippingMethod ?: 1;
    }

    /**
     * Set nickName
     *
     * @param string $nickName
     *
     * @return UserCard
     */
    public function setNickName($nickName)
    {
        $this->nickName = $nickName;

        return $this;
    }

    /**
     * Get nickName
     *
     * @return string
     */
    public function getNickName()
    {
        return $this->nickName;
    }

    /**
     * Set type
     *
     * @param string $type
     *
     * @return UserCard
     */
    public function setType($type)
    {
        $this->type = $type;

        return $this;
    }

    /**
     * Get type
     *
     * @return string
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * Set spendLimit
     *
     * @param integer $spendLimit
     *
     * @return UserCard
     */
    public function setSpendLimit($spendLimit)
    {
        $this->spendLimit = $spendLimit;

        return $this;
    }

    /**
     * Get spendLimit
     *
     * @return integer|null
     */
    public function getSpendLimit()
    {
        return $this->spendLimit;
    }

    /**
     * Set spendLimitDuration
     *
     * @param string $spendLimitDuration
     *
     * @return UserCard
     */
    public function setSpendLimitDuration($spendLimitDuration)
    {
        $this->spendLimitDuration = $spendLimitDuration;

        return $this;
    }

    /**
     * Get spendLimitDuration
     *
     * @return string
     */
    public function getSpendLimitDuration()
    {
        return $this->spendLimitDuration;
    }

    /**
     * Add decline
     *
     * @param \CoreBundle\Entity\UserCardDecline $decline
     *
     * @return UserCard
     */
    public function addDecline(\CoreBundle\Entity\UserCardDecline $decline)
    {
        $this->declines[] = $decline;

        return $this;
    }

    /**
     * Remove decline
     *
     * @param \CoreBundle\Entity\UserCardDecline $decline
     */
    public function removeDecline(\CoreBundle\Entity\UserCardDecline $decline)
    {
        $this->declines->removeElement($decline);
    }

    /**
     * Get declines
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getDeclines()
    {
        return $this->declines;
    }

    /**
     * Set merchant
     *
     * @param \CoreBundle\Entity\Merchant $merchant
     *
     * @return UserCard
     */
    public function setMerchant(\CoreBundle\Entity\Merchant $merchant = null)
    {
        $this->merchant = $merchant;

        return $this;
    }

    /**
     * Get merchant
     *
     * @return \CoreBundle\Entity\Merchant
     */
    public function getMerchant()
    {
        return $this->merchant;
    }

    /**
     * Set accountId
     *
     * @param integer $accountId
     *
     * @return UserCard
     */
    public function setAccountId($accountId)
    {
        $this->accountId = $accountId;

        return $this;
    }

    /**
     * Get accountId
     *
     * @return integer
     */
    public function getAccountId()
    {
        return $this->accountId;
    }

    /**
     * Set providerAccountId
     *
     * @param integer $providerAccountId
     *
     * @return UserCard
     */
    public function setproviderAccountId($providerAccountId)
    {
        $this->providerAccountId = $providerAccountId;

        return $this;
    }

    /**
     * Get providerAccountId
     *
     * @return integer
     */
    public function getproviderAccountId()
    {
        return $this->providerAccountId;
    }

    /**
     * Set plaidAccessToken
     *
     * @param string $plaidAccessToken
     *
     * @return UserCard
     */
    public function setPlaidAccessToken($plaidAccessToken)
    {
        $this->plaidAccessToken = $plaidAccessToken;

        return $this;
    }

    /**
     * Get plaidAccessToken
     *
     * @return string
     */
    public function getPlaidAccessToken()
    {
        return $this->plaidAccessToken;
    }


    /**
     * Add purse
     *
     * @param \CoreBundle\Entity\UserCardPurse $purse
     *
     * @return UserCard
     */
    public function addPurse(\CoreBundle\Entity\UserCardPurse $purse)
    {
        $this->purses[] = $purse;

        return $this;
    }

    /**
     * Remove purse
     *
     * @param \CoreBundle\Entity\UserCardPurse $purse
     */
    public function removePurse(\CoreBundle\Entity\UserCardPurse $purse)
    {
        $this->purses->removeElement($purse);
    }

    /**
     * Get purses
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getPurses()
    {
        return $this->purses;
    }

    /**
     * Set activityTime
     *
     * @param \DateTime $activityTime
     *
     * @return UserCard
     */
    public function setActivityTime($activityTime)
    {
        $this->activityTime = $activityTime;

        return $this;
    }

    /**
     * Get activityTime
     *
     * @return \DateTime
     */
    public function getActivityTime()
    {
        return $this->activityTime;
    }

    /**
     * Add identity
     *
     * @param \SpendrBundle\Entity\PlaidIdentity $identity
     *
     * @return UserCard
     */
    public function addIdentity(\SpendrBundle\Entity\PlaidIdentity $identity)
    {
        $this->identities[] = $identity;

        return $this;
    }

    /**
     * Remove identity
     *
     * @param \SpendrBundle\Entity\PlaidIdentity $identity
     */
    public function removeIdentity(\SpendrBundle\Entity\PlaidIdentity $identity)
    {
        $this->identities->removeElement($identity);
    }

    /**
     * Get identities
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getIdentities()
    {
        return $this->identities;
    }

    /**
     * Get spendrLocations
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getSpendrLocations()
    {
        return $this->spendrLocations;
    }

    public function getCardProvider(): ?string
    {
        return $this->cardProvider;
    }

    public function setCardProvider(?string $cardProvider): static
    {
        $this->cardProvider = $cardProvider;

        return $this;
    }

    public function getDeactivatingAt(): ?\DateTimeInterface
    {
        return $this->deactivatingAt;
    }

    public function setDeactivatingAt(?\DateTimeInterface $deactivatingAt): static
    {
        $this->deactivatingAt = $deactivatingAt;

        return $this;
    }

    /**
     * @return Collection<int, UserCardStatus>
     */
    public function getStatuses(): Collection
    {
        return $this->statuses;
    }

    public function addStatus(UserCardStatus $status): static
    {
        if (!$this->statuses->contains($status)) {
            $this->statuses->add($status);
            $status->setUserCard($this);
        }

        return $this;
    }

    public function removeStatus(UserCardStatus $status): static
    {
        if ($this->statuses->removeElement($status)) {
            // set the owning side to null (unless already changed)
            if ($status->getUserCard() === $this) {
                $status->setUserCard(null);
            }
        }

        return $this;
    }

    public function addSpendrLocation(Location $spendrLocation): static
    {
        if (!$this->spendrLocations->contains($spendrLocation)) {
            $this->spendrLocations->add($spendrLocation);
            $spendrLocation->setBankCard($this);
        }

        return $this;
    }

    public function removeSpendrLocation(Location $spendrLocation): static
    {
        if ($this->spendrLocations->removeElement($spendrLocation)) {
            // set the owning side to null (unless already changed)
            if ($spendrLocation->getBankCard() === $this) {
                $spendrLocation->setBankCard(null);
            }
        }

        return $this;
    }
}
