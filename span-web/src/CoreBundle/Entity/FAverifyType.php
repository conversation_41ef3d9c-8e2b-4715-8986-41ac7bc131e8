<?php

namespace CoreBundle\Entity;

use Api<PERSON><PERSON>le\Entity\ApiEntityInterface;
use CoreBundle\Services\GeoIpService;
use CoreBundle\Utils\Util;
use Doctrine\ORM\Mapping as ORM;

/**
 * FAverifyType
 *
 * @ORM\Table(name="fa_verify_type", options={"comment":"User 2FA type change record"})
 * @ORM\Entity(repositoryClass="CoreBundle\Repository\FAverifyTypeRepository")
 */
class FAverifyType extends BaseEntity 
{
  
    /**
     * @ORM\ManyToOne(targetEntity="SalexUserBundle\Entity\User")
     * @ORM\JoinColumn(name="users_id", referencedColumnName="id", onDelete="cascade")
     */
    private $users;

    /**
     * @var string
     *
     * @ORM\Column(name="from_tpye", type="string", nullable=true)
     */
    private $fromType;

    /**
     * @var string
     *
     * @ORM\Column(name="to_type", type="string")
     */
    private $toType;

    /**
     * @var string
     *
     * @ORM\Column(name="platform_id", type="string", nullable=true)
     */
    private $platform;


    /**
     * Set fromType
     *
     * @param string $fromType
     *
     * @return FAverifyType
     */
    public function setFromType($fromType)
    {
        $this->fromType = $fromType;

        return $this;
    }

    /**
     * Get fromType
     *
     * @return string
     */
    public function getFromType()
    {
        return $this->fromType;
    }

    /**
     * Set toType
     *
     * @param string $toType
     *
     * @return FAverifyType
     */
    public function setToType($toType)
    {
        $this->toType = $toType;

        return $this;
    }

    /**
     * Get toType
     *
     * @return string
     */
    public function getToType()
    {
        return $this->toType;
    }

    /**
     * Set platform
     *
     * @param string $platform
     *
     * @return FAverifyType
     */
    public function setPlatform($platform)
    {
        $this->platform = $platform;

        return $this;
    }

    /**
     * Get platform
     *
     * @return string
     */
    public function getPlatform()
    {
        return $this->platform;
    }

    /**
     * Set users
     *
     * @param \SalexUserBundle\Entity\User $users
     *
     * @return FAverifyType
     */
    public function setUsers(\SalexUserBundle\Entity\User $users = null)
    {
        $this->users = $users;

        return $this;
    }

    /**
     * Get users
     *
     * @return \SalexUserBundle\Entity\User
     */
    public function getUsers()
    {
        return $this->users;
    }
}
