<?php
/**
 * User: Bob
 * Date: 2017/2/21
 * Time: 12:25
 * This is used to record Fee Global Name information
 */

namespace CoreBundle\Entity;

use CoreBundle\Utils\Traits\ConstantTrait;
use CoreBundle\Utils\Util;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\Mapping as ORM;
use J<PERSON>\Serializer\Annotation as Serializer;
use Symfony\Component\Validator\Constraints as Assert;
use function Stringy\create as s;

/**
 * Class FeeGlobalName
 * @ORM\Entity
 * @ORM\Table(name="fee_global_name")
 * @package CoreBundle\Entity
 */
class FeeGlobalName extends BaseEntity
{
    use ConstantTrait;

    public const BANK = 'Bank';
    public const SOFORT = 'Sofort';
    public const YANDEX = 'Yandex';
    public const IDEAL = 'IDEAL';
    public const ALIPAY = 'Alipay';
    public const WEBMONEY = 'Webmoney';
    public const WESTERN_UNION = 'Western Union';
    public const SAFETYPAY = 'SafetyPay';
    public const BOLETO = 'Boleto';
    public const CASHU = 'cashu';
    public const ONE_TIME_MEMBERSHIP_FEE = 'One-time membership fee';
    public const REPLACEMENT_FEE_ACTIVE_CARD = 'Replacement fee active card';
    public const REPLACEMENT_FEE_CLOSED_CARD = 'Replacement fee closed card';
    public const UNLOAD_FEE = 'Unload fee';
    public const APPROVED_TRANSACTION_FEE = 'Approved Transaction';
    public const DECLINED_TRANSACTION_FEE = 'Declined Transaction';
    public const MONTHLY_FEE = 'Monthly Maintenance fee';
    public const ADMIN_DEBIT = 'Admin Debit';
    public const COW_LOAD_FEE = 'Cash on Web load fee';
    public const CARD_CREATION_FEE = 'Card creation fee';
    public const PAYPAL_SUBSCRIPTION_FEE = 'PayPal Subscription Fee';
    public const PAYPAL_SUBSCRIPTION_ANNUAL_FEE = 'PayPal Subscription Annual Fee';
    public const PAYPAL_SUBSCRIPTION_MONTHLY_FEE = 'PayPal Subscription Monthly Fee';

    public const TYPE_FEE = 'fee';
    public const TYPE_TRANSACTION = 'transaction';
    public const TYPE_EVENT = 'event';

    public static function isLoadFee($name) {
        return in_array($name, [
            self::BANK,
            self::SOFORT,
            self::YANDEX,
            self::IDEAL,
            self::ALIPAY,
            self::WEBMONEY,
            self::WESTERN_UNION,
            self::SAFETYPAY,
            self::BOLETO,
            self::CASHU,
        ], true);
    }

    public static function getKey($name) {
        if (self::isLoadFee($name)) {
            $key = 'load';
        } else if (self::ONE_TIME_MEMBERSHIP_FEE === $name) {
            $key = 'membership';
        } else if (self::REPLACEMENT_FEE_ACTIVE_CARD === $name || self::REPLACEMENT_FEE_CLOSED_CARD === $name) {
            $key = 'replacement';
        } else if (self::UNLOAD_FEE === $name) {
            $key = 'unload';
        } else {
            $name = str_replace(' fee', '', $name);
            $key = s($name)->camelize();
        }
        if (Util::endsWith($key, 'Fee')) {
            return (string)$key;
        }
        return $key . 'Fee';
    }

    /**
     * @var string $name
     *
     * @ORM\Column(name="name", type="string")
     * @Assert\NotBlank()
     */
    private $name;

    /**
     * @ORM\ManyToOne(targetEntity="FeeCategory", inversedBy="globals")
     */
    private $feeCategory;

    /**
     * @var ArrayCollection
     * @ORM\OneToMany(targetEntity="CoreBundle\Entity\FeeItem", mappedBy="feeGlobalName")
     * @Serializer\Exclude()
     */
    private $items;

    /**
     * @var string $type
     *
     * @ORM\Column(name="type", type="string", options={"default":"fee"})
     */
    private $type;


    /**
     * Set name
     *
     * @param string $name
     *
     * @return FeeGlobalName
     */
    public function setName($name)
    {
        $this->name = $name;

        return $this;
    }

    /**
     * Get name
     *
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * Set feeCategory
     *
     * @param \CoreBundle\Entity\FeeCategory $feeCategory
     *
     * @return FeeGlobalName
     */
    public function setFeeCategory(\CoreBundle\Entity\FeeCategory $feeCategory = null)
    {
        $this->feeCategory = $feeCategory;

        return $this;
    }

    /**
     * Get feeCategory
     *
     * @return \CoreBundle\Entity\FeeCategory
     */
    public function getFeeCategory()
    {
        return $this->feeCategory;
    }
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->items = new \Doctrine\Common\Collections\ArrayCollection();
    }

    /**
     * Add item
     *
     * @param \CoreBundle\Entity\FeeItem $item
     *
     * @return FeeGlobalName
     */
    public function addItem(\CoreBundle\Entity\FeeItem $item)
    {
        $this->items[] = $item;

        return $this;
    }

    /**
     * Remove item
     *
     * @param \CoreBundle\Entity\FeeItem $item
     */
    public function removeItem(\CoreBundle\Entity\FeeItem $item)
    {
        $this->items->removeElement($item);
    }

    /**
     * Get items
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getItems()
    {
        return $this->items;
    }

    /**
     * Set type
     *
     * @param string $type
     *
     * @return FeeGlobalName
     */
    public function setType($type)
    {
        $this->type = $type;

        return $this;
    }

    /**
     * Get type
     *
     * @return string
     */
    public function getType()
    {
        return $this->type;
    }
}
