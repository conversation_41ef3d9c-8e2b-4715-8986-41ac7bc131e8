<?php

namespace CoreBundle\Entity;

use AdminBundle\Controller\User\UserSearchController;
use Carbon\Carbon;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Util;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\SoftDeleteable\Traits\SoftDeleteableEntity;
use PortalBundle\Util\RegisterStep;
use SalexUserBundle\Entity\User;
use Symfony\Component\HttpFoundation\Request;
use UsUnlockedBundle\Services\ReferService;

/**
 * Promotion
 *
 * @ORM\Table(name="promotion", options={"comment":"Promotion settings / history"})
 * @ORM\Entity(repositoryClass="CoreBundle\Repository\PromotionRepository")
 */
class Promotion extends BaseEntity
{
    use SoftDeleteableEntity;

    const CODE_ONE_YEAR = 'ONEYEAR';
    const CODE_THANKS = 'THANKS';

    const STATUS_ACTIVE = null;
    const STATUS_PAUSED = 'paused';
    const STATUS_EXPIRED = 'expired';

    const FILLS = [
        'name', 'discountFixed',
        'discountRatio', 'currency', 'startAt', 'endAt',
        'minLoad', 'redemptionLimit', 'status',
        'constraints',
    ];

    // https://github.com/terncommerce/span/issues/1277
    public static function applyUsuOneYearMembershipFee(User $user, $referCode)
    {
        if ($referCode !== self::CODE_ONE_YEAR) {
            return;
        }

        $tz = Util::tzNewYork();
        $now = Carbon::now($tz);
        $start = Carbon::create(2021, 4, 4, 23, 59, 00, $tz);
        $end = Carbon::create(2021, 4, 19, 23, 59, 59, $tz);
        if ($now->lt($start) || $now->gt($end)) {
            return;
        }

        if ($user->getRegisterStep() === RegisterStep::PENDING && !$user->getAffiliate())
        {
            Util::updateMeta($user, [
                'skipMembershipFee' => 'promotionOneYear',
            ]);
        }
    }

    public static function applyUsuThanksLoadFee(User $user, $referCode = null)
    {
        if (!$referCode) {
            $referCode = Util::meta($user, 'regReferCode');
        }

        if ($referCode) {
            Util::updateMeta($user, [
                'regReferCode' => $referCode,
            ]);
        }

        if ($referCode !== self::CODE_THANKS) {
            return;
        }

        $tz = Util::tzNewYork();
        $now = Carbon::now($tz);
        $start = Carbon::create(2021, 11, 18, 23, 59, 00, $tz);
        $end = Carbon::create(2021, 11, 26, 23, 59, 59, $tz);
        if ($now->lt($start) || $now->gt($end)) {
            return;
        }

        Util::updateMeta($user, [
            'skipLoadFee' => 'promotionThanks',
        ]);
    }

    protected static function isUsuCommonDayRangeCampaign(Carbon $start, Carbon $end, UserCard $uc = null, UserCardLoad $ucl = null)
    {
        if ($ucl && !$uc) {
            $uc = $ucl->getUserCard();
        }
        if ($uc && !$uc->isUsUnlocked()) {
            return false;
        }
        $platform = Util::platform();
        if ($platform && !$platform->isTernCommerce()) {
            return false;
        }
        $tz = Util::tzNewYork();
        $now = Carbon::now($tz);

        if ($ucl) {
            $initAt = $ucl->getInitializedAt();
            if ($initAt && $end->addDays(5)->gt($now)) {
                $now = Carbon::instance($initAt);
            }
        }

        if (Util::isStaging()) {
            $start->subDay(1);
            $end->subDay(1);
        }

        return $now->gte($start) && $now->lte($end);
    }

    // https://app.asana.com/0/1198894128947639/1203409888709293
    public static function isUsuBlackFridayCampaign2022(UserCard $uc = null, UserCardLoad $ucl = null)
    {
        $tz = Util::tzNewYork();
        $start = Carbon::create(2022, 12, 8, 0, 00, 00, $tz);
        $end = Carbon::create(2022, 12, 31, 23, 59, 59, $tz);
        return self::isUsuCommonDayRangeCampaign($start, $end, $uc, $ucl);
    }

    // https://app.asana.com/0/1201396416449164/1203875529112108/f
    public static function isUsuValentineDayCampaign2023(UserCard $uc = null, UserCardLoad $ucl = null)
    {
        $tz = Util::tzNewYork();
        $start = Carbon::create(2023, 2, 7, 0, 00, 00, $tz);
        $end = Carbon::create(2023, 2, 14, 23, 59, 59, $tz);
        return self::isUsuCommonDayRangeCampaign($start, $end, $uc, $ucl);
    }

    // https://app.asana.com/0/1198894128947639/1204953063879286
    public static function isUsuAmazonPrimeDayCampaign2023(UserCard $uc = null, UserCardLoad $ucl = null)
    {
        $tz = Util::tzNewYork();
        $start = Carbon::create(2023, 7, 6, 0, 00, 00, $tz);
        $end = Carbon::create(2023, 7, 12, 23, 59, 59, $tz);
        return self::isUsuCommonDayRangeCampaign($start, $end, $uc, $ucl);
    }

    // https://app.asana.com/0/1198894128947639/1205157137104432
    public static function isUsuBackToSchoolSpecial2023(UserCard $uc = null, UserCardLoad $ucl = null)
    {
        if (!$uc) {
            return false;
        }
        $user = $uc->getUser();
        $createdAt = $user->getCreatedAt();
        if (!$createdAt) {
            return false;
        }
        $tz = Util::tzNewYork();
        if (Carbon::create(2023, 7, 28, 0, 0, 0, $tz)->lt($createdAt)) {
            return false;
        }
        $start = Carbon::create(2023, 7, 28, 0, 00, 00, $tz);
        $end = Carbon::create(2023, 8, 31, 23, 59, 59, $tz);
        return self::isUsuCommonDayRangeCampaign($start, $end, $uc, $ucl);
    }

    public static function applyUsuLoadFeePromotion(UserCard $uc = null, UserCardLoad $load = null, $amount = null)
    {
        if (self::isUsuBackToSchoolSpecial2023($uc, $load)) {
            return 0;
        }
//        if (self::isUsuAmazonPrimeDayCampaign2023($uc, $load)) {
//            return 0;
//        }
//        if (self::isUsuValentineDayCampaign2023($uc, $load)) {
//            return 0;
//        }
//        if (self::isUsuBlackFridayCampaign2022($uc, $load)) {
//            return 0;
//        }
        return false;
    }

    public static function applyUsuReferralPromotion(UserCard $uc = null, $amount = null)
    {
        $amount = $amount ?? ReferService::REWARD_AMOUNT;
//        if (self::isUsuBlackFridayCampaign2022($uc)) {
//            return $amount * 2;
//        }
        return $amount;
    }

    public function matchUser(User $user)
    {
        $constraints = $this->getConstraints();
        if (!$constraints) {
            return false;
        }
        $data = [];
        parse_str($constraints, $data);
        $request = new Request([], $data);
        $request->setMethod('POST');
        return UserSearchController::matchAction($request, $user);
    }

    public function getDateRange()
    {
        $start = $this->getStartAt();
        $end = $this->getEndAt();
        return implode(' ', [
            $start ? $start->format(Util::DATE_TIME_FORMAT) : '',
            '~',
            $end ? $end->format(Util::DATE_TIME_FORMAT) : '',
        ]);
    }

    public function inDateRange()
    {
        $now = Carbon::now();
        $end = $this->getEndAt();
        if ($end && $now->gt(Carbon::instance($end))) {
            return false;
        }
        $start = $this->getStartAt();
        if ($start && $now->lt(Carbon::instance($start))) {
            return false;
        }
        return true;
    }

    public function getUpOfUser(User $user)
    {
        $self = $this;
        $ups = $user->getPromotions()->filter(function (UserPromotion $up) use ($self) {
            return Util::eq($self, $up->getPromotion());
        });
        return $ups->first();
    }

    public function getLeftRedemption(User $user)
    {
        $limit = $this->getRedemptionLimit();
        if ($limit === null) {
            return 'Unlimited';
        }
        if ($limit <= 0) {
            return 0;
        }
        $up = $this->getUpOfUser($user);
        if (!$up) {
            return $limit;
        }
        return $up->getLeftRedemption();
    }

    public function hasLeftRedemption(User $user)
    {
        $left = $this->getLeftRedemption($user);
        return $left === 'Unlimited' || $left > 0;
    }

    public function isValid(User $user = null)
    {
        $status = $this->getStatus();
        if ($status && $status !== 'active') {
            return false;
        }
        if (!$this->inDateRange()) {
            return false;
        }
        if ($user && !$this->hasLeftRedemption($user)) {
            return false;
        }
        return true;
    }

    public function getDiscountSummary(CardProgramFeeItem $feeItem, $totalAmount, $currency)
    {
        $default = [
            'fixed' => 0,
            'ratio' => 0,
        ];
        $fixedCurrency = $feeItem->getFeeItem()->getCostTernFixedCurrency();
        $minLoad = $this->getMinLoad();
        if ($minLoad) {
            $minLoad = Money::convertWithExtra($minLoad, $fixedCurrency, $currency);
            if ($totalAmount < $minLoad) {
                return $default;
            }
        }
        $fixed = $this->getDiscountFixed();
        if ($fixed) {
            $default['fixed'] = Money::convertWithExtra($fixed, $fixedCurrency, $currency);
        }
        $default['ratio'] = $this->getDiscountRatio() ?: 0;
        return $default;
    }

    /**
     * Get status
     *
     * @return string
     */
    public function getStatus()
    {
        $endAt = $this->getEndAt();
        if ($endAt && Carbon::instance($endAt)->lt(Carbon::now())) {
            return self::STATUS_EXPIRED;
        }
        $status = $this->status;
        if (!$status) {
            return 'active';
        }
        return $status;
    }

    /**
     * @var string
     *
     * @ORM\Column(name="name", type="string", length=255)
     */
    private $name;

    /**
     * @ORM\ManyToMany(targetEntity="CoreBundle\Entity\CardProgramFeeItem")
     */
    private $fees;

    /**
     * @ORM\OneToMany(targetEntity="CoreBundle\Entity\UserPromotion", mappedBy="promotion")
     */
    private $users;

    /**
     * @var int
     *
     * @ORM\Column(name="discount_fixed", type="integer", nullable=true)
     */
    private $discountFixed;

    /**
     * @var float
     *
     * @ORM\Column(name="discount_ratio", type="float", nullable=true)
     */
    private $discountRatio;

    /**
     * @var string
     *
     * @ORM\Column(name="currency", type="string", length=255, nullable=true)
     */
    private $currency;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="start_at", type="datetime", nullable=true)
     */
    private $startAt;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="end_at", type="datetime", nullable=true)
     */
    private $endAt;

    /**
     * @var int
     *
     * @ORM\Column(name="min_load", type="integer", nullable=true, options={"comment":"Minimum load amount required to apply discount"})
     */
    private $minLoad;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=255, nullable=true)
     */
    private $status;

    /**
     * @var int
     *
     * @ORM\Column(name="redemption_limit", type="integer", nullable=true, options={"comment":"Discount apply times limit for each user"})
     */
    private $redemptionLimit;

    /**
     * @var string
     *
     * @ORM\Column(name="constraints", type="text", nullable=true, options={"comment":"Search filters for users"})
     */
    private $constraints;

    /**
     * Set name
     *
     * @param string $name
     *
     * @return Promotion
     */
    public function setName($name)
    {
        $this->name = $name;

        return $this;
    }

    /**
     * Get name
     *
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * Set discountFixed
     *
     * @param integer $discountFixed
     *
     * @return Promotion
     */
    public function setDiscountFixed($discountFixed)
    {
        $this->discountFixed = $discountFixed;

        return $this;
    }

    /**
     * Get discountFixed
     *
     * @return int
     */
    public function getDiscountFixed()
    {
        return $this->discountFixed;
    }

    /**
     * Set discountRatio
     *
     * @param float $discountRatio
     *
     * @return Promotion
     */
    public function setDiscountRatio($discountRatio)
    {
        $this->discountRatio = $discountRatio;

        return $this;
    }

    /**
     * Get discountRatio
     *
     * @return float
     */
    public function getDiscountRatio()
    {
        return $this->discountRatio;
    }

    /**
     * Set startAt
     *
     * @param \DateTime $startAt
     *
     * @return Promotion
     */
    public function setStartAt($startAt)
    {
        $this->startAt = $startAt;

        return $this;
    }

    /**
     * Get startAt
     *
     * @return \DateTime
     */
    public function getStartAt()
    {
        return $this->startAt;
    }

    /**
     * Set endAt
     *
     * @param \DateTime $endAt
     *
     * @return Promotion
     */
    public function setEndAt($endAt)
    {
        $this->endAt = $endAt;

        return $this;
    }

    /**
     * Get endAt
     *
     * @return \DateTime
     */
    public function getEndAt()
    {
        return $this->endAt;
    }

    /**
     * Set minLoad
     *
     * @param integer $minLoad
     *
     * @return Promotion
     */
    public function setMinLoad($minLoad)
    {
        $this->minLoad = $minLoad;

        return $this;
    }

    /**
     * Get minLoad
     *
     * @return int
     */
    public function getMinLoad()
    {
        return $this->minLoad;
    }

    /**
     * Set status
     *
     * @param string $status
     *
     * @return Promotion
     */
    public function setStatus($status)
    {
        $this->status = $status;

        return $this;
    }

    /**
     * Set redemptionLimit
     *
     * @param integer $redemptionLimit
     *
     * @return Promotion
     */
    public function setRedemptionLimit($redemptionLimit)
    {
        $this->redemptionLimit = $redemptionLimit;

        return $this;
    }

    /**
     * Get redemptionLimit
     *
     * @return int
     */
    public function getRedemptionLimit()
    {
        return $this->redemptionLimit;
    }

    /**
     * Set constraints
     *
     * @param string $constraints
     *
     * @return Promotion
     */
    public function setConstraints($constraints)
    {
        $this->constraints = $constraints;

        return $this;
    }

    /**
     * Get constraints
     *
     * @return string
     */
    public function getConstraints()
    {
        return $this->constraints;
    }

    /**
     * Set currency
     *
     * @param string $currency
     *
     * @return Promotion
     */
    public function setCurrency($currency)
    {
        $this->currency = $currency;

        return $this;
    }

    /**
     * Get currency
     *
     * @return string
     */
    public function getCurrency()
    {
        return $this->currency ?: 'USD';
    }
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->users = new ArrayCollection();
        $this->fees = new ArrayCollection();
    }

    /**
     * Add user
     *
     * @param \CoreBundle\Entity\UserPromotion $user
     *
     * @return Promotion
     */
    public function addUser(\CoreBundle\Entity\UserPromotion $user)
    {
        $this->users[] = $user;

        return $this;
    }

    /**
     * Remove user
     *
     * @param \CoreBundle\Entity\UserPromotion $user
     */
    public function removeUser(\CoreBundle\Entity\UserPromotion $user)
    {
        $this->users->removeElement($user);
    }

    /**
     * Get users
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getUsers()
    {
        return $this->users;
    }

    /**
     * Add fee
     *
     * @param \CoreBundle\Entity\CardProgramFeeItem $fee
     *
     * @return Promotion
     */
    public function addFee(\CoreBundle\Entity\CardProgramFeeItem $fee)
    {
        $this->fees[] = $fee;

        return $this;
    }

    /**
     * Remove fee
     *
     * @param \CoreBundle\Entity\CardProgramFeeItem $fee
     */
    public function removeFee(\CoreBundle\Entity\CardProgramFeeItem $fee)
    {
        $this->fees->removeElement($fee);
    }

    /**
     * Get fees
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getFees()
    {
        return $this->fees;
    }
}
