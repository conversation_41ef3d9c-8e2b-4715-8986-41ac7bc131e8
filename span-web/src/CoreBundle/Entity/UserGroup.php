<?php

namespace CoreBundle\Entity;

use Api<PERSON><PERSON>le\Entity\ApiEntityInterface;
use CoreBundle\Utils\Traits\EntityTrait;
use CoreBundle\Utils\Util;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;
use SalexUserBundle\Entity\User;
use SalexUserBundle\Entity\UserConfig;
use TransferMexBundle\Entity\UserGroupTransferMexTrait;

/**
 * UserGroup
 *
 * @ORM\Table(name="user_group", indexes={
 *     @ORM\Index(columns={"name"}),
 *     @ORM\Index(columns={"botm_business_account_id"}),
 * })
 * @ORM\Entity(repositoryClass="CoreBundle\Repository\UserGroupRepository")
 */
class UserGroup extends BaseEntity implements ApiEntityInterface
{
    use UserGroupTransferMexTrait;
    use EntityTrait;

    public const NAME_RECIPIENTS = '__recipients__';
    public const NAME_RECIPIENT_METHODS = '__recipient__methods__';

    public const FUNDING_TYPE_PREFUNDED = 'Prefunded';
    public const FUNDING_TYPE_ACH = 'ACH';

    public const CARD_PROVIDER_RAPID = 'Rapid';
    public const CARD_PROVIDER_BOTM = 'BOTM';
    public const CARD_PROVIDER_TERN = 'TERN';

    public function getSignature()
    {
        return $this->getId() . ' ~ ' . $this->getName();
    }

    /**
     * @param bool $extra
     *
     * @return array
     */
    public function toApiArray(bool $extra = FALSE): array
    {
        return [
            'id' => $this->getId(),
            'name' => $this->getName(),
            'fundingType' => $this->getFundingType(),
        ];
    }

    public function isPrefunded()
    {
        return $this->getFundingType() === self::FUNDING_TYPE_PREFUNDED;
    }

    public function isACH()
    {
        return $this->getFundingType() === self::FUNDING_TYPE_ACH;
    }

    public function isBOTM()
    {
        return $this->getCardProvider() === self::CARD_PROVIDER_BOTM;
    }

    public function isTERN()
    {
        return $this->getCardProvider() === self::CARD_PROVIDER_TERN;
    }

    public function isRapid()
    {
        return $this->getCardProvider() === self::CARD_PROVIDER_RAPID;
    }

    public function getRawRapidAgentNumber()
    {
        return Util::meta($this, 'rapidAgentNumber');
    }

    public function getRapidAgentNumber()
    {
        $agent = Util::meta($this, 'rapidAgentNumber');
        if ($agent) {
            return $agent;
        }
        $platform = $this->getPlatform() ?? Util::platform();
        if ($platform) {
            $agent = Util::meta($platform, 'rapidAgentNumber');
            if (!$agent && $platform->isTransferMex()) {
                $suffix = Util::isLive() ? '' : '_test';
                $agent = Util::getParameter('rapid_agent_account_number' . $suffix);
            }
        }
        return $agent;
    }

    public function queryUsers(Callable $where = null, $returnQuery = false)
    {
        $query = Util::em()->getRepository(User::class)
            ->createQueryBuilder('u')
            ->join('u.userGroups', 'ug')
            ->where('ug = :group')
            ->setParameter('group', $this)
            ->orderBy('u.createdAt', 'desc')
            ->distinct();

        if ($where) {
            $where($query);
        }

        if ($returnQuery) {
            return $query;
        }

        return $query
            ->getQuery()
            ->getResult();
    }

    public function getUserCount($step = null, $type = null)
    {
        $q = Util::em()->getRepository(User::class)
            ->createQueryBuilder('u')
            ->join('u.userGroups', 'ug')
            ->join('u.config', 'config')
            ->join('u.cards', 'uc')
            ->where('ug = :group')
            ->setParameter('group', $this)
        ;

        if ($step) {
            $q->andWhere('u.register_step = :step')
                ->setParameter('step', $step);
        }

        if ($type == 'migrated_botm') {
          $q->andWhere(Util::expr()->like('uc.accountNumber',':isBotm'))
            ->setParameter('isBotm', '%-%');
        } else if ($type == 'linked_botm') {
          $q->andWhere(Util::expr()->isNotNull('config.botmUserAccountId'));
        }

        return (int)($q->select('count(u)')
            ->getQuery()
            ->getSingleScalarResult());
    }

    public function ensureUser(User $user)
    {
        foreach ($this->getUsers() as $u) {
            if (Util::eq($user, $u)) {
                return;
            }
        }
        $this->addUser($user);
        $user->addUserGroup($this);
    }

    /**
     * @return User|null
     */
    public function getPrimaryAdmin()
    {
        $configs = $this->getAdminConfigs();
        if (!$configs || $configs->isEmpty()) {
            return null;
        }
        /** @var UserConfig $config */
        $config = $configs->first();
        return $config->getUser();
    }

    public function getProcessor()
    {
        $provider = $this->getCardProvider();
        if ($provider !== self::CARD_PROVIDER_RAPID) {
            return Processor::find($provider);
        }
        return $this->getCardProgram()->getProcessor();
    }

    public function getApiImpl()
    {
        return $this->getProcessor()->getApiImplForUserGroup($this);
    }

    /**
     * @var Platform
     *
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\Platform")
     */
    private $platform;

    /**
     * @var CardProgram
     *
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\CardProgram")
     */
    private $cardProgram;

    /**
     * @var string
     *
     * @ORM\Column(name="name", type="string", length=1023, nullable=true)
     */
    private $name;

    /**
     * @var string
     *
     * @ORM\Column(name="passcode", type="string", length=255, nullable=true)
     */
    private $passcode;

    /**
     * @var string
     *
     * @ORM\Column(name="funding_type", type="string", length=255, nullable=true, options={"default": "ACH"})
     */
    private $fundingType;

    /**
     * @var string
     * @ORM\Column(name="meta", type="text", nullable=true)
     */
    private $meta;

    /**
     * @var Collection
     *
     * @ORM\ManyToMany(targetEntity="SalexUserBundle\Entity\User", mappedBy="userGroups")
     * @ORM\JoinTable(name="user_group_users",
     *      joinColumns={@ORM\JoinColumn(name="group_id", referencedColumnName="id", unique=false)},
     *      inverseJoinColumns={@ORM\JoinColumn(name="user_id", referencedColumnName="id", unique=false)}
     * )
     * @Serializer\Exclude()
     */
    private $users;

    /**
     * @var ArrayCollection
     * @ORM\OneToMany(targetEntity="SalexUserBundle\Entity\UserConfig", mappedBy="group")
     * @Serializer\Exclude()
     */
    private $adminConfigs;

     /**
     * @var string
     *
     * @ORM\Column(name="card_provider", type="string", length=255, nullable=true, options={"default": "Rapid"})
     */
    private $cardProvider;

    /**
     * @var integer
     *
     * @ORM\Column(name="botm_business_id", type="integer", nullable=true)
     */
    protected $botmBusinessId;

    /**
     * @var integer
     *
     * @ORM\Column(name="botm_business_account_id", type="integer", nullable=true, unique=true)
     */
    protected $botmBusinessAccountId;

     /**
     * @var integer
     *
     * @ORM\Column(name="tern_business_id", type="integer", nullable=true)
     */
    protected $ternBusinessId;

    /**
     * @var integer
     *
     * @ORM\Column(name="tern_business_account_id", type="integer", nullable=true, unique=true)
     */
    protected $ternBusinessAccountId;

    /**
     * Set name
     *
     * @param string $name
     *
     * @return UserGroup
     */
    public function setName($name)
    {
        $this->name = $name;

        return $this;
    }

    /**
     * Get name
     *
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * Set passcode
     *
     * @param string $passcode
     *
     * @return UserGroup
     */
    public function setPasscode($passcode)
    {
        $this->passcode = $passcode;

        return $this;
    }

    /**
     * Get passcode
     *
     * @return string
     */
    public function getPasscode()
    {
        return $this->passcode;
    }

    /**
     * Set meta
     *
     * @param string $meta
     *
     * @return UserGroup
     */
    public function setMeta($meta)
    {
        $this->meta = $meta;

        return $this;
    }

    /**
     * Get meta
     *
     * @return string
     */
    public function getMeta()
    {
        return $this->meta;
    }

    /**
     * Set platform
     *
     * @param Platform|null $platform
     *
     * @return UserGroup
     */
    public function setPlatform(\CoreBundle\Entity\Platform $platform = null)
    {
        $this->platform = $platform;

        return $this;
    }

    /**
     * Get platform
     *
     * @return \CoreBundle\Entity\Platform
     */
    public function getPlatform()
    {
        return $this->platform;
    }

    /**
     * Set cardProgram
     *
     * @param CardProgram|null $cardProgram
     *
     * @return UserGroup
     */
    public function setCardProgram(\CoreBundle\Entity\CardProgram $cardProgram = null)
    {
        $this->cardProgram = $cardProgram;

        return $this;
    }

    /**
     * Get cardProgram
     *
     * @return \CoreBundle\Entity\CardProgram
     */
    public function getCardProgram()
    {
        return $this->cardProgram;
    }

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->users = new ArrayCollection();
        $this->adminConfigs = new ArrayCollection();

        if (!$this->getPasscode()) {
            $this->setPasscode(strtoupper(Util::randString(6)));
        }
    }

    /**
     * Add user
     *
     * @param User $user
     *
     * @return UserGroup
     */
    public function addUser(User $user)
    {
        $this->users[] = $user;

        return $this;
    }

    /**
     * Remove user
     *
     * @param User $user
     */
    public function removeUser(User $user)
    {
        $this->users->removeElement($user);
    }

    /**
     * Get users
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getUsers()
    {
        return $this->users;
    }

    /**
     * Add adminConfig
     *
     * @param \SalexUserBundle\Entity\UserConfig $adminConfig
     *
     * @return UserGroup
     */
    public function addAdminConfig(\SalexUserBundle\Entity\UserConfig $adminConfig)
    {
        $this->adminConfigs[] = $adminConfig;

        return $this;
    }

    /**
     * Remove adminConfig
     *
     * @param \SalexUserBundle\Entity\UserConfig $adminConfig
     */
    public function removeAdminConfig(\SalexUserBundle\Entity\UserConfig $adminConfig)
    {
        $this->adminConfigs->removeElement($adminConfig);
    }

    /**
     * Get adminConfigs
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getAdminConfigs()
    {
        return $this->adminConfigs;
    }

    /**
     * Set fundingType
     *
     * @param string $fundingType
     *
     * @return UserGroup
     */
    public function setFundingType($fundingType)
    {
        $this->fundingType = $fundingType;

        return $this;
    }

    /**
     * Get fundingType
     *
     * @return string
     */
    public function getFundingType()
    {
        return $this->fundingType;
    }

    /**
     * Set cardProvider
     *
     * @param string $cardProvider
     *
     * @return UserGroup
     */
    public function setCardProvider($cardProvider)
    {
        $this->cardProvider = $cardProvider;

        return $this;
    }

    /**
     * Get cardProvider
     *
     * @return string
     */
    public function getCardProvider()
    {
        return $this->cardProvider;
    }

    public function getBotmBusinessId(): ?int
    {
        return $this->botmBusinessId;
    }

    public function setBotmBusinessId(?int $botmBusinessId): static
    {
        $this->botmBusinessId = $botmBusinessId;

        return $this;
    }

    public function getBotmBusinessAccountId(): ?int
    {
        return $this->botmBusinessAccountId;
    }

    public function setBotmBusinessAccountId(?int $botmBusinessAccountId): static
    {
        $this->botmBusinessAccountId = $botmBusinessAccountId;

        return $this;
    }

    public function getTernBusinessId(): ?int
    {
        return $this->ternBusinessId;
    }

    public function setTernBusinessId(?int $ternBusinessId): static
    {
        $this->ternBusinessId = $ternBusinessId;

        return $this;
    }

    public function getTernBusinessAccountId(): ?int
    {
        return $this->ternBusinessAccountId;
    }

    public function setTernBusinessAccountId(?int $ternBusinessAccountId): static
    {
        $this->ternBusinessAccountId = $ternBusinessAccountId;
        
        return $this;
    }
}
