<?php

namespace CoreBundle\Entity;

use CoreBundle\Utils\Util;
use Doctrine\ORM\Mapping as ORM;

/**
 * TenantFeeOption
 *
 * @ORM\Table(name="tenant_fee_option", options={"comment":"Fee options for each tenant. See fee_item for more details of same fields."})
 * @ORM\Entity(repositoryClass="CoreBundle\Repository\TenantFeeOptionRepository")
 */
class TenantFeeOption
{
    const FILLS = [
        'originName',
        'tranCode',
        'function',
        'transactionType',
        'transactionStatus',
    ];

    /**
     * @param Tenant $tenant
     * @param string $originName
     * @param string $tranCode
     * @param string $function
     * @param string $transactionType
     * @param string $transactionStatus
     *
     * @return TenantFeeOption|null
     */
    public static function save (Tenant $tenant, $originName = '', $tranCode = '',
                                 $function = '', $transactionType = '', $transactionStatus = '')
    {
        $args = func_get_args();
        $all = implode('', array_slice($args, 1));
        if (!$all) {
            return null;
        }
        $rs = Util::em()->getRepository(\CoreBundle\Entity\TenantFeeOption::class)
            ->createQueryBuilder('fo')
            ->where('fo.tenant = :tenant')
            ->andWhere('fo.originName = :originName')
            ->andWhere('fo.tranCode = :tranCode')
            ->andWhere('fo.function = :function')
            ->andWhere('fo.transactionType = :transactionType')
            ->andWhere('fo.transactionStatus = :transactionStatus')
            ->setParameter('tenant', $tenant->getId())
            ->setParameter('originName', $originName)
            ->setParameter('tranCode', $tranCode)
            ->setParameter('function', $function)
            ->setParameter('transactionType', $transactionType)
            ->setParameter('transactionStatus', $transactionStatus)
            ->getQuery()
            ->getResult()
        ;
        if ($rs) {
            return $rs[0];
        }
        $fo = new self();
        $fo->setTenant($tenant)
            ->setOriginName($originName)
            ->setTranCode($tranCode)
            ->setFunction($function)
            ->setTransactionType($transactionType)
            ->setTransactionStatus($transactionStatus)
        ;
        Util::persist($fo);

        return $fo;
    }

    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @var Tenant
     *
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\Tenant", inversedBy="feeOptions")
     */
    private $tenant;

    /**
     * @var string
     *
     * @ORM\Column(name="origin_name", type="string", length=255, nullable=true)
     */
    private $originName;

    /**
     * @var string
     *
     * @ORM\Column(name="tran_code", type="string", length=255, nullable=true)
     */
    private $tranCode;

    /**
     * @var string
     *
     * @ORM\Column(name="function", type="string", length=255, nullable=true)
     */
    private $function;

    /**
     * @var string
     *
     * @ORM\Column(name="transaction_type", type="string", length=255, nullable=true)
     */
    private $transactionType;

    /**
     * @var string
     *
     * @ORM\Column(name="transaction_status", type="string", length=255, nullable=true)
     */
    private $transactionStatus;


    /**
     * Get id
     *
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set originName
     *
     * @param string $originName
     *
     * @return TenantFeeOption
     */
    public function setOriginName($originName)
    {
        $this->originName = $originName;

        return $this;
    }

    /**
     * Get originName
     *
     * @return string
     */
    public function getOriginName()
    {
        return $this->originName;
    }

    /**
     * Set tranCode
     *
     * @param string $tranCode
     *
     * @return TenantFeeOption
     */
    public function setTranCode($tranCode)
    {
        $this->tranCode = $tranCode;

        return $this;
    }

    /**
     * Get tranCode
     *
     * @return string
     */
    public function getTranCode()
    {
        return $this->tranCode;
    }

    /**
     * Set transactionType
     *
     * @param string $transactionType
     *
     * @return TenantFeeOption
     */
    public function setTransactionType($transactionType)
    {
        $this->transactionType = $transactionType;

        return $this;
    }

    /**
     * Get transactionType
     *
     * @return string
     */
    public function getTransactionType()
    {
        return $this->transactionType;
    }

    /**
     * Set function
     *
     * @param string $function
     *
     * @return TenantFeeOption
     */
    public function setFunction($function)
    {
        $this->function = $function;

        return $this;
    }

    /**
     * Get function
     *
     * @return string
     */
    public function getFunction()
    {
        return $this->function;
    }

    /**
     * Set tenant
     *
     * @param \CoreBundle\Entity\Tenant $tenant
     *
     * @return TenantFeeOption
     */
    public function setTenant(\CoreBundle\Entity\Tenant $tenant = null)
    {
        $this->tenant = $tenant;

        return $this;
    }

    /**
     * Get tenant
     *
     * @return \CoreBundle\Entity\Tenant
     */
    public function getTenant()
    {
        return $this->tenant;
    }

    /**
     * Set transactionStatus
     *
     * @param string $transactionStatus
     *
     * @return TenantFeeOption
     */
    public function setTransactionStatus($transactionStatus)
    {
        $this->transactionStatus = $transactionStatus;

        return $this;
    }

    /**
     * Get transactionStatus
     *
     * @return string
     */
    public function getTransactionStatus()
    {
        return $this->transactionStatus;
    }
}
