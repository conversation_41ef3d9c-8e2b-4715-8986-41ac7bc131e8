<?php

namespace CoreBundle\Entity;

use CoreBundle\Services\EmailService;
use CoreBundle\Utils\Bundle;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Security;
use CoreBundle\Utils\Traits\EntityTrait;
use CoreBundle\Utils\Util;
use Doctrine\ORM\Mapping as ORM;

/**
 * Config
 *
 * @ORM\Table(name="config", options={"comment":"Global configuration, like IP whitelist, user status list, banned reason list, admin emails, languages, welcome message, etc."})
 * @ORM\Entity(repositoryClass="CoreBundle\Repository\ConfigRepository")
 */
class Config
{
    use EntityTrait;

    public const CONFIG_IP_WHITELIST = 'ip_whitelist';
    public const CONFIG_USER_STATUS = 'user_status';
    public const CONFIG_USER_BANNED_REASON = 'user_banned_reason';
    public const CONFIG_USER_CLOSURE_REASON = 'user_closure_reason';
    public const CONFIG_DEFAULT_ID_VERIFY_ATTEMPT_TIMES = 'default_id_verify_attempt_times';
    public const CONFIG_PAGE_CONTENT_DASHBOARD = 'page_content_dashboard';
    public const CONFIG_YOP_MAIL = 'yop_mail';
    public const CONFIG_ADMINS_MAIL = 'admins_mail';
    public const CONFIG_TESTERS_MAIL = 'testers_mail';
    public const CONFIG_WATCH_PAYMENTS = 'watch_payments';
    public const CONFIG_VELOCITY_SWITCH = 'velocity_switch';
    public const CONFIG_INACTIVE_EMAILS = 'inactive_emails';
    public const CONFIG_INACTIVE_EMAIL_PATTERNS = 'inactive_email_patterns';
    public const CONFIG_WILD_PHONE_NUMBERS = 'wild_phones';
    public const CONFIG_LANGUAGES = 'languages';
    public const CONFIG_WELCOME_MESSAGE = 'welcome';
    public const CONFIG_GLOBAL_CHART_SETTINGS = 'chart_settings';
    public const CONFIG_MEX_RAPYD_BALANCE_THRESHOLD = 'transfermex_rapyd_balance_threshold';
    public const CONFIG_MEX_RAPYD_MIN_LOAD_AMOUNT = 'transfermex_rapyd_min_load_amount';
    public const CONFIG_MEX_RAPID_MIN_BASE_BALANCE = 'transfermex_rapid_min_base_balance';
    public const CONFIG_MEX_UNITELLER_BALANCE_THRESHOLD = 'transfermex_uniteller_balance_threshold';
    public const CONFIG_MEX_UNITELLER_MIN_LOAD_AMOUNT = 'transfermex_uniteller_min_load_amount';
    public const CONFIG_MEX_WEBVIEW_URLS = 'transfermex_webview_urls';
    public const CONFIG_PLAID_SPENDR_IP_WHITELIST = 'plaid_spendr_ip_whitelist';
    public const CONFIG_SPENDR_FUZZY_MATCH = 'spendr_fuzzy_match';
    public const CONFIG_SPENDR_MANUAL_CARD_INSTANT_AMOUNT = 'spendr_manual_card_instant_amount';
    public const CONFIG_SPENDR_AUTH_CARD_INSTANT_AMOUNT = 'spendr_card_instant_amount';
    public const CONFIG_SPENDR_USERNAME_BLACKLIST = 'spendr_username_blacklist';
    public const CONFIG_SPENDR_DEVICE_ID_WHITELIST = 'spendr_device_id_whitelist';
    public const CONFIG_MEX_INTERMEX_BALANCE_THRESHOLD = 'transfermex_intermex_balance_threshold';
    public const CONFIG_MEX_INTERMEX_MIN_LOAD_AMOUNT = 'transfermex_intermex_min_load_amount';

    public const ARRAY_CONFIGS = [
        self::CONFIG_IP_WHITELIST,
        self::CONFIG_USER_STATUS,
        self::CONFIG_USER_BANNED_REASON,
        self::CONFIG_USER_CLOSURE_REASON,
        self::CONFIG_ADMINS_MAIL,
        self::CONFIG_TESTERS_MAIL,
        self::CONFIG_INACTIVE_EMAIL_PATTERNS,
        self::CONFIG_INACTIVE_EMAILS,
        self::CONFIG_WILD_PHONE_NUMBERS,
    ];

    public static function isNewUsuEnabled() {
        return true;

//        if (static::isIPWhitelisted() || Util::isDev()) {
//            return true;
//        }
//        $user = Util::user();
//        if ($user) {
//            $email = $user->getEmail();
//            if (Util::startsWith($email, 'usu_') && Util::endsWith($email, '@yopmail.com')) {
//                return true;
//            }
//        }
////        if (Bundle::isUsUnlocked()) {
////            return true;
////        }
//        return false;
    }

    public static function isIPWhitelisted () {
        $clientIps = Security::getClientIps();
        $all = self::array(self::CONFIG_IP_WHITELIST);

        $in = false;
        foreach ($clientIps as $ip) {
            if (in_array($ip, $all, true)) {
                $in = true;
                break;
            }
        }

        return $in;
    }

    public static function get($key, $default = null) {
        return Util::em()->getRepository(self::class)->getValue($key, $default);
    }

    public static function getDecoded($key, $default = null) {
        return self::array($key, $default);
    }

    public static function isEmailValid($email) {
        return $email && !self::isEmailInactive($email);
    }

    public static function isEmailInactive($email) {
        $email = trim($email);
        if (!$email) {
            return false;
        }
        $email = strtolower($email);
        $patterns = self::array(self::CONFIG_INACTIVE_EMAIL_PATTERNS, [
            '.*@mozej\.com',
            '.*@loaoa\.com',
            '.*@one2mail\.info',
            '.*@carbtc\.net',
            '.*@0hboy\.com',
            '.*@disbox\.org',
            '.*@disbox\.net',
            '.*@tmpmail\.org',
            '.*@tmpmail\.net',
            '.*@mailinator\.com',
            '.*@spam4\.me',
            '.*@allfamus\.com',
            '.*@laoho\.com',
            '.*@null\.com',
            '.*@null\.[a-zA-Z0-9]+',
            '.*@void\.com',
            '.*@test\.com',
            '.*@example\.com',
        ]);
        foreach ($patterns as $pattern) {
            if (preg_match('|' . $pattern . '|', $email)) {
                return true;
            }
        }
        return InactiveEmail::has($email) !== null;
    }

    public static function matchInactiveEmailPattern ($email) {
      $email = strtolower($email);
      if (preg_match('/@((mozej|loaoa|0hboy|mailinator|allfamus|laoho|void|test|example)\.com)|((carbtc|disbox|tmpmail)\.net)|((disbox|tmpmail)\.org)|(one2mail\.info)|(spam4\.me)|(null\.[a-zA-Z0-9]+)/', $email)) {
        return true;
      }

      $patterns = self::array(self::CONFIG_INACTIVE_EMAIL_PATTERNS, []);
      foreach ($patterns as $pattern) {
        if (preg_match('|' . $pattern . '|', $email)) {
            return true;
        }
      }
      return false;
    }

    public static function addInactiveEmail($email) {
        $email = trim($email);
        $items = explode(', ', $email);
        foreach ($items as $item) {
            if (!$item) {
                continue;
            }

            if (self::matchInactiveEmailPattern($email)) {
                continue;
            }

            if (InactiveEmail::has($email)) {
                continue;
            }

            InactiveEmail::create($email);
            Log::info('Added inactive email: ' . $item);
        }
    }

    public static function removeInactiveEmail($email) {
        $email = trim($email);
        if (!$email) {
            return;
        }
        InactiveEmail::remove($email);
    }

    public static function activateInactiveEmail($email) {
        if (!$email) {
            return;
        }
        self::removeInactiveEmail($email);

        try {
            $bounce = EmailService::getInactiveBounceWithRecipient($email);
            if ($bounce) {
                EmailService::activateEmailWithBounce($bounce['ID']);
            }
        } catch (\Throwable $e) {
            Log::error('Failed to activate inactive email in Postmark: ' . $email . ' - ' . $e->getMessage());
            return;
        }
    }

    public static function activeLanguages()
    {
        $all = self::array(self::CONFIG_LANGUAGES);
        return array_filter($all, function ($item) {
            return !isset($item['disabled']) || !$item['disabled'];
        });
    }

    /**
     * @param $key
     * @param array|boolean $default
     * @return array|mixed
     */
    public static function array($key, $default = []) {
        return Util::em()->getRepository(__CLASS__)->getArray($key, $default);
    }

    /**
     * @deprecated Use setJson or setRaw to specify value type
     */
    public static function set($key, $value) {
        Util::em()->getRepository(__CLASS__)->setValue($key, $value);
        return null;
    }

    public static function setJson($key, $value) {
        Util::em()->getRepository(__CLASS__)->setValue($key, $value);
        return null;
    }

    public static function setRaw($key, $value) {
        Util::em()->getRepository(__CLASS__)->setValue($key, $value, false);
        return null;
    }

    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @var string
     *
     * @ORM\Column(name="name", type="string", length=255, unique=true)
     */
    private $name;

    /**
     * @var string
     *
     * @ORM\Column(name="value", type="text", nullable=true)
     */
    private $value;


    /**
     * Get id
     *
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set name
     *
     * @param string $name
     *
     * @return Config
     */
    public function setName($name)
    {
        $this->name = $name;

        return $this;
    }

    /**
     * Get name
     *
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * Set value
     *
     * @param string $value
     *
     * @return Config
     */
    public function setValue($value)
    {
        $this->value = $value;

        return $this;
    }

    /**
     * Get value
     *
     * @return string
     */
    public function getValue()
    {
        return $this->value;
    }
}

