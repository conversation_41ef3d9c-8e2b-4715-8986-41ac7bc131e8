<?php


namespace CoreBundle\Entity;

use Api<PERSON><PERSON>le\Entity\ApiEntityInterface;
use CoreBundle\Utils\Bundle;
use CoreBundle\Utils\Util;
use Doctrine\ORM\Mapping as ORM;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;

/**
 * BaseState
 *
 * @ORM\Table(name="base_state", options={"comment":"Base state for CoreBundle:State & EsSoloBundle:State."})
 * @ORM\Entity(repositoryClass="CoreBundle\Repository\BaseStateRepository")
 * @ORM\InheritanceType("JOINED")
 * @ORM\DiscriminatorColumn(name="discriminator", type="string")
 * @ORM\DiscriminatorMap({"basestate": "CoreBundle\Entity\BaseState", "state": "CoreBundle\Entity\State", "essolo_state": "EsSoloBundle\Entity\State"})
 */
class BaseState implements ApiEntityInterface
{
    public function toApiArray(bool $extra = FALSE): array
    {
        $cityList = City::findByState($this);
        return [
            'id' => $this->getId(),
            'name' => $this->getName(),
            'abbr' => $this->getAbbr(),
            'abbr3' => $this->getAbbr3(),
            'is_uniteller_support' => $this->getIsUniTellerSupport(),
            'is_intermex_support'  => $this->getIntermexStateId() ? true : false,
            'city' => $cityList,
            'freeCity' => empty($cityList)
        ];
    }

    public static function getAbbrInCountry($name, $country = 'US') {
        if (is_string($country)) {
            $country = Country::findByCode($country);
        }
        $states = $country->getSpecificStates();
        /** @var BaseState $state */
        foreach ($states as $state) {
            $n = $state->getName();
            if ($n === $name) {
                return $state->getAbbrOrName();
            }
        }
        return $name;
    }

    /**
     * @param $id
     *
     * @return static
     */
    public static function find($id) {
        if (!$id) {
            return null;
        }
        return Util::em()->getRepository(static::class)
            ->find($id);
    }

     /**
     * @param        $name
     * @param Country $country
     *
     * @return State|null
     */
    public static function findByName($name, Country $country)
    {
        if (is_numeric($name)) {
            return static::find((int)$name);
        }

        $expr = Util::expr();
        $rs = Util::em()->getRepository(static::class)
            ->createQueryBuilder('s')
            ->where('s.country = :country')
            ->andWhere($expr->eq('s.name', ':name'))
            ->andWhere($expr->isNotNull('s.intermexStateId'))
            ->setParameter('country', $country)
            ->setParameter('name', ucwords($name))
            ->getQuery()
            ->getResult();
        if ($rs) {
            return $rs[0];
        }
        return null;
    }

    /**
     * @param        $name
     * @param string $country
     *
     * @return State|null
     */
    public static function findByAbbrOrName($name, $country = 'US')
    {
        if (is_numeric($name)) {
            return static::find((int)$name);
        }

        if (is_string($country)) {
            $country = Country::findByCode($country);
        }

        $expr = Util::expr();
        $rs = Util::em()->getRepository(static::class)
            ->createQueryBuilder('s')
            ->where('s.country = :country')
            ->andWhere($expr->orX(
                $expr->eq('s.abbr', ':abbr'),
                $expr->eq('s.name', ':name')
            ))
            ->setParameter('country', $country)
            ->setParameter('abbr', $name)
            ->setParameter('name', $name)
            ->getQuery()
            ->getResult();
        if ($rs) {
            return $rs[0];
        }
        return null;
    }

    public function getAbbrOrName() {
        return $this->getAbbr() ?: $this->getAbbr3() ?: $this->getName();
    }

    public function getAbbr3OrName() {
        return $this->getAbbr3() ?: $this->getAbbr() ?: $this->getName();
    }

    public function getStateName() {
        return $this->getName();
    }

    public function setStateName($name) {
        return $this->setName($name);
    }

    public static function getSpecificClass() {
        if (Bundle::isEsSolo()) {
            return \EsSoloBundle\Entity\State::class;
        }
        return State::class;
    }

    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @var string
     *
     * @ORM\Column(name="name", type="string", length=255)
     */
    private $name;

    /**
     * @var string
     *
     * @ORM\Column(name="abbr", type="string", length=255, nullable=true)
     */
    private $abbr;

    /**
     * @var string ISO 3166 states abbr
     *
     * @ORM\Column(name="abbr3", type="string", length=255, nullable=true)
     */
    private $abbr3;

    /**
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\Country")
     * @Serializer\Exclude()
     */
    private $country;

     /**
     * @var boolean $isUniTellerSupport
     *
     * @ORM\Column(name="is_uniTeller_support", type="boolean", nullable=true, options={"comment":"If Yes UniTeller support this state"})
     */
    private $isUniTellerSupport;

    /**
     * @var string
     *
     * @ORM\Column(name="intermex_state_id", type="string", length=255, nullable=true)
     */
    private $intermexStateId;

    /**
     * Get id
     *
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set name
     *
     * @param string $name
     *
     * @return BaseState
     */
    public function setName($name)
    {
        $this->name = $name;

        return $this;
    }

    /**
     * Get name
     *
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * Set abbr
     *
     * @param string $abbr
     *
     * @return BaseState
     */
    public function setAbbr($abbr)
    {
        $this->abbr = $abbr;

        return $this;
    }

    /**
     * Get abbr
     *
     * @return string
     */
    public function getAbbr()
    {
        return $this->abbr;
    }

    /**
     * Set country
     *
     * @param \CoreBundle\Entity\Country $country
     *
     * @return BaseState
     */
    public function setCountry(\CoreBundle\Entity\Country $country = null)
    {
        $this->country = $country;

        return $this;
    }

    /**
     * Get country
     *
     * @return \CoreBundle\Entity\Country
     */
    public function getCountry()
    {
        return $this->country;
    }

    /**
     * Set abbr3
     *
     * @param string $abbr3
     *
     * @return BaseState
     */
    public function setAbbr3($abbr3)
    {
        $this->abbr3 = $abbr3;

        return $this;
    }

    /**
     * Get abbr3
     *
     * @return string
     */
    public function getAbbr3()
    {
        return $this->abbr3;
    }

    /**
     * Set isUniTellerSupport
     *
     * @param boolean $isUniTellerSupport
     *
     * @return BaseState
     */
    public function setIsUniTellerSupport($isUniTellerSupport)
    {
        $this->isUniTellerSupport = $isUniTellerSupport;

        return $this;
    }

    /**
     * Get isUniTellerSupport
     *
     * @return boolean
     */
    public function getIsUniTellerSupport()
    {
        return $this->isUniTellerSupport;
    }

    public function getIntermexStateId(): ?string
    {
        return (int)$this->intermexStateId;
    }

    public function setIntermexStateId(?string $intermexStateId): static
    {
        $this->intermexStateId = $intermexStateId;

        return $this;
    }
}
