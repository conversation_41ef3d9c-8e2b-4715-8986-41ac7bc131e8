<?php

namespace CoreBundle\Entity;

use CoreBundle\Utils\Util;
use Doctrine\ORM\Mapping as ORM;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;
use Symfony\Component\Validator\Constraints as Assert;

/**
 * LoadPartner
 *
 * @ORM\Table(name="load_partner")
 * @ORM\Entity
 */
class LoadPartner extends Tenant
{
    public const SYSTEM = 'System';
    public const GLOBAL_COLLECT = 'Global Collect';
    public const ALTERNATIVE_PAYMENT = 'Alternative Payments';
    public const CITCON = 'Citcon';
    public const TWO_THOUSAND_CHARGE = '2000 Charge';
    public const COINFLOW = 'Coinflow';

    public function is($name) {
        $n = str_replace([' ', '_', '-'], '', strtolower($this->getName()));
        if (self::GLOBAL_COLLECT === $name) {
            if ('globalcollect' === $n) {
                return true;
            }
        } else if (self::ALTERNATIVE_PAYMENT === $name) {
            if (in_array($n, [
                'alternativepayment',
                'alternativepayments',
            ], true)) {
                return true;
            }
        } else if (self::TWO_THOUSAND_CHARGE === $name) {
            if (in_array($n, [
                '2000 charge',
                '2000charge',
            ], true)) {
                return true;
            }
        } else if (self::COINFLOW === $name) {
            if (in_array($n, [
                'coinflow',
                'coin flow',
            ], true)) {
                return true;
            }
        } else if (self::CITCON === $name) {
            if ('citcon' === $n) {
                return true;
            }
        } else if (self::SYSTEM === $name) {
            if ('system' === $n) {
                return true;
            }
        }
        return false;
    }

    public static function get($name)
    {
        $all = Util::em()->getRepository(\CoreBundle\Entity\LoadPartner::class)->findAll();
        /** @var LoadPartner $partner */
        foreach ($all as $partner) {
            if ($partner->is($name)) {
                return $partner;
            }
        }
        return null;
    }

    public static function find($name)
    {
        return self::get($name);
    }

    public static function system()
    {
        return self::get(self::SYSTEM);
    }

    public static function coinflow()
    {
        return self::findByName(self::COINFLOW);
    }

    /**
     * @var int
     *
     * @ORM\Column(name="tenant_provider_id", type="string", length=255, nullable=true)
     * @Assert\NotBlank()
     */
    private $tenantProviderId;

    /**
     * Map to many load methods (unidirectional)
     * @ORM\ManyToMany(targetEntity="CoreBundle\Entity\LoadMethod")
     * @ORM\JoinTable(
     *     name="load_partner_method",
     *     joinColumns={@ORM\JoinColumn(name="load_partner_id", referencedColumnName="id")},
     *     inverseJoinColumns={@ORM\JoinColumn(name="load_method_id", referencedColumnName="id")}
     *     )
     * @Serializer\Exclude()
     */
    private $loadMethods;

    //Begin Add by Bob Wen Bao on 2017-04-11 to deal with Brand Partner OR Marketing Partner could be KYC Provider OR Load Partner as well
    /**
     * @var boolean $isBrandPartner
     * @ORM\Column(name="is_brand_partner", type="boolean", nullable=true)
     */
    private $isBrandPartner = false;

    /**
     * @var integer $brandPartnerId
     * @ORM\Column(name="brand_partner_id", type="integer", nullable=true)
     */
    private $brandPartnerId;

    /**
     * @var boolean $isMarketingPartner
     * @ORM\Column(name="is_Marketing_partner", type="boolean", nullable=true)
     */
    private $isMarketingPartner = false;

    /**
     * @var integer $marketingPartnerId
     * @ORM\Column(name="marketing_partner_id", type="integer", nullable=true)
     */
    private $marketingPartnerId;
    //End


    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->loadMethods = new \Doctrine\Common\Collections\ArrayCollection();
    }

    /**
     * Set tenantProviderId
     *
     * @param string $tenantProviderId
     *
     * @return LoadPartner
     */
    public function setTenantProviderId($tenantProviderId)
    {
        $this->tenantProviderId = $tenantProviderId;

        return $this;
    }

    /**
     * Get tenantProviderId
     *
     * @return string
     */
    public function getTenantProviderId()
    {
        return $this->tenantProviderId;
    }

    /**
     * Add loadMethod
     *
     * @param \CoreBundle\Entity\LoadMethod $loadMethod
     *
     * @return LoadPartner
     */
    public function addLoadMethod(\CoreBundle\Entity\LoadMethod $loadMethod)
    {
        $this->loadMethods[] = $loadMethod;

        return $this;
    }

    /**
     * Remove loadMethod
     *
     * @param \CoreBundle\Entity\LoadMethod $loadMethod
     */
    public function removeLoadMethod(\CoreBundle\Entity\LoadMethod $loadMethod)
    {
        $this->loadMethods->removeElement($loadMethod);
    }

    /**
     * Get loadMethods
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getLoadMethods()
    {
        return $this->loadMethods;
    }

    /**
     * Set isBrandPartner
     *
     * @param boolean $isBrandPartner
     *
     * @return LoadPartner
     */
    public function setIsBrandPartner($isBrandPartner)
    {
        $this->isBrandPartner = $isBrandPartner;

        return $this;
    }

    /**
     * Get isBrandPartner
     *
     * @return boolean
     */
    public function getIsBrandPartner()
    {
        return $this->isBrandPartner;
    }

    /**
     * Set brandPartnerId
     *
     * @param integer $brandPartnerId
     *
     * @return LoadPartner
     */
    public function setBrandPartnerId($brandPartnerId)
    {
        $this->brandPartnerId = $brandPartnerId;

        return $this;
    }

    /**
     * Get brandPartnerId
     *
     * @return integer
     */
    public function getBrandPartnerId()
    {
        return $this->brandPartnerId;
    }

    /**
     * Set isMarketingPartner
     *
     * @param boolean $isMarketingPartner
     *
     * @return LoadPartner
     */
    public function setIsMarketingPartner($isMarketingPartner)
    {
        $this->isMarketingPartner = $isMarketingPartner;

        return $this;
    }

    /**
     * Get isMarketingPartner
     *
     * @return boolean
     */
    public function getIsMarketingPartner()
    {
        return $this->isMarketingPartner;
    }

    /**
     * Set marketingPartnerId
     *
     * @param integer $marketingPartnerId
     *
     * @return LoadPartner
     */
    public function setMarketingPartnerId($marketingPartnerId)
    {
        $this->marketingPartnerId = $marketingPartnerId;

        return $this;
    }

    /**
     * Get marketingPartnerId
     *
     * @return integer
     */
    public function getMarketingPartnerId()
    {
        return $this->marketingPartnerId;
    }
}
