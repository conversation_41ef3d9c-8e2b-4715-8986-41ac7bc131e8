<?php

namespace CoreBundle\Entity;

use CoreBundle\Utils\Money;
use Doctrine\ORM\Mapping as ORM;

/**
 * Transaction
 *
 * @ORM\Table(name="transaction", options={"comment":"Transaction details for primary the card loadings. Not important, just some duplicates. See user_card_loads for more details."})
 * @ORM\Entity(repositoryClass="CoreBundle\Repository\TransactionRepository")
 */
class Transaction extends BaseEntity
{
    const TYPE_LOAD_CARD = 'load_card';

    const STATUS_FAILED = 'failed';
    const STATUS_SUCCESS = 'success';
    const DEPOSIT = '611';
    const DEPOSIT_REVERSAL = '612';
    const RESERVE_DEPOSIT = '613';
    const RESERVE_DEPOSIT_REVERSAL = '614';
    const MONTHLY_FEE = '10';
    const MONTHLY_FEE_REVERSAL = '11';
    const ACH_DEPOSIT = '37';
    const POS_PURCHASE = '40';
    const PURCHASE_RETURN = '41';
    const FLAGED_FOR_CHARGEOFF = '51';
    const CARD_TRANSFER_DEBIT = '148';
    const CARD_TRANSFER_CREDIT = '149';
    const DECLINED_TRANSACTION_FEE = '214';
    const DECLINED_TRANSACTION_FEE_REFUND = '215';
    const POS_PURCHASE_FEE = '220';
    const POS_PURCHASE_INTERNATIONAL = '404';
    const RESERVE_FUNDING_DEPOSIT = '461';
    const RESERVE_FUNDING_DEPOSIT_REVERSAL = '462';
    const ACH_DEBIT = '5032';
    const POS_PURCHASE_FEE_ZERO = '220';

    public function formatMoney() {
        return Money::format($this->getAmount(), $this->getCurrency());
    }

    /**
     * @param $type
     * @param $request
     * @param $amount
     * @param $currency
     * @return Transaction
     */
    public static function create($type, $request, $amount, $currency) {
        $entity = new static(); // @phpstan-ignore-line
        $entity->setType($type);
        $entity->setRequest(json_encode($request));
        $entity->setAmount($amount);
        $entity->setCurrency($currency);

        return $entity->persist();
    }

    public function getPaymentId($reference = true) {
        $partner = $this->getPartner();
        if ($partner->is(LoadPartner::GLOBAL_COLLECT)) {
            $resp = $this->getResponse();
            $resp = json_decode($resp, true);
            if ($reference) {
                return $resp['payment']['paymentOutput']['references']['paymentReference'] ?? '';
            }
            return $resp['payment']['id'] ?? '';
        }
        return $this->getRequestKey();
    }

    /**
     * @var string
     *
     * @ORM\Column(name="type", type="string", length=255)
     */
    private $type;

    /**
     * @var LoadPartner
     *
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\LoadPartner")
     */
    private $partner;

    /**
     * @var LoadMethod
     *
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\LoadMethod")
     */
    private $method;

    /**
     * @var string
     *
     * @ORM\Column(name="request", type="text")
     */
    private $request;

    /**
     * @var string
     *
     * @ORM\Column(name="requestKey", type="string", length=255, nullable=true)
     */
    private $requestKey;

    /**
     * @var string
     *
     * @ORM\Column(name="response", type="text", nullable=true)
     */
    private $response;

    /**
     * @ORM\ManyToOne(targetEntity="SalexUserBundle\Entity\User")
     * @ORM\JoinColumn(name="user_id", referencedColumnName="id", onDelete="SET NULL")
     */
    private $requestBy;

    /**
     * @var string
     *
     * @ORM\Column(name="product_entity", type="string", length=255, nullable=true)
     */
    private $productEntity;

    /**
     * @var integer
     *
     * @ORM\Column(name="product_key", type="integer", nullable=true)
     */
    private $productKey;

    /**
     * @var integer
     *
     * @ORM\Column(name="product_count", type="integer", nullable=true)
     */
    private $productCount;

    /**
     * @var int
     *
     * @ORM\Column(name="amount", type="bigint")
     */
    private $amount;

    /**
     * @var string
     *
     * @ORM\Column(name="currency", type="string", length=255)
     */
    private $currency;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=255, nullable=true)
     */
    private $status;

    /**
     * @var string
     * @ORM\Column(name="error", type="text", nullable=true)
     */
    private $error;

    /**
     * Set type
     *
     * @param string $type
     *
     * @return Transaction
     */
    public function setType($type)
    {
        $this->type = $type;

        return $this;
    }

    /**
     * Get type
     *
     * @return string
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * Set partner
     *
     * @param LoadPartner $partner
     *
     * @return Transaction
     */
    public function setPartner($partner)
    {
        $this->partner = $partner;

        return $this;
    }

    /**
     * Get partner
     *
     * @return LoadPartner
     */
    public function getPartner()
    {
        return $this->partner;
    }

    /**
     * Set method
     *
     * @param LoadMethod $method
     *
     * @return Transaction
     */
    public function setMethod($method)
    {
        $this->method = $method;

        return $this;
    }

    /**
     * Get method
     *
     * @return LoadMethod
     */
    public function getMethod()
    {
        return $this->method;
    }

    /**
     * Set request
     *
     * @param string $request
     *
     * @return Transaction
     */
    public function setRequest($request)
    {
        $this->request = $request;

        return $this;
    }

    /**
     * Get request
     *
     * @return string
     */
    public function getRequest()
    {
        return $this->request;
    }

    /**
     * Set requestKey
     *
     * @param string $requestKey
     *
     * @return Transaction
     */
    public function setRequestKey($requestKey)
    {
        $this->requestKey = $requestKey;

        return $this;
    }

    /**
     * Get requestKey
     *
     * @return string
     */
    public function getRequestKey()
    {
        return $this->requestKey;
    }

    /**
     * Set response
     *
     * @param string $response
     *
     * @return Transaction
     */
    public function setResponse($response)
    {
        $this->response = is_string($response) ? $response : json_encode($response);

        return $this;
    }

    /**
     * Get response
     *
     * @return string
     */
    public function getResponse()
    {
        return $this->response;
    }

    /**
     * Set requestBy
     *
     * @param string $requestBy
     *
     * @return Transaction
     */
    public function setRequestBy($requestBy)
    {
        $this->requestBy = $requestBy;

        return $this;
    }

    /**
     * Get requestBy
     *
     * @return string
     */
    public function getRequestBy()
    {
        return $this->requestBy;
    }

    /**
     * Set amount
     *
     * @param integer $amount
     *
     * @return Transaction
     */
    public function setAmount($amount)
    {
        $this->amount = $amount;

        return $this;
    }

    /**
     * Get amount
     *
     * @return int
     */
    public function getAmount()
    {
        return $this->amount;
    }

    /**
     * Set currency
     *
     * @param string $currency
     *
     * @return Transaction
     */
    public function setCurrency($currency)
    {
        $this->currency = $currency;

        return $this;
    }

    /**
     * Get currency
     *
     * @return string
     */
    public function getCurrency()
    {
        return $this->currency;
    }

    /**
     * Set status
     *
     * @param string $status
     *
     * @return Transaction
     */
    public function setStatus($status)
    {
        $this->status = $status;

        return $this;
    }

    /**
     * Get status
     *
     * @return string
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * Set productEntity
     *
     * @param string $productEntity
     *
     * @return Transaction
     */
    public function setProductEntity($productEntity)
    {
        $this->productEntity = $productEntity;

        return $this;
    }

    /**
     * Get productEntity
     *
     * @return string
     */
    public function getProductEntity()
    {
        return $this->productEntity;
    }

    /**
     * Set productKey
     *
     * @param integer $productKey
     *
     * @return Transaction
     */
    public function setProductKey($productKey)
    {
        $this->productKey = $productKey;

        return $this;
    }

    /**
     * Get productKey
     *
     * @return integer
     */
    public function getProductKey()
    {
        return $this->productKey;
    }

    /**
     * Set productCount
     *
     * @param integer $productCount
     *
     * @return Transaction
     */
    public function setProductCount($productCount)
    {
        $this->productCount = $productCount;

        return $this;
    }

    /**
     * Get productCount
     *
     * @return integer
     */
    public function getProductCount()
    {
        return $this->productCount;
    }

    /**
     * Set error
     *
     * @param string $error
     *
     * @return Transaction
     */
    public function setError($error)
    {
        $this->error = $error;

        return $this;
    }

    /**
     * Get error
     *
     * @return string
     */
    public function getError()
    {
        return $this->error;
    }
}
