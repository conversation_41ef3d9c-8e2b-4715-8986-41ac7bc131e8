<?php
/**
 * User: Bob
 * Date: 2017/4/11
 * Time: 12:34
 * This is used to handle the platform fee items
 */

namespace CoreBundle\Entity;

use CashOnWebBundle\Services\PartnerService;
use CoreBundle\Utils\Bundle;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Util;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\Mapping as ORM;

/**
 * Class Platform
 *
 * @ORM\Entity
 * @ORM\Table(name="platform", indexes={
 *      @ORM\Index(columns={"botm_business_account_id"}),
 *  })
 * @package CoreBundle\Entity
 */
class Platform extends Tenant
{
    const NAME_TERN_COMMERCE = 'Tern Commerce';
    const NAME_UTC = 'Universal Transaction Compliance';
    const NAME_ES_SOLO = 'ES SOLO';
    const NAME_FIS = 'FIS';
    const NAME_LEAFLINK = 'LeafLink';
    const NAME_TRANSFER_MEX = 'TransferMex';
    const NAME_CASH_ON_WEB = 'Cash On Web';
    const NAME_PTO_GENIUS = 'PTO Genius';
    const NAME_WILEN = 'Wilen';
    const NAME_FAAS = 'Fintech as a Service';
    const NAME_RED_CARD_ATHLETICS = 'Red Card Athletics';
    const NAME_AMERICA_VOICE = 'America Voice';
    const NAME_IQSTEL = 'iQSTEL';
    const NAME_D2 = 'D2Rx';
    const NAME_FLUTTERWAVE = 'Flutterwave';
	const NAME_SPENDR = 'Spendr';
	const NAME_SKUX = 'SKUx';

    const DEFAULT_LOGO       = '/bundles/app/images/logo.png';
    const DEFAULT_ICON       = '/static/img/tern_logo_new_blue_square.png';
    const DEFAULT_ICON_TOUCH = '/static/img/tern_logo_white_new_blue.png';

    const CUSTOMIZES = [
        'font' => 'Lato',
        'linkColor' => '#428bca',
        'linkActiveColor' => '#e12e2f',
        'buttonColor' => '#428bca',
        'buttonActiveColor' => '#e12e2f',
        'adminPrimaryColor' => '#308fd0',
        'adminPrimaryPositiveColor' => '#35cd68',
        'adminPrimaryNegativeColor' => '#ff6501',
        'adminSecondaryColor' => '#116CAB',
        'adminSecondaryPositiveColor' => '#ACEAC1',
        'adminSecondaryNegativeColor' => '#FCB62B',
    ];

    const PARTNER = [
      'Cash On Web',
    ];

    /**
     * @param $name
     *
     * @return Platform|null
     */
    public static function get($name) {
        return self::cached($name);
    }

    public static function ternCommerce() {
        return static::cached(static::NAME_TERN_COMMERCE);
    }

    public static function esSolo() {
        return static::cached(static::NAME_ES_SOLO);
    }

    public static function utc() {
        return static::cached(static::NAME_UTC);
    }

    public static function fis() {
        return static::cached(static::NAME_FIS);
    }

    public static function leafLink() {
        return static::cached(static::NAME_LEAFLINK);
    }

    public static function transferMex() {
        return static::cached(static::NAME_TRANSFER_MEX);
    }

    public static function cashOnWeb() {
      return static::cached(static::NAME_CASH_ON_WEB);
    }

    public static function ptoGenius() {
        return static::cached(static::NAME_PTO_GENIUS);
    }

    public static function wilen() {
        return static::cached(static::NAME_WILEN);
    }

	public static function faas() {
		return static::cached(static::NAME_FAAS);
	}

	public static function redCard() {
		return static::cached(static::NAME_RED_CARD_ATHLETICS);
	}

	public static function d2() {
		return static::cached(static::NAME_D2);
	}

	public static function americaVoice() {
		return static::cached(static::NAME_AMERICA_VOICE);
	}

	public static function IQStel() {
		return static::cached(static::NAME_IQSTEL);
	}

    public static function spendr() {
        return static::cached(static::NAME_SPENDR);
    }

    public static function skux() {
        return static::cached(static::NAME_SKUX);
    }

    public static function isCorsAllowed($host) {
        if (Util::isCommand()) {
            return true;
        }
        $cors = Bundle::common('isCorsAllowed', [$host]);
        if (is_bool($cors)) {
            return $cors;
        }

        foreach ([
            self::NAME_SPENDR,
            self::NAME_TRANSFER_MEX,
            self::NAME_UTC,
            self::NAME_SKUX,
        ] as $name) {
            $p = self::get($name);
            if ($p && $p->isHostMatched($host)) {
                return $p;
            }
        }

        if (!Util::isLive()) {
            return true;
        }

        return null;
    }

    public static function getAllInternalDomains()
    {
        $prefixes = ['www.', 'staging.', 'test.', 'stage.', 'demo.'];
        $host = str_replace($prefixes, '', Util::getParameter('host_path'));
        $all = [$host];
        foreach ($prefixes as $prefix) {
            $all[] = $prefix . $host;
        }
        $platforms = Util::em()->getRepository(self::class)->findAll();
        /** @var  Platform $platform */
        foreach ($platforms as $platform) {
            $sub = $platform->getSubDomain();
            if ($sub) {
                $all[] = $sub . '.' . $host;
                $all[] = $sub . '-test.' . $host;
            }
            $domains = $platform->getAllCustomDomains();
            foreach ($domains as $domain) {
                $all[] = Util::hostOnly($domain);
            }
        }
        return $all;
    }

    public function isDeprecated() {
        return in_array($this->getName(), [
            self::NAME_CASH_ON_WEB,
            self::NAME_ES_SOLO,
            self::NAME_FIS,
            self::NAME_UTC,
        ]);
    }

    public function getName($fix = false)
    {
        $name = parent::getName();
        if ($fix && $name === self::NAME_TERN_COMMERCE) {
            $name = CardProgram::NAME_US_UNLOCKED;
        }
        return $name;
    }

    public function getFixedName()
    {
        $name = parent::getName();
        if ($name === self::NAME_TERN_COMMERCE) {
            $host = Util::hostOnly(Util::host());
            if (Util::containsSubString($host, [
                'usunlocked',
                'usu.span',
            ])) {
                $name = CardProgram::NAME_US_UNLOCKED;
            }
        }
        return $name;
    }

    public function getKey()
    {
        $name = $this->getName();

        $ref = new \ReflectionClass($this);
        $ccs = $ref->getReflectionConstants();
        foreach ($ccs as $cc) {
            $cn = $cc->getName();
            if (Util::startsWith($cn, 'NAME_')) {
                if ($name === $cc->getValue()) {
                    return substr($cn, 5);
                }
            }
        }

        return strtoupper($name);
    }

    public function getAdminLayout()
    {
        $name = $this->getName();
        if (in_array($name, [
            self::NAME_TRANSFER_MEX,
        ])) {
            return 'jake';
        }
        return 'admin';
    }

    /**
     * Overwrite the logo configured in the platform branding
     */
    public function getSpecialAdminPortalLogo()
    {
        if ($this->isTernCommerce()) {
            return '/static/img/usunlocked_admin_portal.png';
        }
        if ($this->isTransferMex()) {
            return '/static/mex/img/logo_green.png';
        }
        // if ($this->isWilen()) {
        //   return '/static/wilen/wilen-logo-horizontal-white.png';
        // }
        return null;
    }

    public function hasConsumerPortal() {
        if (in_array($this->getName(), [
            self::NAME_TERN_COMMERCE,
            self::NAME_UTC,
            self::NAME_CASH_ON_WEB,
            self::NAME_TRANSFER_MEX,
			self::NAME_SPENDR,
        ])) {
            return true;
        }
        return false;
    }

    public function canConsumerRegister() {
        if (in_array($this->getName(), [
            self::NAME_TERN_COMMERCE,
            self::NAME_UTC,
            self::NAME_CASH_ON_WEB,
        ])) {
            return true;
        }
        return false;
    }

    public function is($name) {
        return $this->getName() === $name;
    }

    public function getBundleName() {
        $map = [
            self::NAME_UTC          => 'ClfBundle',
            self::NAME_ES_SOLO      => 'EsSoloBundle',
            self::NAME_FIS          => 'FisBundle',
            self::NAME_LEAFLINK     => 'LeafLinkBundle',
            self::NAME_TRANSFER_MEX => 'TransferMexBundle',
            self::NAME_CASH_ON_WEB  => 'CashOnWebBundle',
            self::NAME_PTO_GENIUS   => 'PTOBundle',
            self::NAME_WILEN        => 'WilenBundle',

            self::NAME_FAAS               => 'FaasBundle',
            self::NAME_RED_CARD_ATHLETICS => 'FaasBundle',
            self::NAME_D2                 => 'FaasBundle',
            self::NAME_AMERICA_VOICE      => 'FaasBundle',
            self::NAME_IQSTEL             => 'FaasBundle',
            self::NAME_FLUTTERWAVE        => 'FaasBundle',

            self::NAME_SPENDR => 'SpendrBundle',

            self::NAME_SKUX => 'SkuxBundle',

            // TODO: add more mapping
        ];
        $name = $this->getName();
        return $map[$name] ?? 'PortalBundle';
    }

    public function getLayoutPath() {
        if ($this->getName() === self::NAME_UTC) {
            return '@Clf//layout.html.twig';
        }
        return '@Portal/Default/layout.html.twig';
    }

    public static function getLayoutPathOf(Platform $platform = null) {
        if ($platform) {
            return $platform->getLayoutPath();
        }
        return '@Portal/Default/layout.html.twig';
    }

    /**
     * @param bool $extra
     *
     * @return array
     */
    public function toApiArray(bool $extra = FALSE): array
    {
        $data = parent::toApiArray($extra);

        $data = array_merge($data, [
            'customize' => Util::s2j($this->getCustomize()),
            'css' => $this->getCss(),
            'js' => $this->getJs(),
            'domain' => $this->getDomain(),
            'subDomain' => $this->getSubDomain(),
            'logo' => $this->getLogoUrl(),
            'lightLogo' => $this->getLightLogoUrl(),
            'icon' => $this->getIconUrl(),
            'language' => $this->getDefaultLanguage(),
        ]);

        if ($extra) {
            $data['cardPrograms'] = Util::toApiArray($this->getCardPrograms(), $extra);
        }

        return $data;
    }

     /**
     * @return array
     */
    public function toPartnerArray(): array
    {
        $data = parent::toApiArray(false);
        $cardPrograms =  Util::toApiArray($this->getCardPrograms());
        $repo = Util::em()->getRepository(\CoreBundle\Entity\CardProgram::class);
        // var_dump( $cardProgram);
        if ($cardPrograms) {
          $cardProgram = $repo->find($cardPrograms[0]['id']);
          $memberFee = $repo->getPartnerFeeItem($cardProgram,FeeGlobalName::ONE_TIME_MEMBERSHIP_FEE);
          $monthlyFee = $repo->getPartnerFeeItem($cardProgram,FeeGlobalName::MONTHLY_FEE);
          $unloadFee = $repo->getPartnerFeeItem($cardProgram,FeeGlobalName::UNLOAD_FEE);
          $transactonFee = $repo->getPartnerFeeItem($cardProgram,FeeGlobalName::APPROVED_TRANSACTION_FEE);
        }

        $data = array_merge($data, [
            'customize' => Util::s2j($this->getCustomize()),
            'css' => $this->getCss(),
            'js' => $this->getJs(),
            'domain' => $this->getDomain(),
            'subDomain' => $this->getSubDomain(),
            'cuscolor' => $this->getCuscolor(),
            'logo' => $this->getLogoUrl(),
            'lightLogo' => $this->getLightLogoUrl(),
            'icon' => $this->getIconUrl(),
            'language' => $this->getDefaultLanguage(),
            'forwarding' => $this->getForwarding() ? $this->getForwarding() : false,
            'locator' => $this->getLocator() ?  $this->getLocator() : false,
            'locatorUrl' => $this->getLocatorUrl(),
            'oneCardArt' => $this->getUrl('oneCard'),
            'stepOne' => $this->getUrl('stepOne'),
            'stepThree' => $this->getUrl('stepThree'),
            'useInfo' => $this->getUrl('useInfo'),
            'admin' => PartnerService::getMaster($this->getName()),
            'tranIcon' => $this->getUrl('tranIcon'),
            'memberShipFeeStr' => isset($memberFee) && $memberFee ? Money::format($memberFee->getFeeToCusFixed(), $memberFee->getFeeItem()->getCostTernFixedCurrency()) : 0,
            'memberShipFee' => isset($memberFee) && $memberFee ? $memberFee->getFeeToCusFixed() / 100 : 0,
            'tmemberShipFee' => isset($memberFee) && $memberFee ? ( $memberFee->getFeeToCusFixed() - $memberFee->getFeeItem()->getCostTernFixed()) / 100: 0,
            'monthlyFeeStr' => isset($monthlyFee) && $monthlyFee ? Money::format($monthlyFee->getFeeToCusFixed(), $memberFee->getFeeItem()->getCostTernFixedCurrency()) : 0,
            'monthlyFee' => isset($monthlyFee) && $monthlyFee ? $monthlyFee->getFeeToCusFixed() / 100 : 0,
            'tmonthlyFee' => isset($monthlyFee) && $monthlyFee ? ($monthlyFee->getFeeToCusFixed() - $monthlyFee->getFeeItem()->getCostTernFixed()) / 100 : 0,
            'unloadFeeStr' => isset($unloadFee) && $unloadFee ? Money::format($unloadFee->getFeeToCusFixed(), $memberFee->getFeeItem()->getCostTernFixedCurrency()) : 0,
            'unloadFee' => isset($unloadFee) && $unloadFee ? $unloadFee->getFeeToCusFixed() / 100 : 0,
            'tunloadFee' => isset($unloadFee) && $unloadFee ? ($unloadFee->getFeeToCusFixed() - $unloadFee->getFeeItem()->getCostTernFixed()) / 100 : 0,
            'transactionFeeStr' => isset($transactonFee) && $transactonFee ? Money::format($transactonFee->getFeeToCusFixed(), $memberFee->getFeeItem()->getCostTernFixedCurrency()) : 0,
            'transactionFee' => isset($transactonFee) && $transactonFee ? $transactonFee->getFeeToCusFixed() / 100 : 0,
            'ttransactionFee' => isset($transactonFee) && $transactonFee ? ($transactonFee->getFeeToCusFixed() - $transactonFee->getFeeItem()->getCostTernFixed()) / 100 : 0,
        ]);

        return $data;
    }

    public function getUrl ($feild) {
      $img = null;
      switch($feild) {
        case 'stepOne':
          $img = $this->getStepOne();
          break;
        case 'stepThree':
          $img = $this->getStepThree();
          break;
        case 'oneCard':
          $img = $this->getOneCardArt();
          break;
        case 'useInfo':
          $img = $this->getUseInfo();
          break;
        case 'tranIcon':
          $img = $this->getTranIcon();
          break;
      }

      if ($img) {
          return $img->getAssetUrl(true, false);
      }
      return '';
    }
    public function host()
    {
        $schema = Util::getParameter('host_schema');
        $domain = $this->getDomain();
        if ($domain) {
            return $schema . '://' . $domain;
        }
        $subDomain = $this->getSubDomainFull();
        if (!$subDomain) {
            $subDomain = Util::getParameter('host_path');
        }
        return $schema . '://' . $subDomain;
    }

    /**
     * @param $host string no schema and suffix "/", for instance, account.usunlocked.com
     *
     * @return bool
     */
    public function isHostMatched($host)
    {
        $sub = $this->getSubDomainFull();
        if ($sub && $sub === $host) {
            return true;
        }
        $cus = $this->getAllCustomDomains();
        return in_array($host, $cus);
    }

    public function getSubDomainFull()
    {
        $subDomain = $this->getSubDomain();
        if (!$subDomain) {
            return null;
        }
        $host = Util::getParameter('host_path');
        $test = Util::endsWith($subDomain, '-test');
        $found = false;
        $prefixes = ['www.', 'staging.'];
        foreach ($prefixes as $prefix) {
            $index = mb_strpos($host, $prefix);
            if ($index !== false) {
                $replace = $prefix === 'staging.' ? ($subDomain . '-staging') : $subDomain;
                if ($test) {
                    $replace = $subDomain;
                }
                $host = str_replace($prefix, $replace . '.', $host);
                $found = true;
                break;
            }
        }
        if (!$found) {
            $host = $subDomain . '.' . $host;
        }
        return $host;
    }

    public function subDomainHost()
    {
        $schema = Util::getParameter('host_schema');
        $subDomain = $this->getSubDomainFull();
        if (!$subDomain) {
            $subDomain = Util::getParameter('host_path');
        }
        return $schema . '://' . $subDomain;
    }

    public function domainHost()
    {
        $domain = $this->getDomain();
        if (!$domain) {
            return '';
        }
        $request = Util::request();
        return $request->getScheme() . '://' . $domain;
    }

    public function getAllCustomDomains() {
        $all = Util::s2j($this->getOtherDomains());
        $domain = $this->getDomain();
        if ($domain) {
            array_unshift($all, $domain);
        }
        return $all;
    }

    public function getLogoUrl()
    {
        $logo = $this->getLogo();
        if ($logo) {
            return $logo->getAssetUrl(true, false);
        }
        return Util::host() . self::DEFAULT_LOGO;
    }

    public function getLightLogoUrl()
    {
        $logo = $this->getLightLogo();
        if ($logo) {
            return $logo->getAssetUrl(true, false);
        }
        return null;
    }

    public function getCustomizeVersion() {
        static $str = null;
        if ($str) {
            return $str;
        }
        $customizes = Util::json($this, 'customize') ?: [];
        $customizes['__custom_logo__'] = $this->getLogoUrl();
        $customizes['__custom_lightLogo__'] = $this->getLightLogoUrl();
        $customizes['__custom_icon__'] = $this->getIconUrl();
        $customizes['__custom_css__'] = $this->getCss();
        $customizes['__custom_js__'] = $this->getJs();
        Util::fillPlatformVariablesToRender($customizes);
        return md5(json_encode($customizes));
    }

    public function getTouchIconUrl() {
        $icon = $this->getIcon();
        if (!$icon) {
            return self::DEFAULT_ICON_TOUCH;
        }
        return $icon->getAssetUrl(false, false);
    }

    public function getIconUrl() {
        $icon = $this->getIcon();
        if (!$icon) {
            return Util::host() . self::DEFAULT_ICON_TOUCH;
        }
        return $icon->getAssetUrl(true, false);
    }

    /**
     * @param string|int $name
     *
     * @return Platform|object|null
     */
    public static function find($name)
    {
        if (is_numeric($name)) {
            $p = self::findById($name);
            if ($p) {
                return $p;
            }
        }
        return Util::em()->getRepository(__CLASS__)
            ->findOneBy([
                'name' => $name,
            ]);
    }

    public function getContactLinks()
    {
        return Bundle::common('getContactLinks', [], [
            'terms' => '/terms',
            'fees' => '/pricing',
            'faq' => '/faq',
            'support' => '/support',
        ]);
    }

    public function isTernCommerce()
    {
        return $this->getName() === static::NAME_TERN_COMMERCE;
    }

    public function isUTC()
    {
        return $this->getName() === static::NAME_UTC;
    }

    public function isEsSolo()
    {
        return $this->getName() === static::NAME_ES_SOLO;
    }

    public function isFIS()
    {
        return $this->getName() === static::NAME_FIS;
    }

    public function isLeafLink()
    {
        return $this->getName() === static::NAME_LEAFLINK;
    }

    public function isTransferMex()
    {
        return $this->getName() === static::NAME_TRANSFER_MEX;
    }

    public function isCashOnWeb()
    {
        return $this->getName() === static::NAME_CASH_ON_WEB;
    }

    public function isPtoGenius()
    {
        return $this->getName() === static::NAME_PTO_GENIUS;
    }

    public function isFaas()
    {
        return $this->isFaasPlatforms();
    }

    public function isRedCard()
    {
        return $this->getName() === static::NAME_RED_CARD_ATHLETICS;
    }

    public function isD2()
    {
        return $this->getName() === static::NAME_D2;
    }

    public function isAmericaVoice()
    {
        return $this->getName() === static::NAME_AMERICA_VOICE;
    }

    public function isIQStel()
    {
        return $this->getName() === static::NAME_IQSTEL;
    }

    public function isFaasPlatforms()
    {
        $bundle = $this->getBundleName();
        return $bundle === 'FaasBundle';
    }

    public function isWilen()
    {
        return $this->getName() === static::NAME_WILEN;
    }

    public function isSpendr()
    {
        return $this->getName() === static::NAME_SPENDR;
    }

    public function isSKUx()
    {
        return $this->getName() === static::NAME_SKUX;
    }

    protected function isMetaEnabled($field, $default = false)
    {
        $meta = Util::meta($this);
        $value = $meta[$field] ?? $default;
        return $value === true || $value === 'true';
    }

    /**
     * @deprecated Use isIdScanRequired instead since OFAC is always required
     * @return bool
     */
    public function isKYCRequired () {
        return $this->isMetaEnabled('kycRequire');
    }

    public function isIdScanRequired () {
        return $this->isKYCRequired();
    }
    public function isKycSkipCountryValidation () {
        return $this->isMetaEnabled('kycSkipCountryValidation');
    }

    public function isClientsRequired () {
        return $this->isMetaEnabled('clientsRequire');
    }

    public function isPortalsEnabled () {
        return $this->isMetaEnabled('enablePortals');
    }

    public function isApiEnabled () {
        return $this->isMetaEnabled('enableAPIs');
    }

    public function isLoadConfigureEnabled () {
        return $this->isMetaEnabled('enableLoadTypeConfigure');
    }

    /**
     * Get customize
     *
     * @return string
     */
    public function getCustomize()
    {
        $c = $this->customize;
        if ($c && $c !== '[]') {
            return $c;
        }
        return Util::j2s(self::CUSTOMIZES);
    }

    public function getDisabledAPIsArray()
    {
        $apis = Bundle::common('getDisabledAPIs', [], []);
        if ($apis) {
            return $apis;
        }
        return Util::s2j($this->getDisabledAPIs()) ?: [];
    }

    public function getHelpUrl()
    {
        return '';
    }

    public function getProductTeam()
    {
        return $this->getProductName() ?: $this->getName();
    }

    /**
     * @ORM\OneToMany(targetEntity="CoreBundle\Entity\CardProgram", mappedBy="platform")
     */
    private $cardPrograms;

    /**
     * @ORM\OneToMany(targetEntity="CoreBundle\Entity\CardDistributionCenter", mappedBy="platform")
     */
    private $distributionCenters;

    /**
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\Platform", inversedBy="subPlatforms")
     */
    private $parentPlatform;

    /**
     * @ORM\OneToMany(targetEntity="CoreBundle\Entity\Platform", mappedBy="parentPlatform")
     */
    private $subPlatforms;

    /**
     * @ORM\Column(name="api_doc", type="text", nullable=true)
     */
    private $apiDoc;

    /**
     * @var string
     *
     * @ORM\Column(name="sub_domain", type="string", nullable=true, options={"comment":"Sub domain name. For instance: https://usunlocked.virtualcards.us will work if sub_domain is `usunlocked`"})
     */
    private $subDomain;
    /**
     * @var string
     *
     * @ORM\Column(name="domain", type="string", nullable=true)
     */
    private $domain;

    /**
     * @var string
     *
     * @ORM\Column(name="other_domains", type="text", nullable=true)
     */
    private $otherDomains;

    /**
     * @var string
     *
     * @ORM\Column(name="live_domains", type="text", nullable=true)
     */
    private $liveDomains;


    /**
     * @var string
     *
     * @ORM\Column(name="description", type="text", nullable=true)
     */
    private $description;

    /**
     * @var string
     *
     * @ORM\Column(name="default_language", type="string", nullable=true, options={"comment":"Default language code. see `config` table."})
     */
    private $defaultLanguage;

    /**
     * @var Attachment
     *
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\Attachment")
     */
    private $logo;

    /**
     * @var Attachment
     *
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\Attachment")
     */
    private $lightLogo;

    /**
     * @var Attachment
     *
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\Attachment")
     */
    private $icon;

    /**
     * @var string
     *
     * @ORM\Column(name="customize", type="text", nullable=true, options={"comment":"Customize rules like button color"})
     */
    private $customize;

    /**
     * @var string
     *
     * @ORM\Column(name="disabled_apis", type="text", nullable=true, options={"comment":"Disabled APIs"})
     */
    private $disabledAPIs;

    /**
     * @var string
     *
     * @ORM\Column(name="css", type="text", nullable=true, options={"comment":"Customize CSS rules"})
     */
    private $css;

    /**
     * @var string
     *
     * @ORM\Column(name="js", type="text", nullable=true, options={"comment":"Customize JS scripts"})
     */
    private $js;

    /**
     * @var string $support_email_value
     *
     * @ORM\Column(name="support_email_value", type="string" ,nullable=true, options={"comment":"Support email value in all email templates."})
     */
    private $support_email_value;

    /**
     * @var string $live_chat_url_value
     *
     * @ORM\Column(name="live_chat_url_value", type="string",nullable=true, options={"comment":"Live chat url value in all email templates. So are the fields: sender_name, product_name, company_name, html_logo"})
     */
    private $live_chat_url_value;

    /**
     * @var string $sender_name
     *
     * @ORM\Column(name="sender_name", type="string",nullable=true)
     */
    private $sender_name;

    /**
     * @var string $product_name
     *
     * @ORM\Column(name="product_name", type="string",nullable=true)
     */
    private $product_name;

    /**
     * @var string $company_name
     *
     * @ORM\Column(name="company_name", type="string",nullable=true)
     */
    private $company_name;

    /**
     * @var string $htmlLogo Logo Html
     *
     * @ORM\Column(name="html_logo", type="text", nullable=true)
     */
    private $htmlLogo;

    /**
     * @var string $html_address Address Html
     *
     * @ORM\Column(name="html_address", type="text", nullable=true)
     */
    private $html_address;

    /**
     * @var string $text_address Address Text
     *
     * @ORM\Column(name="text_address", type="text", nullable=true)
     */
    private $text_address;

    /**
     * @var string
     *
     * @ORM\Column(name="chart_setting", type="text", nullable=true)
     */
    private $chartSetting;

    /**
     * @var Attachment
     *
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\Attachment")
     */
    private $oneCardArt;

    /**
     * @var Attachment
     *
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\Attachment")
     */
    private $stepOne;

    /**
     * @var Attachment
     *
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\Attachment")
     */
    private $stepThree;

    /**
     * @var Attachment
     *
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\Attachment")
     */
    private $tranIcon;

     /**
     * @var Attachment
     *
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\Attachment")
     */
    private $useInfo;

    /**
     * @var boolean
     *
     * @ORM\Column(name="is_partner", type="boolean", nullable=true)
     */

    protected $isPartner;
    /**
     * @var boolean
     *
     * @ORM\Column(name="locator", type="boolean", nullable=true)
     */

    protected $locator;
    /**
     * @var boolean
     *
     * @ORM\Column(name="forwarding", type="boolean", nullable=true)
     */
    protected $forwarding;

    /**
     * @var boolean
     *
     * @ORM\Column(name="locatorUrl", type="string", nullable=true)
     */
    protected $locatorUrl;

    /**
     * @var boolean
     *
     * @ORM\Column(name="termsUrl", type="string", nullable=true)
     */
    protected $termsUrl;

     /**
     * @var boolean
     *
     * @ORM\Column(name="cuscolor", type="string", nullable=true)
     */
    protected $cuscolor;

    /**
     * @var integer
     *
     * @ORM\Column(name="botm_business_id", type="integer", nullable=true)
     */
    protected $botmBusinessId;

    /**
     * @var integer
     *
     * @ORM\Column(name="botm_business_account_id", type="integer", nullable=true)
     */
    protected $botmBusinessAccountId;

    public function __construct()
    {
        parent::__construct();
        $this->cardPrograms = new ArrayCollection();
        $this->distributionCenters = new ArrayCollection();
        $this->subPlatforms = new ArrayCollection();
    }


    /**
     * Add cardProgram
     *
     * @param \CoreBundle\Entity\CardProgram $cardProgram
     *
     * @return Platform
     */
    public function addCardProgram(\CoreBundle\Entity\CardProgram $cardProgram)
    {
        $this->cardPrograms[] = $cardProgram;

        return $this;
    }

    /**
     * Remove cardProgram
     *
     * @param \CoreBundle\Entity\CardProgram $cardProgram
     */
    public function removeCardProgram(\CoreBundle\Entity\CardProgram $cardProgram)
    {
        $this->cardPrograms->removeElement($cardProgram);
    }

    /**
     * Get cardPrograms
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getCardPrograms()
    {
        return $this->cardPrograms;
    }

    /**
     * Set apiDoc
     *
     * @param string $apiDoc
     *
     * @return Platform
     */
    public function setApiDoc($apiDoc)
    {
        $this->apiDoc = $apiDoc;

        return $this;
    }

    /**
     * Get apiDoc
     *
     * @return string
     */
    public function getApiDoc()
    {
        return $this->apiDoc;
    }

    /**
     * Set subDomain
     *
     * @param string $subDomain
     *
     * @return Platform
     */
    public function setSubDomain($subDomain)
    {
        $this->subDomain = $subDomain;

        return $this;
    }

    /**
     * Get subDomain
     *
     * @return string
     */
    public function getSubDomain()
    {
        return $this->subDomain;
    }

    /**
     * Set domain
     *
     * @param string $domain
     *
     * @return Platform
     */
    public function setDomain($domain)
    {
        $this->domain = $domain;

        return $this;
    }

    /**
     * Get domain
     *
     * @return string
     */
    public function getDomain()
    {
        if ($this->domain && Util::endsWith($this->domain, '/')) {
            return substr($this->domain, 0, -1);
        }
        return $this->domain;
    }

    /**
     * Set defaultLanguage
     *
     * @param string $defaultLanguage
     *
     * @return Platform
     */
    public function setDefaultLanguage($defaultLanguage)
    {
        $this->defaultLanguage = $defaultLanguage;

        return $this;
    }

    /**
     * Get defaultLanguage
     *
     * @return string
     */
    public function getDefaultLanguage()
    {
        return $this->defaultLanguage;
    }

    /**
     * Set customize
     *
     * @param string $customize
     *
     * @return Platform
     */
    public function setCustomize($customize)
    {
        $this->customize = $customize;

        return $this;
    }

    /**
     * Set css
     *
     * @param string $css
     *
     * @return Platform
     */
    public function setCss($css)
    {
        $this->css = $css;

        return $this;
    }

    /**
     * Get css
     *
     * @return string
     */
    public function getCss()
    {
        return $this->css;
    }

    /**
     * Set logo
     *
     * @param \CoreBundle\Entity\Attachment $logo
     *
     * @return Platform
     */
    public function setLogo(\CoreBundle\Entity\Attachment $logo = null)
    {
        $this->logo = $logo;

        return $this;
    }

    /**
     * Get logo
     *
     * @return \CoreBundle\Entity\Attachment
     */
    public function getLogo()
    {
        return $this->logo;
    }

    /**
     * Set icon
     *
     * @param \CoreBundle\Entity\Attachment $icon
     *
     * @return Platform
     */
    public function setIcon(\CoreBundle\Entity\Attachment $icon = null)
    {
        $this->icon = $icon;

        return $this;
    }

    /**
     * Get icon
     *
     * @return \CoreBundle\Entity\Attachment
     */
    public function getIcon()
    {
        return $this->icon;
    }

    /**
     * Set disabledAPIs
     *
     * @param string $disabledAPIs
     *
     * @return Platform
     */
    public function setDisabledAPIs($disabledAPIs)
    {
        $this->disabledAPIs = $disabledAPIs;

        return $this;
    }

    /**
     * Get disabledAPIs
     *
     * @return string
     */
    public function getDisabledAPIs()
    {
        return $this->disabledAPIs;
    }

    /**
     * Add distributionCenter
     *
     * @param \CoreBundle\Entity\CardDistributionCenter $distributionCenter
     *
     * @return Platform
     */
    public function addDistributionCenter(\CoreBundle\Entity\CardDistributionCenter $distributionCenter)
    {
        $this->distributionCenters[] = $distributionCenter;

        return $this;
    }

    /**
     * Remove distributionCenter
     *
     * @param \CoreBundle\Entity\CardDistributionCenter $distributionCenter
     */
    public function removeDistributionCenter(\CoreBundle\Entity\CardDistributionCenter $distributionCenter)
    {
        $this->distributionCenters->removeElement($distributionCenter);
    }

    /**
     * Get distributionCenters
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getDistributionCenters()
    {
        return $this->distributionCenters;
    }

    /**
     * Set supportEmailValue
     *
     * @param string $supportEmailValue
     *
     * @return Platform
     */
    public function setSupportEmailValue($supportEmailValue)
    {
        $this->support_email_value = $supportEmailValue;

        return $this;
    }

    /**
     * Get supportEmailValue
     *
     * @return string
     */
    public function getSupportEmailValue()
    {
        return $this->support_email_value ?: '<EMAIL>';
    }

    /**
     * Set liveChatUrlValue
     *
     * @param string $liveChatUrlValue
     *
     * @return Platform
     */
    public function setLiveChatUrlValue($liveChatUrlValue)
    {
        $this->live_chat_url_value = $liveChatUrlValue;

        return $this;
    }

    /**
     * Get liveChatUrlValue
     *
     * @return string
     */
    public function getLiveChatUrlValue()
    {
        return $this->live_chat_url_value ?: ($this->host() . '/contact');
    }

    /**
     * Set senderName
     *
     * @param string $senderName
     *
     * @return Platform
     */
    public function setSenderName($senderName)
    {
        $this->sender_name = $senderName;

        return $this;
    }

    /**
     * Get senderName
     *
     * @return string
     */
    public function getSenderName()
    {
        return $this->sender_name ?: $this->getName();
    }

    /**
     * Set productName
     *
     * @param string $productName
     *
     * @return Platform
     */
    public function setProductName($productName)
    {
        $this->product_name = $productName;

        return $this;
    }

    /**
     * Get productName
     *
     * @return string
     */
    public function getProductName()
    {
        return $this->product_name ?: $this->getName();
    }

    /**
     * Set companyName
     *
     * @param string $companyName
     *
     * @return Platform
     */
    public function setCompanyName($companyName)
    {
        $this->company_name = $companyName;

        return $this;
    }

    /**
     * Get companyName
     *
     * @return string
     */
    public function getCompanyName()
    {
        return $this->company_name ?: $this->getName();
    }

    /**
     * Set htmlLogo
     *
     * @param string $htmlLogo
     *
     * @return Platform
     */
    public function setHtmlLogo($htmlLogo)
    {
        $this->htmlLogo = $htmlLogo;

        return $this;
    }

    /**
     * Get htmlLogo
     *
     * @return string
     */
    public function getHtmlLogo()
    {
        return $this->htmlLogo ?: self::createHtmlLogo($this->getLogoUrl(), $this->host());
    }

    public static function createHtmlLogo($image, $href)
    {
        return '<div style="text-align: center;"><strong><a class="imgCaptionAnchor" track="on" shape="rect" href="' . $href . '"><img name="ACCOUNT.IMAGE.165" style="max-width: 300px; max-height: 70px;" src="' . $image . '" border="0" hspace="5" vspace="5"></a></strong></div>';
    }

    /**
     * Set htmlAddress
     *
     * @param string $htmlAddress
     *
     * @return Platform
     */
    public function setHtmlAddress($htmlAddress)
    {
        $this->html_address = $htmlAddress;

        return $this;
    }

    /**
     * Get htmlAddress
     *
     * @return string
     */
    public function getHtmlAddress()
    {
        return $this->html_address ?: '';
    }

    /**
     * Set textAddress
     *
     * @param string $textAddress
     *
     * @return Platform
     */
    public function setTextAddress($textAddress)
    {
        $this->text_address = $textAddress;

        return $this;
    }

    /**
     * Get textAddress
     *
     * @return string
     */
    public function getTextAddress()
    {
        return $this->text_address ?: '';
    }

    /**
     * Set chartSetting
     *
     * @param string $chartSetting
     *
     * @return Platform
     */
    public function setChartSetting($chartSetting)
    {
        $this->chartSetting = $chartSetting;
        return $this;
    }

    /**
     * Get chartSetting
     *
     * @return string
     */
    public function getChartSetting()
    {
        return $this->chartSetting;
    }

    /**
     * Set oneCardArt
     *
     * @param \CoreBundle\Entity\Attachment $oneCardArt
     *
     * @return Platform
     */
    public function setOneCardArt(\CoreBundle\Entity\Attachment $oneCardArt = null)
    {
        $this->oneCardArt = $oneCardArt;

        return $this;
    }

    /**
     * Get oneCardArt
     *
     * @return \CoreBundle\Entity\Attachment
     */
    public function getOneCardArt()
    {
        return $this->oneCardArt;
    }

    /**
     * Set stepOne
     *
     * @param \CoreBundle\Entity\Attachment $stepOne
     *
     * @return Platform
     */
    public function setStepOne(\CoreBundle\Entity\Attachment $stepOne = null)
    {
        $this->stepOne = $stepOne;

        return $this;
    }

    /**
     * Get stepOne
     *
     * @return \CoreBundle\Entity\Attachment
     */
    public function getStepOne()
    {
        return $this->stepOne;
    }

    /**
     * Set stepThree
     *
     * @param \CoreBundle\Entity\Attachment $stepThree
     *
     * @return Platform
     */
    public function setStepThree(\CoreBundle\Entity\Attachment $stepThree = null)
    {
        $this->stepThree = $stepThree;

        return $this;
    }

    /**
     * Get stepThree
     *
     * @return \CoreBundle\Entity\Attachment
     */
    public function getStepThree()
    {
        return $this->stepThree;
    }

    /**
     * Set isPartner
     *
     * @param boolean $isPartner
     *
     * @return Platform
     */
    public function setIsPartner($isPartner)
    {
        $this->isPartner = $isPartner;

        return $this;
    }

    /**
     * Get isPartner
     *
     * @return boolean
     */
    public function getIsPartner()
    {
        return $this->isPartner;
    }

    /**
     * Set locator
     *
     * @param boolean $locator
     *
     * @return Platform
     */
    public function setLocator($locator)
    {
        $this->locator = $locator;

        return $this;
    }

    /**
     * Get locator
     *
     * @return boolean
     */
    public function getLocator()
    {
        return $this->locator;
    }

    /**
     * Set forwarding
     *
     * @param boolean $forwarding
     *
     * @return Platform
     */
    public function setForwarding($forwarding)
    {
        $this->forwarding = $forwarding;

        return $this;
    }

    /**
     * Get forwarding
     *
     * @return boolean
     */
    public function getForwarding()
    {
        return $this->forwarding;
    }

    /**
     * Set locatorUrl
     *
     * @param string $locatorUrl
     *
     * @return Platform
     */
    public function setLocatorUrl($locatorUrl)
    {
        $this->locatorUrl = $locatorUrl;

        return $this;
    }

    /**
     * Get locatorUrl
     *
     * @return string
     */
    public function getLocatorUrl()
    {
        return $this->locatorUrl;
    }

    /**
     * Set termsUrl
     *
     * @param string $termsUrl
     *
     * @return Platform
     */
    public function setTermsUrl($termsUrl)
    {
        $this->termsUrl = $termsUrl;

        return $this;
    }

    /**
     * Get termsUrl
     *
     * @return string
     */
    public function getTermsUrl()
    {
        return $this->termsUrl;
    }

    /**
     * Set tranIcon
     *
     * @param \CoreBundle\Entity\Attachment $tranIcon
     *
     * @return Platform
     */
    public function setTranIcon(\CoreBundle\Entity\Attachment $tranIcon = null)
    {
        $this->tranIcon = $tranIcon;

        return $this;
    }

    /**
     * Get tranIcon
     *
     * @return \CoreBundle\Entity\Attachment
     */
    public function getTranIcon()
    {
        return $this->tranIcon;
    }

    /**
     * Set useInfo
     *
     * @param \CoreBundle\Entity\Attachment $useInfo
     *
     * @return Platform
     */
    public function setUseInfo(\CoreBundle\Entity\Attachment $useInfo = null)
    {
        $this->useInfo = $useInfo;

        return $this;
    }

    /**
     * Get useInfo
     *
     * @return \CoreBundle\Entity\Attachment
     */
    public function getUseInfo()
    {
        return $this->useInfo;
    }

    /**
     * Set cuscolor
     *
     * @param string $cuscolor
     *
     * @return Platform
     */
    public function setCuscolor($cuscolor)
    {
        $this->cuscolor = $cuscolor;

        return $this;
    }

    /**
     * Get cuscolor
     *
     * @return string
     */
    public function getCuscolor()
    {
        return $this->cuscolor;
    }

    /**
     * Set otherDomains
     *
     * @param string $otherDomains
     *
     * @return Platform
     */
    public function setOtherDomains($otherDomains)
    {
        $this->otherDomains = $otherDomains;

        return $this;
    }

    /*
     * Get otherDomains
     *
     * @return string
     */
    public function getOtherDomains()
    {
        return $this->otherDomains;
    }

    /**
     * Set description
     *
     * @param string $description
     *
     * @return Platform
     */
    public function setDescription($description)
    {
        $this->description = $description;

        return $this;
    }

    /**
     * Get description
     *
     * @return string
     */
    public function getDescription()
    {
        return $this->description;
    }

    /**
     * Set parentPlatform
     *
     * @param \CoreBundle\Entity\Platform $parentPlatform
     *
     * @return Platform
     */
    public function setParentPlatform(\CoreBundle\Entity\Platform $parentPlatform = null)
    {
        $this->parentPlatform = $parentPlatform;

        return $this;
    }

    /**
     * Get parentPlatform
     *
     * @return \CoreBundle\Entity\Platform
     */
    public function getParentPlatform()
    {
        return $this->parentPlatform;
    }

    /**
     * Add subPlatform
     *
     * @param \CoreBundle\Entity\Platform $subPlatform
     *
     * @return Platform
     */
    public function addSubPlatform(\CoreBundle\Entity\Platform $subPlatform)
    {
        $this->subPlatforms[] = $subPlatform;

        return $this;
    }

    /**
     * Remove subPlatform
     *
     * @param \CoreBundle\Entity\Platform $subPlatform
     */
    public function removeSubPlatform(\CoreBundle\Entity\Platform $subPlatform)
    {
        $this->subPlatforms->removeElement($subPlatform);
    }

    /**
     * Get subPlatforms
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getSubPlatforms()
    {
        return $this->subPlatforms;
    }

    /**
     * Set js
     *
     * @param string $js
     *
     * @return Platform
     */
    public function setJs($js)
    {
        $this->js = $js;

        return $this;
    }

    /**
     * Get js
     *
     * @return string
     */
    public function getJs()
    {
        return $this->js;
    }

    /**
     * Set lightLogo
     *
     * @param \CoreBundle\Entity\Attachment $lightLogo
     *
     * @return Platform
     */
    public function setLightLogo(\CoreBundle\Entity\Attachment $lightLogo = null)
    {
        $this->lightLogo = $lightLogo;

        return $this;
    }

    /**
     * Get lightLogo
     *
     * @return \CoreBundle\Entity\Attachment
     */
    public function getLightLogo()
    {
        return $this->lightLogo;
    }

    /**
     * Set liveDomains
     *
     * @param string $liveDomains
     *
     * @return Platform
     */
    public function setLiveDomains($liveDomains)
    {
        $this->liveDomains = $liveDomains;

        return $this;
    }

    /**
     * Get liveDomains
     *
     * @return string
     */
    public function getLiveDomains()
    {
        return $this->liveDomains;
    }

    public function isIsPartner(): ?bool
    {
        return $this->isPartner;
    }

    public function isLocator(): ?bool
    {
        return $this->locator;
    }

    public function isForwarding(): ?bool
    {
        return $this->forwarding;
    }

    public function getBotmBusinessId(): ?int
    {
        return $this->botmBusinessId;
    }

    public function setBotmBusinessId(?int $botmBusinessId): static
    {
        $this->botmBusinessId = $botmBusinessId;

        return $this;
    }

    public function getBotmBusinessAccountId(): ?int
    {
        return $this->botmBusinessAccountId;
    }

    public function setBotmBusinessAccountId(?int $botmBusinessAccountId): static
    {
        $this->botmBusinessAccountId = $botmBusinessAccountId;

        return $this;
    }
}
