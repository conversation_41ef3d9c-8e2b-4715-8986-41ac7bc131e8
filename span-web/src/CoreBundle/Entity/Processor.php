<?php
/**
 * User: Bob
 * Date: 2017/2/17
 * Time: 7:56
 * This is used to record the processor information
 */

namespace CoreBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use FaasBundle\Services\BOTM\BotmAPI;
use FaasBundle\Services\BOTM\BotmService;
use FaasBundle\Services\IProcessor;
use FaasBundle\Services\IProcessorService;
use TransferMexBundle\Services\RapidAPI;
use TransferMexBundle\Services\RapidService;

/**
 * Class Processor
 * @ORM\Table(name="processor")
 * @ORM\Entity(repositoryClass="CoreBundle\Repository\ProcessorRepository")
 * @package CoreBundle\Entity
 */
class Processor extends Tenant
{
    const NAME_FIRST_VIEW = 'FirstView';
    const NAME_FIS = 'FIS';
    const NAME_GTP = 'GTP';
    const NAME_NI = 'NI';
    const NAME_RAPID = 'Rapid';
    const NAME_BOTM = 'BOTM';
    const NAME_Privacy = 'Privacy';
    const NAME_TERN = 'TERN';

    public function is($name)
    {
        return $this->getName() === $name;
    }

    public function isRapid()
    {
        return $this->is(self::NAME_RAPID);
    }

    public function isBOTM()
    {
        return $this->is(self::NAME_BOTM);
    }


    public function isTERN()
    {
        return $this->is(self::NAME_TERN);
    }

    /**
     * @return IProcessor
     */
    public function getApiClass()
    {
        $name = $this->getName();
        if ($name === self::NAME_RAPID) {
            return RapidAPI::class;
        }
        if ($name === self::NAME_BOTM) {
            return BotmAPI::class;
        }
        throw new \RuntimeException('Unimplemented API for ' . $name);
    }

    /**
     * @return IProcessorService
     */
    public function getApiService()
    {
        $name = $this->getName();
        if ($name === self::NAME_RAPID) {
            return RapidService::class;
        }
        if ($name === self::NAME_BOTM) {
            return BotmService::class;
        }
        throw new \RuntimeException('Unimplemented API service for ' . $name);
    }

    /**
     * @param UserCard $uc
     *
     * @return IProcessor
     */
    public function getApiImplForUserCard(UserCard $uc)
    {
        $cls = $this->getApiClass();
        return call_user_func([
            $cls, 'getForUserCard',
        ], $uc);
    }

    /**
     * @param UserGroup $ug
     *
     * @return IProcessor
     */
    public function getApiImplForUserGroup(UserGroup $ug)
    {
        $cls = $this->getApiClass();
        return call_user_func([
            $cls, 'getForUserGroup',
        ], $ug);
    }

    public function getApiImpl()
    {
        $cls = $this->getApiClass();
        return new $cls();
    }

    public function canIssueCard()
    {
        if ($this->isBOTM()) {
            return true;
        }
        if ($this->isRapid()) {
            return false;
        }
        return null;
    }

    //Begin Add by Bob Wen Bao on 2017-03-06 record program manager and processor relationships
    /**
     * @var boolean $isProgramManager
     *
     * @ORM\Column(name="is_program_manager", type="boolean")
     */
    private $isProgramManager = false;  // TODO Nowhere else set this non-nullable feild, fix this!!

    /**
     * @var integer $programManagerId
     *
     * @ORM\Column(name="program_manager_id", type="integer", nullable=true)
     */
    private $programManagerId;
    //End

    /**
     * Set isProgramManager
     *
     * @param boolean $isProgramManager
     *
     * @return Processor
     */
    public function setIsProgramManager($isProgramManager)
    {
        $this->isProgramManager = $isProgramManager;

        return $this;
    }

    /**
     * Get isProgramManager
     *
     * @return boolean
     */
    public function getIsProgramManager()
    {
        return $this->isProgramManager;
    }

    /**
     * Set programManagerId
     *
     * @param integer $programManagerId
     *
     * @return Processor
     */
    public function setProgramManagerId($programManagerId)
    {
        $this->programManagerId = $programManagerId;

        return $this;
    }

    /**
     * Get programManagerId
     *
     * @return integer
     */
    public function getProgramManagerId()
    {
        return $this->programManagerId;
    }
}
