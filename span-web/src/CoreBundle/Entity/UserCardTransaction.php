<?php
/**
 * User: Bob
 * Date: 2017/4/28
 * Time: 18:25
 * This is used to handle the user card transaction record
 */

namespace CoreBundle\Entity;

use ApiBundle\Entity\ApiEntityInterface;
use Api<PERSON>undle\Entity\Coin;
use Carbon\Carbon;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Util;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use FaasBundle\Entity\UserCardTransactionFaasTrait;
use LeafLinkBundle\Entity\UserCardTransactionLeafLinkTrait;
use SkuxBundle\Entity\UserCardTransactionSkuxTrait;
use TransferMexBundle\Entity\UserCardTransactionTransferMexTrait;
use UsUnlockedBundle\Entity\UserCardAllTransactionTrait;
use UsUnlockedBundle\Entity\UserCardTransactionTrait;

/**
 * Class UserCardTransaction
 * @ORM\Entity(repositoryClass="CoreBundle\Repository\UserCardTransactionRepository")
 * @ORM\Table(name="user_card_transaction", indexes={
 *     @ORM\Index(columns={"user_card_id"}),
 *     @ORM\Index(columns={"tran_id"}),
 *     @ORM\Index(columns={"tran_id", "product_name"}),
 *     @ORM\Index(columns={"user_card_id", "txn_time"}, name="user_card_transaction_user_card_id_txn_time_index"),
 *     @ORM\Index(columns={"tran_desc"}),
 *     @ORM\Index(columns={"txn_time"}),
 *     @ORM\Index(columns={"merchant_id"}),
 *     @ORM\Index(columns={"account_status"}),
 *     @ORM\Index(columns={"ach_batch_id"}),
 *     @ORM\Index(columns={"tran_code"}),
 *     @ORM\Index(columns={"partner"})
 * }, options={"comment":"User card transactions(purchase, load/unload, charge fee, etc) history. Data comes from FirstView's report files. List in consumer portal's activity statement page."})
 * @package CoreBundle\Entity
 */
class UserCardTransaction extends BaseEntity implements ApiEntityInterface
{
    use UserCardTransactionTrait;
    use UserCardAllTransactionTrait;
    use UserCardTransactionLeafLinkTrait;
    use UserCardTransactionTransferMexTrait;
    use UserCardTransactionFaasTrait;
    use UserCardTransactionSkuxTrait;

    const CODE_MONTHLY_FEE = '00010';
    const CODE_MONTHLY_FEE_REVERSAL = '00011';
    const CODE_ACH_PAYROLL_DEPOSIT = '00037';
    const CODE_POS_PURCHASE = '00040';
    const CODE_PURCHASE_RETURN = '00041';
    const CODE_POS_PURCHASE_RETURN = '00043';
    const CODE_DECLINED_TRANSACTION_FEE = '00214';
    const CODE_CLIENT_FUNDED_DEPOSIT = '00611';
    const CODE_RESERVE_DEPOSIT = '00613';
    const CODE_RESERVE_DEPOSIT_REVERSAL = '00614';
    const CODE_PSKW_CARD_LOAD = '00695';

    const STATUS_LL_SENT = 'sent'; // Successful prefed received. Nacha has been sent to FED.
    const STATUS_LL_CANCELED = 'canceled'; // Funds send request was cancelled from YayPay
    const STATUS_LL_PROCESSING = 'processing'; // In que for processing
    const STATUS_LL_RETURNED = 'returned'; // Funds were returned
    const STATUS_LL_SETTLED = 'settled'; // Funds have settled
    const STATUS_LL_QUESTIONABLE = 'questionable'; // Reporting returned an error when dealing with a transaction. More investigation is required.
    const STATUS_LL_RECEIVED = 'received'; // Payment request has been received from LL for processing.
    const STATUS_EXECUTED = 'Executed';
    const STATUS_CANCLED = 'canceled';
    const STATUS_DUPLICATED = 'Duplicated';
    const STATUS_REVERSED = 'Reversed';
    const STATUS_INITIATED = 'Initiated';

    const LL_TYPE_B2B_TO = 'toB2B'; // B2B Type. This is used for distinguishing to and from UCTs.
    const LL_TYPE_B2B_FROM = 'fromB2B'; // B2B Type. This is used for distinguishing to and from UCTs.
    const LL_TYPE_B2B = 'B2B'; // Generic B2B Type.

    const PRODUCT_TRANSFER_MEX = 'TransferMex';

    public const DISPUTE_STATUS_NONE = 'None';
    public const DISPUTE_STATUS_EXPIRED = 'Expired';
    public const DISPUTE_STATUS_SENT = 'Sent';
    public const DISPUTE_STATUS_RESOLVED = 'Resolved';
    public const DISPUTE_STATUS_FAILED = 'Failed';

    const DISMISS_TRAN_CODE_ARR = [
        Transaction::FLAGED_FOR_CHARGEOFF,
        Transaction::CARD_TRANSFER_DEBIT,
        Transaction::CARD_TRANSFER_CREDIT,
        Transaction::RESERVE_FUNDING_DEPOSIT,
        Transaction::RESERVE_FUNDING_DEPOSIT_REVERSAL,
    ];

    public static function getCreditDebitTypeByCode($code)
    {
        if (in_array($code, [
            self::CODE_MONTHLY_FEE_REVERSAL,
            self::CODE_ACH_PAYROLL_DEPOSIT,
            self::CODE_PURCHASE_RETURN,
            self::CODE_RESERVE_DEPOSIT,
            self::CODE_POS_PURCHASE_RETURN,
            self::CODE_PSKW_CARD_LOAD,
        ], true)) {
            return 'Credit';
        }
        return 'Debit';
    }

    /**
     * @param $tranId
     *
     * @return UserCardTransaction|null
     */
    public static function findByTranId($tranId)
    {
        $rs = Util::em()->getRepository(self::class)
            ->createQueryBuilder('uct')
            ->where('uct.tranId = :tranId')
            ->setParameter('tranId', $tranId)
            ->setMaxResults(1)
            ->getQuery()
            ->getResult();
        if (!$rs) {
            return null;
        }
        return $rs[0];
    }

    /**
     * @param $tranId
     * @param $product
     *
     * @return UserCardTransaction|null
     */
    public static function findByTranIdProduct($tranId, $product)
    {
        $rs = Util::em()->getRepository(self::class)
            ->createQueryBuilder('uct')
            ->where('uct.tranId = :tranId')
            ->andWhere('uct.productName = :product')
            ->setParameter('tranId', (string)$tranId)
            ->setParameter('product', $product)
            ->setMaxResults(1)
            ->getQuery()
            ->getResult();
        if (!$rs) {
            return null;
        }
        return $rs[0];
    }

    public function toApiArray(bool $extra = false): array
    {
        $uc = $this->getUserCard();
        $currency = $uc->getCurrency();
        $merchant = $this->getMerchant();
        $data = [
            'id' => $this->getId(),
            'tranId' => $this->getTranId(),
            'amount' => $this->getTxnAmount(),
            'currency' => $uc->getCurrency(),
            'coin' => Coin::create($this->getTxnAmount(), $currency)->toApiArray(),
            'balance' => Coin::create($this->getAvailableBalance(), $currency)->toApiArray(),
            'time' => $this->getTxnTime() ? $this->getTxnTime()->format('c') : '',
            'merchant' => $merchant ? $merchant->toApiArray(true) : null,
            'type' => $this->getCreditDebitType(),
            'description' => $this->getTranDesc(),
            'order' => $this->getUserCardOrder(),
            'status' => $this->getStatus(),
        ];
        if ($extra) {
            $other = Util::renderData('@Admin/CardTransaction/data.html.twig', [
                'item' => $this,
            ]);
            $data = array_merge($data, $other);
        }

        if ($uc->getCardProgram()->isUsUnlocked()) {
            $data['privacyCardType'] = $uc->getType();

            if ($this->isVoiding()) {
                $data['status'] = 'VOIDING';
                $data['voidedAt'] = Util::formatDateTime($this->getPostTime());
                $data['voidedDates'] = Carbon::now()->diffInDays($this->getPostTime(), false) * -1;
                $data['amount'] = $this->getVoidingAmount();
                $data['txnAmount'] = Money::format($this->getVoidingAmount(), $uc->getCurrency());
            } else if ($this->isReturning()) {
                $data['status'] = 'RETURNING (' . $this->getStatus() . ')';
                $data['returnedAt'] = Util::formatDateTime($this->getPostTime());
                $data['returnedDates'] = Carbon::now()->diffInDays($this->getPostTime(), false) * -1;
            }

            if ($this->isForcePost()) {
                $data['status'] .= ' (Force Post)';
            }

            $data['Dispute Status'] = $this->getDisputeStatus();
        }

        return $data;
    }

    public function getReceiptUrl()
    {
        $receipt = $this->getReceipt();
        if ($receipt) {
            return $receipt->getAssetUrl(true);
        }
        return null;
    }

    public function updateOrder()
    {
        if ($this->getActualTranCode() !== self::CODE_POS_PURCHASE) {
            return;
        }
        $uct = Util::em()->getRepository(\CoreBundle\Entity\UserCardTransaction::class)
            ->createQueryBuilder('uct')
            ->where('uct.userCard = :userCard')
            ->andWhere('uct.actualTranCode = :tranCode')
            ->andWhere('uct.tranId < :tranId')
            ->setParameter('userCard', $this->getUserCard())
            ->setParameter('tranCode', self::CODE_POS_PURCHASE)
            ->setParameter('tranId', $this->getTranId())
            ->setMaxResults(1)
            ->orderBy('uct.tranId', 'desc')
            ->getQuery()
            ->getResult();
        $order = 1;
        if ($uct) {
            /** @var UserCardTransaction $uct */
            $uct = $uct[0];
            $order = $uct->getUserCardOrder() + 1;
        }
        $this->setUserCardOrder($order);
    }

    public function getCreditDebitType() {
        if ($this->getTxnAmount() < 0) {
            return 'Debit';
        }
        $pre = $this->getPreBalance();
        $after = $this->getAfterBalance();
        if ($pre < $after) {
            return 'Credit';
        }
        return 'Debit';
    }

    /**
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\UserCard")
     * @ORM\JoinColumn(name="user_card_id", referencedColumnName="id", onDelete="cascade")
     */
    private $userCard;

    /**
     * @var integer $order
     * @ORM\Column(name="user_card_order", type="integer", nullable=true, options={"comment":"Transaction order of the user card. Start from 1. Only count for POS purchase(00040)"})
     */
    private $userCardOrder; // Transaction order of the user card. Start from 1. Only count for POS purchase(00040)

    /**
     * @var integer $preBalance
     * @ORM\Column(name="pre_balance", type="bigint", nullable=true)
     */
    private $preBalance;//Balance before Transaction

    /**
     * @var integer $txnAmount
     * @ORM\Column(name="txn_amount", type="bigint", nullable=true)
     */
    private $txnAmount;//TranAmount

    /**
     * @var integer $txnAmount
     * @ORM\Column(name="txn_amount_usd", type="bigint", nullable=true)
     */
    private $txnAmountUSD;

    /**
     * @var integer $afterBalance
     * @ORM\Column(name="after_balance", type="bigint", nullable=true)
     */
    private $afterBalance;//Balance After Transaction

    /**
     * @var integer $curBalance
     * @ORM\Column(name="cur_balance", type="bigint", nullable=true)
     */
    private $curBalance;//Current Balance

    /**
     * @var string $outstandingAuths
     * @ORM\Column(name="outstanding_auths", type="string", length=1023, nullable=true)
     */
    private $outstandingAuths;//Outstanding Auths

    /**
     * @var integer $availableBalance
     * @ORM\Column(name="available_balance", type="bigint", nullable=true)
     */
    private $availableBalance;//Available Balance

    /**
     * @var \DateTime $txnTime
     * @ORM\Column(name="txn_time", type="datetime", nullable=true)
     */
    private $txnTime;//Tran Date,Tran Time

    /**
     * @var \DateTime $postTime
     * @ORM\Column(name="post_time", type="datetime", nullable=true)
     */
    private $postTime;//Posttime

    /**
     * @var string $tranCode
     * @ORM\Column(name="tran_code", type="string", nullable=true)
     */
    private $tranCode;//Tran Code

    /**
     * @var string $actualTranCode
     * @ORM\Column(name="actual_tran_code", type="string", nullable=true)
     */
    private $actualTranCode;//Actual Tran Code

    /**
     * @var string $tranId
     * @ORM\Column(name="tran_id", type="string", nullable=true)
     */
    private $tranId;//Tran ID

    /**
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\Merchant")
     * @ORM\JoinColumn(onDelete="SET NULL")
     */
    private $merchant;//MCC,Merchant ID,Merchant Location

    /**
     * @var string $tranDesc
     * @ORM\Column(name="tran_desc", type="string", nullable=true, length=1023)
     */
    private $tranDesc;//Tran Description

    /**
     * @var string $overdraftProtected
     * @ORM\Column(name="overdraft_protected", type="string", nullable=true)
     */
    private $overdraftProtected;//Overdraft Protected

    /**
     * @var integer $overdraftAmount
     * @ORM\Column(name="overdraft_amount", type="bigint", nullable=true)
     */
    private $overdraftAmount;//Overdraft Amount

    /**
     * @var string $productName
     * @ORM\Column(name="product_name", type="string", nullable=true)
     */
    private $productName;//Product Name - could also get from user card

    /**
     * @var string $affId
     * @ORM\Column(name="aff_id", type="string", nullable=true)
     */
    private $affId;//AFFID

    /**
     * @var string $accountStatus
     * @ORM\Column(name="account_status", type="string", nullable=true)
     */
    private $accountStatus;//Account Status

    /**
     * @var string $userField1
     * @ORM\Column(name="user_field_1", type="string", nullable=true)
     */
    private $userField1;//User Field1

    /**
     * @var string $userField2
     * @ORM\Column(name="user_field_2", type="string", nullable=true)
     */
    private $userField2;//User Field2

    /**
     * @var integer
     * @ORM\Column(name="fee", type="integer", nullable=true)
     */
    private $fee;

    /**
     * @var Attachment $receipt
     *
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\Attachment")
     */
    private $receipt;

    /**
     * @var string
     * @ORM\Column(name="meta", type="text", nullable=true)
     */
    private $meta;

    /**
     * @var \Datetime $updatedAt
     *
     * @ORM\Column(type="datetime")
     */
    protected $updatedAt;

    /**
     * @var string
     *
     * @ORM\Column(name="ach_batch_id", type="string", length=255, nullable=true)
     */
    private $achBatchId;

     /**
     * @var string $partner
     * @ORM\Column(name="partner", type="string", length=1024, nullable=true)
     */
    private $partner;


    /**
     * Set preBalance
     *
     * @param integer $preBalance
     *
     * @return UserCardTransaction
     */
    public function setPreBalance($preBalance)
    {
        $this->preBalance = $preBalance;

        return $this;
    }

    /**
     * Get preBalance
     *
     * @return integer
     */
    public function getPreBalance()
    {
        return $this->preBalance;
    }


    public function getPreBalanceWithCurrency()
    {
        return Money::format($this->preBalance,$this->getUserCard()->getCurrency(), false);
    }

    public function getTxnAmountWithCurrency()
    {
        return Money::format($this->txnAmount,$this->getUserCard()->getCurrency(), false);
    }

    public function getAfterBalanceWithCurrency()
    {
        return Money::format($this->afterBalance,$this->getUserCard()->getCurrency(), false);
    }

    public function getCurBalanceWithCurrency()
    {
        return Money::format($this->curBalance,$this->getUserCard()->getCurrency(), false);
    }

    public function getAvailableBalanceWithCurrency()
    {
        return Money::format($this->availableBalance,$this->getUserCard()->getCurrency(), false);
    }

    public function getTranCodeFromDesc()
    {
        $desc = $this->getTranDesc();
        $code = trim(substr($desc,0,strpos($desc,'=')));
        $code = ltrim($code, '0');
        if (!$code) {
            $code = trim($this->getActualTranCode());
        }
        return $code;
    }

    public function getDescWithoutCode() {
        $desc = $this->getTranDesc();
        $index =  strpos($desc,'=');
        if ($index === false) {
            return $desc;
        }
        return trim(substr($desc, $index + 1));
    }

    public static function getDisplayedDescFromFV($tranCode, $merchantId, $tranNumber, $default = '') {
        /** @var FeeItem $fee */
        $fee = Util::em()->getRepository(\CoreBundle\Entity\FeeItem::class)->findOneBy([
            'tranCode' => $tranCode
        ]);
        $intTranCode = (int)$tranCode;
        if (is_null($fee)){
            $fee = Util::em()->getRepository(\CoreBundle\Entity\FeeItem::class)->findOneBy([
                'tranCode' => $intTranCode
            ]);
        }
        /** @var Merchant $merchant */
        /*$merchant = Util::em()->getRepository(\CoreBundle\Entity\Merchant::class)->findOneBy(array(
            'merchantId'              => trim($merchantId),
        ));*/
        $merchant = Util::em()->getRepository(\CoreBundle\Entity\Merchant::class)->findOneBy(array(
            'id'              => trim($merchantId),
        ));
        $desc = '';
        switch ($intTranCode) {
            case Transaction::MONTHLY_FEE:
                $desc = is_null($fee) ? '' : $fee->getFeeGlobalName()->getName();
                break;
            case Transaction::MONTHLY_FEE_REVERSAL:
                $desc = is_null($fee) ? '' : $fee->getFeeGlobalName()->getName();
                break;
            case Transaction::ACH_DEPOSIT:
                $desc = is_null($merchant) ? '' : $merchant->getMerchantNameAndLocation();
                break;
            case Transaction::POS_PURCHASE:
                $desc = is_null($merchant) ? '' : $merchant->getMerchantNameAndLocation();
                break;
            case Transaction::PURCHASE_RETURN:
                $desc = is_null($merchant) ? '' : $merchant->getMerchantNameAndLocation();
                break;
            case Transaction::FLAGED_FOR_CHARGEOFF:
                $desc = '';
                break;
            case Transaction::CARD_TRANSFER_DEBIT:
                $desc = '';
                break;
            case Transaction::CARD_TRANSFER_CREDIT:
                $desc = 'Card to Card Transfer Credit';
                break;
            case Transaction::DECLINED_TRANSACTION_FEE:
                $desc = 'Declined Transaction Fee';
                if ($merchant) {
                    $desc .= ' (' . $merchant->getMerchantCustName() . ')';
                }
                break;
            case Transaction::DECLINED_TRANSACTION_FEE_REFUND:
                $desc = 'Refund Declined Transaction Fee';
                if ($merchant) {
                    $desc .= ' (' . $merchant->getMerchantCustName() . ')';
                }
                break;
            case Transaction::POS_PURCHASE_FEE;
                $desc = 'Purchase Fee'
                    . (is_null($merchant) ? '' : ' (' .$merchant->getMerchantCustName() . ')');
                break;
            case Transaction::POS_PURCHASE_INTERNATIONAL:
                $desc = is_null($merchant) ? '' :
                    'Purchase Fee International' . ' (' .$merchant->getMerchantCustName() . ')';
                break;
            case Transaction::RESERVE_FUNDING_DEPOSIT:
                $desc = '';
                break;
            case Transaction::RESERVE_FUNDING_DEPOSIT_REVERSAL:
                $desc = '';
                break;
            case Transaction::ACH_DEBIT:
                $desc = is_null($merchant) ? '' : $merchant->getMerchantNameAndLocation();
                break;
            case Transaction::POS_PURCHASE_FEE_ZERO:
                $desc = is_null($merchant) ? '' : $merchant->getMerchantNameAndLocation();
                break;
            case Transaction::RESERVE_DEPOSIT:
                $desc       = 'Load/Top up transaction';
                $no = UserCardLoad::getTransactionNoFromFv($tranNumber);
                if ($no) {
                    $desc .= ' ID ' . $no;
                }
                break;
            case Transaction::RESERVE_DEPOSIT_REVERSAL:
                $desc = 'Card Balance Unloaded';
                break;
            case Transaction::DEPOSIT:
                $desc = 'Deposit';
                break;
            case Transaction::DEPOSIT_REVERSAL:
                $desc = 'Deposit Reversal';
                break;
        }
        if (!$desc) {
            return $default;
        }
        return $desc;
    }

    public function getDisplayedDesc()
    {
        $tranCode = $this->getTranCodeFromDesc();
        $merchant = $this->getMerchant();
        $merchantId = is_null($merchant) ? null : $merchant->getId();
        return self::getDisplayedDescFromFV($tranCode, $merchantId, $this->getTranId(), $this->getTranDesc());
    }

     /**
     * @param $id
     *
     * @return static|null
     */
    public static function find($id)
    {
        return Util::em()->getRepository(static::class)->find($id);
    }

    /**
     * Get txnAmountUSD
     *
     * @return integer
     */
    public function getTxnAmountUSD()
    {
        $usd = $this->txnAmountUSD;
        if ($usd === null) {
            $amount = $this->getTxnAmount();
            if ($amount) {
                $this->setTxnAmount($amount);
                $usd = $this->txnAmountUSD;
            }
        }
        return $usd;
    }

    /**
     * Set txnAmount
     *
     * @param integer $txnAmount
     *
     * @return UserCardTransaction
     */
    public function setTxnAmount($txnAmount)
    {
        $this->txnAmount = $txnAmount;

        $uc = $this->getUserCard();
        if ($uc) {
            $this->setTxnAmountUSD(Money::convert($txnAmount, $uc->getCurrency(), 'USD'));
        }

        return $this;
    }

    /**
     * Get txnAmount
     *
     * @return integer
     */
    public function getTxnAmount()
    {
        return $this->txnAmount;
    }

    /**
     * Set afterBalance
     *
     * @param integer $afterBalance
     *
     * @return UserCardTransaction
     */
    public function setAfterBalance($afterBalance)
    {
        $this->afterBalance = $afterBalance;

        return $this;
    }

    /**
     * Get afterBalance
     *
     * @return integer
     */
    public function getAfterBalance()
    {
        return $this->afterBalance;
    }

    /**
     * Set curBalance
     *
     * @param integer $curBalance
     *
     * @return UserCardTransaction
     */
    public function setCurBalance($curBalance)
    {
        $this->curBalance = $curBalance;

        return $this;
    }

    /**
     * Get curBalance
     *
     * @return integer
     */
    public function getCurBalance()
    {
        return $this->curBalance;
    }

    /**
     * Set outstandingAuths
     *
     * @param string $outstandingAuths
     *
     * @return UserCardTransaction
     */
    public function setOutstandingAuths($outstandingAuths)
    {
        $this->outstandingAuths = $outstandingAuths;

        return $this;
    }

    /**
     * Get outstandingAuths
     *
     * @return string
     */
    public function getOutstandingAuths()
    {
        return $this->outstandingAuths;
    }

    /**
     * Set availableBalance
     *
     * @param integer $availableBalance
     *
     * @return UserCardTransaction
     */
    public function setAvailableBalance($availableBalance)
    {
        $this->availableBalance = $availableBalance;

        return $this;
    }

    /**
     * Get availableBalance
     *
     * @return integer
     */
    public function getAvailableBalance()
    {
        return $this->availableBalance;
    }

    /**
     * Set txnTime
     *
     * @param \DateTime $txnTime
     *
     * @return UserCardTransaction
     */
    public function setTxnTime($txnTime)
    {
        $this->txnTime = $txnTime;

        return $this;
    }

    /**
     * Get txnTime
     *
     * @return \DateTime
     */
    public function getTxnTime()
    {
        return $this->txnTime;
    }

    /**
     * Set postTime
     *
     * @param \DateTime $postTime
     *
     * @return UserCardTransaction
     */
    public function setPostTime($postTime)
    {
        $this->postTime = $postTime;

        return $this;
    }

    /**
     * Get postTime
     *
     * @return \DateTime
     */
    public function getPostTime()
    {
        return $this->postTime;
    }

    /**
     * Set tranCode
     *
     * @param string $tranCode
     *
     * @return UserCardTransaction
     */
    public function setTranCode($tranCode)
    {
        $this->tranCode = $tranCode;

        return $this;
    }

    /**
     * Get tranCode
     *
     * @return string
     */
    public function getTranCode()
    {
        return $this->tranCode;
    }

    /**
     * Set actualTranCode
     *
     * @param string $actualTranCode
     *
     * @return UserCardTransaction
     */
    public function setActualTranCode($actualTranCode)
    {
        $this->actualTranCode = $actualTranCode;

        return $this;
    }

    /**
     * Get actualTranCode
     *
     * @param bool $parsed
     * @return string|null
     */
    public function getActualTranCode($parsed = false)
    {
        $code = $this->actualTranCode;
        if ($parsed) {
            $code = ltrim($code, '0');
        }
        return $code;
    }

    /**
     * Set tranId
     *
     * @param string $tranId
     *
     * @return UserCardTransaction
     */
    public function setTranId($tranId)
    {
        $this->tranId = $tranId;

        return $this;
    }

    /**
     * Get tranId
     *
     * @return string
     */
    public function getTranId()
    {
        return $this->tranId;
    }

    /**
     * Set tranDesc
     *
     * @param string $tranDesc
     *
     * @return UserCardTransaction
     */
    public function setTranDesc($tranDesc)
    {
        $this->tranDesc = $tranDesc;

        return $this;
    }

    /**
     * Get tranDesc
     *
     * @return string
     */
    public function getTranDesc()
    {
        return $this->tranDesc;
    }

    /**
     * Set overdraftProtected
     *
     * @param string $overdraftProtected
     *
     * @return UserCardTransaction
     */
    public function setOverdraftProtected($overdraftProtected)
    {
        $this->overdraftProtected = $overdraftProtected;

        return $this;
    }

    /**
     * Get overdraftProtected
     *
     * @return string
     */
    public function getOverdraftProtected()
    {
        return $this->overdraftProtected;
    }

    /**
     * Set overdraftAmount
     *
     * @param integer $overdraftAmount
     *
     * @return UserCardTransaction
     */
    public function setOverdraftAmount($overdraftAmount)
    {
        $this->overdraftAmount = $overdraftAmount;

        return $this;
    }

    /**
     * Get overdraftAmount
     *
     * @return integer
     */
    public function getOverdraftAmount()
    {
        return $this->overdraftAmount;
    }

    /**
     * Set productName
     *
     * @param string $productName
     *
     * @return UserCardTransaction
     */
    public function setProductName($productName)
    {
        $this->productName = $productName;

        return $this;
    }

    /**
     * Get productName
     *
     * @return string
     */
    public function getProductName()
    {
        return $this->productName;
    }

    /**
     * Set affId
     *
     * @param string $affId
     *
     * @return UserCardTransaction
     */
    public function setAffId($affId)
    {
        $this->affId = $affId;

        return $this;
    }

    /**
     * Get affId
     *
     * @return string
     */
    public function getAffId()
    {
        return $this->affId;
    }

    /**
     * Set accountStatus
     *
     * @param string $accountStatus
     *
     * @return UserCardTransaction
     */
    public function setAccountStatus($accountStatus)
    {
        $this->accountStatus = $accountStatus;

        return $this;
    }

    /**
     * Get accountStatus
     *
     * @return string
     */
    public function getAccountStatus()
    {
        return $this->accountStatus;
    }

    /**
     * Set userField1
     *
     * @param string $userField1
     *
     * @return UserCardTransaction
     */
    public function setUserField1($userField1)
    {
        $this->userField1 = $userField1;

        return $this;
    }

    /**
     * Get userField1
     *
     * @return string
     */
    public function getUserField1()
    {
        return $this->userField1;
    }

    /**
     * Set userField2
     *
     * @param string $userField2
     *
     * @return UserCardTransaction
     */
    public function setUserField2($userField2)
    {
        $this->userField2 = $userField2;

        return $this;
    }

    /**
     * Get userField2
     *
     * @return string
     */
    public function getUserField2()
    {
        return $this->userField2;
    }

    /**
     * Set userCard
     *
     * @param \CoreBundle\Entity\UserCard $userCard
     *
     * @return UserCardTransaction
     */
    public function setUserCard(\CoreBundle\Entity\UserCard $userCard = null)
    {
        $this->userCard = $userCard;
        return $this;
    }

    /**
     * Get userCard
     *
     * @return \CoreBundle\Entity\UserCard
     */
    public function getUserCard()
    {
        return $this->userCard;
    }

    /**
     * Set merchant
     *
     * @param \CoreBundle\Entity\Merchant $merchant
     *
     * @return UserCardTransaction
     */
    public function setMerchant(\CoreBundle\Entity\Merchant $merchant = null)
    {
        $this->merchant = $merchant;

        return $this;
    }

    /**
     * Get merchant
     *
     * @return \CoreBundle\Entity\Merchant
     */
    public function getMerchant()
    {
        return $this->merchant;
    }

    /**
     * Set txnAmountUSD
     *
     * @param integer $txnAmountUSD
     *
     * @return UserCardTransaction
     */
    public function setTxnAmountUSD($txnAmountUSD)
    {
        $this->txnAmountUSD = $txnAmountUSD;

        return $this;
    }

    /**
     * Set userCardOrder
     *
     * @param integer $userCardOrder
     *
     * @return UserCardTransaction
     */
    public function setUserCardOrder($userCardOrder)
    {
        $this->userCardOrder = $userCardOrder;

        return $this;
    }

    /**
     * Get userCardOrder
     *
     * @return integer
     */
    public function getUserCardOrder()
    {
        return $this->userCardOrder;
    }

    /**
     * Set receipt
     *
     * @param \CoreBundle\Entity\Attachment $receipt
     *
     * @return UserExpense
     */
    public function setReceipt(\CoreBundle\Entity\Attachment $receipt = null)
    {
        $this->receipt = $receipt;

        return $this;
    }

    /**
     * Get receipt
     *
     * @return \CoreBundle\Entity\Attachment
     */
    public function getReceipt()
    {
        return $this->receipt;
    }

    /**
     * Set meta
     *
     * @param string $meta
     *
     * @return UserCardTransaction
     */
    public function setMeta($meta)
    {
        $this->meta = $meta;

        return $this;
    }

    /**
     * Get meta
     *
     * @return string
     */
    public function getMeta()
    {
        return $this->meta;
    }

    /**
     * Set fee
     *
     * @param integer $fee
     *
     * @return UserCardTransaction
     */
    public function setFee($fee)
    {
        $this->fee = $fee;

        return $this;
    }

    /**
     * Get fee
     *
     * @return integer
     */
    public function getFee()
    {
        return $this->fee;
    }

    public function getAchBatchId(): ?string
    {
        return $this->achBatchId;
    }

    public function setAchBatchId(?string $achBatchId): static
    {
        $this->achBatchId = $achBatchId;

        return $this;
    }

    public function getPartner(): ?string
    {
        return $this->partner;
    }

    public function setPartner(?string $partner): static
    {
        $this->partner = $partner;

        return $this;
    }
}
