<?php
/**
 * User: Bob
 * Date: 2017/2/21
 * Time: 12:08
 * This is used to record Fee Category information
 */

namespace CoreBundle\Entity;

use CoreBundle\Utils\Util;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\Mapping as ORM;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;
use Symfony\Component\Validator\Constraints as Assert;

/**
 * Class FeeCategory
 * @ORM\Entity(repositoryClass="CoreBundle\Repository\FeeCategoryRepository")
 * @ORM\Table(name="fee_category")
 * @package CoreBundle\Entity
 */
class FeeCategory extends BaseEntity
{
    public function getTenantTypesArray()
    {
        $a = $this->getTenantTypes();
        if (!$a) {
            return [];
        }
        return Util::s2j($a);
    }

    /**
     * @var string $name
     *
     * @ORM\Column(name="name", type="string")
     * @Assert\NotBlank()
     */
    private $name;

    /**
     * @var string $tenantTypes
     *
     * @ORM\Column(name="tenant_types", type="string", nullable=true)
     */
    private $tenantTypes;

    /**
     * @var ArrayCollection
     * @ORM\OneToMany(targetEntity="CoreBundle\Entity\FeeGlobalName", mappedBy="feeCategory")
     * @Serializer\Exclude()
     */
    private $globals;

    /**
     * Set name
     *
     * @param string $name
     *
     * @return FeeCategory
     */
    public function setName($name)
    {
        $this->name = $name;

        return $this;
    }

    /**
     * Get name
     *
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->globals = new \Doctrine\Common\Collections\ArrayCollection();
    }

    /**
     * Add global
     *
     * @param \CoreBundle\Entity\FeeGlobalName $global
     *
     * @return FeeCategory
     */
    public function addGlobal(\CoreBundle\Entity\FeeGlobalName $global)
    {
        $this->globals[] = $global;

        return $this;
    }

    /**
     * Remove global
     *
     * @param \CoreBundle\Entity\FeeGlobalName $global
     */
    public function removeGlobal(\CoreBundle\Entity\FeeGlobalName $global)
    {
        $this->globals->removeElement($global);
    }

    /**
     * Get globals
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getGlobals()
    {
        return $this->globals;
    }

    /**
     * Set tenantTypes
     *
     * @param string $tenantTypes
     *
     * @return FeeCategory
     */
    public function setTenantTypes($tenantTypes)
    {
        $this->tenantTypes = $tenantTypes;

        return $this;
    }

    /**
     * Get tenantTypes
     *
     * @return string
     */
    public function getTenantTypes()
    {
        return $this->tenantTypes;
    }
}
