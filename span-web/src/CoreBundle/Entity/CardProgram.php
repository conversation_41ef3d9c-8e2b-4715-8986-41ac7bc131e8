<?php
/**
 * User: Bob
 * Date: 2017/3/1
 * Time: 13:59
 * This is used to record card program information
 */

namespace CoreBundle\Entity;

use ApiBundle\Entity\ApiEntityInterface;
use CoreBundle\Entity\Traits\NamedEntityTrait;
use CoreBundle\Utils\Bundle;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Image;
use CoreBundle\Utils\Util;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\ORM\Mapping\Index;
use Doctrine\ORM\Mapping\ManyToMany;
use Gedmo\Mapping\Annotation as Gedmo;
use Ged<PERSON>\SoftDeleteable\Traits\SoftDeleteableEntity;
use JMS\Serializer\Annotation\Exclude;

/**
 * Class CardProgram
 * @ORM\Entity(repositoryClass="CoreBundle\Repository\CardProgramRepository")
 * @ORM\Table(name="card_program", indexes={
 *     @Index(name="name_index", columns={"name"}),
 * })
 * @package CoreBundle\Entity
 * @Gedmo\SoftDeleteable()
 */
class CardProgram extends BaseEntity implements ApiEntityInterface
{
    use NamedEntityTrait;

    const STATUS_TEST_MODE = 'Test Mode';
    const STATUS_PAUSED = 'Paused';
    const STATUS_SUSPENDED = 'Suspended';
    const STATUS_LIVE = 'Live';

    const NAME_US_UNLOCKED = 'US Unlocked';
    const NAME_US_UNLOCKED_LEGACY = 'US Unlocked (Legacy)';
    const NAME_UTC = 'Universal Transaction Compliance';
    const NAME_FIS = 'FIS';
    const NAME_ES_SOLO_TEST = 'ES SOLO UBA/GTP TEST';
    const NAME_LEAFLINK = 'LeafLink';
    const NAME_TRANSFER_MEX_USD = 'TransferMex (USD)';
    const NAME_CASH_ON_WEB = 'Cash On Web';
    const NAME_WILEN = 'Wilen';
    const NAME_PTO = 'PTO';
    const NAME_FAAS = 'Fintech as a Service';
	const NAME_RED_CARD_ATHLETICS = 'Red Card Athletics';
	const NAME_D2 = 'D2Rx';
	const NAME_AMERICA_VOICE = 'America Voice';
	const NAME_IQSTEL = 'iQSTEL';
    const NAME_SKUX = 'SKUx';

    const NAME_SPENDR = 'Spendr';

    const DELIVERY_METHOD_HAND = 'hand';
    const DELIVERY_METHOD_MAIL_FROM_DC = 'mail'; // From distribution center
    const DELIVERY_METHOD_MAIL_FROM_PRINTER = 'mail_from_printer';

    const DEFAULT_BUNDLE     = 'PortalBundle';
    const DEFAULT_LOGO       = '/bundles/app/images/logo.png';
    const DEFAULT_ICON       = '/static/img/tern_logo_new_blue_square.png';
    const DEFAULT_ICON_TOUCH = '/static/img/tern_logo_white_new_blue.png';

    const RESERVED_DOMAINS = ['www', 'developer', 'dev', 'staging', 'test', 'qa', 'prod', 'live'];

    const CUSTOMIZES = [
        'font' => 'Lato',
        'linkColor' => '#428bca',
        'linkActiveColor' => '#e12e2f',
        'buttonColor' => '#428bca',
        'buttonActiveColor' => '#e12e2f',
        'adminPrimaryColor' => '#308fd0',
        'adminPrimaryPositiveColor' => '#35cd68',
        'adminPrimaryNegativeColor' => '#ff6501',
        'adminSecondaryColor' => '#116CAB',
        'adminSecondaryPositiveColor' => '#ACEAC1',
        'adminSecondaryNegativeColor' => '#FCB62B',
    ];

    const WIDGET_NAME_HIDE = 0;
    const WIDGET_NAME_SHOW = 1;

    const CHART_TYPE_LINE = 1;
    const CHART_TYPE_BAR = 2;
    const CHART_TYPE_SUMMARY = 3;

    const X_AXIS_WEEK = 1;
    const X_AXIS_QUARTER = 2;
    const X_AXIS_YEAR = 3;
    const X_AXIS_LAST_MONTH = 4;
    const X_AXIS_LASE_QUARTER = 5;
    const X_AXIS_LAST_YEAR = 6;

    const Y_AXIS_ALL = 0;
    const Y_AXIS_FIRST = 1;
    const Y_AXIS_RELOAD = 2;

    /**
     * @param $name
     *
     * @return CardProgram
     */
    public static function find($name) {
        $cps = Util::em()->getRepository(\CoreBundle\Entity\CardProgram::class)->findBy([
            'name' => $name,
        ]);
        if ($cps) {
            return $cps[0];
        }
        return null;
    }

    public static function get($name) {
        return static::find($name);
    }

    /**
     * @return CardProgram
     */
    public static function usunlocked()
    {
        return self::cached(self::NAME_US_UNLOCKED);
    }

    /**
     * @return CardProgram
     */
    public static function usunlockedLegacy()
    {
        return self::cached(self::NAME_US_UNLOCKED_LEGACY);
    }

    /**
     * @return CardProgram
     */
    public static function fis()
    {
        return self::cached(self::NAME_FIS);
    }

    /**
     * @return CardProgram
     */
    public static function leafLink()
    {
        return self::cached(self::NAME_LEAFLINK);
    }

    /**
     * @return CardProgram
     */
    public static function wilen()
    {
        return self::cached(self::NAME_WILEN);
    }

    /**
     * @return CardProgram
     */
    public static function transferMexUSD()
    {
        return self::cached(self::NAME_TRANSFER_MEX_USD);
    }

    /**
     * @return CardProgram
     */
    public static function skux()
    {
        return self::cached(self::NAME_SKUX);
    }

    /**
     * @return CardProgram
     */
    public static function cashOnWeb()
    {
        return self::cached(self::NAME_CASH_ON_WEB);
    }

	/**
	 * @return CardProgram
	 */
	public static function spendr()
	{
		return self::cached(self::NAME_SPENDR);
	}

	public static function faas()
	{
		return Data::callback(__METHOD__, null, function () {
			return self::find(self::NAME_FAAS);
		});
	}

	/**
	 * @return CardProgram
	 */
	public static function redCard()
	{
		return Data::callback(__METHOD__, null, function () {
			return self::find(self::NAME_RED_CARD_ATHLETICS);
		});
	}

	/**
	 * @return CardProgram
	 */
	public static function d2()
	{
		return Data::callback(__METHOD__, null, function () {
			return self::find(self::NAME_D2);
		});
	}

	/**
	 * @return CardProgram
	 */
	public static function americaVoice()
	{
		return Data::callback(__METHOD__, null, function () {
			return self::find(self::NAME_AMERICA_VOICE);
		});
	}

	/**
	 * @return CardProgram
	 */
	public static function IQStel()
	{
		return Data::callback(__METHOD__, null, function () {
			return self::find(self::NAME_IQSTEL);
		});
	}

    public static function findByKycProviderProgramId($id) {
        $repo = Util::em()->getRepository(static::class);
        $rs = $repo->findBy([
            'kycProviderProgram' => $id,
        ]);
        if ($rs) {
            return $rs[0];
        }
        return null;
    }

    public function requireShipping() {
        return $this->getDeliveryMethod() !== static::DELIVERY_METHOD_HAND;
    }

    public function isInstantIssue() {
        return in_array($this->getDeliveryMethod(), [
            static::DELIVERY_METHOD_HAND,
            static::DELIVERY_METHOD_MAIL_FROM_DC,
        ]);
    }

    public function is($name) {
        return $this->getName() === $name;
    }

    public function isLive() {
        return $this->getStatus() === self::STATUS_LIVE;
    }

    public function isSuspended() {
        return $this->getStatus() === self::STATUS_SUSPENDED;
    }

    public function isPaused() {
        return $this->getStatus() === self::STATUS_PAUSED;
    }

    public function getPausedError() {
        if ($this->isLive()) {
            return '';
        }
        return $this->isSuspended() ? 'Temporarily suspended card program access!' : 'The card program is not in live status!';
    }

    public function isTest() {
        return false;
//        $name = $this->getName();
//        return Util::endsWith($name, ' Test') || Util::endsWith($name, ' <TEST>');
    }

    public function supportDDA() {
        $v = Util::json($this, 'meta', 'supportDDA');
        return $v === true || $v === 'true';
    }

    public function supportExpense() {
        $v = Util::json($this, 'meta', 'supportExpense');
        return $v === true || $v === 'true';
    }

    public function getFixedName()
    {
        return $this->getName();
    }

    public function getLanguages()
    {
        return Util::json($this, 'meta', 'languages') ?: ['en'];
    }

    public function getSupportedLanguages()
    {
        $codes = $this->getLanguages();
        $all = Config::array(Config::CONFIG_LANGUAGES);
        return array_filter($all, function ($item) use ($codes) {
            if (isset($item['disabled']) && $item['disabled']) {
                return false;
            }
            return in_array($item['code'], $codes, true);
        });
    }

    public function getDeliveryMethodName()
    {
        $names = [
            'hand' => 'Hand to customer directly',
            'mail' => 'Mail to customer from distribution center',
            'mail_from_printer' => 'Mail to customer from card printer/manufacturer',
        ];
        $value = $this->getDeliveryMethod();
        return $names[$value] ?? $value;
    }

    public function toApiArray(bool $extra = false): array
    {
        return [
            'id' => $this->getId(),
            'name' => $this->getName(),
            'supportDDA' => $this->supportDDA(),
            'supportExpense' => $this->supportExpense(),
            'cardsAllowed' => $this->getCardsAllowed(),
            'passwordExpiry' => $this->isPasswordExpiryOn(),
            'bin' => $this->getBin(),
            'deliveryMethod' => $this->getDeliveryMethod(),
            'kycProviderProgram' => $this->getKycProviderProgram(),
        ];
    }

    public function toMinApiArray(): array
    {
        return [
            'id' => $this->getId(),
            'name' => $this->getName(),
            'is_cashOnWeb' => $this->isCashOnWeb(),
            'is_locator' =>$this->getPlatform()->getLocator(),
            'locatorUrl' => $this->getPlatform()->getLocatorUrl(),
            'is_forwarding' =>  $this->getPlatform()->getIsPartner() ? $this->getPlatform()->getForwarding() : false,
            'cuscolor' => $this->getPlatform()->getCuscolor(),
            'oneCardArt' => $this->isCashOnWeb() ? $this->getPlatform()->getUrl('oneCard') : '/static/usu/img/usu_white.png',
            'stepOne' => $this->getPlatform()->getUrl('stepOne'),
            'stepThree' => $this->getPlatform()->getUrl('stepThree'),
            'useInfo' => $this->getPlatform()->getUrl('useInfo'),
            'tranIcon' => $this->getPlatform()->getUrl('tranIcon'),
            'logo' => $this->isCashOnWeb() ? $this->getPlatform()->getLogoUrl() : ''
        ];
    }

    public function isServiceEnabled($service)
    {
        // Disable the email and SMS notifications for Es Solo's APIs.
        if ($this->isEsSolo() && Util::isAPI()) {
            return false;
        }

        /** @var CardProgramService $cpService */
        foreach ($this->getCardProgramService() as $cpService) {
            if ($cpService->getService()->getName() === $service) {
                return true;
            }
        }
        return false;
    }

    public function isVelocityOn()
    {
        if (!Velocity::isOn()) {
            return false;
        }
        $on = Util::json($this, 'meta', 'velocitySwitch');
        if ($on === null) {
            $on = true;
        }
        return $on;
    }

    public function needUnloadCardWhenDeactivated()
    {
        if ($this->isEsSolo()) {
            return true;
        }
        return false;
    }

    public function isPasswordExpiryOn()
    {
        $on = Util::json($this, 'meta', 'passwordExpiry');
        if ($on === null) {
            $on = false;
        }
        return $on === true || $on === 'true';
    }

    /**
     * Set icon
     *
     * @param \CoreBundle\Entity\Attachment $icon
     *
     * @return CardProgram
     */
    public function setIcon(\CoreBundle\Entity\Attachment $icon = null)
    {
        if ($icon) {
            $path = Image::fillPNGBackground($icon->prepareForLocalRead());
            Util::updateJson($this, 'meta', [
                'iconTouch' => Util::fixFilePath($path),
            ], false);
        }

        return $this;
    }

    /**
     * Get countries
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getCountries()
    {
        if (Bundle::isEsSolo()) {
            // query all countries
            $countries = Util::em()->getRepository(Country::class)->findAll();
            return new ArrayCollection($countries);
        }
        return $this->countries;
    }

    public function getEmailValue($key, $default = null)
    {
        $method = 'get' . ucfirst($key);
        if (!method_exists($this, $method)) {
            return $default;
        }
        $self = $this->$method();
        if ($self) {
            return $self;
        }
        $platform = $this->getPlatform();
        if (!$platform) {
            return $default;
        }
        if (!method_exists($platform, $method)) {
            return $default;
        }
        return $platform->$method() ?: $default;
    }

    public function isUsUnlocked()
    {
        return $this->getName() === self::NAME_US_UNLOCKED;
    }

    public function isUsUnlockedLegacy()
    {
        return $this->getName() === self::NAME_US_UNLOCKED_LEGACY;
    }

    public function isCashOnWeb()
    {
        return $this->getName() === self::NAME_CASH_ON_WEB;
    }

    public function isSpendr()
    {
        return $this->getName() === self::NAME_SPENDR;
    }


    public function isUTC()
    {
        $platform = $this->getPlatform();
        return $platform && $platform->is(Platform::NAME_UTC);
    }

    public function isEsSolo()
    {
        $platform = $this->getPlatform();
        return $platform && $platform->is(Platform::NAME_ES_SOLO);
    }

    public function isEsSoloUba()
    {
        return $this->isEsSolo() && Util::field($this->getIssuingBank()) === IssuingBank::NAME_UBA;
    }

    public function isEsSoloGtBank()
    {
        return $this->isEsSolo() && Util::field($this->getIssuingBank()) === IssuingBank::NAME_GT;
    }

    public function isFIS()
    {
        $platform = $this->getPlatform();
        return $platform && $platform->is(Platform::NAME_FIS);
    }

    public function isTransferMex()
    {
        $platform = $this->getPlatform();
        return $platform && $platform->is(Platform::NAME_TRANSFER_MEX);
    }

    public function isLeafLink()
    {
        return $this->getName() === self::NAME_LEAFLINK;
    }

    public function isPTO()
    {
        $platform = $this->getPlatform();
        return $platform && $platform->is(Platform::NAME_PTO_GENIUS);
    }

    public function isWilen()
    {
        $platform = $this->getPlatform();
        return $platform && $platform->is(Platform::NAME_WILEN);
    }

    public function getHelpUrl()
    {
        if ($this->isUsUnlocked()) {
            return 'https://www.usunlocked.com/help/';
        }
        if ($this->isCashOnWeb()) {
          return 'https://cashonweb.net/';
        }
        return '';
    }

    public function getProductTeam()
    {
        return $this->getProductName() ?: $this->getName();
    }

    //Step 1 Set Program Name
    /**
     * @var string $name
     *
     * @ORM\Column(name="name", type="string", length=255, unique=true)
     */
    private $name; //unique name

    //Step 2 Brand Tenant
    /**
     * @ORM\ManyToOne(targetEntity="BrandPartner")
     */
    private $brandPartner;//brand tenant ManyToOne

    /**
     * @ORM\Column(name="bin", type="string", length=255, nullable=true)
     */
    private $bin;

    /**
     * @ORM\Column(name="sub_bin", type="string", length=255, nullable=true, options={"comment":"2 degits. can be null"})
     */
    private $subBin;

    /**
     * @ORM\Column(name="currency", type="string", length=255, nullable=true, options={"comment":"Currency code like: USD, EUR, GBP"})
     */
    private $currency;

    /**
     * @ORM\Column(name="delivery_method", type="string", length=255, options={"default"="mail"})
     */
    private $deliveryMethod;

    /**
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\Platform", inversedBy="cardPrograms")
     */
    private $platform;

    /**
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\KycProvider")
     */
    private $kycProvider;

    /**
     * @ORM\Column(name="kyc_provider_program", type="string", nullable=true, options={"comment":"Program ID of the selected KYC Provider"})
     */
    private $kycProviderProgram;

    /**
     * @ORM\Column(name="wallet", type="string", nullable=true, options={"comment":"Sub Wallet/Company used in Es Solo first"})
     */
    private $wallet;

    /**
     * @ORM\Column(name="wallet_api_key", type="string", nullable=true, options={"comment":"Sub Wallet/Company API key used in Es Solo first"})
     */
    private $walletApiKey;

    /**
     * @ORM\Column(name="package_id", type="string", nullable=true, options={"comment":"Required when the issuing bank is GT Bank"})
     */
    private $packageId;

    /**
     * @ORM\Column(name="on_demand_printing", type="boolean", nullable=true)
     */
    private $onDemandPrinting;

    /**
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\CardPrinter")
     */
    private $cardPrinter;

    /**
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\IssuingBank")
     */
    private $issuingBank;

    /**
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\Processor")
     */
    private $processor;

    /**
     * @ORM\ManyToOne(targetEntity="SalexUserBundle\Entity\User")
     */
    private $programOwner;

    /**
     * @var boolean $isCoBrand
     *
     * @ORM\Column(name="is_co_brand", type="boolean", nullable=true, options={"comment":"If Yes need to Select Marketing Partner Tenant"})
     */
    private $isCoBrand;//Co-brand, If Yes need to Select Marketing Partner Tenant

    /**
     * @ORM\ManyToOne(targetEntity="MarketingPartner")
     */
    private $marketingPartner;// ManyToOne

    //Step 3 Portal Use
    //Currently combine with Step 2
    /**
     * @var boolean $isPortalUse
     *
     * @ORM\Column(name="is_portal_use", type="boolean", nullable=true)
     */
    private $isPortalUse;

    //Begin Add by Bob Wen Bao on 2017-04-17 to move cards allowed from card type level to card program level
    /**
     * @var integer $cardsAllowed
     * @ORM\Column(name="cards_allowed", type="integer", nullable=true)
     */
    private $cardsAllowed;
    //End

    //Step 4 Card Type(s)
    /**
     * @ORM\OneToMany(targetEntity="CardProgramCardType", mappedBy="cardProgram", cascade={"all"} , orphanRemoval=true)
     */
    private $cardProgramCardType;

    private $cardType;// OneToMany

    //Step 6 Billing Address options
    /**
     * @ORM\OneToMany(targetEntity="CardProgramReshipper", mappedBy="cardProgram", cascade={"all"} , orphanRemoval=true)
     */
    private $cardProgramReshipper;

    private $reshipper; //OneToMany

    /**
     * @var boolean $hasCustomerAddress
     * @ORM\Column(name="has_cust_address", type="boolean", nullable=true, options={"comment":"Support customer address or not"})
     */
    private $hasCustomerAddress;//Support customer address or not

    //Step 7 Country Management
    /**
     * @ManyToMany(targetEntity="CoreBundle\Entity\Country", inversedBy="cardPrograms")
     * @ORM\OrderBy({"name" = "ASC"})
     * @Exclude
     */
    private $countries;

    //Step 8 Fee Schedule
    /**
     * @ORM\OneToMany(targetEntity="CardProgramFeeItem", mappedBy="cardProgram", cascade={"all"} , orphanRemoval=true)
     */
    private $cardProgramFeeItem;

    /**
     * @ORM\OneToMany(targetEntity="CardProgramFeeRevenueShare", mappedBy="cardProgram", cascade={"all"} , orphanRemoval=true)
     */
    private $cardProgramFeeRevenueShare;

    private $feeItem; //OneToMany

    //Step 9 Add-ons
    /**
     * @ORM\OneToMany(targetEntity="CardProgramService", mappedBy="cardProgram", cascade={"all"} , orphanRemoval=true)
     */
    private $cardProgramService;

    private $service;

    //Common properties
    /**
     * @var string $status
     *
     * @ORM\Column(name="status", type="string", nullable=true, options={"comment":"Test Mode/ Paused / Suspended / Live"})
     */
    private $status;// Status as Test Mode/ Paused / Live

    /**
     * @var string $support_email_value
     *
     * @ORM\Column(name="support_email_value", type="string" ,nullable=true, options={"comment":"Support email value in all email templates."})
     */
    private $support_email_value;

    /**
     * @var string $live_chat_url_value
     *
     * @ORM\Column(name="live_chat_url_value", type="string",nullable=true, options={"comment":"Live chat url value in all email templates. So are the fields: sender_name, product_name, company_name, html_logo"})
     */
    private $live_chat_url_value;

    /**
     * @var string $sender_name
     *
     * @ORM\Column(name="sender_name", type="string",nullable=true)
     */
    private $sender_name;

    /**
     * @var string $product_name
     *
     * @ORM\Column(name="product_name", type="string",nullable=true)
     */
    private $product_name;

    /**
     * @var string $company_name
     *
     * @ORM\Column(name="company_name", type="string",nullable=true)
     */
    private $company_name;

    /**
     * @var string $htmlLogo Logo Html
     *
     * @ORM\Column(name="html_logo", type="text", nullable=true)
     */
    private $htmlLogo;

    /**
     * @var string $html_address Address Html
     *
     * @ORM\Column(name="html_address", type="text", nullable=true)
     */
    private $html_address;

    /**
     * @var string $text_address Address Text
     *
     * @ORM\Column(name="text_address", type="text", nullable=true)
     */
    private $text_address;

    /**
     * @var string
     *
     * @ORM\Column(name="languagetype", type="string",nullable=true)
     */
    private $languagetype;

    /**
     * @var string
     *
     * @ORM\Column(name="meta", type="text", nullable=true)
     */
    private $meta;

    /**
     * @var Collection $velocities
     * @ORM\OneToMany(targetEntity="CoreBundle\Entity\Velocity", mappedBy="cardProgram")
     */
    private $velocities;

    /**
     * @var Collection $velocities
     * @ORM\OneToMany(targetEntity="CoreBundle\Entity\AffiliateMaterial", mappedBy="owner")
     */
    private $materials;

    /**
     * Hook SoftDeleteable behavior
     * updates deletedAt field
     */
    use SoftDeleteableEntity;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->cardProgramCardType = new ArrayCollection();
        $this->cardProgramReshipper = new ArrayCollection();
        $this->cardProgramFeeItem = new ArrayCollection();
        $this->cardProgramService = new ArrayCollection();

        $this->countries = new ArrayCollection();

        $this->deliveryMethod = static::DELIVERY_METHOD_MAIL_FROM_DC;
    }

    /**
     * Set languagetype
     *
     * @param string $languagetype
     *
     * @return CardProgram
     */
    public function setLanguagetype($languagetype)
    {
        $this->languagetype = $languagetype;

        return $this;
    }

    /**
     * Get languagetype
     *
     * @return string
     */
    public function getLanguagetype()
    {
        return $this->languagetype;
    }

    /**
     * Set name
     *
     * @param string $name
     *
     * @return CardProgram
     */
    public function setName($name)
    {
        $this->name = $name;

        return $this;
    }

    /**
     * Get name
     *
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * Set isCoBrand
     *
     * @param boolean $isCoBrand
     *
     * @return CardProgram
     */
    public function setIsCoBrand($isCoBrand)
    {
        $this->isCoBrand = $isCoBrand;

        return $this;
    }

    /**
     * Get isCoBrand
     *
     * @return boolean
     */
    public function getIsCoBrand()
    {
        return $this->isCoBrand;
    }

    /**
     * Set isPortalUse
     *
     * @param boolean $isPortalUse
     *
     * @return CardProgram
     */
    public function setIsPortalUse($isPortalUse)
    {
        $this->isPortalUse = $isPortalUse;

        return $this;
    }

    /**
     * Get isPortalUse
     *
     * @return boolean
     */
    public function getIsPortalUse()
    {
        return $this->isPortalUse;
    }

    /**
     * Set status
     *
     * @param string $status
     *
     * @return CardProgram
     */
    public function setStatus($status)
    {
        $this->status = $status;

        return $this;
    }

    /**
     * Get status
     *
     * @return string
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * Set brandPartner
     *
     * @param \CoreBundle\Entity\BrandPartner $brandPartner
     *
     * @return CardProgram
     */
    public function setBrandPartner(\CoreBundle\Entity\BrandPartner $brandPartner = null)
    {
        $this->brandPartner = $brandPartner;

        return $this;
    }

    /**
     * Get brandPartner
     *
     * @return \CoreBundle\Entity\BrandPartner
     */
    public function getBrandPartner()
    {
        return $this->brandPartner;
    }

    /**
     * Set marketingPartner
     *
     * @param \CoreBundle\Entity\MarketingPartner $marketingPartner
     *
     * @return CardProgram
     */
    public function setMarketingPartner(\CoreBundle\Entity\MarketingPartner $marketingPartner = null)
    {
        $this->marketingPartner = $marketingPartner;

        return $this;
    }

    /**
     * Get marketingPartner
     *
     * @return \CoreBundle\Entity\MarketingPartner
     */
    public function getMarketingPartner()
    {
        return $this->marketingPartner;
    }

    /**
     * Add cardProgramCardType
     *
     * @param \CoreBundle\Entity\CardProgramCardType $cardProgramCardType
     *
     * @return CardProgram
     */
    public function addCardProgramCardType(\CoreBundle\Entity\CardProgramCardType $cardProgramCardType)
    {
        $this->cardProgramCardType[] = $cardProgramCardType;

        return $this;
    }

    /**
     * Remove cardProgramCardType
     *
     * @param \CoreBundle\Entity\CardProgramCardType $cardProgramCardType
     */
    public function removeCardProgramCardType(\CoreBundle\Entity\CardProgramCardType $cardProgramCardType)
    {
        $this->cardProgramCardType->removeElement($cardProgramCardType);
    }

    /**
     * Get cardProgramCardType
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getCardProgramCardType()
    {
        return $this->cardProgramCardType;
    }

    public function getCardType()
    {
        return $this->cardType;
    }
    public function setCardType($cardType)
    {
        $cpct = new CardProgramCardType();
        $cpct->setCardProgram($this);
        $cpct->setCardType($cardType);
        $this->addCardProgramCardType($cpct);
        $this->cardType = $cardType;
    }

    /**
     * Set hasCustomerAddress
     *
     * @param boolean $hasCustomerAddress
     *
     * @return CardProgram
     */
    public function setHasCustomerAddress($hasCustomerAddress)
    {
        $this->hasCustomerAddress = $hasCustomerAddress;

        return $this;
    }

    /**
     * Get hasCustomerAddress
     *
     * @return boolean
     */
    public function getHasCustomerAddress()
    {
        return $this->hasCustomerAddress;
    }

    /**
     * Add cardProgramReshipper
     *
     * @param \CoreBundle\Entity\CardProgramReshipper $cardProgramReshipper
     *
     * @return CardProgram
     */
    public function addCardProgramReshipper(\CoreBundle\Entity\CardProgramReshipper $cardProgramReshipper)
    {
        $this->cardProgramReshipper[] = $cardProgramReshipper;

        return $this;
    }

    /**
     * Remove cardProgramReshipper
     *
     * @param \CoreBundle\Entity\CardProgramReshipper $cardProgramReshipper
     */
    public function removeCardProgramReshipper(\CoreBundle\Entity\CardProgramReshipper $cardProgramReshipper)
    {
        $this->cardProgramReshipper->removeElement($cardProgramReshipper);
    }

    /**
     * Get cardProgramReshipper
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getCardProgramReshipper()
    {
        return $this->cardProgramReshipper;
    }

    public function getReshipper()
    {
        $reshippers = new ArrayCollection();

        foreach($this->cardProgramReshipper as $cpr)
        {
            $reshippers[] = $cpr->getReshipper();
        }
        return $reshippers;
    }
    public function setReshipper($reshippers)
    {
        foreach($reshippers as $reshipper)
        {
            $cpr = new CardProgramReshipper();
            $cpr->setCardProgram($this);
            $cpr->setReshipper($reshipper);
            $this->addCardProgramReshipper($cpr);
        }
    }

    /**
     * Add country
     *
     * @param \CoreBundle\Entity\Country $country
     *
     * @return CardProgram
     */
    public function addCountry(\CoreBundle\Entity\Country $country)
    {
        $this->countries[] = $country;

        return $this;
    }

    /**
     * Add cardProgramFeeItem
     *
     * @param \CoreBundle\Entity\CardProgramFeeItem $cardProgramFeeItem
     *
     * @return CardProgram
     */
    public function addCardProgramFeeItem(\CoreBundle\Entity\CardProgramFeeItem $cardProgramFeeItem)
    {
        $this->cardProgramFeeItem[] = $cardProgramFeeItem;

        return $this;
    }

    /**
     * Remove cardProgramFeeItem
     *
     * @param \CoreBundle\Entity\CardProgramFeeItem $cardProgramFeeItem
     */
    public function removeCardProgramFeeItem(\CoreBundle\Entity\CardProgramFeeItem $cardProgramFeeItem)
    {
        $this->cardProgramFeeItem->removeElement($cardProgramFeeItem);
    }

    /**
     * Get cardProgramFeeItem
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getCardProgramFeeItem()
    {
        return $this->cardProgramFeeItem;
    }

    public function getFeeItem()
    {
        $feeItems = new ArrayCollection();

        foreach($this->cardProgramFeeItem as $cpfi)
        {
            $feeItems[] = $cpfi->getFeeItem();
        }
        return $feeItems;
    }
    public function setFeeItem($feeItems)
    {
        foreach($feeItems as $feeItem)
        {
            $cpfi = new CardProgramFeeItem();
            $cpfi->setCardProgram($this);
            $cpfi->setFeeItem($feeItem);
            $this->addCardProgramFeeItem($cpfi);
        }
    }

    /**
     * Add cardProgramFeeRevenueShare
     *
     * @param \CoreBundle\Entity\CardProgramFeeRevenueShare $cardProgramFeeRevenueShare
     *
     * @return CardProgram
     */
    public function addCardProgramFeeRevenueShare(\CoreBundle\Entity\CardProgramFeeRevenueShare $cardProgramFeeRevenueShare)
    {
        $this->cardProgramFeeRevenueShare[] = $cardProgramFeeRevenueShare;

        return $this;
    }

    /**
     * Remove cardProgramFeeRevenueShare
     *
     * @param \CoreBundle\Entity\CardProgramFeeRevenueShare $cardProgramFeeRevenueShare
     */
    public function removeCardProgramFeeRevenueShare(\CoreBundle\Entity\CardProgramFeeRevenueShare $cardProgramFeeRevenueShare)
    {
        $this->cardProgramFeeRevenueShare->removeElement($cardProgramFeeRevenueShare);
    }

    /**
     * Get cardProgramFeeRevenueShare
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getCardProgramFeeRevenueShare()
    {
        return $this->cardProgramFeeRevenueShare;
    }

    /**
     * Add cardProgramService
     *
     * @param \CoreBundle\Entity\CardProgramService $cardProgramService
     *
     * @return CardProgram
     */
    public function addCardProgramService(\CoreBundle\Entity\CardProgramService $cardProgramService)
    {
        $this->cardProgramService[] = $cardProgramService;

        return $this;
    }

    /**
     * Remove cardProgramService
     *
     * @param \CoreBundle\Entity\CardProgramService $cardProgramService
     */
    public function removeCardProgramService(\CoreBundle\Entity\CardProgramService $cardProgramService)
    {
        $this->cardProgramService->removeElement($cardProgramService);
    }

    /**
     * Get cardProgramService
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getCardProgramService()
    {
        return $this->cardProgramService;
    }

    public function getService()
    {
        $services = new ArrayCollection();

        foreach($this->cardProgramService as $cpc)
        {
            $services[] = $cpc->getService();
        }
        return $services;
    }
    public function setService($services)
    {
        foreach($services as $service)
        {
            $cpc = new CardProgramService();
            $cpc->setCardProgram($this);
            $cpc->setService($service);
            $this->addCardProgramService($cpc);
        }
    }

    /**
     * Remove country
     *
     * @param \CoreBundle\Entity\Country $country
     */
    public function removeCountry(\CoreBundle\Entity\Country $country)
    {
        $this->countries->removeElement($country);
    }

    /**
     * Set cardsAllowed
     *
     * @param integer $cardsAllowed
     *
     * @return CardProgram
     */
    public function setCardsAllowed($cardsAllowed)
    {
        $this->cardsAllowed = $cardsAllowed;

        return $this;
    }

    /**
     * Get cardsAllowed
     *
     * @return integer
     */
    public function getCardsAllowed()
    {
        return $this->cardsAllowed;
    }

    /**
     * Set supportEmailValue
     *
     * @param string $supportEmailValue
     *
     * @return CardProgram
     */
    public function setSupportEmailValue($supportEmailValue)
    {
        $this->support_email_value = $supportEmailValue;

        return $this;
    }

    /**
     * Get supportEmailValue
     *
     * @return string
     */
    public function getSupportEmailValue()
    {
        return $this->support_email_value;
    }

    /**
     * Set liveChatUrlValue
     *
     * @param string $liveChatUrlValue
     *
     * @return CardProgram
     */
    public function setLiveChatUrlValue($liveChatUrlValue)
    {
        $this->live_chat_url_value = $liveChatUrlValue;

        return $this;
    }

    /**
     * Get liveChatUrlValue
     *
     * @return string
     */
    public function getLiveChatUrlValue()
    {
        return $this->live_chat_url_value;
    }

    /**
     * Set senderName
     *
     * @param string $senderName
     *
     * @return CardProgram
     */
    public function setSenderName($senderName)
    {
        $this->sender_name = $senderName;

        return $this;
    }

    /**
     * Get senderName
     *
     * @return string
     */
    public function getSenderName()
    {
        return $this->sender_name;
    }

    /**
     * Set productName
     *
     * @param string $productName
     *
     * @return CardProgram
     */
    public function setProductName($productName)
    {
        $this->product_name = $productName;

        return $this;
    }

    /**
     * Get productName
     *
     * @return string
     */
    public function getProductName()
    {
        return $this->product_name;
    }

    /**
     * Set companyName
     *
     * @param string $companyName
     *
     * @return CardProgram
     */
    public function setCompanyName($companyName)
    {
        $this->company_name = $companyName;

        return $this;
    }

    /**
     * Get companyName
     *
     * @return string
     */
    public function getCompanyName()
    {
        return $this->company_name;
    }

    /**
     * Set htmlLogo
     *
     * @param string $htmlLogo
     *
     * @return CardProgram
     */
    public function setHtmlLogo($htmlLogo)
    {
        $this->htmlLogo = $htmlLogo;

        return $this;
    }

    /**
     * Get htmlLogo
     *
     * @return string
     */
    public function getHtmlLogo()
    {
        return $this->htmlLogo ?: '';
    }

    /**
     * Add velocity
     *
     * @param \CoreBundle\Entity\Velocity $velocity
     *
     * @return CardProgram
     */
    public function addVelocity(\CoreBundle\Entity\Velocity $velocity)
    {
        $this->velocities[] = $velocity;

        return $this;
    }

    /**
     * Remove velocity
     *
     * @param \CoreBundle\Entity\Velocity $velocity
     */
    public function removeVelocity(\CoreBundle\Entity\Velocity $velocity)
    {
        $this->velocities->removeElement($velocity);
    }

    /**
     * Get velocities
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getVelocities()
    {
        return $this->velocities;
    }

    /**
     * Set meta
     *
     * @param string $meta
     *
     * @return CardProgram
     */
    public function setMeta($meta)
    {
        $this->meta = $meta;

        return $this;
    }

    /**
     * Get meta
     *
     * @return string
     */
    public function getMeta()
    {
        return $this->meta;
    }

    /**
     * Add material
     *
     * @param \CoreBundle\Entity\AffiliateMaterial $material
     *
     * @return CardProgram
     */
    public function addMaterial(\CoreBundle\Entity\AffiliateMaterial $material)
    {
        $this->materials[] = $material;

        return $this;
    }

    /**
     * Remove material
     *
     * @param \CoreBundle\Entity\AffiliateMaterial $material
     */
    public function removeMaterial(\CoreBundle\Entity\AffiliateMaterial $material)
    {
        $this->materials->removeElement($material);
    }

    /**
     * Get materials
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getMaterials()
    {
        return $this->materials;
    }

    /**
     * Set htmlAddress
     *
     * @param string $htmlAddress
     *
     * @return CardProgram
     */
    public function setHtmlAddress($htmlAddress)
    {
        $this->html_address = $htmlAddress;

        return $this;
    }

    /**
     * Get htmlAddress
     *
     * @return string
     */
    public function getHtmlAddress()
    {
        return $this->html_address;
    }

    /**
     * Set textAddress
     *
     * @param string $textAddress
     *
     * @return CardProgram
     */
    public function setTextAddress($textAddress)
    {
        $this->text_address = $textAddress;

        return $this;
    }

    /**
     * Get textAddress
     *
     * @return string
     */
    public function getTextAddress()
    {
        return $this->text_address;
    }

    /**
     * Set bin
     *
     * @param string $bin
     *
     * @return CardProgram
     */
    public function setBin($bin)
    {
        $this->bin = $bin;

        return $this;
    }

    /**
     * Get bin
     *
     * @return string
     */
    public function getBin()
    {
        return $this->bin;
    }

    /**
     * Set platform
     *
     * @param \CoreBundle\Entity\Platform $platform
     *
     * @return CardProgram
     */
    public function setPlatform(\CoreBundle\Entity\Platform $platform = null)
    {
        $this->platform = $platform;

        return $this;
    }

    /**
     * Get platform
     *
     * @return \CoreBundle\Entity\Platform
     */
    public function getPlatform()
    {
        return $this->platform;
    }

    /**
     * Set deliveryMethod
     *
     * @param string $deliveryMethod
     *
     * @return CardProgram
     */
    public function setDeliveryMethod($deliveryMethod)
    {
        $this->deliveryMethod = $deliveryMethod;

        return $this;
    }

    /**
     * Get deliveryMethod
     *
     * @return string
     */
    public function getDeliveryMethod()
    {
        return $this->deliveryMethod;
    }

    /**
     * Set kycProvider
     *
     * @param \CoreBundle\Entity\KycProvider $kycProvider
     *
     * @return CardProgram
     */
    public function setKycProvider(\CoreBundle\Entity\KycProvider $kycProvider = null)
    {
        $this->kycProvider = $kycProvider;

        return $this;
    }

    /**
     * Get kycProvider
     *
     * @return \CoreBundle\Entity\KycProvider
     */
    public function getKycProvider()
    {
        return $this->kycProvider;
    }

    /**
     * Set issuingBank
     *
     * @param \CoreBundle\Entity\IssuingBank $issuingBank
     *
     * @return CardProgram
     */
    public function setIssuingBank(\CoreBundle\Entity\IssuingBank $issuingBank = null)
    {
        $this->issuingBank = $issuingBank;

        return $this;
    }

    /**
     * Get issuingBank
     *
     * @return \CoreBundle\Entity\IssuingBank
     */
    public function getIssuingBank()
    {
        return $this->issuingBank;
    }

    /**
     * Set processor
     *
     * @param \CoreBundle\Entity\Processor $processor
     *
     * @return CardProgram
     */
    public function setProcessor(\CoreBundle\Entity\Processor $processor = null)
    {
        $this->processor = $processor;

        return $this;
    }

    /**
     * Get processor
     *
     * @return \CoreBundle\Entity\Processor
     */
    public function getProcessor()
    {
        return $this->processor;
    }

    /**
     * Set kycProviderProgram
     *
     * @param string $kycProviderProgram
     *
     * @return CardProgram
     */
    public function setKycProviderProgram($kycProviderProgram)
    {
        $this->kycProviderProgram = $kycProviderProgram;

        return $this;
    }

    /**
     * Get kycProviderProgram
     *
     * @return string
     */
    public function getKycProviderProgram()
    {
        return $this->kycProviderProgram;
    }

    /**
     * Set wallet
     *
     * @param string $wallet
     *
     * @return CardProgram
     */
    public function setWallet($wallet)
    {
        $this->wallet = $wallet;

        return $this;
    }

    /**
     * Get wallet
     *
     * @return string
     */
    public function getWallet()
    {
        return $this->wallet;
    }

    /**
     * Set walletApiKey
     *
     * @param string $walletApiKey
     *
     * @return CardProgram
     */
    public function setWalletApiKey($walletApiKey)
    {
        $this->walletApiKey = $walletApiKey;

        return $this;
    }

    /**
     * Get walletApiKey
     *
     * @return string
     */
    public function getWalletApiKey()
    {
        return $this->walletApiKey;
    }

    /**
     * Set packageId
     *
     * @param string $packageId
     *
     * @return CardProgram
     */
    public function setPackageId($packageId)
    {
        $this->packageId = $packageId;

        return $this;
    }

    /**
     * Get packageId
     *
     * @return string
     */
    public function getPackageId()
    {
        return $this->packageId;
    }

    /**
     * Set subBin
     *
     * @param string $subBin
     *
     * @return CardProgram
     */
    public function setSubBin($subBin)
    {
        $this->subBin = $subBin;

        return $this;
    }

    /**
     * Get subBin
     *
     * @return string
     */
    public function getSubBin()
    {
        return $this->subBin;
    }

    /**
     * Set currency
     *
     * @param string $currency
     *
     * @return CardProgram
     */
    public function setCurrency($currency)
    {
        $this->currency = $currency;

        return $this;
    }

    /**
     * Get currency
     *
     * @return string
     */
    public function getCurrency()
    {
        return $this->currency;
    }

    /**
     * Set onDemandPrinting
     *
     * @param boolean $onDemandPrinting
     *
     * @return CardProgram
     */
    public function setOnDemandPrinting($onDemandPrinting)
    {
        $this->onDemandPrinting = $onDemandPrinting;

        return $this;
    }

    /**
     * Get onDemandPrinting
     *
     * @return boolean
     */
    public function getOnDemandPrinting()
    {
        return $this->onDemandPrinting;
    }

    /**
     * Set cardPrinter
     *
     * @param \CoreBundle\Entity\CardPrinter $cardPrinter
     *
     * @return CardProgram
     */
    public function setCardPrinter(\CoreBundle\Entity\CardPrinter $cardPrinter = null)
    {
        $this->cardPrinter = $cardPrinter;

        return $this;
    }

    /**
     * Get cardPrinter
     *
     * @return \CoreBundle\Entity\CardPrinter
     */
    public function getCardPrinter()
    {
        return $this->cardPrinter;
    }

    /**
     * Set programOwner
     *
     * @param \SalexUserBundle\Entity\User $programOwner
     *
     * @return CardProgram
     */
    public function setProgramOwner(\SalexUserBundle\Entity\User $programOwner = null)
    {
        $this->programOwner = $programOwner;

        return $this;
    }

    /**
     * Get programOwner
     *
     * @return \SalexUserBundle\Entity\User
     */
    public function getProgramOwner()
    {
        return $this->programOwner;
    }
}
