<?php
/**
 * User: Bob
 * Date: 2017/3/8
 * Time: 22:35
 */

namespace CoreBundle\Repository;


use CoreBundle\Constant\CardProgramStatus;
use CoreBundle\Constant\IdentityType;
use CoreBundle\Entities\FeeStructure;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\CardProgramCardType;
use CoreBundle\Entity\CardProgramCountryLoadPartnerMethodCurrency;
use CoreBundle\Entity\CardProgramFeeItem;
use CoreBundle\Entity\CardProgramFeeRevenueShare;
use CoreBundle\Entity\Country;
use CoreBundle\Entity\FeeGlobalName;
use CoreBundle\Entity\FeeItem;
use CoreBundle\Entity\KycProvider;
use CoreBundle\Entity\LoadMethod;
use CoreBundle\Entity\Promotion;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Entity\UserDiscount;
use CoreBundle\Utils\Util;
use Doctrine\ORM\EntityRepository;
use Faker\Generator;
use Illuminate\Support\Collection;
use SalexUserBundle\Entity\User;
use Tree\Fixture\Transport\Car;
use CoreBundle\Utils\Money;

class CardProgramRepository extends BaseRepository
{
    /**
     * @param CardProgram $cardProgram
     * @param array $options
     */
    public function generateNewCardProgram(CardProgram $cardProgram, array $options)
    {
        $em = $this->getEntityManager();
        //Deal with Card Type
        if ($options['cardTypeDetails'] != null) {
            if (!$cardProgram->getCardProgramCardType()->isEmpty()) {
                $cardProgram->getCardProgramCardType()->clear();
            }
            $cardTypes = $options['cardTypeDetails'];
            foreach ($cardTypes as $cardStr) {
                $cardType = json_decode($cardStr, true);
                $tmp = new CardProgramCardType();
                $targetCardType = $em->find(\CoreBundle\Entity\CardType::class, $cardType['id']);
                $tmp->setCustName($cardType['custName'])->setMaxBalance($cardType['maxBalance'])
                    ->setMaxLoad($cardType['maxLoad'])->setMinLoad($cardType['minLoad'])
                    ->setIsAutoCreated($cardType['isAutoCreated'] == 'Yes' ? 1 : 0)
                    ->setCardsAllowed($cardType['cardsAllowed'])
                    //Begin Add By Bob Wen Bao on 2017-03-26 to handle card information in sign up process
                    ->setArtwork($cardType['artwork'])
                    ->setDescription($cardType['description'])
                    ->setBenefit($cardType['benefit'])
                    ->setCurrencies($cardType['currencies'])
                    ->setFundingAccountNumber($cardType['faccountnum'])
                    ->setRegistrable($cardType['registrable'] ?? true)
                    //End
                    //Need to handle additional logic if any later
                    ->setIsAdditional(false)
                    ->setCardProgram($cardProgram)
                    ->setCardType($targetCardType);
                $cardProgram->addCardProgramCardType($tmp);
            }
        }
        //Deal with Fee Item Revenue
        if (!$cardProgram->getCardProgramFeeItem()->isEmpty() && $options['feeItemRevenues'] != null) {
            $feeItemRevenueStrs = $options['feeItemRevenues'];
            foreach ($feeItemRevenueStrs as $feeItemRevenue) {
                $feeItemRevenues = json_decode($feeItemRevenue, true);
                if (is_array($feeItemRevenues)) {
                    foreach ($feeItemRevenues as $feeItemRevenue) {
                        $tmp = new CardProgramFeeRevenueShare();
                        $tmpFeeItem = $em->find(\CoreBundle\Entity\FeeItem::class, $feeItemRevenue['feeItemId']);
                        $feecur = $tmpFeeItem->getCostTernFixedCurrency();
                        $Fixeds=Money::normalizeAmount($feeItemRevenue['fixed'],$feecur);
                        $tmp->setFeeItem($tmpFeeItem)
                            ->setCardProgram($cardProgram)
                            ->setTenantId($feeItemRevenue['tenantId'])
                            ->setTenantType($feeItemRevenue['tenantType'])
                            ->setShareFixed($Fixeds)
                            ->setShareRatio($feeItemRevenue['ratio']);
                        $cardProgram->addCardProgramFeeRevenueShare($tmp);
                    }
                } else//Need modify here
                {
                    $tmp = new CardProgramFeeRevenueShare();
                    $tmpFeeItem = $em->find(\CoreBundle\Entity\FeeItem::class, $feeItemRevenues['feeItemId']);
                    $feecur = $tmpFeeItem->getCostTernFixedCurrency();
                    $Fixeds=Money::normalizeAmount($feeItemRevenue['fixed'],$feecur);
                    $tmp->setFeeItem($tmpFeeItem)
                        ->setCardProgram($cardProgram)
                        ->setTenantId($feeItemRevenues['tenantId'])
                        ->setTenantType($feeItemRevenues['tenantType'])
                        ->setShareFixed($Fixeds)
                        ->setShareRatio($feeItemRevenues['ratio']);
                    $cardProgram->addCardProgramFeeRevenueShare($tmp);
                }

            }
        }
        if (!$cardProgram->getCardProgramFeeItem()->isEmpty() && $options['feeItemDetails'] != null) {
            if (!$cardProgram->getCardProgramFeeItem()->isEmpty()) {
                $cardProgram->getCardProgramFeeItem()->clear();
            }
            $feeItems = $options['feeItemDetails'];
            foreach ($feeItems as $feeStr) {
                $feeItem = json_decode($feeStr);
                $tmp = new CardProgramFeeItem();
                $tmpFeeItem = $em->find(\CoreBundle\Entity\FeeItem::class, $feeItem->{'id'});
                $feecur = $tmpFeeItem->getCostTernFixedCurrency();
                $Fixedcus=Money::normalizeAmount($feeItem->{'feeToCusFixed'},$feecur);
                $Fixedbp=Money::normalizeAmount($feeItem->{'feeToBPFixed'},$feecur);
                $tmp->setFeeItem($tmpFeeItem)
                    ->setFeeToCusFixed($Fixedcus)
                    ->setFeeToCusRatio($feeItem->{'feeToCusRatio'})
                    ->setFeeToBPFixed($Fixedbp)
                    ->setFeeToBPRatio($feeItem->{'feeToBPRatio'})
                    ->setCardProgram($cardProgram);
                $cardProgram->addCardProgramFeeItem($tmp);
            }
        }
        if (!$cardProgram->getCardProgramFeeItem()->isEmpty() && $options['feeItemDetails'] == null) {
            $cardProgram->getCardProgramFeeItem()->clear();
        }
        $cardProgram->setStatus(CardProgramStatus::TEST)
            ->setLanguagetype($options['languagetype']);
        $em->persist($cardProgram);
        $em->flush();

        //Load partner / Kyc provider settings by country
        $request = $options['request'];
        $loadPartners = $options['loadPartners'];
        $kycProviders = $options['kycProviders'];
        foreach ($cardProgram->getCountries() as $country) {
            // load partner settings
            // form data format:
            //      load_partner_settings_{countryId}: {countryId}_{loadPartnerId}_{loadMethodId}_currencies=xxx&{countryId}_{loadPartnerId}_{loadMethodId}_enabled=xxx
            $formData = $request->get("load_partner_settings_" . $country->getId());
            parse_str($formData, $settings);
//            dump($settings);
            foreach ($loadPartners as $loadPartner) {
                foreach ($loadPartner->getLoadMethods() as $loadMethod) {
                    $currenciesKey = $country->getId() . '_' . $loadPartner->getId() . '_' . $loadMethod->getId() . '_currencies';
                    $currencies = "";
                    if (array_key_exists($currenciesKey, $settings)) {
                        $currencies = join(";", $settings[$currenciesKey]);
                    }

                    $enabledKey = $country->getId() . '_' . $loadPartner->getId() . '_' . $loadMethod->getId() . '_enabled';
                    $enabled = false;
                    if (array_key_exists($enabledKey, $settings)) {
                        if($currencies) {
                            $enabled = $settings[$enabledKey] == 'on';
                        }
                    }

                    if($enabled)
                    {
                        $em->getRepository(\CoreBundle\Entity\CardProgramCountryLoadPartnerMethodCurrency::class)
                            ->setMappedCurrenciesSettings($cardProgram, $country, $loadPartner, $loadMethod, $currencies, $enabled);
                    }
                }
            }
            // kyc provider settings
            // form data format:
            //      kyc_provider_settings_{countryId}: {countryId}_{kycId}_{idType}_requirement=xxx&{countryId}_{kycId}_{idType}_enabled=xxx
            $formData = $request->get("kyc_provider_settings_" . $country->getId());
            parse_str($formData, $settings);
            foreach ($kycProviders as $kycProvider) {
                foreach (IdentityType::All as $idType) {
                    $requirementKey = $country->getId() . '_' . $kycProvider->getId() . '_' . $idType . '_requirement';
                    $requirement = "";
                    if (array_key_exists($requirementKey, $settings)) {
                        $requirement = $settings[$requirementKey];
                    }

                    $enabledKey = $country->getId() . '_' . $kycProvider->getId() . '_' . $idType . '_enabled';
                    $enabled = false;
                    if (array_key_exists($enabledKey, $settings)) {
                        if($requirement) {
                            $enabled = $settings[$enabledKey] == 'on';
                        }
                    }

                    if($enabled)
                    {
                        $em->getRepository(\CoreBundle\Entity\CardProgramCountryKycIds::class)
                            ->setMappedImageRequirementSettings($cardProgram, $country, $kycProvider, $idType, $requirement, $enabled);
                    }
                }
            }
        }
    }

    public function fake(Generator $faker = null, $persist = true)
    {
        $faker = $faker ?: Util::faker();

        /** @var CardProgram $entity */
        $entity = BaseRepository::fakeBaseEntity(new CardProgram());
        $entity->setName('Faked ' . $faker->sentence(3) . ' ' . $faker->numberBetween(1, 999));

        $entity->setIsCoBrand($faker->boolean);
        $entity->setIsPortalUse($faker->boolean);
        $entity->setHasCustomerAddress($faker->boolean);
        $entity->setStatus($faker->randomElement(CardProgramStatus::getConstants()));
        $entity->setCardsAllowed($faker->numberBetween(10000, 10000000));

        if ($persist) {
            Util::persist($entity);
        }

        return $entity;
    }

    public function getLoadMethodsRaw(CardProgram $cardProgram, $countryId)
    {
        if (!$countryId) {
            return [];
        }
        $query = $this->getEntityManager()
            ->getRepository(\CoreBundle\Entity\CardProgramCountryLoadPartnerMethodCurrency::class)
            ->createQueryBuilder('cpclpmc')
            ->join('cpclpmc.cardProgram', 'cp')
            ->join('cpclpmc.loadPartner', 'lp')
            ->join('cpclpmc.loadMethod', 'lm')
            ->where('cp.id = ' . $cardProgram->getId())
            ->andWhere('cpclpmc.country = ' . $countryId)
            ->andWhere('cpclpmc.enabled = 1')
            ->select('cpclpmc, lp, lm')
            ->getQuery();
        return $query->getResult();
    }

    public function getLoadMethods(CardProgram $cardProgram, $countryId)
    {
        $all = $this->getLoadMethodsRaw($cardProgram, $countryId);
        $result = [];
        /** @var CardProgramCountryLoadPartnerMethodCurrency $cpclpmc */
        foreach ($all as $cpclpmc) {
            $lm = $cpclpmc->getLoadMethod();
            $lmId = $lm->getId();

            $lp = $cpclpmc->getLoadPartner();
            $lpId = $lp->getId();

            $d = $lm->toApiArray();
            $d['icon'] = $lm->getIcon(true);
            $d['partner'] = Util::e2a($lp);

            foreach ($cpclpmc->getCurrencyArray() as $currency) {
                $key = $lmId . '_' . $lpId . '_' . $currency;
                if (!isset($result[$key])) {
                    $result[$key] = array_merge($d, [
                        'currency' => $currency,
                    ]);
                }
            }
        }

        return $result;
    }

    public function getFeeItemText(CardProgram $cardProgram, $name, User $user = null,
                                   string $currency = null, ?int $amount = null)
    {
        $feeItem = $this->getFeeItem($cardProgram, $name, $user, $amount);
        if ($feeItem) {
            return $feeItem->getDescription($currency);
        }
        return '0';
    }

    public function getFinalLoadFeeItemText(UserCard $uc, $methodName, $amount, $currency = 'USD')
    {
        if (($p = Promotion::applyUsuLoadFeePromotion($uc, null, $amount)) !== false) {
            return (string)$p;
        }

        /** @var CardProgramFeeItem $feeItem */
        $feeItem = null;
        $loadFee = $uc->calculateFee($methodName, $amount, $feeItem);
        if (!$loadFee || !$feeItem) {
            return '0';
        }
        if (empty($loadFee['final'])) {
            return '0';
        }
        if (empty($loadFee['discount'])) {
            return $feeItem->getDescription($currency);
        }
        return Money::format($loadFee['final'], $currency);
    }

    /**
     * @param CardProgram $cardProgram
     * @param             $name
     * @return CardProgramFeeItem|null
     */
    public function getPartnerFeeItem(CardProgram $cardProgram, $name)
    {
        $name = strtolower($name);

        $cpFeeItems = $cardProgram->getCardProgramFeeItem();
        /** @var CardProgramFeeItem $cpFeeItem */
        foreach ($cpFeeItems as $cpFeeItem) {
            $feeItem = $cpFeeItem->getFeeItem();
            $global = $feeItem->getFeeGlobalName()->getName();
            if (strtolower($global) === $name) {
                return $cpFeeItem;
            }
        }
        return null;
    }

    /**
     * @param CardProgram $cardProgram
     * @param             $name
     * @param User|null   $user
     *
     * @return CardProgramFeeItem|null
     */
    public function getFeeItem(CardProgram $cardProgram, $name, User $user = null, ?int $amount = null)
    {
        $name = strtolower($name);

        $skipLoadFee = false;
        if ($user) {
            if ($amount !== null && $amount < 1000 && $user->isInternalTester()) {
                $skipLoadFee = true;
            }
//            if ($user->hasLegacyBalance()) {
//                $skipLoadFee = true;
//            }
        }

        // https://github.com/terncommerce/span/issues/548
        // Disable the load fee for users with legacy balance
        if ($skipLoadFee) {
            $found = false;
            foreach ([
                FeeGlobalName::MONTHLY_FEE,
                FeeGlobalName::ONE_TIME_MEMBERSHIP_FEE,
                FeeGlobalName::UNLOAD_FEE,
                FeeGlobalName::APPROVED_TRANSACTION_FEE,
                FeeGlobalName::DECLINED_TRANSACTION_FEE,
            ] as $gn) {
                if (strtolower($gn) === $name) {
                    $found = true;
                    break;
                }
            }
            if (!$found) {
                return null;
            }
        }

        if ($name === 'bank transfer' || $name === 'bank transfers') {
            $name = 'bank';
        } else if ($name === 'brazilpayboleto') {
            $name = 'boleto';
        }
        $cpFeeItems = $cardProgram->getCardProgramFeeItem();
        /** @var CardProgramFeeItem $cpFeeItem */
        foreach ($cpFeeItems as $cpFeeItem) {
            $feeItem = $cpFeeItem->getFeeItem();
            $global = $feeItem->getFeeGlobalName()->getName();
            if (strtolower($global) === $name) {
                return $cpFeeItem;
            }
        }
        return null;
    }

    public function calculatePayAmount(UserCardLoad $load, $methodName, $methodCurrency)
    {
        $initialAmount = $load->getInitialAmount();
        $initialCurrency = $load->getInitialCurrency();
        $userCard = $load->getUserCard();
        $user = $userCard->getUser();
        $cardProgram = $userCard->getCard()->getCardProgram();

        $payAmount = $initialAmount;
        $feeItem = $this->getFeeItem($cardProgram, $methodName, $user, $initialAmount);
        if ($feeItem) {
            $payAmount += $feeItem->calculate($initialAmount, $initialCurrency);
        }

        if (!$load->getReload()) {
            $feeItem = $this->getFeeItem($cardProgram, FeeGlobalName::ONE_TIME_MEMBERSHIP_FEE, $user);
            if ($feeItem) {
                $payAmount += $feeItem->calculate($initialAmount, $initialCurrency);
            }
        }

        $converted = Money::convertWithExtra($payAmount, $initialCurrency, $methodCurrency);
        return Money::roundUpIfRequired($methodName, $converted, $methodCurrency);
    }

    public function getImageRequirementSettings(CardProgram $cardProgram, Country $country)
    {
        if ($cardProgram->isUsUnlocked() || $cardProgram->isCashOnWeb()) {
            $kycProvider = KycProvider::find(KycProvider::IDOLOGY);
            return [
                $kycProvider->getId() => [
                    'DL' => [
                        'enabled' => true,
                        'name' => 'Driver License',
                        'requirement' => 'BOTH',
                    ],
                    'ID' => [
                        'enabled' => true,
                        'name' => 'ID Card',
                        'requirement' => 'BOTH',
                    ],
                    'PSP' => [
                        'enabled' => true,
                        'name' => 'Passport',
                        'requirement' => 'FRONT',
                    ],
                ],
            ];
        }

        $all = Util::em()->getRepository(\CoreBundle\Entity\CardProgramCountryKycIds::class)
            ->createQueryBuilder('cpcki')
            ->where('cpcki.cardProgram = ' . $cardProgram->getId())
            ->andWhere('cpcki.country = ' . $country->getId())
            ->getQuery()
            ->getArrayResult();

        $result = [];
        foreach ($all as $item) {
            $kyc = $item['kyc_provider_id'];
            $type = $item['identityType'];
            $result[$kyc][$type] = [
                'enabled' => $item['enabled'],
                'name' => IdentityType::NAMES[$type],
                'requirement' => $item['imageRequirement'],
            ];
        }

        return $result;
    }

    public function getUserIdsWithDeletedCards(\Doctrine\Common\Collections\Collection $cardPrograms)
    {
        $cpIds = [];
        /** @var CardProgram $cardProgram */
        foreach ($cardPrograms as $cardProgram) {
            $cpIds[] = $cardProgram->getId();
        }
        if (!$cpIds) {
            return [];
        }
        $st = Util::em()->getConnection()->executeQuery('SELECT distinct u.id
            FROM users u INNER JOIN user_card uc ON u.id = uc.user_id and uc.deleted_at is not null
              INNER JOIN card_program_card_type ct ON uc.card_id = ct.id AND ct.card_program_id in ('
            . implode(',', $cpIds) . ')');
        $rs = $st->fetchAll(\PDO::FETCH_ASSOC);
        return array_map(function ($r) {
            return $r['id'];
        }, $rs);
    }

    public function getDeletedCardProgramsOfUser(User $user)
    {
        $st = Util::em()->getConnection()->executeQuery('SELECT distinct ct.card_program_id
            FROM users u INNER JOIN user_card uc ON u.id = uc.user_id and uc.deleted_at is not null
              INNER JOIN card_program_card_type ct ON uc.card_id = ct.id and u.id = ' . $user->getId());
        $rs = $st->fetchAll(\PDO::FETCH_ASSOC);
        $result = [];
        foreach ($rs as $r) {
            $id = $r['card_program_id'];
            $result[$id] = $this->find($id);
        }
        return $result;
    }

}
