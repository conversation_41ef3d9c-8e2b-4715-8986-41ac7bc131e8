<?php

namespace CoreBundle\Repository;

use CoreBundle\Entity\CountryLoadPartnerMethodCurrency;
use CoreBundle\Utils\Util;
use Doctrine\ORM\EntityRepository;
use Exception;

class CountryLoadPartnerMethodCurrencyRepository extends EntityRepository
{
    public function getMappedCurrenciesSettings($countryId, $loadPartnerId, $loadMethodId)
    {
        $record = $this->getCountryLoadPartnerMethodCurrency($countryId, $loadPartnerId, $loadMethodId);

        if ($record) {
            return array(
                "currencies" => $record->getCurrencies(),
                "enabled" => $record->getEnabled(),
            );
        }
        return [
          'currencies' => '',
          'enabled' => false,
        ];
    }

    public function getMappedCurrenciesSettingsx($countryId, $loadPartnerId, $loadMethodId)
    {
        $record = $this->getCountryLoadPartnerMethodCurrencyx($countryId, $loadPartnerId, $loadMethodId);

        if ($record) {
            return array(
                "currencies" => $record->getCurrencies(),
                "enabled" => $record->getEnabled(),
            );
        }
        return [
            'currencies' => '',
            'enabled' => false,
        ];
    }

    public function setMappedCurrenciesSettings($countryId, $loadPartnerId, $loadMethodId, $currencies, $enabled)
    {
        $country = $this->getEntityManager()->getRepository(\CoreBundle\Entity\Country::class)
            ->find($countryId);
        if (!$country) {
            throw new Exception("Country not found: " . $countryId);
        }

        $loadPartner = $this->getEntityManager()->getRepository(\CoreBundle\Entity\LoadPartner::class)
            ->find($loadPartnerId);
        if (!$loadPartner) {
            throw new Exception("Load partner not found: " . $loadPartnerId);
        }

        $loadMethod = $this->getEntityManager()->getRepository(\CoreBundle\Entity\LoadMethod::class)
            ->find($loadMethodId);
        if (!$loadMethod) {
            throw new Exception("Load method not found: " . $loadMethodId);
        }

        $em = $this->getEntityManager();
        $record = $this->getCountryLoadPartnerMethodCurrency($countryId, $loadPartnerId, $loadMethodId);

        if ($record) {
            $record->setCurrencies($currencies);
            $record->setEnabled($enabled);
        } else {
            $record = new CountryLoadPartnerMethodCurrency();
            $record->setCountry($country)
                ->setLoadPartner($loadPartner)
                ->setLoadMethod($loadMethod)
                ->setCurrencies($currencies)
                ->setEnabled($enabled);
            $em->persist($record);
        }

        $em->flush();
    }
    public function setMappedCurrenciesSettingsx($countryId, $loadPartnerId, $loadMethodId, $currencies, $enabled)
    {
        $country = $this->getEntityManager()->getRepository(\CoreBundle\Entity\Country::class)
            ->find($countryId);
        if (!$country) {
            throw new Exception("Country not found: " . $countryId);
        }

        $loadPartner = $this->getEntityManager()->getRepository(\CoreBundle\Entity\LoadPartner::class)
            ->find($loadPartnerId);
        if (!$loadPartner) {
            throw new Exception("Load partner not found: " . $loadPartnerId);
        }

        $loadMethod = $this->getEntityManager()->getRepository(\CoreBundle\Entity\LoadMethod::class)
            ->find($loadMethodId);
        if (!$loadMethod) {
            throw new Exception("Load method not found: " . $loadMethodId);
        }

        $em = $this->getEntityManager();
        $record = $this->getCountryLoadPartnerMethodCurrencyx($countryId, $loadPartnerId, $loadMethodId);

        if ($record) {
            $record->setCurrencies($currencies);
            $record->setEnabled($enabled);
        } else {
            $record = new CountryLoadPartnerMethodCurrency();
            $record->setCountry($country)
                ->setLoadPartner($loadPartner)
                ->setLoadMethod($loadMethod)
                ->setCurrencies($currencies)
                ->setEnabled($enabled);
            $em->persist($record);
        }

        $em->flush();
        $this->cardprogramcounryloadoartener($countryId,$loadPartnerId,$loadMethodId,$enabled,$currencies);
    }
    protected function cardprogramcounryloadoartener($countryId,$loadPartnerId,$loadMethodId,$enabled,$currencies)
    {
        $em = $this->getEntityManager();
        $countryloadpartener = $em->getRepository(\CoreBundle\Entity\CardProgramCountryLoadPartnerMethodCurrency::class)
            ->findAll();
        $cardprogramids=array();
        if(!$enabled) {
            foreach ($countryloadpartener as $item) {
                if ($item->getCountry()->getId() == $countryId) {
                    if (!in_array($item->getCardProgram()->getId(), $cardprogramids)) {
                        $cardprogramids[] = $item->getCardProgram()->getId();
                    }
                }
            }
            if($cardprogramids){
              foreach ($cardprogramids as $cpitem){
                  foreach ($countryloadpartener as $itemss) {
                      if($itemss->getCardProgram()->getId()==$cpitem&&$itemss->getCountry()->getId()==$countryId&&$itemss->getLoadPartner()->getId()==$loadPartnerId&&$itemss->getLoadMethod()->getId()==$loadMethodId){
                          $em->remove($itemss);
                          $em->flush();
                      }
                  }
              }
            }
        }
        $currenciesA = explode(';',$currencies);
        $cardprogramidsx=array();$cardprogramidsxs=array();
        foreach ($countryloadpartener as $itemx) {
            if ($itemx->getCountry()->getId() == $countryId && $itemx->getLoadPartner()->getId()==$loadPartnerId&&$itemx->getLoadMethod()->getId()==$loadMethodId) {
                if (!in_array($itemx->getCardProgram()->getId(), $cardprogramidsx)) {
                    $cardprogramidsx[] = $itemx->getCardProgram()->getId();
                    $cardprogramidsxs[] = $itemx;
                }
            }
        }
        if($cardprogramidsxs){
            foreach ($cardprogramidsxs as $cpitem){
                $curr=array();
                $currenciesB = explode(';',$cpitem->getCurrencies());
                if($currenciesA) {
                    foreach ($currenciesB as $cpitemb) {
                       if(in_array($cpitemb,$currenciesA)){
                           if(!in_array($cpitemb,$curr))
                               $curr[]=$cpitemb;
                       }
                    }
                }
                if($curr){
                    $currs='';
                    foreach ($curr as $itemxs){
                        $currs=$itemxs.';'.$currs;
                    }
                    $cpitem->setCurrencies($currs);
                    $em->persist($cpitem);
                    $em->flush();
                }else{
                    $cpitem->setCurrencies($currencies)
                           ->setEnabled(false);
                    $em->persist($cpitem);
                    $em->flush();
                }
            }
        }

    }

    protected function getCountryLoadPartnerMethodCurrency($countryId, $loadPartnerId, $loadMethodId)
    {
        return $this->createQueryBuilder("c")
            ->where("IDENTITY(c.country) = :countryId")
            ->andWhere("IDENTITY(c.loadPartner) = :loadPartnerId")
            ->andWhere("IDENTITY(c.loadMethod) = :loadMethodId")
            ->andWhere("c.enabled = :enabledx")
            ->setParameters(array(
                "countryId" => $countryId,
                "loadPartnerId" => $loadPartnerId,
                "loadMethodId" => $loadMethodId,
                "enabledx" => true,
            ))
            ->getQuery()
            ->getOneOrNullResult();
    }
    protected function getCountryLoadPartnerMethodCurrencyx($countryId, $loadPartnerId, $loadMethodId)
    {
        if (isset($GLOBALS['BATCH'])) {
            $key = implode('_', func_get_args());
            if (isset($GLOBALS['BATCH'][__FUNCTION__])) {
                return $GLOBALS['BATCH'][__FUNCTION__][$key] ?? null;
            }
            $all = $this->findAll();
            $map = [];
            /** @var CountryLoadPartnerMethodCurrency $item */
            foreach ($all as $item) {
                $k = implode('_', [
                    Util::field($item->getCountry(), 'id'),
                    Util::field($item->getLoadPartner(), 'id'),
                    Util::field($item->getLoadMethod(), 'id'),
                ]);
                $map[$k] = $item;
            }

            $GLOBALS['BATCH'][__FUNCTION__] = $map;
            return $map[$key];
        }

        return $this->createQueryBuilder("c")
            ->where("IDENTITY(c.country) = :countryId")
            ->andWhere("IDENTITY(c.loadPartner) = :loadPartnerId")
            ->andWhere("IDENTITY(c.loadMethod) = :loadMethodId")
            ->setParameters(array(
                "countryId" => $countryId,
                "loadPartnerId" => $loadPartnerId,
                "loadMethodId" => $loadMethodId,
            ))
            ->getQuery()
            ->getOneOrNullResult();
    }
}
