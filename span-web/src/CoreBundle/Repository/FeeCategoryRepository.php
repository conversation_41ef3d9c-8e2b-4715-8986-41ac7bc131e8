<?php
/**
 * User: Bob
 * Date: 2017/2/21
 * Time: 13:55
 * This is used to handle specific logic besides standard find\findAll and so on.
 */

namespace CoreBundle\Repository;


use Doctrine\ORM\EntityRepository;

class FeeCategoryRepository extends EntityRepository
{
    /**
     * This is used to find all the fee categories with search box in the index page
     * @param $searchInfo
     * @return array
     */
    public function findLikeName($searchInfo)
    {
        return $this->createQuerybuilder('f')
            ->where('f.name LIKE :name')
            ->setParameter('name', '%'.$searchInfo.'%')
            ->getQuery()
            ->getResult();
    }

}