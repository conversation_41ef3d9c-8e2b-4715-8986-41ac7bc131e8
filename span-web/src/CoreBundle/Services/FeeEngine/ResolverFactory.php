<?php
/**
 * Created by PhpStorm.
 * User: hans
 * Date: 2018/4/16
 * Time: 16:02
 */

namespace CoreBundle\Services\FeeEngine;


use CoreBundle\Entity\FeeItem;

class ResolverFactory
{
    public static function getResolver(FeeItem $feeItem)
    {
        $way = $feeItem->getAppliedBy();
        if ($way === FeeItem::WAY_REPORTED_BY_FTP) {
            return new FTPResolver();
        }
        if ($way === FeeItem::WAY_REPORTED_BY_API) {
            return new APIResolver();
        }
        if ($way === FeeItem::WAY_REPORTED_OFFLINE_BY_TENANT) {
            return new OfflineResolver();
        }
        if ($way === FeeItem::WAY_PROGRAM_CHARGE_AND_CONFIGURATION) {
            return new ProgramResolver();
        }
        return null;
    }
}