<?php

namespace CoreBundle\Services;

use CoreBundle\Entity\Config;
use CoreBundle\Utils\Log;
use PortalBundle\PortalBundle;
use SalexUserBundle\Entity\User;
use UsUnlockedBundle\Services\Mautic\MauticService;

class PostmarkService
{
    public static function processBounce(array $bounce): string
    {
        $email = $bounce['Email'] ?? $bounce['Recipient'] ?? null;
        if (!$email) {
            return 'Unknown email address';
        }
        $user = User::findPlatformUserByEmail($email, PortalBundle::getMemberRoles());
        if (!$user) {
            return 'Unknown user with the email ' . $email;
        }

        $action = '';
        $type = $bounce['Type'] ?? $bounce['RecordType'] ?? '';
        try {
            if (in_array($type, [
                'HardBounce',
                'BadEmailAddress',
                'Blocked',
                'DMARCPolicy',
                'ManuallyDeactivated',
                'Undeliverable',
                'SpamComplaint',
                'SpamNotification',
            ])) {
                Config::addInactiveEmail($user->getEmail());
                MauticService::updateContactForBounce($user, $type, true);
                $action = 'Bounced the user ' . $user->getIdEmail();
            } else if (in_array($type, [
                'Unsubscribe',
                'Unsubscribed',
            ])) {
                MauticService::updateContactForUnsubscribe($user, $type, true);
                $action = 'Unsubscribed the user ' . $user->getIdEmail();
            } else if ($type === 'SubscriptionChange') {
                $suppress = $bounce['SuppressSending'] ?? false;
                if ($suppress) {
                    MauticService::updateContactForUnsubscribe($user, $type, true);
                    $action = 'Subscription suppressed for the user ' . $user->getIdEmail();
                } else {
                    MauticService::updateContactForSubscribe($user, true);
                    $action = 'Subscription resumed for the user ' . $user->getIdEmail();
                }
            } else {
                $action = 'Skipped bounce type ' . $type . ' for the user ' . $user->getIdEmail();
            }
        } catch (\Throwable $t) {
            Log::exception('Failed to process Postmark bounce for the user', $t, [
                'type' => $type,
                'user' => $user->getId(),
            ], false);
            $action = 'Postmark process exception for the user ' . $user->getIdEmail() . ' and type ' . $type;
        }
        return $action;
    }
}
