<?php


namespace CoreBundle\Services;


use CoreBundle\Constant\AlternativePaymentSupported;
use CoreBundle\Entity\CardProgramCountryLoadPartnerMethodCurrency;
use CoreBundle\Entity\FeeItem;
use CoreBundle\Entity\LoadMethod;
use CoreBundle\Entity\LoadPartner;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Util;
use UsUnlockedBundle\Services\ReferService;

class RevenueService
{
    public static function loadMethodCosts()
    {
        return Data::callback(__METHOD__, null, function () {
            return self::queryLoadMethodCosts();
        }, false);
    }

    protected static function queryLoadMethodCosts()
    {
        $em = Util::em();
        $rs = $em->getRepository(CardProgramCountryLoadPartnerMethodCurrency::class)
            ->createQueryBuilder('cpc')
            ->join('cpc.loadMethod', 'lm')
            ->where('cpc.currencies <> \'\'')
            ->andWhere('cpc.currencies is not null')
            ->select('lm.name, cpc.currencies')
            ->distinct()
            ->getQuery()
            ->getArrayResult();
        $all = [];
        foreach ($rs as $r) {
            $lm = $r['name'];
            if (!isset($all[$lm])) {
                $all[$lm] = [];
            }
            $currencies = explode(';', trim($r['currencies'], ';'));
            foreach ($currencies as $currency) {
                if (!in_array($currency, $all[$lm])) {
                    $all[$lm][] = $currency;
                }
            }
        }

        $rs = $em->getRepository(FeeItem::class)
            ->createQueryBuilder('fi')
            ->where('fi.feeName like :feeName')
            ->setParameter('feeName', '% Load fee')
            ->select('fi.feeName, fi.costTernFixed, fi.costTernRatio')
            ->getQuery()
            ->getArrayResult();
        $result = [];
        foreach ($rs as $r) {
            $name = str_replace(' Load fee','', $r['feeName']);
            if (!isset($all[$name])) {
                continue;
            }
            foreach ($all[$name] as $currency) {
                $fee = [
                    'fixed' => (int)$r['costTernFixed'],
                    'ratio' => (float)$r['costTernRatio'],
                ];
                if ($name === AlternativePaymentSupported::POLI && $currency === 'AUD') {
                    $fee['fixed'] = 75;
                }
                $result[$name . '_' . $currency] = $fee;
            }
        }
        return $result;
    }

    public static function updateCostAndRevenue(UserCardLoad $load) {
        $load->setCost(null)
            ->setRevenue(null);

        if ($load->getServerKey() === 'Imported') {
            return $load;
        }
        if (!in_array($load->getLoadStatus(), UserCardLoad::RECEIVED_STATUS_ARRAY, true)) {
            return $load;
        }

        $type = $load->getType();
        if ($type === UserCardLoad::TYPE_LOAD_CARD) {
            return self::updateCostAndRevenueForLoad($load);
        }

        if (in_array($type, [
            UserCardLoad::TYPE_UNLOAD,
            UserCardLoad::TYPE_UNLOAD_CARD_DEACTIVATED,
            UserCardLoad::TYPE_UNLOAD_CARD_CLOSED,
            UserCardLoad::TYPE_UNLOAD_ACCOUNT_CLOSED,
        ])) {
            return self::updateCostAndRevenueForUnload($load);
        }

        return $load;
    }

    protected static function updateCostAndRevenueForLoad(UserCardLoad $load)
    {
        $method = $load->getMethodName();
        if (!$method) {
            return $load;
        }

        if (Util::meta($load, 'fromUpdatePrivacyBalanceBy')) {
            return $load;
        }

        if ($method === LoadMethod::PAYNAME_SYSTEM) {
            $load->setCost($load->getLoadAmount())
                ->setRevenue(-$load->getLoadAmount());
            return $load;
        }

        if ($load->getPartner()->is(LoadPartner::COINFLOW)) {
            $cost = $load->getCost() ?? 0;
            $cost += ($load->getCommissionUSD() ?: 0);
        } else {
            $all = self::loadMethodCosts();
            $key = $method . '_' . $load->getPayCurrency();
            $costRule = $all[$key] ?? null;
            if (!$costRule) {
                $costRule = [
                    'fixed' => 0,
                    'ratio' => 0,
                ];
            }
            $cost = $costRule['fixed'] ?? 0;
            $ratio = ($costRule['ratio'] ?? 0) / 100.0;
            $cost = round($cost + ($load->getReceivedAmountUSD() + $cost) * $ratio);
            $cost += ($load->getCommissionUSD() ?: 0);
            $load->setCost($cost ?: null);
        }

        $fee = ($load->getLoadFeeUSD() ?: 0) + ($load->getMembershipFeeUSD() ?: 0);
        $load->setRevenue(($fee - $cost) ?: null);
        return $load;
    }

    protected static function updateCostAndRevenueForUnload(UserCardLoad $load)
    {
        $fee = $load->getUnloadFeeUSD() ?: 0;
        $load->setCost(null)
            ->setRevenue($fee ?: null);
        return $load;
    }
}
