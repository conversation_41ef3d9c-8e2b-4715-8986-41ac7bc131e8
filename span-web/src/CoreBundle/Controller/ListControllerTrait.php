<?php


namespace CoreBundle\Controller;


use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\QueryListParams;
use CoreBundle\Utils\Traits\DbTrait;
use CoreBundle\Utils\Traits\ExcelTrait;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Util;
use CoreBundle\Utils\Log;
use Doctrine\ORM\QueryBuilder;
use Symfony\Component\HttpFoundation\Request;

trait ListControllerTrait
{
    use DbTrait;
    use ExcelTrait;

    protected $page = 1;
    protected $limit = 10;


    public function traitSearchCached(Request $request, $orderKeys = [], $page = 1, $limit = 10)
    {
        $result = null;
        $timeout = 86400; // Default 24 hour cache

        $this->page = $page;
        $this->limit = $limit;

        $query = $this->query($request);

        $params = $this->queryListParams($query, $request);

        $params->pagination = [
            'page' => $page,
            'size' => $limit,
        ];


        // add sort
        if ($request->get('sortBy')) {
          $params->orderBy = [
            $request->get('sortBy') => $request->get('sortType')
          ];
        }
        $cacheKey = $this->createCacheKey($request, 'search', 'CORE'); // @phpstan-ignore-line

        $force = null;
        $user = $this->user ?? Util::user();
        $fisPlatform = $user->getFisCurrentPlatform();
        $cacheKey .= '.' . $fisPlatform;

        if (!$force) {
            $result = Data::getArray($cacheKey);
        }
        if (!$result) {
            $result = $this->queryListForAPIWithOrderByKeys($params, $request, $page, $limit, $orderKeys, function ($entity) {
                return $this->getRowData($entity);
            });
            Data::setArray($cacheKey, $result, $timeout);
        }

        return $result;
    }

    public function traitSearch(Request $request, $orderKeys = [], $page = 1, $limit = 10)
    {
        $this->page = $page;
        $this->limit = $limit;

        $query = $this->query($request);
        $params = $this->queryListParams($query, $request);
        $this->queryParams = $params;

        $params->pagination = [
            'page' => $page,
            'size' => $limit,
        ];
        // add sort
        if ($request->get('sortBy')) {
          $params->orderBy = [
            $request->get('sortBy') => $request->get('sortType')
          ];
        }
        $result = $this->queryListForAPIWithOrderByKeys($params, $request, $page, $limit, $orderKeys, function ($entity) {
            return $this->getRowData($entity);
        });
        return new SuccessResponse($result);
    }

    protected function query(Request $request)
    {
        return new QueryBuilder(Util::em());
    }

    protected function queryListParams(QueryBuilder $query, Request $request)
    {
        return new QueryListParams($query, $request, '', '');
    }

    protected function getRowData($entity)
    {
        return [];
    }

    public function commonExport(Request $request, $title, $headers, $textColumns = [], $dataCallback = null)
    {
        Util::longerRequest();

        $query = $this->query($request);
        $params = $this->queryListParams($query, $request);
        if ($request->get('query_count')) {
            $count = $this->queryCountForExport($params);
            return new SuccessResponse($count);
        }

        $from  = (int)$request->get('query_from', 0);
        $limit = (int)$request->get('query_limit', 5000);

        $data = $this->queryListForExport($params, $from, $limit, function ($entity) {
            return $this->getRowData($entity);
        });
        if ($dataCallback) {
            $dataCallback($data);
        }

        $timestamp = $request->get('query_timestamp', time());
        $filename = $title . '_' . date('YmdHis', $timestamp);
        $destination = Util::uploadDir('report') . $filename . '.xlsx';
        $excel = $this->loadOrCreateExcel($destination, $filename);

        $excel = $this->generateSheetAppendToExcel($excel, $from . ' ~ ' . ($from + $limit),
            $headers, $data, static function ($col, $row, $excel, $title) {
                return $row[$title] ?? '';
            }, $textColumns);
        $this->saveExcelTo($excel, $destination);

        return new SuccessResponse('report/' . $filename . '.xlsx');
    }
}
