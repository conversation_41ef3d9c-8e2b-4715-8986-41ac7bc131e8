<?php
/**
 * Created by PhpStorm.
 * User: hans
 * Date: 24/02/2018
 * Time: 11:49
 */

namespace CoreBundle\Controller\Cron;


use CoreBundle\Entity\Email;
use CoreBundle\Entity\Platform;
use CoreBundle\Services\APIServices\TwilioService;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Service;
use CoreBundle\Utils\Util;
use SalexUserBundle\Entity\User;

class BaseController extends \PortalBundle\Controller\Common\BaseController
{
    protected $protected = true;

    /**
     * BaseController constructor.
     * @throws \CoreBundle\Exception\RedirectException
     * @throws \PortalBundle\Exception\PortalException
     * @throws \UnexpectedValueException
     */
    public function __construct()
    {
        parent::__construct();

        if ($this->protected) {
            $this->authDevOrLocal();
        }

        Util::longRequest(120000, '3072M');

        if (!Util::$platform && Util::request()) {
            $path = Util::request()->getPathInfo();
            if (Util::startsWith($path, '/t/cron/usu/')) {
                Util::$platform = Platform::ternCommerce();
            } else if (Util::startsWith($path, '/t/cron/fis/')) {
                Util::$platform = Platform::fis();
            } else if (Util::startsWith($path, '/t/cron/leaflink/')) {
                Util::$platform = Platform::leafLink();
            } else if (Util::startsWith($path, '/t/cron/essolo/')) {
                Util::$platform = Platform::esSolo();
            } else if (Util::startsWith($path, '/t/cron/cow/')) {
                Util::$platform = Platform::cashOnWeb();
            } else if (Util::startsWith($path, '/t/cron/mex/')) {
                Util::$platform = Platform::transferMex();
            } else if (Util::startsWith($path, '/t/cron/spendr/')) {
                Util::$platform = Platform::spendr();
            } else if (Util::startsWith($path, '/t/cron/skux/')) {
                Util::$platform = Platform::skux();
            }
        }

        $by = Util::request()->get('__cron_by__');
        if ($by) {
            Util::$cronUser = User::find($by);
        }
    }

    protected function notifyAdmin($msg)
    {
        $msg = 'VirtualCards.us ' . $msg;

        $ts = new TwilioService();
        $info = [
            'to' => '+8618637160569',
            'msg' => $msg,
            'countryCode' => 'CN',
        ];
        $ts->sendVoiceMsg($info);
        $ts->sendSMSMsg($info);

        Service::debugEmail($msg, ['critical' => true]);
        Email::sendToAdmins('VirtualCards.us important notification', $msg);
    }

    protected function notifyDeveloper($msg)
    {
        $msg = 'VirtualCards.us ' . $msg;

        $ts = new TwilioService();
        $info = [
            'to' => '+8618637160569',
            'msg' => $msg,
            'countryCode' => 'CN',
        ];
        $ts->sendVoiceMsg($info);
        $ts->sendSMSMsg($info);

        Service::debugEmail($msg, ['critical' => true]);
        Email::sendToDevelopers('VirtualCards.us important notification', $msg);
    }
}
