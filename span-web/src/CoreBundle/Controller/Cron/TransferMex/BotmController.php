<?php


namespace CoreBundle\Controller\Cron\TransferMex;


use Carbon\Carbon;
use CoreBundle\Controller\Cron\BaseController;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\Email;
use CoreBundle\Entity\ExternalInvoke;
use CoreBundle\Entity\TransferSettlement;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Util;
use Symfony\Component\Routing\Annotation\Route;
use TransferMexBundle\Services\BotmSettleService;

class BotmController extends BaseController
{
    #[Route("/t/cron/mex/botm/update-member-transactions")]
    public function batchMigrateCardsAction()
    {
        $output = Util::executeCommand('span:mex:update-botm-transactions', [
            '--onlyPayroll' => true
        ]);
        return new SuccessResponse($output);
    }

    #[Route("/t/cron/mex/botm/ensure-business-access")]
    public function ensureBusinessAccess()
    {
        Util::executeCommand('span:mex:botm:ensure-config');
        return new SuccessResponse();
    }

    #[Route("/t/cron/mex/botm/nacha/generate/retry")]
    public function generateNacha()
    {
        if (!Data::has('botm_generate_nacha_failed')) {
            return new SuccessResponse(null, 'nothing');
        }
        $generated = BotmSettleService::generateNachaAndSendToBank(sendEmail: false);
        BotmSettleService::verifyAndAlertNachaFileGeneration($generated);
        return new SuccessResponse(null, 'retried');
    }

    #[Route("/t/cron/mex/botm/nacha/error-email/send")]
    public function sendErrorEmail()
    {
        $cacheKey = 'botm_generate_nacha_error_email';
        $cached = Data::getArray($cacheKey);
        if (!$cached || empty($cached['when'])) {
            return new SuccessResponse(null, 'nothing');
        }

        $when = Carbon::parse($cached['when']);
        if ($when->gt(Carbon::now()->subMinutes(10))) {
            return new SuccessResponse(null, 'too early');
        }

        Data::del($cacheKey);
        if ( ! Util::isLive()) {
            return new SuccessResponse(null, 'not live');
        }

        $msg = $cached['error'] ?? null;
        $settlement = !empty($cached['settlement']) ?
            Util::em()->getRepository(TransferSettlement::class)->find($cached['settlement']) :
            null;
        $ei = !empty($cached['ei']) ? ExternalInvoke::find($cached['ei']) : null;
        if (!$msg && $ei) {
            $msg = $ei->getError();
        }
        if (!$msg) {
            $msg = 'Unknown error';
        }

        $subject = 'An error when generating NACHA file in BOTM';
        if ($settlement) {
            $subject .= ' (' . $settlement->getParsedTargetType() . ' #' . $settlement->getId() . ')';
        } else {
            $subject .= ' (' . $when->setTimezone(Util::tzNewYork())->format('Y-m-d H:i') . ' ET)';
        }
        Email::sendWithTemplate([
            '<EMAIL>',
        ], Email::TEMPLATE_SIMPLE_LAYOUT, [
            'body' => '<p>Error message: <strong>' . $msg . '</strong></p>' .
                      '<p>Failed BOTM API: <strong>/files/nacha/daily-gen</strong></p>' .
                      '<p>External invoke ID: <strong>' . ($ei ? $ei->getId() : '') . '</strong></p>' .
                      '<p>HTTP status code: <strong>' . ($ei ? $ei->getStatusCode() : '') . '</strong></p>' .
                      '<p>Request details: <strong>' . ($ei ? $ei->getFormattedRequest() : '') . '</strong></p>' .
                      '<p>Response details: <strong>' . ($ei ? $ei->getFormattedResponse() : '') . '</strong></p>',
            'subject' => $subject,
            '_cc' => [
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
            ],
        ], null, CardProgram::transferMexUSD());
        return new SuccessResponse(null, 'sent');
    }
}
