<?php

namespace CoreBundle\Controller\Cron;

use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class OpCacheController extends BaseController
{
    /**
     * Reset All OpCache.
     *
     * @Route("/t/cron/opcache-reset")
     * @return Response
     * @throws \InvalidArgumentException
     */
    public function opcacheReset() {
//        opcache_reset();
        return new Response();
    }

    /**
     * Update the changed files
     *
     * @Route("/t/cron/opcache-update")
     * @return Response
     * @throws \InvalidArgumentException
     */
    public function opcacheUpdate() {
//        exec('git diff --name-only --line-prefix=`git rev-parse --show-toplevel`/ HEAD~5 HEAD', $outputs);
//        foreach ($outputs as $file) {
//            opcache_invalidate($file, true);
//        }
        return new Response();
    }
}
