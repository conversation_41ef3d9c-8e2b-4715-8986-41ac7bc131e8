<?php


namespace CoreBundle\Controller\Cron\UsUnlocked;


use Carbon\Carbon;
use CoreBundle\Controller\Cron\BaseController;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\CardProgramCardType;
use CoreBundle\Entity\Config;
use CoreBundle\Entity\FeeGlobalName;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserCardBalance;
use CoreBundle\Entity\UserFeeHistory;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Util;
use CoreBundle\Utils\Data;
use Exception;
use SalexUserBundle\Entity\User;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use UsUnlockedBundle\Services\Privacy\PrivacyAPI;
use UsUnlockedBundle\Services\Privacy\PrivacyService;
use UsUnlockedBundle\Services\SlackService;
use CoreBundle\Entity\UserCardLoad;
use UsUnlockedBundle\Entity\PayPalSubscription;
use UsUnlockedBundle\Services\CardProcessorHub;
use UsUnlockedBundle\Services\UserService;

class FeeController extends BaseController
{
    /**
     * @Route("/t/cron/usu/fee/monthly")
     *
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function monthlyFee(Request $request)
    {
        $em = Util::em();
        $expr = Util::expr();
        $cp = CardProgram::usunlocked();
        $feeName = FeeGlobalName::MONTHLY_FEE;
        $feeItem = $em->getRepository(\CoreBundle\Entity\CardProgram::class)->getFeeItem($cp, $feeName);
        if (!$feeItem || !$feeItem->hasFee()) {
            $this->logLine('No need to charge monthly fee since it\'s not enabled in card program level.');
            return new SuccessResponse();
        }
        if (Config::get('usu_disable_monthly_fee', 1) == 1) {
          return new SuccessResponse();
        }
        $this->logLine('Start to charge the monthly fee...');

        // $subQuery = $em->getRepository(UserFeeHistory::class)
        //     ->createQueryBuilder('ufh')
        //     ->join('ufh.user', 'uf')
        //     ->where('ufh.feeName = :feeName')
        //     ->andWhere('ufh.time >= :feeAt')
        //     ->andWhere($expr->orX($expr->isNull('ufh.meta'), $expr->notLike('ufh.meta', ':cowMeta')))
        //     ->select('uf.id')
        //     ->getDQL()
        // ;

        // $date = Carbon::now()->subMonthNoOverflow()->endOfDay();
        // $ucs = $em->getRepository(UserCard::class)
        //     ->createQueryBuilder('uc')
        //     ->join('uc.user', 'u')
        //     ->where('uc.createdAt > :createdAtStart')
        //     ->andWhere('uc.createdAt < :createdAtEnd')
        //     ->andWhere('uc.type = :type')
        //     ->andWhere('uc.card = :card')
        //     ->andWhere('uc.balance > :balance')
        //     ->andWhere($expr->neq('u.id', $expr->all($subQuery)))
        //     ->setParameter('feeName', $feeName)
        //     ->setParameter('feeAt', $date)
        //     ->setParameter('createdAtStart', PrivacyAPI::getStartDate())
        //     ->setParameter('createdAtEnd', $date)
        //     ->setParameter('card', CardProgramCardType::getForCardProgram($cp))
        //     ->setParameter('type', PrivacyAPI::CARD_TYPE_DUMMY)
        //     ->setParameter('balance', 0)
        //     ->setParameter('cowMeta', '%"cowMeta":true%')
        //     ->orderBy('uc.createdAt', 'asc')
        //     ->distinct()
        //     ->getQuery()
        //     ->getResult()
        // ;
         $subQuery = $em->getRepository(PayPalSubscription::class)
            ->createQueryBuilder('ps')
            ->join('ps.user', 'uf')
            ->where(Util::expr()->eq('ps.status', ':status'))
            ->select('uf.id')
            ->getDQL()
        ;
        $ucs = $em->getRepository(UserCard::class)
            ->createQueryBuilder('uc')
            ->join('uc.user', 'u')
            ->andWhere('uc.type = :type')
            ->andWhere('uc.card = :card')
            ->andWhere('uc.balance > :balance')
            ->andWhere($expr->eq('u.id', $expr->any($subQuery)))
            ->setParameter('status', PayPalSubscription::STATUS_CANCELLED)
            ->setParameter('card', CardProgramCardType::getForCardProgram($cp))
            ->setParameter('type', PrivacyAPI::CARD_TYPE_DUMMY)
            ->setParameter('balance', 0)
            ->orderBy('uc.createdAt', 'asc')
            ->distinct()
            ->getQuery()
            ->getResult();

        $this->logLine('Found ' . count($ucs) . ' users to charge the monthly fee');
        $charge = $request->get('charge', 'true') === 'true';

        $count = 0;
        /** @var UserCard $dummy */
        foreach ($ucs as $dummy) {
            /** @var User $user */
            $user = $dummy->getUser();
            $uid = $user->getId();
            $paypalSubscriptionFlag = UserService::getPayPalSubscription($user);
            if ($paypalSubscriptionFlag) {
              $this->logLine("Skip monthly fee on user " . $uid . " with reason the paypal subscription period hasn't expired yet");
              continue;
            }
            $paypalSubscription = UserService::getPreparedPayPalSubscription($user);

            $balance = $dummy->getBalance();
            $fee = $feeItem->calculate($balance, 'USD');
            if (!$fee) {
                $this->logLine('Skip monthly fee on user ' . $uid . ' with balance ' . $balance);
                continue;
            }

            if ($charge) {
                // add promo for St. Patrick's Day
                // check if the user makes a load during March 10  to March 17
                $patrickPromotionCount = (int)Data::get('patrick_promotion_count_' . $uid);
                if ($this->checkPatrickPromotion($dummy) && $patrickPromotionCount < 3) {
                    $this->logLine("Set the monthly fee to 0 on user " . $uid . " for St. Patrick's Day promotion");
                    $fee = 0;
                    $patrickPromotionCount++;
                    Data::set('patrick_promotion_count_' . $uid , $patrickPromotionCount);
                }

                // https://app.asana.com/0/0/1204486670488299/f
                $tz = Util::tzNewYork();
                $freeMonthlyCount = (int)Data::get('free_monthly_count_' . $uid);
                $newMemberDate = Carbon::create(2023,5,8,0,0,0, $tz);

                if (false && $newMemberDate->lt($user->getCreatedAt())  && $freeMonthlyCount < 3) {
                    $this->logLine("Set the monthly fee to 0 on user " . $uid . " for new member");
                    $fee = 0;
                    $freeMonthlyCount++;
                    Data::set('free_monthly_count_' . $uid , $freeMonthlyCount);
                }

                try {
                    $fee = -$dummy->updateBalanceBy(-$fee, UserCardBalance::TYPE_MONTHLY_FEE, null, null, true);
                    UserFeeHistory::createForce($user, $fee, $feeName);
                    PrivacyService::$usersChangedBalance[$uid] = $feeName;
                    $paypalSubscription->setManualAt(Carbon::now($tz))
                                      ->setManualPlanType(PayPalSubscription::PLAN_TYPE_MONTHLY)
                                      ->persist();
                    $this->logLine('Charge monthly fee on user ' . $uid . ' for '. Money::format($fee, 'USD'));
                } catch (Exception $e) {
                    $this->logExceptionLine('Failed to charge the monthly fee', $e, [
                        'user' => $uid,
                        'amount' => $fee,
                    ]);
                }
            }
            $count++;
        }

        if ($charge) {
            $em->flush();
            CardProcessorHub::updateSpendLimitForBalanceChangedUsers();
            SlackService::tada('Finished charging the monthly fee on ' . $count . ' users...');
            Util::executeCommand('span:usu:report:fee', [
                '--alert' => true,
            ]);
        }

        return new SuccessResponse();
    }

    protected function checkPatrickPromotion(UserCard $userCard) {
      $expr = Util::expr();
      $tz = Util::tzNewYork();
      $now = Carbon::now($tz);

      if (Data::get('satisfy_patrick_promotion_' . $userCard->getUser()->getId())) {
        return true;
      }

      if ($now < Carbon::create(2023,3,10,0,0,0,$tz) || $now > Carbon::create(2023,4,23,0,0,0,$tz)) {
        $this->logLine('not start');
        return false;
      }

      $res = Util::em()->getRepository(UserCardLoad::class)
                      ->createQueryBuilder('ucl')
                      ->join('ucl.userCard', 'uc')
                      ->andWhere($expr->eq('uc.id', ':userCardId'))
                      ->andWhere($expr->orX(
                        $expr->andX(
                          Util::expr()->gte('ucl.receivedAt', ':startDay'),
                          Util::expr()->lte('ucl.receivedAt', ':endDay')
                        ),
                        $expr->andX(
                          Util::expr()->gte('ucl.initializedAt', ':startDay'),
                          Util::expr()->lte('ucl.initializedAt', ':endDay'),
                          Util::expr()->lte('ucl.receivedAt', ':endReceivedDay')
                        )
                      ))
                      ->setParameter('userCardId', $userCard->getId())
                      ->setParameter('startDay', Carbon::create(2023,3,10,0,0,0,$tz))
                      ->setParameter('endDay', Carbon::create(2023,3,17,23,59,59,$tz))
                      ->setParameter('endReceivedDay', Carbon::create(2023,3,23,23,59,59,$tz))
                      ->select('count(distinct ucl)')
                      ->distinct()
                      ->getQuery()
                      ->getSingleScalarResult();

      if ($res) {
        // store a flag in redis to void check again
        Data::set('satisfy_patrick_promotion_' . $userCard->getUser()->getId(),true);
      }
      return $res;
    }
}
