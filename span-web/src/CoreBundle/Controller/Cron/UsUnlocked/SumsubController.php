<?php

namespace CoreBundle\Controller\Cron\UsUnlocked;

use CoreB<PERSON>le\Controller\Cron\BaseController;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Util;
use SalexUserBundle\Entity\User;
use Symfony\Component\Routing\Annotation\Route;
use UsUnlockedBundle\Services\SlackService;
use UsUnlockedBundle\Services\Sumsub\SumsubService;

class SumsubController extends BaseController
{
    #[Route("/t/cron/usu/sumsub/update-member/{user}")]
    public function updateSumsubResult(User $user)
    {
        $ret = SumsubService::updateMember($user, true);
        $context = [];
        if ($ret) {
            $context['error'] = $ret;
        }
        SlackService::sendUsuEvents($user,
            'Updated Sumsub KYC Status: ' . $user->getSumsubApplicantStatus(), $context);
        return new SuccessResponse([
            'user' => $user->getId(),
            'sumsubApplicantStatus' => $user->getSumsubApplicantStatus(),
            'sumsubApplicantId' => $user->getSumsubApplicantId(),
        ]);
    }
}
