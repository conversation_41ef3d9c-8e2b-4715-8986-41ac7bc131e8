<?php


namespace CoreBundle\Controller\Webhook;


use Carbon\Carbon;
use CoreBundle\Controller\BaseController;
use Core<PERSON>undle\Entity\CardProgram;
use CoreBundle\Entity\Config;
use CoreBundle\Entity\Email;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\UserCard;
use CoreBundle\Exception\FailedException;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Util;
use DevBundle\Services\SlackService;
use SalexUserBundle\Entity\User;
use SpendrBundle\Entity\SpendrMerchant;
use SpendrBundle\Services\BrazeService;
use SpendrBundle\Services\ConsumerService;
use SpendrBundle\Services\MerchantService;
use SpendrBundle\Services\PlaidService;
use SpendrBundle\Services\UserService;
use Spendr<PERSON>undle\SpendrBundle;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class PlaidController extends BaseController
{
    /**
     * Prod should be https://admin.spendr.com/webhook/plaid/bank-transfer
     * @Route("/webhook/plaid/bank-transfer")
     * @param Request $request
     * @return Response
     * @throws \CoreBundle\Exception\RedirectException
     */
    public function bankTransfer(Request $request)
    {
        $this->checkIp();
        return new SuccessResponse();
    }

    /**
     * @Route("/webhook/plaid/merchant-auth/{merchant}")
     * @param Request $request
     * @param SpendrMerchant $merchant
     * @return FailedException|Response
     */
    public function authResult(Request $request, SpendrMerchant $merchant)
    {
        $this->checkIp();
        if (!$merchant) {
            return new FailedResponse('The account does not match!');
        }
        if ($request->get('webhook_type') == PlaidService::WEBHOOOK_TYPE_AUTH) {
            if ($request->get('environment') != PlaidService::getEnv()) {
                return new FailedResponse('The environment does not match!');
            }
            $accountId = $request->get('account_id');
            $itemId = $request->get('item_id');
            $status = strtolower($request->get('webhook_code'));
            $cards = Util::em()->getRepository(UserCard::class)
                ->createQueryBuilder('uc')
                ->where('uc.user = :user')
                ->andWhere('uc.meta like :meta')
                ->setParameter('user', $merchant->getAdminUser())
                ->setParameter('meta', "%\"account_id\":\"{$accountId}\"%")
                ->getQuery()
                ->getResult();
            if (!$cards) {
                return new FailedResponse('The account does not match!');
            }
            /** @var UserCard $card */
            $card = $cards[0];
            if (Util::meta($card, 'item_id') != $itemId) {
                return new FailedResponse('The account does not match!');
            }
            if ($status == PlaidService::VERIFY_STATUS_VERIFIED ||
                $status == PlaidService::VERIFY_STATUS_MANUAL_VERIFIED) {
                $data = MerchantService::saveAuthPlaidCard($card->getPlaidAccessToken(), $merchant, $card);
                if (is_string($data)) {
                    return new SuccessResponse($data);
                }
            } elseif ($status == PlaidService::VERIFY_STATUS_EXPIRED) {
                $card->setStatus(UserCard::STATUS_INACTIVE)
                    ->persist();
                Util::updateMeta($card, [
                    'verification_status' => $status
                ]);
            }
        }
        return new SuccessResponse();
    }

    /**
     * @Route("/webhook/plaid/consumer-auth/{user}")
     * @param Request $request
     * @param User $user
     * @return Response
     */
    public function consumerAuthResult(Request $request, User $user)
    {
        $this->checkIp();
        if ($request->get('environment') != PlaidService::getEnv()) {
            return new FailedResponse('The environment does not match!');
        }
        if (!$user) {
            return new FailedResponse('The account does not match!');
        }
        if ($request->get('webhook_type') == PlaidService::WEBHOOOK_TYPE_AUTH) {
            $accountId = $request->get('account_id');
            $itemId = $request->get('item_id');
            $status = strtolower($request->get('webhook_code'));
            $cards = Util::em()->getRepository(UserCard::class)
                ->createQueryBuilder('uc')
                ->where('uc.user = :user')
                ->andWhere('uc.meta like :meta')
                ->setParameter('user', $user)
                ->setParameter('meta', "%\"account_id\":\"{$accountId}\"%")
                ->getQuery()
                ->getResult();
            if (!$cards) {
                return new FailedResponse('The account does not match!');
            }
            /** @var UserCard $card */
            $card = $cards[0];
            if (Util::meta($card, 'item_id') != $itemId) {
                return new FailedResponse('The account does not match!');
            }
            if ($status == PlaidService::VERIFY_STATUS_VERIFIED) {
                $res = ConsumerService::updatePendingCard($card->getPlaidAccessToken(), $card, $user);
                if (!is_bool($res)) {
                    return $res;
                }
//                $user->addNote('No identity was run on this user when manually link card.', true, $user->getId());

                $mask = Util::meta($card, 'mask');
                $this->sendWebhookEmail(
                    $user,
                    'Your micro-deposits are ready to verify in Spendr',
                    "Micro deposits have been added to your bank account ending in {$mask}.
                    Please log into your bank and then go to the Spendr App to verify those micro deposit amounts.
                    Once the micro deposits are verified, this bank account will be added as a deposit source into Spendr."
                );
                BrazeService::sendMessages([$user->getId()], [
                    'title' => 'Your micro-deposits are ready to verify in Spendr',
                    'message' => "Micro deposits have been added to your bank account ending in {$mask}.
                    Please log into your bank and then go to the Spendr App to verify those micro deposit amounts.
                    Once the micro deposits are verified, this bank account will be added as a deposit source into Spendr.",
                    'url' => SpendrBundle::getAppDomain() . '?path=/spendr/consumer/deposit'
                ]);
            } elseif ($status == PlaidService::VERIFY_STATUS_EXPIRED) {
                $card->setStatus(UserCard::STATUS_INACTIVE)
                    ->persist();
                Util::updateMeta($card, [
                    'verification_status' => $status
                ]);
                BrazeService::userTrack(null, [
                    'external_id' => BrazeService::getExternalPrefix() . $user->getId(),
                    'name' => 'Bank Link Failed',
                    'time' => Carbon::now()->format('Y-m-d\TH:i:s\Z'),
                    'properties' => [
                        'reason' => 'Verification expired',
                    ]
                ]);

                $this->sendWebhookEmail(
                    $user,
                    'Your bank account micro-deposits verification failed',
                    'Your bank account micro-deposits verification failed for the reason: ' . $request->get('failure_reason')['description']
                );
            }
        } elseif ($request->get('webhook_type') === PlaidService::WEBHOOK_TYPE_ITEM) {
            $itemId = $request->get('item_id');
            $cards = Util::em()->getRepository(UserCard::class)
                ->createQueryBuilder('uc')
                ->where('uc.user = :user')
                ->andWhere('uc.meta like :meta')
                ->setParameter('user', $user)
                ->setParameter('meta', "%\"item_id\":\"{$itemId}\"%")
                ->getQuery()
                ->getResult();
            if (!$cards) {
                return new FailedResponse('The account does not match!');
            }
            if ($request->get('webhook_code') === PlaidService::CODE_ERROR) {
                $error = $request->get('error');
                if ($error['error_code'] === PlaidService::ERROR_ITEM_LOGIN_REQUIRED) {
                    $ids = [];
                    $masks = [];
                    $institution = null;
                    /** @var UserCard $card */
                    foreach ($cards as $card) {
                        Util::updateMeta($card, [
                            'error' => $error['error_code']
                        ]);
                        array_push($ids, $card->getId());
                        array_push($masks, Util::meta($card, 'mask'));
                        $institution = Util::meta($card, 'institution_id') ?? $institution;
                    }
                    $pendingAmount = ConsumerService::getLoadPendingAmount($ids);
                    if ($pendingAmount > 0) {
                        SlackService::warning(
                            $user->getSignature() . ' has card Plaid token expired before Deposit load is settled',
                            (array)$error,
                            SlackService::GROUP_SPENDR_NEG_BALANCE
                        );
                        if (in_array($institution, PlaidService::TANS_INSTITUTIONS)) {
                            $mask = implode(',', $masks);
                            $this->sendWebhookEmail(
                                $user,
                                'Your Spendr bank cards auth expired',
                                "Your Spendr bank cards({$mask}) auth expired.
                                In order to avoid affecting your payment, please open the Spendr App and click the card to relink."
                            );
                            BrazeService::sendMessages([$user->getId()], [
                                'title' => 'Spendr bank cards TAN expired warning',
                                'message' => "Your Spendr bank cards({$mask}) auth expired.
                                In order to avoid affecting your payment, please open the Spendr App and click the card to relink.",
                                'url' => SpendrBundle::getAppDomain() . '?path=/spendr/consumer/deposit'
                            ]);
                        }
                    }
                }
            } elseif (in_array($request->get('webhook_code'), [
                PlaidService::CODE_PENDING_DISCONNECT,
                PlaidService::CODE_PENDING_EXPIRATION
            ])) {
                /** @var UserCard $card */
                foreach ($cards as $card) {
                    Util::updateMeta($card, [
                        'error' => $request->get('webhook_code')
                    ]);
                }
            }
        }

        return new SuccessResponse();
    }

    /**
     * @Route("/webhook/plaid/manual-link-auth")
     * @param Request $request
     * @throws FailedException
     * @throws \TomorrowIdeas\Plaid\PlaidRequestException
     */
    public function spendrAuthResult(Request $request)
    {
        $this->checkIp();
        $partner = UserService::getSpendrBalanceAdminAccount('spendr');
        if ($request->get('webhook_type') == PlaidService::WEBHOOOK_TYPE_AUTH) {
            if ($request->get('environment') != PlaidService::getEnv()) {
                return new FailedResponse('The environment does not match!');
            }
            $accountId = $request->get('account_id');
            $itemId = $request->get('item_id');
            $status = strtolower($request->get('webhook_code'));
            $cards = Util::em()->getRepository(UserCard::class)
                ->createQueryBuilder('uc')
                ->where('uc.user = :user')
                ->andWhere('uc.meta like :meta')
                ->setParameter('user', $partner)
                ->setParameter('meta', "%\"account_id\":\"{$accountId}\"%")
                ->getQuery()
                ->getResult();
            if (!$cards) {
                return new FailedResponse('The account does not match!');
            }
            /** @var UserCard $card */
            $card = $cards[0];
            if (Util::meta($card, 'item_id') != $itemId) {
                return new FailedResponse('The account does not match!');
            }
            if ($status == PlaidService::VERIFY_STATUS_VERIFIED ||
                $status == PlaidService::VERIFY_STATUS_MANUAL_VERIFIED) {
                $data = UserService::saveCardInfo($partner, $card->getPlaidAccessToken(), $card);
                if (is_string($data)) {
                    return new SuccessResponse($data);
                }
            } elseif ($status == PlaidService::VERIFY_STATUS_EXPIRED) {
                $card->setStatus(UserCard::STATUS_INACTIVE)
                    ->persist();
                Util::updateMeta($card, [
                    'verification_status' => $status
                ]);
            }
        }
        return new SuccessResponse();
    }

    protected function ipWhiteList()
    {
        return Util::em()->getRepository(\CoreBundle\Entity\Config::class)
            ->getArray(Config::CONFIG_PLAID_SPENDR_IP_WHITELIST);
    }

    /**
     * @throws FailedException
     * @throws \TomorrowIdeas\Plaid\PlaidRequestException
     */
    protected function checkIp() {
        $context = Util::isStaging() ? Util::jsonRequest() : Util::jsonSimpleRequest();
        Log::debug('Received Plaid webhook', $context);

        $request = $this->request;
        $jwt =  $request->headers->get('plaid-verification');
        if (!$jwt) {
            throw new FailedException('The source does not match!');
        }
        PlaidService::verifyWebhookKey($jwt);
    }

    protected function sendWebhookEmail(User $user, $title, $message)
    {
        $platformMeta = Util::meta(Platform::spendr());
        $supportPhone = $platformMeta['customerSupportPhone'] ?? null;
        $supportEmail = $platformMeta['customerSupportEmail'] ?? null;
        $msg = '';
        if ($supportPhone) {
            $msg .= 'Please contact Spendr Support at ' . $supportPhone;
        }
        if ($supportEmail) {
            $msg .= ' or email at ' . $supportEmail;
        }
        $msg .= ' if you have any questions.';
        Email::sendWithTemplateToUser(
            $user,
            Email::TEMPLATE_SIMPLE_LAYOUT,
            [
                'subject' => $title,
                'body' => "<p>Hello {$user->getFullName()},</p><br/><br/>
                            <p>{$message}</p><p>{$msg}</p><br/><br/>
                            <p>Rewardingly Yours,</p><br/><p>The Spendr Team</p>"
            ],
            CardProgram::spendr()
        );
    }
}
