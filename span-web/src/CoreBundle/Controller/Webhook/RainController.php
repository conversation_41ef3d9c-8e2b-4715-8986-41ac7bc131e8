<?php

namespace CoreBundle\Controller\Webhook;

use Carbon\Carbon;
use CoreBundle\Controller\BaseController;
use CoreBundle\Entity\Config;
use CoreBundle\Entity\Email;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\UserCard;
use CoreBundle\Exception\FailedException;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Services\MerchantService;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Util;
use SalexUserBundle\Entity\User;
use SalexUserBundle\Entity\UserConfig;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use UsUnlockedBundle\Entity\PayPalSubscription;
use UsUnlockedBundle\Entity\RainWebHookRecords;
use UsUnlockedBundle\Services\CardProcessorHub;
use UsUnlockedBundle\Services\Privacy\PrivacyAPI;
use UsUnlockedBundle\Services\Rain\RainAPI;
use UsUnlockedBundle\Services\Rain\RainService;
use UsUnlockedBundle\Services\SlackService;
use UsUnlockedBundle\Services\SpendingService;
use UsUnlockedBundle\Services\UserService;


class RainController extends BaseController
{
    private array $context = [];

    /**
     * @Route("/webhook/rain")
     * @param Request $request
     *
     * @return Response
     */
    public function rainWebhook(Request $request)
    {
        Log::debug('Got Rain webhook', Util::jsonRequest($request));

        Util::$platform = Platform::ternCommerce();
        $body = Util::s2j($request->getContent());
        if (!isset($body['resource']) || empty($body['id'])) {
            throw $this->throwError('Invalid resource/id in the webhook');
        }
        $this->context = [
            'webhookId' => $body['id'],
        ];
        $resource = $body['resource'];
        $data = $body['body'] ?? [];
        $rainUserId = null;
        $api = RainAPI::get();
        $signature = hash_hmac('sha256', $request->getContent(), $api->apiKey);
        $headers = $request->headers->all();
        if (Util::isStaging()) {
            Log::debug('Rain Webhook Headers', $headers);
            Log::debug('Rain Webhook body: ' . $request->getContent());
            Log::debug($signature);
        }
        $actualSignature = $headers['signature'][0] ?? '';
        if ($signature !== $actualSignature) {
            $this->context['signature'] = [
                'expected' => substr($signature, -6),
                'actual' => $actualSignature,
            ];
            throw $this->throwError('Invalid signature');
        }
        $action = $body['action'];

        RainWebHookRecords::storeRecord($action, $resource, $data);
        if ($resource === 'transaction') {
          $type = $data['type'];
          if ($type !== 'spend') {
              $this->sendSlackMessage('Unaccepted transaction types from Rain Webhook!', [
                  'type' => $type,
                  'webhookId' => $body['id'],
              ]);
              if (!in_array($type , ['spend', 'collateral'])) {
                  throw $this->throwError('Unaccepted transaction types');
              }
          }
          $rainUserId = $data[$data['type']]['userId'] ?? NULL;
        } else if ($resource === 'card') {
          $rainUserId = $data['userId'];
        } else if ($resource === 'user') {
          $rainUserId = $data['id'];
        }
        if (!$rainUserId) {
            throw $this->throwError('Rain User ID not found');
        }
        $config = UserConfig::findByRainUserId($rainUserId);
        if (!$config) {
          return  new SuccessResponse([], 'Rain User not found');
        }
        $user = $config->getUser();
        if ($resource === 'user') {
          RainService::updateRainAccount($user);
        } else if ($resource === 'transaction') {
          $this->fillTransaction($user, $data, $action);
        }
//        else if ($resource === 'card') {
//
//        }
        return new SuccessResponse([], 'Success');
    }

    public function fillTransaction(User $user, $data, $action)
    {
        if ($action === 'requested') {
            // ------------------------------- Approve for refund directly -------------------------
            $amount = $data['spend']['amount'] ?? 0;
            if ($amount <= 0) {
                SlackService::sendUsuEvents($user, 'Deny Transaction', 'Rain webhook approval for non-positive amount ' . $amount);
                Log::debug('Rain webhook approval for non-positive amount ' . $amount, $this->context);
                return;
            }

            $errorKey = 'rain_transaction_' . $data['id'];
            if ($amount > 350000) { // 3500 USD
                throw $this->throwError('Too large amount', $errorKey, [
                    'amount' => $amount,
                ], $user);
            }

            // ------------------------------- Verify account balance -----------------------------
            // check the balance and add the transaction
            Log::debug('Check balance of the user ' . $user->getId() . ' for the transaction.', $this->context);
            $balance  = $user->getUsuBalanceOnly();
            if ($balance < $amount) {
                throw $this->throwError('Insufficient balance', $errorKey, [
                    'balance' => $balance,
                ], $user);
            }

            // ------------------------------- Verify user status -----------------------------
            if (!$user->isActive()) {
                throw $this->throwError('Inactive user status', $errorKey, [], $user);
            }

            if (!$user->isUsuSumSubIdVerified()) {
                throw $this->throwError('Invalid KYC status', $errorKey, [], $user);
            }

            if (Email::isUserSkipped($user)) {
                throw $this->throwError('User or country restricted', $errorKey, [], $user);
            }

//            if (!$user->isUsuIdVerifiedAndValid()) {
//                throw $this->throwError('Invalid ID verification', $errorKey, [], $user);
//            }

            // -------------------------- Verify merchant and transaction ---------------------
            if (
                isset($data['spend']['merchantCountry']) &&
                trim($data['spend']['merchantCountry']) &&
                trim($data['spend']['merchantCountry']) !== 'US'
            ) {
                throw $this->throwError('Non-US merchant', $errorKey, [], $user);
            }

            $key = 'rain_transaction_create_flag_' . $user->getId();
            if (Data::lockedHasOnly($key)) {
                throw $this->throwError('Frequent transaction', $errorKey, [], $user);
            }

            if ($amount > 3000) { // 30 USD
                Data::set($key, time(), TRUE, 5);
            }
            $uc = UserCard::findByRainCardId(trim($data['spend']['cardId']));
            if (!$uc) {
                throw $this->throwError('Unknown Rain card', $errorKey, [], $user);
            }
            if (!$uc->isActiveStatus()) {
                throw $this->throwError('Inactive card', $errorKey, [], $user);
            }

            $spendLimit = $uc->getSpendLimit();
            if ($spendLimit !== null && $amount > $spendLimit) {
                throw $this->throwError('Exceed spending limit', $errorKey, [], $user);
            }

            $merchant = $uc->getMerchant();
            if ($merchant && $uc->getType() === PrivacyAPI::CARD_TYPE_SINGLE_USE) {
                throw $this->throwError('One-time card reused', $errorKey, [], $user);
            }

            if (
                $merchant && $uc->getType() === PrivacyAPI::CARD_TYPE_MERCHANT_LOCKED &&
                !RainService::isTheSameMerchant($merchant, $data['spend'])
            ) {
                throw $this->throwError('Merchant and card unmatched', $errorKey, [
                    'expected' => $merchant->getName(),
                    'expectedMerchantId' => $merchant->getRainId(),
                    'expectedOriginal' => $merchant->getOriginalName(),
                    'expectedEnriched' => $merchant->getEnrichedName(),
                    'actualMerchantId' => $data['spend']['merchantId'] ?? null,
                    'actualMerchantName' => $data['spend']['merchantName'] ?? null,
                ], $user);
            }

            $newMerchant = MerchantService::instanceFromRain($data['spend']);
            if ($newMerchant && $newMerchant->isBlocked()) {
                throw $this->throwError('Blocked merchant', $errorKey, [
                    'merchant' => $newMerchant->getName(),
                    'merchantId' => $newMerchant->getId(),
                    'merchantType' => $newMerchant->getMerchantTypeName(),
                    'merchantTypeBlocked' => $newMerchant->isTypeBlocked(),
                ], $user);
            }

            $limit = SpendingService::checkLimit($user);
            if (is_array($limit) && !empty($limit['type'])) {
                throw $this->throwError('Reach ' . strtolower(Util::humanize($limit['type'])),
                    $errorKey, $limit, $user);
            }

            // --------------------------- Verify PayPal subscription -------------------------
            if ((int)Config::get('enable_paypal_subscription', 1) === 1) {
                $paypalSubscription = UserService::getPreparedPayPalSubscription($uc->getUser());
                if (!$paypalSubscription) {
                    throw $this->throwError('No PayPal subscription', $errorKey, [], $user);
                }
                $subscriptionCreated = Carbon::instance($paypalSubscription->getStartAt());
                $now                 = Carbon::now();
                $month               = 1;
                if ($paypalSubscription->getCurrentPlanType() === PayPalSubscription::PLAN_TYPE_ANNUALLY) {
                    $month = 12;
                }
                if ($paypalSubscription->getStatus() === 'CANCELLED' && $subscriptionCreated->addMonths($month)->lte($now)) {
                    throw $this->throwError('PayPal subscription expired', $errorKey, [], $user);
                }
            }
            Log::debug('Rain webhook approval', $this->context);
            return;
        }

        if (Util::isStaging()) {
            Log::debug('Start to fill the rain transaction', $this->context);
            Log::debug('Data', $data);
        }
        // store rain webhook
        RainService::fillTransactions([$data]);
        CardProcessorHub::updateSpendLimitForBalanceChangedUsers();
        RainService::updateTransactionsListCache();
    }

    public function throwError(string $error, string $errorKey = null, array $context = [], User $user = null): \Throwable
    {
        Log::warn('Rain webhook error result: ' . $error, array_merge($this->context, $context));
        if ($errorKey) {
            Data::set($errorKey, $error);
        }
        if ($user) {
          SlackService::sendUsuEvents($user, 'Deny Transaction', $error);
        }
        return new FailedException($error, $context);
    }

    public function sendSlackMessage(string $error, $context) {
        SlackService::warning($error, $context, SlackService::GROUP_DEV);
    }
}
