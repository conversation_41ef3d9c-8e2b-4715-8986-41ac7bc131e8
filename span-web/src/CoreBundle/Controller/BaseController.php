<?php

namespace CoreBundle\Controller;

use Carbon\Carbon;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserToken;
use CoreBundle\Exception\FailedException;
use CoreBundle\Exception\RedirectException;
use CoreBundle\Services\UserService;
use CoreBundle\Utils\Bundle;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Security;
use CoreBundle\Utils\Session;
use CoreBundle\Utils\Util;
use Doctrine\Common\Annotations\AnnotationReader;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\QueryBuilder;
use OpenApi\Annotations\Operation;
use OpenApi\Annotations\Schema;
use OpenApi\Generator;
use PortalBundle\Exception\PortalException;
use ReflectionMethod;
use SalexUserBundle\Entity\User;
use Symfony\Component\HttpFoundation\Exception\JsonException;
use Symfony\Component\HttpFoundation\ParameterBag;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Security\Core\User\UserInterface;

class BaseController extends \App\Controller\BaseController
{
    /**
     * @var Request
     */
    public $request;

    /**
     * @var EntityManager
     */
    public $em;

    /**
     * @var Platform
     */
    public $platform;

    /**
     * @var CardProgram
     */
    public $cardProgram;

    public function __construct()
    {
        parent::__construct();

        if (!$this->container) {
            $this->container = Util::$container;
        }

        $this->request = Util::request(true);
        $this->em = Util::em();
        $this->platform = Util::platform();
        $this->cardProgram = Util::cardProgram();

        $path = $this->request->getPathInfo();
        if (
            $this->platform &&
            $this->platform->isSpendr() &&
            Util::getParameter('spendr_maintenance', true) &&
            !Util::hasPrefix($path, [
                '/spendr/download/batch/',
            ]) &&
            !Util::isTrustedDevIp() &&
            !array_intersect(['*************', '*************'], Security::getClientIps())
        ) {
            $msg = 'Spendr is inaccessible right now due to scheduled maintenance happening until 8am EST. Our support team will be available at 8am to answer any questions and can be reached at 513-440-1590 or <EMAIL>';
            if ($this->request->headers->has('User-Agent')) {
                throw PortalException::tempPage($msg);
            }
            throw new FailedException($msg);
        }

        if (Util::startsWith($path, '/t/cron/spendr/') && Util::getParameter('spendr_maintenance', true)) {
            throw new PortalException('Spendr is under planned maintenance now.');
        }

        // https://shinetechchina.atlassian.net/browse/TCSPAN2-205
        $user = Util::invoker();
        while ($user) {
            if (in_array($path, [
                '/login',
                '/consumercheck',
                '/session-timeout',
                '/logout',
                '/resume-session',
                '/detect-session',
                '/locator',
            ], true)) {
                break;
            }

            $sessionKey = 'session_at_' . $user->getId();
            $pSessionKey = Session::getPlatformActiveSessionKey() . $user->getId();
            $timestamp = time();

            // Fix https://shinetechchina.atlassian.net/browse/TCSPAN2-420
            $impersonatingUser = Util::getImpersonatingUser();
            if ($impersonatingUser) {
                Data::set($sessionKey, $timestamp);
                Data::set($pSessionKey, $timestamp);
                Data::set('session_at_' . $impersonatingUser->getId(), $timestamp);
                Data::set(Session::getPlatformActiveSessionKey() . $impersonatingUser->getId(), $timestamp);
                break;
            }

            if (Util::isAPI() || Util::isCronJob()) {
                Data::set($sessionKey, $timestamp);
                Data::set($pSessionKey, $timestamp);
                break;
            }

            //
            // Choose/Redirect domain if it's not super admin
            //
            if (!$this->request->isXmlHttpRequest() && !$user->isMasterAdmin()) {
                $platforms = $user->getParticipatedPlatforms();
                $found = false;
                if ($platforms->isEmpty()) {
                    if (Util::startsWith($this->request->getRequestUri(), '/admin')) {
                        $to = '/logout';
                        $button = 'Log out';
                        $msg = 'Your account does not bind to any platforms. Please contact admin. ';
                        throw new PortalException($msg, $to, $button, false);
                    }
                    if (Bundle::isRoot()) {
                        $platforms = new ArrayCollection([
                            Platform::ternCommerce(),
                        ]);
                    } else {
                        $found = true;
                    }
                }
                if (!$found) {
                    $host = $this->request->getHost();
                    /** @var Platform $platform */
                    foreach ($platforms as $platform) {
                        $domain = $platform->getDomain();
                        $domains = $platform->getAllCustomDomains();
                        if ($host === $domain || in_array($host, $domains)) {
                            $found = true;
                            break;
                        }
                        $domain = $platform->getSubDomainFull();
                        if ($domain === $host) {
                            $found = true;
                            break;
                        }
                    }
                }

                // The current accessing domain is not the platforms he can access, redirect...
                if (!$found) {
                    if ($this->platform && $this->platform->isCashOnWeb()) {
                        $to = '/logout';
                        $button = 'Log out';
                        $msg = 'Your account does not bind to the platform. Please register new one. ';
                        throw new PortalException($msg, $to, $button, false);
                    }

                    $platform = $platforms->first();
                    $url = $platform->host();
                    throw new RedirectException($url);
                }
            }

            //
            // Check if the session is timeout
            //
            if (Util::isDev()) {
                Data::set($sessionKey, $timestamp);
                Data::set($pSessionKey, $timestamp);
                break;
            }

            $last = Data::get($sessionKey);
            if ($last) {
                $last = Carbon::createFromTimestamp($last);
                $limit = Carbon::now()->subMinutes(20);
                if ($last->lt($limit)) {
                    if ($this->request->isXmlHttpRequest()) {
                        throw new FailedException('Session is timeout! Please login again.', null, 401);
                    }
                    throw new RedirectException('/session-timeout');
                }
            }
            Data::set($sessionKey, $timestamp);
            Data::set($pSessionKey, $timestamp);
            break;
        }
    }

    /**
     * @return UserToken|null
     */
    protected function getApiUserToken()
    {
        $key = 'x-token';
        $token = $this->request->headers->get($key);
        if (!$token) {
            $token = $this->request->get($key);
        }
        if (!$token) {
            return null;
        }
        $token = str_replace('Bearer ', '', $token);
        $rs = $this->em->getRepository(\CoreBundle\Entity\UserToken::class)->findBy([
            'token' => $token,
        ], null, 1);
        if (!$rs) {
            return null;
        }
        /** @var UserToken $ut */
        $ut = $rs[0];
        if (!$ut || !$ut->canAccessAPI()) {
            return null;
        }
        return $ut;
    }

    /**
     * @return User
     */
    protected function getUser(): ?UserInterface
    {
        $api = Util::isAPI();
        $uri = Util::request()->getRequestUri();
        $profile = Util::hasPrefix($uri, [
            '/admin/user/profile',
            '/admin/country/',
            '/attachments/',
        ]);

        $selfCronAdmins = Util::hasPrefix($uri, [
            '/admin/mex/',
        ]) && (Util::isSelfRequest() || Util::isDev());

        if ($api || $profile || $selfCronAdmins || Util::isPrivacyRequest()) {
            $ut = $this->getApiUserToken();
            if ($api && (!$ut || $ut->isExpired())) {
                $path = Util::request()->getPathInfo();
                // The attachments interface should read the cookie as a fallback
                if ($path !== '/attachments') {
                    return null;
                }
            }
            if ($ut) {
                $userId = $ut->decodeUserId();
                if ($userId) {
                    $user = $this->em->getRepository(\SalexUserBundle\Entity\User::class)->find($userId);

                    if (!Util::$user) {
                        Util::$user = $user;
                    }

                    if ($user || $api) {
                        return $user;
                    }
                }
            }
        }

        if (Util::isCronJob() || Util::isCommand()) {
            return Util::getDefaultMasterAdmin();
        }

        try {
            $user = Util::$security->getUser();
            if (!$user) {
                $user = parent::getUser();
            }
            return $user;
        } catch (\LogicException $exception) {
            return null;
        }
    }

    protected function render($view, array $parameters = array(), Response $response = null): Response
    {
        $user = $this->getUser();
        $request = Util::request();
        if ($user && !$user->isSuperAdmin()) {
            if (!$request->isXmlHttpRequest()) {
                $path = $request->getPathInfo();

                $lastRequest = Util::s2j($user->getLastRequest());
                $last = $lastRequest['route'] ?? '';
                if ($path !== $last) {
                    $user->setLastRequest(Util::j2s([
                        'route' => $path,
                        'times' => 1,
                        'ago' => time(),
                    ]));
                } else {
                    $now = time();
                    $ago = $lastRequest['ago'] ?? $now;
                    if ($now - $ago < 60) {
                        $times = $lastRequest['times'] ?? 1;
                        if ($times > 10) {
                            throw PortalException::tempPage('Request is too frequent! Please try again later.');
                        }
                    } else {
                        $times = 0;
                    }
                    $user->setLastRequest(Util::j2s([
                        'route' => $path,
                        'times' => $times + 1,
                        'ago' => $now,
                    ]));
                }

                // Util::persist($user);
            }
        }

        Util::fillUserVariablesToRender($parameters);
        Util::fillPlatformVariablesToRender($parameters);
        Util::fillAffiliateVariablesToRender($parameters);

        $view = Bundle::replaceRenderView($view);

        $newResponse = parent::render($view, $parameters, $response);
        return Util::pageResponseForPreview($newResponse);
    }

    // https://shinetechchina.atlassian.net/browse/TCSPAN2-78
    protected function skipCardProgram(UserCard $userCard)
    {
        if (!$userCard->getId() && Util::isDemo()) {
            return false;
        }

        $cp = $userCard->getCard()->getCardProgram();
        if (Util::isAPI()) {
            $allCps = Util::invoker()->getOpenCardPrograms(true);
            return !Util::includes($allCps, $cp);
        }
        if (!$this->cardProgram) {
            if ($userCard->isUsUnlocked()) {
                return true;
            }
            return false;
        }
        if (!Util::eq($cp, $this->cardProgram)) {
            return true;
        }
        return false;
    }

    protected function authCsrfToken($field = '_csrf_token', $id = 'authenticate')
    {
        $token = $this->request->get($field);
        if (!$this->isCsrfTokenValid($id, $token)) {
            throw new PortalException('Form is invalid or expired!');
        }
    }

    protected function fixRequestMethod($requestMethod)
    {
        if ($this->request->getMethod() === 'GET') {
            $requestMethod = 'query';
        }
        return $requestMethod;
    }

    protected function getApiUser()
    {
        if (property_exists($this, 'user')) {
            return $this->user;
        }
        return $this->getUser();
    }

    protected function validateParameters($method, $requestMethod = 'request')
    {
        $request = $this->request;
        $requestMethod = $this->fixRequestMethod($requestMethod);

        $reader = new AnnotationReader();
        $refMethod = new ReflectionMethod($method);
        $annotations = $reader->getMethodAnnotations($refMethod);
        foreach ($annotations as $annotation) {
            if (!$annotation instanceof Operation) {
                continue;
            }

            if ( ! Generator::isDefault($annotation->parameters)) {
                foreach ($annotation->parameters as $parameter) {
                    $key = $parameter->name;

                    if ($parameter->required && !Generator::isDefault($parameter->required)) {
                        if (!$request->$requestMethod->has($key)) {
                            throw PortalException::create('Parameter \'' . $key . '\' cannot be omitted!');
                        }
                    }

                    if ($request->$requestMethod->has($key)) {
                        $value = $request->$requestMethod->get($key);

                        if ($parameter->required && $value === '' && !Generator::isDefault($parameter->required)) {
                            throw PortalException::create('Parameter \'' . $key . '\' cannot be empty!');
                        }

                        $this->validateSchema($request, $requestMethod, $key, $value, $parameter->schema);
                    }
                }
            }

            if ( ! Generator::isDefault($annotation->requestBody) && ! Generator::isDefault($annotation->requestBody->content)) {
                $mediaType = $annotation->requestBody->content[0] ?? null;
                if ($mediaType && ! Generator::isDefault($mediaType) && ! Generator::isDefault($mediaType->schema)) {
                    $schema = $mediaType->schema;
                    if ($schema->required && ! Generator::isDefault($schema->required)) {
                        $required = (array)$schema->required;
                        foreach ($required as $key) {
                            if (!$request->$requestMethod->has($key)) {
                                throw PortalException::create('The parameter \'' . $key . '\' is required.');
                            }
                        }

                        if ( ! Generator::isDefault($schema->properties)) {
                            foreach ($schema->properties as $property) {
                                $key = $property->property;
                                if ( ! Generator::isDefault($key) || ! $request->$requestMethod->has($key)) {
                                    continue;
                                }
                                $value = $request->$requestMethod->get($key);
                                if ($value === '' && in_array($key, $required)) {
                                    throw PortalException::create('Parameter \'' . $key . '\' cannot be empty!');
                                }

                                $this->validateSchema($request, $requestMethod, $key, $value, $property);
                            }
                        }
                    }
                }
            }

            if (isset($annotation->security[0]['token'])) {
                if (!$this->getApiUser()) {
                    throw PortalException::create('Invalid user token!');
                }
            }
        }

        return $request;
    }

    protected function validateSchema(Request $request, string $requestMethod, $key, $value, Schema $schema)
    {
        if (!Generator::isDefault($schema->enum)) {
            if (!in_array($value, $schema->enum, true)) {
                throw PortalException::create('Parameter ' . $key
                                              . ' is invalid! Valid values: ' . implode(', ', $schema->enum));
            }
        }

        if ($schema->type === 'integer') {
            $value = (int)$value;

            if (!Generator::isDefault($schema->minimum) && $value < $schema->minimum) {
                throw PortalException::create('Parameter ' . $key
                                              . ' is invalid! Minimum value: ' . $schema->minimum);
            }

            if (!Generator::isDefault($schema->maximum) && $value > $schema->maximum) {
                throw PortalException::create('Parameter ' . $key
                                              . ' is invalid! Maximum value: ' . $schema->maximum);
            }
        }

        if ($schema->type === 'boolean') {
            $paramArray = $request->$requestMethod->all();
            $boolValue = filter_var($value, FILTER_VALIDATE_BOOLEAN);
            $paramArray[$key] = $boolValue;
            $request->$requestMethod->replace($paramArray);
        }

        if ($schema->type === 'string') {
            if ($schema->format === 'email') {
                $filtered = filter_var($value, FILTER_VALIDATE_EMAIL);
                if (!$filtered || $filtered !== $value) {
                    throw PortalException::create('Parameter ' . $key
                                                  . ' is invalid! Format: ' . $schema->format);
                }
            }

            if ($schema->format === 'password') {
                $checked = UserService::checkNewPasswordSecurity($value, $this->getApiUser());
                if (is_string($checked)) {
                    $msg = 'Parameter ' . $key . ' is invalid! ' . $checked;
                    throw PortalException::create($msg);
                }
            }

            if ($schema->format === 'date') {
                $matches = [];
                preg_match('/^\d{4}-\d{2}-\d{2}$/',
                    $value, $matches);
                if (!$matches) {
                    throw PortalException::create('Parameter ' . $key
                                                  . ' is invalid! Format: YYYY-MM-DD.');
                }
            }

            if ($schema->format === 'dateTime') {
                try {
                    new \DateTime($value);
                } catch(\Exception $exception) {
                    throw PortalException::create('Parameter ' . $key
                                                  . ' is invalid! See ISO8601 full-time format.');
                }
            }

            if (!Generator::isDefault($schema->minLength) && strlen($value) < $schema->minLength) {
                throw PortalException::create('Parameter ' . $key . ' requires the minimum length ' . $schema->minLength . '! ');
            }

            if (!Generator::isDefault($schema->maxLength) && strlen($value) > $schema->maxLength) {
                throw PortalException::create('Parameter ' . $key . ' exceeds the maximum length ' . $schema->maxLength . '! ');
            }
        }
    }

    protected function validateTimeRange($startField, $endField, $maxDays = null, $startOfDay = true, $requestMethod = 'request')
    {
        $request = $this->request;
        $requestMethod = $this->fixRequestMethod($requestMethod);

        $startValue = $request->$requestMethod->get($startField);
        $endValue = $request->$requestMethod->get($endField);

        $tz = Util::timezone();
        $start = is_string($startValue) ? new Carbon($startValue, $tz) : Carbon::createFromTimestamp($startValue, $tz);
        $end = is_string($endValue) ? new Carbon($endValue, $tz) : Carbon::createFromTimestamp($endValue, $tz);

        if ($start->gt($end)) {
            throw PortalException::temp($startField . ' should be earlier than ' . $endField);
        }

        if ($maxDays !== null && $start->diffInDays($end) > $maxDays) {
            throw PortalException::temp($startField . ' cannot be ' . $maxDays . ' days earlier than ' . $endField);
        }

        if ($startOfDay) {
            $start->startOfDay();
            $end->startOfDay();
        }

        return [$start, $end];
    }

    protected function setDefaultParams(array $values, Request $request = null, $requestMethod = 'request')
    {
        if (!$request) {
            $request = $this->request;
        }
        $requestMethod = $this->fixRequestMethod($requestMethod);
        /** @var ParameterBag $bag */
        $bag = $request->$requestMethod;
        foreach ($values as $field => $value) {
            if (!$bag->has($field)) {
                $bag->set($field, $value);
            }
        }
    }

    protected function copyParam($fromField, $toField, ParameterBag $bag)
    {
        if ($bag->has($fromField)) {
            $bag->set($toField, $bag->get($fromField));
        }
    }

    protected function authDevOrLocal()
    {
        Util::authDevOrLocal();
    }

    protected function queryAdminsInCurrentPlatform(QueryBuilder $q, $alias = 'u')
    {
        return Util::queryAdminsInCurrentPlatform($q, $alias);
    }

    protected function queryMembersInCurrentPlatform(QueryBuilder $q, $alias = 'u')
    {
        return Util::queryMembersInCurrentPlatform($q, $alias);
    }

    public function getSession(Request $request = null)
    {
        $request = $request ?? $this->request ?? Util::request();
        if ($request->hasSession()) {
            return $request->getSession();
        }
        return null;
    }

    public function getSessionValue(string $key, $default = null, Request $request = null)
    {
        $session = $this->getSession($request);
        if (!$session) {
            return $default;
        }
        return $session->get($key, $default);
    }

    public function aem($manager = 'analytics')
    {
        return Util::aem($manager);
    }

    public function getPostData($key, $value = null) {
        $request = $this->request ?? Util::request();
        if ($request->request && $request->request->has($key)) {
            try {
                return $request->request->get($key);
            } catch (\Throwable) {}
        }
        if ($request->getMethod() === 'POST') {
            try {
                $content = $request->toArray();
                if (array_key_exists($key, $content)) {
                    return $content[$key];
                }
            } catch (JsonException) {}
        }

        return $request->get($key, $value);
    }
}
