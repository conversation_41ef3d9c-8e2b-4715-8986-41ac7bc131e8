<?php

namespace DevBundle\Command;

use Carbon\Carbon;
use CoreBundle\Command\BaseCommand;
use CoreBundle\Command\BaseHostedCommand;
use CoreBundle\Constant\GlobalCollectSupported;
use CoreBundle\Entity\Attachment;
use CoreBundle\Entity\BaseState;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\CardProgramCardType;
use CoreBundle\Entity\Config;
use CoreBundle\Entity\Country;
use CoreBundle\Entity\Email;
use CoreBundle\Entity\EntitySignature;
use CoreBundle\Entity\ExternalInvoke;
use CoreBundle\Entity\InactiveEmail;
use CoreBundle\Entity\MerchantType;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\RapidOperationQueue;
use CoreBundle\Entity\RapidTransaction;
use CoreBundle\Entity\RapydTransaction;
use CoreBundle\Entity\Transfer;
use CoreBundle\Entity\TransferSettlement;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Entity\UserCardPurse;
use CoreBundle\Entity\UserCardTransaction;
use CoreBundle\Entity\UserGroup;
use CoreBundle\Entity\UserVelocity;
use CoreBundle\Entity\Velocity;
use CoreBundle\Services\APIServices\AlternativePaymentService;
use CoreBundle\Services\AuthService;
use CoreBundle\Services\Google\GoogleSheetService;
use CoreBundle\Services\LoadCardService;
use CoreBundle\Services\MerchantService;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\JwtUtil;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Security;
use CoreBundle\Utils\Util;
use Doctrine\DBAL\Types\Type;
use FaasBundle\Services\BOTM\BotmAPI;
use FisBundle\Services\Reward\PrepaidAPI;
use libphonenumber\PhoneNumberUtil;
use Nacha\Batch;
use phpseclib\Crypt\RSA;
use phpseclib\Net\SFTP;
use PortalBundle\Exception\PortalException;
use PragmaRX\Google2FA\Google2FA;
use Predis\Command\Redis\STRLEN;
use SalexUserBundle\Entity\User;
use SalexUserBundle\Entity\UserConfig;
use SkuxBundle\Services\SkuxService;
use Stringy\Stringy;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Intl\Locales;
use TransferMexBundle\Entity\MonthlySnapshotReport;
use TransferMexBundle\Services\EmployerService;
use TransferMexBundle\Services\RapidAPI;
use TransferMexBundle\Services\RapidService;
use TransferMexBundle\Services\RapydDisburseService;
use TransferMexBundle\Services\SlackService;
use TransferMexBundle\Services\TransferService;
use TransferMexBundle\Services\UniTellerRemittanceService;
use TransferMexBundle\TransferMexBundle;
use UsUnlockedBundle\Services\IDologyService;
use UsUnlockedBundle\Services\Mautic\MauticService;
use UsUnlockedBundle\Services\Privacy\PrivacyAPI;
use UsUnlockedBundle\Services\Privacy\PrivacyService;
use UsUnlockedBundle\Services\Rain\RainService;
use function Functional\pluck;
use TransferMexBundle\Services\MemberService;
use CoreBundle\Utils\Excel;
use TransferMexBundle\Entity\ImportMembersRecord;
use TransferMexBundle\Services\IntermexRemittanceService;
use TransferMexBundle\Services\ManualSettleService;
use GuzzleHttp\Psr7\Request;
use Illuminate\Support\Js;

class TestCommand extends BaseHostedCommand
{
    protected $singletonMinutes = 10;

    protected function configure()
    {
        $this
            ->setName('span:dev:test')
            ->setDescription('Temporary test command')
            ->addOption('method', null, InputOption::VALUE_REQUIRED, 'Custom method to execute')
            ->addOption('sleep', null, InputOption::VALUE_OPTIONAL, 'Sleep how many seconds', 3)
            ->addOption('force', null, InputOption::VALUE_NONE, 'Additional param to test force')
        ;
    }

    public function getSingletonKey()
    {
        return $this->getName() . '_' . $this->input->getOption('method');
    }

    protected function beforePrepare()
    {
        Email::$disabled = true;
    }

    protected function hostedExecute()
    {
        $method = $this->input->getOption('method');
        if ($method) {
            $this->$method();
            return;
        }

        $sleep = (int)$this->input->getOption('sleep');
        for ($i = 0; $i < $sleep; $i++) {
            sleep(1);
            $this->line('Sleep ' . ($i + 1));
        }
    }

    protected function testCountryCallingCode()
    {
        $country = Country::findByCode('DO');
        $this->line(Util::j2se($country->toApiArray()));

        $phoneUtil = PhoneNumberUtil::getInstance();
        $code = $phoneUtil->getCountryCodeForRegion($country->getIsoCode());
        $this->line('Code: ' . $code);
    }

    protected function migrateInactiveEmailFromConfigToTable()
    {
        $all = Config::array(Config::CONFIG_INACTIVE_EMAILS);
        foreach ($all as $e) {
            InactiveEmail::ensure($e);
        }
    }

    protected function testHashBase64()
    {
        $a = date('c');
        $this->line($a);
        $this->line(Util::hash($a));
        $this->line(Util::hash($a, base64: true));
    }

    protected function listLocales()
    {
        dd(Locales::getNames());
    }

    protected function listTimezones()
    {
        dd(\DateTimeZone::listIdentifiers());
    }

    protected function exportCountriesForMautic()
    {
        $all = Util::em()->getRepository(Country::class)
            ->createQueryBuilder('c')
            ->select('c.name')
            ->orderBy('c.name', 'ASC')
            ->getQuery()
            ->getArrayResult();
        $result = [];
        foreach ($all as $c) {
            $result[] = $c['name'];
        }
        $path = Util::secureDir('mautic') . 'countries.json';
        file_put_contents($path, Util::formatJSON($result));
        $this->line('Output countries to ' . $path);
        return $result;
    }

    protected function exportRegionsForMautic()
    {
        $result = [];
        $countries = Util::em()->getRepository(Country::class)
            ->createQueryBuilder('c')
            ->orderBy('c.name', 'ASC')
            ->getQuery()
            ->getResult();
        /** @var Country $c */
        foreach ($countries as $c) {
            $cn = $c->getName();
            $result[$cn] = [];
            $states = $c->getSortedStates();
            /** @var BaseState $s */
            foreach ($states as $s) {
                $result[$cn][] = $s->getName();
            }
        }
        $path = Util::secureDir('mautic') . 'regions.json';
        file_put_contents($path, Util::formatJSON($result));
        $this->line('Output regions to ' . $path);
        return $result;
    }

    protected function testMautic()
    {
        $me = MauticService::getSelf();
        dd($me);
    }

    protected function testRainSimilarText()
    {
        $a = 'STEAMGAMES.COM';
//        $b = 'WL *Steam Purchase       ';
//        $b = 'GOOGLE.COM';
        $b = 'GAMES.COM';
        $this->line($a . ' <> ' . $b);
        $this->line('Similar: ' . (RainService::isSimilarText($a, $b) ? 'YES' : 'NO'));
    }

    protected function testSimilarText()
    {
        $s1 = mb_strtolower('STEAMGAMES.COM');
//        $s2 = mb_strtolower('WL *STEAM PURCHASE'); // 7, 43%
//        $s2 = mb_strtolower('Steam Games'); // 10, 80%
//        $s2 = mb_strtolower('SQ *BRION BONK MERCHAN'); // 4, 22%
//        $s2 = mb_strtolower('AMERICAN RED CROSS'); // 4, 25%
//        $s2 = mb_strtolower('AMAZON MKTPLACE PMTS'); // 6, 35%
//        $s2 = mb_strtolower('APPLE.COM/BILL'); // 5, 35%
//        $s2 = mb_strtolower('GOOGLE.COM'); // 5, 41%
        $s2 = mb_strtolower('GAMES.COM'); // 5, 41%
        $similarity = similar_text($s1, $s2, $percent);
        $this->line($s1 . ' <> ' . $s2);
        $this->line('Similarity: ' . $similarity . ' (' . $percent . '%)');
        $this->line('Levenshtein: ' . levenshtein($s1, $s2));
    }

    protected function testExceptionBrief()
    {
        try {
            throw PortalException::create('Test exception brief', [
                'test' => 'test',
                'data' => 'data',
            ]);
        } catch (\Throwable $t) {
            dd(Util::getExceptionBrief($t));
        }
    }

    protected function testMutedExternalInvoke()
    {
        [$ei, , $muted] = ExternalInvoke::mute(function () {
            return ExternalInvoke::tempFailedArray();
//            throw new PortalException('Test');
        });
        dd($muted);
    }

    protected function testMerchantFromRain()
    {
        $data = Util::s2je(<<<EOT
{
    "id": "ec1b821a-2649-4723-8089-993fd9374bfb",
    "type": "spend",
    "spend": {
        "amount": 0,
        "currency": "usd",
        "localAmount": 0,
        "localCurrency": "usd",
        "authorizedAmount": 0,
        "authorizationMethod": "E-commerce request through public network",
        "merchantName": "STEAMGAMES.COM 425-9522985",
        "merchantCity": "425-8899642  ",
        "merchantCountry": "US",
        "merchantCategory": "Digital Goods: Games",
        "merchantCategoryCode": "5816",
        "merchantId": "d9394e6a-3d27-5945-89a1-569ab6c23348",
        "cardId": "0914a02c-4f26-4efa-817b-2b600bb401c1",
        "cardType": "virtual",
        "userId": "38c1ebe7-9e83-40e8-8cdd-9d99e6bda319",
        "userFirstName": "Hans",
        "userLastName": "Zhang",
        "userEmail": "<EMAIL>",
        "status": "completed",
        "authorizedAt": "2025-06-16T08:47:01.859Z",
        "enrichedMerchantIcon": "https:\/\/storage.googleapis.com\/heron-merchant-assets\/icons\/mrc_kGuToG5rTheZ4UrEef4Lu2.png",
        "enrichedMerchantName": "Steam Games",
        "enrichedMerchantCategory": "Entertainment - General",
        "postedAt": "2025-06-16T08:47:46.195Z",
        "receipt": false
    }
}
EOT
        );
        $merchant = MerchantService::instanceFromRain($data['spend']);
        $this->line('Parsed merchant name: ' . RainService::parseMerchantName($data['spend']));
        $this->line('ID: ' . $merchant->getId());
        $this->line('Merchant ID: ' . $merchant->getMerchantId());
        $this->line('Merchant Name: ' . $merchant->getName());
        $this->line('Merchant Enriched Name: ' . $merchant->getEnrichedName());
        $this->line('Merchant Category: ' . $merchant->getCategory());
        $this->line('Merchant Icon: ' . $merchant->getIconUrl());
    }

    protected function testCardTypeName()
    {
        $uc = UserCard::find(115853);
        dd($uc->getCardTypeName());
    }

    protected function migrateRainUserIds()
    {
        $us = Util::em()->getRepository(User::class)
            ->createQueryBuilder('u')
            ->where('u.meta is not null')
            ->andWhere(Util::expr()->like('u.meta', ':rainAccountId'))
            ->setParameter('rainAccountId', '%rainUserId%')
            ->getQuery()
            ->getResult();
        $this->line('Found ' . count($us) . ' users with Rain User ID');
        /** @var User $u */
        foreach ($us as $u) {
            $config = $u->ensureConfig();
            $rainUserId = Util::meta($u, 'rainUserId');
            $config->setRainUserId($rainUserId ?? $config->getRainUserId())
                ->persist();
            $this->line('Updated Rain User ID to ' . $rainUserId . ' for ' . $u->getSignature());
        }
    }

    protected function migrateRainCardIds()
    {
        $us = Util::em()->getRepository(UserCard::class)
            ->createQueryBuilder('u')
            ->where('u.meta is not null')
            ->andWhere(Util::expr()->like('u.meta', ':meta'))
            ->setParameter('meta', '%"rainCardId"%')
            ->getQuery()
            ->getResult();
        $this->line('Found ' . count($us) . ' cards with Rain Card ID');
        /** @var UserCard $uc */
        foreach ($us as $uc) {
            if ($uc->getAccountNumber()) {
                $this->line('Already has account number on card ' . $uc->getId());
                continue;
            }
            $uc->setAccountNumber(Util::meta($uc, 'rainCardId'))
                ->persist();
            $this->line('Updated Rain Card ID to ' . $uc->getAccountNumber() . ' for card ' . $uc->getId());
        }
    }

    protected function testCoinflowLink()
    {
//        $result = [];
//        parse_str($s, $result);
//        dd($result);
    }

    protected function testCommandCaller()
    {
        Util::executeCommand('span:dev:test', [
            '--method' => 'testCommandCallerForce',
            '--force',
        ]);
    }

    protected function testCommandCallerForce()
    {
        $force = $this->input->getOption('force');
        dd($force);
    }

    protected function testGoogleSheet()
    {
        $ret = GoogleSheetService::test();
        var_dump($ret);
    }

    protected function testDbMaxAllowedPacket()
    {
        $conn = Util::em()->getConnection();

        $values = $conn->executeQuery("SHOW VARIABLES LIKE 'max_allowed_packet';")->fetchAssociative();
        $this->line('Current value: ');
        Util::dump($values);

        $updated = $conn->executeStatement("SET GLOBAL max_allowed_packet = 64 * 1024 * 1024;");
        $this->line('Updated global: ' . $updated);

        try {
            $updated = $conn->executeStatement("SET SESSION max_allowed_packet = 512 * 1024 * 1024;");
        } catch (\Throwable $e) {
            $updated = $e->getMessage();
        }
        $this->line('Updated session: ' . $updated);

        $values = $conn->executeQuery("SHOW VARIABLES LIKE 'max_allowed_packet';")->fetchAssociative();
        $this->line('New value: ');
        Util::dump($values);
    }

    protected function testConfigGet()
    {
        $threshold = (int)Config::get(Config::CONFIG_MEX_INTERMEX_BALANCE_THRESHOLD, 6000000);
        $this->line($threshold);
    }

    protected function testSftpErrors()
    {
        set_error_handler(function ($errno, $errstr, $errfile, $errline) {
            Util::consoleLine('set_error_handler', 'comment',
                compact($errno, $errstr, $errfile, $errline));
        });

        $sftp = new SFTP('192.168.0.1');
        if ($sftp->login('a', 'b')) {
            $this->line('Logged in');
        } else {
            $this->lines([
                '',
                'error_get_last => ' . Util::j2s(error_get_last()),
                'getSFTPLog => ' . Util::j2s($sftp->getSFTPLog()),
                'getErrors => ' . Util::j2s($sftp->getErrors()),
                'getLastError => ' . $sftp->getLastError(),
                'getBannerMessage => ' . $sftp->getBannerMessage(),
                'getSFTPErrors => ' . Util::j2s($sftp->getSFTPErrors()),
                'getLastSFTPError => ' . $sftp->getLastSFTPError(),
            ]);
        }

        restore_error_handler();
    }

    protected function testNachaBatchControl()
    {
        $batch = new Batch();
        $batch->getHeader()->setCompanyId('0863959262');
        var_dump((string)$batch);
    }

    protected function testULimit()
    {
        var_dump(Util::executeShell('ulimit -n'));
        var_dump(Util::executeShell('ulimit -n 65535'));
        var_dump(Util::executeShell('ulimit -n'));
    }

    protected function formatDbZipNames()
    {
        $path = __DIR__ . '/../../../../shared/diff.txt';
        $content = file_get_contents($path);
        $names = explode("\n", $content);
        foreach ($names as $i => $name) {
            if (!$name) {
                continue;
            }
            // db_1706337003_min.zip
            $name = Util::removePrefix($name, 'db_');
            $name = Util::removeSuffix($name, '_min.zip');
            $date = date('Y-m-d', $name);
            $names[$i] = $name . ' - ' . $date;
        }
        file_put_contents($path, implode("\n", $names));
    }

    protected function listApInstitutions()
    {
        $codes = [
            'AR',
            'AT',
            'BE',
            'HR',
            'CY',
            'CZ',
            'DK',
            'EE',
            'FI',
            'FR',
            'DE',
            'GI',
            'GR',
            'HU',
            'IS',
            'IE',
            'IT',
            'LV',
            'LT',
            'LU',
            'MK',
            'MT',
            'NL',
            'NO',
            'PL',
            'PT',
            'RO',
            'RS',
            'SK',
            'SI',
            'ES',
            'SE',
            'CH',
            'TN',
            'TR',
            'GB',
        ];
        foreach ($codes as $code) {
            $country = Country::findByCode($code);
            if (!$country) {
                $this->line('Unknown country code: ' . $code);
                continue;
            }
            $this->line('Checking for country ' . $country->getName() . ' (' . $code . ')');
            $items = AlternativePaymentService::listInstitutions($country);
            if (!$items) {
                $this->line('    ---- Nothing');
                continue;
            }
            foreach ($items as $item) {
                if (!empty($item['isPSUIdRequired'])) {
                    $this->line('    ---- ' . $item['code'] . ' ---- ' . $item['name'] . ' >>>> PSUId required', 'comment');
                } else {
                    $this->line('    ---- ' . $item['code'] . ' ---- ' . $item['name']);
                }
            }
        }
    }

    protected function testAesRsaEncryption()
    {
        $s = date('c');
        $this->line($s);
        $this->line('RSA 1 => ' . Security::rsaEncrypt($s));
        $this->line('RSA 2 => ' . Security::rsaEncrypt($s));
        $this->line('AES 1 => ' . Security::aesEncrypt($s));
        $this->line('AES 2 => ' . Security::aesEncrypt($s));
        $this->line('AES EX 1 => ' . Security::aesEncryptEx($s));
        $this->line('AES EX 2 => ' . Security::aesEncryptEx($s));
    }

    protected function testBotmBusinessAccountCreation()
    {
        $_ids = Util::em()->getRepository(UserGroup::class)
            ->createQueryBuilder('ug')
            ->where('ug.botmBusinessAccountId is not null')
            ->select('ug.botmBusinessId')
            ->getQuery()
            ->getArrayResult();
        $ids = array_pluck($_ids, 'botmBusinessId');

        $ug = UserGroup::find(224);
        $ugName = strtolower($ug->getName());
        $api = new BotmAPI();
        $exists = ExternalInvoke::host($api->listBusiness());
        foreach ($exists as $e) {
            $name = strtolower($e['legal_business_name']);
            $this->line($name);
            if ($name === $ugName && !in_array($e['id'], $ids)) {
                $this->line('Stop and set to ' . $e['id']);
                break;
            }
        }
    }

    protected function testPdfBox()
    {
        $user = User::find(*********);
        $attachment = Attachment::find(458833);
        $result = EmployerService::extractDirectDepositZip($user, $attachment);
        dd($result);
    }

    protected function testSumResolver()
    {
        $all = Util::resolveSum([
            8, 2, 1, 3, 6, 5, 10, 11,
        ], 10);
        dd($all);
    }

    protected function testUserConfigModification()
    {
        $uc = $this->em->getRepository(UserConfig::class)->find(9);
        $uc->setBotmUserAccountId(123);
        $this->line($uc->getBotmUserAccountId());

        $user = User::find(*********);
        $this->line($user->ensureConfig()->getBotmUserAccountId());
    }

    protected function testDateFormats()
    {
        // Rapyd maintenance email date format
        $dates = [
            '16/05/24 01:00 AM', // May 16th => Invalid. Split it, fix year and rotate the month/day
            '28/05/2024 03:00 PM', // May 28th =>  Split it and rotate the month/day
            '06/04/2024 01:00 AM', // June 04th
            'November 27 (27/11/2023)', // Invalid, use the format in the brackets
            'October 30',
        ];
        foreach ($dates as $date) {
            try {
                $dt = Carbon::parseFromLocale($date, 'ar');
                $this->line($date . ' => ' . $dt->format('c'));
            } catch (\Throwable $t) {
                $this->line($date . ' => ' . $t->getMessage());
            }
        }
    }

    protected function testCheckProcessName()
    {
        $name = trim(file_get_contents('/tmp/span_test_process_name'));
        $pid = trim(file_get_contents('/tmp/span_test_process_except')) ?: getmypid();

        $found = Util::isPidRunning($pid, true);
        $this->line('Is pid running: ' . $found);

        $matches = Util::hasProcessMatches($name, $pid, true);
        if (!$matches) {
            $this->line('No matches');
            return;
        }
        foreach ($matches as $match) {
            $this->line($match);
        }
    }

    protected function unlockFailedTransfersInQueue()
    {
        $all = '215329, 215328, 215327, 215326, 215325, 215324, 215323, 215322, 215321, 215320, 215319, 215318, 215317, 215316, 215315, 215314, 215313, 215312, 215311, 215310, 215309, 215308, 215307, 215306, 215305, 215304, 215303, 215302, 215301, 215300, 215299, 215298, 215297, 215296, 215295, 215293, 215292, 215291, 215290, 215289, 215288, 215287, 215286, 215285, 215284, 215283, 215282, 215281, 215280, 215278, 215277, 215275, 215274, 215273, 215272, 215271, 215269, 215268, 215267, 215266, 215265, 215264, 215263, 215261, 215260, 215259, 215258, 215257, 215256, 215254, 215253, 215252, 215251, 215250, 215248, 215247, 215246, 215245, 215244, 215243, 215241, 215240, 215239, 215238, 215237, 215236, 215235, 215234, 215233, 215232, 215229, 215228, 215227, 215226, 215225, 215224, 215223, 215222, 215221, 215220, 215219, 215218, 215217, 215216, 215215, 215214, 215213, 215212, 215211, 215210, 215209, 215208, 215207, 215205, 215203, 215202, 215201, 215199, 215198, 215196, 215194, 215193, 215192, 215191, 215189, 215188, 215187, 215186, 215185, 215184, 215183, 215182, 215181, 215180, 215178, 215177, 215176, 215175, 215174, 215173, 215172, 215171, 215170, 215169, 215168, 215167, 215164, 215163, 215162, 215161, 215160, 215159, 215158, 215157, 215156, 215155, 215154, 215153, 215152, 215151, 215150, 215149, 215148, 215147, 215146, 215145, 215144, 215143, 215142, 215141, 215140, 215139, 215138, 215137, 215136, 215135, 215134, 215133, 215132, 215131, 215130, 215129, 215128, 215127, 215126, 215125, 215124, 215122, 215121, 215120, 215118, 215117, 215116, 215115, 215114, 215113, 215112, 215111, 215110, 215109, 215108, 215107, 215106, 215105, 215104, 215103, 215102, 215101, 215100, 215098, 215097, 215096, 215095, 215094, 215093, 215092, 215091, 215090, 215089, 215088, 215087, 215086, 215085, 215084, 215083, 215081, 215079, 215078, 215077, 215076, 215075, 215074, 215073, 215072, 215071, 215069, 215068, 215067, 215066, 215065, 215064, 215063, 215062, 215061, 215060, 215059, 215058, 215057, 215056, 215055, 215054, 215053, 215052, 215050, 215049, 215048, 215047, 215046, 215045, 215044, 215043, 215042, 215041, 215040, 215039, 215038, 215037, 215036, 215035, 215034, 215033, 215032, 215030, 215029, 215028, 215027, 215026, 215025, 215024, 215023, 215022, 215021, 215020, 215019, 215018, 215017, 215015, 215014, 215013, 215012, 215011, 215010, 215008, 215006, 215004, 215003, 215002, 215001, 215000, 214999, 214998, 214997, 214996, 214995, 214994, 214992, 214991, 214990, 214989, 214988, 214987, 214985, 214983, 214982, 214981, 214980, 214979, 214978, 214977, 214976, 214975, 214974, 214973, 214972, 214971, 214970, 214969, 214968, 214967, 214966, 214965, 214964, 214963, 214962, 214961, 214960, 214959, 214958, 214957, 214956, 214955, 214954, 214953, 214952, 214951, 214950, 214949, 214948, 214947, 214946, 214945, 214944, 214943, 214942, 214941, 214940, 214939, 214938, 214937, 214936, 214935, 214934, 214933, 214932, 214931, 214930, 214928, 214927, 214926, 214925, 214924, 214923, 214922, 214921, 214920, 214919, 214918, 214917, 214916, 214915, 214914, 214913, 214912, 214911, 214910, 214909, 214908, 214907, 214906, 214905, 214904, 214903, 214902, 214901, 214900, 214899, 214898, 214897, 214896, 214895, 214894, 214893, 214892, 214891, 214890, 214889, 214888, 214887, 214886, 214885, 214884, 214883, 214882, 214881, 214880, 214879, 214878, 214877, 214876, 214875, 214874, 214873, 214872, 214871, 214870, 214869, 214868, 214867, 214866, 214865, 214864, 214863, 214862, 214861, 214860, 214859, 214858, 214857, 214856, 214855, 214854, 214853, 214852, 214851, 214850, 214849, 214848, 214847, 214846, 214845, 214844, 214843, 214842, 214841, 214840, 214839, 214838, 214837, 214836, 214834, 214833, 214832, 214831, 214830, 214829, 214828, 214827, 214826, 214825, 214824, 214823, 214822, 214821, 214820, 214819, 214818, 214817, 214816, 214815, 214814, 214813, 214812, 214811, 214810, 214809, 214808, 214807, 214806, 214805, 214804, 214803, 214802, 214801, 214800, 214799, 214798, 214797, 214796, 214795, 214794, 214793, 214792, 214791, 214790, 214789, 214788, 214787, 214786, 214785, 214784, 214783, 214782, 214781, 214780, 214779, 214778, 214777, 214776, 214775, 214774, 214773, 214772, 214771, 214770, 214769';
        $items = explode(', ', $all);
        $count = count($items);
        foreach ($items as $i => $id) {
            $prefix = $i . '/' . $count . ' - ';
            $id = trim($id);
            if (!$id) {
                $this->line($prefix . 'Skip 1: ' . $id);
                continue;
            }
            $transfer = Transfer::find($id);
            if (!$transfer) {
                $this->line($prefix . 'Skip 2: ' . $id);
                continue;
            }
            if ($transfer->getStatus() !== 'Processing') {
                $this->line($prefix . 'Skip 2-2: ' . $id);
                continue;
            }
            if (!str_contains($transfer->getError(), 'Your payment method type (bank or cash provider) is not available anymore')) {
                $this->line($prefix . 'Skip 3: ' . $id);
                continue;
            }
            $cacheKey = 'rapid_create_payout_' . $id;
            if (!Data::has($cacheKey)) {
                $this->line($prefix . 'Skip 4: ' . $id);
                continue;
            }
            Data::del($cacheKey);
            $this->line($prefix . 'Remove cache for failed transfer in queue ' . $cacheKey, 'info');
        }
    }

    protected function unlockEmployerPayoutRapidKeyDo()
    {
        $this->unlockEmployerPayoutRapidKey(true);
    }

    protected function unlockEmployerPayoutRapidKey($clear = false)
    {
        $data = [
            245914,
            245915,
            245916,
            245917,
            245918,
            245919,
            245921,
            245923,
            245924,
            245926,
            245927,
            245928,
            245930,
            245931,
            245933,
            245934,
            245936,
            245938,
            245940,
            245941,
            245942,
            245943,
            245944,
            245946,
            245947,
            245948,
            245949,
            245951,
            245952,
            245953,
            245954,
            245956,
            245957,
            245959,
            245960,
            245961,
            245963,
            245964,
            245965,
            245966,
            245967,
            245968,
            245969,
            245970,
            245971,
            245972,
            245973,
            245974,
            245975,
            245976,
            245977,
            245978,
            245979,
            245980,
            245981,
            245982,
            245983,
            245984,
            245985,
            245986,
            245987,
            245988,
            245989,
            245990,
            245991,
            245992,
            245993,
            245994,
            245995,
            245996,
            245997,
            245998,
            245999,
            246000,
            246001,
            246002,
            246003,
            246004,
            246005,
            246006,
            246007,
            246008,
            246009,
            246010,
            246011,
            246012,
            246013,
            246014,
            246015,
            246016,
            246017,
            246018,
            246019,
            246020,
            246021,
            246022,
            246023,
            246024,
            246025,
            246026,
            246027,
            246028,
            246029,
            246030,
            246031,
            246032,
            246033,
            246034,
            246035,
            246036,
            246037,
            246038,
            246039,
            246040,
            246041,
            246042,
            246043,
            246044,
            246045,
            246046,
            246047,
            246048,
            246049,
            246050,
            246051,
            246052,
            246053,
            246054,
            246055,
            246056,
            246057,
            246058,
            246059,
            246060,
            246061,
            246062,
            246063,
            246064,
            246065,
            246066,
            246067,
            246068,
            246069,
            246070,
            246071,
            246072,
            246073,
            246074,
            246075,
            246076,
            246077,
            246078,
            246079,
            246080,
            246081,
            246082,
            246083,
            246084,
            246085,
            246086,
            246087,
            246088,
            246089,
            246090,
            246091,
            246092,
            246093,
            246094,
            246095,
            246096,
            246097,
            246098,
            246099,
            246100,
            246101,
            246102,
            246103,
            246104,
            246105,
            246106,
            246107,
            246108,
            246109,
            246110,
            246111,
            246112,
            246113,
            246114,
            246115,
            246116,
            246117,
            246118,
            246119,
            246120,
            246121,
            246122,
            246123,
            246124,
            246125,
            246126,
            246127,
            246128,
            246129,
            246130,
            246131,
            246132,
            246133,
            246134,
            246135,
            246136,
            246137,
            246138,
            246139,
            246140,
            246141,
            246142,
            246143,
            246144,
            246145,
            246146,
            246147,
            246148,
            246149,
            246150,
            246151,
            246152,
            246153,
            246154,
            246155,
            246156,
            246157,
            246158,
            246159,
            246160,
            246161,
            246162,
            246163,
            246164,
            246165,
            246166,
            246167,
            246168,
            246169,
            246170,
            246171,
            246172,
            246173,
            246174,
            246175,
            246176,
            246177,
            246178,
            246179,
            246180,
            246181,
            246182,
            246183,
            246184,
            246185,
            246186,
            246187,
            246188,
            246189,
            246190,
            246191,
            246192,
            246193,
            246194,
            246195,
            246196,
            246197,
            246198,
            246199,
            246200,
            246201,
            246202,
            246203,
            246204,
        ];
        foreach ($data as $d) {
            $key = RapidService::CACHE_KEY . $d;
            if (Data::has($key)) {
                $this->line('Found ' . $key);
                if ($clear) {
                    Data::del($key);
                    $this->line('Cleared ' . $key);
                }
            } else {
                $this->line('Not found ' . $key);
            }
        }
    }

    protected function unlockEmployerPayoutSingleDateCacheDo()
    {
        $this->unlockEmployerPayoutSingleDateCache(true);
    }

    protected function unlockEmployerPayoutSingleDateCache($clear = false)
    {
        $date = '10/19/2023';
        $data = [
            [
                'employee_id' => 500174260,
                'amount' => 57446,
            ],
            [
                'employee_id' => 500147491,
                'amount' => 95783,
            ],
            [
                'employee_id' => 500150907,
                'amount' => 58422,
            ],
            [
                'employee_id' => 500147500,
                'amount' => 95783,
            ],
            [
                'employee_id' => 500148501,
                'amount' => 95783,
            ],
            [
                'employee_id' => 500149377,
                'amount' => 54526,
            ],
            [
                'employee_id' => 500149384,
                'amount' => 77893,
            ],
            [
                'employee_id' => 500149437,
                'amount' => 183266,
            ],
            [
                'employee_id' => 500149434,
                'amount' => 183267,
            ],
            [
                'employee_id' => 500149435,
                'amount' => 164631,
            ],
            [
                'employee_id' => 500149430,
                'amount' => 181771,
            ],
            [
                'employee_id' => 500149443,
                'amount' => 130081,
            ],
            [
                'employee_id' => 500168146,
                'amount' => 181771,
            ],
            [
                'employee_id' => 500149438,
                'amount' => 162650,
            ],
            [
                'employee_id' => 500168180,
                'amount' => 181771,
            ],
            [
                'employee_id' => 500168165,
                'amount' => 181771,
            ],
            [
                'employee_id' => 500161516,
                'amount' => 408748,
            ],
            [
                'employee_id' => 500149441,
                'amount' => 68150,
            ],
            [
                'employee_id' => 500168182,
                'amount' => 126848,
            ],
            [
                'employee_id' => 500284007,
                'amount' => 69999,
            ],
            [
                'employee_id' => 500168161,
                'amount' => 73000,
            ],
            [
                'employee_id' => 500168421,
                'amount' => 107028,
            ],
            [
                'employee_id' => 500168140,
                'amount' => 183266,
            ],
            [
                'employee_id' => 500168153,
                'amount' => 202070,
            ],
            [
                'employee_id' => 500168156,
                'amount' => 70000,
            ],
            [
                'employee_id' => 500168159,
                'amount' => 112121,
            ],
            [
                'employee_id' => 500168162,
                'amount' => 96308,
            ],
            [
                'employee_id' => 500168173,
                'amount' => 70000,
            ],
            [
                'employee_id' => 500168164,
                'amount' => 117978,
            ],
            [
                'employee_id' => 500168181,
                'amount' => 66244,
            ],
            [
                'employee_id' => 500168136,
                'amount' => 151734,
            ],
            [
                'employee_id' => 500168160,
                'amount' => 112121,
            ],
            [
                'employee_id' => 500168424,
                'amount' => 112121,
            ],
            [
                'employee_id' => 500168170,
                'amount' => 101162,
            ],
            [
                'employee_id' => 500168177,
                'amount' => 112121,
            ],
            [
                'employee_id' => 500168157,
                'amount' => 70000,
            ],
            [
                'employee_id' => 500168158,
                'amount' => 68150,
            ],
            [
                'employee_id' => 500168166,
                'amount' => 101162,
            ],
            [
                'employee_id' => 500168129,
                'amount' => 70000,
            ],
            [
                'employee_id' => 500168141,
                'amount' => 66244,
            ],
            [
                'employee_id' => 500168139,
                'amount' => 72999,
            ],
            [
                'employee_id' => 500168143,
                'amount' => 66244,
            ],
            [
                'employee_id' => 500156365,
                'amount' => 179292,
            ],
            [
                'employee_id' => 500168144,
                'amount' => 151734,
            ],
            [
                'employee_id' => 500168423,
                'amount' => 151734,
            ],
            [
                'employee_id' => 500168147,
                'amount' => 179292,
            ],
            [
                'employee_id' => 500168138,
                'amount' => 96309,
            ],
            [
                'employee_id' => 500168132,
                'amount' => 102619,
            ],
            [
                'employee_id' => 500168185,
                'amount' => 102619,
            ],
            [
                'employee_id' => 500168188,
                'amount' => 96308,
            ],
            [
                'employee_id' => 500168184,
                'amount' => 96308,
            ],
            [
                'employee_id' => 500168171,
                'amount' => 96308,
            ],
            [
                'employee_id' => 500168167,
                'amount' => 102619,
            ],
            [
                'employee_id' => 500168183,
                'amount' => 102619,
            ],
            [
                'employee_id' => 500168186,
                'amount' => 112121,
            ],
            [
                'employee_id' => 500168174,
                'amount' => 102619,
            ],
            [
                'employee_id' => 500168187,
                'amount' => 96308,
            ],
            [
                'employee_id' => 500168134,
                'amount' => 112206,
            ],
            [
                'employee_id' => 500168152,
                'amount' => 66244,
            ],
            [
                'employee_id' => 500179214,
                'amount' => 73000,
            ],
            [
                'employee_id' => 500179220,
                'amount' => 66244,
            ],
            [
                'employee_id' => 500179222,
                'amount' => 68150,
            ],
            [
                'employee_id' => 500269142,
                'amount' => 151734,
            ],
            [
                'employee_id' => 500269143,
                'amount' => 73000,
            ],
            [
                'employee_id' => 500147484,
                'amount' => 95416,
            ],
            [
                'employee_id' => 500147502,
                'amount' => 95783,
            ],
            [
                'employee_id' => 500156403,
                'amount' => 95414,
            ],
            [
                'employee_id' => 500172004,
                'amount' => 85033,
            ],
            [
                'employee_id' => 500147476,
                'amount' => 95783,
            ],
            [
                'employee_id' => 500147481,
                'amount' => 95783,
            ],
            [
                'employee_id' => 500168193,
                'amount' => 377726,
            ],
            [
                'employee_id' => 500168192,
                'amount' => 377726,
            ],
            [
                'employee_id' => 500147478,
                'amount' => 117929,
            ],
            [
                'employee_id' => 500148502,
                'amount' => 94092,
            ],
            [
                'employee_id' => 500147493,
                'amount' => 95783,
            ],
            [
                'employee_id' => 500148504,
                'amount' => 95783,
            ],
            [
                'employee_id' => 500148505,
                'amount' => 95784,
            ],
            [
                'employee_id' => 500147488,
                'amount' => 139213,
            ],
            [
                'employee_id' => 500147479,
                'amount' => 95783,
            ],
            [
                'employee_id' => 500147480,
                'amount' => 95783,
            ],
            [
                'employee_id' => 500149420,
                'amount' => 101162,
            ],
            [
                'employee_id' => 500149415,
                'amount' => 183267,
            ],
            [
                'employee_id' => 500276252,
                'amount' => 69999,
            ],
            [
                'employee_id' => 500156362,
                'amount' => 91230,
            ],
            [
                'employee_id' => 500161494,
                'amount' => 429593,
            ],
            [
                'employee_id' => 500148705,
                'amount' => 429594,
            ],
            [
                'employee_id' => 500156367,
                'amount' => 91230,
            ],
            [
                'employee_id' => 500281702,
                'amount' => 95416,
            ],
            [
                'employee_id' => 500172017,
                'amount' => 85033,
            ],
            [
                'employee_id' => 500148695,
                'amount' => 470153,
            ],
            [
                'employee_id' => 500168149,
                'amount' => 94900,
            ],
            [
                'employee_id' => 500172005,
                'amount' => 85033,
            ],
            [
                'employee_id' => 500148701,
                'amount' => 377726,
            ],
            [
                'employee_id' => 500170236,
                'amount' => 179292,
            ],
            [
                'employee_id' => 500161501,
                'amount' => 377726,
            ],
            [
                'employee_id' => 500168202,
                'amount' => 429593,
            ],
            [
                'employee_id' => 500161491,
                'amount' => 377726,
            ],
            [
                'employee_id' => 500161498,
                'amount' => 429593,
            ],
            [
                'employee_id' => 500161495,
                'amount' => 408747,
            ],
            [
                'employee_id' => 500161496,
                'amount' => 408748,
            ],
            [
                'employee_id' => 500161489,
                'amount' => 429594,
            ],
            [
                'employee_id' => 500161493,
                'amount' => 144536,
            ],
            [
                'employee_id' => 500148698,
                'amount' => 408747,
            ],
            [
                'employee_id' => 500148697,
                'amount' => 408748,
            ],
            [
                'employee_id' => 500148704,
                'amount' => 408748,
            ],
            [
                'employee_id' => 500148702,
                'amount' => 377726,
            ],
            [
                'employee_id' => 500148699,
                'amount' => 408748,
            ],
            [
                'employee_id' => 500156360,
                'amount' => 109651,
            ],
            [
                'employee_id' => 500165986,
                'amount' => 92493,
            ],
            [
                'employee_id' => 500148696,
                'amount' => 634948,
            ],
            [
                'employee_id' => 500156401,
                'amount' => 92493,
            ],
            [
                'employee_id' => 500165991,
                'amount' => 104748,
            ],
            [
                'employee_id' => 500165987,
                'amount' => 90041,
            ],
            [
                'employee_id' => 500165988,
                'amount' => 92493,
            ],
            [
                'employee_id' => 500156390,
                'amount' => 122999,
            ],
            [
                'employee_id' => 500165995,
                'amount' => 69869,
            ],
            [
                'employee_id' => 500165979,
                'amount' => 144527,
            ],
            [
                'employee_id' => 500156391,
                'amount' => 95416,
            ],
            [
                'employee_id' => 500165994,
                'amount' => 92493,
            ],
            [
                'employee_id' => 500156400,
                'amount' => 144537,
            ],
            [
                'employee_id' => 500172015,
                'amount' => 116964,
            ],
            [
                'employee_id' => 500165997,
                'amount' => 77888,
            ],
            [
                'employee_id' => 500165981,
                'amount' => 91751,
            ],
            [
                'employee_id' => 500165977,
                'amount' => 91752,
            ],
            [
                'employee_id' => 500165982,
                'amount' => 144528,
            ],
            [
                'employee_id' => 500156397,
                'amount' => 157574,
            ],
            [
                'employee_id' => 500156399,
                'amount' => 92494,
            ],
            [
                'employee_id' => 500156350,
                'amount' => 58422,
            ],
            [
                'employee_id' => 500165996,
                'amount' => 92103,
            ],
            [
                'employee_id' => 500149411,
                'amount' => 429594,
            ],
            [
                'employee_id' => 500156402,
                'amount' => 177599,
            ],
            [
                'employee_id' => 500165983,
                'amount' => 69869,
            ],
            [
                'employee_id' => 500156352,
                'amount' => 58422,
            ],
            [
                'employee_id' => 500149431,
                'amount' => 166027,
            ],
            [
                'employee_id' => 500156395,
                'amount' => 104178,
            ],
            [
                'employee_id' => 500179213,
                'amount' => 58422,
            ],
            [
                'employee_id' => 500156392,
                'amount' => 174246,
            ],
            [
                'employee_id' => 500156393,
                'amount' => 144570,
            ],
            [
                'employee_id' => 500165989,
                'amount' => 92493,
            ],
            [
                'employee_id' => 500156354,
                'amount' => 70591,
            ],
            [
                'employee_id' => 500147505,
                'amount' => 429594,
            ],
            [
                'employee_id' => 500156405,
                'amount' => 101256,
            ],
            [
                'employee_id' => 500183960,
                'amount' => 70591,
            ],
            [
                'employee_id' => 500179215,
                'amount' => 44358,
            ],
            [
                'employee_id' => 500165978,
                'amount' => 133449,
            ],
            [
                'employee_id' => 500156349,
                'amount' => 58422,
            ],
            [
                'employee_id' => 500156347,
                'amount' => 58422,
            ],
            [
                'employee_id' => 500165990,
                'amount' => 92495,
            ],
            [
                'employee_id' => 500165992,
                'amount' => 92495,
            ],
            [
                'employee_id' => 500165993,
                'amount' => 102718,
            ],
            [
                'employee_id' => 500168178,
                'amount' => 179292,
            ],
            [
                'employee_id' => 500168133,
                'amount' => 123602,
            ],
            [
                'employee_id' => 500156353,
                'amount' => 58422,
            ],
            [
                'employee_id' => 500156356,
                'amount' => 58422,
            ],
            [
                'employee_id' => 500156358,
                'amount' => 58422,
            ],
            [
                'employee_id' => 500156348,
                'amount' => 58422,
            ],
            [
                'employee_id' => 500156359,
                'amount' => 58422,
            ],
            [
                'employee_id' => 500168148,
                'amount' => 68150,
            ],
            [
                'employee_id' => 500148694,
                'amount' => 377725,
            ],
            [
                'employee_id' => 500161517,
                'amount' => 86918,
            ],
            [
                'employee_id' => 500165998,
                'amount' => 94120,
            ],
            [
                'employee_id' => 500166001,
                'amount' => 105991,
            ],
            [
                'employee_id' => 500148691,
                'amount' => 122805,
            ],
            [
                'employee_id' => 500144743,
                'amount' => 77893,
            ],
            [
                'employee_id' => 500149389,
                'amount' => 111091,
            ],
            [
                'employee_id' => 500152656,
                'amount' => 77893,
            ],
            [
                'employee_id' => 500149402,
                'amount' => 77893,
            ],
            [
                'employee_id' => 500152658,
                'amount' => 77893,
            ],
            [
                'employee_id' => 500156378,
                'amount' => 91231,
            ],
            [
                'employee_id' => 500156386,
                'amount' => 103255,
            ],
            [
                'employee_id' => 500156383,
                'amount' => 77892,
            ],
            [
                'employee_id' => 500156363,
                'amount' => 91230,
            ],
            [
                'employee_id' => 500156384,
                'amount' => 91230,
            ],
            [
                'employee_id' => 500156368,
                'amount' => 91231,
            ],
            [
                'employee_id' => 500156376,
                'amount' => 91230,
            ],
            [
                'employee_id' => 500168155,
                'amount' => 68150,
            ],
            [
                'employee_id' => 500172013,
                'amount' => 85033,
            ],
            [
                'employee_id' => 500156380,
                'amount' => 91230,
            ],
            [
                'employee_id' => 500156372,
                'amount' => 77893,
            ],
            [
                'employee_id' => 500156366,
                'amount' => 77893,
            ],
            [
                'employee_id' => 500156373,
                'amount' => 91230,
            ],
            [
                'employee_id' => 500156361,
                'amount' => 91230,
            ],
            [
                'employee_id' => 500156370,
                'amount' => 77893,
            ],
            [
                'employee_id' => 500156379,
                'amount' => 77893,
            ],
            [
                'employee_id' => 500156389,
                'amount' => 77893,
            ],
            [
                'employee_id' => 500156364,
                'amount' => 77893,
            ],
            [
                'employee_id' => 500156382,
                'amount' => 77893,
            ],
            [
                'employee_id' => 500171998,
                'amount' => 135030,
            ],
            [
                'employee_id' => 500215936,
                'amount' => 91231,
            ],
            [
                'employee_id' => 500247911,
                'amount' => 72050,
            ],
            [
                'employee_id' => 500247913,
                'amount' => 91230,
            ],
            [
                'employee_id' => 500247914,
                'amount' => 77893,
            ],
            [
                'employee_id' => 500247915,
                'amount' => 77893,
            ],
            [
                'employee_id' => 500247918,
                'amount' => 77893,
            ],
            [
                'employee_id' => 500284010,
                'amount' => 91230,
            ],
            [
                'employee_id' => 500147781,
                'amount' => 136108,
            ],
            [
                'employee_id' => 500147780,
                'amount' => 136108,
            ],
            [
                'employee_id' => 500147783,
                'amount' => 136108,
            ],
            [
                'employee_id' => 500147785,
                'amount' => 136108,
            ],
            [
                'employee_id' => 500147784,
                'amount' => 136108,
            ],
            [
                'employee_id' => 500147787,
                'amount' => 188777,
            ],
            [
                'employee_id' => 500147782,
                'amount' => 136108,
            ],
            [
                'employee_id' => 500147786,
                'amount' => 136108,
            ],
            [
                'employee_id' => 500241367,
                'amount' => 136108,
            ],
            [
                'employee_id' => 500241364,
                'amount' => 136108,
            ],
            [
                'employee_id' => 500260681,
                'amount' => 136108,
            ],
            [
                'employee_id' => 500161485,
                'amount' => 377726,
            ],
            [
                'employee_id' => 500172014,
                'amount' => 429593,
            ],
            [
                'employee_id' => 500172012,
                'amount' => 429593,
            ],
            [
                'employee_id' => 500179163,
                'amount' => 85033,
            ],
            [
                'employee_id' => 500148669,
                'amount' => 77893,
            ],
            [
                'employee_id' => 500165926,
                'amount' => 58422,
            ],
            [
                'employee_id' => 500165950,
                'amount' => 54527,
            ],
            [
                'employee_id' => 500165948,
                'amount' => 183266,
            ],
            [
                'employee_id' => 500165971,
                'amount' => 77892,
            ],
            [
                'employee_id' => 500165927,
                'amount' => 77893,
            ],
            [
                'employee_id' => 500165953,
                'amount' => 77893,
            ],
            [
                'employee_id' => 500165934,
                'amount' => 85033,
            ],
            [
                'employee_id' => 500165966,
                'amount' => 150862,
            ],
            [
                'employee_id' => 500165962,
                'amount' => 150862,
            ],
            [
                'employee_id' => 500165955,
                'amount' => 150862,
            ],
            [
                'employee_id' => 500165944,
                'amount' => 150863,
            ],
            [
                'employee_id' => 500165970,
                'amount' => 150862,
            ],
            [
                'employee_id' => 500165946,
                'amount' => 150863,
            ],
            [
                'employee_id' => 500165951,
                'amount' => 150863,
            ],
            [
                'employee_id' => 500172021,
                'amount' => 150862,
            ],
            [
                'employee_id' => 500172023,
                'amount' => 150862,
            ],
            [
                'employee_id' => 500149429,
                'amount' => 151734,
            ],
            [
                'employee_id' => 500149401,
                'amount' => 72999,
            ],
            [
                'employee_id' => 500149404,
                'amount' => 95783,
            ],
            [
                'employee_id' => 500149409,
                'amount' => 101162,
            ],
            [
                'employee_id' => 500149372,
                'amount' => 50767,
            ],
            [
                'employee_id' => 500149374,
                'amount' => 54527,
            ],
            [
                'employee_id' => 500149375,
                'amount' => 54526,
            ],
            [
                'employee_id' => 500149376,
                'amount' => 54527,
            ],
            [
                'employee_id' => 500149378,
                'amount' => 54527,
            ],
            [
                'employee_id' => 500149381,
                'amount' => 50767,
            ],
            [
                'employee_id' => 500149383,
                'amount' => 54527,
            ],
            [
                'employee_id' => 500149385,
                'amount' => 54527,
            ],
            [
                'employee_id' => 500149393,
                'amount' => 72999,
            ],
            [
                'employee_id' => 500149394,
                'amount' => 54527,
            ],
            [
                'employee_id' => 500149395,
                'amount' => 101163,
            ],
            [
                'employee_id' => 500149396,
                'amount' => 68150,
            ],
            [
                'employee_id' => 500149397,
                'amount' => 66245,
            ],
            [
                'employee_id' => 500161344,
                'amount' => 151734,
            ],
            [
                'employee_id' => 500160688,
                'amount' => 377726,
            ],
            [
                'employee_id' => 500186019,
                'amount' => 96308,
            ],
            [
                'employee_id' => 500156357,
                'amount' => 49527,
            ],
            [
                'employee_id' => 500156351,
                'amount' => 49527,
            ],
            [
                'employee_id' => 500172000,
                'amount' => 78133,
            ],
            [
                'employee_id' => 500172010,
                'amount' => 78133,
            ],
            [
                'employee_id' => 500172011,
                'amount' => 85033,
            ],
            [
                'employee_id' => 500172002,
                'amount' => 85033,
            ],
            [
                'employee_id' => 500172006,
                'amount' => 85033,
            ],
            [
                'employee_id' => 500172001,
                'amount' => 85033,
            ],
            [
                'employee_id' => 500172007,
                'amount' => 85033,
            ],
            [
                'employee_id' => 500182885,
                'amount' => 85033,
            ],
            [
                'employee_id' => 500269147,
                'amount' => 77893,
            ],
            [
                'employee_id' => 500269144,
                'amount' => 77893,
            ],
            [
                'employee_id' => 500269145,
                'amount' => 77893,
            ],
            [
                'employee_id' => 500269146,
                'amount' => 77893,
            ],
            [
                'employee_id' => 500276255,
                'amount' => 77893,
            ],
            [
                'employee_id' => 500183957,
                'amount' => 70591,
            ],
            [
                'employee_id' => 500183958,
                'amount' => 70591,
            ],
            [
                'employee_id' => 500183959,
                'amount' => 70591,
            ],
            [
                'employee_id' => 500183961,
                'amount' => 70591,
            ],
            [
                'employee_id' => 500186015,
                'amount' => 159168,
            ],
            [
                'employee_id' => 500183964,
                'amount' => 70591,
            ],
            [
                'employee_id' => 500183965,
                'amount' => 70591,
            ],
            [
                'employee_id' => 500183963,
                'amount' => 70591,
            ],
            [
                'employee_id' => 500183966,
                'amount' => 70591,
            ],
            [
                'employee_id' => 500183967,
                'amount' => 70591,
            ],
            [
                'employee_id' => 500183968,
                'amount' => 70591,
            ],
            [
                'employee_id' => 500183956,
                'amount' => 70591,
            ],
            [
                'employee_id' => 500215931,
                'amount' => 70591,
            ],
            [
                'employee_id' => 500247906,
                'amount' => 58422,
            ],
            [
                'employee_id' => 500247907,
                'amount' => 58421,
            ],
            [
                'employee_id' => 500201375,
                'amount' => 54527,
            ],
        ];
        foreach ($data as $d) {
            $key = 'employer_pay_members_' . $date . '_' . $d['employee_id'] . '_' . $d['amount'];
            if (Data::has($key)) {
                $this->line('Found ' . $key);
                if ($clear) {
                    Data::del($key);
                    $this->line('Cleared ' . $key);
                }
            } else {
                $this->line('Not found ' . $key);
            }
        }
    }

    public function testChangePinWith2Cards()
    {
        $cardNumber = trim(file_get_contents('/var/secure/span/331960.txt'));
        if (!$cardNumber) {
            return;
        }

        $uc = UserCard::find(331960);
        $api = RapidAPI::getForUserCard($uc);
        $cards = $api->getCachedCardDetailsData($uc);
        foreach ($cards as $card) {
            $masked = $card['maskedCardNumber'];
            $pan = substr($masked, 0, 4) . $cardNumber . substr($masked, -4);
            echo 'Checking pan ' . $pan . "\n";
        }

        $newPin = Util::randNumber(4);

        try {
            RapidService::changePin($uc, $newPin, $cardNumber);
        } catch (\Exception $e) {
            $this->lineNoLog($e->getMessage());
        }
    }

    public function testPayPalDenyEmail()
    {
        Email::$disabled = false;
        $body = Util::render('@UsUnlocked/Email/paypal_denied.html.twig', [
            'amount' => Money::format(3215, 'USD'),
            'when' => Carbon::parse('2023-06-08T16:49:40Z')->format(Util::DATE_TIME_TZ_FORMAT),
            'desc' => 'PAYPAY*TEST NAME',
        ]);
        $user = User::findByEmail('<EMAIL>');
        Email::sendWithTemplateToUser($user, Email::TEMPLATE_BASE_LAYOUT, [
            'name' => $user->getFullName(),
            'subject' => 'Your PayPal load was denied',
            'body' => $body,
        ]);
    }

    public function testMoveBalanceMatches()
    {
        foreach ([
            'move_balance_from_legacy_12345',
            'move_balance_from_legacy_12345_@_2023-07-18',
        ] as $subject) {
            $matches = [];
            preg_match('|move_balance_from_legacy_(\d+)(_.+)?|', $subject, $matches);
            Util::dump($subject, $matches);
        }
    }

    public function testCachePushPopActions()
    {
        $key = 'test_push_pop';
        Data::push($key, 1);
        Data::push($key, 2);
        Data::push($key, 3);
        var_dump(Data::pop($key));
        var_dump(Data::pop($key));
        var_dump(Data::pop($key));
        var_dump(Data::pop($key));
    }

    public function testCacheArrayActions()
    {
        $key = 'test_cache_array';
        Data::pushToArray($key, 1);
        Data::pushToArray($key, 2);
        Data::pushToArray($key, 4);
        Data::pushToArray($key, 4);
        Data::removeFromArray($key, 1);
        Data::removeFromArray($key, 2);
        Data::removeFromArray($key, 3);
        dd(Data::getArray($key));
    }

    public function testRequestAndCommand()
    {
        $request = Util::request();
        $this->line($request->getUri());
        $this->line($request->getRequestUri());
        $this->line($request->getBaseUrl());

        $command = Util::$console;
        if ($command) {
            $this->line($command->getName());
        }
    }

    public function testPluck()
    {
        $all = [
            ['id' => 1],
            ['id' => 2],
            ['id' => 3],
        ];
        $ids = pluck($all, 'id');
        dd($ids);
    }

    public function testTwilioSlackAlert()
    {
        $ei = '[HTTP 503] Unable to create record: Service is unavailable. Please try again';
        $info = [
            'to' => '+52 686 383 5293',
            'msg' => 'Tu código de seguridad es 210885. TransferMex',
            'countryCode' => 'MX',
            'method' => 'SMS',
            'request_uri' => '/mex/m/user/pin-send',
        ];
        if (Util::containsSubString($ei, [
            'HTTP 500',
            'HTTP 501',
            'HTTP 502',
            'HTTP 503',
            'HTTP 504',
        ])) {
            $context = $info;
            $context['error'] = $ei;
            if (Util::$user) {
                $context['user'] = Util::$user->getSignature();
            }
            if (!empty($context['msg'])) {
                $context['msg'] = preg_replace('/ \d+/', ' ***', $context['msg']);
            }
            if (!empty($context['to'])) {
                $context['to'] = substr($context['to'], 0, 7) . '*****';
            }
            \DevBundle\Services\SlackService::alert('`Twilio` service error', $context);
        }
    }

    public function testSettingCache()
    {
        $key = 'temp_key';
        Data::set($key, 123);
        $this->line(Data::get($key));

        $types = Type::getTypesMap();
        dd($types);

//        $uct = UserCardTransaction::find(2776971);
//        $uct->updateTransferMexFields();
//        $uct->persist();
//        dd($uct->toApiArrayForTransferMex());

        // $this->testFisPurses0();
        // $this->testFisPurses();
//        $this->testTransfer('day');
//        $this->testTransfer('week');
//        $this->testTransfer('month');
    }

    public function testAES()
    {
        $this->line('key: ' . Util::getDecryptedParameter('aes_key'), null, [], false);
        $this->line('iv: ' . Util::getDecryptedParameter('aes_iv'), null, [], false);

        $a = Security::aesEncrypt('Hello');
        $this->line('ciphertext: ' . $a);
        $this->line('encrypted: ' . Security::aesDecrypt($a));
    }

    public function testSupportID()
    {
        $uid = trim(file_get_contents('/tmp/uid'));
        $user = User::find($uid);
        $key = 'supportId2Fa';

        $secret = AuthService::ensureSecret($user, $key);
        $this->lineNoLog('secret: ' . Security::rsaDecrypt($secret));
        $this->lineNoLog('timestamp: ' . microtime(true) . ' - ' . AuthService::getGoogle2FA()->getTimestamp());

        $code = AuthService::getCurrentCode($user, $key);
        $this->lineNoLog('code: ' . $code);
    }

    public function testThe2FaVerify()
    {
        $secret = trim(file_get_contents('/tmp/test_2fa_secret'));
        $this->line('Secret: ' . Util::maskPan($secret, 3), null, [], false);

        $code = trim(file_get_contents('/tmp/test_2fa_code'));
        $this->line('Code: ' . $code);

        $new = new Google2FA();
        $this->line('New code - ' . $new->getCurrentOtp($secret));
        $this->line('Verify new code - ' . ($new->verify($code, $secret) ? 'true' : 'false'));

//        $old = new GoogleAuthenticator();
//        $this->line('Old code - ' . $old->getCode($secret));
//        $this->line('Verify old code - ' . ($old->checkCode($secret, $code) ? 'true' : 'false'));
    }

    public function test2Fa()
    {
        $new = new Google2FA();
        $secret = $new->generateSecretKey();
        $this->line('Secret new - ' . $secret);
        $this->line('Current code - ' . $new->getCurrentOtp($secret));

//        $old = new GoogleAuthenticator();
//        $this->line('Old code - ' . $old->getCode($secret));
//        $this->line('==================');

//        $secret = $old->generateSecret();
//        $this->line('Secret old - ' . $secret);
//        $this->line('Current code - ' . $new->getCurrentOtp($secret));
//        $this->line('Old code - ' . $old->getCode($secret));
    }

    public function refundLoadFeeForSkippedLoads()
    {
        foreach (LoadCardService::SKIP_LOADS as $ref) {
            $this->line('===== Checking payment reference ' . $ref);
            $ucl = UserCardLoad::findByPaymentReference($ref);
            if (!$ucl) {
                continue;
            }
            $fee = $ucl->getLoadFeeUSD();
            $this->line('Load fee to charge: ' . $fee . ', for ucl ' . $ucl->getId());
            if (!$fee) {
                continue;
            }
            $uc = $ucl->getUserCard();
            $user = $uc->getUser();
            $type = 'load_fee_reverse';
            $uc->updateBalanceBy($fee, $type, $ucl->getMethodName() . ' of refunding load fee for ucl ' . $ucl->getId(), null, true);
            PrivacyService::$usersChangedBalance[$user->getId()] = $type;
            Util::updateMeta($ucl, 'manuallyLoadShowFee');
            $this->line('Refunded the load fee for user ' . $user->getId());
        }

        PrivacyService::updateSpendLimitForBalanceChangedUsers();
    }

    public function testGcMft()
    {
        $sftp = new SFTP(Util::getConfigKey('gc_ftp_host'));

        $rsa = new RSA();
        $rsa->setPassword(Util::getKmsParameter('gc_ftp_password'));
//        $rsa->setPublicKeyFormat(RSA::PUBLIC_FORMAT_OPENSSH);
//        $rsa->setPrivateKeyFormat(RSA::PRIVATE_FORMAT_OPENSSH);
//        $keys = $rsa->createKey(4096, 31536000);
//        file_put_contents('/var/secure/span/id_rsa_gc2', $keys['privatekey']);
//        file_put_contents('/var/secure/span/id_rsa_gc2.pub', $keys['publickey']);
//        dd($keys);


        $loaded = $rsa->loadKey(file_get_contents(Util::getConfigKey('gc_ftp_ssh')));
        if (!$loaded) {
            $this->line('Failed to load the gc private key...', 'error');
            return false;
        }

        if (!$sftp->login(
            Util::getKmsParameter('gc_ftp_username'),
//            Util::getConfigKey('gc_ftp_password'),
            $rsa
        )) {
            $this->line('Failed to login the gc ftp server...', 'error', [
                'errors' => $sftp->getErrors(),
                'sftp_errors' => $sftp->getSFTPErrors(),
            ]);
            return false;
        }
        $files = $sftp->nlist('./out');
        dd($files);
    }

    public static function testStaticMethod()
    {
        Util::consoleLine('static method...');

        self::testAndOperatorAsIf();
        Util::consoleLine('instance method...');
    }

    protected function testSpanishEmail()
    {
        Email::$disabled = false;
        Email::sendWithTemplate(['<EMAIL>'], Email::TEMPLATE_SIMPLE_LAYOUT, [
            '_lang' => 'en',
            'subject' => 'Test en',
        ]);
        Email::sendWithTemplate(['<EMAIL>'], Email::TEMPLATE_SIMPLE_LAYOUT, [
            '_lang' => 'es',
            'subject' => 'Test es',
        ]);
        Email::$disabled = true;
    }

    protected static function testAndOperatorAsIf()
    {
        /** @var BaseCommand $console */
        $console = null;
        $console && $console->line('Hello from null');

        $console = Util::$console;
        $console && $console->line('Hello from console');
    }

    protected function testFindRapydTransactions()
    {
        $transfer = Transfer::find(90);
        $r = $this->em->getRepository(RapydTransaction::class)
            ->createQueryBuilder('rt')
            ->where('rt.transfer = :transfer')
            ->select('sum(rt.amount) sm, count(rt.id) ct')
            ->setParameter('transfer', $transfer)
            ->getQuery()
            ->getSingleResult();
        dd($r);
    }

    protected function testTmxMarkupPlan()
    {
        $tz = Util::tzCentral();
        $dates = [
            Carbon::create(2023, 6, 27, 15, 12, 0, $tz),
            Carbon::create(2023, 6, 28, 15, 12, 0, $tz),
            Carbon::create(2023, 7, 4, 15, 12, 0, $tz),
            Carbon::create(2023, 7, 5, 15, 12, 0, $tz),
            Carbon::create(2023, 7, 13, 15, 12, 0, $tz),
            Carbon::create(2023, 7, 20, 15, 12, 0, $tz),
            Carbon::create(2023, 7, 28, 15, 12, 0, $tz),
            Carbon::create(2023, 8, 2, 0, 0, 0, $tz),
            Carbon::create(2023, 8, 12, 0, 0, 0, $tz),
            Carbon::create(2023, 8, 18, 0, 0, 0, $tz),
            Carbon::create(2023, 8, 23, 0, 0, 0, $tz),
            Carbon::create(2023, 8, 30, 0, 0, 0, $tz),
            Carbon::create(2023, 9, 3, 0, 0, 0, $tz),
            Carbon::create(2023, 9, 10, 0, 0, 0, $tz),
            Carbon::create(2023, 9, 15, 0, 0, 0, $tz),
        ];
        /** @var Carbon $date */
        foreach ($dates as $date) {
            $rate = $this->testTmxMarkupPlanItem($date);
            $this->line($date->format('Y-m-d') . ' -> ' . $rate);
        }
    }

    protected function testTmxMarkupPlanItem(Carbon $now)
    {
        $rate = RapydDisburseService::RATE_MARK_UP_PARTNER;
        $tz = Util::tzCentral();
        $from = Carbon::create(2023, 6, 28, 0, 0, 0, $tz);
        if ($now->lt($from)) {
            return $rate;
        }
        for ($i = 0; $i < 10; $i++) {
            $rate += 0.0005;
            $from->addWeek();
            if ($now->lt($from)) {
                break;
            }
        }
        return round($rate, 6);
    }

    protected function testStringy()
    {
        $type = 'positive_balance';
        $this->line(Stringy::create($type)->pascalCase());
        $this->line(Stringy::create($type)->studlyCase());
        $this->line(Stringy::create($type)->titleizeForHumans());
        $this->line(Stringy::create($type)->titleize());
        $this->line(Stringy::create($type)->toTitleCase());
        $this->line(Stringy::create($type)->camelize());
        $this->line(Stringy::create($type)->upperCamelize());

        $ss = [
            'HANS_ZHANG',
            'HANS-ZHANG',
            'Hans Zhang',
            '*(&@FS)BC',
            '张三',
            'édgar catalán',
        ];
        foreach ($ss as $s) {
            $this->line($s . ' => ' . Util::humanize($s));
            $this->line($s . ' => ' . Util::title($s));
            $this->line($s . ' => ' . Stringy::create($s)->toTitleCase());
            $this->line($s . ' => ' . Stringy::create($s)->dasherize());
            $this->line($s . ' => ' . Stringy::create($s)->underscored());
            $this->line($s . ' => ' . Stringy::create($s)->toAscii());
            $this->line($s . ' => ' . Stringy::create($s)->pascalCase());
            $this->line($s . ' => ' . Stringy::create($s)->upperCamelize());
        }
    }

    protected function testPrivacyErrorEmail()
    {
        Data::setArray('privacy_error_pool', [
            6059618,
            5890938,
            5890908,
            5890850,
            5890846,
            5890439,
            5890389,
            5890377,
            5890367,
            5890358,
            5890302,
            5890293,
            5890266,
            5890154,
            5890144,
            5692095,
            5654459,
            5654434,
            5654426,
            5654419,
        ]);
    }

    protected function testRapidErrorEmail()
    {
        Data::setArray('rapid_error_pool', [
            5706889,
            5706893,
            5707033,
            5707034,
            5707036,
            5707037,
            5707038,
            5707039,
            5707040,
            5707045,
            5707046,
            5707049,
            5707050,
            5707051,
            5707052,
            5707053,
        ]);
    }

    protected function testRapydErrorEmail()
    {
        Data::setArray('rapyd_error_pool', [
            5532076,
        ]);
    }

    protected function testUniTellerErrorEmail()
    {
        Data::setArray('uniteller_error_pool', [
            5716390,
            5717320,
            5718292,
            5750487,
            5755229,
            5755235,
            5755241,
            5769745,
            5777599,
            5795635,
            5819602,
            5825286,
            5826953,
        ]);
    }

    protected function testTotalProperty()
    {
        $parts = [];
        TransferService::getInstantTotalProperty($parts);

        foreach ($parts as $i => $part) {
            if (is_array($part)) {
                foreach ($part as $j => $item) {
                    $parts[$i][$j] = Money::usdFormat($item);
                }
            } else {
                $parts[$i] = Money::usdFormat($part);
            }
        }
        Util::dump($parts);
    }

    protected function testTransfer ($type) {
        $tz = Util::tzNewYork();
        $expr = Util::expr();
        $start = '2021-01-04';
        $total = MonthlySnapshotReport::MONTH_AGGREGATE;
        // $type = 'month';
        $fromOption = $start;
        $from = Carbon::createFromFormat(Util::DATE_TIME_FORMAT_SEARCH_FULL, $fromOption . ' 00:00:00', $tz);
        $fromStart = $fromOption === $start;

        $base = [
          'type' => $type,
          'month' => $total,

          'transferCount' => 0,
          'transferAmount' => 0,
          'transferAvgCount' => 0,
          'transferAvgAmount' => 0,

          'bankTransferCount' => 0,
          'bankTransferAmount' => 0,
          'bankTransferAvgCount' => 0,
          'bankTransferAvgAmount' => 0,

          'cashTransferCount' => 0,
          'cashTransferAmount' => 0,
          'cashTransferAvgCount' => 0,
          'cashTransferAvgAmount' => 0,
      ];

      $data = [];
      if ($type === 'week') {
          for ($date = $from->copy(); !$date->isFuture(); $date->addWeek()) {
              $month = Util::formatWeek($date);
              $base['month'] = $month;
              $data[$month] = $base;
          }
      } else if ($type === 'day') {
          for ($date = $from->copy(); !$date->isFuture(); $date->addDay()) {
              $month = $date->format('Y-m-d');

            if (!in_array($month, [
              '2023-04-01',
              '2023-04-02',
              '2023-04-03',
              '2023-04-04',
              '2023-04-05',
              '2023-04-06',
              '2023-04-07',
              '2023-04-08',
              '2023-04-09',
              '2023-04-10',
              '2023-04-11',
              '2023-04-12',
              '2023-04-13',
              '2023-04-14',
              '2023-04-15',
              '2023-04-16',
              '2023-04-17',
              '2023-04-18',
              '2023-04-19',
              '2023-04',
              ])) {
              //  Log::debug('skip');
              continue;
            }
            if (!in_array($month, [
              '2023-W16',
              '2023-W15',
              '2023-W14'])) {

              }
              $base['month'] = $month;
              $data[$month] = $base;
          }
      } else {
          for ($date = $from->copy(); !$date->isFuture(); $date->addMonthNoOverflow()) {
              $month = $date->format('Y-m');

            if (!in_array($month, [
              '2023-04-01',
              '2023-04-02',
              '2023-04-03',
              '2023-04-04',
              '2023-04-05',
              '2023-04-06',
              '2023-04-07',
              '2023-04-08',
              '2023-04-09',
              '2023-04-10',
              '2023-04-11',
              '2023-04-12',
              '2023-04-13',
              '2023-04-14',
              '2023-04-15',
              '2023-04-16',
              '2023-04-17',
              '2023-04-18',
              '2023-04-19',
              '2023-04',
              ])) {
               // Log::debug('skip');
              continue;
            }
              $base['month'] = $month;
              $data[$month] = $base;
          }
      }
      if ($fromStart) {
          $month = $total;
          $base['month'] = $month;
          $data[$month] = $base;
      }
      // Log::debug('data---11111', $data);
       // ---------- Transfer ----------
        $rs = $this->em->getRepository(Transfer::class)
        ->createQueryBuilder('t')
        ->join('t.sender', 'sender')
        ->where($expr->in('t.partner', ':partner'))
        ->andWhere($expr->in('t.status', ':statuses'))
        ->andWhere('t.sendAt >= :sendAt')
        ->andWhere('t.payoutType >= :payoutType')
        ->setParameter('partner', [Transfer::PARTNER_RAPYD, Transfer::PARTNER_UNITELLER])
        ->setParameter('statuses', Transfer::STATUSES_SENT)
        ->setParameter('sendAt', Util::toUTC($from))
        ->setParameter('payoutType', 'bank')
        ->select('sender.id user, t.payoutType, t.receiveAt, t.sendAt, t.sendAmount, t.revenue, t.platformRevenue, t.partner')
        ->getQuery()
        ->getArrayResult();
        $this->line('Total ' . count($rs) . ' bank transfer transactions...');
        // get UniTeller cash transfer
        $cashTransferMex = $this->em->getRepository(Transfer::class)
            ->createQueryBuilder('t')
            ->join('t.sender', 'sender')
            ->where($expr->in('t.partner', ':partner'))
            ->andWhere($expr->in('t.status', ':statuses'))
            ->andWhere('t.receiveAt >= :receiveAt')
            ->andWhere('t.payoutType >= :payoutType')
            ->setParameter('payoutType', 'cash')
            ->setParameter('partner', [Transfer::PARTNER_RAPYD, Transfer::PARTNER_UNITELLER])
            ->setParameter('statuses', [Transfer::STATUS_COMPLETED])
            ->setParameter('receiveAt', Util::toUTC($from))
            ->select('sender.id user, t.payoutType, t.sendAt, t.receiveAt, t.sendAmount, t.revenue, t.platformRevenue, t.partner')
            ->getQuery()
            ->getArrayResult();

         $this->line('Total ' . count($cashTransferMex) . ' cash transfer transactions...');

        $rs = array_merge($rs, $cashTransferMex);

        $count = count($rs);

        $total = 0;
        $this->line('Total ' . $count . ' transfer transactions...');
        foreach ($rs as $i => $r) {
            if (($i + 1) % 1000 === 0) {
                $this->line(($i + 1) . '/' . $count . ': Calculating transfers...');
            }

            $month = $r['payoutType'] == 'bank' ? $this->formatDate($r['sendAt'], $tz, $type) : ($r['receiveAt'] ? $this->formatDate($r['receiveAt'], $tz, $type) : $this->formatDate($r['sendAt'], $tz, $type));

            if (!in_array($month, [
              '2023-04-01',
              '2023-04-02',
              '2023-04-03',
              '2023-04-04',
              '2023-04-05',
              '2023-04-06',
              '2023-04-07',
              '2023-04-08',
              '2023-04-09',
              '2023-04-10',
              '2023-04-11',
              '2023-04-12',
              '2023-04-13',
              '2023-04-14',
              '2023-04-15',
              '2023-04-16',
              '2023-04-17',
              '2023-04-18',
              '2023-04-19',
              '2023-04',
              '2023-W16',
              '2023-W15',
              '2023-W14',
              ])) {
               // Log::debug('skip');
              continue;
            }
            if (in_array($month, [
              '2023-04-01',
              '2023-04-02',
              '2023-04-03',
              '2023-04-04',
              '2023-04-05',
              '2023-04-06',
              '2023-04-07',
              '2023-04-08',
              '2023-04-09',
              '2023-04-10',
              '2023-04-11',
              '2023-04-12',
              '2023-04-13',
              '2023-04-14',
              '2023-04-15',
              '2023-04-16',
              '2023-04-17',
              '2023-04-18',
              '2023-04-19'])) {
                $total++;
              }
           // Log::debug('skip 01');
            if (empty($data[$month])) {
                $base['month'] = $month;
                $data[$month] = $base;
            }
            $user = $r['user'];
            //$data[$month]['transferMembers'][$user] = true;
            $data[$month]['transferCount']++;
            $data[$month]['transferAmount'] += $r['sendAmount'];

            // $data[$month]['incomeTransfer'] += $r['revenue'];

            // $data[$month]['extraRevenuePlatform'] += $r['platformRevenue'] ?: 0;

            // $data[$month]['grossRevenuePlatform'] += $r['revenue'] * 0.5 + $r['platformRevenue'] ?: 0;
            // $data[$month]['grossRevenuePartner'] += $r['revenue'] * 0.5;

            // if ($fromStart) {
            //     $data[$total]['transferMembers'][$user] = true;
            // }


            if ($r['partner'] == Transfer::PARTNER_UNITELLER) {
              // $data[$month]['cashTransferMembers'][$user] = true;
              $data[$month]['cashTransferCount']++;
              $data[$month]['cashTransferAmount'] += $r['sendAmount'];
              // if ($fromStart) {
              //     $data[$total]['cashTransferMembers'][$user] = true;
              // }
            } else if ($r['partner'] == Transfer::PARTNER_RAPYD) {
              // $data[$month]['bankTransferMembers'][$user] = true;
              $data[$month]['bankTransferCount']++;
              $data[$month]['bankTransferAmount'] += $r['sendAmount'];
              // if ($fromStart) {
              //     $data[$total]['bankTransferMembers'][$user] = true;
              // }
            }
        }
        Log::debug('total---' . $total);
       //  Log::debug('base---', $base);
        Log::debug('data---', $data);
    }

    protected function formatDate(\DateTime $dt, \DateTimeZone $tz, $type)
    {
        $dt = Carbon::instance($dt)->setTimezone($tz);
        if ($type === 'week') {
            return Util::formatWeek($dt);
        }
        if ($type === 'day') {
            return $dt->format('Y-m-d');
        }
        return $dt->format('Y-m');
    }

    protected function testEmoji()
    {
        foreach ([
            'Pekee❤️😍😘 de Martinez',
            'a 测试 123',
            'Ángel Fernando',
            'Utestående arbeid',
            "+1\u{202a}**********\u{202c}",
            "+1\u202a**********\u202c",
        ] as $item) {
            $this->line('"' . $item . '" => ' . Util::cleanUtf8String($item));
            $this->line('"' . $item . '" ====> ' . Util::cleanUnicodeStringExtra($item));
        }
    }

    protected function clearSingletonCommandLocks()
    {
        Data::delAllWith('singleton_command_');
    }

    protected function testDoctrineRefresh()
    {
        $me = User::findByEmail('<EMAIL>');
        $this->line('Source: ' . $me->getSource());
        $this->line('Team: ' . $me->getTeamName());
        sleep(10);
        Util::em()->refresh($me);
        $this->line('Source: ' . $me->getSource());
        $this->line('Team: ' . $me->getTeamName());
    }

    protected function testDoctrineDetach()
    {
        $me = User::findByEmail('<EMAIL>');
        $this->line('Source: ' . $me->getSource());
        $this->line('Team: ' . $me->getTeamName());
        sleep(10);
//        Util::em()->detach($me);
//        $me = User::findByEmail('<EMAIL>');
        $me = Util::detach($me);
        $this->line('Source: ' . $me->getSource());
        $this->line('Team: ' . $me->getTeamName());
    }

    protected function testFisPurses0()
    {
        $xml = <<<EOT
<?xml version="1.0" encoding="utf-8"?>
<WebServicePayloadOfGetPurseBalance_ByCUID xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://www.a2asoa.com/">
  <ServiceResponse>
    <ErrorNumber>0</ErrorNumber>
    <ErrorDescription />
    <ExceptionType />
    <Duration>187</Duration>
    <RequestReceived>2022-10-24T00:12:10.4467176-04:00</RequestReceived>
    <ResponseSent>2022-10-24T00:12:10.6197269-04:00</ResponseSent>
  </ServiceResponse>
  <PayloadCollection>
    <DataCollection>
      <GetPurseBalance_ByCUID>
        <SSN>500109163</SSN>
        <ClientUniqueid>500109163</ClientUniqueid>
        <Proxy>0045926132077</Proxy>
        <SubProgramID>797740</SubProgramID>
        <CardStatus>READY</CardStatus>
        <PurseEffectiveDate>Jan  1 2022 12:00AM</PurseEffectiveDate>
        <PurseExpirationDate>Dec 31 2023 12:00AM</PurseExpirationDate>
        <PurseNumber>32200</PurseNumber>
        <PurseType>CMB</PurseType>
        <PurseStatus>ACTIVE</PurseStatus>
        <PurseBalance>0.00</PurseBalance>
      </GetPurseBalance_ByCUID>
    </DataCollection>
  </PayloadCollection>
</WebServicePayloadOfGetPurseBalance_ByCUID>
EOT;
        $api = new PrepaidAPI();
        $resp = $api->parseXmlResponse(new ExternalInvoke(), $xml);
        var_dump($resp);
        return;
    }

    protected function testFisPurses()
    {
        $xml = <<<EOT
<?xml version="1.0" encoding="utf-8"?>
<WebServicePayloadOfGetPurseBalance_ByCUID xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://www.a2asoa.com/">
  <ServiceResponse>
    <ErrorNumber>0</ErrorNumber>
    <ErrorDescription/>
    <ExceptionType/>
    <Duration>16</Duration>
    <RequestReceived>2022-09-05T04:08:48.4248616-04:00</RequestReceived>
    <ResponseSent>2022-09-05T04:08:48.4458562-04:00</ResponseSent>
  </ServiceResponse>
  <PayloadCollection>
    <DataCollection>
      <GetPurseBalance_ByCUID>
        <SSN>500109163</SSN>
        <ClientUniqueid>500109163</ClientUniqueid>
        <Proxy>0905491798655</Proxy>
        <SubProgramID>797740</SubProgramID>
        <CardStatus>READY</CardStatus>
        <PurseEffectiveDate>Jan  1 2022 12:00AM</PurseEffectiveDate>
        <PurseExpirationDate>Dec 31 2023 12:00AM</PurseExpirationDate>
        <PurseNumber>32200</PurseNumber>
        <PurseType>CMB</PurseType>
        <PurseStatus>ACTIVE</PurseStatus>
        <PurseBalance>3.69</PurseBalance>
      </GetPurseBalance_ByCUID>
      <GetPurseBalance_ByCUID>
        <SSN>500109163</SSN>
        <ClientUniqueid>500109163</ClientUniqueid>
        <Proxy>0905491798655</Proxy>
        <SubProgramID>797740</SubProgramID>
        <CardStatus>READY</CardStatus>
        <PurseEffectiveDate>Jan  1 2022 12:00AM</PurseEffectiveDate>
        <PurseExpirationDate>Dec 31 2100 12:00AM</PurseExpirationDate>
        <PurseNumber>42200</PurseNumber>
        <PurseType>CSH</PurseType>
        <PurseStatus>ACTIVE</PurseStatus>
        <PurseBalance>2.46</PurseBalance>
      </GetPurseBalance_ByCUID>
    </DataCollection>
  </PayloadCollection>
</WebServicePayloadOfGetPurseBalance_ByCUID>
EOT;
        $api = new PrepaidAPI();
        $resp = $api->parseXmlResponse(new ExternalInvoke(), $xml);
        var_dump($resp);
        return;

        $purse = null;
        $first = null;
        if ($purse) {
            foreach ($result as $item) {
                $it = $item['GetPurseBalance_ByCUID']['PurseNumber'] ?? '';
                if ((string)$it === $purse->getNumber()) {
                    $first = $item['GetPurseBalance_ByCUID'];
                    break;
                }
            }
        }
        if (!$first) {
            $first = $result[0]['GetPurseBalance_ByCUID'] ?? [];
        }

        // Update cards first
        if (empty($uc->getDbaNo())) {
            $uc->setDbaNo($first['PurseNumber']);
        }
        $active = in_array($first['CardStatus'], ['READY', 'ACTIVE']);
        $uc->setStatus($active ? UserCard::STATUS_ACTIVE : UserCard::STATUS_INACTIVE, false, false)
            ->setNativeStatus($first['CardStatus'])
            ->persist();

        // Create or update purses
        $em = Util::em();
        foreach ($result as $item) {
            $it = $item['GetPurseBalance_ByCUID'] ?? [];
            if (empty($it['PurseNumber'])) {
                continue;
            }
            $pu = $uc->findPurseByNumber($it['PurseNumber']);
            if (!$pu) {
                $pu = new UserCardPurse();
                $pu->setUserCard($uc)
                    ->setNumber($it['PurseNumber']);
            }
            $pu->setCardStatus($it['CardStatus'])
                ->setStatus($it['PurseStatus'])
                ->setType($it['PurseType'])
                ->setEffectiveDate(Carbon::parse($it['PurseEffectiveDate']))
                ->setExpirationDate(Carbon::parse($it['PurseExpirationDate']))
                ->setBalance(Money::normalizeAmount($it['PurseBalance'], 'USD'));

            Util::updateMeta($pu, [
                'fisDetail' => $it,
            ], false);

            $em->persist($pu);

            if ($purse && (string)$purse->getNumber() === (string)$pu->getNumber()) {
                $purse = $pu;
            }
        }
    }

    protected function previousTest()
    {
        $user = User::find(*********);
        Email::sendWithTemplateToUser($user, Email::TEMPLATE_BASE_LAYOUT, [
            'subject' => 'Failed to load your account',
            'body' => '<p>There was an issue loading your account, please reach out to <a href="mailto:<EMAIL>"><EMAIL></a> for further assistance.</p>',
        ], CardProgram::usunlocked());
        return;

        $origin = '<p>JULIO ALDANA 500333 123RBFOH Watsonville Transfer max
FCO I MADERO SN CENTRO Checking Account XXX';
        $matches = [];
        preg_match('`<p>([A-Za-z\d#.,&;/\- ]+?) (MX_)?(\d+) .+\n.+ Account XXX`m', $origin, $matches);
        dd($matches);

        Util::$platform = Platform::transferMex();
        $user = User::find(*********);
        dd(RapidOperationQueue::hasPendingNegativeOps($user));

//        Email::sendWithTemplateToUser($user, Email::TEMPLATE_SIMPLE_LAYOUT, [
//            'name' => $user->getFullName(),
//            'subject' => 'Hola!',
//            'body' => '<p>Hola!</p>',
//        ]);

//        $domains = Platform::getAllInternalDomains();
//        dd($domains);
//
//        $lines = explode("\n", file_get_contents(__DIR__ . '/../../../../db/local/change_pin_log.txt'));
//        $lines = array_filter($lines, function ($line) {
//            return strpos($line, 'Failed for ') !== false;
//        });
//        file_put_contents(__DIR__ . '/../../../../db/local/change_pin_log_failed.txt', implode("\n", $lines));
//        return;

//        Util::updateMeta($user, [
//            'passcode' => '123',
//        ]);
//        dd($user->hasPasscode());

//        $this->testSimpleEncrypt();
//        $this->testSimpleDecrypt();
    }

    protected function testSimpleDecrypt()
    {
        $cipher = file_get_contents('/tmp/test_ciphertext.txt');
        $plain = Security::rsaDecryptWhenNecessary($cipher);
        dd($plain);
    }

    protected function testSimpleEncrypt()
    {
        Security::createRsaKeyPair();
        $plain = 'Hello World';
        $cipher = Security::rsaEncrypt($plain);
        $plain2 = Security::rsaDecrypt($cipher);
        dd([
            $plain,
            $cipher,
            $plain2,
        ]);
    }

    protected function checkFisPurseDates()
    {
        $dates = [
            'Nov  1 2021 12:00AM',
            'Dec 31 2021 12:00AM',
        ];
        foreach ($dates as $date) {
            $c = Carbon::parse($date);
            var_dump($c);
        }
    }

    protected function findCombination()
    {
        $numbers = [
            720, 367, 981, 97, 1307,
            64, 365, 1376, 1723, 547,
            195, 195, 321, 157,
        ];
        $target = 3043;
        $found = [];
        foreach ($numbers as $a) {
            foreach ($numbers as $b) {
                if ($a + $b === $target) {
                    $keys = [$a, $b];
                    sort($keys);
                    $key = implode('+', $keys);
                    if (empty($found[$key])) {
                        $found[$key] = true;
                        $this->line($a . ' + ' . $b . ' = ' . $target);
                    }
                }
            }
        }
        foreach ($numbers as $a) {
            foreach ($numbers as $b) {
                foreach ($numbers as $c) {
                    if ($a + $b + $c === $target) {
                        $keys = [$a, $b, $c];
                        sort($keys);
                        $key = implode('+', $keys);
                        if (empty($found[$key])) {
                            $found[$key] = true;
                            $this->line($a . ' + ' . $b . ' + ' . $c . ' = ' . $target);
                        }
                    }
                }
            }
        }
        foreach ($numbers as $a) {
            foreach ($numbers as $b) {
                foreach ($numbers as $c) {
                    foreach ($numbers as $d) {
                        if ($a + $b + $c + $d === $target) {
                            $keys = [$a, $b, $c, $d];
                            sort($keys);
                            $key = implode('+', $keys);
                            if (empty($found[$key])) {
                                $found[$key] = true;
                                $this->line($a . ' + ' . $b . ' + ' . $c . ' + ' . $d . ' = ' . $target);
                            }
                        }
                    }
                }
            }
        }
    }

    protected function testPdfFormat()
    {
        $ht = <<<EOT
<p><b>Name                                                 EMP#                          Crew                                Deposit Information                           Amount
</b></p>
<p>PEDRO ANTONIO AGUSTIN 674283 123JEH1_Oxnard Transfer Max
TIZINGARI 35 LAZARO Checking Account XXX3068 712.61
CARDENAS
PATAMBAN  59770 MX </p>

</div></div>
"
EOT;

        preg_match('`<p>([A-Za-z\d#.,&;/\- ]+) ([A-Z_\d]+) .+\n.+ Account XXX`m', $ht, $matches);
        var_dump($matches);
    }

    protected function oldExecute()
    {
        $all = SlackService::getMentionUsers([
            '<@U03GHKYFR53>',
            '<@UEGRC871C>',
        ]);
        $this->line(json_encode($all));
        return;

        Util::$platform = Platform::spendr();
        $mentions = SlackService::getMentionGroup('neg_balance');
        $this->line('Mentions: ' . json_encode($mentions));
        return;

        $a = Carbon::parse('2022-05-26 00:00:00');
        $b = Carbon::parse('2022-05-27 15:01:00');
        $this->line($a->diffInDays($b));

        return;

        $uct = UserCardTransaction::find(742118);
        $old = $uct->toApiArrayForTransferMex();

        $uct->updateTransferMexFields();
        $new = $uct->toApiArrayForTransferMex();

        $uct->persist();

        var_dump($old);
        var_dump($new);

        return;

        $completed = $this->queryStatusOfDupTransfers();
        echo json_encode($completed) . "\n";
        die;

        $this->reverseDupTransfers();

        die;

//        $parsed = Carbon::parse('2022-04-21 07:19:21.263', Util::tzUTC());
//        dd($parsed->format('Y-m-d H:i:s'));

//
//        Util::$platform = Platform::transferMex();
//        $uc = UserCard::find(272420);
//        $api = RapidAPI::getForUserCard($uc);
//        RapidService::checkAndSendEmailsToRapidForTransfer(
//            $api,
//            $uc,
//            'Card API error: Account limit has been reached',
//            45988,
//        );
//        die;
//
//        $settle = $this->em->getRepository(TransferSettlement::class)->find(20);
//        TransferService::checkAndSendEmailsToRapid(
//            $settle,
//            'Card API error: Account limit has been reached',
//            '123456',
//            TransferService::getSettlementParameters($settle),
//        );
//        die;
//
//        $this->testPhoneNumbers();
//        die;
//
//        $date = Carbon::parse('7/2/2022 11:59:59 PM');
//        dd($date);

        $country = Country::findByNumericCode(840);
        dd($country ? $country->getName() : null);

        $skux = new SkuxService();
//        $result = $skux->sendVerificationCode('+***********');
//        $result = $skux->register('+***********', '123456');
        $skux->login('+***********', '123456', 'test_device');
        dd([
            $skux->accessToken,
            $skux->refreshToken,
        ]);

        $this->fixUserHistoryLoadLog();
        return;

        $this->testSnapshotReportFixAlert();
        return;

//
//        $uc = UserCard::find(38821);
//        $uc->setBalance($uc->getBalance() + 1000, 'test', 'Test 😀 ...');
//        $uc->persist();
//
//        die;

        Util::setWeekStartAtMonday();

        // 2022-01-02T18:43:16-05:00 => 2022-W52
        $date = Carbon::parse('2022-01-02T18:43:16-05:00');
        $week = Util::formatWeek($date);
        dd($date, $week, Util::getStartOfWeek($week));

        foreach ([
            '2018-W01',
            '2018-W07',
            '2018-W52',
            '2018-W53',
            '2019-W01',
            '2019-W07',
            '2019-W52',
            '2019-W53',
            '2020-W01',
            '2020-W07',
            '2020-W52',
            '2020-W53',
            '2021-W01',
            '2021-W07',
            '2021-W52',
            '2021-W53',
            '2022-W01',
            '2022-W07',
            '2022-W09',
            '2022-W52',
            '2022-W53',
        ] as $item) {
            $date = Util::getStartOfWeek($item);
            $this->line($item . ' === ' . $date->format('Y-m-d') . ' === ' . Util::formatWeek($date));
        }
        return;

//        $this->fixTransferMexExtraTransactions();
//
//        return;

//        $user = User::findByEmail('<EMAIL>');
//        dd([
//            $user->getAffiliateName(),
//            $user->everLoaded(),
//        ]);
//        $pwd = Util::encodePassword($user, 'Span1234');
//        dd($pwd);

//        $name = "\x42\x6f\x6c\x61\xf1\x6f\x73\x20\x4d\x61\x72\x74\x69\x6e\x65";
//        $name = "阿🎃\x50\xe9\x72\x65\x7a\x20\x43\x68\x6f\x63";
//        $name = "\u{FFF0}\u{FFF1}\u{FFF2}\u{FFF3}\u{FFF4}\u{FFF5}\u{FFF6}\u{FFF7}\u{FFF8}\u{FFF9}\u{FFFA}\u{FFFB}\u{FFFC}\u{FFFD}\u{FFFE}\u{FFFF}\u{FFFa}\u{5238}";
//        $name3 = mb_convert_encoding($name, 'UTF-8', 'UTF-8');
//        $name4 = iconv("UTF-8","UTF-8//IGNORE", $name3);
//        dd([
//            $name3,
//            $name4,
//            Util::cleanUtf8String($name),
//            json_encode($name3),
//        ]);


//        self::testMethod('a', 'b');
//        self::testMethod(1, 2, 3);
//        die;

//        echo json_encode(MerchantType::PRIVACY_TYPES, JSON_PRETTY_PRINT);
//        die;

        // {"accountNumber":"***********","cardNumber":"5220-x*********2449","cardLastFourDigits":"2449","status":"On Hold","__sandbox":true,"agentAccountNumber":"**********"}
//        $params = [
//            'accountNumber' => '123',
//            'cardNumber' => '45**2932',
//            'cardLastFourDigits' => '2932',
//            'status' => 'On Hold',
//        ];
//        if ($params['cardLastFourDigits'] && strpos($params['cardNumber'], '*') !== false) {
//            unset($params['cardNumber']);
//        }
//        dd($params);

//        $now = Carbon::create(2021, 9, 2, 0, 0, 0, 'Asia/Shanghai');
//        var_dump($now);
//
//        $date = Util::timeLocal($now->copy());
//        var_dump($date);
//
//        $date = Util::toUTC($now->copy());
//        var_dump($date);
//
//        die;

//        $fileSize = filesize(__FILE__);
//        dd($fileSize);

//        $q = Pdo::query('select * from users where id = *********');
//        $rs = $q->fetch();
//        dd($rs);

//        $platform = new MySqlPlatform();
//        $sql = $platform->getListTableColumnsSQL('email', 'tern_span_live');
//        dd($sql);

//        $d = Util::em()->getConnection()->getSchemaManager()->listTableColumns('alert');
//        dd($d);

//        $o = ClabeValidator::validate('');
//        dd($o);

//        $dateFormat = 'm/d/Y H:i:s';
//        $time = Carbon::createFromFormat($dateFormat, '01/13/2021 02:27:57');
//        dd($time);

//        $desc = 'Deposited to Indigo Glaze, Account: ***********, Account Loaded for  Indigo Glaze, Account: ***********, rapyd_transfer_reverse - 37';
//        $matches = [];
//        preg_match('|rapyd_transfer_reverse - (\d+)|', $desc, $matches);
//        dd($matches);

//        $a = new Carbon();
//        $a->subDays(150);
//        dd($a);

//        $u = User::find(*********);
////        $u = User::find(*********);
//        SettingService::initForUser($u);


//        $str = 'manual_load - Test by Hans for API';
//        var_dump(Util::title($str));
//        var_dump(Util::title($str, true));
//        var_dump(Util::humanize($str));

//        $ts = $this->em->getRepository(TransferSettlement::class)->find(9);
//        TransferService::settle($ts);

//        [$ei, $result] = RapidSoapAPI::GetGiftCardCatalog();
//        dd($result);

//        $this->echoConstants();

//        $this->testPhoneNumbers();

//        $service = new ImportService();
//        $service->fixAccountBalanceBaseSubProgram();

//        $now = Carbon::now(Util::tzNewYork());
//        dd($now);
//        $user = User::find(*********);
//        Promotion::applyUsuOneYearMembershipFee($user, Promotion::CODE_ONE_YEAR);
//        dd('empty');

//        $start = Carbon::create(2020, 1, 30, 0, 0, 0); // PrivacyAPI::getStartDate();
//        $this->line($start->format('c')); // 2020-01-30T00:00:00+00:00
//        $this->line($start->copy()->addMonthNoOverflow()->format('c')); // 2020-02-29T00:00:00+00:00
//        $this->line($start->copy()->addMonthWithOverflow()->format('c')); // 2020-03-01T00:00:00+00:00

//        $this->exportTransferMexLocationsToExcel();

//        $user = User::findByEmail('<EMAIL>');
//        $encoder  = Util::getPasswordEncoder();
//        $valid = $encoder->isPasswordValid($user, 'Mex12345');
//        dd($valid);
//        $a = RapydDisburseService::getBrands();
//        echo json_encode($a);

//        $this->testCallback(function ($string) {
//            var_dump($string);
//            var_dump(func_get_args());
//        });

//        $info = RapydDisburseService::getRequiredFieldInfo('mx_abc_capital_bank', 'account_number');
//        dd($info);
    }

    protected function testCache()
    {
        Data::set('abc', 'def_' . date('c'));
        $value = Data::get('abc');
        $this->line('Cache abc value: ' . $value);
    }

    protected function queryVoidedTransactions()
    {
        $cpct = CardProgramCardType::getForCardProgram(CardProgram::usunlocked());
        $em = Util::em();
        $expr = Util::expr();
        $startAt = Carbon::create(2022, 5, 1, 0, 0, 0);
        $q = $em->getRepository(UserCardTransaction::class)
            ->createQueryBuilder('uct')
            ->join('uct.userCard', 'uc')
            ->where($expr->in('uct.accountStatus', ':statuses'))
            ->andWhere('uc.card = :card')
            ->andWhere('uct.txnTime >= :txnTime')
            ->andWhere('uct.txnAmount <> :txnAmount')
            ->setParameter('statuses', [
                PrivacyAPI::TRANSACTION_STATUS_SETTLING,
                PrivacyAPI::TRANSACTION_STATUS_SETTLED,
            ])
            ->setParameter('card', $cpct)
            ->setParameter('txnAmount', 0)
            ->setParameter('txnTime', Util::toUTC($startAt));
        $transactions = $q->getQuery()->getResult();
        $count = count($transactions);
        $this->line("Found $count transactions...", 'info');

        $found = 0;
        /** @var UserCardTransaction $uct */
        foreach ($transactions as $i => $uct) {
            $this->line("$i/$count: " . $uct->getId());

            Util::usleep(100, 300);
            $api = PrivacyAPI::getForUserCard($uct->getUserCard());
            [, $tran] = $api->viewTransaction($uct->getTranId());
            $voided = false;
            if (!$tran) {
                $voided = 'missing';
            } else if (!empty($tran['status']) && in_array($tran['status'], [
                PrivacyAPI::TRANSACTION_STATUS_VOIDED,
                PrivacyAPI::TRANSACTION_STATUS_DECLINED,
                PrivacyAPI::TRANSACTION_STATUS_EXPIRED,
            ])) {
                $voided = $tran['status'];
            }
            if ($voided) {
                $this->line('    ==== ' . $voided . ' ' . $uct->getTranId(), 'error');
                $found++;
                if ($found > 10) {
                    break;
                }
            }
        }
    }

    protected function fixUserHistoryLoadLog()
    {
        // User 500138712
        $rs = $this->em->getRepository(UserCardLoad::class)
            ->createQueryBuilder('ucl')
            ->where('ucl.userCard = :uc')
            ->andWhere($this->expr->like('ucl.error', ':error'))
            ->andWhere($this->expr->notLike('ucl.meta', ':meta'))
            ->setParameter('uc', 337126)
            ->setParameter('error', '%Disabled for users on blacklist%')
            ->setParameter('meta', '%sentExceedMaxBalanceMail%')
            ->getQuery()
            ->getResult();
        $this->line('Found ' . count($rs) . ' loads to fix');

        /** @var UserCardLoad $r */
        foreach ($rs as $r) {
            $this->line('Updating ' . $r->getId());
            Util::updateMeta($r, [
                'sentExceedMaxBalanceMail' => true,
            ]);
        }
    }

    protected function testSnapshotReportFixAlert()
    {
        $rs = $this->em->getRepository(MonthlySnapshotReport::class)
            ->createQueryBuilder('msr')
            ->where('msr.meta like :meta')
            ->setParameter('meta', '%"fixDetails":{%')
            ->orderBy('msr.id', 'desc')
            ->setMaxResults(1)
            ->getQuery()
            ->getResult();
        if (!$rs) {
            $this->line('Empty');
            return;
        }
        /** @var MonthlySnapshotReport $r */
        $r = $rs[0];

        SlackService::prepareForPlatform(Platform::transferMex());
        SlackService::hans('Updated the daily snapshot report to include a fix', $r->parseFixDetails());
    }

    protected function fixTransferMexExtraTransactions()
    {
        Util::$platform = Platform::transferMex();
        $expr = $this->expr;
        $apis = RapidService::getAllApis();
        $rs = $this->em->getRepository(RapidTransaction::class)
            ->createQueryBuilder('rt')
            ->where('rt.platform = :platform')
            ->andWhere($expr->notLike('rt.description1', ':desc1'))
            ->andWhere($expr->notLike('rt.description1', ':desc2'))
            ->andWhere($expr->notLike('rt.description1', ':desc3'))
            ->andWhere($expr->notLike('rt.description1', ':desc4'))
            ->andWhere($expr->notLike('rt.description1', ':desc5'))
            ->andWhere($expr->orX(
                $expr->isNull('rt.user'),
                $expr->notIn('rt.user', ':users')
            ))
            ->setParameter('platform', Platform::transferMex())
            ->setParameter('desc1', '%rapyd_transfer%')
            ->setParameter('desc2', '%rapyd_transfer_reverse%')
            ->setParameter('desc3', '%Payout TranID%')
            ->setParameter('desc4', '%Reverse tranId%')
            ->setParameter('desc5', '%Refund dup transfer%')
            ->setParameter('users', TransferMexBundle::hideUsers())
            ->getQuery()
            ->getResult();
        $count = count($rs);
        $this->line('Found ' . $count . ' extra transactions');

        /** @var RapidTransaction $r */
        foreach ($rs as $i => $r) {
            /** @var RapidAPI $api */
            $api = $apis[$r->getAgentNumber()] ?? null;
            if (!$api || !($api->entity instanceof UserGroup)) {
                continue;
            }
            /** @var UserGroup $group */
            $group = $api->entity;
            $employer = $group->getPrimaryAdmin();
            $uc = EmployerService::getMexDummyCard($employer);
            $uct = $r->createUserCardTransactionAsExtra($uc);
            $this->em->persist($uct);
            $this->line($i . '/' . $count . ': Create uct for ' . $r->getTxnRefNumber());
        }
        $this->em->flush();
        $this->done();
        return 0;
    }

    protected function fixFisMonetaryNew()
    {
        Util::$platform = Platform::fis();
        $repo = $this->em->getRepository(UserVelocity::class);

//        $sub = $this->em->getRepository(MonetaryNew::class)
//            ->createQueryBuilder('mn')
//            ->getQuery()
//            ->getDQL();
//
//        $expr = $this->em->getExpressionBuilder();
//        $rs = $repo->createQueryBuilder('uv')
//            ->where('uv.monetary is not null')
//            ->andWhere($expr->neq('uv.monetary', $expr->all($sub)))
//            ->select(['uv.id', 'uv.monetary'])
//            ->getQuery()
//            ->getArrayResult();

        $rs = $this->em->getConnection()
            ->executeQuery('select id, monetary_id from user_velocity where monetary_id is not null and monetary_id not in (select id from fis_monetary_new)')
            ->fetchAll();

        $this->line('Found ' . count($rs) . ' records to fix');

        foreach ($rs as $r) {
            /** @var UserVelocity $uv */
            $uv = $repo->find($r['id']);
            Util::updateMeta($uv, [
                'oldMonetaryId' => $r['monetary_id'],
            ], false);
            $uv->setMonetary(null);
            $this->line('Processed ' . $r['id']);
        }

        $this->em->flush();
    }

    protected static function testMethod($a, $b = null)
    {
        var_dump(func_get_args());
    }

    protected function echoConstants()
    {
        var_dump(__CLASS__);
        var_dump(__METHOD__);
        var_dump(__FUNCTION__);
        var_dump(__FILE__);
        var_dump(__DIR__);
        var_dump(__LINE__);
        var_dump(__NAMESPACE__);
    }

    protected function testCallback($callback)
    {
        $callback('string', 123, true);
    }

    protected function exportTransferMexLocationsToExcel()
    {
        $content = Util::s2j(file_get_contents(__DIR__ . '/../../../../shared/locations-all-mxn-funds-out-live.json'));
        $all = $content['data']['data'];

        $this->line('read file with lines ' . count($all));

        $target = fopen(Util::uploadDir() . 'mex_locations.csv', 'wb');
        fputcsv($target, [
            'ID', 'Name', 'Latitude', 'Longitude',
            'Address', 'City', 'State', 'Zip Code',
            'Phone Number', 'Country', 'Currency Code',
            'Location Type', 'Located In', 'Partner',
            'Brands', 'Categories',
        ]);
        foreach ($all as $i => $item) {
            $ad = $item['address'];
            fputcsv($target, [
                $item['id'],
                $item['alias'],
                $item['latitude'],
                $item['longitude'],
                trim($ad['address_line1'] . ' ' . $ad['address_line2']),
                $ad['city'],
                $ad['state'],
                $ad['zip_code'],
                $ad['phone_number'],
                $ad['country'],
                $item['currency_code'],
                $item['location_type'],
                $item['located_in'],
                $item['partner'],
                implode('/', $item['brands']),
                implode('/', $item['categories']),
            ]);
            if ($i %  300 === 0) {
                $this->line('processed line ' . $i);
            }
        }
        fclose($target);
        $this->line($target);
    }

    protected function testTransDesc()
    {
        $all = [
            [
                'Fund Reversal :********** - TransferMex Agent 1',
                'DEBIT',
                'CES_LOADCARDREVERSAL',
            ],
            [
                'Deposited by TransferMex Agent 1, **********',
                'CREDIT',
                'CES_LOADCARD',
            ],
            [
                'Transfer from OLD Account - TG',
                'CREDIT',
                'MANUAL_CUSTOMER_CREDIT',
            ],
            [
                'POS_PURCHASE - 42279 VERTERANS AVENUE HAMMOND      LAUS - DOLLAR-GENERAL # - Approved POS Purchase',
                'DEBIT',
                NULL,
            ],
            [
                'POS_PURCHASE_WITH_CASHBACK - 2704 WEST THOMAS S     HAMMOND      LAUS - ROUSES MARKET #61 - Approved POS Purchase',
                'DEBIT',
                NULL,
            ],
            [
                'ISSUER_FEE - DECLINE - 1906 HAMMOND SQUARE DR HAMMOND      LAUS - 1906 HAMMOND SQUARE DR - DECLINE - 1906 HAMMOND SQUARE DR HAMMOND      LAUS - 1906 HAMMOND SQUARE DR - Insufficient funds',
                'DEBIT',
                'ISSUER_FEE',
            ],
            [
                'ACQUIRER_FEE - ATM_WITHDRAW - *LAKEWAY               LAKEWAY      TXUS - BANK OF AMERICA - Approved ATM Withdraw',
                'DEBIT',
                'ACQUIRER_FEE',
            ],
            [
                'PRE_AUTH - - STEAM PURCHASE 425-9522985 WAUS - STEAM PURCHASE - (TXN AMOUNT[1499] | ACQ FEE[0] | GRAUTITY[0] | ISSUER FEE[0] | HOLD AMT[1499])',
                'DEBIT',
                'POS_PURCHASE',
            ],
            [
                'PRE_AUTH - - AMZN Mktp US Amzn.com/billWAUS - AMZN Mktp US - (TXN AMOUNT[1373] | ACQ FEE[0] | GRAUTITY[0] | ISSUER FEE[0] | HOLD AMT[1373])',
                'DEBIT',
                'POS_PURCHASE',
            ],
            [
                'AUTH_COMPLETION_DEBIT - STEAM PURCHASE 425-9522985 WAUS - STEAM PURCHASE - Single Settlement - Capture',
                'DEBIT',
                'AUTH_COMPLETION_DEBIT',
            ],
            [
                'AUTH_COMPLETION_DEBIT - AMZN MKTP US*WB11C2QK3 AMZN.COM/BILLWAUS - AMZN MKTP US*WB1 - Single Settlement - Capture',
                'DEBIT',
                'AUTH_COMPLETION_DEBIT',
            ],
            [
                'WENDY\`S 217            HAMMOND      LAUS-WENDY\`S 217',
                'DEBIT',
                '00',
            ],
            [
                'Fund Reversal - 3040029581 - Huggins Farms - TransferMex - rapyd_transfer - 152105',
                'DEBIT',
                'CES_LOADCARDREVERSAL',
            ],
        ];
        $uct = new UserCardTransaction();
        foreach ($all as $fields) {
            $uct->setTranDesc($fields[0])
                ->setTranCode($fields[1])
                ->setActualTranCode($fields[2]);
            $uct->updateTransferMexFields();
            var_dump([
                $uct->getTransferMexType(),
                $uct->getSimplifiedDescription(),
                $uct->getTransferMexResult(),
            ]);
        }
    }

    protected function testPhoneNumbers()
    {
//        $format = Util::formatFullPhone('96919133', 'AN');

        $all = [
            'US' => [
                '+****************',
                '****** 405 2759',
                '(*************',
                '**************',
                '************',
                '9174052759',
                '19174052759',
                '+8618637160569',
            ],
            'CN' => [
                '+86 186 3716 0569',
                '+8618637160569',
                '186 3716 0569',
                '8618637160569',
                '18637160569',
            ],
            '' => [
                '+19174052759',
                '+8618637160569',
                '+86 186',
                '******',
                '+86 18637160569',
                '+86 186371605691869',
                '+86 186-3716-0569',
                '+86 186 3716 0569',

                // errors
                '+570275697',
                '+0570275697',
            ],
            'AN' => [
                '96919133',
            ],
        ];
//        foreach ($all as $country => $items) {
//            foreach ($items as $item) {
//                $format = Util::formatFullPhone($item, $country ?: null);
//                $this->line($country . ' >>> ' . $item . ' -> ' . $format);
//            }
//        }
        foreach ($all as $code => $items) {
            $country = $code ? Country::findByCode($code) : null;
            foreach ($items as $item) {
//                $country = Util::parseCountryFromPhoneNumber($item);
//                $this->line($code . ' > ' . $item . ' -> ' . ($country ? $country->getIsoCode() : ''));

                $this->line($item . ' -> ' . Util::getPhoneDigitsWithOutCountryIso($item, $country));
            }
        }
    }

    protected function testOfac()
    {
        $fields = [
            'invoice' => '',
            'amount' => '',
            'shipping' => '',
            'tax' => '',
            'total' => '',
            //            'idType' => $uiv ? ($uiv->getIDologyDocType() ?: '') : '',
            'idIssuer' => '',
            //            'idNumber' => $uiv ? ($uiv->getNumber() ?: '') : '',
            'paymentMethod' => '',
            'firstName' => 'Randy',
            'lastName' => 'Youngblood',
            'address' => '661 South Fair St.',
            'city' => 'Morrison',
            'state' => 'TN', // 2
            'zip' => '37357', // 5
            'ssnLast4' => '', // 4
            'ssn' => '', // 9
            //            'dobMonth' => $dob ? $dob->format('m') : '', // 2
            //            'dobYear' => $dob ? $dob->format('Y') : '', // 4
            'ipAddress' => '',
            //            'emailAddress' => $user->getEmail(),
            //            'telephone' => $user->getMobilephone(),
            'sku' => '',
            //            'uid' => $user->getId(),
        ];
        /** @var ExternalInvoke $ei */
        list($ei, $data) = IDologyService::request('pa-standalone', $fields);
        var_dump($data);
    }

    protected function testSnapshot() {
      $fromStart = '2021-01-04';
      $type = 'day';
      $tz = Util::tzNewYork();
      $total = MonthlySnapshotReport::MONTH_AGGREGATE;
      $from = Carbon::createFromFormat(Util::DATE_TIME_FORMAT_SEARCH_FULL, $fromStart . ' 00:00:00', $tz);
      $from->startOfDay();
      $expr = Util::expr();
      $bankTransfer = $this->em->getRepository(Transfer::class)
                ->createQueryBuilder('t')
                ->join('t.sender', 'sender')
                ->where($expr->in('t.partner', ':partner'))
                ->andWhere($expr->in('t.status', ':statuses'))
                ->andWhere('t.sendAt >= :sendAt')
                ->andWhere('t.payoutType = :payoutType')
                ->setParameter('partner', [Transfer::PARTNER_RAPYD, Transfer::PARTNER_UNITELLER])
                ->setParameter('statuses', Transfer::STATUSES_SENT)
                ->setParameter('sendAt', Util::toUTC($from))
                ->setParameter('payoutType', 'bank')
                ->select('sender.id user, t.payoutType, t.receiveAt, t.sendAt, t.sendAmount, t.revenue, t.platformRevenue, t.partner')
                ->getQuery()
                ->getArrayResult();
      Log::debug($this->em->getRepository(Transfer::class)
      ->createQueryBuilder('t')
      ->join('t.sender', 'sender')
      ->where($expr->in('t.partner', ':partner'))
      ->andWhere($expr->in('t.status', ':statuses'))
      ->andWhere('t.sendAt >= :sendAt')
      ->andWhere('t.payoutType >= :payoutType')
      ->setParameter('partner', [Transfer::PARTNER_RAPYD, Transfer::PARTNER_UNITELLER])
      ->setParameter('statuses', Transfer::STATUSES_SENT)
      ->setParameter('sendAt', Util::toUTC($from))
      ->setParameter('payoutType', 'bank')
      ->select('sender.id user, t.payoutType, t.receiveAt, t.sendAt, t.sendAmount, t.revenue, t.platformRevenue, t.partner')
      ->getQuery()->getSQL());
      $this->line('Total ' . count($bankTransfer) . ' bank transfer transactions...');
      // get UniTeller cash transfer
      $cashTransferMex = $this->em->getRepository(Transfer::class)
                              ->createQueryBuilder('t')
                              ->join('t.sender', 'sender')
                              ->where($expr->in('t.partner', ':partner'))
                              ->andWhere($expr->in('t.status', ':statuses'))
                              ->andWhere('t.receiveAt >= :receiveAt')
                              ->andWhere('t.payoutType = :payoutType')
                              ->setParameter('payoutType', 'cash')
                              ->setParameter('partner', [Transfer::PARTNER_RAPYD, Transfer::PARTNER_UNITELLER])
                              ->setParameter('statuses', [Transfer::STATUS_COMPLETED])
                              ->setParameter('receiveAt', Util::toUTC($from))
                              ->select('sender.id user, t.payoutType, t.sendAt, t.receiveAt, t.sendAmount, t.revenue, t.platformRevenue, t.partner')
                              ->getQuery()
                              ->getArrayResult();

      $this->line('Total ' . count($cashTransferMex) . ' cash transfer transactions...');

      $rs = array_merge($bankTransfer, $cashTransferMex);

      $count = count($rs);
      $base = [
        'type' => $type,
        'month' => $total,

        'loadMembers' => [],
        'loadCount' => 0,
        'loadAmount' => 0,
        'loadAvgCount' => 0,
        'loadAvgAmount' => 0,

        'spendMembers' => [],
        'spendCount' => 0,
        'spendAmount' => 0,
        'spendAvgCount' => 0,
        'spendAvgAmount' => 0,

        'spendPosAmount' => 0,
        'spendPinPosAmount' => 0,
        'spendOtherPosAmount' => 0,
        'spendAtmAmount' => 0,

        'transferMembers' => [],
        'transferCount' => 0,
        'transferAmount' => 0,
        'transferAvgCount' => 0,
        'transferAvgAmount' => 0,

        'bankTransferMembers' => [],
        'bankTransferCount' => 0,
        'bankTransferAmount' => 0,
        'bankTransferAvgCount' => 0,
        'bankTransferAvgAmount' => 0,

        'cashTransferMembers' => [],
        'cashTransferCount' => 0,
        'cashTransferAmount' => 0,
        'cashTransferAvgCount' => 0,
        'cashTransferAvgAmount' => 0,

        'incomeTransfer' => 0,
        'incomeTransferUniTeller' => 0,
        'incomeTransferRapyd' => 0,

        'costKyc' => 0,
        'costFeeCredits' => 0,

        'extraRevenuePlatform' => 0, // = FX
        'extraRevenuePartner' => 0,

        'fixRevenuePlatform' => 0,
        'fixRevenuePartner' => 0,
        'fixRevenueUniteller' => 0,
        'fixRevenueRapyd' => 0,

        'grossRevenuePlatform' => 0, // = incomeTransfer / 2 + extraRevenuePlatform
        'grossRevenuePartner' => 0, // = incomeTransfer / 2

        'grossRevenuePlatformUniTeller' => 0, // = incomeTransfer / 2 + extraRevenuePlatform
        'grossRevenueUniTeller' => 0, // = incomeTransfer / 2

        'grossRevenuePlatformRapyd' => 0, // = incomeTransfer / 2 + extraRevenuePlatform
        'grossRevenueRapyd' => 0, // = incomeTransfer / 2


        'netRevenuePlatform' => 0, // = grossRevenuePlatform - costFeeCredits / 2
        'netRevenuePlatformPerMember' => 0,
        'netRevenuePlatformGrowth' => 0,

        'netRevenuePartner' => 0, // = grossRevenuePartner - costFeeCredits / 2 - costKyc
        'netRevenuePartnerPerMember' => 0,
        'netRevenuePartnerGrowth' => 0,

        'netRevenueRapyd' => 0, // = grossRevenuePartner - costFeeCredits / 2 - costKyc
        'netRevenueRapydPerMember' => 0,
        'netRevenueRapydGrowth' => 0,

        'netRevenueUniTeller' => 0, // = grossRevenuePartner - costFeeCredits / 2 - costKyc
        'netRevenueUniTellerPerMember' => 0,
        'netRevenueUniTellerGrowth' => 0,
    ];
      $this->line('Total ' . $count . ' transfer transactions...');
      $data = [];
      foreach ($rs as $i => $r) {
          if (($i + 1) % 1000 === 0) {
              $this->line(($i + 1) . '/' . $count . ': Calculating transfers...');
          }

          $month = $r['partner'] == Transfer::PARTNER_RAPYD ? $this->formatDate($r['sendAt'], $tz, $type) : ($r['receiveAt'] ? $this->formatDate($r['receiveAt'], $tz, $type) : $this->formatDate($r['sendAt'], $tz, $type));
          if (empty($data[$month])) {
              $base['month'] = $month;
              $data[$month] = $base;
          }
          if ($month == '2023-05-16' && $r['partner'] == Transfer::PARTNER_UNITELLER) {
            Log::debug('111111111111111111', $r);
          }
          if ($r['user'] == 500148670 && $r['partner'] == Transfer::PARTNER_UNITELLER) {
            Log::debug($i);
            Log::debug('+++++++++=----');
          }
          $user = $r['user'];
          $data[$month]['transferMembers'][$user] = true;
          $data[$month]['transferCount']++;
          $data[$month]['transferAmount'] += $r['sendAmount'];

          $data[$month]['incomeTransfer'] += $r['revenue'];

          $data[$month]['extraRevenuePlatform'] += $r['platformRevenue'] ?: 0;

          $data[$month]['grossRevenuePlatform'] += $r['revenue'] * 0.5 + $r['platformRevenue'] ?: 0;
          $data[$month]['grossRevenuePartner'] += $r['revenue'] * 0.5;




          if ($r['partner'] == Transfer::PARTNER_UNITELLER) {
            if ($month == '2023-05-16') Log::debug('----22222222', $data[$month]);
            $data[$month]['cashTransferMembers'][$user] = true;
            $data[$month]['cashTransferCount']++;
            $data[$month]['cashTransferAmount'] += $r['sendAmount'];
            if ($month == '2023-05-16') Log::debug('-----***********', $r);
            if ($fromStart) {
                $data[$total]['cashTransferMembers'][$user] = true;
            }
            $data[$month]['incomeTransferUniTeller'] += $r['revenue'];
            $data[$month]['grossRevenuePlatformUniTeller'] += $r['revenue'] * 0.5 + $r['platformRevenue'] ?: 0;
            $data[$month]['grossRevenueUniTeller'] += $r['revenue'] * 0.5;
          } else if ($r['partner'] == Transfer::PARTNER_RAPYD) {
            $data[$month]['bankTransferMembers'][$user] = true;
            $data[$month]['bankTransferCount']++;
            $data[$month]['bankTransferAmount'] += $r['sendAmount'];
            if ($fromStart) {
                $data[$total]['bankTransferMembers'][$user] = true;
            }
            $data[$month]['incomeTransferRapyd'] += $r['revenue'];
            $data[$month]['grossRevenuePlatformRapyd'] += $r['revenue'] * 0.5 + $r['platformRevenue'] ?: 0;
            $data[$month]['grossRevenueRapyd'] += $r['revenue'] * 0.5;
          }
      }
      Log::debug("sdadad", $data['2023-05-16']);
    }

    protected function testSettleEmail() {
      Email::$disabled = false;
      $settleList = Util::em()->getRepository(TransferSettlement::class)
                          ->createQueryBuilder('ts')
                          ->where('ts.status = :status')
                          ->andWhere('ts.targetType = :targetType')
                          ->setParameter('status',TransferSettlement::STATUS_SENT )
                          ->setParameter('targetType',TransferSettlement::TARGET_TYPE_UNITELLER )
                         ->distinct()
                          ->getQuery()
                          ->getResult();
      Log::debug('Found ' . count($settleList) . ' settles');
      if (count($settleList)) {
        Log::debug('sent email to UniTeller ' . $settleList[0]->getId());
        TransferService::sentToUnitellerForSettle($settleList[0], true);
      }
    }

    protected function testIntermexSettleEmail() {
      Email::$disabled = false;
      $settleList = Util::em()->getRepository(TransferSettlement::class)
                          ->createQueryBuilder('ts')
                          ->andWhere('ts.targetType = :targetType')
                          ->setParameter('targetType',TransferSettlement::TARGET_TYPE_INTERMEX )
                          ->orderBy('ts.id', 'desc')
                          ->distinct()
                          ->getQuery()
                          ->getResult();
      Log::debug('Found ' . count($settleList) . ' settles');
      if (count($settleList)) {
        foreach ($settleList as $settle) {
            if ($settle->getId() != 2603 && $settle->getId() != 2602) {
                continue;
            }
            Log::debug('sent email to Intermex ' . $settle->getId());
            TransferService::sentToIntermexForSettle($settle, true);
        }
      }
    }

    protected function updateSettleForUniTeller() {
      Email::$disabled = false;
      $settleList = Util::em()->getRepository(TransferSettlement::class)
                          ->createQueryBuilder('ts')
                          ->andWhere('ts.targetType = :targetType')
                          ->setParameter('targetType',TransferSettlement::TARGET_TYPE_UNITELLER )
                          ->distinct()
                          ->getQuery()
                          ->getResult();
      Log::debug('Found ' . count($settleList) . ' settles');
      if (count($settleList)) {
        foreach ($settleList as $item) {
          $item->setTotalFxRateReve($item->getTotalTransfer() * UniTellerRemittanceService::RATE_MARK_UP_PARTNER)
               ->setCashAmount($item->getTotalTransfer() ?? 0)
               ->setTotalAmount($item->getTotalTransfer() ?? 0);
          Util::persist($item);
        }
      }
    }

    protected function checkDuplicatedPaymentTest() {
      $hasDuplicatedAchCredits = [];
      $repoTransfer = $this->em->getRepository(UserCardTransaction::class);
      $res = $repoTransfer->createQueryBuilder('uct')
            ->andWhere(Util::expr()->in('uct.userCard', ':userCard'))
            ->andWhere(Util::expr()->eq('uct.tranCode', ':tranCode'))
            ->setParameter('userCard', [582420])
            ->setParameter('tranCode', 'CREDIT')
            ->orderBy('uct.id', 'desc')
            ->getQuery()
            ->getResult();
      $platform = Platform::transferMex();
      /** @var UserCardTransaction $t */
      foreach ($res as $t) {
        $uc = $t->getUserCard();
        $member = $uc->getUser();
        Log::debug($t->getId());
        Log::debug('23456789765');
        if ($this->checkDuplicatedPayment($uc, $t->getTxnAmount())) {
          // set alert message
          Log::debug('1234567890-=============');
          SlackService::$channel = null;
          SlackService::prepareForPlatform($platform);
          $content = [
            'id' => $t->getId(),
            'platform' => $platform->getName(),
            'employer' => $member->getPrimaryGroupName(),
            'member' => $member->getSignature(),
            'description' => $t->getTranDesc(),
            'type' => $t->getTranCode(),
            'amount' => Money::format($t->getTxnAmountUSD(), 'USD'),
            'status' => $t->getStatus(),
          ];
          SlackService::alert('Duplicated credit transaction on the same date!', $content , SlackService::GROUP_TRANSFER_MEX_INTERNAL_MORE);
          $hasDuplicatedAchCredits[$member->getId()]['platform'] = $platform->getName();
          $hasDuplicatedAchCredits[$member->getId()]['list'][] = $t;
        }
      }
      try {
        foreach ($hasDuplicatedAchCredits as $memberId => $item) {
          $member = User::find($memberId);
          $platform = Platform::find($item['platform']);
          /** @var UserCardTransaction $t */
          foreach ($item['list'] as $t) {
            SlackService::prepareForPlatform($platform);
            SlackService::alert('Duplicated ACH credits summary for `' . $member . '` in latest 24 hrs', [
                'Date' => Carbon::now()->format(Util::DATE_FORMAT_SEARCH),
                'Member' =>  $member->getSignature(),
                'Duplicated Amount' => Money::formatUSD($t->getTxnAmount()),
                'Transfer Id' => $t->getTranId(),
            ], SlackService::GROUP_TRANSFER_MEX_INTERNAL_MORE);
          }
        }
        $this->sentDuplicatedPaymentEmail($hasDuplicatedAchCredits);
      } catch (\Exception $e) {
        Log::exception('Failed to send the duplicated ACH credits batch summary alert: ' . $e->getMessage(), $e);
      }
    }


    protected function checkDuplicatedPayment($userCard, $amount) {
      $startTime = Carbon::now()->subHours(24);
      Log::debug( $startTime);
      Log::debug($userCard->getId());
      Log::debug($amount);
      $res = Util::em()->getRepository(UserCardTransaction::class)
                     ->createQueryBuilder('uct')
                     ->where(Util::expr()->eq('uct.txnAmount', ':amount'))
                     ->andWhere(Util::expr()->gte('uct.txnTime',':txnTime'))
                     ->andWhere(Util::expr()->eq('uct.userCard',':userCard'))
                     ->andWhere(Util::expr()->eq('uct.tranCode',':tranCode'))
                     ->setParameter('amount', $amount)
                     ->setParameter('userCard', $userCard)
                     ->setParameter('txnTime', $startTime)
                     ->setParameter('tranCode', 'CREDIT')
                     ->select('count(distinct uct)')
                     ->distinct()
                     ->getQuery()
                     ->getSingleScalarResult();
                     Log::debug('---------');
    Log::debug($res);
       return $res > 1 ? true : false;
    }

    protected function sentDuplicatedPaymentEmail ($hasDuplicatedAchCredits) {
      $list = [];
      foreach ($hasDuplicatedAchCredits as $memberId => $item) {
        $member = User::find($memberId);
        /** @var UserCardTransaction $t */
        foreach ($item['list'] as $t) {
          $list[] = [
            'Member' => $member->getSignature(),
            'Date' => Util::formatDateTime( $t->getTxnTime(), Util::DATE_FORMAT, 'America/New_York'),
            'Amount'  => Money::format($t->getTxnAmount(), 'USD'),
            'TransferId' => $t->getId(),
          ];
        }
      }
      if (empty($list)) {
        return;
      }
      $subject = 'Duplicate payments';
      $body = Util::render('@TransferMex/Rapid/duplicated_payment_summary.html.twig', [
            'params' => $list,
      ]);
      if (Util::isLive() && !Util::isDev()) {
        $to = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
        ];
      } else {
          $to = [
              '<EMAIL>',
              '<EMAIL>',
              '<EMAIL>',
          ];
      }

      Email::sendWithTemplate($to, Email::TEMPLATE_SIMPLE_LAYOUT, [
          'name' => 'Support Team',
          'subject' => $subject,
          'body' => $body,
      ]);
    }

    protected function checkMaintenance() {
      $type = 'rapyd';
      $maintenances = [
        [
          'Start Date' => new Carbon('03/25/2024 08:10'),
          'End Date' => new Carbon('03/25/2024 08:40'),
        ],
        [
          'Start Date' => new Carbon('03/25/2024 08:55'),
          'End Date' => new Carbon('03/25/2024 09:00'),
        ],
      ];
      Data::setArray('transfer_mex_maintenance_list_' . $type, $maintenances);
      $res = TransferMexBundle::checkMaintenance($type);
      $this->line($res);
    }

    protected function checkCurp() {
      $curps = MemberService::getAllCurpHash();
      Log::debug('list', $curps);
      $importMemberRecord = ImportMembersRecord::findByfileName('agempleo-02-04.xlsx');
      $path = $importMemberRecord->getPath();
      $excel = Excel::openExcel($path);
      $sheet = $excel->getActiveSheet();
      $rows = $sheet->toArray();
      foreach ($rows as $row) {
        $curp = trim($row[4]);
        if ($curp ) {
          Log::debug($curp);
          $hash = TransferMexBundle::getCurpHash($curp);
          Log::debug($hash);
          Log::debug('checkout: ' . in_array($hash, $curps));
        }
      }
    }

    protected function checkAccountNumer() {
      $account = '*****************';
      $result = UserCard::findByDepositNumber( $account);
      if ($result) {
        Log::debug('success');
      }
    }

    protected function sendSettlementEmail() {
        $settle = Util::em()->getRepository(TransferSettlement::class)->findOneBy(['id' => 16]);
        ManualSettleService::doSettle($settle);
    }

    protected function generateIntermexForm1025() {
      $transfer = Transfer::find(111);
      $path = IntermexRemittanceService::generateForm1025($transfer);
      $this->line($path);
      $this->line(file_exists($path) ? 'Generate' : 'Error');
    }

    protected function rapydEmailTest () {
      $startTime = null;
      $endTime = null;
      $oldContent = 'Time frame:

15/09/2024 01:00 AM - 15/09/2024 05:00 AM (GMT+8)';
      $content = 'Time frame:

05/09/2024 01:00 AM - 03:00 AM (GMT-6)';

      $pattern = '/\d{2}\/\d{2}\/\d{2,4},*\s*\d{2}:\d{2}\s*[AP]M\s*(until|-)\s*(\d{2}\/\d{2}\/\d{2,4})*\s*\d{2}:\d{2}\s*[AP]M\s*(\(GMT\s*[+-]\d{1,2}\)|\(UTC\))/';
      $matches = [];
      preg_match($pattern, $oldContent, $matches);
      var_dump($matches);
      // Log::debug('--maintenance time--', $matches);
      // preg_match($pattern, $oldContent, $matches);
      // var_dump($matches);
      Log::debug('--maintenance time--', $matches);
      $currentMonth = date('n');
      try {
        $timeList = explode($matches[1], $matches[0]);
        $startList = [];
        $endList = [];
        $this->line('-------');
        var_dump($timeList );
        // get the start time list form the string like 20/06/2024 01:00 AM
        if (strpos($timeList[0], ',') !== false) {
          $startList = explode(',', trim($timeList[0]));
        } else {
          $startList = explode(' ', trim($timeList[0]));
        }
        // get the end time list form the string like 20/06/2024 01:00 AM
        if (strpos($timeList[1], ',') !== false) {
          $endList = explode(',', trim($timeList[1]));
        } else {
          $endList = explode(' ', trim($timeList[1]));
        }
        // get the year month day and check their order
        $startList[0] = explode('/', $startList[0]);
        $endList[0] = explode('/', $endList[0]);
        $this->line('-------');
        var_dump($startList);
        $this->line('-------');
        var_dump($endList);
        var_dump(count($endList[0]));
        var_dump($currentMonth);
        if (count($endList[0]) == 3 ) {
          $this->line('111111==========22222');
          if ($startList[0][1] < $currentMonth || $startList[0][1] > 12 || $startList[0][1] > ($currentMonth + 1)) {
            $startTime = Carbon::create(($startList[0][2] > 2000 ? $startList[0][2] : $startList[0][2] + 2000), $startList[0][0], $startList[0][1], substr(trim($startList[1]), 0, 2), substr(trim($startList[1]), 3, 2) );
            $endTime = Carbon::create(( $endList[0][2] > 2000 ? $endList[0][2] : $endList[0][2] + 2000), $endList[0][0], $endList[0][1], substr(trim($endList[1]), 0, 2), substr(trim($endList[1]), 3, 2) );
          } else {
            $startTime = Carbon::create(($startList[0][2] > 2000 ? $startList[0][2] : $startList[0][2] + 2000), $startList[0][1], $startList[0][0], substr(trim($startList[1]), 0, 2), substr(trim($startList[1]), 3, 2) );
            $endTime = Carbon::create(( $endList[0][2] > 2000 ? $endList[0][2] : $endList[0][2] + 2000), $endList[0][1], $endList[0][0], substr(trim($endList[1]), 0, 2), substr(trim($endList[1]), 3, 2) );
          }
        } else {
          $this->line('111111==========11111111');
          if ($startList[0][1] < $currentMonth || $startList[0][1] > 12 || $startList[0][1] > ($currentMonth + 1)) {
            $startTime = Carbon::create(($startList[0][2] > 2000 ? $startList[0][2] : $startList[0][2] + 2000), $startList[0][0], $startList[0][1], substr(trim($startList[1]), 0, 2), substr(trim($startList[1]), 3, 2) );
            $endTime = Carbon::create(( $startList[0][2] > 2000 ? $startList[0][2] : $startList[0][2] + 2000), $startList[0][0], $startList[0][1], substr(trim($endList[0][0]), 0, 2), substr(trim($endList[0][0]), 3, 2) );
          } else {
            $startTime = Carbon::create(($startList[0][2] > 2000 ? $startList[0][2] : $startList[0][2] + 2000), $startList[0][1], $startList[0][0], substr(trim($startList[1]), 0, 2), substr(trim($startList[1]), 3, 2) );
            $endTime = Carbon::create(( $startList[0][2] > 2000 ? $startList[0][2] : $startList[0][2] + 2000), $startList[0][1], $startList[0][0], substr(trim($endList[0][0]), 0, 2), substr(trim($endList[0][0]), 3, 2) );
          }
        }

        // check the timezone and set the time to UTC
        var_dump($matches[3]);
        $this->line('==========');
        if (strpos($matches[3], 'UTC') == false) {
          var_dump('1111111111');
          $hourMatches = [];
          $patternForHour = '/\(GMT\s*([+-])?(\d+)\)/';
          preg_match($patternForHour, $matches[3], $hourMatches);
          if ($hourMatches[1] == '-') {
            $startTime =  $startTime->addHours($hourMatches[2]);
            $endTime =  $endTime->addHours($hourMatches[2]);
          } else if ($hourMatches[1] == '+') {
            $startTime =  $startTime->subHours($hourMatches[2]);
            $endTime =  $endTime->subHours($hourMatches[2]);
          }
        }
      } catch (\Exception $exception) {
        $this->line('==========11111111');
        Log::debug('Parse date error: ' . $exception->getMessage());
        $needManual = true;
      }
      var_dump($startTime);
      var_dump($endTime);
    }

    protected function createSignature() {
      $ts = 1743647673;
      $method = 'post';
      $url = '/resources/accessTokens/sdk';
      $paramsList = [
        [
          'levelName' =>  "id-and-liveness",
          // 'ttlInSecs' => 600,
          'userId' => "500110202",
        ],
        [
          // 'ttlInSecs' => 600,
          'userId' => "500110202",
          'levelName' =>  "id-and-liveness",
        ],
      ];
      $secretKey = '';
      // $separator = str_contains($url, '?') ? '&' : '?';
      foreach($paramsList as $params) {
        $url = '/resources/accessTokens/sdk';
        $bodyStr = json_encode($params);
        // if ($bodyStr) {
        //     $url .= $bodyStr;
        // }
        $body = \GuzzleHttp\Psr7\Utils::streamFor(json_encode($params));

        $request = new Request('POST', $url);

        Log::debug($ts . strtoupper($request->getMethod()) . $request->getUri() . $body);
        //  Log::debug(strtoupper($method));
        //   Log::debug($bodyStr);
        //   Log::debug($url);
        //   Log::debug($ts);
        //   Log::debug($secretKey);
          Log::debug($ts . strtoupper($method) . $url . $bodyStr);
          Log::debug('==========');
          Log::debug(hash_hmac('sha256', $ts . strtoupper($request->getMethod()) . $request->getUri() . $body, $secretKey));
          Log::debug(hash_hmac('sha256', $ts . strtoupper($method) . $url . $bodyStr, $secretKey));
      }

    }

    protected function sentSettlementEmailToUniteller() {
        Email::$disabled = false;
        $settle = Util::em()->getRepository(TransferSettlement::class)->findOneBy([
            'targetType' => 'uniteller',
            'status' => 'Sent',
        ], ['id' => 'desc']);
        TransferService::sentToUnitellerForSettle($settle, true);
    }
}
