<?php


namespace DevBundle\Controller\UsUnlocked;


use Core<PERSON><PERSON>le\Entity\CardProgram;
use Core<PERSON>undle\Entity\CardProgramCardType;
use CoreBundle\Entity\ExternalInvoke;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserCardDecline;
use CoreBundle\Entity\UserCardTransaction;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Util;
use DevBundle\Controller\BaseController;
use SalexUserBundle\Entity\User;
use Symfony\Component\Routing\Annotation\Route;
use UsUnlockedBundle\Services\Privacy\PrivacyAPI;

class PrivacyController extends BaseController
{
    /**
     * @Route("/dev/us/privacy/view-card/{uc}")
     * @param UserCard $uc
     *
     * @return SuccessResponse
     */
    public function viewCard(UserCard $uc)
    {
        $service = PrivacyAPI::getForUserCard($uc);
        list(, $data) = $service->viewCard($uc);
        return new SuccessResponse($data);
    }

    /**
     * @Route("/dev/us/privacy/view-card-of-user/{user}")
     * @param User $user
     *
     * @return SuccessResponse
     */
    public function viewCardOfUser(User $user)
    {
        $ucs = $user->getCardsInUsUnlocked(null);
        $all = [];
        /** @var UserCard $uc */
        foreach ($ucs as $uc) {
            if (!$uc->getToken()) {
                continue;
            }
            $service = PrivacyAPI::getForUserCard($uc);
            list(, $data) = $service->viewCard($uc);
            $all[$uc->getToken()] = $data;

            usleep(100000);
        }
        return new SuccessResponse($all);
    }

    /**
     * @Route("/dev/us/privacy/view-card-by-token/{token}")
     *
     * @param $token
     *
     * @return SuccessResponse
     */
    public function viewCardByToken($token)
    {
        $service = PrivacyAPI::get();
        $uc = new UserCard();
        $uc->setToken($token);
        list(, $data) = $service->viewCard($uc);
        return new SuccessResponse($data);
    }

    /**
     * @Route("/dev/us/privacy/view-card2-by-token/{token}")
     *
     * @param $token
     *
     * @return SuccessResponse
     */
    public function viewCard2ByToken($token)
    {
        $service = PrivacyAPI::get();
        $uc = new UserCard();
        $uc->setToken($token);
        list(, $data) = $service->viewCardNew($uc);
        return new SuccessResponse($data);
    }

    /**
     * @Route("/dev/us/privacy/update-card-by-token/{token}")
     * @param $token
     *
     * @return SuccessResponse
     */
    public function updateCardByToken($token)
    {
        $uc = UserCard::findByPrivacyToken($token);
        return $this->updateCard($uc);
    }

    /**
     * @Route("/dev/us/privacy/activate-card/{uc}")
     * @param $uc
     *
     * @return SuccessResponse
     */
    public function activateCard(UserCard $uc)
    {
        $uc->setStatus(UserCard::STATUS_ACTIVE)
            ->persist();
        return new SuccessResponse();
    }

    /**
     * @Route("/dev/us/privacy/deactivate-card/{uc}")
     * @param $uc
     *
     * @return SuccessResponse
     */
    public function deactivateCard(UserCard $uc)
    {
        $uc->setStatus(UserCard::STATUS_INACTIVE, true, false)
            ->persist();
        return new SuccessResponse();
    }

    /**
     * @Route("/dev/us/privacy/update-card/{uc}")
     * @param $uc
     *
     * @return SuccessResponse
     */
    public function updateCard(UserCard $uc)
    {
        $service = PrivacyAPI::getForUserCard($uc);
        $service->updateCard($uc);
        return new SuccessResponse();
    }

    /**
     * @Route("/dev/us/privacy/close-card/{uc}")
     * @param $uc
     *
     * @return SuccessResponse
     */
    public function closeCard(UserCard $uc)
    {
        $service = PrivacyAPI::getForUserCard($uc);
        if ($uc->getAccountNumber()) {
            $service->closeCard($uc);
        }
        $uc->setStatus(UserCard::STATUS_CLOSED, false, false)
            ->persist();
        return new SuccessResponse();
    }

    /**
     * @Route("/dev/us/privacy/view-spend-limit-history/{token}")
     *
     * @param $token
     *
     * @return SuccessResponse
     */
    public function viewSpendLimitHistory($token)
    {
        $expr = Util::expr();
        $rs = $this->em->getRepository(ExternalInvoke::class)
            ->createQueryBuilder('ei')
            ->where($expr->in('ei.type', ':types'))
            ->andWhere($expr->like('ei.response', ':response'))
            ->setParameter('types', [
                'privacy_POST_card',
                'privacy_PUT_card',
            ])
            ->setParameter('response', '%' . $token . '%')
            ->orderBy('ei.id', 'asc')
            ->getQuery()
            ->getResult()
        ;
        $data = [];
        /** @var ExternalInvoke $r */
        foreach ($rs as $r) {
            $data[] = $r->toArray();
        }
        return new SuccessResponse($data);
    }

    /**
     * @Route("/dev/us/privacy/list-transactions/{uc}")
     * @param UserCard $uc
     *
     * @return SuccessResponse
     */
    public function listTransactions(UserCard $uc)
    {
        $service = PrivacyAPI::getForUserCard($uc);
        list(, $data) = $service->listTransactions($uc, 'all', 1, 1000);
        return new SuccessResponse($data);
    }

    /**
     * @Route("/dev/us/privacy/list-transactions-by-token/{token}")
     *
     * @param $token
     *
     * @return SuccessResponse
     */
    public function listTransactionsByToken($token)
    {
        $service = PrivacyAPI::get();
        $uc = new UserCard();
        $uc->setToken($token);
        list(, $data) = $service->listTransactions($uc);
        return new SuccessResponse($data);
    }

    /**
     * @Route("/dev/us/privacy/view-transaction/{token}")
     *
     * @param $token
     *
     * @return SuccessResponse
     */
    public function viewTransaction($token)
    {
        $service = PrivacyAPI::get();
        list(, $data) = $service->listTransactions(
            null, PrivacyAPI::TRANSACTION_APPROVAL_STATUS_ALL,
            1, 50,
            null, null,
            $token
        );
        if (!empty($data['data'][0]['card_token'])) {
            $uc = new UserCard();
            $uc->setToken($data['data'][0]['card_token']);
            list(, $cardData) = $service->viewCard($uc);
            $data['card'] = $cardData;
        }
        $uct = UserCardTransaction::findByTranId($token);
        if ($uct) {
            $data['meta'] = Util::meta($uct);
        } else {
            $ucd = UserCardDecline::findByTranId($token);
            if ($ucd) {
                $data['meta'] = Util::meta($ucd);
            }
        }
        return new SuccessResponse($data);
    }

    /**
     * @Route("/dev/us/privacy/fix-card-merchant")
     *
     * @return SuccessResponse
     */
    public function fixCardMerchant()
    {
        $cpct = CardProgramCardType::getForCardProgram(CardProgram::usunlocked());
        $ucs = $this->em->getRepository(UserCard::class)
            ->createQueryBuilder('uc')
            ->where('uc.card = :card')
            ->andWhere('uc.merchant is null')
            ->andWhere('uc.accountNumber is not null')
            ->andWhere('uc.type = :type')
            ->setParameter('card', $cpct)
            ->setParameter('type', PrivacyAPI::CARD_TYPE_MERCHANT_LOCKED)
            ->getQuery()
            ->getResult()
        ;
        $result = [];
        /** @var UserCard $uc */
        foreach ($ucs as $uc) {
            $usages = $uc->getUsages();
            if ($usages->isEmpty()) {
                continue;
            }
            /** @var UserCardTransaction $usage */
            $usage = $usages->first();
            if ($usage && $usage->getMerchant()) {
                $uc->setMerchant($usage->getMerchant());
                $this->em->persist($uc);

                $result[$uc->getToken()] = $usage->getMerchant()->getMerchantNameAndLocation();
            }
        }
        $this->em->flush();
        return new SuccessResponse($result);
    }

    /**
     * @Route("/dev/us/privacy/hosted-ui/{hash}")
     *
     * @param $hash
     *
     * @return SuccessResponse
     */
    public function hostedUI($hash)
    {
        $uc = UserCard::find($hash);
        if (!$uc) {
            $uc = UserCard::findByPrivacyToken($hash);
        }

        $url = PrivacyAPI::getForUserCard($uc)->hostedUI($uc);
        return new SuccessResponse($url);
    }
}
