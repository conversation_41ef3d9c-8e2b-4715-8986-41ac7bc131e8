<?php
/**
 * Created by PhpStorm.
 * User: hans
 * Date: 2017/11/9
 * Time: 10:59
 */

namespace DevBundle\Controller;


use Carbon\Carbon;
use CoreBundle\Entity\UserCardDecline;
use CoreBundle\Entity\UserCardTransaction;
use CoreBundle\Utils\Service;
use CoreBundle\Utils\Util;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;

class CardUsageController extends BaseController
{
    /**
     * @Route("/fix/card-usage-timezone/{start}/{limit}")
     * @param int $start
     * @param int $limit
     * @return JsonResponse
     * @throws \Doctrine\ORM\ORMInvalidArgumentException
     * @throws \Doctrine\ORM\OptimisticLockException
     * @throws \Doctrine\ORM\NoResultException
     * @throws \Doctrine\ORM\NonUniqueResultException
     */
    public function fixTimeZone($start = -1, $limit = 5000)
    {
        $em = Util::em();
        $repo = $em->getRepository(\CoreBundle\Entity\UserCardTransaction::class);
        $q = $repo->createQueryBuilder('uct');
        if ($start === -1) {
            $count = $q->select('count(uct)')
                ->getQuery()
                ->getSingleScalarResult();
            return $this->autoLinksAction($count, $limit, 'card-usage-timezone');
        }
        $rs = $q->setFirstResult($start)
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
        $result = [];
        $tzNewYork = Util::tzNewYork();
        $tzPacific = new \DateTimeZone('America/Los_Angeles');
        /** @var UserCardTransaction $t */
        foreach ($rs as $t) {
            $time = Carbon::instance($t->getTxnTime());
            $seconds = $tzPacific->getOffset($time) - $tzNewYork->getOffset($time);
            $time->addSeconds($seconds);
            $t->setTxnTime($time);

            $time = Carbon::instance($t->getPostTime());
            $seconds = $tzPacific->getOffset($time) - $tzNewYork->getOffset($time);
            $time->addSeconds($seconds);
            $t->setPostTime($time);

            $em->persist($t);

            $result[] = $t->getId();
        }
        $em->flush();

        return new JsonResponse($result);
    }

    /**
     * @Route("/fix/card-decline-timezone/{start}/{limit}")
     * @param int $start
     * @param int $limit
     * @return JsonResponse
     * @throws \Doctrine\ORM\ORMInvalidArgumentException
     * @throws \Doctrine\ORM\OptimisticLockException
     * @throws \Doctrine\ORM\NoResultException
     * @throws \Doctrine\ORM\NonUniqueResultException
     */
    public function fixDeclinedTimeZone($start = -1, $limit = 5000)
    {
        $em = Util::em();
        $repo = $em->getRepository(\CoreBundle\Entity\UserCardDecline::class);
        $q = $repo->createQueryBuilder('uct');
        if ($start === -1) {
            $count = $q->select('count(uct)')
                ->getQuery()
                ->getSingleScalarResult();
            return $this->autoLinksAction($count, $limit, 'card-decline-timezone');
        }
        $rs = $q->setFirstResult($start)
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
        $result = [];
        $tzNewYork = Util::tzNewYork();
        $tzPacific = new \DateTimeZone('America/Los_Angeles');
        /** @var UserCardDecline $t */
        foreach ($rs as $t) {
            $time = Carbon::instance($t->getTxnTime());
            $seconds = $tzPacific->getOffset($time) - $tzNewYork->getOffset($time);
            $time->addSeconds($seconds);
            $t->setTxnTime($time);

            $em->persist($t);

            $result[] = $t->getId();
        }
        $em->flush();

        return new JsonResponse($result);
    }

    /**
     * @Route("/fix/card-usage-order/{start}/{limit}")
     * @param int $start
     * @param int $limit
     * @return JsonResponse
     * @throws \Doctrine\ORM\ORMInvalidArgumentException
     * @throws \Doctrine\ORM\OptimisticLockException
     * @throws \Doctrine\ORM\NoResultException
     * @throws \Doctrine\ORM\NonUniqueResultException
     */
    public function fixUsageOrder($start = -1, $limit = 3000)
    {
        $em = Util::em();
        $repo = $em->getRepository(\CoreBundle\Entity\UserCardTransaction::class);
        $q = $repo->createQueryBuilder('uct')
            ->where('uct.actualTranCode = :tranCode')
            ->setParameter('tranCode', UserCardTransaction::CODE_POS_PURCHASE);
        if ($start === -1) {
            $count = $q->select('count(uct)')
                ->getQuery()
                ->getSingleScalarResult();
            return $this->autoLinksAction($count, $limit, 'card-usage-order');
        }
        $rs = $q->orderBy('uct.tranId', 'asc')
            ->setFirstResult($start)
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
        $result = [];
        /** @var UserCardTransaction $t */
        foreach ($rs as $t) {
            $t->updateOrder();
            $em->persist($t);

            $result[] = $t->getId();
        }
        $em->flush();

        return new JsonResponse($result);
    }

    /**
     * @Route("/fix/run-fv-parser/{start}/{limit}")
     * @param int $start
     * @param int $limit
     * @return JsonResponse
     * @throws \Doctrine\ORM\ORMInvalidArgumentException
     * @throws \Doctrine\ORM\OptimisticLockException
     * @throws \Doctrine\ORM\NoResultException
     * @throws \Doctrine\ORM\NonUniqueResultException
     */
    public function runFvParser($start = -1, $limit = 1)
    {
        if ($start === -1) {
            return $this->autoLinksAction(300, $limit, 'run-fv-parser');
        }
        $date = Carbon::now()->subDays($start);
        Service::sendAsync('/t/cron/fv-transactions-date/' . $date->format('Y_m_d'));

        return new JsonResponse();
    }
}