<?php

namespace DevBundle\Controller\Skux;

use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Data;
use DevBundle\Controller\BaseController;
use SkuxBundle\Services\SkuxService;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class SkuxApiController extends BaseController
{
    protected function getAPI()
    {
        $accessToken = Data::get('skux_dev_test_access_token');
        $refreshToken = Data::get('skux_dev_test_refresh_token');
        return new SkuxService($accessToken, $refreshToken, null);
    }

    /**
     * @Route("/dev/skux/api/send-verification-code/{number}")
     *
     * @return SuccessResponse
     */
    public function sendVerificationCode(Request $request, $number)
    {
        $api = new SkuxService();
        $result = $api->sendVerificationCode($number);
        return new SuccessResponse($result);
    }

    /**
     * @Route("/dev/skux/api/register/{number}/{code}")
     *
     * @return SuccessResponse
     */
    public function register(Request $request, $number, $code = '123456')
    {
        $api = new SkuxService();
        $result = $api->register($number, $code, 'test_device');
        return new SuccessResponse($result);
    }

    /**
     * @Route("/dev/skux/api/login/{number}/{code}")
     *
     * @return SuccessResponse
     */
    public function login(Request $request, $number, $code = '123456')
    {
        $api = new SkuxService();
        $result = $api->login($number, $code, 'test_device');

        Data::set('skux_dev_test_access_token', $api->accessToken);
        Data::set('skux_dev_test_refresh_token', $api->refreshToken);

        return new SuccessResponse([
            'result' => $result,
            'accessToken' => $api->accessToken,
            'refreshToken' => $api->refreshToken,
        ]);
    }

    /**
     * @Route("/dev/skux/api/refresh-token")
     *
     * @return SuccessResponse
     */
    public function refreshToken(Request $request)
    {
        $api = $this->getAPI();
        $result = $api->refreshToken();

        Data::set('skux_dev_test_access_token', $api->accessToken);
        Data::set('skux_dev_test_refresh_token', $api->refreshToken);

        return new SuccessResponse([
            'result' => $result,
            'accessToken' => $api->accessToken,
            'refreshToken' => $api->refreshToken,
        ]);
    }

    /**
     * @Route("/dev/skux/api/current-user")
     *
     * @return SuccessResponse
     */
    public function currentUser(Request $request)
    {
        $api = $this->getAPI();
        $result = $api->currentUser();

        return new SuccessResponse($result);
    }

    /**
     * @Route("/dev/skux/api/get-login-endpoint")
     *
     * @return SuccessResponse
     */
    public function getLoginEndpoint(Request $request)
    {
        $api = $this->getAPI();
        $result = $api->getLoginEndpoint();

        return new SuccessResponse($result);
    }

    /**
     * @Route("/dev/skux/api/offers")
     *
     * @return SuccessResponse
     */
    public function offers(Request $request)
    {
        $api = $this->getAPI();
        $result = $api->offers();

        return new SuccessResponse($result);
    }

    /**
     * @Route("/dev/skux/api/offer/{uuid}")
     *
     * @return SuccessResponse
     */
    public function offer(Request $request, $uuid)
    {
        $api = $this->getAPI();
        $result = $api->offer($uuid);

        return new SuccessResponse($result);
    }

    /**
     * @Route("/dev/skux/api/offer-need-terms/{uuid}")
     *
     * @return SuccessResponse
     */
    public function needTermsCond(Request $request, $uuid)
    {
        $api = $this->getAPI();
        $result = $api->needsTermsAndConditions($uuid);

        return new SuccessResponse($result);
    }

    /**
     * @Route("/dev/skux/api/offer-claim/{uuid}")
     *
     * @return SuccessResponse
     */
    public function claimOffer(Request $request, $uuid)
    {
        $api = $this->getAPI();
        $result = $api->claimOffer($uuid, 'test_device');

        return new SuccessResponse($result);
    }

    /**
     * @Route("/dev/skux/api/claim-task/{uuid}")
     *
     * @return SuccessResponse
     */
    public function getClaimOfferTask(Request $request, $uuid)
    {
        $api = $this->getAPI();
        $result = $api->getClaimPurseTask($uuid);

        return new SuccessResponse($result);
    }

    /**
     * @Route("/dev/skux/api/last-offer-viewed/{id}")
     *
     * @return SuccessResponse
     */
    public function lastOfferViewed(Request $request, $id = null)
    {
        $api = $this->getAPI();
        $result = $api->lastOfferViewed($id);

        return new SuccessResponse($result);
    }

    /**
     * @Route("/dev/skux/api/cards")
     *
     * @return SuccessResponse
     */
    public function cards(Request $request)
    {
        $api = $this->getAPI();
        $result = $api->getUserCards();

        return new SuccessResponse($result);
    }

    /**
     * @Route("/dev/skux/api/retailers")
     *
     * @return SuccessResponse
     */
    public function retailers(Request $request)
    {
        $api = $this->getAPI();
        $result = $api->retailers();

        return new SuccessResponse($result);
    }

    /**
     * @Route("/dev/skux/api/retailer/{id}")
     *
     * @return SuccessResponse
     */
    public function retailer(Request $request, $id)
    {
        $api = $this->getAPI();
        $result = $api->retailer($id);

        return new SuccessResponse($result);
    }
}
