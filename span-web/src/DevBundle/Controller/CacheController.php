<?php
/**
 * Created by PhpStorm.
 * User: hans
 * Date: 2018/12/5
 * Time: 14:26
 */

namespace DevBundle\Controller;


use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\CardProgramCardType;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Util;
use InvalidArgumentException;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class CacheController extends BaseController
{
    /**
     * @Route("/dev/cache/clear/entities")
     * @return Response
     * @throws InvalidArgumentException
     */
    public function clearEntityCaches() {
        $prefixes = [
            // TODO: Add all possible keys here
            CardProgramCardType::class . '::getForCardProgram',
            CardProgram::class . '::usunlocked',
        ];
        foreach ($prefixes as $prefix) {
            Data::delAllWith($prefix);
        }
        return new SuccessResponse();
    }

    /**
     * @Route("/dev/cache/{prefix}/keys")
     * @param Request $request
     * @param         $prefix
     *
     * @return Response
     */
    public function cacheKeysPrefix(Request $request, $prefix) {
        if ($prefix === '___') {
            $prefix = $request->query->get("prefix");
        }
        return new JsonResponse(Data::keys($prefix));
    }

    /**
     * @Route("/dev/cache/{prefix}/contain-keys")
     * @param Request $request
     * @param         $prefix
     *
     * @return Response
     */
    public function cacheKeysContains(Request $request, $prefix) {
        if ($prefix === '___') {
            $prefix = $request->query->get("prefix");
        }
        return new JsonResponse(Data::keys($prefix, '*'));
    }

    /**
     * @Route("/dev/cache/{key}")
     * @param Request $request
     * @param         $key
     *
     * @return Response
     */
    public function value(Request $request, $key) {
        if ($key === '___') {
            $key = $request->query->get("key");
        }
        return new JsonResponse([
            'key' => $key,
            'value' => (string)(Data::get($key)),
        ]);
    }

    /**
     * @Route("/dev/cache/{key}/ttl")
     * @param Request $request
     * @param         $key
     *
     * @return Response
     */
    public function ttl(Request $request, $key) {
        if ($key === '___') {
            $key = $request->query->get("key");
        }
        return new JsonResponse([
            'key' => $key,
            'value' => Data::ttl($key),
        ]);
    }

    /**
     * @Route("/dev/cache/{key}/clear")
     * @param Request $request
     * @param         $key
     *
     * @return Response
     */
    public function clearCache(Request $request, $key) {
        if ($key === '___') {
            $key = $request->query->get("key");
        }
        $v = Data::get($key);
        Data::del($key);

        return new Response((string)$v);
    }

    /**
     * @Route("/dev/cache/{key}/hash/get/{hashKey}")
     * @param Request $request
     * @param         $key
     * @param         $hashKey
     *
     * @return Response
     */
    public function hashGet(Request $request, $key, $hashKey) {
        if ($key === '___') {
            $key = $request->query->get("key");
        }
        $value = Data::hGet($key, $hashKey);
        return new SuccessResponse([
            'key' => $key,
            'hashKey' => $hashKey,
            'value' => $value,
        ]);
    }

    /**
     * @Route("/dev/cache/{key}/hash/set/{hashKey}")
     * @param Request $request
     * @param         $key
     * @param         $hashKey
     *
     * @return Response
     */
    public function hashSet(Request $request, $key, $hashKey) {
        if ($key === '___') {
            $key = $request->query->get("key");
        }
        $value = $request->get('value');
        Data::hSet($key, $hashKey, $value);
        return new SuccessResponse([
            'key' => $key,
            'hashKey' => $hashKey,
            'value' => Data::hGet($key, $hashKey),
        ]);
    }

    /**
     * @Route("/dev/cache/{key}/hash/all")
     * @param Request $request
     * @param         $key
     *
     * @return Response
     */
    public function hashAll(Request $request, $key) {
        if ($key === '___') {
            $key = $request->query->get("key");
        }
        $all = Data::hGetAll($key);
        return new SuccessResponse([
            'key' => $key,
            'all' => $all,
        ]);
    }

    /**
     * @Route("/dev/cache/{key}/push")
     * @param Request $request
     * @param         $key
     *
     * @return Response
     */
    public function pushCache(Request $request, $key) {
        if ($key === '___') {
            $key = $request->query->get("key");
        }
        $value = $request->get('value');
        $size = Data::push($key, $value);

        return new SuccessResponse(compact('key', 'value', 'size'));
    }

    /**
     * @Route("/dev/cache/{key}/pop")
     * @param Request $request
     * @param         $key
     *
     * @return Response
     */
    public function popCache(Request $request, $key) {
        if ($key === '___') {
            $key = $request->query->get("key");
        }
        $value = Data::pop($key);

        return new SuccessResponse(compact('key', 'value'));
    }

    /**
     * @Route("/dev/cache/{key}/update")
     * @param Request $request
     * @param         $key
     *
     * @return SuccessResponse
     */
    public function updateCache(Request $request, $key) {
        $v = Data::get($key);
        Data::set($key, $request->get('value'));

        return new SuccessResponse([
            'old' => $v,
            'new' => Data::get($key),
        ]);
    }

    /**
     * @Route("/dev/cache/{prefix}/clear-keys")
     * @param Request $request
     * @param         $prefix
     *
     * @return Response
     */
    public function clearCachePrefix(Request $request, $prefix) {
        if ($prefix === '___') {
            $prefix = $request->query->get("prefix");
        }
        Data::delAllWith($prefix);

        return new Response('');
    }

    /**
     * @Route("/dev/cache/login/attempts")
     * @return Response
     * @throws InvalidArgumentException
     */
    public function loginAttempts() {
        $prefix = Util::request()->get('prefix', 'login_threshold_block_');
        $redis = Util::newLocalRedis();
        $all = $redis->keys($prefix . '*');
        $replaces = [
            $prefix . 'REMOTE_ADDR_',
            $prefix . 'HTTP_X_FORWARDED_FOR_',
            $prefix . 'HTTP_CF_CONNECTING_IP_',
        ];
        $result = [];
        foreach ($all as $key) {
            $ip = str_replace($replaces, '', $key);
            $result[$ip] = (int)$redis->get($key);
        }
        arsort($result);
        $max = (int)(Util::request()->get('max', 100));
        return new SuccessResponse(array_slice($result, 0, $max));
    }

    /**
     * @Route("/dev/cache/login/log")
     * @return Response
     * @throws InvalidArgumentException
     */
    public function loginLog(Request $request) {
        $redis = Util::newLocalRedis();
        $ip = $request->get('ip');
        $prefix = 'secure_log_';

        if (!$ip) {
            $keys = $redis->keys($prefix . '*');
            return new JsonResponse($keys);
        }

        $value = $redis->get($prefix . $ip);
        if ($value) {
            $paths = json_decode($value, TRUE, 512, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE | JSON_THROW_ON_ERROR);
        } else {
            $paths = [];
        }
        return new JsonResponse($paths);
    }
}
