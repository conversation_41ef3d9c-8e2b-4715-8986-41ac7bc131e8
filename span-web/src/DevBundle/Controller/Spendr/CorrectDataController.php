<?php

namespace DevB<PERSON>le\Controller\Spendr;

use Carbon\Carbon;
use ClfBundle\Entity\Merchant;
use Clf<PERSON><PERSON>le\Entity\Transaction;
use Clf<PERSON><PERSON>le\Entity\TransactionStatus;
use Clf<PERSON><PERSON>le\Entity\TransactionType;
use Core<PERSON>undle\Entity\AchTransactions;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\EntitySignature;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserCardBalance;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Entity\UserCardTransaction;
use CoreBundle\Entity\UserFeeHistory;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Queue;
use CoreBundle\Utils\Util;
use DevBundle\Controller\BaseController;
use Doctrine\ORM\AbstractQuery;
use LeafLinkBundle\Controller\AchServiceController;
use SalexUserBundle\Entity\User;
use SpendrBundle\Entity\SpendrMerchant;
use SpendrBundle\Entity\SpendrTransaction;
use SpendrBundle\Services\ACH\SpendrACHService;
use SpendrBundle\Services\ACH\SpendrWebhookService;
use SpendrBundle\Services\Bank\ImportTransactionsService;
use SpendrBundle\Services\FeeService;
use SpendrBundle\Services\LoadService;
use SpendrBundle\Services\MerchantService;
use SpendrBundle\Services\SlackService;
use SpendrBundle\Services\UserService;
use SpendrBundle\SpendrBundle;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;


class CorrectDataController extends BaseController
{
	/**
	 * @Route("/dev/spendr/correct/merchant-withdraw-fee")
	 * @throws \PortalBundle\Exception\PortalException
	 * @throws \Doctrine\ORM\NonUniqueResultException
	 */
	public function correctMerchantWithdrawFee()
	{
		$feeName = FeeService::TO_TERN_MERCHANT_WITHDRAWAL_FEE;
		$ucbs = $this->em->getRepository(UserCardBalance::class)
			->createQueryBuilder('ucb')
			->where('ucb.type = :type')
			->setParameter('type', $feeName)
			->groupBy('ucb.comment')
			->orderBy('ucb.id', 'asc')
			->getQuery()
			->getResult();

		if (!$ucbs) {
			return new FailedResponse('No record found!');
		}

		$fee = FeeService::calculate($feeName);
		if (!$fee) {
			return new FailedResponse('No fee!');
		}

		$spendrAdmin = UserService::getSpendrBalanceAdminAccount('spendr');

		/** @var UserCardBalance $ucb */
		foreach ($ucbs as $ucb)
		{
			$comment = 'Correct fee. ' . $ucb->getComment();
			$ufh = $this->em->getRepository(UserFeeHistory::class)
				->createQueryBuilder('ufh')
				->where('ufh.comment = :comment')
				->setParameter('comment', $comment)
				->andWhere('ufh.feeName = :feeName')
				->setParameter('feeName', $feeName)
				->getQuery()
				->getOneOrNullResult();
			if (!$ufh) {
				UserFeeHistory::create($spendrAdmin, $fee, $feeName, null, $comment, Platform::spendr());
			}
		}
		Util::flush();

		return new SuccessResponse();
	}

	/**
	 * @Route("/dev/spendr/correct/bank-ach-return-fee")
	 * @throws \PortalBundle\Exception\PortalException
	 * @throws \Doctrine\ORM\NonUniqueResultException
	 */
	public function correctToBankAchReturnFee()
	{
		$feeName = FeeService::TO_BANK_SPENDR_ACH_RETURN_FEE;
		$ucbs = $this->em->getRepository(UserCardBalance::class)
			->createQueryBuilder('ucb')
			->where('ucb.type = :type')
			->setParameter('type', $feeName)
			->groupBy('ucb.comment')
			->orderBy('ucb.id', 'asc')
			->getQuery()
			->getResult();

		if (!$ucbs) {
			return new FailedResponse('No record found!');
		}

		$continueId = [];
		/** @var UserCardBalance $ucb */
		foreach ($ucbs as $ucb) {
			$metaEntity = Util::meta($ucb, 'entity');
			$loadId = $metaEntity['id'];
			if (!$loadId) {
				$continueId[] = $loadId;
				continue;
			}

			$load = UserCardLoad::find($loadId);
			if (!$load) {
				$continueId[] = $loadId;
				continue;
			}

			$spendrToBank = $this->em->getRepository(UserFeeHistory::class)
				->createQueryBuilder('ufh')
				->where('ufh.feeName = :name')
				->setParameter('name', $feeName)
				->andWhere('ufh.entityId = :entityId')
				->setParameter('entityId', $loadId)
				->getQuery()
				->getOneOrNullResult();
			if ($spendrToBank) {
				$continueId[] = $loadId;
				continue;
			}

			FeeService::chargeAchReturnFeeToBank($load, $ucb);
		}

		Util::flush();

		return new SuccessResponse($continueId);
	}

	/**
	 * @Route("/dev/spendr/correct/spendr-transaction/{id}")
	 *
	 * @param Request $request
	 * @param $id
	 * @return FailedResponse|SuccessResponse
	 */
	public function correctSpendrTransaction(Request $request, $id)
	{
		if (!$id) {
			return new FailedResponse('Invalid parameters.');
		}

		/** @var SpendrTransaction $st */
		$st = Util::em()->getRepository(SpendrTransaction::class)
			->find($id);

		if (!$st) {
			return new FailedResponse('Txn not found.');
		}

		if (
			Util::meta($st, 'corrected')
			|| $st->getStatus() !== SpendrTransaction::STATUS_COMPLETED
		) {
			return new FailedResponse('Txn can not be corrected.');
		}

		if ($st->getDescription() === 'RET ACH - UTL') {
			$this->correctSpendrTransactionRetAch($st);
		} else if ($st->getDescription() === 'SERVICE CHARGE') {
			$this->correctSpendrTransactionServiceCharge($st);
		}

		return new SuccessResponse();
	}

	private function correctSpendrTransactionRetAch(SpendrTransaction $st)
	{
		$spendrDummy = UserService::getSpendrBalanceDummyCard('spendr');
		$amount = $st->getCredit();
		$comment = sprintf(
			'Correct data. Bank ledger. Deducted %s from partner balance through imported spendr transaction %s at %s.',
			Money::format($amount, 'USD'),
			$st->getSignature(),
			Carbon::now(),
		);
		$spendrDummy->updatePrivacyBalanceBy(-$amount, UserCardBalance::TYPE_IMPORT, $comment, false, $st);

		Util::updateMeta($st, [
			'corrected' => true
		]);
	}

	private function correctSpendrTransactionServiceCharge(SpendrTransaction $st)
	{
		$spendrDummy = UserService::getSpendrBalanceDummyCard('spendr');
		$feeName = FeeService::TO_BANK_SPENDR_MONTHLY_ACCOUNT_FEE;
		$fee = FeeService::calculate($feeName);
		/** @var UserFeeHistory $ufh */
		$ufh = FeeService::hasFeeHistory($spendrDummy->getUser(), $feeName, $st);
		if (!$ufh || ($ufh->getAmount() <= $fee)) {
			return null;
		}

		$amount = $st->getDebit();
		$difference = $amount - $fee;
		if ($difference <= 0) {
			return null;
		}

		$comment = sprintf(
			'Correct data. Bank ledger. Added %s to partner balance through imported spendr transaction %s at %s.',
			Money::format($difference, 'USD'),
			$st->getSignature(),
			Carbon::now(),
		);
		$spendrDummy->updatePrivacyBalanceBy($difference, UserCardBalance::TYPE_IMPORT, $comment, false, $st);

		Util::updateMeta($st, [
			'corrected' => true
		]);
	}

	/**
	 * @Route("/dev/spendr/correct/pending-service-charge")
	 *
	 * @return FailedResponse|SuccessResponse
	 */
	public function handleSpendrTransactionPendingServiceCharge()
	{
		ImportTransactionsService::handleBankServiceChargeRows();
		return new SuccessResponse();
	}

	/**
	 * Problems caused by the deposit try again function(Load ID: 423439, User ID: *********)
	 * correct txn id: 6a8r6y691s80iq, incorrect txn id: 2syo59hmo197Ms
	 *
	 * @Route("/dev/spendr/correct/load-txn")
	 *
	 * @return FailedResponse|SuccessResponse
	 */
	public function correctLoadFee()
	{
		$load = UserCardLoad::find(423439);
		if (!$load) {
			return new FailedResponse('Load txn not found.');
		}
		$data = [
			'id' => '6a8r6y691s80iq',
			'status' => UserCardTransaction::STATUS_LL_RETURNED,
			'returnCode' => SpendrACHService::RETURN_CODE_INSUFFICIENT_FUNDS,
			'context' => [
				'message' => AchServiceController::RETURN_CODE_R01
			]
		];

		SpendrACHService::handlePrefundLoadReturnedStatus($load, $data);

		return new SuccessResponse();
	}

	/**
	 * @Route("/dev/spendr/correct/ucb-meta")
	 *
	 * @return FailedResponse|SuccessResponse
	 */
	public function correctUserCardBalanceMeta()
	{
		$ucbs = $this->em->getRepository(UserCardBalance::class)
			->createQueryBuilder('ucb')
			->where('ucb.comment like :comment')
			->setParameter(
				'comment',
				'%Failed to pull funds, return funds to partner balance from the consumer%'
			)
			->getQuery()
			->getResult();

		if (!$ucbs) {
			return new FailedResponse('No record found.');
		}

		$count = 0;
		/** @var UserCardBalance $ucb */
		foreach ($ucbs as $ucb) {
			if ($ucb->getMeta() !== '[]') {
				continue;
			}
			$comment = $ucb->getComment();
			$loadId = substr($comment, 11, 6);
			$load = UserCardLoad::find($loadId);
			if (!$load) {
				continue;
			}

			Util::updateMeta($ucb, [
				'entity' => [
					'class' => 'CoreBundle\Entity\UserCardLoad',
					'id' => (int)$loadId,
					'token' => null
				]
			], false);
			$count++;
		}
		Util::flush();

		return new SuccessResponse($count);
	}

	/**
	 * https://app.asana.com/0/1200410643730426/1202319448094234/f
	 *
	 * @Route("/dev/spendr/correct/returned-load/{unloadId}")
	 *
	 * @param $unloadId
	 * @return FailedResponse|SuccessResponse
	 * @throws \PortalBundle\Exception\PortalException
	 * @throws \Throwable
	 */
	public function correctReturnedLoadData($unloadId)
	{
		if (!$unloadId) {
			return new FailedResponse('Invalid parameter.');
		}

		$unloadId = (int)$unloadId;
		$unload = UserCardLoad::find($unloadId);
		$loadId = Util::meta($unload, 'Spendr load failed');

		$ucbHistory = $this->em->getRepository(UserCardBalance::class)
			->createQueryBuilder('ucb')
			->where('ucb.comment like :comment')
			->setParameter(
				'comment',
				'Correct data. Unload ID: ' . $unloadId . '. Returned load ID: ' . $loadId . '. Because of repeated operations%'
			)
			->getQuery()
			->getResult();

		if ($ucbHistory) {
			return new FailedResponse('Already corrected.');
		}

		if (
			$unload->getLoadStatus() !== UserCardLoad::LOAD_STATUS_LOADED
			|| $unload->getType() !== UserCardLoad::TYPE_UNLOAD
			|| !$loadId
			|| !Util::meta($unload, 'returnInstantLoadFundsToPartnerBalance')
		) {
			return new FailedResponse('This transaction can not be corrected.');
		}

		$load = UserCardLoad::find($loadId);

		if (
			$load->getLoadStatus() !== UserCardLoad::LOAD_STATUS_ERROR
			|| !LoadService::isInstantLoad($load)
			|| !Util::meta($load, 'returnedByBank')
			|| !Util::meta($load, 'returnedCode')
		) {
			return new FailedResponse('This transaction can not be corrected.');
		}

		$expr = Util::expr();

		$spendrDummy = UserService::getSpendrBalanceDummyCard('spendr');

		$ucbs = $this->em->getRepository(UserCardBalance::class)
			->createQueryBuilder('ucb')
			->where('ucb.type = :type')
			->setParameter('type', UserCardBalance::TYPE_UNLOAD_CARD)
			->andWhere($expr->like('ucb.comment', ':comment'))
			->setParameter('comment', '%Unload ID: ' . $unloadId . '. Failed to pull funds, return funds to partner balance from the consumer%')
			->andWhere('ucb.userCard = :userCard')
			->setParameter('userCard', $spendrDummy)
			->groupBy('ucb.comment')
			->setMaxResults(1)
			->getQuery()
			->getResult();

		if (!$ucbs) {
			return new FailedResponse('user_card_balance record not found.', [$ucbs]);
		}

		/** @var UserCardBalance $ucb */
		$ucb = $ucbs[0];

		$entity = Util::meta($ucb, 'entity');
		if (!$entity || !$entity['id'] || ($entity['id'] !== $unloadId)) {
			return new FailedResponse("user_card_balance's entity not found.", [
				$entity,
				$entity['id'],
				$unloadId,
			]);
		}

		$realAmount = $unload->getLoadAmount();
		$processedAmount = $ucb->getCurrentBalance() - $ucb->getPreviousBalance();

		if (($processedAmount > 0) && ($processedAmount <= $realAmount)) {
			$spendrComment = sprintf(
				'Correct data. Unload ID: %s. Returned load ID: %s. Because of repeated operations. ' .
				'Deducted %s from the Partner %s through Returned load transaction at %s.',
				$unloadId,
				$loadId,
				Money::format($processedAmount, 'USD'),
				$spendrDummy->getUser()->getSignature(),
				Carbon::now(),
			);

			$spendrDummy->updatePrivacyBalanceBy(
				-$processedAmount,
				UserCardBalance::TYPE_UNLOAD_CARD,
				$spendrComment,
				false,
				$unload
			);

			Util::updateMeta($load, [
				'corrected' => true
			]);

			SlackService::eyes($spendrComment);
		} else {
			return new FailedResponse('Processed amount issue.', [
				'processed amount' => $processedAmount,
				'real amount' => $realAmount,
			]);
		}

		Util::flush();
		return new SuccessResponse();
	}

	/**
	 * Previously, because of the pending fee, the consumer balance was directly changed,
	 * but the user_card_balance was not written, and this method was used to supplement the record.
	 *
	 * @Route("/dev/spendr/correct/pending-fee/supplement-record/{cardId}")
	 *
	 * @param $cardId
	 * @return FailedResponse|SuccessResponse
	 */
	public function addCorrectPendingFeeRecordToUCB($cardId)
	{
//		$ucIds = [358060, 371731, 371750];

		/** @var UserCard $uc */
		$uc = $this->em->getRepository(UserCard::class)
			->find($cardId);

		if (!$uc) {
			return new FailedResponse('User card not found.');
		}

		$pendingFees = $uc->getPendingFees();
		if (!$pendingFees) {
			return new FailedResponse('No pending fee.');
		}

		$ucbHistory = $this->em->getRepository(UserCardBalance::class)
			->createQueryBuilder('ucb')
			->where('ucb.comment like :comment')
			->setParameter('comment', '%through the pending fee record of card ' . $cardId . '%')
			->getQuery()
			->getResult();

		if ($ucbHistory) {
			return new FailedResponse('Already corrected.');
		}

		$pfs = Util::s2j($pendingFees);
		$count = 0;
		foreach ($pfs as $i => $pf) {
			if ($pf['type'] !== UserCard::PENDING_FEE_UNLOAD) {
				continue;
			}
			$unloadId = $pf['load'];
			$amount = $pf['amount'];

			$unload = UserCardLoad::find($unloadId);
			if (!$unload) {
				continue;
			}

			$comment = sprintf(
				'Correct data. Add a record of processing pending fee before. ' .
				'Unload ID: %s. Deducted %s from the consumer %s through the pending fee record of card %s at %s.',
				$unloadId,
				Money::format($amount, 'USD'),
				$uc->getUser()->getSignature(),
				$cardId,
				Carbon::now(),
			);

			$ucb = new UserCardBalance();
			$ucb->setUserCard($uc)
				->setSource(UserCardBalance::SOURCE_CARD)
				->setType(UserCardBalance::TYPE_UNLOAD_CARD)
				->setComment(Util::cleanUtf8String($comment))
				->setCurrentBalance(0)
				->setPreviousBalance(0);

			Util::updateMeta($ucb, $unload, false);

			Util::em()->persist($ucb);

			$count++;
		}

		Util::flush();

		return new SuccessResponse($count);
	}

	/**
	 * consumer ID: 500157564. Load ID: 416604(-$15), 416605(-$20). Unload ID: 417109, 417110
	 *
	 * @Route("/dev/spendr/correct/ach-return-fee/to-spendr/{loadId}/{fee}")
	 *
	 * @param $loadId
	 * @param $fee
	 * @return FailedResponse|SuccessResponse
	 * @throws \PortalBundle\Exception\PortalException
	 */
	public function correctAchReturnFeeToSpendr($loadId, $fee)
	{
		// 1. consumer: Deduct the wrongly fee -$15 & Deduct the correct fee -$15
		// 2. partner: Add the wrongly fee +$15 & Add the correct fee +$15

		$ucbHistory = $this->em->getRepository(UserCardBalance::class)
			->createQueryBuilder('ucb')
			->where('ucb.comment like :comment')
			->setParameter('comment', '%Correct data. Load ID: ' . $loadId . '. Deduct the wrongly added fee before%')
			->getQuery()
			->getResult();

		if ($ucbHistory) {
			return new FailedResponse('Already corrected.');
		}

		$fee = (int)$fee;
		if (!$fee) {
			return new FailedResponse('Invalid fee.');
		}

		$load = UserCardLoad::find($loadId);

		if (!$load) {
			return new FailedResponse('Load not found.');
		}

		$consumerDummy = UserCard::find($load->getUserCard()->getId());
		$spendrDummy = UserService::getSpendrBalanceDummyCard('spendr');

		if (!$consumerDummy || !$spendrDummy) {
			return new FailedResponse('Dummy card not found.');
		}

		$feeName = FeeService::TO_SPENDR_CONSUMER_ACH_RETURN_FEE;

		// correct consumer record
		$consumerComment = sprintf(
			'Correct data. Load ID: %s. Deduct the wrongly added fee before(%s) and deduct the correct fee(%s) on consumer %s at %s. Amount: %s',
			$loadId,
			Money::format($fee, 'USD'),
			Money::format($fee, 'USD'),
			$consumerDummy->getUser()->getSignature(),
			Carbon::now(),
			Money::format($fee * 2, 'USD')
		);

		$ucb = new UserCardBalance();
		$ucb->setUserCard($consumerDummy)
			->setSource(UserCardBalance::SOURCE_CARD)
			->setType($feeName)
			->setComment(Util::cleanUtf8String($consumerComment))
			->setCurrentBalance(0)
			->setPreviousBalance(0);

		Util::updateMeta($ucb, $load, false);

		Util::em()->persist($ucb);

		// correct partner record
		$spendrComment = sprintf(
			'Correct data. Load ID: %s. Add the wrongly deducted fee before(%s) and add the correct fee(%s) to the partner %s at %s. Amount: %s',
			$loadId,
			Money::format($fee, 'USD'),
			Money::format($fee, 'USD'),
			$spendrDummy->getUser()->getSignature(),
			Carbon::now(),
			Money::format($fee * 2, 'USD')
		);

		$spendrUcb = new UserCardBalance();
		$spendrUcb->setUserCard($spendrDummy)
			->setSource(UserCardBalance::SOURCE_CARD)
			->setType($feeName)
			->setComment(Util::cleanUtf8String($spendrComment))
			->setCurrentBalance(0)
			->setPreviousBalance(0);

		Util::updateMeta($spendrUcb, $load, false);

		Util::em()->persist($spendrUcb);

		Util::flush();

		return new SuccessResponse();
	}

	/**
	 * @Route("/dev/spendr/correct/ucb-txn-entity")
	 *
	 * @return FailedResponse|SuccessResponse
	 */
	public function addTransactionEntityToUCB() {
		$ucbs = $this->em->getRepository(UserCardBalance::class)
			->createQueryBuilder('ucb')
			->where('ucb.comment like :comment')
			->setParameter('comment', '%In transaction%at 202%')
			->andWhere('ucb.meta = :meta')
			->setParameter('meta', '[]')
			->setMaxResults(50)
			->getQuery()
			->getResult();

		if (!$ucbs) {
			return new FailedResponse('No ucb records.');
		}

		$data = [];
		$wrong = [];
		/** @var UserCardBalance $ucb */
		foreach ($ucbs as $ucb) {
			$comment = $ucb->getComment();
			$preLen = strlen('In transaction ');
			$token = substr($comment, $preLen, 36);

			/** @var Transaction $txn */
			$txn = $this->em->getRepository(Transaction::class)
				->findOneBy([
					'token' => $token
				]);

			if (!$txn) {
				$wrong[] = [
					$ucb->getId(),
					$ucb->getComment(),
					$token,
				];
				continue;
			}

			$meta = [
				'entity' => (array)(new EntitySignature($txn)),
			];

			Util::updateMeta($ucb, $meta);

			$data[] = [
				$ucb->getId(),
				$ucb->getComment(),
				$token,
				$txn->getId(),
				$txn->getToken(),
				$txn->getMerchant()->getId(),
			];
		}

		return new SuccessResponse([$wrong, $data]);
	}

	/**
	 * @Route("/dev/spendr/correct/ucb-load-entity")
	 *
	 * @return FailedResponse|SuccessResponse
	 */
	public function addLoadEntityToUCB() {
		$ucbs = $this->em->getRepository(UserCardBalance::class)
			->createQueryBuilder('ucb')
			->where('ucb.comment like :comment')
//			->setParameter('comment', 'Load ID: %')
			->setParameter('comment', 'Correct data. Unload ID:%')
			->andWhere('ucb.meta = :meta')
			->setParameter('meta', '[]')
			->setMaxResults(10)
			->getQuery()
			->getResult();

		if (!$ucbs) {
			return new FailedResponse('No ucb records.');
		}

		$data = [];
		$wrong = [];
		/** @var UserCardBalance $ucb */
		foreach ($ucbs as $ucb) {
			$comment = $ucb->getComment();
//			$preLen = strlen('Load ID: ');
			$preLen = strlen('Correct data. Unload ID: ');

			if (Util::isLive()) {
				$loadId = substr($comment, $preLen, 6);
			} else {
				$loadId = substr($comment, $preLen, 3);
			}

			/** @var UserCardLoad $load */
			$load = UserCardLoad::find($loadId);

			if (!$load) {
				$wrong[] = [
					$ucb->getId(),
					$ucb->getComment(),
					$loadId,
				];
				continue;
			}

			$meta = [
				'entity' => (array)(new EntitySignature($load)),
			];
			Util::updateMeta($ucb, $meta);

			$data[] = [
				$ucb->getId(),
				$ucb->getComment(),
				$loadId,
				$load->getId(),
				$load->getInitialAmountText(),
			];
		}

		return new SuccessResponse([$wrong, $data]);
	}

	/**
	 * @Route("/dev/spendr/correct/ucb-spendr-txn-entity")
	 *
	 * @return FailedResponse|SuccessResponse
	 */
	public function addSpendrTxnEntityToUCB() {
		$ucbs = $this->em->getRepository(UserCardBalance::class)
			->createQueryBuilder('ucb')
			->where('ucb.comment like :comment')
			->setParameter('comment', '%through imported spendr transaction%')
			->andWhere('ucb.meta = :meta')
			->setParameter('meta', '[]')
			->setMaxResults(50)
			->getQuery()
			->getResult();

		if (!$ucbs) {
			return new FailedResponse('No ucb records.');
		}

		$data = [];
		$wrong = [];
		/** @var UserCardBalance $ucb */
		foreach ($ucbs as $ucb) {
			$comment = $ucb->getComment();
			$str = 'through imported spendr transaction ';
			$strlen = strlen($str);
			$prePos = strpos($comment, $str);
			$endPos = strpos($comment, ' at 202');
			$signature = substr($comment, $prePos + $strlen, $endPos - $prePos - $strlen);

			/** @var SpendrTransaction $st */
			$st = $this->em->getRepository(SpendrTransaction::class)
				->findOneBy([
					'signature' => $signature
				]);

			if (!$st) {
				$wrong[] = [
					$ucb->getId(),
					$ucb->getComment(),
					$signature,
				];
				continue;
			}

			$meta = [
				'entity' => (array)(new EntitySignature($st)),
			];

			Util::updateMeta($ucb, $meta);

			$data[] = [
				$ucb->getId(),
				$ucb->getComment(),
				$signature,
				$st->getSignature(),
				$st->getId(),
				$st->getDebit(),
				$st->getCredit(),
			];
		}

		return new SuccessResponse([$wrong, $data]);
	}

	/**
	 * @Route("/dev/spendr/correct/ucb-fee-entity")
	 *
	 * @return FailedResponse|SuccessResponse
	 * @throws \PortalBundle\Exception\PortalException
	 * @throws \Doctrine\ORM\NonUniqueResultException
	 */
	public function addLoadEntityToUCBByUfh()
	{
		$feeName = FeeService::TO_TERN_CONSUMER_LOAD_FEE;
		$roles = [
			Role::ROLE_ADMIN,
			Role::ROLE_SPENDR_PROGRAM_OWNER
		];
		$spendrAdmin = UserService::getSpendrBalanceAdminAccount('spendr');
		$ternAdmin = UserService::getSpendrBalanceAdminAccount('tern');
		if (!$spendrAdmin || !$ternAdmin) {
			return new FailedResponse('Can not find partner and tern accounts');
		}
		$ucbs = $this->em->getRepository(UserCardBalance::class)
			->createQueryBuilder('ucb')
			->join('ucb.userCard', 'uc')
			->join('uc.card', 'cpct')
			->leftJoin('cpct.cardProgram', 'cp')
			->join('uc.user', 'u')
			->leftJoin('u.teams', 't')
			->where('ucb.type = :type')
			->setParameter('type', $feeName)
			->andWhere('ucb.meta = :meta')
			->setParameter('meta', '[]')
			->andWhere(Util::expr()->in('t.name', ":teams"))
			->setParameter('teams', $roles)
			->andWhere(Util::expr()->in('u.id', ':adminIds'))
			->setParameter('adminIds', [
				$spendrAdmin->getId(),
				$ternAdmin->getId()
			])
			->andWhere(Util::expr()->eq('cp.name', ':cpn'))
			->setParameter('cpn', CardProgram::NAME_SPENDR)
			->setMaxResults(50)
			->getQuery()
			->getResult();

		if (!$ucbs) {
			return new FailedResponse('No ucb records.');
		}

		$data = [];
		$wrong = [];
		/** @var UserCardBalance $ucb */
		foreach ($ucbs as $ucb) {
			$comment = $ucb->getComment();
			$ucbAmount = (int)(abs($ucb->getPreviousBalance() - $ucb->getCurrentBalance()));
			$spendrBalanceAdmin = UserService::getSpendrBalanceAdminAccount('spendr');

			$feeRecords = $this->em->getRepository(UserFeeHistory::class)
				->createQueryBuilder('ufh')
				->where('ufh.comment = :comment')
				->setParameter('comment', $comment)
				->andWhere('ufh.user = :user')
				->setParameter('user', $spendrBalanceAdmin) // ***
				->andWhere('ufh.feeName = :feeName')
				->setParameter('feeName', $feeName)
				->andWhere('ufh.entity = :entity')
				->setParameter('entity', 'CoreBundle\Entity\UserCardLoad') // ***
				->andWhere('ufh.entityId is not null')
				->andWhere('ufh.meta like :meta')
				->setParameter('meta', '%"spendrMeta":true%')
				->getQuery()
				->getResult();

			$wrongFlag = false;
			if (!$feeRecords || count($feeRecords) !== 1) {
				$wrongFlag = true;
			}

			$feeRecord = null;
			if (!$wrongFlag) {
				/** @var UserFeeHistory $feeRecord */
				$feeRecord = $feeRecords[0];
				if ((int)($feeRecord->getAmount()) !== (int)$ucbAmount) {
					$wrong = true;
				}
			}

			if ($wrongFlag) {
				$wrong[] = [
					$ucb->getId(),
					$ucb->getComment(),
					Money::format($ucbAmount, 'USD', false),
					$feeRecord && $feeRecord->getAmount()? Money::format($feeRecord->getAmount(), 'USD', false) : null,
				];
				continue;
			}

			$load = UserCardLoad::find($feeRecord->getEntityId());
			$meta = [
				'entity' => (array)(new EntitySignature($load)),
			];

			Util::updateMeta($ucb, $meta);

			$data[] = [
				$ucb->getId(),
				$feeRecord->getComment(),
				$ucb->getComment(),
				Money::format($ucbAmount, 'USD', false),
				Money::format($feeRecord->getAmount(), 'USD', false),
				$feeRecord->getEntityId(),
				$feeRecord->getEntity(),
				$feeRecord->getId()
			];
		}

		return new SuccessResponse([
			'wrong' => [
				'count' => count($wrong),
				'data' => $wrong,
			],
			'success' => [
				'count' => count($data),
				'data' => $data
			]
		]);
	}

	/**
	 * @Route("/dev/spendr/correct/ucb-txn-fee-entity")
	 *
	 * @return FailedResponse|SuccessResponse
	 * @throws \PortalBundle\Exception\PortalException
	 */
	public function addTxnEntityToUCBByUfh()
	{
		$feeName = FeeService::TO_TERN_TRANSACTION_FEE;
		$roles = [
			Role::ROLE_ADMIN,
			Role::ROLE_SPENDR_PROGRAM_OWNER
		];
		$spendrAdmin = UserService::getSpendrBalanceAdminAccount('spendr');
		$ternAdmin = UserService::getSpendrBalanceAdminAccount('tern');
		if (!$spendrAdmin || !$ternAdmin) {
			return new FailedResponse('Can not find partner or tern accounts');
		}
		$ucbs = $this->em->getRepository(UserCardBalance::class)
			->createQueryBuilder('ucb')
			->join('ucb.userCard', 'uc')
			->join('uc.card', 'cpct')
			->leftJoin('cpct.cardProgram', 'cp')
			->join('uc.user', 'u')
			->leftJoin('u.teams', 't')
			->where('ucb.type = :type')
			->setParameter('type', $feeName)
			->andWhere('ucb.meta = :meta')
			->setParameter('meta', '[]')
			->andWhere(Util::expr()->in('t.name', ":teams"))
			->setParameter('teams', $roles)
			->andWhere(Util::expr()->in('u.id', ':adminIds'))
			->setParameter('adminIds', [
				$spendrAdmin->getId(),
				$ternAdmin->getId()
			])
			->andWhere(Util::expr()->eq('cp.name', ':cpn'))
			->setParameter('cpn', CardProgram::NAME_SPENDR)
			->setMaxResults(100)
			->getQuery()
			->getResult();

		if (!$ucbs) {
			return new FailedResponse('No ucb records.');
		}

		$data = [];
		$wrong = [];
		/** @var UserCardBalance $ucb */
		foreach ($ucbs as $ucb) {
			$comment = $ucb->getComment();
			$ucbAmount = (int)(abs($ucb->getPreviousBalance() - $ucb->getCurrentBalance()));
			$spendrBalanceAdmin = UserService::getSpendrBalanceAdminAccount('spendr');

			$feeRecords = $this->em->getRepository(UserFeeHistory::class)
				->createQueryBuilder('ufh')
//				->join('ufh.user', 'u')
//				->leftJoin('u.teams', 't')
//				->where(Util::expr()->in('t.name', ":teams"))
//				->setParameter('teams', SpendrBundle::getMerchantBalanceAdminRoles())
				->andWhere('ufh.comment = :comment')
				->setParameter('comment', $comment)
				->andWhere('ufh.user = :user')
				->setParameter('user', $spendrBalanceAdmin) // ***
				->andWhere('ufh.feeName = :feeName')
				->setParameter('feeName', $feeName)
				->andWhere('ufh.entity = :entity')
				->setParameter('entity', 'ClfBundle\Entity\Transaction') // ***
				->andWhere('ufh.entityId is not null')
				->andWhere('ufh.meta like :meta')
				->setParameter('meta', '%"spendrMeta":true%')
				->getQuery()
				->getResult();

			$wrongFlag = false;
			if (!$feeRecords || count($feeRecords) !== 1) {
				$wrongFlag = true;
			}

			$feeRecord = null;
			if (!$wrongFlag) {
				/** @var UserFeeHistory $feeRecord */
				$feeRecord = $feeRecords[0];
				if ((int)($feeRecord->getAmount()) !== (int)$ucbAmount) {
					$wrong = true;
				}
			}

			if ($wrongFlag) {
				$wrong[] = [
					$ucb->getId(),
					$ucb->getComment(),
					Money::format($ucbAmount, 'USD', false),
					$feeRecord && $feeRecord->getAmount()? Money::format($feeRecord->getAmount(), 'USD', false) : null,
				];
				continue;
			}

			$txn = Transaction::find($feeRecord->getEntityId());
			$meta = [
				'entity' => (array)(new EntitySignature($txn)),
			];

			Util::updateMeta($ucb, $meta);

			$data[] = [
				$ucb->getId(),
				$feeRecord->getComment(),
				$ucb->getComment(),
				Money::format($ucbAmount, 'USD', false),
				Money::format($feeRecord->getAmount(), 'USD', false),
				$feeRecord->getEntityId(),
				$feeRecord->getEntity(),
				$feeRecord->getId()
			];
		}

		return new SuccessResponse([
			'wrong' => [
				'count' => count($wrong),
				'data' => $wrong,
			],
			'success' => [
				'count' => count($data),
				'data' => $data
			]
		]);
	}

	/**
	 * @Route("/dev/spendr/correct/pending-load-txn")
	 * @return FailedResponse|SuccessResponse
	 */
	public function correctPendingLoadTxn()
	{
		if (!Util::isLive()) {
			return new FailedResponse();
		}
		$id = 414364;
		if (!$id) {
			return new FailedResponse('Invalid load id.');
		}

		$load = UserCardLoad::find($id);
		if (!$load) {
			return new FailedResponse('Can not find the load txn.');
		}

		if ($load->getLoadStatus() !== UserCardLoad::LOAD_STATUS_INITIATED) {
			return new FailedResponse();
		}

		SpendrACHService::handlePrefundLoadSettledStatus($load);
		LoadService::LoadCard($load);
		Util::flush();

		return new SuccessResponse();
	}

	/**
	 * @Route("/dev/spendr/correct/txn-time/{page}/{pageSize}")
	 * @param $page
	 * @param $pageSize
	 * @return FailedResponse|SuccessResponse
	 */
	public function correctTxnTime($page = 1, $pageSize = 10)
	{
		$expr = Util::expr();
		$ucbs = $this->em->getRepository(UserCardBalance::class)
			->createQueryBuilder('ucb')
			->join('ucb.userCard', 'c')
			->join('c.user', 'u')
			->join('c.card', 'cpct')
			->leftJoin('cpct.cardProgram', 'cp')
			->where($expr->eq('cp.name',':cpn'))
			->setParameter('cpn', CardProgram::NAME_SPENDR)
			->andWhere($expr->in('ucb.type', ':types'))
			->setParameter('types', [
				UserCardBalance::TYPE_TRANSACTION,
				UserCardBalance::TYPE_REFUND
			])
			->setFirstResult(($page - 1) * $pageSize)
			->setMaxResults($pageSize)
			->groupBy('ucb.meta')
			->getQuery()
			->getResult();

		if (!$ucbs) {
			return new SuccessResponse('No ucb records.');
		}

		$wrong = [];
		$correct = [];
		/** @var UserCardBalance $ucb */
		foreach ($ucbs as $ucb) {
			$metaEntity = Util::meta($ucb, 'entity');
			$txnId = $metaEntity['id'];
			if (!$txnId) {
				$wrong[] = [
					'ucb_id' => $ucb->getId(),
					'reason' => 'no txn id'
				];
				continue;
			}

			/** @var Transaction $txn */
			$txn = Transaction::find($txnId);
			if (!$txn) {
				$wrong[] = [
					'ucb_id' => $ucb->getId(),
					'reason' => 'no txn'
				];
				continue;
			}

			$createdAt = $ucb->getCreatedAt();
			$txn->setTxnTime($createdAt);
			Util::em()->persist($txn);
			$correct[] = [
				'ucb_id' => $ucb->getId(),
				'txn id' => $txnId,
			];
		}
		Util::flush();

		return new SuccessResponse([
			'wrong' => $wrong,
			'correct' => $correct
		]);
	}

	/**
	 * When changing instant load to promotion load, return the load fee
	 *
	 * @Route("/dev/spendr/correct/promo-load-fee/{loadId}")
	 * @param $loadId
	 * @return FailedResponse|SuccessResponse
	 * @throws \PortalBundle\Exception\PortalException
	 */
	public function correctPromotionLoadFee($loadId)
	{
		if (!$loadId) {
			return new FailedResponse('Invalid load id.');
		}

		$load = UserCardLoad::find($loadId);
		if (!$load) {
			return new FailedResponse('Invalid load.');
		}

		$meta = Util::meta($load);
		if (!LoadService::isPromotionLoad($load) || !$meta['isPromotion'] || !$meta['corrected']) {
			return new FailedResponse("It is not promotion load txn.");
		}

		$partnerDummy = UserService::getSpendrBalanceDummyCard('spendr');
		$bankDummy = UserService::getSpendrBalanceDummyCard('bank');
		$ternDummy = UserService::getSpendrBalanceDummyCard('tern');

		/** @var UserFeeHistory $bankFeeHistory */
		$bankFeeHistory = FeeService::hasFeeHistory(
			$partnerDummy->getUser(),
			FeeService::TO_BANK_CONSUMER_LOAD_FEE,
			$load
		);
		if (!$bankFeeHistory) {
			return new FailedResponse('No bank fee history.');
		}

		/** @var UserFeeHistory $ternFeeHistory */
		$ternFeeHistory = FeeService::hasFeeHistory(
			$partnerDummy->getUser(),
			FeeService::TO_TERN_CONSUMER_LOAD_FEE,
			$load
		);
		if (!$ternFeeHistory) {
			return new FailedResponse('No tern fee history.');
		}

		// 1. return bank fee
		$bankFee = $bankFeeHistory->getAmount();
		$bankFeeComment = sprintf(
			'Correct data. Load ID: %s. Return %s from Bank balance %s to Partner %s at %s. Amount: %s',
			$load->getId(),
			strtolower(FeeService::TO_BANK_CONSUMER_LOAD_FEE),
			$bankDummy->getUser()->getSignature(),
			$partnerDummy->getUser()->getSignature(),
			Carbon::now(),
			Money::format($bankFee, 'USD')
		);

		$bankDummy->updatePrivacyBalanceBy(
			-$bankFee,
			FeeService::TO_BANK_CONSUMER_LOAD_FEE,
			$bankFeeComment,
			false,
			$load
		);
		$partnerDummy->updatePrivacyBalanceBy(
			$bankFee,
			FeeService::TO_BANK_CONSUMER_LOAD_FEE,
			$bankFeeComment,
			false,
			$load
		);


		// 2. return tern fee
		$ternFee = $ternFeeHistory->getAmount();
		$ternFeeComment = sprintf(
			'Correct data. Load ID: %s. Return %s from Tern balance %s to Partner %s at %s. Amount: %s',
			$load->getId(),
			strtolower(FeeService::TO_TERN_CONSUMER_LOAD_FEE),
			$ternDummy->getUser()->getSignature(),
			$partnerDummy->getUser()->getSignature(),
			Carbon::now(),
			Money::format($ternFee, 'USD')
		);

		$ternDummy->updatePrivacyBalanceBy(
			-$ternFee,
			FeeService::TO_TERN_CONSUMER_LOAD_FEE,
			$ternFeeComment,
			false,
			$load
		);
		$partnerDummy->updatePrivacyBalanceBy(
			$ternFee,
			FeeService::TO_TERN_CONSUMER_LOAD_FEE,
			$ternFeeComment,
			false,
			$load
		);

		// todo: delete feeHistory record

		Util::flush();

		return new SuccessResponse([
			'bank fee history id' => $bankFeeHistory->getId(),
			'tern fee history id' => $ternFeeHistory->getId(),
		]);
	}

	/**
	 * @Route("/dev/spendr/correct/load-fee/{loadId}")
	 * @param $loadId
	 * @return FailedResponse|SuccessResponse
	 */
	public function correctMissingLoadFees($loadId)
	{
		if (!$loadId) {
			return new FailedResponse('Invalid load id.');
		}

		$load = UserCardLoad::find($loadId);
		if (!$load) {
			return new FailedResponse('Invalid load.');
		}

		if (
			$load
			&& !LoadService::isInstantLoad($load)
			&& !LoadService::isPrefundLoad($load)
		) {
			return new FailedResponse('Invalid load type.');
		}

//		FeeService::chargeConsumerLoadFee($load, $load->getInitialAmount(), true);

		return new SuccessResponse();
	}

	/**
	 * @Route("/dev/spendr/correct/load-bank-fee/{loadId}")
	 * @param $loadId
	 * @return FailedResponse|SuccessResponse
	 * @throws \PortalBundle\Exception\PortalException
	 */
	public function correctLoadBankFee($loadId)
	{
		if (!$loadId) {
			return new FailedResponse('Invalid load id.');
		}

		$load = UserCardLoad::find($loadId);
		if (!$load) {
			return new FailedResponse('Invalid load.');
		}

		$instant = LoadService::isInstantLoad($load);
		$prefund = LoadService::isPrefundLoad($load);
		if (!$instant && !$prefund) {
			return new FailedResponse("It is not valid load txn.");
		}

		$partnerDummy = UserService::getSpendrBalanceDummyCard('spendr');
		$bankDummy = UserService::getSpendrBalanceDummyCard('bank');

		/** @var UserFeeHistory $bankFeeHistory */
		$bankFeeHistory = FeeService::hasFeeHistory(
			$partnerDummy->getUser(),
			FeeService::TO_BANK_CONSUMER_LOAD_FEE,
			$load
		);
		if (!$bankFeeHistory) {
			return new FailedResponse('No bank fee history.');
		}

		$historyFee = (int)($bankFeeHistory->getAmount());
		$fee = (int)(FeeService::consumerLoadFeeToBank($load->getInitialAmount()));

		if ($historyFee === $fee) {
			return new FailedResponse('The fee is correct, no correction required');
		}

		if ($historyFee > $fee) {
			// return diff from bank to partner
			$diff = $historyFee - $fee;
			$comment = sprintf(
				'Correct data. Load ID: %s. Return over-deducted fees (%s) from Bank balance %s to Partner %s at %s.'.
				' Funds should be deducted: %s. Actually deducted: %s. Over-deducted: %s',
				$load->getId(),
				strtolower(FeeService::TO_BANK_CONSUMER_LOAD_FEE),
				$bankDummy->getUser()->getSignature(),
				$partnerDummy->getUser()->getSignature(),
				Carbon::now(),
				Money::format($fee, 'USD'),
				Money::format($historyFee, 'USD'),
				Money::format($diff, 'USD')
			);

			$bankDummy->updatePrivacyBalanceBy(
				-$diff,
				FeeService::TO_BANK_CONSUMER_LOAD_FEE,
				$comment,
				false,
				$load
			);
			$partnerDummy->updatePrivacyBalanceBy(
				$diff,
				FeeService::TO_BANK_CONSUMER_LOAD_FEE,
				$comment,
				false,
				$load
			);
		} else if ($historyFee < $fee) {
			// charge the diff from partner to bank
			$diff = $fee - $historyFee;
			$comment = sprintf(
				'Correct data. Load ID: %s. Correct %s. Deduct the difference amount from Partner %s to Bank %s at %s.'.
				' Funds should be deducted: %s. Actually deducted: %s. Difference: %s',
				$load->getId(),
				strtolower(FeeService::TO_BANK_CONSUMER_LOAD_FEE),
				$partnerDummy->getUser()->getSignature(),
				$bankDummy->getUser()->getSignature(),
				Carbon::now(),
				Money::format($fee, 'USD'),
				Money::format($historyFee, 'USD'),
				Money::format($diff, 'USD')
			);
			$partnerDummy->updatePrivacyBalanceBy(
				-$diff,
				FeeService::TO_BANK_CONSUMER_LOAD_FEE,
				$comment,
				false,
				$load
			);
			$bankDummy->updatePrivacyBalanceBy(
				$diff,
				FeeService::TO_BANK_CONSUMER_LOAD_FEE,
				$comment,
				false,
				$load
			);
		}

//		$bankFeeHistory->setAmount($fee);
//		Util::em()->persist($bankFeeHistory);
		Util::updateMeta($load, [
			"corrected" => true
		]);
		Util::flush();

		return new SuccessResponse();
	}

	/**
	 * correct load 409284
	 *
	 * @Route("/dev/spendr/correct/indigo-load-txn")
	 * @return FailedResponse|SuccessResponse
	 * @throws \PortalBundle\Exception\PortalException
	 */
	public function correctMissingPartnerFee()
	{
		if (!Util::isLive()) {
			return new FailedResponse();
		}
		$loadId = 409284;

		$load = UserCardLoad::find($loadId);
		if (!$load) {
			return new FailedResponse('Invalid load.');
		}

		if (Util::meta($load, 'corrected')) {
			return new FailedResponse('Already corrected.');
		}

		$partnerDummy = UserService::getSpendrBalanceDummyCard('spendr');
		$bankDummy = UserService::getSpendrBalanceDummyCard('bank');
		$ternDummy = UserService::getSpendrBalanceDummyCard('tern');

		// correct tern fee
		// partner - $0.1, tern + $0.02
		// add user fee history record
		$ternFeeType = FeeService::TO_TERN_CONSUMER_LOAD_FEE;
		$ternFee = 10;
		$ternFeeDiff = 2;
		$ternFeeComment = sprintf(
			'Correct data. Load ID: %s. Add underpayment amount. Add %s to Tern %s at %s. Amount: %s',
			$load->getId(),
			strtolower($ternFeeType),
			$ternDummy->getUser()->getId(),
			Carbon::now(),
			Money::format($ternFeeDiff, 'USD')
		);
		$ternDummy->updatePrivacyBalanceBy(
			$ternFeeDiff,
			$ternFeeType,
			$ternFeeComment,
			false,
			$load
		);
		$partnerTernFeeComment = sprintf(
			'Correct data. Load ID: %s. Charge %s on Partner %s at %s. Amount: %s',
			$load->getId(),
			strtolower($ternFeeType),
			$partnerDummy->getUser()->getId(),
			Carbon::now(),
			Money::format($ternFee, 'USD')
		);
		$partnerDummy->updatePrivacyBalanceBy(
			-$ternFee,
			$ternFeeType,
			$partnerTernFeeComment,
			false,
			$load
		);
		UserFeeHistory::create($partnerDummy->getUser(), $ternFee, $ternFeeType, $load, $partnerTernFeeComment, Platform::spendr());

		// correct bank fee
		// partner - $0.13, bank $0.25 - $0.13 = $0.12, so bank - $0.12
		// add user fee history record
		$bankFeeType = FeeService::TO_BANK_CONSUMER_LOAD_FEE;
		$bankFee = 13;
		$diff = 12;
		$bankFeeComment = sprintf(
			'Correct data. Load ID: %s. Deduct overpayment. Deduct %s on Bank %s at %s. Amount: %s',
			$load->getId(),
			strtolower($bankFeeType),
			$bankDummy->getUser()->getId(),
			Carbon::now(),
			Money::format($diff, 'USD')
		);
		$bankDummy->updatePrivacyBalanceBy(
			-$diff,
			FeeService::TO_BANK_CONSUMER_LOAD_FEE,
			$bankFeeComment,
			false,
			$load
		);
		$partnerBankFeeComment = sprintf(
			'Correct data. Load ID: %s. Charge %s on Partner %s at %s. Amount: %s',
			$load->getId(),
			strtolower($bankFeeType),
			$partnerDummy->getUser()->getId(),
			Carbon::now(),
			Money::format($bankFee, 'USD')
		);
		$partnerDummy->updatePrivacyBalanceBy(
			-$bankFee,
			FeeService::TO_BANK_CONSUMER_LOAD_FEE,
			$partnerBankFeeComment,
			false,
			$load
		);
		UserFeeHistory::create($partnerDummy->getUser(), $bankFee, $bankFeeType, $load, $partnerBankFeeComment, Platform::spendr());

		Util::flush();
		return new SuccessResponse();
	}

	/**
	 * @Route("/dev/spendr/correct/canceled-load-txn/{loadId}")
	 *
	 * @param $loadId
	 * @return FailedResponse|SuccessResponse
	 */
	public function correctCanceledLoadTxn($loadId)
	{
		if (!$loadId) {
			return new FailedResponse('Invalid load id.');
		}

		$load = UserCardLoad::find($loadId);
		if (!$load) {
			return new FailedResponse('Invalid load.');
		}

		$ach = AchTransactions::findTransactionByTranId($load->getTransactionNo());

		SpendrWebhookService::spendrTransactionWebhook($ach, 'Failed at Yodlee check.');

		return new SuccessResponse();
	}

	/**
	 * @Route("/dev/spendr/correct/txn-spendr-fee/{txnId}")
	 *
	 * @param $txnId
	 * @return FailedResponse|SuccessResponse
	 * @throws \PortalBundle\Exception\PortalException
	 */
	public function correctTxnSpendrFee($txnId)
	{
		if (!Util::isLive()) {
			return new FailedResponse();
		}
		if (!$txnId || !in_array($txnId, [
			24414,24415
		])) {
			return new FailedResponse('Invalid load id.');
		}
		/** @var Transaction $txn */
		$txn = Transaction::find($txnId);

		if (!$txn) {
			return new FailedResponse('Invalid txn.');
		}

		if (Util::meta($txn, 'corrected')) {
			return new FailedResponse('Already corrected.');
		}

        $diff = $should = $actual = 0;
		if ((int)$txnId === 24414) {
			$should = 2012;
//			$actual = 907;
//			$diff = 2012 - 907; // 1105
			$actual = 907 + 784;
			$diff = 2012 - 907 - 784; // 321
		} else if ((int)$txnId === 24415) {
			$should = 1429;
			$actual = 645;
			$diff = 1429 - 645; // 784
		}

		$feeType = FeeService::TO_SPENDR_TRANSACTION_FEE;
		$partnerDummy = UserService::getSpendrBalanceDummyCard('spendr');
		$partnerBankFeeComment = sprintf(
			'Correct data. Transaction ID: %s. Add %s to Partner %s at %s. Amount: %s. ' .
			'Funds should be added: %s. Actually added: %s. Difference: %s',
			$txn->getId(),
			strtolower($feeType),
			$partnerDummy->getUser()->getId(),
			Carbon::now(),
			Money::format($diff, 'USD'),
			Money::format($should, 'USD'),
			Money::format($actual, 'USD'),
			Money::format($diff, 'USD'),
		);
		$partnerDummy->updatePrivacyBalanceBy(
			$diff,
			FeeService::TO_SPENDR_TRANSACTION_FEE,
			$partnerBankFeeComment,
			false,
			$txn
		);
		Util::updateMeta($txn,[
			'corrected' => true
		]);
		Util::flush();

		return new SuccessResponse();
	}

	/**
	 * @Route("/dev/spendr/correct/negative-balance/{page}/{pageSize}")
	 *
	 * @param $page
	 * @param $pageSize
	 * @return FailedResponse|SuccessResponse
	 * @throws \PortalBundle\Exception\PortalException
	 */
	public function correctNegativeBalance($page, $pageSize)
	{
		$ucs = $this->em->getRepository(UserCard::class)
			->createQueryBuilder('uc')
			->join('uc.user', 'u')
			->join('uc.card', 'cpct')
			->leftJoin('cpct.cardProgram', 'cp')
			->join('u.teams', 't')
			->where(Util::expr()->in('t.name', ':roles'))
			->setParameter('roles', SpendrBundle::getConsumerRoles())
			->andWhere('cp.name = :cpn')
			->setParameter('cpn', CardProgram::NAME_SPENDR)
			->andWhere('uc.pendingFees is not null')
			->andWhere('uc.pendingFees <> :pendingFees')
			->setParameter('pendingFees', '[]')
			->setFirstResult(($page - 1) * $pageSize)
			->setMaxResults($pageSize)
			->getQuery()
			->getResult();

		if (!$ucs) {
			return new FailedResponse('No uc records.');
		}

		/** @var UserCard $uc */
		foreach ($ucs as $uc) {
			$pendingBalances = Util::s2j($uc->getPendingFees());
			$total = 0;
			foreach ($pendingBalances as $key=>$pb) {
				if (
					isset($pb['oldData'])
					&& (!isset($pb['corrected']) || !$pb['corrected'])
					&& (
						($pb['type'] === UserCard::PENDING_FEE_BACK_TO_PARTNER)
						|| ($pb['type'] === UserCard::PENDING_ACH_RETURN_FEE_BACK_TO_PARTNER)
					)
				) {
					$total += $pb['amount'];
					$pendingBalances[$key]['corrected'] = true;
				}
			}
			if ($total) {
				$spendrDummy = UserService::getSpendrBalanceDummyCard('spendr');
				$spendrComment = sprintf(
					'Correct data. %s. Deduct pending fee of consumer %s from the partner %s at %s. Amount: %s.',
					UserCard::PENDING_FEE_BACK_TO_PARTNER,
					$uc->getUser()->getSignature(),
					$spendrDummy->getUser()->getSignature(),
					Carbon::now(),
					Money::format($total, 'USD'),
				);

				$spendrDummy->updatePrivacyBalanceBy(
					-$total,
					UserCard::PENDING_FEE_BACK_TO_PARTNER,
					$spendrComment,
					false,
					$uc
				);

//				SlackService::tada($spendrComment);

				$uc->setPendingFees(Util::j2s($pendingBalances))
					->persist();
			}
		}
		Util::flush();

		return new SuccessResponse();
	}

	/**
	 * @Route("/dev/spendr/correct/test-txn-tern-fee")
	 *
	 * @return FailedResponse|SuccessResponse
	 * @throws \PortalBundle\Exception\PortalException
	 */
	public function correctTestTxnTernFee()
	{
		if (!Util::isLive()) {
			return new FailedResponse();
		}
		$txnIds = [24408, 24409];

		$ts = $this->em->getRepository(Transaction::class)
			->createQueryBuilder('t')
			->where(Util::expr()->in('t.id', ':ids'))
			->setParameter('ids', $txnIds)
			->andWhere('t.meta not like :meta')
			->setParameter('meta', '%"correctedTernFee":true%')
			->getQuery()
			->getResult();

		if (!$ts) {
			return new FailedResponse('No txn record');
		}

		$ternDummy = UserService::getSpendrBalanceDummyCard('tern');
		/** @var Transaction $t */
		foreach ($ts as $t) {
			$comment = sprintf(
				'Correct data. Correct test data. Deduct %s from Tern balance %s at %s. Amount: %s.',
				FeeService::TO_TERN_TRANSACTION_FEE,
				$ternDummy->getUser()->getSignature(),
				Carbon::now(),
				Money::format(10, 'USD')
			);
			$ternDummy->updatePrivacyBalanceBy(
				-10,
				FeeService::TO_TERN_TRANSACTION_FEE,
				$comment,
				false,
				$t
			);

			Util::updateMeta($t, [
				'correctedTernFee' => true
			]);
		}
		Util::flush();

		return new SuccessResponse();
	}

	/**
	 * @Route("/dev/spendr/correct/test-txn-spendr-fee")
	 *
	 * @return FailedResponse|SuccessResponse
	 * @throws \PortalBundle\Exception\PortalException
	 */
	public function correctTestTxnSpendrFee()
	{
		if (!Util::isLive()) {
			return new FailedResponse();
		}
		$txnIds = [24408, 24409];

		$ts = $this->em->getRepository(Transaction::class)
			->createQueryBuilder('t')
			->where(Util::expr()->in('t.id', ':ids'))
			->setParameter('ids', $txnIds)
			->andWhere('t.meta not like :meta')
			->setParameter('meta', '%"correctedSpendrFee":true%')
			->getQuery()
			->getResult();

		if (!$ts) {
			return new FailedResponse('No txn record');
		}

		$partnerDummy = UserService::getSpendrBalanceDummyCard('spendr');
		/** @var Transaction $t */
		foreach ($ts as $t) {
			$fee = $t->getSpendrFee();
			$comment = sprintf(
				'Correct data. Correct test data. Deduct %s from Partner balance %s at %s. Amount: %s.',
				FeeService::TO_SPENDR_TRANSACTION_FEE,
				$partnerDummy->getUser()->getSignature(),
				Carbon::now(),
				Money::format($fee, 'USD')
			);
			$partnerDummy->updatePrivacyBalanceBy(
				-$fee,
				FeeService::TO_SPENDR_TRANSACTION_FEE,
				$comment,
				false,
				$t
			);

			Util::updateMeta($t, [
				'correctedSpendrFee' => true
			]);
		}
		Util::flush();

		return new SuccessResponse();
	}

	/**
	 * Note: Due to some misoperation, the load transaction was cancelled, and an unload was generated,
	 * which is used to roll back the unload
	 *
	 * @Route("/dev/spendr/correct/rollback/{unloadId}")
	 */
	public function rollbackUnloadTxn($unloadId)
	{
		if (!$unloadId) {
			return new FailedResponse('Invalid unload id.');
		}

		$unload = UserCardLoad::find($unloadId);
		if (!$unload) {
			return new FailedResponse('No record.');
		}

		$failedLoadId = Util::meta($unload, 'Spendr load failed');
		$failedLoad = UserCardLoad::find($failedLoadId);
		$isInstantCanceled = Util::meta($unload, 'returnInstantLoadFundsToPartnerBalance');
		$rollback = Util::meta($unload, 'Spendr rollback');
		if (!$failedLoadId || !$isInstantCanceled || $rollback) {
			return new FailedResponse('The unload transaction cannot be rolled back.');
		}

		if (
			$failedLoad->getLoadStatus() !== UserCardLoad::LOAD_STATUS_ERROR
			|| !LoadService::isInstantLoad($failedLoad)
			|| Util::meta($failedLoad, 'Spendr reload')
		) {
			return new FailedResponse('The load transaction can only be reloaded once.');
		}

		$consumerDummy = $unload->getUserCard();
		$msg = sprintf(
			'Start rolling back the load transaction. Failed load ID: %s. Unload ID: %s. Amount: %s.',
			$failedLoad->getId(),
			$unload->getId(),
			Money::format($unload->getLoadAmount(), 'USD')
		);
		SlackService::alert($msg, ['Consumer' => $consumerDummy->getUser()->getSignature()]);

		// 1. rollback unload: rollback by generating a new rollback load
		$rollbackLoad = $consumerDummy->ensureLoad();
		$rollbackLoad->setInitialAmount($unload->getLoadAmount())
			->setPayCurrency('USD')
			->setPayAmount($unload->getLoadAmount())
			->setInitialCurrency('USD')
			->setInitializedAt(new \DateTime())
			->setReceivedCurrency('USD')
			->setReceivedAmount($unload->getLoadAmount())
			->setCompletedAt(new \DateTime())
			->setStatus(UserCardLoad::STATUS_COMPLETED)
			->setLoadStatus(UserCardLoad::LOAD_STATUS_RECEIVED)
			->setMeta(Util::j2s([
				LoadService::LOAD_TYPE_ROLLBACK => true,
				'Spendr rollback unload' => $unload->getId()
			]))
			->persist();

		$msg = sprintf(
			'Rollback load. Generate a rollback load (Load ID: %s) for the unload transaction with id %s. Amount: %s',
			$rollbackLoad->getId(),
			$unload->getId(),
			Money::format($unload->getLoadAmount(), 'USD')
		);
		SlackService::eyes($msg);

		Util::updateMeta($unload, [
			'Spendr rollback' => true
		]);

		// 2. reset failed load status, and generate new user_card_transaction & ach_transaction records
		$oldTxnNo = $failedLoad->getTransactionNo();
		$oldTxn = UserCardTransaction::findByTranId($oldTxnNo);
		if ($oldTxnNo) {
			// For easy checking later
			Util::updateMeta($failedLoad, [
				'spendrOldTransactionNo' => $oldTxnNo
			]);
		}

		$loadAmount = $failedLoad->getLoadAmount();
		$config = $consumerDummy->getUser()->ensureConfig();
		$uc = $oldTxn->getUserCard();
		$transaction = SpendrACHService::createPayment(
			$uc,
			'debit',
			$loadAmount,
			$config->getBankAccountId()
		);

		$failedLoad->setTransactionNo($transaction->getTranId())
			->setReceivedCurrency('USD')
			->setReceivedAmount($loadAmount)
			->setCompletedAt(new \DateTime())
			->setStatus(UserCardLoad::STATUS_COMPLETED)
			->setLoadStatus(UserCardLoad::LOAD_STATUS_LOADED)
			->setError('')
			->persist();
		Util::updateMeta($failedLoad, [
			'Spendr reload' => true
		]);

		$msg = sprintf(
			'Rollback load. Change the load status of the failed load (Load ID: %s) to %s, '.
			 'and generate a new ACH transaction record (Txn ID: %s) for it.',
			$failedLoad->getId(),
			$failedLoad->getLoadStatus(),
			$transaction->getTranId(),
		);
		SlackService::eyes($msg);

		LoadService::LoadCard($rollbackLoad);

		return new SuccessResponse();
	}

	/**
	 * Rollback all bank fees to Spendr
	 *
	 * @Route("/dev/spendr/correct/rollback-bank-fee/{feeName}")
	 * @param $feeName
	 * @return FailedResponse|SuccessResponse
	 * @throws \PortalBundle\Exception\PortalException
	 */
	public function rollbackBankFeeToSpendr($feeName)
	{
		if (!in_array($feeName, ['load_fee', 'return_fee', 'monthly_account_fee'])) {
			return new FailedResponse('Invalid fee name.');
		}

		if ($feeName === 'load_fee') {
			$feeName = FeeService::TO_BANK_CONSUMER_LOAD_FEE;
		} else if ($feeName === 'return_fee') {
			$feeName = FeeService::TO_BANK_SPENDR_ACH_RETURN_FEE;
		} else if ($feeName === 'monthly_account_fee') {
			$feeName = FeeService::TO_BANK_SPENDR_MONTHLY_ACCOUNT_FEE;
		}

		$res = $this->getFees($feeName);

		if (!$res || !$res['amount'] || !$res['total']) {
			return new FailedResponse('No record.');
		}

		SlackService::prepareForPlatform(Platform::spendr());

		$partnerDummy = UserService::getSpendrBalanceDummyCard('spendr');
		$bankDummy = UserService::getSpendrBalanceDummyCard('bank');

		$comment = sprintf(
			'Correct data. Rollback %s. Rollback all "%s" fees from Bank balance (%s) to Partner (%s) at %s. Amount: %s',
			$feeName,
			$feeName,
			$bankDummy->getUser()->getSignature(),
			$partnerDummy->getUser()->getSignature(),
			Carbon::now(),
			Money::format($res['amount'], 'USD')
		);

		$partnerDummy->updatePrivacyBalanceBy($res['amount'], $feeName, $comment, false, null);
		$bankDummy->updatePrivacyBalanceBy(-$res['amount'], $feeName, $comment, false, null);

		SlackService::eyes($comment);

		return new SuccessResponse('Rollback successful.', [
			'Count' => $res['total'],
			'Amount' => $res['amount']
		]);
	}

	private function getFees($feeName)
	{
		if (!$feeName) {
			return null;
		}

		$spendrAdmin = UserService::getSpendrBalanceAdminAccount('spendr');

		$expr = Util::expr();
		$res = Util::em()->getRepository(UserFeeHistory::class)
			->createQueryBuilder('ufh')
			->where($expr->eq('ufh.feeName', ':feeName'))
			->setParameter('feeName', $feeName)
			->andWhere($expr->eq('ufh.user', ':user'))
			->setParameter('user', $spendrAdmin)
			->select('sum(ufh.amount) as amount, count(distinct ufh) as total')
			->getQuery()
			->getArrayResult();

		return $res ? $res[0] : null;
	}

	/**
	 * Rollback all tern fees to Partner
	 *
	 * @Route("/dev/spendr/correct/rollback-tern-fee/{feeName}")
	 * @param $feeName
	 * @return FailedResponse|SuccessResponse
	 * @throws \PortalBundle\Exception\PortalException
	 */
	public function rollbackTernFeeToPartner($feeName)
	{
		if (!in_array($feeName, ['load_fee', 'txn_fee', 'withdraw_fee', 'on_file_fee'])) {
			return new FailedResponse('Invalid fee name.');
		}

		if ($feeName === 'load_fee') {
			$feeName = FeeService::TO_TERN_CONSUMER_LOAD_FEE;
		} else if ($feeName === 'txn_fee') {
			$feeName = FeeService::TO_TERN_TRANSACTION_FEE;
		} else if ($feeName === 'withdraw_fee') {
			$feeName = FeeService::TO_TERN_MERCHANT_WITHDRAWAL_FEE;
		} else if ($feeName === 'on_file_fee') {
			$feeName = FeeService::TO_TERN_MERCHANT_MONTHLY_ON_FILE_FEE;
		}

		$res = $this->getFees($feeName);

		if (!$res || !$res['amount'] || !$res['total']) {
			return new FailedResponse('No record.');
		}

		SlackService::prepareForPlatform(Platform::spendr());

		$partnerDummy = UserService::getSpendrBalanceDummyCard('spendr');
		$ternDummy = UserService::getSpendrBalanceDummyCard('tern');

		$comment = sprintf(
			'Correct data. Rollback %s. Rollback all "%s" fees from Tern balance (%s) to Partner (%s) at %s. Amount: %s',
			$feeName,
			$feeName,
			$ternDummy->getUser()->getSignature(),
			$partnerDummy->getUser()->getSignature(),
			Carbon::now(),
			Money::format($res['amount'], 'USD')
		);

		$partnerDummy->updatePrivacyBalanceBy($res['amount'], $feeName, $comment, false, null);
		$ternDummy->updatePrivacyBalanceBy(-$res['amount'], $feeName, $comment, false, null);

		SlackService::eyes($comment);

		return new SuccessResponse(
			[
				'Count' => $res['total'],
				'Amount' => $res['amount']
			],
			'Rollback successful.'
		);
	}

	/**
	 * Re-execute load fee and service charge logic
	 *
	 * @Route("/dev/spendr/correct/bank-ledger/{desc}")
	 * @param $desc
	 * @return FailedResponse|SuccessResponse
	 */
	public function correctBankLedger($desc)
	{
//		if (!Util::isLive()) {
//			return new FailedResponse('No permission.');
//		}

		if (!in_array($desc, [
			'service_charge', 'load_fee'
		])) {
			return new FailedResponse('Invalid parameters.');
		}

		SlackService::prepareForPlatform(Platform::spendr());
		if ($desc === 'load_fee') {
			ImportTransactionsService::handleBankAchLoadFeeRows();
		} else if ($desc === 'service_charge') {
			ImportTransactionsService::handleBankServiceChargeRows();
		}

		return new SuccessResponse();
	}

	/**
	 * @Route("/dev/spendr/correct/calculate-bank-fee/{date}")
	 * @param $date
	 * @return FailedResponse|SuccessResponse
	 */
	public function calculateBankFee($date = null) {
		$carbonDate = $date ? Carbon::parse($date) : null;
		$start = $date ? Carbon::parse($date)->startOfMonth() : null;
		$end = $date ? Carbon::parse($date)->endOfMonth() : null;

		$loadFee = (int)(FeeService::calculateBankLoadFee($start, $end));
		$returnFee = (int)(FeeService::calculateBankReturnedLoadFee($start, $end));
		$monthlyAccountFee = (int)(FeeService::calculateBankMonthlyAccountFee($start, $end));
		$wireFee = (int)(FeeService::calculateBankWireFee($start, $end));
		$achSetupFee = (int)(FeeService::calculateBankAchSetupFee($start, $end));
		$fee = $loadFee + $returnFee + $monthlyAccountFee + $wireFee + $achSetupFee;

		$monthlyAccountFeeUfh = (int)(FeeService::getBankMonthlyAccountFee($start, $end));
		$returnedLoadFeeUfh = (int)(FeeService::getBankReturnedLoadFee($start, $end));
		$loadFeeUfh = (int)(FeeService::getBankLoadFee($start, $end));
		$wireFeeUfh = (int)(FeeService::getBankWireFee($start, $end));
		$achSetupFeeUfh = (int)(FeeService::getBankAchSetupFee($start, $end));

		$feeUfh = $loadFeeUfh + $returnedLoadFeeUfh + $monthlyAccountFeeUfh + $wireFeeUfh + $achSetupFeeUfh;
		return new SuccessResponse(
			[
				'calculate' => [
					'all bank fees' => $fee,
					'load fee' => $loadFee,
					'return fee' => $returnFee,
					'monthly account fee' => $monthlyAccountFee,
					'wire fee' => $wireFee,
					'ach setup fee' => $achSetupFee,
				],
				'ufh' => [
					'all bank fee' => $feeUfh,
					'load fee' => $loadFeeUfh,
					'return fee' => $returnedLoadFeeUfh,
					'monthly account fee' => $monthlyAccountFeeUfh,
					'wire fee' => $wireFeeUfh,
					'ach setup fee' => $achSetupFeeUfh,
				]
			]
		);
	}

	/**
	 * @Route("/dev/spendr/correct/calculate-tern-fee/{date}")
	 * @param $date
	 * @return FailedResponse|SuccessResponse
	 */
	public function calculateTernFee($date = null) {
		$start = $date ? Carbon::parse($date)->startOfMonth() : null;
		$end = $date ? Carbon::parse($date)->endOfMonth() : null;

		$loadFee = (int)(FeeService::calculateTernLoadFee($start, $end));
		$txnFee = (int)(FeeService::calculateTernTxnFee($start, $end));
		$withdrawFee = (int)(FeeService::calculateTernWithdrawFee($start, $end));
		$onFileFee = (int)(FeeService::calculateTernMonthlyOnFileFee($start, $end));

		$fee = $loadFee + $txnFee + $withdrawFee + $onFileFee;

//		$loadFeeUfh = (int)(FeeService::getBankOrTernFeesFromUFH(FeeService::TO_TERN_CONSUMER_LOAD_FEE));
//		$txnFeeUfh = (int)(FeeService::getBankOrTernFeesFromUFH(FeeService::TO_TERN_TRANSACTION_FEE));
//		$withdrawFeeUfh = (int)(FeeService::getBankOrTernFeesFromUFH(FeeService::TO_TERN_MERCHANT_WITHDRAWAL_FEE));
//		$onFileFeeUfh = (int)(FeeService::getBankOrTernFeesFromUFH(FeeService::TO_TERN_MERCHANT_MONTHLY_ON_FILE_FEE));
//
//		$feeUfh = $loadFeeUfh + $txnFeeUfh + $withdrawFeeUfh + $onFileFeeUfh;

		return new SuccessResponse(
			[
				'calculate fee' => [
					'all tern fees' => $fee,
					'load fee' => $loadFee,
					'txn fee' => $txnFee,
					'withdraw fee' => $withdrawFee,
					'on file fee' => $onFileFee,
				],
//				'ufh fee' => [
//					'all tern fees' => $feeUfh,
//					'load fee' => $loadFeeUfh,
//					'txn fee' => $txnFeeUfh,
//					'withdraw fee' => $withdrawFeeUfh,
//					'on file fee' => $onFileFeeUfh,
//				]
			]
		);
	}

	/**
	 * @Route("/dev/spendr/correct/import/consumer-load-fee/{txnId}")
	 * @param $txnId
	 * @return FailedResponse|SuccessResponse
	 * @throws \PortalBundle\Exception\PortalException
	 */
	public function correctConsumerLoadFeeByImport($txnId)
	{
		if (!$txnId) {
			return new FailedResponse('Invalid parameters.');
		}

		/** @var SpendrTransaction $st */
		$st = $this->em->getRepository(SpendrTransaction::class)
			->find($txnId);

		if (!$st) {
			return new FailedResponse('No record.');
		}

		if (Util::meta($st, 'corrected')) {
			return new FailedResponse('Already corrected.');
		}

		if (
			(Util::isLive() && (int)$txnId === 298)
			|| (Util::isStaging() && (int)$txnId === 189) // for test
		) {
			$backFunds = 3750;
			$partnerDummy = UserService::getSpendrBalanceDummyCard('spendr');
			$comment = sprintf(
				'Correct data. Correct "%s". Return the wrong fee to Partner balance (%s) at %s. Amount: %s',
				FeeService::TO_BANK_CONSUMER_LOAD_FEE,
				$partnerDummy->getUser()->getSignature(),
				Carbon::now(),
				Money::format($backFunds, 'USD')
			);
			$partnerDummy->updatePrivacyBalanceBy(
				-$backFunds,
				FeeService::TO_BANK_CONSUMER_LOAD_FEE,
				$comment,
				false,
				$st
			);
			SlackService::eyes($comment);
			Util::updateMeta($st, [
				'corrected' => true
			]);
		}

		return new SuccessResponse();
	}

	/**
	 * User ID: *********.
	 * Load ID: 481479.
	 * Amount: $25.
	 * Error due to repeated load, $25 extra load
	 *
	 * @Route("/dev/spendr/correct/consumer-wrong-txn")
	 *
	 * @return FailedResponse|SuccessResponse
	 * @throws \PortalBundle\Exception\PortalException
	 */
	public function correctConsumerWrongTxn()
	{
		if (Util::isLive()) {
			$userId = *********;
			$loadId = 481479;
			$load = UserCardLoad::find($loadId);
			if (Util::meta($load, 'corrected')) {
				return new FailedResponse('The transaction data has been corrected and cannot be repeated.');
			}
			$user = $load->getUserCard()->getUser();
			if ($user->getId() !== $userId) {
				return new FailedResponse('The data cannot be corrected');
			}
		} else {
			// for test
			$userId = *********;
			$loadId = 1004;
			$load = UserCardLoad::find($loadId);
			if (Util::meta($load, 'corrected')) {
				return new FailedResponse('The transaction data has been corrected and cannot be repeated.');
			}
			$user = $load->getUserCard()->getUser();
			if ($user->getId() !== $userId) {
				return new FailedResponse('The data cannot be corrected');
			}
		}

		$returnAmount = 2500;

		$consumerDummy = UserService::getDummyCard($user);
		$partnerDummy = UserService::getSpendrBalanceDummyCard('spendr');

		$comment = sprintf(
			'Correct data. Load ID: %s. Return the part of the repeated load to the Partner balance (%s) at %s. Amount: %s',
			$loadId,
			$partnerDummy->getUser()->getSignature(),
			Carbon::now(),
			Money::format($returnAmount, 'USD')
		);
		$partnerDummy->updatePrivacyBalanceBy(
			$returnAmount,
			UserCardBalance::TYPE_LOAD_CARD,
			$comment,
			false,
			$load
		);
		$consumerDummy->updatePrivacyBalanceBy(
			-$returnAmount,
			UserCardBalance::TYPE_LOAD_CARD,
			$comment,
			false,
			$load
		);
		SlackService::eyes($comment);
		Util::updateMeta($load, [
			'corrected' => true
		]);
		return new SuccessResponse();
	}

	/**
	 * https://app.asana.com/0/1201058194083721/1203847221884132/f
	 * Merchant ID: 43.
	 * Txn Fee: from 5% + $0.50 to 3.5% + $0.50.
	 *
	 * @Route("/dev/spendr/correct/merchant-txn-fee/{handleType}")
	 *
	 * @param $handleType
	 * @return FailedResponse|SuccessResponse
	 * @throws \PortalBundle\Exception\PortalException
	 * @throws \Throwable
	 */
	public function correctTxnFeeByFeeChange($handleType)
	{
		if (Util::isLive()) {
			$merchantId = 43;
		} else {
			$merchantId = 53;
		}

		/** @var Merchant $merchant */
		$merchant = Merchant::find($merchantId);
		$merchantAdmin = $merchant->getAdminUser();
		$feeName = FeeService::TO_SPENDR_TRANSACTION_FEE;

		$txns = Util::em()->getRepository(Transaction::class)
			->createQueryBuilder('t')
			->where('t.merchant = :merchant')
			->setParameter('merchant', $merchant)
			->andWhere('t.type = :type')
			->setParameter('type', TransactionType::get(TransactionType::NAME_PURCHASE))
			->andWhere('t.status = :status')
			->setParameter('status', TransactionStatus::get(TransactionStatus::NAME_COMPLETED))
			->andWhere('t.spendrFee is not null')
			->getQuery()
			->getResult();

		$newFees = null;
		$oldFees = null;

		if (!$txns) {
			return new FailedResponse('No txns.');
		}

		/** @var Transaction $txn */
		foreach ($txns as $txn) {
			if (Util::meta($txn, 'corrected')) {
				continue;
			}
			$old = $txn->getSpendrFee();
			$new = FeeService::getTransactionFeeToSpendr($txn, $txn->getAmount());
			if ((int)$old !== (int)$new) {
				$oldFees += $old;
				$newFees += $new;
				if ($handleType === 'execute') {
					$txn->setSpendrFee($new);
					Util::persist($txn);
					Util::updateMeta($txn, [
						'corrected' => true
					]);

					/** @var UserFeeHistory $ufh */
					$ufh = FeeService::hasFeeHistory($merchantAdmin, $feeName, $txn);
					if ($ufh) {
						$comment = $ufh->getComment();
						$strPos = strpos($comment, '. Amount: USD $');
						$newComment = substr($comment, 0, $strPos);
						$amountStr = '. Amount: ' . Money::formatUSD($new);
						$newComment .= $amountStr;
						$ufh->setAmount($new)
							->setComment($newComment);
						Util::persist($ufh);
					}
				}
			}
		}

		Util::flush();

		$overageFee = $oldFees - $newFees;

		if ($handleType === 'test') {
			return new SuccessResponse([
				'merchant id' => $merchantId,
				'old fee' => $oldFees,
				'new fee' => $newFees,
				'overage fee' => $overageFee
			]);
		}

		if ($overageFee <= 0) {
			return new FailedResponse('No overage fees.');
		}

		if ($handleType !== 'execute') {
			return new FailedResponse('Invalid handle type.');
		}

		$merchantDummy = UserService::getDummyCard($merchantAdmin);
		$partnerDummy = UserService::getSpendrBalanceDummyCard('spendr');

		$comment = sprintf(
			"Correct data. %s. Due to the reduction of the fee, the merchant's (Merchant ID: %s) overpayment was returned from the Partner balance (%s) at %s. Amount: %s.",
			$feeName,
			$merchantId,
			$partnerDummy->getUser()->getSignature(),
			Carbon::now(),
			Money::format($overageFee, 'USD')
		);
		$merchantDummy->updatePrivacyBalanceBy(
			$overageFee,
			FeeService::TO_SPENDR_TRANSACTION_FEE,
			$comment,
			false,
			null
		);
		$partnerDummy->updatePrivacyBalanceBy(
			-$overageFee,
			FeeService::TO_SPENDR_TRANSACTION_FEE,
			$comment,
			false,
			null
		);
		SlackService::eyes($comment);

		return new SuccessResponse();
	}

	/**
	 * https://app.asana.com/0/1201058194083721/1203847221884132/f
	 * Merchant ID: 43.
	 * Refund Txn Fee: from 5% to 3.5%.
	 *
	 * @Route("/dev/spendr/correct/merchant-refund-txn-fee/{handleType}")
	 *
	 * @param $handleType
	 * @return FailedResponse|SuccessResponse
	 * @throws \PortalBundle\Exception\PortalException
	 * @throws \Throwable
	 */
	public function correctRefundTxnFeeByFeeChange($handleType)
	{
		if (Util::isLive()) {
			$merchantId = 43;
		} else {
			$merchantId = 53;
		}

		/** @var Merchant $merchant */
		$merchant = Merchant::find($merchantId);
		$merchantAdmin = $merchant->getAdminUser();
		$feeName = FeeService::TO_MERCHANT_REFUND_TRANSACTION_FEE;
		$partnerDummy = UserService::getSpendrBalanceDummyCard('spendr');
		$partnerAdmin = $partnerDummy->getUser();

		$txns = Util::em()->getRepository(Transaction::class)
			->createQueryBuilder('t')
			->where('t.merchant = :merchant')
			->setParameter('merchant', $merchant)
			->andWhere('t.type = :type')
			->setParameter('type', TransactionType::get(TransactionType::NAME_REFUND))
			->andWhere('t.status = :status')
			->setParameter('status', TransactionStatus::get(TransactionStatus::NAME_COMPLETED))
			->andWhere('t.refundFee is not null')
			->getQuery()
			->getResult();

		$newFees = null;
		$oldFees = null;

		if (!$txns) {
			return new FailedResponse('No txns.');
		}

		/** @var Transaction $txn */
		foreach ($txns as $txn) {
			if (Util::meta($txn, 'corrected')) {
				continue;
			}
			$old = $txn->getRefundFee();
			$new = FeeService::getRefundTransactionFeeToMerchant($txn->getAmount(), $txn);
			if ((int)$old !== (int)$new) {
				$oldFees += $old;
				$newFees += $new;
				if ($handleType === 'execute') {
					$txn->setRefundFee($new);
					Util::persist($txn);
					Util::updateMeta($txn, [
						'corrected' => true
					]);

					/** @var UserFeeHistory $ufh */
					$ufh = FeeService::hasFeeHistory($partnerAdmin, $feeName, $txn);
					if ($ufh) {
						$comment = $ufh->getComment();
						$strPos = strpos($comment, '. Amount: USD $');
						$newComment = substr($comment, 0, $strPos);
						$amountStr = '. Amount: ' . Money::formatUSD($new);
						$newComment .= $amountStr;
						$ufh->setAmount($new)
							->setComment($newComment);
						Util::persist($ufh);
					}
				}
			}
		}

		Util::flush();

		$overageFee = $oldFees - $newFees;

		if ($handleType === 'test') {
			return new SuccessResponse([
				'merchant id' => $merchantId,
				'old fee' => $oldFees,
				'new fee' => $newFees,
				'overage fee' => $overageFee
			]);
		}

		if ($overageFee <= 0) {
			return new FailedResponse('No overage fees.');
		}

		if ($handleType !== 'execute') {
			return new FailedResponse('Invalid handle type.');
		}

		$merchantDummy = UserService::getDummyCard($merchantAdmin);

		$comment = sprintf(
			"Correct data. %s. Due to the reduction of the fee, the Partner's (%s) overpayment was returned from the Merchant balance (%s. Merchant ID: %s) at %s. Amount: %s.",
			$feeName,
			$partnerAdmin->getSignature(),
			$merchantAdmin->getSignature(),
			$merchantId,
			Carbon::now(),
			Money::format($overageFee, 'USD')
		);
		$partnerDummy->updatePrivacyBalanceBy(
			$overageFee,
			$feeName,
			$comment,
			false,
			null
		);
		$merchantDummy->updatePrivacyBalanceBy(
			-$overageFee,
			$feeName,
			$comment,
			false,
			null
		);
		SlackService::eyes($comment);

		return new SuccessResponse();
	}

	/**
	 * @Route("/dev/spendr/correct/queue-error/partner-balance")
	 *
	 * @return FailedResponse|SuccessResponse
	 */
	public function correctPartnerBalanceByQueueError()
	{
		Queue::spendrCorrectData('correctPartnerBalanceByQueueError');
		return new SuccessResponse();
	}

	/**
	 * @Route("/dev/spendr/correct/queue-error/consumer-return-fee/{loadId}/{type}/{amount}")
	 *
	 * @param $type
	 * @param $loadId
	 * @param $amount
	 * @return FailedResponse|SuccessResponse
	 */
	public function correctConsumerReturnFeeByQueueError($loadId, $type, $amount)
	{
		Queue::spendrCorrectConsumerReturnFee(
			'correctConsumerReturnFeeByQueueError',
			$loadId,
			$type,
			$amount
		);
		return new SuccessResponse();
	}

	/**
	 * https://app.asana.com/0/1200410643730426/1204215971181882/f
	 *
	 * @Route("/dev/spendr/correct/queue-error/received-prefund-load/{loadId}")
	 *
	 * @param $loadId
	 * @return FailedResponse|SuccessResponse
	 */
	public function correctReceivedPrefundLoadByQueueError($loadId)
	{
		if (Util::isLive()) {
			$ids = [521927, 525036, 525716, 530622, 533511, 534009, 534074];
		} else if (Util::isStaging()) {
			// for test
			$ids = [2578];
		} else {
			return new FailedResponse();
		}

		if (!in_array($loadId, $ids)) {
			return new FailedResponse('Invalid load ID.');
		}

		Queue::spendrLoad($loadId);
		return new SuccessResponse();
	}

	/**
	 * https://app.asana.com/0/1200410643730426/1204270520158244/f
	 *
	 * @Route("/dev/spendr/correct/normal-error/exec-spendr-txn")
	 *
	 * @return FailedResponse|SuccessResponse
	 */
	public function correctExecSpendrTxnByNormalError()
	{
//		ImportTransactionsService::handleLoadRows();
		ImportTransactionsService::handlePartnerLoadRows();
		return new SuccessResponse();
	}
}
