<?php

namespace DevBundle\Controller\Faas;

use Carbon\Carbon;
use CoreBundle\Entity\ExternalInvoke;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserGroup;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Util;
use DevBundle\Controller\BaseController;
use FaasBundle\Services\BOTM\BotmAPI;
use FaasBundle\Services\BOTM\BotmService;
use SalexUserBundle\Entity\User;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use TransferMexBundle\Entity\Recipient;
use TransferMexBundle\Services\BotmPayoutService;
use TransferMexBundle\Services\BotmSettleService;
use TransferMexBundle\Services\IntermexRemittanceService;

class BotmController extends BaseController
{
    #[Route("/dev/faas/botm/clear-profile")]
    public function getProfile()
    {
        $botm = new BotmAPI();
        $botm->clearProfile();
        return new SuccessResponse();
    }

    #[Route("/dev/faas/botm/businesses")]
    public function listBusiness()
    {
        $botm = new BotmAPI();
        [, $data] = $botm->listBusiness();
        return new SuccessResponse($data);
    }

    #[Route("/dev/faas/botm/business/base")]
    public function ensureBaseBusiness()
    {
        $botm = new BotmAPI();
        $bid = $botm->getBaseBusinessId();
        if ($bid) {
            $accountId = $botm->ensureBusinessAccountId();
            return new SuccessResponse([
                'businessId' => $bid,
                'accountId' => $accountId,
            ], 'Already created before');
        }

        $group = new UserGroup();
        $group->setName('TransferMex Base')
            ->setCreatedAt(new \DateTime());

        $result = ExternalInvoke::host($botm->createBusiness($group));

        $botm->getPlatform()
            ->setBotmBusinessId($result['business_id']);
        Util::flush();

        $accountId = $botm->ensureBusinessAccountId();
        return new SuccessResponse([
            $result,
            $accountId,
        ]);
    }

    #[Route("/dev/faas/botm/business/{group_id}/group")]
    public function ensureBusinessAccountOnGroup($group_id)
    {
        $ug = UserGroup::find($group_id);
        BotmService::ensureUserGroupBusiness($ug);
        return new SuccessResponse([
            'businessId' => $ug->getBotmBusinessId(),
            'businessAccountId' => $ug->getBotmBusinessAccountId(),
        ]);
    }

    #[Route("/dev/faas/botm/business/{group_id}/admin")]
    public function ensureBizAdmin($group_id)
    {
        $ug = UserGroup::find($group_id);
        $admin = $ug->getPrimaryAdmin();
        $api = BotmAPI::getForUserGroup($ug);
        $id = $api->ensureBusinessAdmin($admin);
        return new SuccessResponse($id);
    }

    #[Route("/dev/faas/botm/business/{business_id}/account")]
    public function ensureBusinessAccount($business_id)
    {
        $botm = new BotmAPI(businessId: (int)$business_id);
        $data = $botm->listBusinessAccounts();
        return new SuccessResponse($data);
    }

    #[Route("/dev/faas/botm/business/account/{account_id}")]
    public function getBusinessAccount($account_id)
    {
        $account_id = (int)$account_id;
        if ( ! $account_id) {
            $account_id = (new BotmAPI())->getBaseBusinessAccountId();
        }
        $botm = BotmAPI::getForAgent('botm_' . $account_id);
        $data = $botm->retrieveBusinessAccount($account_id);
        return new SuccessResponse($data);
    }

    #[Route("/dev/faas/botm/business/account/{account_id}/transactions")]
    public function getBusinessAccountTransactions(Request $request, $account_id)
    {
        $account_id = (int)$account_id;
        if ( ! $account_id) {
            $account_id = (new BotmAPI())->getBaseBusinessAccountId();
        }
        $params = $request->query->all();
        $params['start_date'] = $params['start_date'] ?? Carbon::now()->subDays(30)->format('Y-m-d');
        $params['end_date'] = $params['end_date'] ?? Carbon::tomorrow()->format('Y-m-d');
        $botm = BotmAPI::getForAgent('botm_' . $account_id);
        $data = $botm->listBusinessTransactions(...$params);
        return new SuccessResponse($data);
    }

    #[Route("/dev/faas/botm/transfer/internal/{type}/{from_account_id}/{to_account_id}/{amount}")]
    public function internalAccountTransfer(Request $request, $type, $from_account_id, $to_account_id, $amount)
    {
        $comment = $request->get('comment', '');
        if (!$comment) {
            return new FailedResponse('Empty comment!');
        }
        $botm = new BotmAPI();
        $data = $botm->internalAccountTransfer(
            $type,
            $from_account_id,
            $to_account_id,
            $amount,
            $comment,
        );
        return new SuccessResponse($data);
    }

    #[Route("/dev/faas/botm/transfer/balance/{type}/{from_account_id}/{to_account_id}/{amount}")]
    public function transferBalance(Request $request, $type, $from_account_id, $to_account_id, $amount)
    {
        $comment = $request->get('comment', '');
        if (!$comment) {
            return new FailedResponse('Empty comment!');
        }
        $botm = new BotmAPI();
        $data = $botm->transferAccountBalance(
            $type,
            $from_account_id,
            $to_account_id,
            $amount,
            $comment,
            'dev_' . Util::randTimeNumber(),
        );
        return new SuccessResponse($data);
    }

    #[Route("/dev/faas/botm/transfer/move-funds/{fromGroupId}/{toGroupId}/{amount}")]
    public function moveFunds(Request $request, $fromGroupId, $toGroupId, $amount)
    {
        $comment = $request->get('comment', '');
        if (!$comment) {
            return new FailedResponse('Empty comment!');
        }
        $fromGroupId = (int)$fromGroupId;
        $fromGroup = $fromGroupId ? UserGroup::find($fromGroupId) : null;
        $toGroupId = (int)$toGroupId;
        $toGroup = $toGroupId ? UserGroup::find($toGroupId) : null;
        BotmService::moveFunds($amount, $comment, $fromGroup, $toGroup);
        return new SuccessResponse();
    }

    #[Route("/dev/faas/botm/me")]
    public function me()
    {
        $botm = new BotmAPI();
        $data = ExternalInvoke::throwIfFailed($botm->me());
        return new SuccessResponse($data);
    }

    #[Route("/dev/faas/botm/program/accounts")]
    public function programAccounts()
    {
        $botm = new BotmAPI();
        $data = ExternalInvoke::throwIfFailed($botm->listProgramAccounts());
        return new SuccessResponse($data);
    }

    #[Route("/dev/faas/botm/card/transactions")]
    public function cardTransactions(Request $request)
    {
        $params = $request->query->all();
        $botm = new BotmAPI();
        $data = ExternalInvoke::throwIfFailed($botm->listCardTransactions(...$params));
        return new SuccessResponse($data);
    }

    #[Route("/dev/faas/botm/account/{id}")]
    public function account($id)
    {
        $botm = new BotmAPI();
        $data = ExternalInvoke::throwIfFailed($botm->retrieveUserAccountById($id));
        if (!empty($data['user_id'])) {
            $data['_user'] = ExternalInvoke::host(
                $botm->retrieveUserRaw($data['user_id']),
            );
        }
        return new SuccessResponse($data);
    }

    #[Route("/dev/faas/botm/account/{id}/cards")]
    public function accountCards($id)
    {
        $botm = new BotmAPI();
        $data = ExternalInvoke::throwIfFailed($botm->listCardsRaw($id));
        return new SuccessResponse($data);
    }

    #[Route("/dev/faas/botm/account/{id}/transactions/{v1}")]
    public function accountTransactions(Request $request, $id, $v1 = null)
    {
        if (!is_numeric($id)) {
            $id = BotmService::getCachedAccountIdByProxyValue($id);
        }

        $user = new User();
        Util::updateMeta($user, [
            'botmUserAccountId' => $id,
        ], false);

        $uc = new UserCard();
        $uc->setUser($user);

        $others = [];
        if ($v1) {
            $others['v1'] = true;
        }
        if ($request->get('order_direction')) {
            $others['order_direction'] = $request->get('order_direction');
        }

        $botm = new BotmAPI();
        $data = ExternalInvoke::throwIfFailed($botm->consumerTransactions(
            $uc,
            Carbon::now()->subDays(365),
            Carbon::tomorrow(),
            otherParams: $others,
        ));
        return new SuccessResponse($data);
    }

    #[Route("/dev/faas/botm/user/{uid}")]
    public function userById($uid)
    {
        $botm = new BotmAPI();
        $data = ExternalInvoke::throwIfFailed($botm->retrieveUserRaw($uid));
        return new SuccessResponse($data);
    }

    #[Route("/dev/faas/botm/user/{user}/ensure")]
    public function ensureBotmUserAccount(User $user)
    {
        $botm = new BotmAPI();
        $data = ExternalInvoke::host($botm->ensureBotmUserAccountId($user));
        return new SuccessResponse($data);
    }

    #[Route("/dev/faas/botm/user/{user}/create/force")]
    public function createBotmUserAccountForce(User $user)
    {
        $botm = new BotmAPI();
        $data = ExternalInvoke::host($botm->createUser($user, true));
        if (empty($data['user_id'])) {
            return new FailedResponse('Empty user id', $data);
        }
        if (empty($data['user_account_ids'][0])) {
            $data = ExternalInvoke::host($botm->createUserAccount($user, $data['user_id']));
        }
        return new SuccessResponse($data);
    }

    #[Route("/dev/faas/botm/user/{user}/data")]
    public function userData(User $user)
    {
        BotmAPI::getUserDataForBotm($user, true);
        $data['original_email'] = $user->getEmail();
        return new SuccessResponse($data);
    }

    #[Route("/dev/faas/botm/user/{user}/create-card")]
    public function createCard(User $user)
    {
        $uc = $user->getOneCardInPlatform(Platform::transferMex());
        BotmService::createCard($uc);
        return new SuccessResponse();
    }

    #[Route("/dev/faas/botm/user/{user}/create-card/force/{accountId}")]
    public function createCardForce(User $user, $accountId)
    {
        $uc = $user->getOneCardInPlatform(Platform::transferMex());
        $api = new BotmAPI();
        $data = ExternalInvoke::throwIfFailed($api->createCard($uc, accountId: $accountId));
        return new SuccessResponse($data);
    }

    #[Route("/dev/faas/botm/user/{uid}/join-business/{bid}")]
    public function joinBusiness($uid, $bid)
    {
        $api = new BotmAPI();
        $data = $api->joinBusinessRaw($uid, $bid);
        return new SuccessResponse($data);
    }

    #[Route("/dev/faas/botm/card/retrieve-card/{proxy}")]
    public function retrieveCard($proxy)
    {
        $uc = new UserCard();
        $uc->setAccountNumber($proxy);
        $botm = new BotmAPI();
        $data = ExternalInvoke::throwIfFailed($botm->retrieveCard($uc));
        return new SuccessResponse($data);
    }

    #[Route("/dev/faas/botm/card/transaction/{token}")]
    public function cardTransaction($token)
    {
        $botm = new BotmAPI();
        $data = ExternalInvoke::throwIfFailed($botm->retrieveCardTransaction($token));
        return new SuccessResponse($data);
    }

    #[Route("/dev/faas/botm/card/simulate/auth/{proxy}/{amount}")]
    public function simulate(Request $request, $proxy, $amount)
    {
        $descriptor = $request->get('descriptor');
        if (!$descriptor) {
            $descriptor = Util::faker()->userName;
        }

        $uc = new UserCard();
        $uc->setAccountNumber($proxy);
        $botm = new BotmAPI();
        $pan = BotmService::decryptCachedPan($uc);
        $data = ExternalInvoke::throwIfFailed($botm->cardSimulateAuth(
            $amount, $descriptor, $pan));
        return new SuccessResponse($data);
    }

    #[Route("/dev/faas/botm/card/pan/{proxy}")]
    public function cardPan($proxy)
    {
        $uc = new UserCard();
        $uc->setAccountNumber($proxy);
        $api = BotmAPI::getForUserCard($uc);
        $details = ExternalInvoke::host($api->retrieveCardPan($uc));
        return new SuccessResponse($details);
    }

    #[Route("/dev/faas/botm/card/update/{proxy}")]
    public function updateCard(Request $request, $proxy)
    {
        $params = $request->query->all();
        $uc = new UserCard();
        $api = BotmAPI::getForUserCard($uc);
        $api->proxyValue = $proxy;
        $details = ExternalInvoke::host($api->updateCard($uc, $params));
        return new SuccessResponse($details);
    }

    #[Route("/dev/faas/botm/card/assign/{proxy}/{account_id}")]
    public function cardAssign($proxy, $account_id)
    {
        $uc = new UserCard();
        $uc->setAccountNumber($proxy);
        $api = BotmAPI::getForUserCard($uc);
        $details = ExternalInvoke::host($api->assignCardRaw($proxy, $account_id));
        return new SuccessResponse($details);
    }

    #[Route("/dev/faas/botm/user/{user}/list-cards")]
    public function listCards(User $user)
    {
        $botm = new BotmAPI();
        $data = ExternalInvoke::throwIfFailed($botm->listCards($user));
        return new SuccessResponse($data);
    }

    #[Route("/dev/faas/botm/user/list/search")]
    public function listUsers(Request $request)
    {
        $params = [
            'pageSize' => 100,
        ];
        foreach (['first_name', 'last_name', 'phone', 'email', 'full_text_search_term', 'pageSize'] as $field) {
            if ($request->query->has($field)) {
                $params[$field] = $request->get($field);
            }
        }

        $botm = new BotmAPI();
        $data = ExternalInvoke::throwIfFailed($botm->listUsers($params));
        return new SuccessResponse($data);
    }

    #[Route("/dev/faas/botm/card/create-bulk-blank/{count}")]
    public function createBlankCards($count)
    {
        $botm = new BotmAPI();
        $data = ExternalInvoke::throwIfFailed($botm->createBlankCards($count));
        return new SuccessResponse($data);
    }

    #[Route("/dev/faas/botm/card/list-blanks")]
    public function listBlankCards()
    {
        $botm = new BotmAPI();
        $data = ExternalInvoke::throwIfFailed($botm->listBlankCards());
        return new SuccessResponse($data);
    }

    #[Route("/dev/faas/botm/card/list")]
    public function listAllCards(Request $request)
    {
        $botm = new BotmAPI();
        $data = ExternalInvoke::throwIfFailed($botm->listCards(
            null,
            $request->get('pageSize', 100),
            $request->get('page', 1),
        ));
        return new SuccessResponse($data);
    }

    #[Route("/dev/faas/botm/card/hash-account-number/{number}")]
    public function hashAccountNumber(Request $request, $number)
    {
        $hash = hash_hmac('sha256', $number, Util::getConfigKey('botm_account_number_hash'));

        $users = $this->em->getRepository(UserCard::class)
            ->createQueryBuilder('uc')
            ->where('uc.nickName = :name')
            ->setParameter('name', $hash)
            ->select('IDENTITY(uc.user) uid')
            ->getQuery()
            ->getArrayResult();

        return new SuccessResponse(compact('hash', 'users'));
    }

    #[Route("/dev/faas/botm/ach/generate")]
    public function generateAch()
    {
        $generated = BotmSettleService::generateNachaAndSendToBank(sendEmail: false);
        BotmSettleService::verifyAndAlertNachaFileGeneration($generated);
        return new SuccessResponse($generated);
    }

    #[Route("/dev/faas/botm/webhooks")]
    public function getWebhooks()
    {
        $api = new BotmAPI();
        $data = ExternalInvoke::host($api->getWebhooks());
        return new SuccessResponse($data);
    }

    #[Route("/dev/faas/botm/webhook/subscriptions")]
    public function getWebhookSubscriptions()
    {
        $api = new BotmAPI();
        $data = ExternalInvoke::host($api->getWebhookSubscriptions());
        return new SuccessResponse($data);
    }

    #[Route("/dev/faas/botm/transfer/program")]
    public function getTransferProgram()
    {
        $api = new BotmAPI();
        $data = ExternalInvoke::host($api->getTransferProgram());
        return new SuccessResponse($data);
    }

    #[Route("/dev/faas/botm/transfer/rates/{method}/{country}/{sell}/{buy}")]
    public function getTransferRates(string $method, string $country = 'MX', string $sell = 'USD', string $buy = 'MXN')
    {
        $botm = new BotmAPI();
        $data = $botm->getTransferRate(
            $method, $country,
            $sell, $buy,
        );
        return new SuccessResponse($data);
    }

    #[Route("/dev/faas/botm/transfer/cached-rates/{type}/{force}/{country}/{sell}/{buy}")]
    public function getTransferRatesCached(
        string $type, string $force = 'false',
        string $country = 'MX', string $sell = 'USD', string $buy = 'MXN',
    ) {
        $force = $force === 'true';
        $data = BotmPayoutService::getDailyRate(
            $type, $force, $country, $sell, $buy
        );
        return new SuccessResponse($data);
    }

    #[Route("/dev/faas/botm/recipient/{user}")]
    public function ensureRecipient(User $user)
    {
        $recipient = $user->ensureTransferMexRecipient();
        $id = BotmPayoutService::ensureBotmRecipientId($recipient);
        return new SuccessResponse([
            'id' => $id,
            'data' => Util::meta($recipient, 'botmRecipient'),
        ]);
    }

    #[Route("/dev/faas/botm/wire/create/{user}/{amount}")]
    public function createWire(Request $request, User $user, int $amount)
    {
        $recipient = $user->ensureTransferMexRecipient();
        $api = new BotmAPI();
        $data = $api->createWire(
            $recipient, $amount,
            $request->get('message'),
            $request->get('purpose'),
        );
        return new SuccessResponse($data);
    }
    #[Route("/dev/faas/botm/wire/confirm/{transactionId}")]
    public function confirmWire(Request $request, string $transactionId)
    {
        $api = new BotmAPI();
        $data = $api->confirmWire(
            $transactionId
        );
        return new SuccessResponse($data);
    }

    #[Route("/dev/faas/botm/wire/cancel/{transactionId}/{cancelReasonId}")]
    public function cancelWire(Request $request, string $transactionId, string $cancelReasonId)
    {
        $api = new BotmAPI();
        $data = $api->cancelWire(
            $transactionId,
            $cancelReasonId
        );
        return new SuccessResponse($data);
    }

    #[Route("/dev/faas/botm/wire/release/{transactionId}/{pinNumber}")]
    public function releaseWire(Request $request, string $transactionId, string $pinNumber)
    {
        $api = new BotmAPI();
        $data = $api->releaseWire(
            $transactionId,
            $pinNumber
        );
        return new SuccessResponse($data);
    }

    #[Route("/dev/faas/botm/wire/status/{transactionId}")]
    public function getWireStatus(Request $request, string $transactionId, string $cancelReasonId)
    {
        $api = new BotmAPI();
        $data = $api->getWireStatus(
            $transactionId
        );
        return new SuccessResponse($data);
    }

    #[Route("/dev/faas/botm/wire/geexchange")]
    public function getExchange(Request $request)
    {
        $data = []; // IntermexRemittanceService::getPayoutOptions();
        return new SuccessResponse($data);
    }
}
