<?php


namespace DevB<PERSON>le\Controller\TransferMex;


use CoreB<PERSON>le\Entity\ExternalInvoke;
use CoreBundle\Entity\Country;
use CoreBundle\Exception\FailedException;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Util;
use DevBundle\Controller\BaseController;
use PortalBundle\Exception\PortalException;
use SalexUserBundle\Entity\User;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use TransferMexBundle\Services\Tern\TernAPI;
use CoreBundle\Utils\Data;
use Carbon\Carbon;
use CoreBundle\Utils\Log;
use TransferMexBundle\TransferMexBundle;

class TernController extends BaseController
{
    /**
     * @Route("/dev/mex/tern/getToken")
     * @param string $env
     *
     * @return FailedException|SuccessResponse
     */
    public function getClientToken($env = null)
    {
        Util::longerRequest();
        $service = new TernAPI();

        /** @var ExternalInvoke $ei */
        $token = $service->getClientToken();

        return new SuccessResponse($token);
    }

     /**
     * @Route("/dev/mex/tern/getOrganizations")
     *
     * @return FailedException|SuccessResponse
     */
    public function getOrganizations()
    {
        Util::longerRequest();
        $service = new TernAPI();

        /** @var ExternalInvoke $ei */
        [$ei, $res] = $service->getOrganizations();
      
        return new SuccessResponse($res);
    }
   
}
