<?php


namespace DevBundle\Controller\TransferMex;


use CoreB<PERSON>le\Entity\ExternalInvoke;
use CoreBundle\Entity\Country;
use CoreBundle\Exception\FailedException;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Util;
use DevBundle\Controller\BaseController;
use PortalBundle\Exception\PortalException;
use SalexUserBundle\Entity\User;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use TransferMexBundle\Services\UniTellerAPI;
use TransferMexBundle\Services\RapydDisburseService;
use TransferMexBundle\Services\UniTellerRemittanceService;
use CoreBundle\Utils\Data;
use Carbon\Carbon;
use CoreBundle\Utils\Log;
use TransferMexBundle\TransferMexBundle;

class UniTellerController extends BaseController
{
    /**
     * @Route("/dev/mex/uniteller/getToken/{env}", defaults={"env"=null})
     * @param string $env
     *
     * @return FailedException|SuccessResponse
     */
    public function getClientToken($env = null)
    {
        Util::longerRequest();
        $service = new UniTellerAPI($env);

        /** @var ExternalInvoke $ei */
        $token = $service->getClientToken();
        $date = Carbon::create(2020, 06, 20, 03, 52,29, 'EST');
        $res = Util::toUTC(Carbon::instance($date->setTimezone(Util::tzNewYork())));

        return new SuccessResponse($token);
    }

    /**
     * @Route("/dev/mex/uniteller/getUserToken/{user}")
     * @param User $user
     *
     * @return FailedException|SuccessResponse
     */
    public function getUserToken(User $user)
    {
        Util::longerRequest();
        $service = new UniTellerAPI();

        /** @var ExternalInvoke $ei */
        $token = $service->getUserToken($user);

        return new SuccessResponse($token);
    }



    /**
     * @Route("/dev/mex/uniteller/getSecurityQuestions/{env}", defaults={"env"=null})
     * @param string $env
     *
     * @return FailedException|SuccessResponse
     */
    public function getClientSecurityQuestions($env = null)
    {
        Util::longerRequest();
        /** @var ExternalInvoke $ei */
        $result = UniTellerRemittanceService::securityQuestions();

        return new SuccessResponse($result);
    }

    /**
     * @Route("/dev/mex/uniteller/getDestCountryWithCurrency/{env}", defaults={"env"=null})
     * @param string $env
     *
     * @return FailedException|SuccessResponse
     */
    public function getDestCountryWithCurrency($env = null)
    {
        Util::longerRequest();
        $service = new UniTellerAPI($env);

        /** @var ExternalInvoke $ei */
        $params = [
          'partnerCode' => Util::getConfigKey('uniteller_partner_code'),
        ];
        [$ei, $result] = $service->getDestCountryWithCurrency($params);

        if ($ei->isFailed()) {
          throw PortalException::temp($ei->getError());
        }
        return new SuccessResponse($result);
    }

    /**
     * @Route("/dev/mex/uniteller/checkDupllcateUser/{user}", defaults={"env"=null})
     * @param User $user
     *
     * @return FailedException|SuccessResponse
     */
    public function checkDupllcateUser(User $user)
    {
        Util::longerRequest();

        /** @var ExternalInvoke $ei */
        [$ei, $result] = UniTellerRemittanceService::checkDupllcateUser($user);

        return new SuccessResponse($result);
    }

    /**
     * @Route("/dev/mex/uniteller/registerUser/{user}", defaults={"env"=null})
     * @param User $user
     *
     * @return FailedException|SuccessResponse
     */
    public function registerUser(User $user)
    {
        Util::longerRequest();

        /** @var ExternalInvoke $ei */
        [$ei, $result] = UniTellerRemittanceService::registerUniTeller($user, [
          'id' => 1,
          'answer' => 'test'
        ]);

        return new SuccessResponse($result);
    }

    /**
     * @Route("/dev/mex/uniteller/getCountryState/{user}", defaults={"env"=null})
     * @param User $user
     *
     * @return FailedException|SuccessResponse
     */

    public function getCountryState(User $user)
    {
        Util::longerRequest();
        $service = new UniTellerAPI();

        /** @var ExternalInvoke $ei */
        [$ei, $result] = $service->getCountryState($user->getCountry());

        return new SuccessResponse($result);
    }

     /**
     * @Route("/dev/mex/uniteller/getStates", defaults={"env"=null})
     *
     * @return FailedException|SuccessResponse
     */

    public function getStates()
    {
        Util::longerRequest();
        $service = new UniTellerAPI();

        /** @var ExternalInvoke $ei */
        [$ei, $result] = $service->getStateList();

        return new SuccessResponse($result);
    }

     /**
     * @Route("/dev/mex/uniteller/getRate", defaults={"env"=null})
     *
     * @return FailedException|SuccessResponse
     */

    public function getRate(Request $request)
    {
        Util::longerRequest();
        $country = Country::find($request->get('countryId'));
        /** @var ExternalInvoke $ei */
        $result = UniTellerRemittanceService::getDailyRate(true, $country ? $country->getIsoCode() : 'MX', $country ? $country->getCurrency() : 'MXN');

        return new SuccessResponse($result);
    }

     /**
     * @Route("/dev/mex/uniteller/getUser/{user}", defaults={"env"=null})
     * @param User $user
     *
     * @return FailedException|SuccessResponse
     */

    public function getUserInfo(User $user)
    {
        Util::longerRequest();
        $service = new UniTellerAPI();
        /** @var ExternalInvoke $ei */
        $params = [
          'partnerCode' => Util::getConfigKey('uniteller_partner_code'),
          'userId' => $user->getEmail()
        ];
        [$ei, $result] = $service->getProfile($params, $user);

        return new SuccessResponse($result);
    }

     /**
     * @Route("/dev/mex/uniteller/getPayerWithReceptionMethodsList/{user}", defaults={"env"=null})
     * @param User $user
     *
     * @return FailedException|SuccessResponse
     */
    public function getPayerWithReceptionMethodsList(User $user)
    {
        Util::longerRequest();
        $service = new UniTellerAPI();

        /** @var ExternalInvoke $ei */
        $params = [
          'partnerCode' => Util::getConfigKey('uniteller_partner_code'),
          'countryISOCode' => $user->getCountry() ? $user->getCountry()->getIsoCode() : 'MX',
          'currencyISOCode' => $user->getCountry() ? $user->getCountry()->getCurrency() : 'MXN',
          'stateISOCode' =>  $user->getState() ? $user->getState()->getAbbr3() : ''
        ];
        [$ei, $result] = $service->getPayerWithReceptionMethodsList($params, $user);

        $list = [];
        $bankList = [];
        $cashPickList = [];
        if ($result['payerWithReceptionMethods'] && count($result['payerWithReceptionMethods'])) {
          foreach ($result['payerWithReceptionMethods'] as $payer) {
            $payerItem = [
              'id' => $payer['payer']['id'],
              'label' => $payer['payer']['name'],
              'value' => $payer['payer']['payerSpecificCode'],
              'min' => 0,
              'addtionalField' => [],
              'max' => TransferMexBundle::getMaxUniTellerTransferAmount(),
              'beneAccountRegex' => $payer['payer']['beneAccountRegex'],
              'accountRegexMsg' => $payer['payer']['accountRegexMsg']
            ];
            foreach ($payer['receptionMethods'] as $item) {
              if ($item['isAddtionalFieldRequired'] == 'YES') {
                $params = [
                  'partnerCode' => Util::getConfigKey('uniteller_partner_code'),
                  'payerSpecificCode' => $payerItem['value'],
                  'currencyISOCode' => $user->getCountry() ? $user->getCountry()->getCurrency() : 'MXN',
                  'receptionMethodName' =>  $item['name']
                ];
                [$ei, $res] = $service->getPayerAdditionalFields($params);
                $additionalFields = [];
                if (!$ei->isFailed()) {
                  foreach ($res['additionalFieldInfo'] as $additionalField) {
                    $additionalFields[] = [
                      'label' => $additionalField['fieldLabel'],
                      'name'  => $additionalField['fieldName'],
                      'type'  => $additionalField['fieldType'],
                      'options' => $additionalField['fieldOptions'],
                    ];
                  }
                }
                $payerItem['additionalFields'] = $additionalFields;
              }
              $payerItem['max'] = $item['txnLimit'];

              if ($item['name'] === 'Cash Pickup') {
                Log::debug('max limit for the payer: ' .  $payerItem['label']);
                $payerItem['min'] = UniTellerRemittanceService::getMinAmountByType($payerItem['value'], 'cash');
                $cashPickList[] = $payerItem;
              } else if ($item['name'] === 'Account Credit') {
                $payerItem['min'] = UniTellerRemittanceService::getMinAmountByType($payerItem['value'], 'bank');
                $bankList[] = $payerItem;
                $key = 'uniteller_account_validate_' . $payerItem['value'];
                $errorKey = 'uniteller_account_validate_error_' . $payerItem['value'];
                if ($payerItem['beneAccountRegex'] && !Data::get($key)) {
                  Data::set($key, $payerItem['beneAccountRegex']);
                  Data::set($errorKey, $payerItem['accountRegexMsg']);
                }
              }
            }
          }
        }
        $list['bankUniTeller'] = $bankList;
        $list['cashUniTeller'] = $cashPickList;
        $list['payer'] = $result['payerWithReceptionMethods'];
        // Data::setArray('transfermex_unitiller_reception_methods', $list);

        return new SuccessResponse($list);
    }

      /**
     * @Route("/dev/mex/uniteller/AddBeneficiary/{user}", defaults={"env"=null})
     * @param User $user
     *
     * @return FailedException|SuccessResponse
     */
    public function AddBeneficiary(User $user)
    {
        Util::longerRequest();
        $service = new UniTellerAPI();

        $param = [];
        /** @var ExternalInvoke $ei */
        [$ei, $result] = $service->AddBeneficiary($param,$user);

        return new SuccessResponse($result);
    }

     /**
     * @Route("/dev/mex/uniteller/getSendingMethodSummaryList/{user}", defaults={"env"=null})
     * @param User $user
     *
     * @return FailedException|SuccessResponse
     */

    public function getSendingMethodSummaryList(User $user)
    {
        Util::longerRequest();

        /** @var ExternalInvoke $ei */
        $result = UniTellerRemittanceService::getSendingMethodSummaryList($user);

        return new SuccessResponse($result);
    }

    /**
     * @Route("/dev/mex/uniteller/getUserProfileInfo/{user}", defaults={"env"=null})
     * @param User $user
     *
     * @return FailedException|SuccessResponse
     */

    public function getUserProfileInfo(User $user)
    {
        Util::longerRequest();

        /** @var ExternalInvoke $ei */
        [$ei, $result] = UniTellerRemittanceService::getUserProfileInfo($user);

        return new SuccessResponse($result);
    }

    /**
     * @Route("/dev/mex/uniteller/getStateDisclaimer/{user}", defaults={"env"=null})
     * @param User $user
     *
     * @return FailedException|SuccessResponse
     */

    public function getStateDisclaimer(User $user)
    {
        Util::longerRequest();

        /** @var ExternalInvoke $ei */
        [$ei, $result] = UniTellerRemittanceService::getStateDisclaimer($user);

        return new SuccessResponse($result);
    }

     /**
     * @Route("/dev/mex/uniteller/getTransactionSummaryList/{user}", defaults={"env"=null})
     * @param User $user
     *
     * @return FailedException|SuccessResponse
     */

    public function getTransactionSummaryList(User $user)
    {
        Util::longerRequest();

        /** @var ExternalInvoke $ei */
        [$ei, $result] = UniTellerRemittanceService::getTransactionSummaryList($user);

        return new SuccessResponse($result);
    }

    /**
     * @Route("/dev/mex/uniteller/getTransactionList", defaults={"env"=null})
     *
     * @return FailedException|SuccessResponse
     */

    public function getTransactionList()
    {
        Util::longerRequest();

        /** @var ExternalInvoke $ei */
        [$ei, $result] = UniTellerRemittanceService::getAllTransactions();
        return new SuccessResponse($result);
    }

     /**
     * @Route("/dev/mex/uniteller/getPayerBranch", defaults={"env"=null})
     *
     * @return FailedException|SuccessResponse
     */

    public function getPayBranchList(User $user)
    {
        Util::longerRequest();
        $payer = [
          'payerSpecificCode' => 'WMTSUP99',
          'countryISOCode' => $user->getCountry() ? $user->getCountry()->getIsoCode() : 'MX',
          'currencyISOCode' => $user->getCountry() ? $user->getCountry()->getCurrency() : 'MXN',
          'stateISOCode' =>  $user->getState() ? $user->getState()->getAbbr3() : ''
        ];
        $type = 'Cash Pickup';
        /** @var ExternalInvoke $ei */
        [$ei, $result] = UniTellerRemittanceService::getPayBranchList($payer, $type);
        return new SuccessResponse($result);
    }


}
