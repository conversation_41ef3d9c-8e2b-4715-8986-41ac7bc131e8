<?php

namespace UsUnlockedBundle\Command;

use Carbon\Carbon;
use CoreBundle\Command\BaseHostedCommand;
use Core<PERSON>undle\Entity\ExternalInvoke;
use CoreBundle\Entity\LoadPartner;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Utils\Util;
use Symfony\Component\Console\Input\InputOption;
use UsUnlockedBundle\Services\Coinflow\CoinflowAPI;
use UsUnlockedBundle\Services\Coinflow\CoinflowQueue;
use UsUnlockedBundle\Services\Coinflow\CoinflowService;
use UsUnlockedBundle\Services\SlackService;

class SyncCoinflowPaymentsCommand extends BaseHostedCommand
{
    protected ?int $theUclId = null;
    protected bool $checkOnly = false;
    protected int $interval = 0;

    protected function configure()
    {
        $this
            ->setName('span:usu:coinflow:sync-payments')
            ->setDescription('Sync Coinflow payments')
            ->addOption('ucl', null, InputOption::VALUE_REQUIRED, 'The ucl ID to check')
            ->addOption('hours', null, InputOption::VALUE_REQUIRED, 'Number of hours to sync before `from` (or now)')
            ->addOption('check', null, InputOption::VALUE_NONE, 'Check only, do not update in queue')
            ->addOption('from', null, InputOption::VALUE_REQUIRED, 'The datetime to check from. Format: ISO. Default to now')
            ->addOption('method', null, InputOption::VALUE_REQUIRED, 'The method to call instead')
        ;
    }

    public function getSingletonKey()
    {
        return implode('_', [
            $this->getName(),
            $this->input->getOption('ucl'),
        ]);
    }

    protected function hostedExecute()
    {
        $method = $this->input->getOption('method');
        if ($method) {
            return $this->$method();
        }

        $this->checkOnly = $this->input->getOption('check');

        $theUcl = $this->input->getOption('ucl');
        if ($theUcl) {
            $this->theUclId = (int)$theUcl;
        }

        $hours = $this->input->getOption('hours');
        if ($hours === null) {
            $current = Carbon::now()->hour;
            $hours = $current === 2 ? 48 : 8;
        }

        $from = $this->input->getOption('from');
        if ($from) {
            $until = Carbon::parse($from);
        } else {
            $until = Carbon::now();
        }

        $since = $until->copy()->subHours($hours);
        if (!$from) {
            $until->endOfDay();
        }

        if (!$this->theUclId) {
            $this->interval = $hours >= 24 ? 1500 : 100;
        }

        $this->line('Start to sync payments since ' . $since->format('c') . ' to ' . $until->format('c'));

        $total = 0;
        $filled = 0;
        $page = 1;
        while ($page < 100) { // at most 100 pages
            /** @var ExternalInvoke $ei */
            [$ei, $data] = CoinflowAPI::getAllPayments($since, $until, page: $page);
            if ($ei->isFailed()) {
                $this->line('Failed on page ' . $page . ': ' . $ei->getError());
                break;
            }
            if (empty($data)) {
                break;
            }
            $count = count($data);
            $total += $count;
            $this->line('Found ' . $count . ' payments on page ' . $page);
            $filled += $this->processList($data);
            unset($data);
            $page++;
            Util::usleep($this->interval);
        }

        if ($filled) {
            SlackService::clock('Filled `' . $filled . '` new payment IDs from Coinflow', [
                'since' => $since->format(Util::DATE_TIME_FORMAT),
                'total' => $total,
            ]);
        }

        if ($hours >= 24 && !$this->checkOnly && !$this->theUclId) {
            $this->expirePayments();
        }

        return 0;
    }

    protected function expirePayments()
    {
        $rs = Util::em()->getRepository(UserCardLoad::class)
            ->createQueryBuilder('ucl')
            ->where('ucl.partner = :partner')
            ->andWhere('ucl.initializedAt is not null')
            ->andWhere('ucl.initializedAt < :earliest')
            ->andWhere('ucl.paymentId is null')
            ->andWhere('ucl.loadStatus in (:statuses)')
            ->setParameter('partner', LoadPartner::coinflow())
            ->setParameter('earliest', Carbon::now()->subMinutes(CoinflowService::EXPIRY_PAYMENT_MINUTES))
            ->setParameter('statuses', [
                UserCardLoad::LOAD_STATUS_INITIATED,
                UserCardLoad::LOAD_STATUS_PENDING,
            ])
            ->getQuery()
            ->getResult();
        $count = count($rs);
        $this->line('Found ' . $count . ' expired Coinflow transactions');

        /**
         * @var int $i
         * @var UserCardLoad $r
         */
        foreach ($rs as $i => $r) {
            CoinflowService::expirePayment($r);
            $this->line($i . '/' . $count . ': Expire Coinflow payment ' . $r->getId() . ' to ' . $r->getLoadStatus());
            Util::usleep($this->interval);
        }
    }

    protected function processList(array &$list)
    {
        $filled = 0;
        foreach ($list as &$item) {
            if (empty($item['webhookInfo']['example']) || empty($item['paymentId'])) {
                continue;
            }
            $w = Util::s2j($item['webhookInfo']['example']);
            if (empty($w['ucl'])) {
                continue;
            }
            if ($this->theUclId && (int)$w['ucl'] !== $this->theUclId) {
                continue;
            }
            $ucl = UserCardLoad::find($w['ucl']);
            if (!$ucl) {
                $this->line('Unknown load ID ' . $w['ucl'] . ' for payment ' . $item['paymentId'], 'comment');
                continue;
            }
            if (!$this->theUclId && self::hasSingletonRunning(implode('_', [
                $this->getName(),
                $w['ucl'],
            ]))) {
                $this->line('Skip load ' . $w['ucl'] . ' because it is already being processed in another singleton command',
                    'comment');
                continue;
            }

            $oldPaymentId = $ucl->getPaymentId();
            if ( ! $oldPaymentId) {
                CoinflowService::assignPaymentIdWithCheck($ucl, $item['paymentId']);
                $this->line('Filled payment ID ' . $item['paymentId'] . ' for load ' . $w['ucl']);
                $filled++;
            } else if ($oldPaymentId !== $item['paymentId']) {
                $status = null;
                foreach (CoinflowAPI::PAYMENT_INFO_FIELDS as $mk) {
                    $key = $mk . 'Info';
                    $status = $item[$key]['status'] ?? $status;
                    if ($status !== null) {
                        $status = strtolower($status);
                        break;
                    }
                }
                if (in_array($status, [
                    CoinflowAPI::STATUS_DEPOSITED,
                    CoinflowAPI::STATUS_SETTLED,
                    CoinflowAPI::STATUS_REFUNDED,
                ])) {
                    $assigned = CoinflowService::assignPaymentIdWithCheck($ucl, $item['paymentId']);
                    if ($assigned) {
                        $this->line('Overwrite payment ID from ' . $oldPaymentId . ' to ' . $item['paymentId'] .
                                    'for load ' . $w['ucl'] . ' with status ' . $status, 'comment');
                        $filled++;
                    } else {
                        $this->line('Skip payment ID from ' . $oldPaymentId . ' to ' . $item['paymentId'] .
                                    'for load ' . $w['ucl'] . ' with status ' . $status, 'comment');
                    }
                }
            }

            if ( ! $this->checkOnly) {
                CoinflowQueue::updatePayment($ucl);
                Util::usleep($this->interval);
            }
        }
        unset($item);
        return $filled;
    }
}
