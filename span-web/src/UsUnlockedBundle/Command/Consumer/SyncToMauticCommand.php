<?php

namespace UsUnlockedBundle\Command\Consumer;

use CoreB<PERSON>le\Command\BaseHostedCommand;
use CoreBundle\Entity\ExternalInvoke;
use CoreBundle\Entity\Platform;
use CoreBundle\Services\PostmarkService;
use CoreBundle\Utils\Util;
use Postmark\PostmarkClient;
use SalexUserBundle\Entity\User;
use SalexUserBundle\Entity\UserConfig;
use Symfony\Component\Console\Input\InputOption;
use UsUnlockedBundle\Services\Mautic\InviteService;
use UsUnlockedBundle\Services\Mautic\MauticService;
use UsUnlockedBundle\Services\SlackService;

class SyncToMauticCommand extends BaseHostedCommand
{
    protected int $offset = 0;
    protected int $interval = 0;
    protected ?int $limit = null;
    protected bool $dry = false;
    protected bool $force = false;

    protected function configure()
    {
        $this
            ->setName('span:usu:user:mautic')
            ->setDescription('Sync users to mautic')
            ->addOption('wave', null, InputOption::VALUE_REQUIRED, 'wave number from 1. omit to sync all')
            ->addOption('offset', null, InputOption::VALUE_REQUIRED, 'from index', 0)
            ->addOption('limit', null, InputOption::VALUE_REQUIRED, 'total count')
            ->addOption('interval', null, InputOption::VALUE_REQUIRED, 'interval (millisecond) between each sync', 100)
            ->addOption('force', null, InputOption::VALUE_NONE, 'force mode to ignore hash')
            ->addOption('dry', null, InputOption::VALUE_NONE, 'dry run mode, do not actually sync')
            ->addOption('method', null, InputOption::VALUE_REQUIRED, 'the method to call')
        ;
        $this->currentPlatform = Platform::ternCommerce();
    }

    protected function hostedExecute()
    {
        $this->interval = (int)$this->input->getOption('interval');
        $this->offset = (int)$this->input->getOption('offset');
        $this->limit = $this->input->getOption('limit');
        if ($this->input->getOption('force')) {
            $this->force = true;
            $this->line('Force mode enabled, will ignore existing data hash.');
        }
        if ($this->input->getOption('dry')) {
            $this->dry = true;
            $this->line('Dry run mode enabled, no actual sync will be performed.');
        }

        $method = $this->input->getOption('method');
        if ($method) {
            $this->$method();
            return;
        }

        $wave = $this->input->getOption('wave');
        if (!$wave) {
            $this->line('Syncing all users to mautic...');
            $this->syncAllUsers();
        } else {
            $this->line("Syncing users from wave $wave to mautic...");
            $this->syncWaveUsers($wave);
        }
    }

    protected function syncUnsubscribedUsers()
    {
        $total = 0;
        $new = 0;
        $limit = 1000;
        for ($page = 0; $page < 200; $page++) {
            $this->line('========== Querying page ' . $page . '...');
            $resp = MauticService::listContacts($page * $limit, $limit, search: 'segment:unsubscribed-contacts', others: [
                'orderBy' => 'date_identified',
                'orderByDir' => 'desc',
            ]);
            $data = ExternalInvoke::host($resp);
            if (empty($data['contacts'])) {
                $this->line('No more unsubscribed users found.');
                break;
            }
            $this->line('========== Processing page ' . $page . ', ' . count($data['contacts']) . ' contacts...');
            foreach ($data['contacts'] as $contact) {
                $uid = $contact['fields']['all']['span_id'] ?? null;
                $user = User::find($uid);
                if (!$user) {
                    $this->line('User with ID ' . $uid . ' not found, skipping.', 'comment');
                    continue;
                }
                $total++;
                try {
                    $updated = MauticService::updateContactForUnsubscribe($user);
                    if ($updated) {
                        $new++;
                        $this->line('Unsubscribed user ' . $user->getId());
                    }
                } catch (\Throwable $e) {
                    $this->line('Failed to unsubscribe user ' . $user->getId() . ': ' . $e->getMessage(), 'error');
                }
            }
        }
        SlackService::wave('Synced unsubscribed users from Mautic', [
            'total' => $total,
            'new' => $new,
        ]);
    }

    protected function syncEmailBouncedUsers()
    {
        $total = 0;
        $new = 0;
        $limit = 1000;
        for ($page = 0; $page < 200; $page++) {
            $this->line('========== Querying page ' . $page . '...');
            $resp = MauticService::listContacts($page * $limit, $limit, search: 'segment:email-bounced-contacts', others: [
                'orderBy' => 'date_identified',
                'orderByDir' => 'desc',
            ]);
            $data = ExternalInvoke::host($resp);
            if (empty($data['contacts'])) {
                $this->line('No more email bounced users found.');
                break;
            }
            $this->line('========== Processing page ' . $page . ', ' . count($data['contacts']) . ' contacts...');
            foreach ($data['contacts'] as $contact) {
                $uid = $contact['fields']['all']['span_id'] ?? null;
                $user = User::find($uid);
                if (!$user) {
                    $this->line('User with ID ' . $uid . ' not found, skipping.', 'comment');
                    continue;
                }
                $total++;
                try {
                    $updated = MauticService::updateContactForBounce($user);
                    if ($updated) {
                        $new++;
                        $this->line('Email bounced user ' . $user->getId());
                    }
                } catch (\Throwable $e) {
                    $this->line('Failed to mark email bounced user ' . $user->getId() . ': ' . $e->getMessage(), 'error');
                }
            }
        }
        SlackService::wave('Synced email bounced users from Mautic', [
            'total' => $total,
            'new' => $new,
        ]);
    }

    protected function fixWave1Users()
    {
        $all = InviteService::getWaveUserIds(1);

        $total = 0;
        $new = 0;
        $limit = 1000;
        for ($page = 0; $page < 100; $page++) {
            $this->line('========== Querying page ' . $page . '...');
            $resp = MauticService::listContacts($page * $limit, $limit, search: 'segment:wave-1-all', others: [
                'orderBy' => 'date_identified',
                'orderByDir' => 'desc',
            ]);
            $data = ExternalInvoke::host($resp);
            if (empty($data['contacts'])) {
                $this->line('No more users found.');
                break;
            }
            $this->line('========== Processing page ' . $page . ', ' . count($data['contacts']) . ' contacts...');
            foreach ($data['contacts'] as $contact) {
                $uid = $contact['fields']['all']['span_id'] ?? null;
                $user = User::find($uid);
                if (!$user) {
                    $this->line('User with ID ' . $uid . ' not found, skipping.', 'comment');
                    continue;
                }
                if (in_array($uid, $all)) {
                    continue; // skip users who are already in wave 1
                }
                $total++;
                if ($this->dry) {
                    $new++;
                    $this->line('Would fix user wave for ' . $user->getId());
                    continue;
                }
                try {
                    $synData = ExternalInvoke::host(
                        MauticService::editContact($user, extra: [
                            'invite_wave' => 2,
                        ], force: $this->force)
                    );
                    if (is_array($synData)) {
                        $new++;
                        $this->line('Fixed user wave for ' . $user->getId());
                    }
                } catch (\Throwable $e) {
                    $this->line('Failed to fix user ' . $user->getId() . ': ' . $e->getMessage(), 'error');
                }
            }
        }
        $this->line('Totally fixed ' . $new . '/' . $total . ' wave 1 users to wave 2');
    }

    protected function syncPostmarkBounces()
    {
        $client = new PostmarkClient(Util::getKmsParameter('email_token'));
        $start = '2025-07-01T00:00:00';
        $pageSize = 100;
        $limit = (int)($this->input->getOption('limit') ?: 1000);
        $processed = 0;
        for ($page = 0; $page < 100; $page++) {
            $bounces = $client->getBounces(
                count: $pageSize,
                offset: $page * $pageSize,
                inactive: 'true',
                fromdate: $start
            );
            if (empty($bounces['Bounces'])) {
                $this->line('No more bounces found.');
                break;
            }
            $this->line("========== Processing page $page");
            foreach ($bounces['Bounces'] as $i => $bounce) {
                $action = PostmarkService::processBounce([
                    'Email' => $bounce['Email'] ?? null,
                    'Type' => $bounce['Type'] ?? null,
                ]);
                $this->line($i . ' in page ' . $page . ': processed bounce for ' . ($bounce['Email'] ?? 'unknown') . ': ' . $action);
                $processed++;
                if ($processed >= $limit) {
                    break 2;
                }
            }
        }
    }

    protected function syncAllUsers()
    {
        $userIds = InviteService::getWaveOnlyUserIds(99);
        $this->line('Found ' . count($userIds) . ' valid users to sync to Mautic');

        $rs = Util::em()->getRepository(UserConfig::class)
            ->createQueryBuilder('uc')
            ->where('uc.mauticContactId is not null')
            ->select('IDENTITY(uc.user) as userId')
            ->orderBy('userId', 'DESC')
            ->distinct()
            ->getQuery()
            ->getArrayResult();
        $syncedUserIds = array_column($rs, 'userId');
        $this->line('Found ' . count($syncedUserIds) . ' users already synced to Mautic');

        $userIds = array_unique(array_merge($userIds, $syncedUserIds));
        $this->line('Found ' . count($userIds) . ' users totally to sync to Mautic');

        $userIds = array_slice($userIds, $this->offset, $this->limit);
        if (empty($userIds)) {
            $this->line("No users to sync.");
            return;
        }
        $this->commonSyncUsers($userIds);
    }

    protected function syncWaveUsers(int $wave)
    {
        $userIds = InviteService::getWaveUserIds($wave);
        $userIds = array_slice($userIds, $this->offset, $this->limit);
        if (empty($userIds)) {
            $this->line("No users to sync for wave $wave.");
            return;
        }
        $extra = [
            'invite_wave' => $wave,
        ];
        $this->commonSyncUsers($userIds, $extra);
    }

    protected function commonSyncUsers(array $userIds, array $extra = [], int $wave = 0)
    {
        $batchSize = 100;
        $synced = [];
        $errors = [];
        $count = count($userIds);
        foreach ($userIds as $i => $userId) {
            $prefix = $i . '/' . $count . ' - ';
            if ($i % $batchSize === 0) {
                $this->line($prefix . '===== Processing ' . $userId . '...');
                Util::completeDoctrineBatch();
                $this->waitInterval();
            }
            $user = User::find($userId);
            if (!$user) {
                $this->line($prefix . "User with ID $userId not found.");
                continue;
            }
            if ($this->dry) {
                if ($user->isErased() || $user->isDeleted()) {
                    MauticService::deleteContact($user);
                } else {
                    $params = MauticService::getContactData($user);
                    $oldHash = MauticService::getDataHash($user);
                    $hash = Util::base64Hash($params);
                    if ($hash !== $oldHash) {
                        $this->line($prefix . 'Dry run mode on user ' . $user->getId());
                    }
                }
                continue;
            }
            try {
                if ($user->isErased() || $user->isDeleted()) {
                    $resp = MauticService::deleteContact($user);
                } else {
                    $resp = MauticService::editContact($user, extra: $extra, force: $this->force);
                }
                $synData = ExternalInvoke::host($resp);
                if (is_array($synData)) {
                    $synced[] = $userId;
                    $this->line($prefix . 'Synced to Mautic: ' . $user->getId() . ' -> ' .
                                ($synData['contact']['id'] ?? 'N/A'));
                    $this->waitInterval();
                } else if ($synData !== 'skipped') {
                    throw new \RuntimeException('Empty response');
                }
            } catch (\Throwable $e) {
                $this->line($prefix . 'Error syncing user ' . $user->getId() . ': ' . $e->getMessage(),
                    'comment');
                $this->waitInterval();
                $errors[$userId] = $e->getMessage();
                if (count($errors) > 100) {
                    $this->line('Too many errors, stopping sync.', 'error');
                    break;
                }
            }
        }
        if ($wave) {
            $msg = sprintf('Completed syncing wave %d to Mautic. Synced: %d/%d. ',
                $wave, count($synced), $count);
        } else {
            $msg = sprintf('Completed syncing all users to Mautic. Synced: %d/%d. ',
                count($synced), $count);
        }
        if ($errors) {
            $msg .= 'Errors: ' . count($errors);
            $errors = array_slice($errors, 0, 3);
            foreach ($errors as $userId => $error) {
                $errors[$userId] = Util::maxLength($error, 150);
            }
            SlackService::warning($msg, $errors, [
                SlackService::MENTION_HANS,
            ]);
        } else {
            SlackService::clap($msg);
        }
    }

    protected function waitInterval()
    {
        if ($this->interval > 0) {
            usleep($this->interval * 1000);
        }
    }
}
