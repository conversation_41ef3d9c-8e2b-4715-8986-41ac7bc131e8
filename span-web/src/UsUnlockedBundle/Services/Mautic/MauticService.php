<?php

namespace UsUnlockedBundle\Services\Mautic;

use Carbon\Carbon;
use CoreBundle\Entity\Email;
use CoreBundle\Entity\ExternalInvoke;
use CoreBundle\Entity\LoadMethod;
use CoreBundle\Entity\LoginAttempt;
use CoreB<PERSON>le\Utils\Util;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Psr7\Request;
use SalexUserBundle\Entity\User;
use SalexUserBundle\Entity\UserReferral;
use UsUnlockedBundle\Entity\PayPalSubscription;
use UsUnlockedBundle\Services\LoadService;
use UsUnlockedBundle\Services\SpendingService;
use UsUnlockedBundle\Services\UserRefererService;
use UsUnlockedBundle\Services\UserService;
use UsUnlockedBundle\UsUnlockedBundle;

class MauticService
{
    public const CONTACTS_UNSUBSCRIBED = 1;
    public const CONTACTS_BOUNCED = 2;
    public const CONTACTS_MANUAL = 3;

    public static function getHost(): string
    {
        if (Util::isLive()) {
            return 'https://go.usunlocked.com';
        }
        return 'http://mautic.local';
    }

    public static function request(string $endpoint, string $method = 'GET', array $params = [],
                                   bool $saveEi = false, string $requestType = NULL)
    {
        $requestType = $requestType ?? $endpoint;
        $context = $params;
        $ei = ExternalInvoke::create('mautic_' . $method . '_' . $requestType, $context, null,
            $saveEi);

        $password = Util::getKmsParameter('mautic_password');
        if (!$password) {
            return ExternalInvoke::tempFailedArray('Mautic API access is not configured!');
        }

        $url = self::getHost() . '/api/' . $endpoint;
        $request = new Request($method, $url);

        $client = new Client();
        $options = [
            'auth' => ['api', $password],
            'headers' => [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ],
        ];
        if ($method === 'GET') {
            $options['query'] = $params;
        } else {
            $options['json'] = $params;
        }

        try {
            $response = $client->send($request, $options);
        } catch (\Throwable $exception) {
            $response = $exception instanceof RequestException ? $exception->getResponse() : null;
            $msg = $exception->getMessage();
            $rawContent = null;
            if ($response) {
                $body = $response->getBody();
                $rawContent = $body ? $body->getContents() : '{}';
                $content = Util::s2j($rawContent ?: '{}') ?? [];
                if (!empty($content['errors'])) {
                    foreach ($content['errors'] as $error) {
                        $details = $error['details'] ?? [];
                        foreach ($details as $field => $items) {
                            $input = $params[$field] ?? null;
                            $msg = ($error['message'] ?? 'Unknown error.') . ' Value: `' . $input . '`.';
                            break 2;
                        }
                    }
                } else {
                    $msg = $content['error']['message'] ??
                           $content['error_description'] ??
                           $msg;
                }
                $ei->setStatusCode($response->getStatusCode());
            }
            $ei->fail($rawContent, 'Mautic API error: ' . $msg)
                ->persist();
            return [$ei, null];
        }

        $rawContent = $response->getBody()->getContents();
        $content = Util::s2j($rawContent) ?? [];

        if ($saveEi) {
            $ei->succeed($rawContent, $response->getStatusCode())
                ->persist();
        }

        return [$ei, $content];
    }

    public static function getSelf()
    {
        return ExternalInvoke::host(self::request('users/self'));
    }

    public static function formatDateTime($date = null, $format = Util::DATE_TIME_FORMAT_FULL): ?string
    {
        return Util::formatDateTime($date, $format, Util::tzUTC()) ?: null;
    }

    public static function getDataHash(User $user): ?string
    {
        $config = $user->ensureConfig();
        if ($config->getMauticHash()) {
            return $config->getMauticHash();
        }
        return Util::meta($user, 'mauticHash');
    }

    public static function isCountrySkipped(?string $isoCode)
    {
        if (!$isoCode) {
            return false;
        }
        return in_array(strtoupper($isoCode), UsUnlockedBundle::SKIP_MARKETING_COUNTRIES);
    }

    public static function getContactData(User $user): array
    {
        $createdAt = $user->getCreatedAt();
        $lastActive = self::formatDateTime($user->getLastActiveAt());
        $meta = Util::meta($user);
        $d = [
            // Builtin
            'firstname' => $user->getFirstName(),
            'lastname' => $user->getLastName(),
            'email' => $user->getEmail(),
            'mobile' => $user->getMobilephone(),
            'phone' => $user->getPhone(),
            'address1' => Util::maxLength($user->getAddress(), 64) ?: null,
            'address2' => Util::maxLength($user->getAddressline(), 64) ?: null,
            'city' => Util::maxLength(trim($user->getCity() ?? ''), 64) ?: null,
            'state' => $user->getStateName(),
            'zipcode' => Util::maxLength(trim($user->getZip() ?? ''), 64) ?: null,
            'country' => $user->getCountryName(),
            'region' => $user->getCountryRegion(),
            'preferred_locale' => $user->getCorrectedLanguageCode(),
            'timezone' => $user->getCorrectedTimezone(),
            'last_active' => $lastActive,
            'ipAddress' => $user->getRegistrationIp(),
            'lastActive' => $lastActive,

            // Custom fields
            'span_id' => $user->getId(),
            'created_date' => self::formatDateTime($createdAt),
            'email_verified' => (bool)$user->getEmailVerified(),
            'invite_method' => 'ORGANIC',
            'user_status' => $user->getStatus(),
            'last_successful_login_date' => self::formatDateTime($user->getLastLogin()),
            'last_failed_login_date' => self::formatDateTime(LoginAttempt::getLastFailAt($user)),
            'on_waitlist' => true,
            'waitlist_added_date' => null,
            'subscription_status' => 'NONE',
            'subscription_date' => null,
            'subscription_canceled_date' => null,
            'subscription_end_date' => null,
            'kyc_status' => 'NOT_STARTED',
            'kyc_last_update' => null,
            'has_balance' => false,
            'first_load_date' => null,
            'first_load_type' => null,
            'loaded_with_card' => false,
            'loaded_with_bank' => false,
            'loaded_with_crypto' => false,
            'card_last_load_date' => null,
            'bank_last_load_date' => null,
            'crypto_last_load_date' => null,
            'last_spend_txn_date' => null,
            'favorite_merchant_1' => null,
            'favorite_merchant_2' => null,
            'favorite_merchant_3' => null,
            'user_referral_count' => 0,
            'opt_in_consent' => true,
            'bounced_email' => $meta['mautic_bounced_email'] ?? false,
            'bounced_date' => $meta['mautic_bounced_date'] ?? null,
            'unsubscribed' => $meta['mautic_unsubscribed'] ?? false,
            'unsubscribed_date' => $meta['mautic_unsubscribed_date'] ?? null,
        ];

        $clearedInviteWave = false;
        $waitlistStart = Carbon::create(2025, 6);
        $canInvite = UserRefererService::canInvite($user);
        if ($canInvite) {
            if ($canInvite instanceof UserReferral && $canInvite->getWave() === 3) {
                if (!$createdAt || $waitlistStart->gt($createdAt)) {
                    $d['on_waitlist'] = false;
                } else {
                    $d['invite_wave'] = null;
                    $clearedInviteWave = 3;
                }
            } else {
                $d['on_waitlist'] = false;
            }
            $d['user_referral_count'] = UserRefererService::getReferralCountOfUser($user);
        }

        $referral = UserRefererService::getReferralOfUser($user);
        if ($referral && !$referral->getWave()) {
            $d['invite_method'] = 'REFERRAL';
        }

        if ($createdAt && (!$referral || $referral->getWave() !== 1)) {
            $earliest = Carbon::create(2025, 6, 1);
            $d['waitlist_added_date'] = self::formatDateTime($earliest->lte($createdAt) ? $createdAt : $earliest);
        }

        $skipped = Email::isUserSkipped($user);
        $countrySkipped = self::isCountrySkipped($user->getCountryCode());
        if ($skipped || $countrySkipped || !$user->isActive() || ($createdAt && $waitlistStart->gt($createdAt))) {
            $d['on_waitlist'] = false;
            $d['waitlist_added_date'] = null;
        }

        $sub = UserService::getPreparedPayPalSubscription($user);
        if ($sub) {
            $subStatus = $sub->getStatus();
            if ($subStatus !== PayPalSubscription::STATUS_ACTIVE) {
                $d['subscription_status'] = $sub->getStatus();
            } else {
                $d['subscription_status'] = $sub->getCurrentPlanType();
            }
            $d['subscription_status'] = strtoupper($d['subscription_status']);
            $d['subscription_date'] = self::formatDateTime($sub->getStartAt());

            if ($subStatus === PayPalSubscription::STATUS_CANCELLED) {
                $d['subscription_canceled_date'] = self::formatDateTime($sub->getUpdatedAt());
                $d['subscription_end_date'] = self::formatDateTime($sub->getEndAt());
            }

            if (in_array($d['subscription_status'], [
                'MONTHLY',
                'ANNUALLY',
                'WAIVED',
            ])) {
                $d['on_waitlist'] = false;
                if ($clearedInviteWave) {
                    $d['invite_wave'] = $clearedInviteWave;
                }
            }
        }

        $uiv = $user->getIdVerify();
        if ($uiv) {
            $d['kyc_status'] = $uiv->isAccepted() ? 'APPROVED' : 'FAILED';
            $d['kyc_last_update'] = self::formatDateTime($uiv->getUpdatedAt());
        }

        $dummy = $user->getUsuDummyCardOnly();
        if ($dummy) {
            $d['has_balance'] = (int)($dummy->getBalance()) > 0;
        }

        $ucl = $user->getFirstLoadedLoad();
        if ($ucl) {
            $d['first_load_date'] = self::formatDateTime($ucl->getLoadAt());

            $methodName = $ucl->getMethod()->getName();
            if ($methodName === LoadMethod::PAYNAME_COINFLOW_CRYPTO) {
                $d['first_load_type'] = 'CRYPTO';
            } else if ($methodName === LoadMethod::PAYNAME_COINFLOW_CARD) {
                $d['first_load_type'] = 'CARD';
            } else {
                $d['first_load_type'] = 'BANK';
            }

            $cardLoad = LoadService::getLatestLoadedLoad($user, LoadMethod::PAYNAME_COINFLOW_CARD);
            if ($cardLoad) {
                $d['loaded_with_card'] = true;
                $d['card_last_load_date'] = self::formatDateTime($cardLoad->getLoadAt());
            }
            $cryptoLoad = LoadService::getLatestLoadedLoad($user, LoadMethod::PAYNAME_COINFLOW_CRYPTO);
            if ($cryptoLoad) {
                $d['loaded_with_crypto'] = true;
                $d['crypto_last_load_date'] = self::formatDateTime($cryptoLoad->getLoadAt());
            }
            $bankLoad = LoadService::getLatestLoadedLoad($user, null, [
                LoadMethod::PAYNAME_COINFLOW_CARD,
                LoadMethod::PAYNAME_COINFLOW_CRYPTO,
            ]);
            if ($bankLoad) {
                $d['loaded_with_bank'] = true;
                $d['bank_last_load_date'] = self::formatDateTime($bankLoad->getLoadAt());
            }

            $merchants = SpendingService::getTopMerchants($user);
            foreach ($merchants as $i => $m) {
                if ($i >= 3) {
                    break;
                }
                $d['favorite_merchant_' . ($i + 1)] = $m['name'] ?? null;
            }
        }

        $uct = SpendingService::getLatestApprovedTransaction($user);
        if ($uct) {
            $d['last_spend_txn_date'] = self::formatDateTime($uct->getTxnTime());
        }

        $d['is_suppressed'] = $skipped
                              || $d['bounced_email']
                              || $d['unsubscribed']
                              || !$d['opt_in_consent']
                              || !$d['email_verified'];

        ksort($d);
        return $d;
    }

    /**
     * @param User  $user
     * @param array $extra will not be added to hash
     * @param bool  $force
     *
     * @return array
     */
    public static function editContact(User $user,
                                       array $extra = [],
                                       bool $force = false): array
    {
        $config = $user->ensureConfig();
        $params = self::getContactData($user);
        $oldHash = self::getDataHash($user);
        $hash = Util::base64Hash($params);
        if (!$force && $oldHash && $hash === $oldHash) {
            return ExternalInvoke::tempSuccessArray('No changes to sync.', 'skipped');
        }

        $old = Util::s2je($config->getMauticData());
        $oldData = $old['current'] ?? [];
        foreach ($extra as $k => $v) {
            $params[$k] = $v;
        }
        $params['last_sync_date'] = self::formatDateTime(new \DateTime());

        $id = $config->getMauticContactId() ?? 0;
        $method = $id ? 'PATCH' : 'PUT';
        /** @var ExternalInvoke $ei */
        [$ei, $data] = self::request('contacts/' . $id . '/edit', $method, $params,
            requestType: 'contracts/x/edit');
        if (!$ei->isFailed()) {
            $contact = $data['contact'] ?? [];
            if (!$id && !empty($contact['id'])) {
                $config->setMauticContactId($contact['id']);
            }
            Util::updateMeta($user, [
                'mauticSyncDate' => $params['last_sync_date'],
                'mauticHash' => $hash,
            ], false);
            $config->setMauticHash($hash)
                ->setMauticData(Util::j2se([
                    'old' => $oldData,
                    'current' => $params,
                ]))
                ->persist();
        }
        return [$ei, $data];
    }

    public static function deleteContact(User $user): array
    {
        $config = $user->ensureConfig();
        $id = $config->getMauticContactId();
        if (!$id) {
            return ExternalInvoke::tempSuccessArray(data: 'skipped');
        }
        /** @var ExternalInvoke $ei */
        [$ei, $data] = self::request('contacts/' . $id . '/delete', 'DELETE',
            requestType: 'contracts/x/delete');
        if (!$ei->isFailed()) {
            $config->setMauticContactId(null)
                ->persist();
        }
        return [$ei, $data];
    }

    /**
     * @param User   $user
     * @param int    $reason  Int value of the reason. Use Contacts constants: Contacts::UNSUBSCRIBED (1), Contacts::BOUNCED (2), Contacts::MANUAL (3). Default is Manual
     * @param string $channel Channel of DNC. For example ‘email’, 'sms’… Default is email.
     * @param string $comments
     *
     * @return array
     */
    public static function addDoNotContact(User $user, int $reason = self::CONTACTS_MANUAL,
                                           string $channel = 'email', string $comments = ''): array
    {
        $id = $user->ensureConfig()->getMauticContactId();
        return self::request('contacts/' . $id . '/dnc/' . $channel . '/add', 'POST', [
            'reason' => $reason,
            'comments' => $comments,
        ], requestType: 'contacts/x/dnc/y/add');
    }

    public static function removeDoNotContact(User $user, string $channel = 'email'): array
    {
        $id = $user->ensureConfig()->getMauticContactId();
        return self::request('contacts/' . $id . '/dnc/' . $channel . '/remove', 'POST',
            requestType: 'contacts/x/dnc/y/remove');
    }

    public static function listContacts(int $start = 0, int $limit = 100, string $search = '', array $others = []): array
    {
        $params = [
            'start' => $start,
            'limit' => $limit,
        ];
        if ($search) {
            $params['search'] = $search;
        }
        $params = array_merge($params, $others);
        return self::request('contacts', 'GET', $params);
    }

    public static function updateContactForBounce(User $user, string $comments = '', bool $addToDnc = false): ?array
    {
        $meta = Util::meta($user);
        if (!empty($meta['mautic_bounced_email'])) {
            return null;
        }
        Util::updateMeta($user, [
            'mautic_bounced_email' => true,
            'mautic_bounced_date' => self::formatDateTime(new \DateTime()),
        ]);
        $resp = self::editContact($user);
        if ($addToDnc) {
            self::addDoNotContact($user, self::CONTACTS_BOUNCED, comments: $comments);
        }
        return $resp;
    }

    public static function updateContactForUnsubscribe(User $user, string $comments = '', bool $addToDnc = false): ?array
    {
        $meta = Util::meta($user);
        if (!empty($meta['mautic_unsubscribed'])) {
            return null;
        }
        Util::updateMeta($user, [
            'mautic_unsubscribed' => true,
            'mautic_unsubscribed_date' => self::formatDateTime(new \DateTime()),
        ]);
        $resp = self::editContact($user);
        if ($addToDnc) {
            self::addDoNotContact($user, self::CONTACTS_UNSUBSCRIBED, comments: $comments);
        }
        return $resp;
    }

    public static function updateContactForSubscribe(User $user, bool $removeFromDnc = false): ?array
    {
        $meta = Util::meta($user);
        if (empty($meta['mautic_unsubscribed'])) {
            return null;
        }
        $meta = Util::meta($user);
        unset($meta['mautic_unsubscribed'], $meta['mautic_unsubscribed_date']);
        $user->setMeta(Util::j2se($meta))
            ->persist();
        $resp = self::editContact($user);
        if ($removeFromDnc) {
            self::removeDoNotContact($user);
        }
        return $resp;
    }
}
