<?php

namespace UsUnlockedBundle\Services\Mautic;

use Carbon\Carbon;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\CardProgramCardType;
use CoreBundle\Entity\Email;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Entity\UserCardSnap;
use CoreBundle\Utils\Util;
use Doctrine\ORM\QueryBuilder;
use SalexUserBundle\Entity\User;
use UsUnlockedBundle\Services\Privacy\PrivacyAPI;
use UsUnlockedBundle\UsUnlockedBundle;

class InviteService
{
    protected static function getWaveQuery(int $wave): QueryBuilder
    {
        $method = 'getWave' . $wave . 'Query';
        if (method_exists(self::class, $method)) {
            return self::$method();
        }
        throw new \InvalidArgumentException('Wave ' . $wave . ' is not supported.');
    }

    public static function getWaveOnlyUserIds(int $wave): array
    {
        $all = self::getWaveQuery($wave)
            ->orderBy('u.id', 'DESC')
            ->distinct()
            ->getQuery()
            ->getArrayResult();
        return array_map(
            static fn(array $row) => (int)$row['id'],
            $all
        );
    }

    public static function getWaveUserIds(int $wave): array
    {
        $list = self::getWaveOnlyUserIds($wave);
        if ($wave === 1) {
            return $list;
        }
        $subtracts = [];
        for ($i = 1; $i < $wave; $i++) {
            $subtracts[] = self::getWaveOnlyUserIds($i);
        }
        $list = array_diff($list, ...$subtracts);
        $list = array_values($list);
        return $list;
    }

    protected static function getWave1Query(): QueryBuilder
    {
        return Util::em()->getRepository(UserCardSnap::class)
            ->createQueryBuilder('ucs')
            ->join('ucs.userCard', 'uc')
            ->join('uc.user', 'u')
            ->join('u.teams', 't')
            ->join('u.country', 'country')
            ->where('uc.card = :card')
            ->andWhere('uc.type = :type')
            ->andWhere('t.name = :teamName')
            ->andWhere('u.status = :status')
            ->andWhere('ucs.balance > 0')
            ->andWhere('ucs.time = :snapshot')
            ->andWhere('u.id not in (:excludedUsers)')
            ->andWhere('u.createdAt < :createdAt')
            ->andWhere('country.isoCode not in (:excludedCountries)')
            ->setParameter('card', CardProgramCardType::getForCardProgram(CardProgram::usunlocked()))
            ->setParameter('type', PrivacyAPI::CARD_TYPE_DUMMY)
            ->setParameter('teamName', Role::ROLE_CONSUMER)
            ->setParameter('status', UserCard::STATUS_ACTIVE)
            ->setParameter('excludedCountries', UsUnlockedBundle::BLOCKED_COUNTRIES)
            ->setParameter('excludedUsers', Email::getSkippedUserIds())
            ->setParameter('createdAt', Carbon::create(2025, 7, 3))
            ->setParameter('snapshot', '2025-05')
            ->select('u.id');
    }

    protected static function getWave2Query(): QueryBuilder
    {
        return Util::em()->getRepository(UserCardLoad::class)
            ->createQueryBuilder('ucl')
            ->join('ucl.userCard', 'uc')
            ->join('uc.card', 'ct')
            ->join('ct.cardProgram', 'cp')
            ->join('uc.user', 'u')
            ->join('u.teams', 't')
            ->join('u.country', 'country')
            ->where('ucl.type = :load_type')
            ->andWhere('ucl.loadStatus = :load_status')
            ->andWhere('cp.platform = :platform')
            ->andWhere('t.name = :teamName')
            ->andWhere('u.status = :status')
            ->andWhere('u.createdAt < :createdAt')
            ->andWhere('uc.balance <= 0 or uc.balance is null')
            ->andWhere('country.isoCode not in (:excludedCountries)')
            ->andWhere('u.id not in (:excludedUsers)')
            ->setParameter('load_type', UserCardLoad::TYPE_LOAD_CARD)
            ->setParameter('load_status', UserCardLoad::LOAD_STATUS_LOADED)
            ->setParameter('platform', Platform::ternCommerce())
            ->setParameter('teamName', Role::ROLE_CONSUMER)
            ->setParameter('status', UserCard::STATUS_ACTIVE)
            ->setParameter('excludedCountries', UsUnlockedBundle::BLOCKED_COUNTRIES)
            ->setParameter('excludedUsers', Email::getSkippedUserIds())
            ->setParameter('createdAt', Carbon::create(2025, 7, 3))
            ->select('u.id');
    }

    protected static function getWave3Query(): QueryBuilder
    {
        $expr = Util::expr();
        return Util::em()->getRepository(User::class)
            ->createQueryBuilder('u')
            ->join('u.cards', 'uc')
            ->join('uc.card', 'ct')
            ->join('ct.cardProgram', 'cp')
            ->join('u.teams', 't')
            ->leftJoin('u.country', 'country')
            ->where('cp.platform = :platform')
            ->andWhere('t.name = :teamName')
            ->andWhere('u.status = :status')
            ->andWhere('u.id not in (:excludedUsers)')
            ->andWhere('u.emailVerified = 1')
            ->andWhere('u.createdAt < :createdAt')
            ->andWhere($expr->orX(
                $expr->andX(
                    $expr->isNotNull('u.country'),
                    $expr->notIn('country.isoCode', ':excludedCountries')
                ),
                $expr->andX(
                    $expr->isNull('u.country'),
                    $expr->notLike('u.email', ':emailPattern')
                )
            ))
            ->andWhere('u.country is null or country.isoCode not in (:excludedCountries)')
            ->setParameter('platform', Platform::ternCommerce())
            ->setParameter('teamName', Role::ROLE_CONSUMER)
            ->setParameter('status', User::STATUS_ACTIVE)
            ->setParameter('excludedUsers', Email::getSkippedUserIds())
            ->setParameter('excludedCountries', UsUnlockedBundle::BLOCKED_COUNTRIES)
            ->setParameter('emailPattern', '%@%.ru') // Exclude Russian emails
            ->setParameter('createdAt', Carbon::create(2025, 7, 3))
        ;
    }

    protected static function getWave99Query(): QueryBuilder
    {
        $expr = Util::expr();
        return Util::em()->getRepository(User::class)
            ->createQueryBuilder('u')
            ->join('u.cards', 'uc')
            ->join('uc.card', 'ct')
            ->join('ct.cardProgram', 'cp')
            ->join('u.teams', 't')
            ->leftJoin('u.country', 'country')
            ->where('cp.platform = :platform')
            ->andWhere('t.name = :teamName')
            ->andWhere('u.status in (:statuses)')
            ->andWhere('u.id not in (:excludedUsers)')
            ->andWhere('u.emailVerified = 1')
            ->andWhere($expr->orX(
                $expr->andX(
                    $expr->isNotNull('u.country'),
                    $expr->notIn('country.isoCode', ':excludedCountries')
                ),
                $expr->andX(
                    $expr->isNull('u.country'),
                    $expr->notLike('u.email', ':emailPattern')
                )
            ))
            ->andWhere('u.country is null or country.isoCode not in (:excludedCountries)')
            ->setParameter('platform', Platform::ternCommerce())
            ->setParameter('teamName', Role::ROLE_CONSUMER)
            ->setParameter('statuses', [
                User::STATUS_ACTIVE,
                User::STATUS_UNDER_REVIEW,
            ])
            ->setParameter('excludedUsers', Email::getSkippedUserIds())
            ->setParameter('excludedCountries', UsUnlockedBundle::BLOCKED_COUNTRIES)
            ->setParameter('emailPattern', '%@%.ru') // Exclude Russian emails
            ;
    }

    public static function getWaveTitles(int $wave): array
    {
        $all = [
            [],
            [ // Wave 1
                "US Unlocked is Back, and You're First In – Your Balance Is Waiting + $5 Credit Inside",
            ],
            [ // Wave 2
                'Remember US Unlocked? We\'re back (and way easier now)',
                'US Unlocked is back - with Apple Pay, crypto, and more',
                'We\'re back! US Unlocked now accepts credit cards & Apple Pay',
                'US Unlocked has relaunched with easier funding options',
            ],
            [
                // Wave 3
                'The funding hassle is gone - your US Unlocked account awaits',
                'Ready to finally use that US Unlocked account?',
                'Load your US Unlocked card in seconds now',
                'Apple Pay, credit cards, crypto - US Unlocked accepts it all',
            ],
            [
                // Wave 4,
                'Your wait is over - US Unlocked is live!',
                'Finally! Your US Unlocked account is ready',
                'The US shopping solution you waited for is here',
            ],
        ];
        return $all[$wave] ?? [];
    }

    public static function getWaveBatchTitle(int $wave, int $batch = 0): string
    {
        $titles = self::getWaveTitles($wave);
        return $titles[$batch] ?? $titles[0] ?? '';
    }
}
