<?php


namespace UsUnlockedBundle\Controller;


use Carbon\Carbon;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\FeeGlobalName;
use CoreBundle\Entity\LoadMethod;
use CoreBundle\Entity\LoadPartner;
use CoreBundle\Entity\MerchantType;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserCardDecline;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Entity\UserCardTransaction;
use CoreBundle\Entity\UserFeeHistory;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Services\MerchantService;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Util;
use Doctrine\Common\Collections\Collection;
use PortalBundle\Exception\PortalException;
use Stringy\Stringy;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use UsUnlockedBundle\Services\LegacyService;
use UsUnlockedBundle\Services\Privacy\PrivacyAPI;
use UsUnlockedBundle\Services\Privacy\PrivacyService;
use UsUnlockedBundle\Services\UsuService;
use SalexUserBundle\Entity\User;
use UsUnlockedBundle\Services\CardProcessorHub;
use UsUnlockedBundle\Services\Rain\RainAPI;

class HistoryController extends BaseController
{
	/**
	 * @Route("/p/history")
	 * @param Request $request
	 * @param bool    $return
	 *
	 * @return FailedResponse|SuccessResponse|array
	 */
	public function index(Request $request, $return = false)
    {
        $status = $request->get('status', PrivacyAPI::TRANSACTION_APPROVAL_STATUS_APPROVALS);
        $force  = $request->get('mode') === 'force' || $return;
        $cacheKey = PrivacyService::getTransactionsListCacheKey($this->user->getId(), $status);
        if (!Data::has($cacheKey)) {
            $force = true;
        }

        if ($force) {
            $items = $this->queryTxnList($status);
            if ($return) {
                return $items;
            }
        }

        $page = (int)$request->get('page', 1);

        $defaultSize = Util::isLocal() ? 50 : 200;
        if ($status === PrivacyAPI::TRANSACTION_APPROVAL_STATUS_APPROVALS && Util::isServer()) {
            $defaultSize = 500;
        }
        $size = (int)$request->get('size', $defaultSize);

        $all = Data::getArray($cacheKey);
        $total = count($all);
        $items = array_slice($all, $size * ($page - 1), $size);

        return new SuccessResponse([
            'items' => $items,
            'total' => $total,
        ]);
    }

    protected function queryTxnList($status)
    {
		if ($this->platform->isCashOnWeb()) {
			$ucs = $this->user->getCardsInCashOnWeb(null);
		} else {
			$ucs = $this->user->getCardsInUsUnlocked(null);
		}

		$items = [];
		if ($status === PrivacyAPI::TRANSACTION_APPROVAL_STATUS_DECLINES) {
			$items = array_merge($items, $this->getDeclinedTransactions($ucs));
			Util::usort($items, [
				'timestamp' => 'desc',
			]);
		} else if ($status === PrivacyAPI::TRANSACTION_APPROVAL_STATUS_APPROVALS) {
			$pendingBalance = LegacyService::getVisiblePendingBalance($this->user);
			if ($pendingBalance) {
				$items[] = [
					'type' => 'alert',
					'title' => 'Previous Balance',
					'subtitle' => 'Pending Deposit',
					'amountValue' => $pendingBalance,
					'amount' => Money::format($pendingBalance, 'USD', false),
					'timestamp' => Carbon::now()->addYearWithoutOverflow()->getTimestamp(),
					'action' => [
						'modal' => '#helpLegacyBalance',
					],
				];
			}

			$items = array_merge(
				$items,
				$this->getUsageTransactions($ucs),
				$this->getLoadTransactions($ucs),
				$this->getDeclinedTransactionFees($ucs),
				$this->getIndividualFees()
			);
			Util::usort($items, [
				'timestamp' => 'desc',
			]);

			$balance = 0;
			for ($i = count($items) - 1; $i >= 0; $i--) {
				$item = $items[$i];
				if ($item['type'] === 'alert') {
					continue;
				}
				$items[$i]['amountStr'] =  Money::format($item['amountValue'], 'USD', false);
				$balance += $item['amountValue'];
				$items[$i]['balance'] = Money::format($balance, 'USD', false);
			}
		}

        $cacheKey = PrivacyService::getTransactionsListCacheKey($this->user->getId(), $status);
        Data::setArray($cacheKey, $items, 0, false);

        return $items;
	}

	protected function getUsageTransactions(Collection $ucs)
	{
		$settledStatuses = [
			'Completed',
			'Settling',
			'Settled',
		];
		$items = [];
		/** @var UserCard $uc */
		foreach ($ucs as $uc) {
			if (!$uc->isIssued()) {
				continue;
			}
			$cp = $uc->getCardProgram();
			$usages = $uc->getUsages();
			/** @var UserCardTransaction $usage */
			foreach ($usages as $usage) {
                if (Util::meta($usage, 'hideForConsumer')) {
                    continue;
                }

				$merchant = $usage->getMerchant();
				$status   = $usage->getFormattedStatus();
				$settled  = in_array($status, $settledStatuses);
                $subtitle = '';
				$subClass = NULL;
                $subTip = null;
				if ($status === 'Merchant Hold') {
					$subClass = 'orange heavy';
				} else if ($settled) {
					$subClass = '';
				} else if ($status === 'Returned') {
					$subClass = 'text-primary heavy';
				} else {
					$subClass = 'text-danger heavy';
				}

                $amountValue = -$usage->getTxnAmount();
                $amount = UsuService::formatMoney($amountValue);

				if ($usage->isReturn()) {
                    if ($usage->isQueuedReturn() || ($uc->isRainCard() && $usage->getStatus() == RainAPI::TRANSACTION_STATUS_PENDING)) {
                        $amountValue = 0;
                        $subtitle = 'Returning';
                        $subClass = 'orange heavy';
                    } else if ($usage->isValidReturn() || ($uc->isRainCard() && $usage->getStatus() == RainAPI::TRANSACTION_STATUS_COMPLETED)) {
                        $subtitle = 'Returned';
                        $subClass = 'text-primary heavy';
                    } else {
                        $amountValue = 0;
                        $subtitle = 'Pending/Voided Return';
                        $subClass = '';
                    }
				} else if ($usage->isVoiding()) {
                    $amountValue = -$usage->getTotalHoldingAmount();
                    $amount = UsuService::formatMoney($amountValue);

					if ($usage->isPartialClearing()) {
						$subtitle = 'Partial Clearing';
						if ($usage->getTxnAmount()) {
							$subtitle = 'Partially clearing ' . Money::format($usage->getTxnAmount(), 'USD', false);
						}
					} else {
						$subtitle = 'Voiding';
						if ($usage->getOverdraftProtected() !== 'entire' && $usage->getTxnAmount()) {
							$subtitle = 'Voiding ' . Money::format($usage->getVoidingAmount(), 'USD', false);
						}
					}
					$subClass = 'orange heavy';
				} else if ($settled) {
					$subtitle = 'Completed';
				} else {
					$subtitle = $status;
					if ($status === 'Declined') {
						$detail = Util::meta($usage, 'privacyDetail');
						if (!empty($detail['status']) && $detail['status'] === 'DECLINED' && !empty($detail['result'])) {
							$subtitle = 'Declined (' . Util::title($detail['result']) . ')';
						}
					}
				}

                if (!$usage->isVoiding() && $usage->isInvalid()) {
                    $amountValue = 0;

                    if ($usage->isVoided()) {
                        $amount = UsuService::formatMoney($usage->getOriginalTxnAmount());
                    }

//                    if ($status === 'Expired') {
//                        $detail = Util::meta($usage, 'privacyDetail');
//                        if (!empty($detail['settled_amount'])) {
//                            $amountValue = -$detail['settled_amount'];
//                            $amount = UsuService::formatMoney($amountValue);
//                        }
//                    }
                }

                $negAuth = $usage->isNegativeAuth();
                if ($negAuth) {
                    $subtitle = 'PayPal Credit';
                    $subClass = 'orange heavy';

                    if ($usage->isDisallowedNegAuth()) {
                        $subtitle = 'Disallowed PayPal Credit';
                        $subTip = 'Use your own PayPal account to load. The name must match.';
                        $amountValue = 0;
                    }
                }

                if ($amountValue !== 0 && Util::meta($usage, 'skipForInvalidReversalReturn')) {
                    $detail = Util::meta($usage, 'privacyDetail') ?? [];
                    $events = $detail['events'] ?? [];
                    if ($events) {
                        $event = $events[count($events) - 1];
                        $eventType = $event['type'] ?? null;
                        if ($eventType === 'RETURN_REVERSAL') {
                            $amountValue -= $event['amount'] ?? 0;
                            $amount = UsuService::formatMoney($amountValue);
                        }
                    }
                }

				$time = $usage->getTxnTime();
				$fee = $usage->getFee();
				if (!$fee) {
					$time = $usage->getCreatedAt();
				}
                $merchantType = $merchant?->getMerchantType();
                $itemMeta = [
					'type'        => 'transaction',
					'id'          => $usage->getId(),
					'token'       => $usage->getTranId(),
					'title'       => $usage->getMerchantName(),
					'subtitle'    => $subtitle,
					'subClass'    => $subClass,
                    'subTip'      => $subTip,
					'category'    => Util::field($merchant, 'category'),
					'iconClass'   => $merchantType ? $merchantType->getCategoryIcon() : (Util::field($merchant, 'categoryIcon') ?? 'other'),
					'iconBgColor' => $merchantType ? $merchantType->getCategoryColor() : (Util::field($merchant, 'categoryColor') ?? '#7C8698'),
                    'icon'        => $merchant?->getIconUrl(),
					'amount'      => $amount,
					'amountValue' => $amountValue,
					'timestamp'   => $time->getTimestamp(),
					'action'      => $uc->getCardProgram()->getPlatform()->isCashOnWeb() ? ['event' => 'show-transaction-detail'] : NULL
				];
                if ($negAuth) {
                    $loadMethod = LoadMethod::find(LoadMethod::PAYNAME_PAYPAL);
                    if ($loadMethod) {
                        $itemMeta['icon'] = $loadMethod->getIconUrl();
                        $itemMeta['iconBoxClass'] = 'load_method';
                        unset($itemMeta['iconBgColor'], $itemMeta['iconClass'], $itemMeta['category']);
                    }
                }
                $items[] = $itemMeta;

				if ($usage->getFee()) {
					$amountValue = -$usage->getFee();
					$amount = UsuService::formatMoney($amountValue);
                    $feeName = Util::meta($usage, 'txnFeeName') ?? 'Transaction Fee';
					$items[] = [
						'type'        => 'fee',
						'id'          => $usage->getId(),
						'token'       => $usage->getTranId(),
						'title'       => $cp->getName(),
						'subtitle'    => $feeName,
						'icon'        => $uc->getCardProgram()->getPlatform()->isCashOnWeb() ?  $uc->getCardProgram()->getPlatform()->getUrl('tranIcon') : '/static/usu/img/txn/debit_3d.jpg',
						'amount'      => $amount,
						'amountValue' => $amountValue,
						'timestamp'   => $time->getTimestamp() + 1,
						'action'      => NULL,
					];
				}
			}
		}
		return $items;
	}

	protected function getDeclinedTransactionFees(Collection $ucs)
	{
		$items = [];
		/** @var UserCard $uc */
		foreach ($ucs as $uc) {
			if (!$uc->isIssued()) {
				continue;
			}
			$cp = $uc->getCardProgram();
			$declines = $uc->getDeclines();
			/** @var UserCardDecline $decline */
			foreach ($declines as $decline) {
				if (!$decline->getFee()) {
					continue;
				}
				$amountValue = -$decline->getFee();
				$amount = UsuService::formatMoney($amountValue);
				$items[] = [
					'type'        => 'fee',
					'id'          => $decline->getId(),
					'token'       => $decline->getTxnId(),
					'title'       => $cp->getName(),
					'subtitle'    => 'Declined Transaction Fee',
					'icon'        => '/static/usu/img/txn/debit_3d.jpg',
					'amount'      => $amount,
					'amountValue' => $amountValue,
					'timestamp'   => $decline->getTxnTime()->getTimestamp(),
					'action'      => NULL,
				];
			}
		}
		return $items;
	}

	protected function getDeclinedTransactions(Collection $ucs)
	{
		$items = [];
		/** @var UserCard $uc */
		foreach ($ucs as $uc) {
			if (!$uc->isIssued()) {
				continue;
			}
			$declines = $uc->getDeclines();
			/** @var UserCardDecline $decline */
			foreach ($declines as $decline) {
				$merchant = $decline->getMerchantEntity();
				$amountValue = -$decline->getTxnAmount();
				$amount = UsuService::formatMoney($amountValue);
                $merchantType = $merchant?->getMerchantType();
				$items[]     = [
					'type'        => 'decline',
					'id'          => $decline->getId(),
					'token'       => $decline->getTxnId(),
					'title'       => $decline->getMerchantName(),
					'subtitle'    => $decline->getOptimizedDeclineReason(),
					'subClass'    => 'text-danger heavy',
					'category'    => Util::field($merchant, 'category'),
					'iconClass'   => $merchantType ? $merchantType->getCategoryIcon() : (Util::field($merchant, 'categoryIcon')  ?? 'other'),
					'iconBgColor' => $merchantType ? $merchantType->getCategoryColor() : (Util::field($merchant, 'categoryColor')  ?? '#7C8698'),
                    'icon'        => $merchant?->getIconUrl(),
					'amount'      => $amount,
					'amountValue' => 0,
					'timestamp'   => $decline->getTxnTime()->getTimestamp(),
					'action'      => NULL,
				];
			}
		}
		return $items;
	}

	protected function getIndividualFees()
	{
		$repo = Util::em()->getRepository(UserFeeHistory::class);
		$expr = Util::expr();
		$rs = $repo->createQueryBuilder('ufh')
			->where('ufh.user = :user')
			->andWhere($expr->in('ufh.feeName', ':feeNames'))
			->setParameter('user', $this->user)
			->setParameter('feeNames', [
				FeeGlobalName::MONTHLY_FEE,
				FeeGlobalName::CARD_CREATION_FEE
			])
			->orderBy('ufh.time')
			->getQuery()
			->getResult()
		;
		$platform = Util::$platform;
		if ($platform && $platform->isCashOnWeb()) {
			$cp = CardProgram::cashOnWeb();
		} else {
			$cp = CardProgram::usunlocked();
		}
		$items = [];
		/** @var UserFeeHistory $r */
		foreach ($rs as $r) {
			if ($platform && $platform->isCashOnWeb()) {
				$amountValue = -$r->getAmount() - $r->getCost();
			} else {
				$amountValue = -$r->getAmount();
			}
			$amount      = UsuService::formatMoney($amountValue);
			$items[]     = [
				'type'        => $r->getFeeName() == FeeGlobalName::MONTHLY_FEE ? 'monthly' : 'cardFee',
				'id'          => $r->getId(),
				'title'       => $cp->getName(),
				'subtitle'    => $r->getFeeName(),
				'icon'        => $platform && $platform->isCashOnWeb() ?  $platform->getUrl('tranIcon') : '/static/usu/img/txn/debit_3d.jpg',
				'amount'      => $amount,
				'amountValue' => $amountValue,
				'timestamp'   => $r->getTime()->getTimestamp(),
				'action'      => NULL,
			];
		}
		return $items;
	}

	protected function getLoadTransactions(Collection $ucs)
	{
		$items = [];
		$platform = Util::$platform;
		/** @var UserCard $uc */
		foreach ($ucs as $uc) {
			if (!$uc->isIssued()) {
				continue;
			}
			$cpName = $uc->getCardProgram()->getName();
			$loads = $uc->getLoads();
			/** @var UserCardLoad $ucl */
			foreach ($loads as $ucl) {
				if (!$ucl->isValid() ||
					($platform && $platform->isCashOnWeb() && $ucl->getPartner() && ($ucl->getId() !== 384742))) {
					continue;
				}

				$status = $ucl->getLoadStatus();
				if (in_array($status, [
						UserCardLoad::LOAD_STATUS_INITIATED,
						UserCardLoad::LOAD_STATUS_PENDING,
					]) && $ucl->isArchived()) {
					continue;
				}

				$pendingToLoad = /* (Util::meta($ucl, 'splitToLoads') || Util::meta($ucl, 'sentExceedMaxBalanceMail')) && */
                                 $status === UserCardLoad::LOAD_STATUS_RECEIVED;
				$fromBalanceBy = Util::meta($ucl, 'fromUpdatePrivacyBalanceBy');
				if ($fromBalanceBy && !$pendingToLoad) {
					continue;
				}

				$timeObj = $ucl->getLoadAt() ?: $ucl->getReceivedAt() ?: $ucl->getInitializedAt() ?: $ucl->getCreatedAt();
				$timestamp = $timeObj->getTimestamp();

				$feeUSD = 0;
				$loadAmount = $ucl->getLoadAmountUSD();
                $loadedStatuses = [
                    // Ensure fees are charged
                    UserCardLoad::LOAD_STATUS_LOADED,
                    UserCardLoad::LOAD_STATUS_REFUNDED,
                ];
                $showFees = ($loadAmount || $ucl->isUnload()) && in_array($status, $loadedStatuses);
                if (Util::meta($ucl, 'manuallyLoadShowFee')) {
                    $showFees = true;
                }
				if ($showFees) {
					$fees = [];
					foreach (Util::s2j($ucl->getFeeStructure()) as $key => $fee) {
						if (!$fee) {
							continue;
						}
						if (in_array($key, [
							'loadFee',
							'membershipFee',
//                            'replaceFee',
							'unloadFee',
						])) {
							$fees[Util::title($key, true)] = -$fee;
						}

						// "otherFees": [
						//    {
						//      "type": "Pending membership or load fee",
						//      "amount": 399,
						//      "load": 293831,
						//      "text": "USD $3.99"
						//    }
						//  ],
						else if ($key === 'otherFees' && $fee) {
							try {
								foreach ($fee as $_fee) {
									$fees[Util::title($_fee['type'], true)] = -$_fee['amount'];
								}
							} catch (\Exception $e) {}
						}
					}
					$index = 0;
					foreach ($fees as $name => $amountValue) {
						$items[] = [
							'type'        => 'fee',
							'title'       => $cpName,
							'subtitle'    => $name,
							'icon'        => $uc->getCardProgram()->getPlatform()->isCashOnWeb() ?  $uc->getCardProgram()->getPlatform()->getUrl('tranIcon') : '/static/usu/img/txn/debit_3d.jpg',
							'amount'      => UsuService::formatMoney($amountValue),
							'amountValue' => $amountValue,
							'timestamp'   => $timestamp + $index + 1,
						];
						$index++;

						if ($name !== 'Unload Fee') {
							$feeUSD += -$amountValue;
						}
					}
				}

				$unload      = $ucl->isUnload();
				$amountValue = $loadAmount ?: $ucl->getInitialAmount();
				$amountValue = ($unload ? -1 : 1) * $amountValue;
				$amount = UsuService::formatMoney($amountValue);

				if (!in_array($status, [
					UserCardLoad::LOAD_STATUS_LOADED,
					UserCardLoad::LOAD_STATUS_REFUNDED,
				])) {
					$amountValue = 0;
				} else if (!$loadAmount) { // Received amount is not enough for the fees
					$amountValue = 0;
					$amount = UsuService::formatMoney($amountValue);
				} else {
					$amountValue += $feeUSD;
					$amount = UsuService::formatMoney($amountValue);
				}

				$action = null;
				if (!$unload) {
					if ( ! in_array($status, [
                        UserCardLoad::LOAD_STATUS_UNKNOWN,
                        UserCardLoad::LOAD_STATUS_INITIATED,
                        UserCardLoad::LOAD_STATUS_PENDING,
                    ])) {
						$action = [
							'event' => 'show-load-card-dialog-status',
                            'status' => ucwords($status),
						];
					} else {
						$action = [
							'event' => 'show-load-card-dialog-instruction',
						];
					}
				}

				$subClass = 'text-danger heavy';
				$statusFormat = $ucl->getLoadStatusFormat();
				if ($statusFormat === 'Processing') {
					$subClass = 'text-warning heavy';
				} else if ($statusFormat === 'Ready to Spend') {
					$subClass = 'text-success';
				}

				$iconBoxClass = '';
				$method = $ucl->getMethod();
				if ($unload) {
					$title = 'Unload account';
					$icon = '/static/usu/img/txn/debit_3d.jpg';
				} else {
					$title = $ucl->getMethodName(short: true) . ' Load';
					$icon = '/static/usu/img/txn/credit_3d.jpg';

					if ($method && !$method->is(LoadMethod::PAYNAME_SYSTEM)) {
						$icon = $method->getIconUrl();
						$iconBoxClass = 'load_method';

                        $squareIcon = $method->getSquareIcon();
                        if ($squareIcon) {
                            $icon = $squareIcon;
                            $iconBoxClass = '';
                        }
					}

                    if ($title === ' Load') {
                        $title = Stringy::create($ucl->getType())->humanize()->titleize();
                    }
				}

				if ($method && $method->is(LoadMethod::PAYNAME_SYSTEM)) {
					$icon = $amountValue >= 0 ? '/static/usu/img/txn/credit_3d.jpg' : '/static/usu/img/txn/debit_3d.jpg';
					$title = $cpName;
				}

                $partner = $ucl->getPartner();
                if ($partner && $partner->is(LoadPartner::SYSTEM)) {
                    $description = Util::meta($ucl, 'systemLoadDescription');
                    if ($description) {
                        $statusFormat = $description;
                        $subClass = '';
                    }
                }

				// The voided transaction's amount is too high, so it was split
				if ($fromBalanceBy && $pendingToLoad) {
					$statusFormat = 'Pending to load';
					$subClass = 'text-warning';
					$amountValue = -$ucl->getReceivedAmountUSD();
					$amount = UsuService::formatMoney($amountValue);
				}

				$createdBy = $ucl->getCreateBy();
				$createdByInfo = null;
				if ($createdBy){
					$createdByInfo = User::find($createdBy);
				}

				$items[]     = [
					'type'        => $unload ? 'unload' : 'load',
					'id' => $ucl->getId(),
					'title'       => $title,
					'subtitle'    => $statusFormat,
					'subClass'    => $subClass,
					'subTip'      => $ucl->getLoadError(),
					'icon'        => $uc->getCardProgram()->getPlatform()->isCashOnWeb() ?  $uc->getCardProgram()->getPlatform()->getUrl('tranIcon') : $icon,
					'iconBoxClass' => $iconBoxClass,
					'amount'      => $amount,
					'amountValue' => $amountValue,
					'timestamp'   => $timestamp,
					'action' => !$unload && $uc->getCardProgram()->getPlatform()->isCashOnWeb() ? ['event' => 'show-transaction-detail'] : $action,
					'location'    => $createdBy && $createdByInfo ? $createdByInfo->getLoadLocator() ? $createdByInfo->getLoadLocator()->getName() : '' : '',
				];
			}
		}
		return $items;
	}

	/**
	 * @Route("/p/history/detail")
	 *
	 * @param Request $request
	 *
	 * @return SuccessResponse
	 */
	public function detail(Request $request)
	{
		$type = $request->get('type');
		$id = $request->get('id');

		$data = null;

		return new SuccessResponse([
			'type' => $type,
			'data' => $data,
		]);
	}

	/**
	 * @Route("/p/history/view-transaction/{token}")
	 *
	 * @param $token
	 *
	 * @return SuccessResponse
	 */
    public function viewTransaction($token)
    {
        $result = [
            'data' => NULL,
            'meta' => NULL,
        ];
        $uct    = UserCardTransaction::findByTranId($token);
        $ucd    = null;
        $uc     = NULL;
        if ($uct) {
            $result['meta'] = Util::meta($uct);
            $uc             = $uct->getUserCard();
        } else {
            $ucd = UserCardDecline::findByTranId($token);
            if ($ucd) {
                $result['meta'] = Util::meta($ucd);
                $uc             = $ucd->getUserCard();
            }
        }
        if (!$uc) {
            throw PortalException::temp('Unknown transaction!');
        }
        $service = CardProcessorHub::getForUserCard($uc);
        list(, $data) = $service->viewTransaction($token);
        if ($data) {
            $data['isPrivacy'] = TRUE;
            if ($uc->isRainCard()) {
                $merchant = MerchantService::updateFromRain($uct ?? $ucd, $data['spend']);

                $data['isPrivacy'] = FALSE;
                $data['created']   = $data['spend']['authorizedAt'];
                $data['status']    = strtoupper($data['spend']['status']);
                $data['amount']    = Money::format($data['spend']['amount'], 'USD', FALSE);
                $data['reason']    = !empty($data['spend']['declinedReason']) ? $data['spend']['declinedReason'] : '';
                $data['card']      = [
                    'created'   => Util::formatApiDateTime($uc->getIssuedAt()),
                    'last_four' => $uc->getPan(TRUE),
                    'card_name' => strtoupper($uc->getNickName()),
                    'state'     => strtoupper($uc->getStatus()),
                    'type'      => $uc->getCardTypeName(),
                ];
                $data['merchant']  = [
                    'name'         => $data['spend']['merchantName'],
                    'category'     => strtoupper($data['spend']['merchantCategory']),
                    'categoryCode' => strtoupper($data['spend']['merchantCategoryCode']),
                    'city'         => $data['spend']['merchantCity'],
                    'state'        => $merchant->getEnrichedName(),
                    'country'      => $data['spend']['merchantCountry'],
                    'icon'         => $merchant->getIconUrl(),
                ];
            } else {
                $data['amount']         = Money::format($data['amount'], 'USD', FALSE);
                $data['settled_amount'] = Money::format($data['settled_amount'], 'USD', FALSE);
                $data['card'] = [
                    'created'   => Util::formatApiDateTime($uc->getIssuedAt()),
                    'last_four' => $uc->getPan(TRUE),
                    'card_name' => $uc->getNickName() ?? strtoupper($uc->getNickName()),
                    'state'     => $uc->getNativeStatus(),
                    'type'      => $uc->getType(),
                ];

                if ($data['events']) {
                    foreach ($data['events'] as $key => $event) {
                        $data['events'][$key]['amount'] = Money::format(-$event['amount'], 'USD', FALSE);
                    }
                }
            }
        }
        $result['data'] = $data;
        if ($result['meta'] && isset($result['meta']['privacyDetail'])) {
            $privacyDetail                                     = $result['meta']['privacyDetail'];
            $result['meta']['privacyDetail']['amount']         = Money::format($privacyDetail['amount'], 'USD', FALSE);
            $result['meta']['privacyDetail']['settled_amount'] = Money::format($privacyDetail['settled_amount'], 'USD', FALSE);
            if ($privacyDetail['events']) {
                foreach ($privacyDetail['events'] as $key => $event) {
                    $result['meta']['privacyDetail']['events'][$key]['amount'] = Money::format(-$event['amount'], 'USD', FALSE);
                }
            }
        }
        if ($result['meta'] && isset($result['meta']['rainDetail'])) {
            $rainDetail                                           = $result['meta']['rainDetail'];
            $result['meta']['rainDetail']['created']              = $rainDetail['spend']['authorizedAt'] ?? null;
            $result['meta']['rainDetail']['status']               = strtoupper($rainDetail['spend']['status']);
            $result['meta']['rainDetail']['merchantCategory']     = strtoupper($rainDetail['spend']['merchantCategory']);
            $result['meta']['rainDetail']['merchantCategoryCode'] = strtoupper($rainDetail['spend']['merchantCategoryCode']);

            $result['meta']['rainDetail']['amount'] = Money::format($rainDetail['spend']['amount'], 'USD', FALSE);
        }

        return new SuccessResponse($result);
    }
}
