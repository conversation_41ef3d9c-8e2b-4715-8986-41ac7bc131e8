<template>
  <div class="row"
       id="index-cards"
       v-if="!$store.state.User.loadLocator">
    <abstract></abstract>
    <cards></cards>
    <history></history>

    <HelpLegacyBalance></HelpLegacyBalance>
    <CardDetailPopup></CardDetailPopup>
    <SetupTwoFactorPopup></SetupTwoFactorPopup>
    <VerifyOwnerPopup></VerifyOwnerPopup>
    <InviteFriendDialog></InviteFriendDialog>
    <FfDialog></FfDialog>
    <SwitchPaypalDialog></SwitchPaypalDialog>
    <HistoryDetailPopup></HistoryDetailPopup>
  </div>
  <div v-else>
    <loadLocator></loadLocator>
  </div>
</template>

<script>
  import abstract from './abstract'
  import cards from './cards'
  import history from './history'
  import FfDialog from './abstract/ff-dialog.vue'
  import HelpLegacyBalance from './common/help-legacy-balance'
  import CardDetailPopup from './common/card-detail-popup'
  import SetupTwoFactorPopup from './common/setup-two-factor-popup'
  import VerifyOwnerPopup from './common/verify-owner-popup'
  import HistoryDetailPopup from './history/history-detail-popup'
  import InviteFriendDialog from './common/invite-friend-dialog'
  import SwitchPaypalDialog from './common/switch-paypal-dialog'

  import loadLocator from './loadLocator'

  export default {
    components: {
      FfDialog,
      InviteFriendDialog,
      abstract,
      cards,
      history,
      HelpLegacyBalance,
      CardDetailPopup,
      loadLocator,
      SetupTwoFactorPopup,
      VerifyOwnerPopup,
      HistoryDetailPopup,
      SwitchPaypalDialog
    },
    mounted () {
      this.$nextTick(() => {
        setTimeout(() => {
          const u = new URL(location.href)
          const evt = u.searchParams.get('evt');
          if (evt) {
            this.$root.$emit(evt)
          }
        }, 1000)
      })
    }
  }
</script>

<style lang="scss">
#index-cards {
  height: 100%;
  min-width: 900px;

  > .col.card {
    background: white;
    border-radius: 10px;
    padding: 0;
    margin: 0 7px;
    border-color: #e7e8f3;
    height: 100%;
    overflow: auto;

    .card-body {
      height: 100%;
      overflow: auto;
      padding: 20px 24px 0;
    }

    .title > {
      h4 {
        font-size: 1.3rem;
        margin-bottom: 0;
        color: #0a2251;
      }

      .sub-title {
        color: #888;
        font-weight: 300;
      }
    }
  }
}

#index-card-abstract {
  margin-left: 14px !important;
}

#index-card-history {
  margin-right: 14px !important;
}

#index-card-cards,
#index-card-history {
  > .card-body {
    display: flex;
    flex-direction: column;
  }
}

@media (max-width: 1034px) {
  #index-cards {
    padding: 0 4px;

    > .col.card {
      margin: 0 5px;

      .card-body {
        padding: 15px 15px 0;
      }
    }
  }

  #index-card-abstract {
    margin-left: 8px !important;
  }

  #index-card-history {
    margin-right: 8px !important;
  }
}
</style>
