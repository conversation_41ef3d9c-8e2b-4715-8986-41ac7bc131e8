{% extends '@UsUnlocked/layout/base.html.twig' %}

{% block head %}
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-select@1.13.9/dist/css/bootstrap-select.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/ion-rangeslider@2.3.1/css/ion.rangeSlider.min.css">
    {% block platformCss %} <link rel="stylesheet" href="/static/usun/index.css?v={{ _v() }}">  {% endblock %}

    {% block head_index %}{% endblock %}
{% endblock %}

{% block body %}
    {% if alerts is not defined %}
        {% set alerts = [] %}
    {% endif %}

    <nav class="navbar navbar-expand fixed-top navbar-light bg-white" id="header">
        <div class="container-xl">
            <a class="navbar-brand logo" href="/">
                <img src="{{ _cp.logoUrl }}" alt="">
            </a>
            {% if not cashOnWeb() %}
                <a href="javascript:"
                   onclick="ts.goPortalEvent('show-invite-friend-dialog')"
                   class="btn btn-success ml-auto mr-25 d-sm-none">
                    <i class="fa fa-fw fa-share-alt"></i> Invite Friends!
                </a>
                <a href="javascript:"
                   onclick="ts.goPortalEvent('show-invite-friend-dialog')"
                   class="btn btn-success ml-auto mr-25 d-none d-sm-block">
                    <i class="fa fa-fw fa-share-alt"></i> Invite Your Friends!
                </a>
{#                <a href="javascript:"#}
{#                   onclick="ts.goPortalEvent('show-refer-friend-dialog')"#}
{#                   class="btn btn-success ml-auto mr-25 d-none d-sm-block">#}
{#                    <i class="fa fa-fw fa-dollar-sign"></i> Earn Cash Now#}
{#                </a>#}
            {% endif %}
            {# Notifications #}

            <script>
                let seenArray = []
                let jobsArray = []
                let alertArray = []
                let systemMessage = 0
            </script>

            <div class="dropdown   {% if cashOnWeb() %} ml-auto {% endif %}" id="notification_dropdown">
                <div class="ml-auto d-flex user-info user-alert-info" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <div class="notification-bell">
                        <div class="img w-30 mr-10">
                            <img src="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/PjwhRE9DVFlQRSBzdmcgIFBVQkxJQyAnLS8vVzNDLy9EVEQgU1ZHIDEuMS8vRU4nICAnaHR0cDovL3d3dy53My5vcmcvR3JhcGhpY3MvU1ZHLzEuMS9EVEQvc3ZnMTEuZHRkJz48c3ZnIGhlaWdodD0iNTEycHgiIGlkPSJMYXllcl8xIiBzdHlsZT0iZW5hYmxlLWJhY2tncm91bmQ6bmV3IDAgMCA1MTIgNTEyOyIgdmVyc2lvbj0iMS4xIiB2aWV3Qm94PSIwIDAgNTEyIDUxMiIgd2lkdGg9IjUxMnB4IiB4bWw6c3BhY2U9InByZXNlcnZlIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj48Zz48cGF0aCBkPSJNMzgxLjcsMjI1LjljMC05Ny42LTUyLjUtMTMwLjgtMTAxLjYtMTM4LjJjMC0wLjUsMC4xLTEsMC4xLTEuNmMwLTEyLjMtMTAuOS0yMi4xLTI0LjItMjIuMWMtMTMuMywwLTIzLjgsOS44LTIzLjgsMjIuMSAgIGMwLDAuNiwwLDEuMSwwLjEsMS42Yy00OS4yLDcuNS0xMDIsNDAuOC0xMDIsMTM4LjRjMCwxMTMuOC0yOC4zLDEyNi02Ni4zLDE1OGgzODRDNDEwLjIsMzUyLDM4MS43LDMzOS43LDM4MS43LDIyNS45eiIvPjxwYXRoIGQ9Ik0yNTYuMiw0NDhjMjYuOCwwLDQ4LjgtMTkuOSw1MS43LTQzSDIwNC41QzIwNy4zLDQyOC4xLDIyOS40LDQ0OCwyNTYuMiw0NDh6Ii8+PC9nPjwvc3ZnPg=="
                                 class="" alt="">
                            <span class="badge"></span>
                        </div>
                        <div class="status"></div>
                    </div>
                </div>

                <div class="dropdown-menu dropdown-menu-right dropdown-alert">
                    <h6 class="dropdown-header">
                        <span class=" text-shadow mr-2">Notifications</span>
                    </h6>

                    {% for alert in alerts %}

                        <div id="{{ alert.job }}">
                            <div class="d-flex flex-container" onmouseover="removeDot('{{ alert.job }}')">
                                    <a href="{{ alert.link }}" class="dropdown-item d-flex flex-row justify-between alert-hover" type="button">
                                        {% if alert.icon is same as('alert') %}
                                            <svg xmlns="http://www.w3.org/2000/svg" width="50" height="70" viewBox="0 0 24 24" class="oval">
                                                <g fill="none" fill-rule="evenodd">
                                                    <g fill="{{ alert.color }}">
                                                        <path d="M11.66 1.06c.22-.08.46-.08.68 0l9 3.25c.396.143.66.519.66.94V12c0 5.5-3.329 9.198-9.734 10.964-.172.047-.353.048-.525.002C5.33 21.246 2 17.545 2 12V5.25c0-.421.264-.797.66-.94l9-3.25zM12 3.062l-8 2.89V12c0 4.497 2.585 7.44 7.997 8.962C17.412 19.4 20 16.454 20 12V5.952l-8-2.889zm-.95 3.867c0-.552.447-1 1-1 .552 0 1 .448 1 1v6.613c0 .552-.448 1-1 1-.553 0-1-.448-1-1V6.93zM12 18c-.552 0-1-.448-1-1s.448-1 1-1 1 .448 1 1-.448 1-1 1z"/>
                                                    </g>
                                                </g>
                                            </svg>

                                        {% elseif alert.icon is same as('warning') %}
                                            <svg xmlns="http://www.w3.org/2000/svg" width="50" height="70" viewBox="0 0 24 24">
                                                <g fill="none" fill-rule="evenodd">
                                                    <g fill="{{ alert.color }}">
                                                        <path d="M12 2c5.523 0 10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12 6.477 2 12 2zm0 2c-4.418 0-8 3.582-8 8s3.582 8 8 8 8-3.582 8-8-3.582-8-8-8zm0 11c.552 0 1 .448 1 1s-.448 1-1 1-1-.448-1-1 .448-1 1-1zm0-8c.552 0 1 .448 1 1v5c0 .552-.448 1-1 1s-1-.448-1-1V8c0-.552.448-1 1-1z"/>
                                                    </g>
                                                </g>
                                            </svg>
                                        {% elseif alert.icon is same as('update') %}
                                            <svg xmlns="http://www.w3.org/2000/svg" width="50" height="70" viewBox="0 0 24 24">
                                                <g fill="none" fill-rule="evenodd">
                                                    <g>
                                                        <path d="M0 0L24 0 24 24 0 24z"/>
                                                        <path fill="{{ alert.color }}" d="M7.026 8.924v-1.83C7.026 4.284 9.28 2 12.07 2c2.79 0 5.045 2.284 5.045 5.094v1.83h1.428c.812 0 1.457.666 1.457 1.472v10.132c0 .806-.645 1.472-1.457 1.472H5.457C4.645 22 4 21.334 4 20.528V10.396c0-.806.645-1.472 1.457-1.472h1.569zm-1.203 1.818v9.44h12.354v-9.44H5.823zm9.47-1.846V7.094c0-1.812-1.446-3.276-3.223-3.276-1.776 0-3.222 1.464-3.222 3.276v1.802h6.445z"/>
                                                    </g>
                                                </g>
                                            </svg>
                                        {% elseif alert.icon is same as('promotion') %}
                                            <svg xmlns="http://www.w3.org/2000/svg" width="50" height="70" viewBox="0 0 24 24">
                                                <g fill="none" fill-rule="evenodd">
                                                    <g>
                                                        <circle cx="12" cy="12" r="12"/>
                                                        <g fill="{{ alert.color }}">
                                                            <path d="M13.5 0c1.763 0 3.222 1.304 3.465 3H20c1.105 0 2 .895 2 2v2c0 1.105-.895 2-2 2v11c0 1.105-.895 2-2 2H4c-1.105 0-2-.895-2-2V9C.895 9 0 8.105 0 7V5c0-1.105.895-2 2-2h3.035C5.278 1.304 6.737 0 8.5 0c.98 0 1.866.403 2.501 1.052C11.634.402 12.52 0 13.5 0zM10 9H4v11h6V9zm8 0h-6v11h6V9zm-8-4H2v2h8V5zm10 0h-8v2h8V5zm-6.356-2.993L13.5 2c-.653 0-1.209.418-1.415 1h2.83c-.206-.582-.762-1-1.415-1zM8.5 2c-.653 0-1.209.418-1.415 1h2.83c-.206-.582-.762-1-1.415-1z" transform="translate(1 1)"/>
                                                        </g>
                                                    </g>
                                                </g>
                                            </svg>
                                        {% elseif alert.icon is same as('blog') %}
                                            <svg xmlns="http://www.w3.org/2000/svg" width="50" height="70" viewBox="0 0 24 24">
                                                <g fill="none" fill-rule="evenodd">
                                                    <g>
                                                        <circle cx="12" cy="12" r="12"/>
                                                        <g fill="{{ alert.color }}">
                                                            <path d="M18.957 7.907c0-.55.447-.997.998-.997.55 0 .997.446.997.997v9.916c0 2.307-1.87 4.177-4.177 4.177H6.177C3.87 22 2 20.13 2 17.823V7.225c0-2.307 1.87-4.177 4.177-4.177h9.6c.552 0 .998.446.998.997 0 .551-.446.998-.997.998H6.177c-1.205 0-2.182.977-2.182 2.182v10.598c0 1.205.977 2.182 2.182 2.182h10.598c1.205 0 2.182-.977 2.182-2.182V7.907zm.88-5.597c.38-.4 1.012-.415 1.41-.035.4.38.415 1.011.035 1.41l-9.55 10.03c-.38.399-1.011.414-1.41.034-.4-.38-.415-1.011-.035-1.41l9.55-10.03z" transform="translate(1 1)"/>
                                                        </g>
                                                    </g>
                                                </g>
                                            </svg>
                                        {% elseif alert.icon is same as('shopping') %}
                                            <svg xmlns="http://www.w3.org/2000/svg" width="50" height="70" viewBox="0 0 24 24">
                                                <g fill="none" fill-rule="evenodd">
                                                    <g>
                                                        <circle cx="12" cy="12" r="12"/>
                                                        <g fill="{{ alert.color }}">
                                                            <path d="M6.276 4c.32 0 .611.171.766.453l.046.098.032.102.354 1.536 13.664.001c.203 0 .397.069.55.191l.074.066.065.074c.113.145.174.32.173.512l-.008.117-.023.105-2.275 7.665c-.085.285-.317.501-.622.578l-.104.02-.105.007H8.55c-.15 0-.272.118-.272.26 0 .11.073.206.164.242l.047.013.062.005h10.312c.477 0 .864.373.864.835 0 .384-.269.712-.658.809l-.1.02-.073.004c.355.349.574.828.574 1.357 0 1.064-.89 1.93-1.983 1.93-1.094 0-1.983-.866-1.983-1.93 0-.527.218-1.006.571-1.355h-4.772c.353.35.571.828.571 1.355 0 1.064-.889 1.93-1.983 1.93s-1.983-.866-1.983-1.93c0-.529.22-1.008.574-1.357-1.072-.035-1.932-.885-1.932-1.928 0-.63.317-1.205.826-1.561l.12-.079.039-.021L5.584 5.67h-2.72c-.41 0-.755-.277-.843-.65l-.016-.094L2 4.835c0-.384.27-.712.659-.809l.1-.02L2.864 4h3.412zm3.615 13.961c.629 0 1.14.498 1.14 1.109 0 .612-.511 1.109-1.14 1.109-.628 0-1.14-.497-1.14-1.109 0-.611.512-1.109 1.14-1.109zm7.596 0c.628 0 1.14.498 1.14 1.109 0 .612-.512 1.109-1.14 1.109-.629 0-1.14-.497-1.14-1.109 0-.611.511-1.109 1.14-1.109zM19.99 7.86H7.86l1.384 5.995h8.968L19.99 7.86z"/>
                                                        </g>
                                                    </g>
                                                </g>
                                            </svg>
                                        {% endif %}
                                    <div class=" flex">
                                        <div class="alert-body">
                                            <p class="alert-title">{{ alert.name}}</p>
                                            <div class="alert-message ">{{ alert.message | raw}}</div>

{#                                            <div class="p-20">#}
{#                                                <p><strong>Welcome Back to US Unlocked</strong></p>#}
{#                                                <p>You’re in — and the new platform is live.</p>#}
{#                                                <ul>#}
{#                                                    <li>Your new cards offer more flexibility, more load options, and instant issue.</li>#}
{#                                                    <li>You can now invite friends with <strong>exclusive referral codes</strong>.</li>#}
{#                                                    <li>Each code lets them <strong>skip the waitlist</strong> and unlock full access.</li>#}
{#                                                </ul>#}
{#                                                <div><strong>You have 10 private invites.</strong></div>#}
{#                                                <p>Use them wisely — they do not reset.</p>#}
{#                                                <hr />#}
{#                                                <div onclick="event.stopPropagation(); event.preventDefault(); $('#notification_dropdown').dropdown('hide'); ts.goPortalEvent('show-invite-friend-dialog')"#}
{#                                                     class="text-danger pointer underline">Copy My Referral Link</div>#}
{#                                            </div>#}
                                        </div>
                                    </div>
                                    {% if alert.job %}
                                        <div class="viewed-dot" id="{{ alert.job }}-dot"></div>
                                    {% endif %}
                                </a>
                                {% if alert.job %}
                                  <a class="alert-close" onclick="dismiss('{{ alert.job }}')"><i class="fa fa-times"></i></a>
                                {% endif %}
                            </div>
                            <div class="dropdown-divider"></div>
                        </div>

                        <script>
                            {% if not alert.job %}
                                alertArray.push({{ alert.id }})
                                systemMessage = 1
                            {% else %}
                                seenArray.push({{ alert.job }})
                                jobsArray.push({{ alert.job }})
                            {% endif %}
                        </script>

                    {% endfor %}

                    <div class="alert empty-message">
                        <div class="alert-body">
                            <p class="no-alerts-message">You have no notifications.</p>
                        </div>
                    </div>

                    <h6 class="dismiss-container">
                        <button class="dismiss-button" onclick="dismissAll()">Dismiss All</button>
                    </h6>

                </div>
            </div>

            <script>

                const removeDot = (job) => {
                    if (seenArray.find(function (element) {
                        return element === job
                    })) {
                        const index = seenArray.indexOf(job)
                        seenArray.splice(index, 1)
                        let $dot = $(`#${job}-dot`)
                        $dot.remove()
                        updateAlertBubble();
                    }
                }

                const updateAlertBubble = () => {
                    const $badge = $('.badge');
                    const count = seenArray.length + alertArray.length;
                    if (count > 0) {
                        $badge.text(count);
                        $('.empty-message').hide()
                    } else {
                        $badge.text(null)
                        $('.empty-message').show()
                    }
                }

                const dismissAll = () => {
                    for (let job of jobsArray) {
                        let $job = $(`#${job}`)
                        $job.remove()
                        deleteAlert(job)
                    }
                    seenArray = []
                    jobsArray = []
                    updateAlertBubble()
                }

                const dismiss = (job) => {
                    let $job = $(`#${job}`)
                    $job.remove()
                    let index = jobsArray.indexOf(job)
                    jobsArray.splice(index, 1)
                    updateAlertBubble()
                    deleteAlert(job)
                }

                const deleteAlert = (job) => {
                    let settings = {
                        "url": `p/alert_job/delete/{{ user.id }}/${job}`,
                        "method": "POST",
                        "timeout": 0,
                    };

                    $.ajax(settings).done(function (response) {
                    });
                }

                $(document).ready(function() {
                    $('.empty-message').hide()
                    updateAlertBubble();

                    $('.toast').toast('show')
                })

            </script>

            {# Notifications #}

            <div class="dropdown">
                <div class="ml-auto d-flex user-info" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <div class="mr-10 d-none d-sm-block">Welcome, {{ user.firstName }}</div>
                    <div class="avatar-box">
                        <div class="img">
                            <img src="{{ user.avatarUrl | default('/static/img/avatar.png') }}"
                                 class="{{ user.avatarUrl ? '' : 'default' }}" alt="">
                        </div>
                        <div class="status"></div>
                    </div>
                    <i class="fa fa-caret-down"></i>
                </div>
                <div class="dropdown-menu dropdown-menu-right">
                    <h6 class="dropdown-header">
                        <span class=" text-shadow mr-2">My</span>
                        <img class="shadow" src="{{ _cp.logoUrl }}" alt="">
                    </h6>
                    <div class="email-info">{{ user.email }}</div>

                    {% if not user.isUsuSumSubIdVerified() and not user.isRegisterRainUser() %}
                        <a href="javascript:" class="dropdown-item heavy text-danger"
                           onclick="ts.goPortalEvent('show-kyc-dialog')"
                           type="button"><i class="fas fa-fw fa-user-check"></i> Verify My Identity</a>

                        <div class="dropdown-divider"></div>
                    {% endif %}
                    <a href="/p" class="dropdown-item" type="button"><i class="fa fa-fw fa-credit-card"></i> My Cards</a>
                    <a href="/p/profile" class="dropdown-item" type="button"><span class="fa fa-fw fa-cog"></span> My Settings</a>
                    {% if user.canSwitchPaypalPlan() %}
                        <a href="javascript:" class="dropdown-item" type="button"
                            onclick="ts.goPortalEvent('show-switch-paypal-plan-dialog')">
                            <i class="fa fa-fw fa-exchange-alt"></i> Switch To Annual
                        </a>
                    {% endif %}
                    <a href="javascript:" class="dropdown-item" type="button"
                       onclick="ts.goPortalEvent('show-help-tutorial')">
                        <i class="fa fa-fw fa-question-circle"></i> Help
                    </a>
                    <div class="dropdown-divider"></div>

                    {% if not cashOnWeb() %}
                        <a href="javascript:" class="dropdown-item" type="button"
                            onclick="ts.goPortalEvent('show-invite-friend-dialog')">
                            <i class="fa fa-fw fa-share-alt"></i> Invite Friends
                        </a>
{#                        <a href="javascript:" class="dropdown-item" type="button"#}
{#                           onclick="ts.goPortalEvent('show-refer-friend-dialog')">#}
{#                            <i class="fa fa-fw fa-dollar-sign"></i> Refer Friends, Earn Cash#}
{#                        </a>#}

                        <div class="dropdown-divider"></div>
                    {% endif %}
                    <a href="/logout" class="dropdown-item"><span class="fa fa-fw fa-sign-out-alt"></span> Logout</a>

                    {% if is_granted('IS_IMPERSONATOR') and _impersonatingUser %}
                        <div class="dropdown-divider"></div>
                        <a href="/login_as/exit?url=/admin" class="dropdown-item">
                            <i class="fa fa-external-link-alt"></i>
                            Go Back
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </nav>

    <div class="container-xl" id="root">
        {% block body_content %}{% endblock %}
    </div>

    <nav class="navbar navbar-expand fixed-bottom navbar-light bg-white" id="footer">
        <div class="container-xl">
            <a href="https://www.ternitup.com/" target="_blank" class="navbar-brand mr-auto logo">
                <img src="/static/img/tern_logo_new.svg" alt="">
            </a>
        </div>
    </nav>

{#    <div class="toast snack" style="position: absolute; bottom: 20px; z-index: 9999; background-color: #fff2f2; min-width: 100%; text-align: center" data-autohide="true" data-delay="15000">#}
{#        <div class="toast-header" style="align-content: center">#}
{#            <img src="..." class="rounded mr-2" alt="...">#}
{#            <strong class="mr-auto">Bootstrap</strong>#}
{#            <small>11 mins ago</small>#}
{#            <button type="button" class="ml-2 mb-1 close" data-dismiss="toast" aria-label="Close">#}
{#                <span aria-hidden="true">&times;</span>#}
{#            </button>#}
{#        </div>#}
{#        <div class="toast-body">#}
{#            Hello, world! This is a toast message.#}
{#        </div>#}
{#    </div>#}

{% for alert in alerts %}

    {% if alert.type is same as('snack') %}

        <div class="toast snack" style="position: absolute; z-index: 9999; bottom: 0; background-color: {{ alert.color }}; min-width: 100%; text-align: center; color: #ffffff" data-autohide="false">
            <div class="toast-header" style="align-content: center">
                <strong class="mr-auto">{{ alert.name }}</strong>
                <button type="button" class="ml-2 mb-1 close" data-dismiss="toast" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="toast-body">
                {{alert.message}}
            </div>
        </div>

    {% endif %}

{% endfor %}


{% endblock %}

{% block foot %}
    <script src="https://cdn.jsdelivr.net/npm/bootstrap-select@1.13.9/dist/js/bootstrap-select.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/ion-rangeslider@2.3.1/js/ion.rangeSlider.min.js"></script>
    {% block platformJs %}<script src="/static/usun/index.js?v={{ _v() }}"></script>{% endblock %}

    {% block foot_index %}{% endblock %}
    {% if cashOnWeb() %}
        <script src="https://maps.googleapis.com/maps/api/js?key={{ util('googleFrontendKey') }}"></script>
    {% endif %}
{% endblock %}
