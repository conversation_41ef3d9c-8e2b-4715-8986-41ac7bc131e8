<?php

namespace UsUnlockedBundle;

use Symfony\Component\HttpKernel\Bundle\Bundle;

class UsUnlockedBundle extends Bundle
{
    public const KYC_ON_ALL = true;

    public const TERMS_FIELDS = [
        'eSign',
        'agreements',
        'accuracy',
        'solicitation',
    ];

    public const BLOCKED_COUNTRIES = [
        'AF', // Afghanistan
        'AL', // Albania
        'BA', // Bosnia and Herzegovina
        'BY', // Belarus
        'BF', // Burkina Faso
        'BI', // Burundi
        'CF', // Central African Republic
        'CU', // Cuba
        'CD', // Democratic Republic of the Congo
        'CS', // Serbia & Montenegro ----- become 'RS' and 'ME'
        'ER', // Eritrea
        'HT', // Haiti
        'IR', // Iran
        'IQ', // Iraq
        'XK', // Kosovo
        'LB', // Lebanon
        'LR', // Liberia
        'LY', // Libya
        'MD', // Moldova
        'ME', // Montenegro
        'MK', // North Macedonia
        'MM', // Myanmar (Burma)
        'KP', // North Korea
        'RU', // Russia
        'RS', // Serbia
        'SO', // Somalia
        'SY', // Syria
        'UA', // Ukraine
        'US', // United States
        'VE', // Venezuela
        'VN', // Vietnam
        'YE', // Yemen
        'ZW', // Zimbabwe
    ];

    /**
     * Unsupported by our card provider (Rain), but actually supported so we won't block
     */
    public const SKIP_MARKETING_COUNTRIES = [
        'TR', // Turkey
        'IL', // Israel
        'CN', // Mainland China
        'IN', // India
        'NP', // Nepal
    ];
}
