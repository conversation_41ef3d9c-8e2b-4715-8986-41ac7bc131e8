<?php

namespace PTOBundle;

use Core<PERSON><PERSON>le\Entity\Module;
use CoreBundle\Entity\Role;
use SalexUserBundle\Entity\User;
use Symfony\Component\HttpKernel\Bundle\Bundle;

class PTOBundle extends Bundle
{
    public static function getApiPrefix()
    {
        return '/api/pto/';
    }

    public static function getEnabledAPIs()
    {
        return [];
    }

    public static function getUserDashboardType()
    {
        return 'pto';
    }

//    public static function apiAdminUserProfile($data)
//    {
//        $data['cpKey'] = 'cp_pto';
//        $data['adminLayout'] = 'j'; // New admin portal layout
//        return $data;
//    }

    public static function getMemberRoles()
    {
        return [
            Role::ROLE_CONSUMER,
            Role::ROLE_FAAS_MEMBER,
        ];
    }

    public static function getAdminRoles()
    {
        return [
            Role::ROLE_ADMIN,
            Role::ROLE_MASTER_ADMIN,
            Role::ROLE_FAAS_ADMIN,
            Role::ROLE_FAAS_AGENT,
            Role::ROLE_FAAS_CLIENT,
        ];
    }

    public static function getMenusForCurrentUser()
    {
        return [
            [
                'id' => Module::ID_DEVELOPER,
                'name' => 'Developer Tools',
                'route' => '',
                'icon' => 'fa fa-fw fa-code',
                'mdIcon' => 'mdi-code-tags',
                'svgIcon' => false,
                'children' => [
                    [
                        'id' => Module::ID_DEV_DOCUMENTATION,
                        'name' => 'Documentation',
                        'route' => '/developer',
                        'mdRoute' => ''
                    ],
                    [
                        'id' => Module::ID_DEVELOPER_RESOURCES,
                        'name' => 'API Config',
                        'route' => '/admin/developer',
                        'mdRoute' => ''
                    ]
                ]
            ],
        ];
    }

    public static function getApiPermissionTree($default)
    {
        return [
            'Users' => [
                'Create User Profile' => 'api_pto_create_user_profile',
                'Update User Profile' => 'api_pto_update_user_profile',
                'Get User Profile' => 'api_pto_get_user_profile',
                'Get Users By Usertype' => 'api_pto_get_users_by_usertype',
                'Get User Widget Key' => 'api_pto_get_user_widget_key'
            ],
            'Payment Cards' => [
                'Create Payment Card' => 'api_pto_create_payment_card',
                'Update Payment Card' => 'api_pto_update_payment_card',
                'Get Payment Card Summary' => 'api_pto_get_payment_card_summary',
                'Get Payment Card Details' => 'api_pto_get_payment_card_details',
            ],
            'Transactions' => [
                'Push to Card' => 'api_pto_push_2_card',
                'Bank Account Transfer' => 'api_pto_bank_account_transfer',
                'Internal Account Transfer' => 'api_pto_internal_account_transfer'
            ],
            'Organizations' => [
                'Create Organization' => 'api_pto_create_org',
                'Update Organization' => 'api_pto_update_org',
                'Update Organization Tree' => 'api_pto_update_org_tree',
                'Get Organization Tree' => 'api_pto_get_org_tree',
                'Get Organization Info' => 'api_pto_get_org_info',
                'Create User Type' => 'api_pto_create_user_type',
                'Update User Type' => 'api_pto_update_user_type',
                'Get User Type' => 'api_pto_get_user_type',
                'Get Organization User Types' => 'api_pto_get_org_user_types'
            ],
            'Internal Accounts' => [
                'Create Internal Account' => 'api_pto_create_internal_account',
                'Get User Internal Account Summary' => 'api_pto_get_user_internal_account_summary',
                'Get User Internal Account Bank Details' => 'api_pto_get_internal_account_bank_details',
                'Get User Internal Account Balance' => 'api_pto_get_internal_account_balance'
            ],
            'External Accounts' => [
                'Create External Account' => 'api_pto_create_external_account'
            ],
            'Reports' => [
                'Get User Transaction Report' => 'api_pto_get_transaction_report_user',
                'Get Payment Transaction Report' => 'api_pto_get_transaction'
            ]
        ];
    }
}
