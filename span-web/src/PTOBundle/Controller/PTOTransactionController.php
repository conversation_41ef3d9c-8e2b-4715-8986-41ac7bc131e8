<?php


namespace PTOBundle\Controller;

use ApiBundle\Services\RPNService;
use Exception;
use PTOB<PERSON>le\Entity\PTOExternalAccount;
use PTOBundle\Entity\PTOInternalAccount;
use PTOB<PERSON>le\Entity\PTOPaymentCard;
use PTOB<PERSON>le\Entity\PTOTransaction;
use PTOBundle\Entity\PTOUser;
use PTOBundle\Entity\PTOUserType;
use Ramsey\Uuid\Uuid;
use OpenApi\Annotations as SWG;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\JsonResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Util;
use PTOBundle\Services\SlackService;


class PTOTransactionController extends \ApiBundle\Controller\BaseController
{
    private function transactionSuccessResponse(PTOTransaction $txn)
    {
        return new SuccessResponse([
            'Transaction ID' => $txn->getUuid(),
            'From User ID' => $txn->getFromUser(),
            'From Payment ID' => $txn->getFromUserPayment(),
            'To User ID' => $txn->getToUser(),
            'To Payment ID' => $txn->getToUserPayment(),
            'Transaction Amount' => $txn->getAmount(),
            'Transaction Type' => $txn->getTransactionType(),
            'Organization ID' => $txn->getOrganizationId(),
            'Transaction Status' => $txn->getStatus()
        ]);
    }

    /**
     * @Route("/api/pto/bank-funds-transfer", methods={"POST"}, name="api_pto_bank_funds_transfer")
     * @SWG\Post(
     *   tags={"Transactions"},
     *   summary="Initiate Bank Funds Transfer",
     *   description="Initiate a transfer of funds between accounts owned by the user. Can be used to move funds between external and internal accounts owned by the same user. Both the origin and destination bank can't be external.",
     *   @SWG\RequestBody(required=true, @SWG\MediaType(mediaType="application/x-www-form-urlencoded", schema=@SWG\Schema(required={"userUuid", "requestUuid", "fromAccountUuid", "toAccountUuid", "txnAmount"}, properties={
     *     @SWG\Property(property="userUuid", description="Unique ID for the user initiating the transaction. This user should be one that owns both bank accounts.", type="string"),
     *     @SWG\Property(property="requestUuid", description="Unique ID for the transaction. Will be provided by Tern if not provided by the organization.", type="string"),
     *     @SWG\Property(property="fromAccountUuid", description="Unique ID for the source of the funds. This must be an internal or external account.", type="string"),
     *     @SWG\Property(property="toAccountUuid", description="Unique ID for the destination bank account. Cannot be external if origin account is also external.", type="string"),
     *     @SWG\Property(property="txnAmount", description="Transaction amount in main units with decimal. $10 = 10.00", type="string"),
     *     @SWG\Property(property="currency", description="Currency for the transaction. Currently only USD is supported. This will default to USD unless specified otherwise.", type="string"),
     *   }))),
     *   security={{"x-api-key":{}, "x-timezone":{}, "x-i18n":{}, "x-token":{}}},
     *   @SWG\Response(response=200, ref="#/components/responses/Response"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     *
     * @return JsonResponse
     * @throws Exception
     */
    public function PTOBankTransfer(): JsonResponse
    {
        $request = $this->validateParameters(__METHOD__);

        $fromUser = PTOUser::findUserByUuid($request->get('userUuid'));
        $userType = PTOUserType::findUserTypeByUuid($fromUser->getUserTypeId());

        if (PTOTransaction::findTransactionByUuid($request->get('requestUuid')) !== NULL)
        {
            return new FailedResponse('This transaction request ID is already in use.', [
                'requestUuid' => $request->get('requestUuid')
            ], 409);
        }

        if ($fromUser === NULL)
        {
            return new FailedResponse('This user ID does not exist.', [
                'User ID' => $request->get('userUuid')
            ], 409);
        }

        if ($request->get('requestUuid') === NULL)
        {
            $txnId = Uuid::uuid4();
        } else {
            $txnId = $request->get('requestUuid');
        }

        if ($request->get('currency') === NULL)
        {
            $currency = 'USD';
        } else {
            $currency = $request->get('currency');
        }

        //TODO: Consider adding user as restriction
        $fromAccount = PTOInternalAccount::findInternalAccountByUuid($request->get('fromAccountUuid'));
        if ($fromAccount === NULL)
        {
            $fromAccount = PTOExternalAccount::findExternalAccountByUuid($request->get('fromAccountUuid'));
            if ($fromAccount === NULL)
            {
                return new FailedResponse('The originating account does not exist.', [
                    'Account' => $request->get('fromAccountUuid')
                ], 409);
            }
            $fromAccountIsInternal = false;
        } else {
            $fromAccountIsInternal = true;
        }

        if ($fromAccount->getUser() !== $fromUser->getUuid())
        {
            return new FailedResponse('The originating account must belong to the provided user', [
                'Account' => $request->get('fromAccountUuid'),
                'User' => $request->get('userUuid')
            ], 409);
        }

        $toAccount = PTOInternalAccount::findInternalAccountByUuid($request->get('toAccountUuid'));
        if ($toAccount === NULL)
        {
            $toAccount = PTOExternalAccount::findExternalAccountByUuid($request->get('toAccountUuid'));
            if ($toAccount === NULL)
            {
                return new FailedResponse('The destination account does not exist.', [
                    'Account' => $request->get('toAccountUuid')
                ], 409);
            }
            $toAccountIsInternal = false;
        } else {
            $toAccountIsInternal = true;
        }

        if ($toAccount->getUser() !== $toAccount->getUuid())
        {
            return new FailedResponse('The originating account must belong to the provided user', [
                'Account' => $request->get('toAccountUuid'),
                'User' => $request->get('userUuid')
            ], 409);
        }

        if (!$fromAccountIsInternal && !$toAccountIsInternal)
        {
            return new FailedResponse('Both the origin and destination account cannot be external.', [
                'fromAccount' => $request->get('toAccountUuid'),
                'toAccount' => $request->get('fromAccountUuid')
            ], 409);
        }

        $txn = new PTOTransaction();
        $txn->setUuid($txnId)
            ->setFromUser($fromUser->getUuid())
            ->setFromUserPayment($fromAccount->getExternalAccountNumber())
            ->setToUser($fromUser->getUuid())
            ->setToUserPayment($toAccount->getExternalAccountNumber())
            ->setOrganizationId($userType->getOrgId())
            ->setAmount($request->get('txnAmount'))
            ->setCurrency($currency)
            ->setProcessor('RPN')
            ->setTransactionType(PTOTransaction::PTO_TXN_BANK_FUNDS_TRANSFER);

        $transfer = null;
        if ($fromAccountIsInternal && $toAccountIsInternal)
        {
            $transfer = RPNService::transferFundsBetweenInternalAccounts($fromUser, $toAccount, $fromAccount, $request->get('txnAmount'));
        }

        if ($fromAccountIsInternal && !$toAccountIsInternal)
        {
            $transfer = RPNService::InternalToExternalBankTransfer($fromAccount, $toAccount, $fromUser, $request->get('txnAmount'));
        }

        if (!$fromAccountIsInternal && $toAccountIsInternal)
        {
            $transfer = RPNService::ExternaltoInternalBankTransfer($fromAccount, $toAccount, $fromUser, $request->get('txnAmount'));
        }

        if ($transfer === 'Failure' || !$transfer)
        {
            return new FailedResponse('Processor failure. Tern has been alerted.');
        }

        $txn->setStatus(PTOTransaction::PTO_TXN_STATUS_COMPLETED)
            ->persist();

        return $this->transactionSuccessResponse($txn);
    }

    /**
     * @Route("/api/pto/internal-account-transfer", methods={"POST"}, name="api_pto_internal_account_transfer")
     * @SWG\Post(
     *   tags={"Transactions"},
     *   summary="Initiate Internal Account Transfer",
     *   description="Initiate an internal account to internal account funds transfer. These accounts can be owned by two seperate users. Provided userUuid must be for originating account.",
     *   @SWG\RequestBody(required=true, @SWG\MediaType(mediaType="application/x-www-form-urlencoded", schema=@SWG\Schema(required={"userUuid", "requestUuid", "fromInternalAccountUuid", "toInternalAccountUuid", "txnAmount"}, properties={
     *     @SWG\Property(property="userUuid", description="Unique ID for the user initiating the transaction. This user should be one that owns the FROM internal account.", type="string"),
     *     @SWG\Property(property="requestUuid", description="Unique ID for the transaction. Will be provided by Tern if not provided by the organization.", type="string"),
     *     @SWG\Property(property="fromInternalAccountUuid", description="Unique ID for the source of the funds. This must be an internal account.", type="string"),
     *     @SWG\Property(property="toInternalAccountUuid", description="Unique ID for the destination internal account.", type="string"),
     *     @SWG\Property(property="txnAmount", description="Transaction amount in main units with decimal. $10 = 10.00", type="string"),
     *     @SWG\Property(property="currency", description="Currency for the transaction. Currently only USD is supported. This will default to USD unless specified otherwise.", type="string"),
     *   }))),
     *   security={{"x-api-key":{}, "x-timezone":{}, "x-i18n":{}, "x-token":{}}},
     *   @SWG\Response(response=200, ref="#/components/responses/Response"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     *
     * @return JsonResponse
     * @throws Exception
     */
    public function PTOInternalAccountTransfer(): JsonResponse
    {
        $request = $this->validateParameters(__METHOD__);

        $fromUser = PTOUser::findUserByUuid($request->get('userUuid'));
        $userType = PTOUserType::findUserTypeByUuid($fromUser->getUserTypeId());

        if (PTOTransaction::findTransactionByUuid($request->get('requestUuid')) !== NULL)
        {
            return new FailedResponse('This transaction request ID is already in use.', [
                'requestUuid' => $request->get('requestUuid')
            ], 409);
        }

        if ($fromUser === NULL)
        {
            return new FailedResponse('This user ID does not exist.', [
                'User ID' => $request->get('userUuid')
            ], 409);
        }

        if ($userType->getAllowP2pTransfers() === false)
        {
            return new FailedResponse("This user's User Type is not permissioned for Internal Account transfers.", [
                'User ID' => $request->get('userUuid'),
                'User Type ID' => $userType->getUuid()
            ], 409);
        }

        if ($request->get('requestUuid') === NULL)
        {
            $txnId = Uuid::uuid4();
        } else {
            $txnId = $request->get('requestUuid');
        }

        $fromPaymentAccount = PTOInternalAccount::findInternalAccountByUuid($request->get('fromInternalAccountUuid'));
        if ($fromPaymentAccount === NULL)
        {
            return new FailedResponse('This internal account does not exist.', [
                'Internal Account' => $request->get('fromInternalAccountUuid')
            ], 409);
        }

        // Get to user info

        $toPaymentAccount = PTOInternalAccount::findInternalAccountByUuid($request->get('toInternalAccountUuid'));
        if ($toPaymentAccount === NULL)
        {
            return new FailedResponse('This internal account does not exist.', [
                'Internal Account' => $request->get('toInternalAccountUuid')
            ], 409);
        }

        $toUser = PTOUser::findUserByUuid($toPaymentAccount->getUser());
        if ($toUser === NULL)
        {
            return new FailedResponse("This internal account's user is no longer in our system.", [
                'Internal Account' => $request->get('toInternalAccountUuid'),
                'User Uuid' => $toPaymentAccount->getUser(),
            ], 409);
        }

        if ($request->get('currency') === NULL)
        {
            $currency = 'USD';
        } else {
            $currency = $request->get('currency');
        }

        $txn = new PTOTransaction();
        $txn->setUuid($txnId)
            ->setFromUser($fromUser->getUuid())
            ->setFromUserPayment($fromPaymentAccount->getExternalAccountNumber())
            ->setToUser($toUser->getUuid())
            ->setToUserPayment($toPaymentAccount->getExternalAccountNumber())
            ->setOrganizationId($userType->getOrgId())
            ->setAmount($request->get('txnAmount'))
            ->setCurrency($currency)
            ->setProcessor('RPN')
            ->setTransactionType(PTOTransaction::PTO_TXN_INTERNAL_ACCOUNT_TRANSFER);

        $transfer = RPNService::transferFundsBetweenUsers($fromUser, $toUser, $txn->getAmount());

        if ($transfer === 'Failure')
        {
            return new FailedResponse('Processor failure. Tern has been alerted.');
        }

        // Delay processing to ensure successful transmission.
//        sleep(4);
//        $result = RPNService::createP2CTransfer($txn, $toUser);
//
//        if ($result === 'Failure')
//        {
//            // Return money to original account.
//            RPNService::transferFundsBetweenUsers($toUser, $fromUser, $txn->getAmount());
//            return new FailedResponse('Processor failure. Tern has been alerted.');
//        }

        if ($transfer === 'Pending')
        {
            $txn->setStatus(PTOTransaction::PTO_TXN_STATUS_PENDING)
                ->persist();

            return $this->transactionSuccessResponse($txn);
        }

        $txn->setStatus(PTOTransaction::PTO_TXN_STATUS_COMPLETED)
            ->persist();

        return $this->transactionSuccessResponse($txn);
    }

    /**
     * @Route("/api/pto/push-2-card", methods={"POST"}, name="api_pto_push_2_card")
     * @SWG\Post(
     *   tags={"Transactions"},
     *   summary="Initiate Push To Card Transaction",
     *   description="Initiate a Push To Card Transaction. This can be used to push funds instnatly to a card from an internal account.",
     *   @SWG\RequestBody(required=true, @SWG\MediaType(mediaType="application/x-www-form-urlencoded", schema=@SWG\Schema(required={"userUuid", "requestUuid", "fromInternalAccountUuid", "toPaymentCardUuid", "txnAmount"}, properties={
     *     @SWG\Property(property="userUuid", description="Unique ID for the user initiating the transaction. This user should be one that owns the FROM internal account.", type="string"),
     *     @SWG\Property(property="requestUuid", description="Unique ID for the transaction. Will be provided by Tern if not provided by the organization.", type="string"),
     *     @SWG\Property(property="fromInternalAccountUuid", description="Unique ID for the source of the funds. This must be an internal account.", type="string"),
     *     @SWG\Property(property="toPaymentCardUuid", description="Unique ID for the destination payment card.", type="string"),
     *     @SWG\Property(property="txnAmount", description="Transaction amount in main units with decimal. $10 = 10.00", type="string"),
     *     @SWG\Property(property="currency", description="Currency for the transaction. Currently only USD is supported. This will default to USD unless specified otherwise.", type="string"),
     *   }))),
     *   security={{"x-api-key":{}, "x-timezone":{}, "x-i18n":{}, "x-token":{}}},
     *   @SWG\Response(response=200, ref="#/components/responses/Response"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     *
     * @return JsonResponse
     * @throws Exception
     */
    public function PTOPush2CardTxn(): JsonResponse
    {
        $request = $this->validateParameters(__METHOD__);

        $fromUser = PTOUser::findUserByUuid($request->get('userUuid'));
        $userType = PTOUserType::findUserTypeByUuid($fromUser->getUserTypeId());

        if (PTOTransaction::findTransactionByUuid($request->get('requestUuid')) !== NULL)
        {
            return new FailedResponse('This transaction request ID is already in use.', [
                'requestUuid' => $request->get('requestUuid')
            ], 409);
        }

        if ($fromUser === NULL)
        {
            return new FailedResponse('This user ID does not exist.', [
                'User ID' => $request->get('userUuid')
            ], 409);
        }

        if ($userType->getAllowP2pTransfers() === false)
        {
            return new FailedResponse("This user's User Type is not permissioned for Push to Card transfers.", [
                'User ID' => $request->get('userUuid'),
                'User Type ID' => $userType->getUuid()
            ], 409);
        }

        if ($request->get('requestUuid') === NULL)
        {
            $txnId = Uuid::uuid4();
        } else {
            $txnId = $request->get('requestUuid');
        }

        $fromPaymentAccount = PTOInternalAccount::findInternalAccountByUuid($request->get('fromInternalAccountUuid'));
        if ($fromPaymentAccount === NULL)
        {
            return new FailedResponse('This internal account does not exist.', [
                'Internal Account' => $request->get('fromInternalAccountUuid')
            ], 409);
        }

        // Get to user info

        $toPaymentCard = PTOPaymentCard::findCardByUuid($request->get('toPaymentCardUuid'));
        if ($toPaymentCard === NULL)
        {
            return new FailedResponse('This payment card does not exist.', [
                'Payment Card' => $request->get('toPaymentCardUuid')
            ], 409);
        }

        $toUser = PTOUser::findUserByUuid($toPaymentCard->getUserId());
        if ($toUser === NULL)
        {
            return new FailedResponse("This payment card's user is no longer in our system.", [
                'Payment Card' => $request->get('toPaymentCardUuid')
            ], 409);
        }

        if ($request->get('currency') === NULL)
        {
            $currency = 'USD';
        } else {
            $currency = $request->get('currency');
        }

        $txn = new PTOTransaction();
        $txn->setUuid($txnId)
            ->setFromUser($fromUser->getUuid())
            ->setFromUserPayment($fromPaymentAccount->getExternalAccountNumber())
            ->setToUser($toUser->getUuid())
            ->setToUserPayment($toPaymentCard->getExternalToken())
            ->setOrganizationId($userType->getOrgId())
            ->setAmount($request->get('txnAmount'))
            ->setCurrency($currency)
            ->setProcessor('RPN')
            ->setTransactionType(PTOTransaction::PTO_TXN_PUSH_TO_CARD);

        $transfer = RPNService::transferFundsBetweenUsers($fromUser, $toUser, $txn->getAmount());

        if ($transfer === 'Failure')
        {
            return new FailedResponse('Processor failure. Tern has been alerted.');
        }

        // Delay processing to ensure successful transmission.
        sleep(4);
        $result = RPNService::createP2CTransfer($txn, $toUser);

        if ($result === 'Failure')
        {
            // Return money to original account.
            RPNService::transferFundsBetweenUsers($toUser, $fromUser, $txn->getAmount());
            return new FailedResponse('Processor failure. Tern has been alerted.');
        }

        if ($result === 'Pending')
        {
            $txn->setStatus(PTOTransaction::PTO_TXN_STATUS_PENDING)
                ->persist();

            return $this->transactionSuccessResponse($txn);
        }

        $txn->setStatus(PTOTransaction::PTO_TXN_STATUS_COMPLETED)
            ->persist();

        return $this->transactionSuccessResponse($txn);
    }
}
