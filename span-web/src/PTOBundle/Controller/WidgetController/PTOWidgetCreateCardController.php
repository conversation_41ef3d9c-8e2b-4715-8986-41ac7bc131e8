<?php


namespace PTOB<PERSON>le\Controller\WidgetController;


use Api<PERSON><PERSON>le\Services\RPNService;
use Core<PERSON><PERSON>le\Controller\BaseController;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Services\SSLEncryptionService;
use PortalBundle\Exception\PortalException;
use PTOB<PERSON>le\Controller\PTOPaymentCardController;
use PTOB<PERSON>le\Entity\PTOPaymentCard;
use PTOBundle\Entity\PTOUser;
use PTOBundle\Entity\PTOUserType;
use Ramsey\Uuid\Uuid;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use CoreBundle\Utils\Util;

class PTOWidgetCreateCardController extends BaseController
{
    public static function getCardTypeName($num)
    {
        switch ($num)
        {
            case 1:
                return 'Visa';
                break;
            case 2:
                return 'Mastercard';
                break;
            case 4:
                return 'American Express';
                break;
            case 3:
                return 'Discover';
                break;
            case 5:
                return 'Other';
                break;
            default:
                return false;
        }
    }

    /**
     * @Route("/p/create-card/{widgetToken}")
     * @param Request $request
     * @param string $widgetToken
     * @return Response
     * @throws PortalException
     */
    public function PTOCreateCard(Request $request)
    {
        $user = PTOUser::findUserByWidgetToken($request->get('widgetToken'));
        if ($user)
        {
            $response = new Response($this->renderView('@PTO/Widgets/create-card.html.twig', [
                'user' => $user
            ]), 200);
            $response->headers->set('X-Frame-Options', '');
            return $response;
        }

        $user = PTOUser::findUserByUuid($request->get('widgetToken'));
        if ($user)
        {
            $response = new Response($this->renderView('@PTO/Widgets/create-card.html.twig', [
                'user' => $user
            ]), 200);
            $response->headers->set('X-Frame-Options', '');
            return $response;
        }

        return $this->render('@PTO/Widgets/widget-failed.html.twig');
    }

    /**
     * @Route("/p/iframe-test")
     * @param Request $request
     * @return Response
     * @throws PortalException
     */
    public function PTOIframeTest(Request $request)
    {
        return $this->render('@PTO/Widgets/iframe-test.html.twig');
    }

    /**
     * @Route("/p/submit-card/{widgetToken}")
     * @param Request $request
     * @param string $widgetToken
     * @return Response
     * @throws PortalException
     */
    public function PTOWidgetSubmitCard(Request $request)
    {
        $user = PTOUser::findUserByWidgetToken($request->get('widgetToken'));

        if (!$user)
        {
            $user = PTOUser::findUserByUuid($request->get('widgetToken'));
        }

        if ($user)
        {
            $userType = PTOUserType::findUserTypeByUuid($user->getUserTypeId());

            if ($userType->getStorePaymentCards() === false)
            {
                $response = new Response($this->renderView('@PTO/Widgets/card-failed.html.twig', [
                    'user' => $user
                ]), 200);
                $response->headers->set('X-Frame-Options', '');
                return $response;
            }

            $cardTypeNum = PTOPaymentCardController::getPaymentCardType($request->get('cardNumber'));
            $cardTypeName = self::getCardTypeName($cardTypeNum);
            if (!$cardTypeName)
            {
                $response = new Response($this->renderView('@PTO/Widgets/card-failed.html.twig', [
                    'user' => $user
                ]), 200);
                $response->headers->set('X-Frame-Options', '');
                return $response;
            }

            $cardNumLength = strlen($request->get('cardNumber'));
            $cvvLength = strlen($request->get('cvv'));

            if ($cardNumLength < 16 && $cardNumLength > 19)
            {
                $response = new Response($this->renderView('@PTO/Widgets/card-failed.html.twig', [
                    'user' => $user
                ]), 200);
                $response->headers->set('X-Frame-Options', '');
                return $response;
            }

            if ($cvvLength < 3 && $cvvLength > 4)
            {
                $response = new Response($this->renderView('@PTO/Widgets/card-failed.html.twig', [
                    'user' => $user
                ]), 200);
                $response->headers->set('X-Frame-Options', '');
                return $response;
            }

            $card = new PTOPaymentCard();
            $card->setUuid(Uuid::uuid4())
                ->setUserId($user->getUuid())
                ->setCardName($request->get('cardName'))
                ->setCardType($cardTypeNum)
                ->setCardNumber(SSLEncryptionService::encrypt($request->get('cardNumber')))
                ->setCvv($request->get('cvv'))
                ->setExpirationYear($request->get('expirationYear'))
                ->setExpirationMonth($request->get('expirationMonth'))
                ->setCardNumberLast4(substr($request->get('cardNumber'), -4))
                ->setIsPrimary($request->get('isPrimary'));

            //TODO: Set primary for user based on isPrimary

            $externalToken = RPNService::createPaymentCard($card, $user);

            if ($externalToken === 'Error')
            {
                $response = new Response($this->renderView('@PTO/Widgets/card-failed.html.twig', [
                    'user' => $user
                ]), 200);
                $response->headers->set('X-Frame-Options', '');
                return $response;
            }

            $card->setExternalToken($externalToken)
                ->persist();

            $response = new Response($this->renderView('@PTO/Widgets/card-success.html.twig', [
                'user' => $user,
                'cardType' => $cardTypeName,
                'cardNumber'  => $card->getCardNumberLast4()
            ]), 200);
            $response->headers->set('X-Frame-Options', '');
            return $response;
        }

        $response = new Response($this->renderView('@PTO/Widgets/card-failed.html.twig', [
            'user' => $user
        ]), 200);
        $response->headers->set('X-Frame-Options', '');
        return $response;
    }
}