<?php


namespace PTOBundle\Controller;

use ApiB<PERSON>le\Services\RPNService;
use Exception;
use PTOB<PERSON>le\Entity\PTOInternalAccount;
use PTOBundle\Entity\PTOPaymentCard;
use PTOB<PERSON>le\Entity\PTOUser;
use PTOB<PERSON>le\Entity\PTOUserType;
use Ramsey\Uuid\Uuid;
use OpenApi\Annotations as SWG;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\JsonResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Util;
use CoreBundle\Services\SSLEncryptionService;

class PTOPaymentCardController extends \ApiBundle\Controller\BaseController
{

    private function paymentCardSuccessResponse(PTOPaymentCard $card)
    {
        return new SuccessResponse([
            'Card UUID' => $card->getUuid(),
            'Card Name' => $card->getCardName(),
            'Card Type' => intval($card->getCardType()),
            'Card Number Last 4' => $card->getCardNumberLast4(),
            'CVV' => $card->getCvv(),
            'Expiration Year' => $card->getExpirationYear(),
            'Expiration Month' => $card->getExpirationMonth(),
            'isPrimary' => $card->getIsPrimary()
        ]);
    }

    public static function getPaymentCardType($number)
    {
        global $type;

        $cardType = array(
            "visa"       => "/^4[0-9]{12}(?:[0-9]{3})?$/",
            "mastercard" => "/^5[1-5][0-9]{14}$/",
            "amex"       => "/^3[47][0-9]{13}$/",
            "discover"   => "/^6(?:011|5[0-9]{2})[0-9]{12}$/",
        );

        if (preg_match($cardType['visa'], $number))
        {
            $type = "visa";
            return 1;

        }

        if (preg_match($cardType['mastercard'], $number))
        {
            $type = "mastercard";
            return 2;
        }

        if (preg_match($cardType['amex'], $number))
        {
            $type = "amex";
            return 4;

        }

        if (preg_match($cardType['discover'], $number))
        {
            $type = "discover";
            return 3;
        }

        // Lessen requirements for staging.
        if (Util::isDev() || Util::isStaging())
        {
            $type = "other";
            return 5;
        }

        return false;
    }

    public function luhn_check($number) {

        // Strip any non-digits (useful for credit card numbers with spaces and hyphens)
        $number=preg_replace('/\D/', '', $number);

        // Set the string length and parity
        $number_length=strlen($number);
        $parity=$number_length % 2;

        // Loop through each digit and do the maths
        $total=0;
        for ($i=0; $i<$number_length; $i++) {
            $digit=$number[$i];
            // Multiply alternate digits by two
            if ($i % 2 == $parity) {
                $digit*=2;
                // If the sum is two digits, add them together (in effect)
                if ($digit > 9) {
                    $digit-=9;
                }
            }
            // Total up the digits
            $total+=$digit;
        }

        // If the total mod 10 equals 0, the number is valid
        return ($total % 10 == 0) ? TRUE : FALSE;

    }

    /**
     * @Route("/api/pto/create-payment-card", methods={"POST"}, name="api_pto_create_payment_card")
     * @SWG\Post(
     *   tags={"Payment Cards"},
     *   summary="Create Payment Card For User Profile",
     *   description="Create a payment card for a user profile to be used as an external payment source for Push to Card transactions.",
     *   @SWG\RequestBody(required=true, @SWG\MediaType(mediaType="application/x-www-form-urlencoded", schema=@SWG\Schema(required={"userUuid", "cardName", "cardNumber", "cvv", "expirationYear", "expirationMonth"}, properties={
     *     @SWG\Property(property="userUuid", description="Unique ID for the user.", type="string"),
     *     @SWG\Property(property="cardName", description="The name of the Payment Card. Only used as an identifier.", type="string"),
     *     @SWG\Property(property="cardType", description="Int indicating the card type. 1 = Visa, 2 = MasterCard, 3 - Discover, 4 = Amex.", type="integer"),
     *     @SWG\Property(property="cardNumber", description="The 16 - 19 digit card number.", type="string"),
     *     @SWG\Property(property="cvv", description="The 3-4 digit security code for the card.", type="string"),
     *     @SWG\Property(property="expirationYear", description="The Year of the card expiration in YYYY format.", type="string"),
     *     @SWG\Property(property="expirationMonth", description="The Month of the card expiration in MM format", type="string"),
     *     @SWG\Property(property="isPrimary", description="Sets the card as the default for the User Profile. This will overwrite any previous defaults. Will default to false unless specified.", type="boolean"),
     *   }))),
     *   security={{"x-api-key":{}, "x-timezone":{}, "x-i18n":{}, "x-token":{}}},
     *   @SWG\Response(response=200, ref="#/components/responses/Response"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     *
     * @return JsonResponse
     * @throws Exception
     */
    public function PTOCreatePaymentCard(): JsonResponse
    {
        $request = $this->validateParameters(__METHOD__);

        if (PTOUser::findUserByUuid($request->get('userUuid')) === NULL)
        {
            return new FailedResponse('This user ID does not exist.', [
                'User ID' => $request->get('userUuid')
            ], 409);
        }

        $user = PTOUser::findUserByUuid($request->get('userUuid'));
        $userType = PTOUserType::findUserTypeByUuid($user->getUserTypeId());

        if ($userType->getStorePaymentCards() === false)
        {
            return new FailedResponse('This user IDs type is not permissioned to store payment cards.', [
                'User ID' => $request->get('userUuid'),
                'User Type' => $userType->getName(),
                'User Type ID' => $userType->getUuid()
            ], 409);
        }

        $cardTypes = [1, 2, 3, 4];
        if (!array_has($cardTypes, $request->get('cardType')))
        {
            if ($request->get('cardType') !== NULL)
            {
                return new FailedResponse('CardType was not a valid option.', [
                    'Card Type' => $request->get('cardType')
                ]);
            }

            $cardType = self::getPaymentCardType($request->get('cardNumber'));
            if ($cardType === false)
            {
                return new FailedResponse('Was unable to automatically determine card type. The card number may be incorrect.', [
                    'Card Number' => $request->get('cardNumber')
                ]);
            }
        } else {
            $cardType = $request->get('cardType');
        }

        $cardNumLength = strlen($request->get('cardNumber'));
        $cvvLength = strlen($request->get('cvv'));
        $expYearNum = intval($request->get('expirationYear'));

        if ($cardNumLength < 16 && $cardNumLength > 19)
        {
            return new FailedResponse('Card number must be between 16 and 19 digits.', [
                'Card Number' => $request->get('cardNumber')
            ], 409);
        }

        if ($cvvLength < 3 && $cvvLength > 4)
        {
            return new FailedResponse('CVV must be between 3 and 4 digits.', [
                'CVV' => $request->get('cvv')
            ], 409);
        }

        if ($expYearNum < 2020 || $expYearNum > 2035)
        {
            return new FailedResponse('Expiration year is invalid.', [
                'Expiration Year' => $request->get('expirationYear')
            ], 409);
        }

        $monthArray = ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12'];
        if (!in_array($request->get('expirationMonth'), $monthArray, true))
        {
            return new FailedResponse('Expiration month is invalid', [
                'Expiration Month' => $request->get('expirationMonth')
            ], 409);
        }

        if ($request->get('isPrimary') === NULL){
            $isPrimary = false;
        } else {
            $isPrimary = $request->get('isPrimary');
        }

        $card = new PTOPaymentCard();
        $card->setUuid(Uuid::uuid4())
            ->setUserId($request->get('userUuid'))
            ->setCardName($request->get('cardName'))
            ->setCardType($cardType)
            ->setCardNumber(SSLEncryptionService::encrypt($request->get('cardNumber')))
            ->setCvv($request->get('cvv'))
            ->setExpirationYear($request->get('expirationYear'))
            ->setExpirationMonth($request->get('expirationMonth'))
            ->setCardNumberLast4(substr($request->get('cardNumber'), -4))
            ->setIsPrimary($isPrimary);

        //TODO: Set primary for user based on isPrimary

        $externalToken = RPNService::createPaymentCard($card, $user);

        if ($externalToken === 'Error')
        {
            return new FailedResponse('Processor failure. Tern has been alerted.');
        }

        $card->setExternalToken($externalToken)
            ->persist();

        $storedCard = PTOPaymentCard::findCardByUuid($card->getUuid());

        return $this->paymentCardSuccessResponse($storedCard);
    }

    /**
     * @Route("/api/pto/update-payment-card", methods={"POST"}, name="api_pto_update_payment_card")
     * @SWG\Post(
     *   tags={"Payment Cards"},
     *   summary="Update Payment Card For User Profile",
     *   description="Update a payment card for a user profile to be used as an external payment source for Push to Card transactions. Any fields not included will not be updated.",
     *   @SWG\RequestBody(required=true, @SWG\MediaType(mediaType="application/x-www-form-urlencoded", schema=@SWG\Schema(required={"uuid"}, properties={
     *     @SWG\Property(property="uuid", description="Unique ID for the payment card you wish to update.", type="string"),
     *     @SWG\Property(property="cardName", description="The name of the Payment Card. Only used as an identifier.", type="string"),
     *     @SWG\Property(property="cardType", description="Int indicating the card type. 1 = Visa, 2 = MasterCard, 3 - Discover, 4 = Amex.", type="integer"),
     *     @SWG\Property(property="cardNumber", description="The 16 - 19 digit card number.", type="string"),
     *     @SWG\Property(property="cvv", description="The 3-4 digit security code for the card.", type="string"),
     *     @SWG\Property(property="expirationYear", description="The Year of the card expiration in YYYY format.", type="string"),
     *     @SWG\Property(property="expirationMonth", description="The Month of the card expiration in MM format", type="string"),
     *     @SWG\Property(property="isPrimary", description="Sets the card as the default for the User Profile. This will overwrite any previous defaults. Will default to false unless specified.", type="boolean"),
     *   }))),
     *   security={{"x-api-key":{}, "x-timezone":{}, "x-i18n":{}, "x-token":{}}},
     *   @SWG\Response(response=200, ref="#/components/responses/Response"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     *
     * @return JsonResponse
     * @throws Exception
     */
    public function PTOUpdatePaymentCard(): JsonResponse
    {
        // Will create a new RAPID payment card with new info and will overwrite the old one if update includes things other than isPrimary.
        $request = $this->validateParameters(__METHOD__);

        $card = PTOPaymentCard::findCardByUuid($request->get('uuid'));

        if ($card === NULL)
        {
            return new FailedResponse('No card found with that uuid.', [
                'UUID' => $request->get('uuid')
            ], 409);
        }

        $updateRPNCard = false;

        if ($request->get('cardName') !== NULL)
        {
            $card->setCardName($request->get('cardName'));
        }

        if ($request->get('cardType') !== NULL)
        {
            $cardTypes = [1, 2, 3, 4];
            if (!array_has($cardTypes, $request->get('cardType')))
            {
                return new FailedResponse('CardType was not a valid option.', [
                    'Card Type' => $request->get('cardType')
                ]);
            }

            $card->setCardType($request->get('cardType'));
            $updateRPNCard = true;
        }

        if ($request->get('cardNumber') !== NULL)
        {
            $cardNumLength = strlen($request->get('cardNumber'));
            if ($cardNumLength < 16 && $cardNumLength > 19)
            {
                return new FailedResponse('Card number must be between 16 and 19 digits.', [
                    'Card Number' => $request->get('cardNumber')
                ], 409);
            }

            $card->setCardNumber(SSLEncryptionService::encrypt($request->get('cardNumber')))
                ->setCardNumberLast4(substr($request->get('cardNumber'), -4));
            $updateRPNCard = true;
        }

        if ($request->get('cvv') !== NULL)
        {
            $cvvLength = strlen($request->get('cvv'));
            if ($cvvLength < 3 && $cvvLength > 4)
            {
                return new FailedResponse('CVV must be between 3 and 4 digits.', [
                    'CVV' => $request->get('cvv')
                ], 409);
            }

            $card->setCvv($request->get('cvv'));
            $updateRPNCard = true;
        }

        if ($request->get('expirationYear') !== NULL)
        {
            $expYearNum = intval($request->get('expirationYear'));
            if ($expYearNum < 2020 || $expYearNum > 2035)
            {
                return new FailedResponse('Expiration year is invalid.', [
                    'Expiration Year' => $request->get('expirationYear')
                ], 409);
            }

            $card->setExpirationYear($request->get('expirationYear'));
            $updateRPNCard = true;
        }

        if ($request->get('expirationMonth') !== NULL)
        {
            $monthArray = ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12'];
            if (!in_array($request->get('expirationMonth'), $monthArray, true))
            {
                return new FailedResponse('Expiration month is invalid', [
                    'Expiration Month' => $request->get('expirationMonth')
                ], 409);
            }

            $card->setExpirationMonth($request->get('expirationMonth'));
            $updateRPNCard = true;
        }

        if ($request->get('isPrimary') !== NULL)
        {
            //TODO: isPrimary function

            $card->setIsPrimary($request->get('isPrimary'));
        }

        if ($updateRPNCard === true)
        {
            $user = PTOUser::findUserByUuid($card->getUserId());
            $externalToken =  RPNService::createPaymentCard($card, $user);
            $card->setExternalToken($externalToken);
        }
        $card->persist();

        return $this->paymentCardSuccessResponse($card);
    }

    /**
     * @Route("/api/pto/get-payment-card-summary", methods={"POST"}, name="api_pto_get_payment_card_summary")
     * @SWG\Post(
     *   tags={"Payment Cards"},
     *   summary="Get Summary of Payment Cards for User Profile",
     *   description="Get a summary of all of the payment cards associated with a specific User Profile.",
     *   @SWG\RequestBody(required=true, @SWG\MediaType(mediaType="application/x-www-form-urlencoded", schema=@SWG\Schema(required={"userUuid"}, properties={
     *     @SWG\Property(property="userUuid", description="Unique ID for the user that you want to get the card summary for.", type="string"),
     *   }))),
     *   security={{"x-api-key":{}, "x-timezone":{}, "x-i18n":{}, "x-token":{}}},
     *   @SWG\Response(response=200, ref="#/components/responses/Response"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     *
     * @return JsonResponse
     * @throws Exception
     */
    public function PTOGetUserPaymentCards(): JsonResponse
    {
        $request = $this->validateParameters(__METHOD__);

        $user = PTOUser::findUserByUuid($request->get('userUuid'));

        if ($user === NULL)
        {
            return new FailedResponse('This user ID does not exist.', [
                'User ID' => $request->get('userUuid')
            ], 409);
        }

        $id = $user->getUuid();

        $rs = Util::em()->getRepository(PTOPaymentCard::class)
            ->createQueryBuilder('pc')
            ->where('pc.userId = :userId')
            ->setParameter('userId', $id)
            ->getQuery()
            ->getResult();
        if (!$rs) {
            return new FailedResponse('No Internal Accounts found for user.', [
                'User ID' => $id
            ], 409);
        }

        $pcArray = [];

        foreach ($rs as $pc)
        {
            $pcArray[] = [
                'Payment Card UUID' => $pc->getUuid(),
                'Card Name' => $pc->getCardName(),
                'Card Number Last 4' => $pc->getCardNumberLast4(),
                'isPrimary' => $pc->getIsPrimary()
            ];
        }

        return new SuccessResponse([
            'User ID' => $id,
            'Internal Accounts' => $pcArray
        ]);
    }

    /**
     * @Route("/api/pto/get-payment-card-details", methods={"POST"}, name="api_pto_get_payment_card_details")
     * @SWG\Post(
     *   tags={"Payment Cards"},
     *   summary="Get Details of Payment Card",
     *   description="Get the details for a specific payment card. The ID for the User Profile is required for verification.",
     *   @SWG\RequestBody(required=true, @SWG\MediaType(mediaType="application/x-www-form-urlencoded", schema=@SWG\Schema(required={"userUuid", "uuid"}, properties={
     *     @SWG\Property(property="userUuid", description="Unique ID for the user that owns the card. This is necessary for verification.", type="string"),
     *     @SWG\Property(property="uuid", description="Unique ID for the payment card.", type="string"),
     *   }))),
     *   security={{"x-api-key":{}, "x-timezone":{}, "x-i18n":{}, "x-token":{}}},
     *   @SWG\Response(response=200, ref="#/components/responses/Response"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     *
     * @return JsonResponse
     * @throws Exception
     */
    public function PTOGetCardDetails(): JsonResponse
    {
        $request = $this->validateParameters(__METHOD__);

        $card = PTOPaymentCard::findCardByUuid($request->get('uuid'));
        if ($card === NULL)
        {
            return new FailedResponse('No card found with that uuid.', [
                'UUID' => $request->get('uuid')
            ], 409);
        }

        $user = PTOUser::findUserByUuid($request->get('userUuid'));
        if ($user === NULL)
        {
            return new FailedResponse('This user ID does not exist.', [
                'User ID' => $request->get('userUuid')
            ], 409);
        }

        if ($card->getUserId() !== $user->getUuid())
        {
            return new FailedResponse('This user does not own this card.', [
                'User ID' => $request->get('userUuid'),
                'Payment Card ID' => $request->get('uuid')
            ], 409);
        }

        return $this->paymentCardSuccessResponse($card);
    }
}
