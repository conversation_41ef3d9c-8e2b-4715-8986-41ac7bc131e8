<?php


namespace PTOBundle\Controller;

use ApiBundle\Services\RPNService;
use Exception;
use PTO<PERSON><PERSON>le\Entity\PTOInternalAccount;
use PTOB<PERSON>le\Entity\PTOUser;
use PTOB<PERSON>le\Entity\PTOUserType;
use Ramsey\Uuid\Uuid;
use OpenApi\Annotations as SWG;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\JsonResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Util;


class PTOInternalAccountController extends \ApiBundle\Controller\BaseController
{
    /**
     * @Route("/api/pto/create-internal-account", methods={"POST"}, name="api_pto_create_internal_account")
     * @SWG\Post(
     *   tags={"Internal Accounts"},
     *   summary="Create An Internal Account for a User Profile",
     *   description="Create an internal bank account for a User Profile. This account can be used to fund Push to Card transactions.",
     *   @SWG\RequestBody(required=true, @SWG\MediaType(mediaType="application/x-www-form-urlencoded", schema=@SWG\Schema(required={"userUuid", "accountType", "name"}, properties={
     *     @SWG\Property(property="userUuid", description="Unique ID for the user. Generated by Tern using the Create User Profile API Call.", type="string"),
     *     @SWG\Property(property="accountType", description="Type of account the internal bank account will be. 1 = Checking, 2 = Savings.", type="integer"),
     *     @SWG\Property(property="name", description="Name of the account. Only used as an identifier.", type="string"),
     *     @SWG\Property(property="currency", description="Currency the account will use. Will default to USD unless specified otherwise.", type="string"),
     *     @SWG\Property(property="isPrimary", description="Is this the default payment account for this user? This will overwrite any previous defaults. Will default to false unless specified otherwise.", type="boolean"),
     *   }))),
     *   security={{"x-api-key":{}, "x-timezone":{}, "x-i18n":{}, "x-token":{}}},
     *   @SWG\Response(response=200, ref="#/components/responses/Response"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     *
     * @return JsonResponse
     * @throws Exception
     */
    public function PTOCreateInternalAccount(): JsonResponse
    {
        $request = $this->validateParameters(__METHOD__);

        if (PTOUser::findUserByUuid($request->get('userUuid')) === NULL)
        {
            return new FailedResponse('This user ID does not exist.', [
                'User ID' => $request->get('userUuid')
            ], 409);
        }

        $user = PTOUser::findUserByUuid($request->get('userUuid'));
        $userType = PTOUserType::findUserTypeByUuid($user->getUserTypeId());

        if ($userType->getInternalBankAccoount() === false)
        {
            return new FailedResponse('This user IDs type is not permissioned for internal accounts.', [
                'User ID' => $request->get('userUuid'),
                'User Type' => $userType->getName(),
                'User Type ID' => $userType->getUuid()
            ], 409);
        }

        if ($request->get('currency') === NULL)
        {
            $currency = 'USD';
        } else {
            $currency = $request->get('currency');
        }

        if ($request->get('isPrimary') === NULL){
            $isPrimary = false;
        } else {
            $isPrimary = $request->get('isPrimary');
        }

        $ic = new PTOInternalAccount();
        $ic->setUuid(Uuid::uuid4())
            ->setUser($request->get('userUuid'))
            ->setAccountType($request->get('accountType'))
            ->setName($request->get('name'))
            ->setCurrency($currency)
            ->setIsPrimary($isPrimary);

        //TODO: Set primary for user based on isPrimary

        $ic->setExternalAccountNumber($user->getExternalAccountId());
        $ic->persist();

        return new SuccessResponse([
            'Internal Account UUID' => $ic->getUuid(),
            'Name' => $ic->getName(),
            'Account Type' => $ic->getAccountType(),
            'Currency' => $ic->getCurrency(),
            'User Uuid' => $ic->getUser(),
            'isPrimary' => $ic->getIsPrimary(),
            //TODO: Delete before live
            'External ID' => $ic->getExternalAccountNumber()
        ]);
    }

    /**
     * @Route("/api/pto/get-user-internal-account-summary", methods={"POST"}, name="api_pto_get_user_internal_account_summary")
     * @SWG\Post(
     *   tags={"Internal Accounts"},
     *   summary="Get Summary of All Internal Account for a User Profile",
     *   description="Get a summary of all internal bank accounts associated with a user.",
     *   @SWG\RequestBody(required=true, @SWG\MediaType(mediaType="application/x-www-form-urlencoded", schema=@SWG\Schema(required={"userUuid"}, properties={
     *     @SWG\Property(property="userUuid", description="Unique ID for the user. Generated by Tern using the Create User Profile API Call.", type="string"),
     *   }))),
     *   security={{"x-api-key":{}, "x-timezone":{}, "x-i18n":{}, "x-token":{}}},
     *   @SWG\Response(response=200, ref="#/components/responses/Response"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     *
     * @return JsonResponse
     * @throws Exception
     */
    public function PTOGetUserAccountSummary(): JsonResponse
    {
        $request = $this->validateParameters(__METHOD__);
        $user = PTOUser::findUserByUuid($request->get('userUuid'));

        if ($user === NULL)
        {
            return new FailedResponse('This user ID does not exist.', [
                'User ID' => $request->get('userUuid')
            ], 409);
        }

        $id = $user->getUuid();

        $rs = Util::em()->getRepository(PTOInternalAccount::class)
            ->createQueryBuilder('ia')
            ->where('ia.user = :user')
            ->setParameter('user', $id)
            ->getQuery()
            ->getResult();
        if (!$rs) {
            return new FailedResponse('No Internal Accounts found for user.', [
                'User ID' => $id
            ], 409);
        }

        $iaArray = [];

        foreach ($rs as $ia)
        {
            $iaArray[] = [
                'Internal Account UUID' => $ia->getUuid(),
                'Name' => $ia->getName(),
                'Account Type' => $ia->getAccountType(),
                'Currency' => $ia->getCurrency(),
                'User Uuid' => $ia->getUser(),
                'isPrimary' => $ia->getIsPrimary(),
                //TODO: Delete this before live
                'external ID' => $ia->getExternalAccountNumber()
            ];
        }

        return new SuccessResponse([
            'User ID' => $id,
            'Internal Accounts' => $iaArray
        ]);
    }

    /**
     * @Route("/api/pto/get-internal-account-bank-details", methods={"POST"}, name="api_pto_get_internal_account_bank_details")
     * @SWG\Post(
     *   tags={"Internal Accounts"},
     *   summary="Get Internal Account Bank Details",
     *   description="Get ABA, Routing, and Bank Name of internal account for external push transfers.",
     *   @SWG\RequestBody(required=true, @SWG\MediaType(mediaType="application/x-www-form-urlencoded", schema=@SWG\Schema(required={"userUuid", "iaUuid"}, properties={
     *     @SWG\Property(property="userUuid", description="Unique ID for the user. Generated by Tern using the Create User Profile API Call.", type="string"),
     *     @SWG\Property(property="iaUuid", description="Unique ID for the internal account.", type="string"),
     *     @SWG\Property(property="getBalance", description="Boolean for if the current account balance should be retrieved for this call. Defaults to false.", type="boolean"),
     *   }))),
     *   security={{"x-api-key":{}, "x-timezone":{}, "x-i18n":{}, "x-token":{}}},
     *   @SWG\Response(response=200, ref="#/components/responses/Response"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     *
     * @return JsonResponse
     * @throws Exception
     */
    public function PTOGetInternalAccountBankDetails(): JsonResponse
    {
        $request = $this->validateParameters(__METHOD__);
        $user = PTOUser::findUserByUuid($request->get('userUuid'));
        $ia = PTOInternalAccount::findInternalAccountByUuid($request->get('iaUuid'));

        if ($user === NULL)
        {
            return new FailedResponse('This user ID does not exist.', [
                'User ID' => $request->get('userUuid')
            ], 409);
        }

        if ($ia === NULL)
        {
            return new FailedResponse('This internal account ID does not exist.', [
                'Internal Account ID' => $request->get('iaUuid')
            ], 409);
        }

        $iaUser = $ia->getUser();
        if ($iaUser !== $user->getUuid())
        {
            return new FailedResponse('This Internal Account does not belong to the provided User.', [
                'User ID' => $request->get('userUuid'),
                'Internal Account ID' => $request->get('iaUuid'),
            ], 409);
        }

        if ($request->get('getBalance') === true)
        {
            $balance = RPNService::getAccountBalance($ia);
            if ($balance === 'Failure')
            {
                return new FailedResponse('There was an issue retrieving the account balance. Tern has been notified.', [
                    'User ID' => $request->get('userUuid'),
                    'Internal Account ID' => $request->get('iaUuid')
                ]);
            }

            return new SuccessResponse([
                'Account Number' => '44555' . $ia->getExternalAccountNumber(),
                'Routing Number' => '*********',
                'Bank Name' => 'Cache Valley Bank',
                'Current Balance' => $balance['currentBalance'],
                'Available Balance' => $balance['availableBalance']
            ]);
        }

        return new SuccessResponse([
            'Account Number' => '44555' . $ia->getExternalAccountNumber(),
            'Routing Number' => '*********',
            'Bank Name' => 'Cache Valley Bank'
        ]);
    }

    /**
     * @Route("/api/pto/get-internal-account-balance", methods={"POST"}, name="api_pto_get_internal_account_balance")
     * @SWG\Post(
     *   tags={"Internal Accounts"},
     *   summary="Get Internal Account Balance",
     *   description="Get the current and available balance of an internal account.",
     *   @SWG\RequestBody(required=true, @SWG\MediaType(mediaType="application/x-www-form-urlencoded", schema=@SWG\Schema(required={"userUuid", "iaUuid"}, properties={
     *     @SWG\Property(property="userUuid", description="Unique ID for the user. Generated by Tern using the Create User Profile API Call.", type="string"),
     *     @SWG\Property(property="iaUuid", description="Unique ID for the internal account.", type="string"),
     *   }))),
     *   security={{"x-api-key":{}, "x-timezone":{}, "x-i18n":{}, "x-token":{}}},
     *   @SWG\Response(response=200, ref="#/components/responses/Response"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     *
     * @return JsonResponse
     * @throws Exception
     */
    public function PTOGetInternalAccountBalance(): JsonResponse
    {
        $request = $this->validateParameters(__METHOD__);
        $user = PTOUser::findUserByUuid($request->get('userUuid'));
        $ia = PTOInternalAccount::findInternalAccountByUuid($request->get('iaUuid'));

        if ($user === NULL)
        {
            return new FailedResponse('This user ID does not exist.', [
                'User ID' => $request->get('userUuid')
            ], 409);
        }

        if ($ia === NULL)
        {
            return new FailedResponse('This internal account ID does not exist.', [
                'Internal Account ID' => $request->get('iaUuid')
            ], 409);
        }

        $iaUser = $ia->getUser();
        if ($iaUser !== $user->getUuid())
        {
            return new FailedResponse('This Internal Account does not belong to the provided User.', [
                'User ID' => $request->get('userUuid'),
                'Internal Account ID' => $request->get('iaUuid')
            ], 409);
        }

        $balance = RPNService::getAccountBalance($ia);
        if ($balance === 'Failure')
        {
            return new FailedResponse('There was an issue retrieving the account balance. Tern has been notified.', [
                'User ID' => $request->get('userUuid'),
                'Internal Account ID' => $request->get('iaUuid')
            ]);
        }

        return new SuccessResponse([
            'Current Balance' => $balance['currentBalance'],
            'Available Balance' => $balance['availableBalance']
        ]);
    }
}
