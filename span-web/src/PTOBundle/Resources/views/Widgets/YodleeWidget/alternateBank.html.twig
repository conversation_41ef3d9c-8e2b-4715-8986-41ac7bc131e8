{% extends '@LeafLink/Layouts/onboard.html.twig' %}

{% block body_box %}
    <style>
        /* Chrome, Safari, Edge, Opera */
        input::-webkit-outer-spin-button,
        input::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }

        /* Firefox */
        input[type=number] {
            -moz-appearance: textfield;
        }
    </style>

    <div>
        <p class="alt-bank-title">Manually Link Your Bank</p>
        <div>
            <div class="container alt-bank">
                <form action="/p/submit-bank/submit-account-details" method="POST">
                    <input type="text" hidden="true" class="form-control bank-name" id="companyId" name="bankAccountId" value="{{ user.widgetToken }}">
                    <br>
                    <div class="form-group alt-form">
                        <label for="bankRouting">9-Digit Routing Number</label>
                        <input required="required" type="number" class="form-control bank-routing" id="bankRouting" name="bankRouting" maxlength=9 oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">
                    </div>
                    <div class="form-group alt-form">
                        <label for="bankAba">Bank Account Number</label>
                        <input required="required" type="number" class="form-control bank-aba" id="bankAba" name="bankAba" maxlength="17" oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">
                    </div>
                    <div class="form-group alt-form">
                        <label for="bankName">Institution Name</label>
                        <input required="required" type="text" class="form-control bank-name" id="bankName" name="bankName">
                    </div>
                    <br>
                    <button type="submit" class="btn btn-primary bank-submit">Link Bank</button>
                </form>
                <br>
                <div class="text-center">
                    <a class="ll-link-text" href="/p/submit/{{ user.widgetToken }}">Back to Bank Search</a>
                </div>
            </div>
        </div>
    </div>

{% endblock %}