<?php

/**
 * Because both "transaction" and "load transaction" need to change the balance.
 * So just execute them on the same topic.
 */

namespace SpendrBundle\Consumers;


use Carbon\Carbon;
use ClfBundle\Entity\Transaction;
use Clf<PERSON><PERSON>le\Entity\TransactionStatus;
use Clf<PERSON><PERSON>le\Entity\TransactionType;
use CoreBundle\Controller\Cron\Spendr\FeeControllerTrait;
use CoreBundle\Controller\Cron\Spendr\FFBAchServiceController;
use CoreBundle\Entity\AchBatch;
use CoreBundle\Entity\AchTransactions;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Entity\UserCardTransaction;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Service;
use CoreBundle\Utils\Util;
use Enqueue\Client\TopicSubscriberInterface;
use Interop\Queue\Context;
use Interop\Queue\Message;
use PortalBundle\Exception\PortalException;
use SpendrBundle\Services\ACH\BatchService;
use SpendrBundle\Services\ACH\FFBAchService;
use SpendrBundle\Services\ChangeBalanceService;
use SpendrBundle\Services\LoadService;
use SpendrBundle\Services\LocationService;
use SpendrBundle\Services\Pay\PayService;
use SpendrBundle\Services\PlaidService;
use SpendrBundle\Services\Queue\CorrectDataService;
use SpendrBundle\Services\Queue\LoadQueueService;
use SpendrBundle\Services\Queue\TransactionQueueService;
use SpendrBundle\Services\TerminalService;
use SpendrBundle\Services\TransactionService;
use SpendrBundle\Services\SlackService;

class TransactionProcessor extends SpendrBaseProcessor implements TopicSubscriberInterface
{
	use FeeControllerTrait;

    public static function getSubscribedTopics()
    {
        return ['spendrTransaction'];
    }

    public function process(Message $message, Context $context)
    {
        parent::process($message, $context);
        Log::debug($this->logPrefix . '=== Spendr Transaction === ' . $message->getBody());

        $msg = Util::s2j($message->getBody());
        if (empty($msg['type'])) {
            return self::REJECT;
        }

        Util::$platform = Platform::spendr();
        Util::$cardProgram = CardProgram::spendr();

        $method = 'process' . ucfirst($msg['type']);
        if (method_exists($this, $method)) {
            return $this->$method($msg);
        }

        return self::REJECT;
    }

    // ----------------------------- transaction module start -----------------------------
    protected function processPayment($msg)
    {
		$txnToken = $msg['txnToken'] ?? null;
		$appId = $msg['appId'] ?? null;
		$timezone = $msg['timezone'] ?? null;

        if (empty($msg['txnToken'])) {
            return self::REJECT;
        }

        Util::em()->clear();

        /** @var Transaction $t */
        $t = Transaction::findByToken($txnToken);
        if (!$t) {
			Log::debug(
				'=== Consumer Transaction: Transaction not found.',
				['txnToken' => $txnToken]
			);
            Data::del('spendr_confirm_payment_' . $txnToken);
        	return self::REJECT;
		}
        $txnId = $t->getId();
		$consumer = $t->getConsumer();
        if (!$consumer) {
			Log::debug(
				'=== Consumer Transaction: No consumer is bound.',
				[
					'txnToken' => $txnToken,
					'txnId' => $txnId
				]
			);
            Data::del('spendr_confirm_payment_' . $txnToken);
        	return self::REJECT;
		}

		$consumerId = $consumer->getId();

		$lockKey = ChangeBalanceService::lockKey($consumerId);
		if (!$lockKey) {
			return self::REJECT;
		}
		$requeueTimesKey = 'spendr_requeue_transaction_payment_' . $consumerId;
		if (Data::lockedHas($lockKey)) {
			if ($this->canRequeue($requeueTimesKey)) {
				return self::REQUEUE;
			} else {
                Data::del('spendr_confirm_payment_' . $txnToken);
				return self::REJECT;
			}
		} else {
			if (Data::has($requeueTimesKey)) {
				Data::del($requeueTimesKey, true);
			}
		}

        $checkRes = TransactionService::beforePay($consumer, $txnToken, $timezone);
        if (is_string($checkRes)) {
        	Log::debug('=== Consumer Transaction: ' . $checkRes, [
        		'txnId' => $txnId,
				'txnToken' => $txnToken
			]);
			ChangeBalanceService::clearLock($consumerId);
            Data::del('spendr_confirm_payment_' . $txnToken);
        	return self::REJECT;
        }

        try {
            TransactionService::pay($t, $appId);

            Log::debug('=== Consumer Transaction: Paid.', [
				'txnId' => $txnId,
				'txnToken' => $txnToken,
				'amount' => Money::formatUSD($t->getAmount()),
				'consumer' => $t->getConsumer()->getId(),
			]);
        } catch (PortalException $exception) {
            Log::exception($exception->getMessage(), $exception);
        } catch (\Throwable $throwable) {
			SlackService::exception(
				'Failed to pay transaction ' . $t->getId() . $throwable->getMessage(),
				$throwable,
				[
					'transaction' => $t->getId(),
				],
				true,
				[
					SlackService::MENTION_TRACY,
					SlackService::MENTION_JERRY
				]
			);
        }

		ChangeBalanceService::clearLock($consumerId);
        Data::del('spendr_confirm_payment_' . $txnToken);
        return self::ACK;
    }

	protected function processRestrictAreaPayment($msg)
    {
		$txnToken = $msg['txnToken'] ?? null;
		$loadId = $msg['loadId'] ?? null;
		$appId = $msg['appId'] ?? null;
		$timezone = $msg['timezone'] ?? null;

        if (empty($msg['txnToken']) || empty($msg['loadId'])) {
            return self::REJECT;
        }

        Util::em()->clear();

		$checkRes = TransactionQueueService::checkBeforeRestrictAreaTxn($msg);

		if (!$checkRes || ($checkRes && !$checkRes['success'])) {
			if ($checkRes && !$checkRes['success']) {
				$load = $checkRes['load'];
				if ($load) {
					LoadQueueService::cancelLoadWhenQueueCheckError($load, $checkRes['error'], true);
				}
			}
            Data::del('spendr_confirm_payment_' . $txnToken);
			return self::REJECT;
		}

		$load = $checkRes['load'];
		$t = $checkRes['txn'];
		$userId = $checkRes['userId'];

		$lockKey = ChangeBalanceService::lockKey($userId);
		if (!$lockKey) {
			LoadQueueService::cancelLoadWhenQueueCheckError($load, 'The system cannot be locked and cannot be traded', true);
            Data::del('spendr_confirm_payment_' . $txnToken);
			return self::REJECT;
		}
		$requeueTimesKey = 'spendr_requeue_transaction_payment_' . $userId;
		if (Data::lockedHas($lockKey)) {
			if ($this->canRequeue($requeueTimesKey)) {
				return self::REQUEUE;
			} else {
				LoadQueueService::cancelLoadWhenQueueCheckError($load, 'The system cannot rejoin the queue and cannot trade', true);
                Data::del('spendr_confirm_payment_' . $txnToken);
				return self::REJECT;
			}
		} else {
			if (Data::has($requeueTimesKey)) {
				Data::del($requeueTimesKey, true);
			}
		}

		$loadRes = 'Start txn.';
		try {
			$loadRes = Service::loadCard($load, true);
			LoadService::afterLoad($load, $loadRes, true);

			$error = false;
			if (is_string($loadRes)) {
				// do sth
				$error = true;
			} else {
				TransactionService::pay($t, $appId);
			}

			Log::debug('=== Consumer Transaction(Restrict area): Finished.', [
				'txnId' => $t->getId(),
				'loadId' => $loadId,
				'txnToken' => $txnToken,
				'txnAmount' => Money::formatUSD($t->getAmount()),
				'loadAmount' => Money::formatUSD($load->getInitialAmount()),
				'consumer' => $userId,
				'error' => $error
			]);
        } catch (PortalException $exception) {
            Log::exception($exception->getMessage(), $exception);
        } catch (\Throwable $throwable) {
			SlackService::exception(
				'Failed to pay transaction(Restrict area) ' . $t->getId() . $throwable->getMessage(),
				$throwable,
				[
					'transaction' => $t->getId(),
					'load' => $load->getId()
				],
				true,
				[
					SlackService::MENTION_TRACY,
					SlackService::MENTION_JERRY
				]
			);
        }

		TransactionQueueService::afterRestrictAreaTxnFinished($txnToken, $loadId);

		ChangeBalanceService::clearLock($userId);
        Data::del('spendr_confirm_payment_' . $txnToken);
        return self::ACK;
    }

    protected function processUpdate($msg)
	{
		$txnToken = $msg['txnToken'] ?? null;
		$action = $msg['action'] ?? null;
		if (
			empty($msg['txnToken'])
			|| empty($msg['action'])
			|| ($msg['action'] && !in_array($msg['action'], ['rescan', 'cancel']))
		) {
			return self::REJECT;
		}

		$t = TransactionService::beforeUpdate($txnToken);
		if (is_string($t)) {
			Log::debug(
				'=== Consumer Transaction ' . $action . ': ' . $t,
				['txnToken' => $txnToken]
			);
			return self::REJECT;
		}

		try {
			if ($action === 'rescan') {
				TransactionService::rescan($t);
			} else {
				TransactionService::cancel($t);
			}

			Log::debug('=== Consumer Transaction ' . $action . ': Done.', [
				'txnId' => $t->getId(),
				'txnToken' => $txnToken,
				'consumer' => $t->getConsumer() ? $t->getConsumer()->getId() : null,
				'status' => $t->getStatus()->getName()
			]);
		} catch (PortalException $exception) {
			Log::exception($exception->getMessage(), $exception);
		} catch (\Throwable $throwable) {
			SlackService::exception(
				'Failed to update transaction ' . $t->getId() . $throwable->getMessage(),
				$throwable,
				[
					'transaction' => $t->getId(),
				],
				true,
				[
					SlackService::MENTION_TRACY,
					SlackService::MENTION_JERRY
				]
			);
		}

		return self::ACK;
	}

	protected function processRefund($msg)
	{
		$txnToken = $msg['txnToken'] ?? null;
		$refundAmount = $msg['refundAmount'] ?? null;
		$employeeId = $msg['employeeId'] ?? null;
		$locationId = $msg['locationId'] ?? null;
		$terminalId = $msg['terminalId'] ?? null;
		$timezone = $msg['timezone'] ?? null;
		if (
			empty($msg['txnToken'])
			|| empty($msg['refundAmount'])
			|| empty($msg['employeeId'])
			|| empty($msg['locationId'])
			|| empty($msg['terminalId'])
			|| empty($msg['timezone'])
		) {
			return self::REJECT;
		}

		$txn = Transaction::findByToken($txnToken);

		if (!$txn || !$txn->getConsumer()) {
			return self::REJECT;
		}

		$userId = $txn->getConsumer()->getId();

		$lockKey = ChangeBalanceService::lockKey($userId);
		if (!$lockKey) {
			return self::REJECT;
		}
		$requeueTimesKey = 'spendr_requeue_transaction_refund_' . $userId;
		if (Data::lockedHas($lockKey)) {
			if ($this->canRequeue($requeueTimesKey)) {
				return self::REQUEUE;
			} else {
				return self::REJECT;
			}
		} else {
			if (Data::has($requeueTimesKey)) {
				Data::del($requeueTimesKey, true);
			}
		}

		$location = LocationService::verifyLocation($locationId, true, $employeeId);
		if (is_string($location)) {
			Log::debug(
				'=== Consumer Transaction Refund: verify location.' . $location,
				['txnToken' => $txnToken, 'locationId' => $locationId]
			);
			ChangeBalanceService::clearLock($userId);
			return self::REJECT;
		}
		$terminal = TerminalService::verifyTerminal($terminalId, $location);
		if (is_string($terminal)) {
			Log::debug(
				'=== Consumer Transaction Refund: verify terminal.' . $terminal,
				['txnToken' => $txnToken, 'terminalId' => $terminalId]
			);
			ChangeBalanceService::clearLock($userId);
			return self::REJECT;
		}
		$t = TransactionService::beforeRefund($txnToken, $refundAmount, true);
		if (is_string($t)) {
			Log::debug(
				'=== Consumer Transaction Refund: verify txn.' . $t,
				['txnToken' => $txnToken]
			);
			ChangeBalanceService::clearLock($userId);
			return self::REJECT;
		}

		try {
			TransactionService::refundAction($t, $refundAmount, $employeeId, $terminalId, $timezone);

			Log::debug('=== Consumer Transaction Refund: Done.', [
				'txnId' => $t->getId(),
				'txnToken' => $txnToken,
				'consumer' => $t->getConsumer()->getId(),
				'status' => $t->getStatus()->getName()
			]);
		} catch (PortalException $exception) {
			Log::exception($exception->getMessage(), $exception);
		} catch (\Throwable $throwable) {
			SlackService::exception(
				'Failed to refund transaction ' . $t->getId() . '. ' . $throwable->getMessage(),
				$throwable,
				[
					'transaction' => $t->getId(),
				],
				false,
				[
					SlackService::MENTION_TRACY,
					SlackService::MENTION_JERRY
				]
			);
		}

		ChangeBalanceService::clearLock($userId);
		return self::ACK;
	}

	protected function processCancelExpiredTransactions($msg)
    {
        try {
            Util::em()->clear();
            $txns = Util::em()->getRepository(Transaction::class)
                ->createQueryBuilder('t')
                ->join('t.merchant', 'm')
                ->join('m.adminUser', 'u')
                ->join('u.teams', 'r')
                ->where('r.name = :role')
                ->setParameter('role', Role::ROLE_SPENDR_MERCHANT_ADMIN)
                ->andWhere('t.status = :status')
                ->setParameter('status', TransactionStatus::get(TransactionStatus::NAME_PENDING))
                ->andWhere('t.type = :type')
                ->setParameter('type', TransactionType::get(TransactionType::NAME_PURCHASE))
                ->andWhere('t.dateTime < :endTime')
                ->setParameter('endTime', Carbon::now()->subMinutes(TransactionService::transactionExpiredTime))
                ->select('t.id')
                ->getQuery()
                ->getArrayResult();
            if (!count($txns)) {
                return self::REJECT;
            }

            $ids = Util::flattenArray($txns, 'id');

            $res = Util::em()->createQueryBuilder()
                ->update(Transaction::class, 't')
                ->set('t.status', ':status')
                ->where(Util::expr()->in('t.id', ':ids'))
                ->setParameters([
                    'status' => TransactionStatus::get(TransactionStatus::NAME_CANCELLED),
                    'ids' => $ids
                ])
                ->getQuery()
                ->execute();
            Log::debug('Spendr cancel outdated transactions', [
                $res
            ]);

            foreach ($ids as $id) {
                PayService::sendWebhook(Transaction::find($id));
            }
        } catch (PortalException $exception) {
            Log::exception('Failed to cancel expired transactions ' . $exception->getMessage(), $exception);
        } catch (\Throwable $throwable) {
            SlackService::exception(
                'Failed to cancel expired transactions ' . $throwable->getMessage(),
                $throwable,
                null,
                true,
				[SlackService::MENTION_JERRY, SlackService::MENTION_TRACY]
            );
        }

        return self::ACK;
    }
	// ----------------------------- transaction module end -----------------------------







	// ----------------------------- load module start -----------------------------
	protected function processLoadCard($msg)
	{
		if (empty($msg['loadId'])) {
			return self::REJECT;
		}

		Util::em()->clear();

		$load = UserCardLoad::find($msg['loadId']);
		$user = $load && $load->getUserCard() ? $load->getUserCard()->getUser() : null;

		if (!$user) {
			return self::REJECT;
		}

		$userId = $user->getId();

		$lockKey = ChangeBalanceService::lockKey($userId);
		if (!$lockKey) {
			return self::REJECT;
		}
		$requeueTimesKey = 'spendr_requeue_load_card_' . $userId;
		if (Data::lockedHas($lockKey)) {
			if ($this->canRequeue($requeueTimesKey)) {
				return self::REQUEUE;
			} else {
				return self::REJECT;
			}
		} else {
			if (Data::has($requeueTimesKey)) {
				Data::del($requeueTimesKey, true);
			}
		}

		$errorMsg = LoadQueueService::checkLoad($load, $user);
		SlackService::prepareForPlatform(Platform::spendr());

		if (is_string($errorMsg)) {
			//todo: Is it necessary to set the status of load to error here? Or just write to errorMsg, and re-queue?

			/**
			 * 1. Transactions that have already been processed by the bank are not allowed to be cancelled.
			 * 2. Transactions that process rollbacks are not allowed to be cancelled.
			 * If there is an abnormality in the above situation, it will be temporarily checked manually
			 */
			$achTxn = $load->getTransactionNo() ? AchTransactions::findTransactionByTranId($load->getTransactionNo()) : null;
			if (
				($achTxn && $achTxn->getTranStatus() === UserCardTransaction::STATUS_LL_SETTLED)
				|| LoadService::isLoadForUnloadFailed($load)
				|| LoadService::isUnloadForLoadFailed($load)
			) {
				$allowCancel = false;
			} else {
				$allowCancel = true;
			}

			if ($allowCancel) {
				LoadQueueService::cancelLoadWhenQueueCheckError($load, $errorMsg);
			} else {
				SlackService::alert('=== Spendr Load Card: verify(not allowed cancel). ' . $errorMsg, [
					'LoadId' => $load->getId(),
					'LoadStatus' => $load->getLoadStatus(),
					'User' => $userId,
				], [SlackService::MENTION_TRACY, SlackService::MENTION_JERRY]);
			}

			ChangeBalanceService::clearLock($userId);
			return self::REJECT;
		}

		try {
			$res = Service::loadCard($load, true);

			LoadService::afterLoad($load, $res);
			Log::debug('=== Spendr Load Card: Finished.', [
				'LoadId' => $load->getId(),
				'User' => $userId,
			]);
		} catch (PortalException $exception) {
			Log::exception($exception->getMessage(), $exception);
		} catch (\Throwable $throwable) {
			SlackService::exception(
				'Failed to load card ' . $load->getId() . ': ' . $throwable->getMessage(),
				$throwable,
				[
					'load' => $load->getId(),
				],
				true,
				[
					SlackService::MENTION_TRACY,
					SlackService::MENTION_JERRY
				]
			);
		}

		ChangeBalanceService::clearLock($userId);
		return self::ACK;
	}

	protected function processCancelLoad($msg)
	{
		if (
			empty($msg['loadId'])
			|| empty($msg['cancelReason'])
		) {
			return self::REJECT;
		}

		$load = UserCardLoad::find($msg['loadId']);
		if (!$load) {
			Log::debug('=== Spendr Cancel Load: Unknown load.', [
				'LoadId' => $msg['loadId'],
			]);
			return self::REJECT;
		}
		$user = $load->getUserCard()->getUser();

		if (!$user) {
			return self::REJECT;
		}
		$userId = $user->getId();

		$lockKey = ChangeBalanceService::lockKey($userId);
		if (!$lockKey) {
			return self::REJECT;
		}
		$requeueTimesKey = 'spendr_requeue_cancel_load_' . $userId;
		if (Data::lockedHas($lockKey)) {
			if ($this->canRequeue($requeueTimesKey)) {
				return self::REQUEUE;
			} else {
				return self::REJECT;
			}
		} else {
			if (Data::has($requeueTimesKey)) {
				Data::del($requeueTimesKey, true);
			}
		}

		$checkRes = LoadService::beforeCancelLoad($load);
		if (is_string($checkRes)) {
			Log::debug('=== Spendr Cancel Load: ' . $checkRes . '.', [
				'LoadId' => $load->getId(),
				'LoadStatus' => $load->getLoadStatus(),
				'User' => $user->getId(),
			]);
			ChangeBalanceService::clearLock($userId);
			return self::REJECT;
		}

		try {
			LoadService::cancelLoad($load, $msg['cancelReason']);

			Log::debug('=== Spendr Cancel Load: Finished.', [
				'LoadId' => $load->getId(),
				'LoadStatus' => $load->getLoadStatus(),
				'User' => $user->getId(),
			]);
            // send decision report to Plaid
            if ($user->isSpendrConsumer() && (LoadService::isInstantLoad($load) || LoadService::isPrefundLoad($load))) {
                PlaidService::sendDecisionReport($load->getTransactionNo(), false);
            }
		} catch (PortalException $exception) {
			Log::exception($exception->getMessage(), $exception);
		} catch (\Throwable $throwable) {
			SlackService::exception(
				'Failed to cancel load ' . $load->getId() . ': ' . $throwable->getMessage(),
				$throwable,
				[
					'load' => $load->getId(),
				],
				true,
				[
					SlackService::MENTION_TRACY,
					SlackService::MENTION_JERRY
				]
			);
		}

		ChangeBalanceService::clearLock($userId);
		return self::ACK;
	}

	protected function processReturnLoad($msg)
	{
		$txnId = $msg['tranId'];
		$returnCode = $msg['returnCode'];
		if (
			empty($txnId)
			|| empty($returnCode)
		) {
			return self::REJECT;
		}

		$achTxn = AchTransactions::findTransactionByTranId($txnId);
		if (!$achTxn) {
			Log::debug('=== Spendr Return Load: Unknown transaction.', [
				'Transaction' => $txnId,
				'Error Code' => $returnCode
			]);
			return self::REJECT;
		}

		$checkRes = FFBAchService::beforeReturn($achTxn);
		if (is_string($checkRes)) {
			Log::debug('=== Spendr Return Load: ' . $checkRes . '.', [
				'Transaction' => $txnId,
				'Error Code' => $returnCode,
				'Transaction Status' => $achTxn->getTranStatus()
			]);
			return self::REJECT;
		}

		try {
			FFBAchService::returnAction($achTxn, $returnCode);

			Log::debug('=== Spendr Return Load: Finished.', [
				'Transaction' => $txnId,
				'Error Code' => $returnCode,
			]);
		} catch (PortalException $exception) {
			Log::exception($exception->getMessage(), $exception);
		} catch (\Throwable $throwable) {
			SlackService::exception(
				'Failed to return load txn ' . $txnId . ': ' . $throwable->getMessage(),
				$throwable,
				[
					'ach transaction' => $txnId,
				],
				true,
				[
					SlackService::MENTION_TRACY,
					SlackService::MENTION_JERRY
				]
			);
		}

		return self::ACK;
	}

	protected function processBatchFile($msg)
	{
		$processType = $msg['processType'];
		if (empty($processType)) {
			return self::REJECT;
		}
		
		$batchForTest = $msg['batchForTest'];

		if (!in_array($processType, [
			AchBatch::ACH_BATCH_PROCESS_TYPE_STANDARD,
			AchBatch::ACH_BATCH_PROCESS_TYPE_SAME_DAY
		])) {
			Log::debug('=== Spendr Batch File: Invalid process type.', [
				'Process Type' => $processType
			]);
			return self::REJECT;
		}

		try {
            FFBAchServiceController::executeAchBatchGeneration($processType, $batchForTest);
			Log::debug('=== Spendr Batch File: Finished.', [
				'Process Type' => $processType,
			]);
		} catch (\Throwable $throwable) {
            FFBAchServiceController::clearAchBatchLock();
			SlackService::exception(
				'Failed to batch file',
				$throwable,
				null,
				true,
                [
                    SlackService::MENTION_TRACY,
                    SlackService::MENTION_SPENDR_RYAN,
                    SlackService::MENTION_HANS,
                ],
			);
		}

		return self::ACK;
	}

    protected function processResetFailedBatch($msg)
    {
        if (FFBAchServiceController::isAchBatchLocked()) {
            Log::debug('=== Spendr Reset Failed File: Locked.');
            return self::REJECT;
        }

        $batch = BatchService::findLatestFailedBatch();
        if (!$batch) {
            Log::debug('=== Spendr Reset Failed File: Not found.');
            return self::REJECT;
        }

        BatchService::resetLatestFailedBatch($batch);

        return self::ACK;
    }

	protected function processChargeConsumerInactivityFee()
	{
		try {
			$this->traitChargeMonthlyInactivityFee();
			Log::debug('=== Spendr Charge Consumer Inactivity Fee: Finished.');
		} catch (PortalException $exception) {
			Log::exception($exception->getMessage(), $exception);
		} catch (\Throwable $throwable) {
			SlackService::exception(
				'Failed to charge consumer inactivity fee: ' . $throwable->getMessage(),
				$throwable,
				null,
				true,
				[
					SlackService::MENTION_TRACY,
					SlackService::MENTION_JERRY
				]
			);
		}

		return self::ACK;
	}

	protected function processChargeConsumerInactivityFeeAction($msg)
	{
		if (
			empty($msg['userId'])
			|| empty($msg['feeName'])
			|| empty($msg['fee'])
		) {
			return self::REJECT;
		}

		$lockKey = ChangeBalanceService::lockKey($msg['userId']);
		if (!$lockKey) {
			return self::REJECT;
		}
		$requeueTimesKey = 'spendr_requeue_consumer_inactivity_fee_' . $msg['userId'];
		if (Data::lockedHas($lockKey)) {
			if ($this->canRequeue($requeueTimesKey)) {
				return self::REQUEUE;
			} else {
				return self::REJECT;
			}
		} else {
			if (Data::has($requeueTimesKey)) {
				Data::del($requeueTimesKey, true);
			}
		}

		try {
			$this->traitChargeMonthlyInactivityFeeAction($msg['userId'], $msg['feeName'], $msg['fee']);
			Log::debug('=== Spendr Charge single Consumer Inactivity Fee: Finished.');
		} catch (PortalException $exception) {
			Log::exception($exception->getMessage(), $exception);
		} catch (\Throwable $throwable) {
			SlackService::exception(
				'Failed to charge single consumer inactivity fee: ' . $throwable->getMessage(),
				$throwable,
				null,
				true,
				[
					SlackService::MENTION_TRACY,
					SlackService::MENTION_JERRY
				]
			);
		}

		ChangeBalanceService::clearLock($msg['userId']);
		return self::ACK;
	}

	// ----------------------------- load module end -----------------------------

	protected function processCorrectData($msg)
	{
		$funName = $msg['funName'];
		if (empty($funName)) {
			return self::REJECT;
		}

		try {
			$res = CorrectDataService::correctData($funName, $msg);
			Log::debug('=== Correct data "' . $funName . '": Finished.', [$res]);
		} catch (PortalException $exception) {
			Log::exception($exception->getMessage(), $exception);
		} catch (\Throwable $throwable) {
			SlackService::exception(
				'Failed to correct data "' . $funName . '": ' . $throwable->getMessage(),
				$throwable,
				[
					'method name' => $funName
				],
				true,
				[
					SlackService::MENTION_TRACY,
					SlackService::MENTION_JERRY
				]
			);
		}

		return self::ACK;
	}
}
