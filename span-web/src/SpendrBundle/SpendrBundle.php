<?php

namespace SpendrBundle;


use Core<PERSON>undle\Entity\Module;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserToken;
use CoreBundle\Utils\Util;
use PortalBundle\Exception\PortalException;
use SalexUserBundle\Entity\User;
use Spendr<PERSON><PERSON>le\Entity\SpendrGroup;
use Spendr<PERSON><PERSON>le\Entity\SpendrMerchant;
use SpendrBundle\Entity\SpendrTip;
use SpendrBundle\Entity\TippingEntity;
use SpendrBundle\Services\MerchantService;
use SpendrBundle\Services\Next\RemixService;
use SpendrBundle\Services\Pay\PayService;
use SpendrBundle\Services\TipService;
use Symfony\Component\HttpKernel\Bundle\Bundle;

class SpendrBundle extends Bundle
{
    public const FIRST_MERCHANT_ID = 23;
    public const SYSTEM_LAUNCH_YEAR = 2022;

	public static function getUserDashboardType()
	{
		return 'spendr';
	}

    public static function apiAdminUserProfile($data)
    {
        $data['cpKey'] = 'cp_spendr';
        $data['adminLayout'] = 'h';

        $user = Util::user();

        if ($user && $user->inTeams(self::getMerchantAdminRoles())) {
            $merchant = $user->getSpendrAdminMerchant();
            $data['merchantId'] = $merchant->getId();
            $data['merchantStatus'] = $merchant->getOnboardingStatus();
            $data['merchantApproved'] = $merchant->isApproved();
            $data['isLocationHasDummyMerchant'] = MerchantService::isLocationHasDummyMerchant($merchant);

            if (in_array($data['merchantStatus'], [
                SpendrMerchant::ONBOARDING_STATUS_INITIAL,
                SpendrMerchant::ONBOARDING_STATUS_DENIED,
            ])) {
                $data['defaultRoute'] = '/h/spendr/merchant/onboard';
            }
        }

        return $data;
    }

    public static function getAdminDomain()
    {
        if (Util::isLive()) {
            return 'https://admin.spendr.com';
        }
        if (Util::isStaging()) {
            return 'https://stage.spendr.com';
        }
        return Util::request()->getScheme() . '://spendr.span.local';
    }

    public static function getAppDomain()
    {
        if (Util::isLive()) {
            return 'https://app.spendr.com';
        }
        if (Util::isStaging()) {
            return 'https://app-stage.spendr.com';
        }
        return Util::request()->getScheme() . '://spendr-app.span.local';
    }

    public static function getApiDomain()
    {
        if (Util::isLive()) {
            return 'https://spendr.virtualcards.us';
        }
        if (Util::isStaging()) {
            return 'https://spendr-test.virtualcards.us';
        }
        return Util::request()->getScheme() . '://spendr.span.local';
    }

    public static function getAppHosts()
    {
        return [
            'app.spendr.com',
            'app-test.spendr.com',
            'spendr-app-test.virtualcards.us',
            'spendr-app.span.local',
        ];
    }

    public static function getWebAppEntrance()
    {
        return '/spendr';
    }

    public static function getWebAppPath()
    {
        return '/static/spendr/web/';
    }

    public static function limitAdminDomain()
    {
        $host = strtolower(Util::request()->getSchemeAndHttpHost());
        if (Util::endsWith($host, '.virtualcards.us')) {
            return;
        }
        if (self::getAdminDomain() !== $host) {
            throw PortalException::tempPage('Please log into the admin domain to access this page.');
        }
    }

    public static function frameOptions()
    {
        $prefix = 'ALLOW-FROM ';
        $host = Util::host();
        if ($host === 'http://localhost' || Util::startsWith($host, 'http://localhost:')) {
            return $prefix . $host;
        }
        if (in_array($host, [
            'https://admin.spendr.com',
            'https://app.spendr.com',
            'https://app-test.spendr.com',
            'https://test.spendr.com',
            'https://spendr.virtualcards.us',
            'https://spendr-test.virtualcards.us',
            'https://spendr.span.hans',
            'https://spendr.span.local',
            'https://spendr-app.span.hans',
            'https://spendr-app.span.local',
        ])) {
            return $prefix . $host;
        }
        return Util::FRAME_OPTIONS;
    }

    public static function isSpendrAdminLoggedInAs()
    {
        $u = Util::getImpersonatingUser();
        if (!$u) {
            return false;
        }
        return $u->inTeams(self::getSpendrAdminRoles());
    }

	public static function isSpendrEmployeeCSLoggedInAs()
	{
		$u = Util::getImpersonatingUser();
		if (!$u) {
			return false;
		}
		return $u->inTeams(self::getSpendrEmployeeCSRoles());
	}

	public static function isSpendrEmployeeComplianceLoggedInAs()
	{
		$u = Util::getImpersonatingUser();
		if (!$u) {
			return false;
		}
		return $u->inTeams(self::getSpendrEmployeeComplianceRoles());
	}

	public static function isSpendrEmployeeAccountantLoggedInAs()
	{
		$u = Util::getImpersonatingUser();
		if (!$u) {
			return false;
		}
		return $u->inTeams([Role::ROLE_SPENDR_ACCOUNTANT]);
	}

	public static function isSpendrEmployeeLoggedInAs()
	{
		$u = Util::getImpersonatingUser();
		if (!$u) {
			return false;
		}
		return $u->inTeams(self::getSpendrEmployeeRoles());
	}

	public static function getAdminRoles() {
		return array_merge(
			[
				Role::ROLE_SPENDR_BANK
			],
			self::getSpendrAdminRoles(),
			self::getMerchantAdminRoles(),
			self::getMerchantEmployeeRoles(),
            self::getLocationBalanceAdminRoles()
		);
	}

    public static function getSpendrGroupAdminRoles() {
        return [
            Role::ROLE_SPENDR_GROUP_ADMIN,
        ];
    }

	public static function getSpendrAdminRoles() {
		return array_merge(
			[
				Role::ROLE_SPENDR_PROGRAM_OWNER
			],
			self::getSpendrEmployeeRoles()
		);
	}

	public static function getSpendrEmployeeRoles() {
		return array_merge(
			self::getSpendrEmployeeCSRoles(),
			self::getSpendrEmployeeComplianceRoles(),
			[Role::ROLE_SPENDR_ACCOUNTANT]
		);
	}

	public static function getSpendrEmployeeCSRoles() {
		return [
			Role::ROLE_SPENDR_CUSTOMER_SUPPORT,
		];
	}

	public static function getSpendrEmployeeComplianceRoles() {
		return [
			Role::ROLE_SPENDR_COMPLIANCE,
		];
	}

	public static function getViewOnlyRoles() {
		return [
			Role::ROLE_SPENDR_ACCOUNTANT,
			Role::ROLE_SPENDR_MERCHANT_OTHER_ADMIN
		];
	}

	public static function notAllowedExport()
	{
		$user = Util::user();
		return $user->inTeams([
			Role::ROLE_SPENDR_BANK,
			Role::ROLE_SPENDR_CUSTOMER_SUPPORT,
			Role::ROLE_SPENDR_MERCHANT_OTHER_ADMIN
		]) || self::isSpendrEmployeeCSLoggedInAs();
	}

	public static function getMerchantAdminRoles() {
		return array_merge(
			self::getMerchantBalanceAdminRoles(),
			self::getMerchantDashboardAdminRoles()
		);
	}

	public static function getMerchantBalanceAdminRoles() {
		return [
			Role::ROLE_SPENDR_MERCHANT_ADMIN,
		];
	}

    public static function getApiAdminRoles() {
        return [
            Role::ROLE_SPENDR_GROUP_ADMIN,
        ];
    }

	public static function getMerchantDashboardAdminRoles() {
		return [
			Role::ROLE_SPENDR_MERCHANT_MASTER_ADMIN,
			Role::ROLE_SPENDR_MERCHANT_OPERATOR_ADMIN,
			Role::ROLE_SPENDR_MERCHANT_MANAGER_ADMIN,
			Role::ROLE_SPENDR_MERCHANT_ACCOUNTANT_ADMIN,
			Role::ROLE_SPENDR_MERCHANT_OTHER_ADMIN,
		];
	}

	public static function getMerchantDashboardTopLevelAdminRoles() {
		return [
			Role::ROLE_SPENDR_MERCHANT_MASTER_ADMIN,
			Role::ROLE_SPENDR_MERCHANT_OPERATOR_ADMIN,
		];
	}

	public static function getMerchantDashboardLowLevelAdminRoles() {
		return [
			Role::ROLE_SPENDR_MERCHANT_MANAGER_ADMIN,
			Role::ROLE_SPENDR_MERCHANT_ACCOUNTANT_ADMIN,
			Role::ROLE_SPENDR_MERCHANT_OTHER_ADMIN,
		];
	}

	public static function getMerchantEmployeeRoles() {
		return [
			Role::ROLE_SPENDR_MERCHANT_MANAGER,
			Role::ROLE_SPENDR_MERCHANT_ASSISTANT_MANAGER,
			Role::ROLE_SPENDR_CLERKS,
		];
	}

	public static function getMerchantEmployeeMobileLoginRoles() {
		return [
			Role::ROLE_SPENDR_MERCHANT_MANAGER,
			Role::ROLE_SPENDR_MERCHANT_ASSISTANT_MANAGER,
		];
	}

	public static function getLocationBalanceAdminRoles() {
		return [
			Role::ROLE_SPENDR_LOCATION_BALANCE_ADMIN,
		];
	}

	public static function getConsumerRoles() {
		return [
			Role::ROLE_SPENDR_CONSUMER
		];
	}

    public static function getMemberRoles() {
        return self::getConsumerRoles();
    }

	public static function getMobileRoles() {
		return array_merge(self::getMerchantEmployeeRoles(), self::getConsumerRoles());
	}

	public static function getMobileLoginRoles() {
		return array_merge(self::getMerchantEmployeeMobileLoginRoles(), self::getConsumerRoles());
	}

	public static function getRoles() {
		return array_merge(
			self::getAdminRoles(),
			self::getConsumerRoles()
		);
	}

	public static function getPlatformName() {
		return Platform::NAME_SPENDR;
	}

	public static function getMenusForCurrentUser()
	{
        $user = Util::user();
        $sa = $user->isMasterAdmin();
        $owner = $user->inTeam(Role::ROLE_SPENDR_PROGRAM_OWNER);
        $bank = $user->inTeam(Role::ROLE_SPENDR_BANK);
        $group = $user->inTeam(Role::ROLE_SPENDR_GROUP_ADMIN);

        $merchantAdmin = $user->inTeams(self::getMerchantAdminRoles());
        $merchantTopLevel = $user->inTeams(self::getMerchantDashboardTopLevelAdminRoles());
        $merchantManagerAdmin = $user->inTeams([Role::ROLE_SPENDR_MERCHANT_MANAGER_ADMIN]);
        $merchantAccountantAdmin = $user->inTeams([Role::ROLE_SPENDR_MERCHANT_ACCOUNTANT_ADMIN]);
        $merchantOtherAdmin = $user->inTeams([Role::ROLE_SPENDR_MERCHANT_OTHER_ADMIN]);

        $spendrEmployee = $user->inTeams(self::getSpendrEmployeeRoles());
        $spendrEmployeeCustomerSupport = $user->inTeams(self::getSpendrEmployeeCSRoles());
        $spendrEmployeeCompliance = $user->inTeams(self::getSpendrEmployeeComplianceRoles());
        $spendrEmployeeAccountant = $user->inTeams([Role::ROLE_SPENDR_ACCOUNTANT]);

        $merchant = $user->getSpendrAdminMerchant();
        $tipping = null;
        if ($merchant) {
            $tipping = TippingEntity::findOneByMerchant();
            if (!$tipping || !$tipping->getEnable()) {
                $tipping = null;
            } else {
                $tipping = TippingEntity::findOneByMerchant($merchant);
            }
        }

        if (!$sa && !$owner && !$bank && !$merchantAdmin && !$spendrEmployee && !$group) {
            return [];
        }

        $all = [];
        if (!$spendrEmployeeCustomerSupport && !$group) {
        	$all[] = [
				'id' => Module::ID_DASHBOARD,
				'name' => 'Dashboard',
				'route' => '',
				'mdRoute' => 'spendr/dashboard',
				'icon' => '',
				'mdIcon' => 'mdi-chart-box-outline',
				'svgIcon' => false,
				'children' => [],
			];
		}

        if ($sa || $owner || $merchantAdmin || $bank) {
            $all[] = [
                'id' => Module::ID_MEX_ADMIN,
                'name' => 'Admins',
                'route' => '',
                'mdRoute' => $merchantAdmin ? 'spendr/merchant/admins' : 'spendr/admins',
                'icon' => '',
                'mdIcon' => 'mdi-shield-crown-outline',
                'svgIcon' => false,
                'children' => [],
            ];
        }

        if ($sa || $owner || $spendrEmployeeCompliance || $spendrEmployeeAccountant) {
			$all[] = [
                'id' => Module::ID_SPENDR_GROUP_LIST,
                'name' => 'Groups',
                'route' => '',
                'mdRoute' => 'spendr/groups',
                'icon' => '',
                'mdIcon' => 'mdi-home-group',
                'svgIcon' => false,
                'children' => [],
            ];
		}

        if ($sa || $owner || $bank || $spendrEmployee) {
            array_push($all,
                [
                    'id' => Module::ID_MERCHANT_LIST,
                    'name' => 'Merchants',
                    'route' => '',
                    'mdRoute' => 'spendr/merchants',
                    'icon' => '',
                    'mdIcon' => 'mdi-cart-outline',
                    'svgIcon' => false,
                    'children' => [],
                ],
			);
        }

        if ($sa || $owner || $spendrEmployee) {
        	array_push($all,
				[
					'id' => Module::ID_MEX_MEMBERS,
					'name' => 'Members',
					'route' => '',
					'mdRoute' => 'spendr/members',
					'icon' => '',
					'mdIcon' => 'mdi-account-multiple-outline',
					'svgIcon' => false,
					'children' => [],
				],
			);
		}

		if ($merchantAdmin) {
			if (!$merchantManagerAdmin && !$merchantOtherAdmin && !$merchantAccountantAdmin) {
				array_push($all, [
					'id' => Module::ID_MEX_SETTINGS,
					'name' => 'Onboarding',
					'route' => '',
					'mdRoute' => 'spendr/merchant/onboard',
					'icon' => '',
					'mdIcon' => 'mdi-file-document-multiple-outline',
					'svgIcon' => false,
					'children' => [],
				]);
			}
            array_push($all,
                [
                    'id' => Module::ID_MEX_MEMBERS,
                    'name' => 'Consumers',
                    'route' => '',
                    'mdRoute' => 'spendr/consumers',
                    'icon' => '',
                    'mdIcon' => 'mdi-account-multiple-outline',
                    'svgIcon' => false,
                    'children' => [],
                ],
                [
                    'id' => Module::ID_MEX_SETTINGS_LOCATION,
                    'name' => 'Locations',
                    'route' => '',
                    'mdRoute' => 'spendr/locations',
                    'icon' => '',
                    'mdIcon' => 'mdi-map-marker-multiple-outline',
                    'svgIcon' => false,
                    'children' => [],
                ],
                [
                    'id' => Module::ID_MEX_EMPLOYER_EMPLOYEE,
                    'name' => 'Employees',
                    'route' => '',
                    'mdRoute' => 'spendr/employees',
                    'icon' => '',
                    'mdIcon' => 'mdi-badge-account-horizontal-outline',
                    'svgIcon' => false,
                    'children' => [],
                ]);
        }

		if ($sa || $owner || $spendrEmployeeCompliance || $spendrEmployeeAccountant) {
			array_push($all,
				[
					'id' => Module::ID_SPENDR_SETTING_REWARDS,
					'name' => 'Rewards',
					'route' => '',
					'mdRoute' => 'spendr/rewards',
					'icon' => 'fa fa-regular fa-money-bill',
					'mdIcon' => 'mdi-gift-outline',
					'svgIcon' => false,
					'children' => [],
				],
			);
		}

        $report = [];
		if ($sa || $owner || $spendrEmployeeCompliance || $spendrEmployeeAccountant || $merchantAdmin) {
			array_push($all,
				[
					'id' => Module::ID_TRANSACTIONS,
					'name' => 'Transactions',
					'route' => '',
					'mdRoute' => $sa || $owner ? 'spendr/merchant-transactions' : 'spendr/merchant/transactions',
					'icon' => 'fa fa-fw fa-list',
					'mdIcon' => 'mdi-format-list-bulleted',
					'svgIcon' => false,
					'children' => [],
				], [
					'id' => Module::ID_LOADS,
					'name' => 'Load/Unload',
					'route' => '',
					'mdRoute' => $sa || $owner ? 'spendr/loads' : 'spendr/merchant/loads',
					'icon' => 'fa fa-fw fa-credit-card',
					'mdIcon' => 'mdi-credit-card',
					'svgIcon' => false,
					'children' => [],
				]
			);
		}

		if ($sa) {
			array_push($report,
				[
					'name' => 'Load/Unload Report',
					'route' => '',
					'mdRoute' => 'spendr/load-report'
				],
				[
					'name' => 'Transaction Report',
					'route' => '',
					'mdRoute' => 'spendr/transaction-report'
				],
				[
					'name' => 'Running Balance Report',
					'route' => '',
					'mdRoute' => 'spendr/running-balance-report'
				],
			);
		}

		if ($sa || $owner || $spendrEmployeeCompliance || $spendrEmployeeAccountant) {
            array_push($report,
                [
                    'name' => 'Promo Code Report',
                    'route' => '',
                    'mdRoute' => 'spendr/promotion-report',
                ],
                [
                    'name' => 'Earn Report',
                    'route' => '',
                    'mdRoute' => 'spendr/earn-report',
                ],
                [
                    'name' => 'Rewards Report',
                    'route' => '',
                    'mdRoute' => 'spendr/rewards-report',
                ],
                [
                    'name' => 'Referral Report',
                    'route' => '',
                    'mdRoute' => 'spendr/referral-report',
                ],
            );
        }

		if ($sa || $owner || $spendrEmployeeCompliance || $spendrEmployeeAccountant) {
			array_push($report,
				[
					'name' => 'Tern Fee Report',
					'route' => '',
					'mdRoute' => 'spendr/fee-report/tern'
				],
				[
					'name' => 'Bank Fee Report',
					'route' => '',
					'mdRoute' => 'spendr/fee-report/bank'
				],
				[
					'name' => 'Estimated Bank Fee Report',
					'route' => '',
					'mdRoute' => 'spendr/fee-report/estimated-bank'
				],
			);
		}

		if ($sa || $owner || $spendrEmployeeCompliance || $spendrEmployeeAccountant) {
			array_push($all,
				[
					'id' => Module::ID_BANK_LEDGER,
					'name' => 'Bank Ledger',
					'route' => '',
					'mdRoute' => 'spendr/transactions',
					'icon' => '',
					'mdIcon' => 'mdi-file-document-multiple-outline',
					'svgIcon' => false,
					'children' => [],
				]
			);
		}

        if ($sa || $owner || $spendrEmployeeCompliance || $spendrEmployeeAccountant || ($merchantAdmin && $tipping)) {
            array_push($report,
                [
                    'name' => 'Tip Report',
                    'route' => '',
                    'mdRoute' => $merchantAdmin ? 'spendr/merchant/tip-report' : 'spendr/tip-report'
                ]
            );
            if ($tipping && !$tipping->getChangeable() && !TipService::getTipCount($merchant)) {
                array_pop($report);
            }
        }

		if ($sa || $owner || $spendrEmployeeCompliance || $spendrEmployeeAccountant || $merchantAdmin) {
		    array_push($report,
                [
                    'id' => Module::ID_BALANCE_HISTORY,
                    'name' => 'Balance History',
                    'route' => '',
                    'mdRoute' => $merchantAdmin ? 'spendr/merchant/balance-history' : 'spendr/balance-history'
                ]
            );
		}

		if ($sa || $owner || $spendrEmployeeCompliance || $spendrEmployeeAccountant || $bank) {
			array_push($report,
                [
                    'id' => Module::ID_ACH_FILES,
                    'name' => 'Nacha Activity',
                    'route' => '',
                    'mdRoute' => 'spendr/ach-files'
                ]
			);
		}

		if ($sa || $owner || $spendrEmployeeCompliance || $spendrEmployeeAccountant || $bank) {
			array_push($report,
                [
                    'id' => Module::ID_ACH_FILES,
                    'name' => 'Nacha Returns',
                    'route' => '',
                    'mdRoute' => 'spendr/ach-returns'
                ]);
		}

		if ($sa || $owner || $spendrEmployeeCompliance || $spendrEmployeeAccountant) {
			array_push($report,
                [
                    'id' => Module::ID_RECON_ACTIVITY,
                    'name' => 'Activity Report',
                    'route' => '',
                    'mdRoute' => 'spendr/recon-report/activity'
                ],
				[
                    'id' => Module::ID_RECON_LEDGER,
                    'name' => 'Ledger Report',
                    'route' => '',
                    'mdRoute' => 'spendr/recon-report/ledger'
                ],
                [
                    'id' => Module::ID_MEX_KYC_EXCEPTIONS,
                    'name' => 'KYC Exceptions',
                    'route' => '',
                    'mdRoute' => 'spendr/kyc_report'
                ]
			);
		}

        if (count($report)) {
            array_push($all, [
                'id' => Module::ID_REPORTS,
                'name' => 'Reports',
                'route' => '',
                'mdRoute' => '',
                'icon' => 'fa fa-fw fa-file-text',
                'mdIcon' => 'mdi-file-chart-outline',
                'svgIcon' => false,
                'children' => $report,
            ]);
        }
        if ($sa || $owner || $spendrEmployeeCompliance || $spendrEmployeeAccountant) {
            $setting = [
                [
                    'id' => Module::ID_SPENDR_SETTING_PLAID,
                    'name' => 'Card Setting',
                    'route' => '',
                    'mdRoute' => 'spendr/plaid-setting'
                ],
                [
                    'id' => Module::ID_SPENDR_SETTING_FUZZY_MATCH,
                    'name' => 'Fuzzy Match',
                    'route' => '',
                    'mdRoute' => 'spendr/fuzzy-match-setting'
                ],
                [
                    'id' => Module::ID_SPENDR_SETTING_VERSION,
                    'name' => 'App Version',
                    'route' => '',
                    'mdRoute' => 'spendr/app-version-setting'
                ],
                [
                    'id' => Module::ID_SPENDR_SETTING_REWARDS,
                    'name' => 'Earn Rewards',
                    'route' => '',
                    'mdRoute' => 'spendr/rewards-setting'
                ],
                [
                    'id' => Module::ID_SPENDR_SETTING_TIPPING,
                    'name' => 'Tipping',
                    'route' => '',
                    'mdRoute' => 'spendr/tipping',
                ],
                [
                    'id' => Module::ID_SPENDR_SETTING_REFERRAL,
                    'name' => 'User Referral',
                    'route' => '',
                    'mdRoute' => 'spendr/referral-setting',
                ],
                [
                    'id' => Module::ID_SPENDR_SETTING_RESTRICT,
                    'name' => 'Restrict Features',
                    'route' => '',
                    'mdRoute' => 'spendr/restrict',
                ],
                [
                    'id' => Module::ID_SPENDR_SETTING_USERNAME_BLACKLIST,
                    'name' => 'Username Blacklist',
                    'route' => '',
                    'mdRoute' => 'spendr/username-blacklist',
                ],
                [
                    'id' => Module::ID_SPENDR_SETTING_BANKCARD_BLACKLIST,
                    'name' => 'Bankcard Blacklist',
                    'route' => '',
                    'mdRoute' => 'spendr/bankcard-blacklist'
                ]
            ];

            if ($sa || ($owner && in_array($user->getEmail(), ['<EMAIL>']))) {
                $setting[] = [
                    'id' => Module::ID_ERROR_MONITOR,
                    'name' => 'System Log',
                    'route' => '',
                    'mdRoute' => 'monitor/system-error',
                ];
            }

            array_push($all,
                [
                    'id' => Module::ID_SPENDR_SETTING,
                    'name' => 'Setting',
                    'route' => '',
                    'mdRoute' => '',
                    'icon' => 'fa fa-regular fa-gear',
                    'mdIcon' => 'mdi-cog-outline',
                    'svgIcon' => false,
                    'children' => $setting,
                ],
            );
        }
        if ($merchantAdmin) {
            $setting = [];
            if ($tipping && $tipping->getChangeable()) {
                $setting[] = [
                    'id' => Module::ID_SPENDR_SETTING_TIPPING,
                    'name' => 'Tipping',
                    'route' => '',
                    'mdRoute' => 'spendr/merchant/tipping',
                ];
            }

            $setting[] = [
                'id' => Module::ID_SPENDR_MERCHANT_SETTING_AUTO_WITHDRAWAL,
                'name' => 'Auto Withdrawal',
                'route' => '',
                'mdRoute' => 'spendr/merchant/setting/auto-withdrawal'
            ];
            $all[] = [
                'id' => Module::ID_SPENDR_SETTING,
                'name' => 'Setting',
                'route' => '',
                'mdRoute' => '',
                'icon' => 'fa fa-solid fa-gift',
                'mdIcon' => 'mdi-cash',
                'svgIcon' => false,
                'children' => $setting,
            ];
        }
        if ($user->inTeams(self::getApiAdminRoles())) {
            $all[] = [
                'id' => Module::ID_DEVELOPER,
                'name' => 'Developer Tools',
                'route' => '',
                'icon' => 'fa fa-fw fa-code',
                'mdIcon' => 'mdi-code-tags',
                'children' => [
                    [
                        'id' => Module::ID_DEVELOPER_RESOURCES,
                        'name' => 'API Config',
                        'route' => '',
                        'mdRoute' => 'spendr/group/api/config'
                    ],
                    [
                        'id' => Module::ID_DEV_DOCUMENTATION,
                        'name' => 'Documentation',
                        'route' => '',
                        'mdRoute' => 'spendr/group/api/doc'
                    ],
                ],
            ];
        }
        return $all;

            // Phase 1.1
//			[
//				'id' => Module::ID_MERCHANT_ONBOARD_QUEUE,
//				'name' => 'Onboarding Queue',
//				'route' => '',
//				'mdRoute' => 'spendr/onboarding',
//				'icon' => '',
//				'mdIcon' => 'mdi-rocket-outline',
//				'svgIcon' => false,
//				'children' => [],
//			],
//			[
//				'id' => Module::ID_REPORTS,
//				'name' => 'Reports',
//				'route' => '',
//				'mdRoute' => '',
//				'icon' => '',
//				'mdIcon' => 'mdi-script-text-outline',
//				'svgIcon' => false,
//				'children' => [
//                    [
//                        'id' => Module::ID_PROMOTION_REPORT,
//                        'name' => 'Promotion Report',
//                        'route' => '',
//                        'mdRoute' => 'spendr/reports/promotion',
//                    ],
//                ],
//			],

            // Phase 2
//            [
//                'id' => Module::ID_MONITOR,
//                'name' => 'Monitoring',
//                'route' => '',
//                'mdRoute' => '',
//                'icon' => '',
//                'mdIcon' => 'mdi-console',
//                'svgIcon' => false,
//                'children' => [
//                    [
//                        'id' => Module::ID_VELOCITY_REPORT,
//                        'name' => 'Monitoring Report',
//                        'route' => '',
//                        'mdRoute' => 'spendr/monitoring/report',
//                    ],
//                    [
//                        'id' => Module::ID_VELOCITY_SETTING,
//                        'name' => 'Monitoring Settings',
//                        'route' => '',
//                        'mdRoute' => 'spendr/monitoring/setting',
//                    ],
//                ],
//            ],
	}

    public static function getApiPermissionTree($default)
    {
        $user = Util::user();
        if ($user && $user->isMasterAdmin()) {
            return [
                'Report' => [
                    'Test' => 'api_spendr_report_test',
                ],
                'Export' => [
                    'Config' => 'api_spendr_export_config',
                    'TipConfig' => 'api_spendr_export_tip_config',
                    'Users' => 'api_spendr_export_users',
                    'Cards' => 'api_spendr_export_cards',
                    'Ledger' => 'api_spendr_export_ledger',
                    'Loads' => 'api_spendr_export_loads',
                    'Transactions' => 'api_spendr_export_transactions',
                    'AchTransactions' => 'api_spendr_export_ach_transactions',
                    'AchBatches' => 'api_spendr_export_ach_batches',
                    'Fees' => 'api_spendr_export_fees',
                    'Merchants' => 'api_spendr_export_merchants',
                    'Raw' => 'api_spendr_export_raw',
                    'Notes' => 'api_spendr_export_notes',
                    'Tokens' => 'api_spendr_export_tokens',
                    'Identity' => 'api_spendr_export_identity_logs',
                    'Attachments' => 'api_spendr_export_attachments',
                    'AttachmentData' => 'api_spendr_export_attachment_data',
                    'Avatar' => 'api_spendr_export_avatar'
                ],
                'ACH' => [
                    'Upload ACH file' => 'api_spendr_ach_upload',
                    'Import bank ledger' => 'api_spendr_ach_import_bank_ledger',
                    'Create batch file' => 'api_spendr_ach_batch_file',
                    'Create batch files' => 'api_spendr_ach_batch_all',
                    'Download batch file' => 'api_spendr_ach_download_batch_file',
                    'Sync batch' => 'api_spendr_ach_sync_batch',
                    'Import return file' => 'api_spendr_ach_import_return',
                    'Sync transaction' => 'api_spendr_ach_sync_transaction',
                    'Reset failed batch' => 'api_spendr_ach_reset_failed_batch_file',
                ],
                'Plaid' => [
                    'Check balance' => 'api_spendr_plaid_check_balance',
                    'Create link token' => 'api_spendr_plaid_create_link_token',
                    'Exchange access token' => 'api_spendr_plaid_exchange_access_token',
                    'Get account info' => 'api_spendr_plaid_account',
                    'Add card to the previous Plaid account' => 'api_spendr_plaid_add_card',
                    'Get account meta' => 'api_spendr_plaid_meta',
                    'Send decision report' => 'api_spendr_plaid_send_decision_report',
                    'Send return report' => 'api_spendr_plaid_send_return_report',
                ],
                'Card' => [
                    'Blacklist list' => 'api_spendr_plaid_bankcard_blacklist_list',
                    'Blacklist save' => 'api_spendr_plaid_bankcard_blacklist_save',
                    'Blacklist remove' => 'api_spendr_plaid_bankcard_blacklist_remove',
                    'Blacklist check' => 'api_spendr_plaid_bankcard_blacklist_check',
                ],
            ];
        }

        return [];
    }

    public static function needAuthApiModule(User $invoker, $module)
    {
        if ( ! Util::hasPrefix($module, [
            'api_spendr_report_',
            'api_spendr_export_',
            'api_spendr_pay_',
        ])) {
            return null;
        }
        $ut = UserToken::findForUser($invoker, UserToken::SCOPE_API);
        if ($ut && $ut->getToken() === RemixService::TOKEN) {
            return false;
        }
        if ($ut && Util::startsWith($module, 'api_spendr_pay_')) {
            if ($invoker->inTeams(self::getApiAdminRoles())) {
                return false;
            }
        }
        return null;
    }
}
