<?php

namespace SpendrBundle\Services;

use Carbon\Carbon;
use C<PERSON><PERSON><PERSON>le\Entity\Transaction;
use <PERSON><PERSON><PERSON><PERSON>le\Entity\TransactionStatus;
use <PERSON><PERSON><PERSON><PERSON>le\Entity\TransactionType;
use Core<PERSON><PERSON>le\Entity\CardProgram;
use Core<PERSON>undle\Entity\UserCardLoad;
use CoreBundle\Exception\FailedException;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Util;
use SalexUserBundle\Entity\User;
use SpendrBundle\Entity\Product;

class BrazeService
{
    const API_USER_ALIAS_CREATE = 'users/alias/new';
    const API_USER_TRACK = 'users/track';
    const API_USER_IDENTIFY = 'users/identify';
    const API_USER_DELETE = 'users/delete';
    const API_USER_EXPORT = 'users/export/ids';
    const API_CREATE_SEND_ID = 'sends/id/create';
    const API_MESSAGE_SEND = 'messages/send';

    const TYPE_SUBSCRIBED = 'subscribed';
    const TYPE_UNSUBSCRIBED = 'unsubscribed';
    const TYPE_OPTEDIN = 'opted_in';
    const TYPE_ALL = 'all';

    public static function getApiRoot()
    {
        return Util::getParameter('braze_rest_endpoint');
    }

    public static function getApiKey()
    {
        return Util::getParameter('braze_rest_api_key');
    }

    public static function getAppId($platform)
    {
        if ($platform == 'ios') {
            return Util::getParameter('braze_ios_api_key', true);
        } elseif ($platform == 'android') {
            return Util::getParameter('braze_android_api_key', true);
        }
        return null;
    }

    public static function getExternalPrefix()
    {
        if (Util::isLive()) {
            return '';
        } elseif (Util::isStaging()) {
            return 'spendr_staging_';
        } else {
            return 'spendr_local_';
        }
    }

    public static function createUserAlias(User $user)
    {
        $url = self::getApiRoot() . self::API_USER_ALIAS_CREATE;
        $curl = curl_init();
        $parameters = [
            'user_aliases' => [[
                'external_id' => self::getExternalPrefix() . $user->getId(),
                'alias_name' => $user->getEmail(),
                'alias_label' => 'Consumer'
            ]]
        ];
        curl_setopt_array($curl, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($parameters),
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json',
                "Authorization: Bearer " . self::getApiKey(),
            ),
        ));
        $json = curl_exec($curl);
        $res = json_decode($json, true);
        curl_close($curl);
        if ($res['message'] == 'success') {
            return true;
        }
        return false;
    }

    /**
     * Get braze user attributes
     * @param User $user
     * @return array
     */
    public static function getUserAttributes(User $user, $export = false)
    {
        $attributes = [];
        try {
            // See https://www.braze.com/docs/api/objects_filters/user_attributes_object for key-values
            $banks = [];
            foreach (ConsumerService::getUserBankList($user) as $item) {
                array_push($banks, $item['bankName']);
            }
            $uc = UserService::getDummyCard($user);
            $promotionAmount = 0;
            if ($uc) {
                $promotionAmount = Util::em()->getRepository(UserCardLoad::class)
                    ->createQueryBuilder('ucl')
                    ->join('ucl.userCard', 'c')
                    ->join('c.card', 'cpct')
                    ->where(Util::expr()->eq('cpct.cardProgram',':cardProgram'))
                    ->setParameter('cardProgram', CardProgram::spendr())
                    ->andWhere('ucl.userCard = :uc')
                    ->andWhere('ucl.type = :type')
                    ->andWhere('ucl.status = :status')
                    ->andWhere('ucl.loadStatus = :loadStatus')
                    ->andWhere('ucl.loadType = :loadType')
                    ->setParameter('uc', $uc)
                    ->setParameter('type', UserCardLoad::TYPE_LOAD_CARD)
                    ->setParameter('status', UserCardLoad::STATUS_COMPLETED)
                    ->setParameter('loadStatus', UserCardLoad::LOAD_STATUS_LOADED)
                    ->setParameter('loadType', LoadService::LOAD_TYPE_PROMOTION)
                    ->select('sum(ucl.initialAmount)')
                    ->getQuery()
                    ->getSingleScalarResult();
            }
            $purchases = Util::em()->getRepository(Transaction::class)
                ->createQueryBuilder('t')
                ->join('t.consumer', 'u')
                ->join('t.location', 'l')
                ->where('u.id = :id')
                ->andWhere('t.status = :status')
                ->andWhere('t.type = :type')
                ->setParameter('id', $user->getId())
                ->setParameter('status', TransactionStatus::get(TransactionStatus::NAME_COMPLETED))
                ->setParameter('type', TransactionType::get(TransactionType::NAME_PURCHASE))
                ->orderBy('t.dateTime', 'desc')
                ->getQuery()
                ->getResult();
            $spendCount = count($purchases);
            $spendAmount = 0;
            $merchants = [];
            /** @var Transaction $item */
            foreach ($purchases as $item) {
                $spendAmount += $item->getAmount();
                $location = $item->getLocation();
                if ($location && !in_array($location->getName(), $merchants)) {
                    array_push($merchants, $location->getName());
                }
            }
            $spendAmount = Money::formatAmountToNumber($spendAmount);
            $products = Util::em()->getRepository(Product::class)
                ->createQueryBuilder('p')
                ->where('p.createBy = :userId')
                ->setParameter('userId', $user->getId())
                ->select('count(distinct p)')
                ->getQuery()
                ->getSingleScalarResult();
            $attributes = [
                'external_id' => self::getExternalPrefix() . $user->getId(),
                'first_name' => $user->getFirstName(),
                'last_name' => $user->getLastName(),
                'email' => $user->getEmail(),
//                'dob' => Carbon::instance($user->getBirthday())->toDateString(),
//                'gender' => $user->getGender(),
                'home_city' => $user->getCity(),
//                'country' => $user->getCountry()->getIsoCode(),
                'phone' => $user->getMobilephone(),
                'onboard_status' => ConsumerService::text($user->getRegisterStep()),
                'bank_linked' => count($banks),
                'created_at' => Carbon::instance($user->getCreatedAt())->format('Y-m-d\TH:i:s\Z'),
                'state' => $user->getStateName(),
                'count_of_returns' => FeeService::getConsumerAchReturnTimes($user),
                'total $ amount of manual promos, earn rewards, and promo codes received' => Money::formatAmountToNumber($promotionAmount),
                'account_balance' => $uc ? Money::formatAmountToNumber($user->getBalance()) : 0,
                'total_amount_spend' => $spendAmount,
                'avg_spend' => $spendCount ? ($spendAmount / $spendCount) : 0,
                'bank_name' => array_slice($banks, 0, 3),
                'zip_code' => $user->getZip(),
                'street_address' => $user->getAddress(),
                'id_doc_loaded' => $user->isViewIdButtonEnabled(),
                'count_of_products_loaded' => $products,
                'funding_status' => ConsumerService::getConsumerLoadStatus($user),
                'last_merchant_purchased_at' => $merchants ? $merchants[0] : '',
                'name_of_merchants_purchased_from' => array_slice($merchants, 0, 3)
            ];
            if ($user->getBirthday()) {
                $attributes['dob'] = Carbon::instance($user->getBirthday())->toDateString();
            }
            if ($user->getCountry()) {
                $attributes['country'] = $user->getCountry()->getIsoCode();
            }
            if ($export) {
                $attributes = array_merge($attributes, [
                    'email_subscribe' => self::TYPE_OPTEDIN,
                    'push_subscribe' => self::TYPE_OPTEDIN,
                ]);
                $attributes['bank_name'] = implode(' & ', $attributes['bank_name']);
                $attributes['name_of_merchants_purchased_from'] = implode(' & ', $attributes['name_of_merchants_purchased_from']);
            }
        } catch (\Exception $e) {
            Log::warn($e->getMessage(), [
                $e->getLine(),
                $e->getTrace()
            ]);
        }
        return $attributes;
    }

    /**
     * Use this endpoint to record custom events, purchases, and update user profile attributes.
     * @param User|null $user
     * @param array|null $event
     * @param array|null $purchase
     * @param array $attributes
     * @param bool $isList
     * @return bool
     */
    public static function userTrack($user = null, $event = null, $purchase = null, $attributes = [], $isList = false)
    {
        if (Util::isLocal()) {
            return true;
        }

        if (!$user && !$event && !$purchase && !$attributes) return false;
        $url = self::getApiRoot() . self::API_USER_TRACK;
        $curl = curl_init();
        if ($user) {
            $attributes = self::getUserAttributes($user);
        }
        if (!$isList && $attributes) {
            $attributes = [$attributes];
        }
        $parameters = [
            // See https://www.braze.com/docs/api/objects_filters/user_attributes_object for key-value
            'attributes' => $attributes ? $attributes : [],
            // See https://www.braze.com/docs/api/objects_filters/event_object/ for key-values
            'events' => $event ? [ $event ] : [],
            // See https://www.braze.com/docs/api/objects_filters/purchase_object/ for key-values
            'purchases' => $purchase ? [ $purchase ] : []
        ];
        curl_setopt_array($curl, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($parameters),
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json',
                "Authorization: Bearer " . self::getApiKey(),
            ),
        ));
        $json = curl_exec($curl);
        $res = json_decode($json, true);
        curl_close($curl);
        if ($res['message'] == 'success') {
            if (isset($res['errors'])) {
                SlackService::eyes('User Track minor error', $res);
            }
            return true;
        }
        return false;
    }

    public static function identifyUser(User $user)
    {
        $url = self::getApiRoot() . self::API_USER_IDENTIFY;
        $curl = curl_init();
        $parameters = [
            'aliases_to_identify' => [[
                'external_id' => self::getExternalPrefix() . $user->getId(),
                'user_alias' => [
                    'alias_name' => $user->getEmail(),
                    'alias_label' => 'Consumer'
                ]
            ]]
        ];
        curl_setopt_array($curl, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($parameters),
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json',
                "Authorization: Bearer " . self::getApiKey(),
            ),
        ));
        $json = curl_exec($curl);
        $res = json_decode($json, true);
        curl_close($curl);
        if ($res['message'] == 'success') {
            if (isset($res['errors'])) {
                SlackService::eyes('Identify User minor error', $res);
            }
            return true;
        } else {
            throw new FailedException($res['message'], [
                'Member Id' => $user->getId(),
                'Resp' => $res
            ]);
        }
    }

    /**
     * This endpoint allows you to delete any user profile by specifying a known user identifier.
     * Up to 50 external_ids, user_aliases, or braze_ids can be included in a single request.
     * Only one of external_ids, user_aliases, or braze_ids can be included in a single request.
     * @param User $user
     * @return bool
     * @throws FailedException
     */
    public static function userDelete(User $user)
    {
        $url = self::getApiRoot() . self::API_USER_DELETE;
        $curl = curl_init();
        $parameters = [
            'external_ids' => [self::getExternalPrefix() . $user->getId()],
//            'user_aliases' => [
//                [
//                    'alias_name' => $user->getEmail(),
//                    'alias_label' => 'Consumer'
//                ]
//            ]
//            'braze_ids' => []
        ];
        curl_setopt_array($curl, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($parameters),
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json',
                "Authorization: Bearer " . self::getApiKey(),
            ),
        ));
        $json = curl_exec($curl);
        $res = json_decode($json, true);
        curl_close($curl);
        if ($res['message'] == 'success') {
            SlackService::info('Delete Braze User: ' . $user->getId(), $res, [
                SlackService::MENTION_JERRY
            ]);
            return true;
        } else {
            throw new FailedException($res['message'], [
                'Member Id' => $user->getId(),
                'Resp' => $res
            ]);
        }
    }

    /**
     * Export user attributes, events, purchase etc. as json
     * https://www.braze.com/docs/api/endpoints/export/user_data/post_users_identifier/
     * @param User $user
     * @param array|null $fields
     * @return false|mixed
     */
    public static function exportUser(User $user, $fields = null)
    {
        $url = self::getApiRoot() . self::API_USER_EXPORT;
        $curl = curl_init();
        $parameters = [
            'external_ids' => [self::getExternalPrefix() . $user->getId()]
        ];
        if ($fields) {
            $parameters['fields_to_export'] = $fields;
        }
        curl_setopt_array($curl, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($parameters),
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json',
                "Authorization: Bearer " . self::getApiKey(),
            ),
        ));
        $json = curl_exec($curl);
        $res = json_decode($json, true);
        curl_close($curl);
        if ($res['message'] == 'success') {
            return $res;
        }
        return false;
    }

    public static function createSendId()
    {
        $url = self::getApiRoot() . self::API_CREATE_SEND_ID;
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode([
                'campaign_id' => ''
            ]),
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json',
                "Authorization: Bearer " . self::getApiKey(),
            ),
        ));
        $json = curl_exec($curl);
        $res = json_decode($json, true);
        curl_close($curl);
        if ($res['message'] == 'success') {
            return $res['send_id'];
        }
        return false;
    }

    public static function sendMessages($uids, $push = null, $email = null, $subscribe = self::TYPE_ALL)
    {
        $url = self::getApiRoot() . self::API_MESSAGE_SEND;
        $curl = curl_init();
        $parameters = [
            'external_user_ids' => [],
            'recipient_subscription_state' => $subscribe,
            'messages' => []
        ];
        foreach ($uids as $uid) {
            $parameters['external_user_ids'][] = self::getExternalPrefix() . $uid;
        }
        if ($push) {
            $parameters['messages'] = array_merge($parameters['messages'], [
                'apple_push' => [
                    'alert' => [
                        'title' => $push['title'] ?? 'Push title',
                        'body' => $push['message'] ?? 'Push message',
                    ],
                    'content-available' => true,
                    'custom_uri' => $push['url'] ?? null,
                    'send_to_most_recent_device_only' => true,
                ],
                'android_push' => [
                    'title' => $push['title'] ?? 'Push title',
                    'alert' => $push['message'] ?? 'Push message',
                    'priority' => 1,
                    'custom_uri' => $push['url'] ?? null,
                    'send_to_most_recent_device_only' => true,
                ]
            ]);
        }
        if ($email) {
            $parameters['messages']['email'] = [
                'app_id' => self::getAppId('ios'),
                'from' => 'Spendr Support <<EMAIL>>',
                'email_template_id' => null,
                'subject' => $email['subject'] ?? 'Email subject',
                'body' => $email['body'] ?? 'Email body'
            ];
        }
        curl_setopt_array($curl, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($parameters),
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json',
                "Authorization: Bearer " . self::getApiKey(),
            ),
        ));
        $json = curl_exec($curl);
        $res = json_decode($json, true);
        curl_close($curl);
        if ($res['message'] == 'success') {
            return $res;
        }
        return false;
    }
}
