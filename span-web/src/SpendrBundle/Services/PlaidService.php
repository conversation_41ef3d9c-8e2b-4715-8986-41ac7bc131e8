<?php

namespace SpendrBundle\Services;

use Carbon\Carbon;
use CoreB<PERSON>le\Entity\ExternalInvoke;
use CoreB<PERSON>le\Entity\UserCard;
use CoreBundle\Exception\FailedException;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Services\SSLEncryptionService;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Util;
use Firebase\JWT\JWK;
use Firebase\JWT\JWT;
use PortalBundle\Exception\PortalException;
use Ramsey\Uuid\Uuid;
use SpendrBundle\SpendrBundle;
use TomorrowIdeas\Plaid\Entities\User;
use TomorrowIdeas\Plaid\Plaid;
use TomorrowIdeas\Plaid\PlaidRequestException;

class PlaidService
{
    // Event Type
    const EVENT_TYPE_PENDING = 'pending';
    const EVENT_TYPE_POSTED = 'posted';
    const EVENT_TYPE_REVERSED = 'reversed';

    // Webhook Type
    const WEBHOOOK_TYPE_AUTH = 'AUTH';
    const WEBHOOK_TYPE_ITEM = 'ITEM';

    // Webhook Code or verification_status
    const VERIFY_STATUS_PENDING = 'pending_automatic_verification';
    const VERIFY_STATUS_MANUAL_PENDING = 'pending_manual_verification';
    const VERIFY_STATUS_VERIFIED = 'automatically_verified';
    const VERIFY_STATUS_MANUAL_VERIFIED = 'manually_verified';
    const VERIFY_STATUS_FAILED = 'verification_failed';
    const VERIFY_STATUS_EXPIRED = 'verification_expired';
    // Webhook Code for Item
    const CODE_ERROR = 'ERROR';
    const CODE_NEW_ACCOUNT = 'NEW_ACCOUNTS_AVAILABLE';
    const CODE_PENDING_DISCONNECT = 'PENDING_DISCONNECT';
    const CODE_PENDING_EXPIRATION = 'PENDING_EXPIRATION';
    const USER_PERMISSION_REVOKED = 'USER_PERMISSION_REVOKED';
    // Plaid Error Code
    const ERROR_ITEM_LOGIN_REQUIRED = 'ITEM_LOGIN_REQUIRED';
    const ERROR_ITEM_NOT_FOUND = 'ITEM_NOT_FOUND';
    const ERROR_NO_AUTH_ACCOUNTS = 'NO_AUTH_ACCOUNTS';

    const CACHE_KEY = 'spendr_plaid_env';
    const ENV_PRODUCTION = 'production';
    const ENV_SANDBOX = 'sandbox';
    const ENV_DEVELOPMENT = 'development';

    // Plaid institutions is tokenized account numbers
    const TANS_INSTITUTIONS = [
        'ins_13', // PNC
        'ins_56'  // Chase
    ];

    /**
     * Get plaid env
     * @param null $env
     * @return string
     */
    public static function getEnv($env = null)
    {
        if ($env) {
            return $env;
        }
        if (Util::isLive()) {
            return self::ENV_PRODUCTION;
        } else if (Util::isLocal()) {
            return self::ENV_SANDBOX;
        } else {
            if (Data::has(self::CACHE_KEY)) {
                return Data::get(self::CACHE_KEY);
            }
            return self::ENV_SANDBOX;
        }
    }

    public static function getUrlAndSecret($path, $env = null)
    {
        $plaid_env = self::getEnv($env);
        if ($plaid_env == self::ENV_PRODUCTION) {
            $url = "https://production.plaid.com/" . $path;
            $secret = Util::getKmsParameter('plaid_spendr_prod_secret');
        } elseif ($plaid_env == self::ENV_SANDBOX) {
            $url = "https://sandbox.plaid.com/" . $path;
            $secret = Util::getKmsParameter('plaid_spendr_sandbox_secret');
        } else {
            $url = "https://development.plaid.com/" . $path;
            $secret = Util::getKmsParameter('plaid_spendr_dev_secret');
        }
        return [$url, $secret];
    }

    public static function request($url, $parameters)
    {
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => Util::j2s($parameters),
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json'
            ),
        ));
        $json = curl_exec($curl);
        curl_close($curl);
        ExternalInvoke::create(
            'spendr_plaid_url_' . $url,
            array_except($parameters, ['client_id', 'secret', 'access_token']),
            $json,
            true
        );
        return $json;
    }

    /**
     * Get plaid products
     * @param bool $force
     * @param bool $manual
     * @param null $env
     * @return string[]
     */
    public static function getProduct($force = false, $manual = false, $env = null)
    {
        if ($force) return [];
        if (self::getEnv($env) === self::ENV_PRODUCTION && !$manual) return ['auth', 'balance_plus'];
        return ['auth'];
    }

    public static function getWebhook()
    {
        return SpendrBundle::getAdminDomain() . '/webhook/plaid/merchant-auth/';
    }

    public static function getPartnerWebhook()
    {
        return SpendrBundle::getAdminDomain() . '/webhook/plaid/manual-link-auth';
    }

    public static function getConsumerWebhook()
    {
        if (Util::isLive()) {
            return 'https://app.spendr.com/webhook/plaid/consumer-auth/';
        }
        return 'https://stage.spendr.com/webhook/plaid/consumer-auth/';
    }

    /**
     * Get ios universal link url
     * @return string
     */
    public static function getRedirectUri()
    {
        if (Util::isLive()) {
            return "https://app.spendr.com/";
        }
        return "https://stage.spendr.com/";
    }

    /**
     * Get android package name
     * @return string
     */
    public static function getPackageName()
    {
        return "com.ternitup.spendrapp";
    }

    /**
     * Init plaid sdk
     * @param null $env
     * @return Plaid
     */
    public static function initPlaid($env = null)
    {
        $plaid_env = self::getEnv($env);
        if ($plaid_env == 'production') {
            $secret = Util::getKmsParameter('plaid_spendr_prod_secret');
        } else if ($plaid_env == 'sandbox') {
            $secret = Util::getKmsParameter('plaid_spendr_sandbox_secret');
        } else {
            $secret = Util::getKmsParameter('plaid_spendr_dev_secret');
        }
        return new Plaid(
            Util::getKmsParameter('plaid_spendr_client_id'),
            $secret,
            $plaid_env
        );
    }

	public static function curlLinkToken($bankAccountId, $platform = null, $accessToken = null, $webhook = '', $sameDay = true, $env = null, $redirectUri = null, $pkgName = null)
    {
        [$url, $secret] = self::getUrlAndSecret('link/token/create', $env);
        $parameters = [
            "client_id" => Util::getKmsParameter('plaid_spendr_client_id'),
            "secret" => $secret,
            "client_name" => "Spendr",
            "language" => "en",
            "country_codes" => ["US"],
            "user" => [
                "client_user_id" => $bankAccountId
            ],
            "products" => self::getProduct($accessToken != null, $sameDay, $env),
            "access_token" => $accessToken,
            "redirect_uri" => $platform == 'iOS' ? self::getRedirectUri() : $redirectUri,
            "android_package_name" => $platform == 'android' ? self::getPackageName() : $pkgName,
            "auth" => [
                "auth_type_select_enabled" => false,
                "automated_microdeposits_enabled" => !empty($webhook),
                "instant_match_enabled" => true,
                "instant_microdeposits_enabled" => $sameDay,
                "same_day_microdeposits_enabled" => $sameDay,
            ]
        ];
//        if ($accessToken != null && $plaid_env === self::ENV_PRODUCTION) {
//            $parameters['additional_consented_products'] = ['balance_plus'];
//        }
        if ($webhook) {
            $parameters['webhook'] = $webhook;
        }
        $json = self::request($url, $parameters);
        $res = json_decode($json, true);
        if (!isset($res['link_token'])) {
            $msg = isset($res['display_message']) ? $res['display_message'] : $res['error_message'];
            SlackService::warning(
                'Plaid create link token error: ' . $msg,
                [
                    'request_id' => $res['request_id'],
                ]
            );
        }
        return (object)$res;
    }

    /**
     * @param $jwt
     * @return FailedException|bool
     * @throws FailedException
     */
    public static function verifyWebhookKey($jwt)
    {
        $tks = explode('.', $jwt);
        if (count($tks) !== 3) {
            throw new FailedException('The source does not match!');
        }
        $headerRaw = JWT::urlsafeB64Decode($tks[0]);
        if (!$headerRaw) {
            throw new FailedException('The source does not match!');
        }
        $header = JWT::jsonDecode($headerRaw);
        if (!$header || $header->alg != 'ES256') {
            throw new FailedException('The source does not match!');
        }
        try {
            $plaid = self::initPlaid();
            $res = $plaid->webhooks->getVerificationKey($header->kid);
            $key = JWK::parseKey((array)$res->key);
            $payload = JWT::decode($jwt, $key);

            if (isset($payload->iat) && time() - $payload->iat <= 300) {
                return true;
            }
        } catch (PlaidRequestException $e) {
            Log::error('Plaid get verification key error: ' . $e->getMessage());
        }

        throw new FailedException('Expired Token.');
    }

    /**
     * Exchange plaid access token
     * @param $publicToken
     * @param null $env
     * @return object|null
     */
    public static function exchangeAccessToken($publicToken, $env = null)
    {
        $plaid = self::initPlaid($env);
        $token = null;
        try {
            $token = $plaid->items->exchangeToken($publicToken);
        } catch (PlaidRequestException $e) {
            Log::warn('Plaid exchange access token error for ' . substr($publicToken, -4) .
                      ' : ' . $e->getMessage());
            ExternalInvoke::create(
                'spendr_plaid_url_item/public_token/exchange',
                null,
                [$e->getCode(), $e->getMessage(), $e->getResponse()],
                true
            );
        }
        return $token;
    }

    /**
     * Get plaid account status by accessToken or publicToken
     * @param string $accessToken
     * @param string|null $publicToken
     * @return object|null
     */
    public static function getAccountStatus(string $accessToken, string $publicToken = null)
    {
        if (!$accessToken && !$publicToken) {
            return null;
        }
        $plaid = self::initPlaid();
        $response = null;
        try {
            if (!$accessToken) {
                $token = $plaid->items->exchangeToken($publicToken);
                $accessToken = $token->access_token;
            }
            $response = $plaid->accounts->list($accessToken);
        } catch (PlaidRequestException $e) {
            Log::warn(
                'Plaid get account status error: ' . $e->getMessage(),
                [ 'access token' => $accessToken, 'data' => $e->getResponse() ]
            );
            SlackService::warning(
                'Plaid get account status error: ' . $e->getMessage(),
                [ 'resp' => $e->getResponse() ],
            );
            ExternalInvoke::create(
                'spendr_plaid_url_accounts/get',
                null,
                [$e->getCode(), $e->getMessage(), $e->getResponse()],
                true
            );
        }
        return $response;
    }

    /**
     * Get plaid account info by accessToken or publicToken
     * @param string $accessToken
     * @param string|null $publicToken
     * @return object|null
     */
	public static function getAccountInformation(string $accessToken, string $publicToken = null)
	{
        if (!$accessToken && !$publicToken) {
            return null;
        }
        $plaid = self::initPlaid();
		$response = null;
		try {
            if (!$accessToken) {
                $token = $plaid->items->exchangeToken($publicToken);
                $accessToken = $token->access_token;
            }
			$response = $plaid->auth->get($accessToken);
		} catch (PlaidRequestException $e) {
            SlackService::warning(
                'Plaid get account info error: ' . $e->getMessage(),
                [ 'resp' => $e->getResponse() ],
            );
            ExternalInvoke::create(
                'spendr_plaid_url_auth/get',
                null,
                [$e->getCode(), $e->getMessage(), $e->getResponse()],
                true
            );
		}
		return $response;
	}

    /**
     * Get plaid account identity info by accessToken or publicToken
     * @param UserCard $uc
     * @param string|null $publicToken
     * @param null $env
     * @return object|null
     */
	public static function getIdentityInformation(UserCard $uc, string $publicToken = null, $env = null)
    {
        $accessToken = $uc->getPlaidAccessToken();
        if (!$accessToken && !$publicToken) {
            return null;
        }
        $plaid = self::initPlaid($env);
        $response = null;
        try {
            if (!$accessToken) {
                $token = $plaid->items->exchangeToken($publicToken);
                $accessToken = $token->access_token;
            }
            $response = $plaid->accounts->getIdentity($accessToken);
        } catch (PlaidRequestException $e) {
            if (Util::isCommand()) return null;
            SlackService::warning(
                'Plaid get identity info error: ' . $e->getMessage(),
                [ 'resp' => $e->getResponse() ],
            );
            ExternalInvoke::create(
                'spendr_plaid_url_identity/get',
                null,
                [$e->getCode(), $e->getMessage(), $e->getResponse()],
                true
            );
        }
        return $response;
    }

    /**
     * Get plaid balance account data
     * @param string|null $accessToken
     * @param string|null $accountId
     * @param bool $needOptions
     * @return mixed|null
     */
    public static function getBalanceInfo($accessToken, $accountId, $needOptions = false, $env = null)
    {
        if (!$accessToken || !$accountId) {
            return null;
        }
        $plaid = self::initPlaid($env);
        $balance = null;
        try {
            $info = $plaid->accounts->getBalance($accessToken, $needOptions ? [
                'min_last_updated_datetime' => Carbon::parse('-1 months')->format('Y-m-d\TH:i:s\Z')
            ] : []);
            if (isset($info->error) && $info->error) {
                $balance = $info->error;
            } elseif (isset($info->accounts) && count($info->accounts)) {
                $balance = array_first($info->accounts, function ($item) use($accountId) {
                    return $item->account_id == $accountId;
                });
            }
        } catch (PlaidRequestException $e) {
            $balance = $e->getResponse();
            Log::warn('Plaid get balance error: ' . $balance->error_message);
            ExternalInvoke::create(
                'spendr_plaid_url_identity/get',
                null,
                [$e->getCode(), $e->getMessage(), $e->getResponse()],
                true
            );
        }
        return $balance;
    }

    public static function getBalancePlusInfo($accessToken, $accountId, $amount, $user = null, $clientTxnId = null, $env = null)
    {
        if (!$accessToken || !$accountId) {
            return null;
        }
        $plaid_env = self::getEnv($env);
        [$url, $secret] = self::getUrlAndSecret('accounts/balance/get', $env);

        $parameters = [
            "client_id" => Util::getKmsParameter('plaid_spendr_client_id'),
            "secret" => $secret,
            "access_token" => $accessToken,
            "options" => [
                'min_last_updated_datetime' => Carbon::parse('-1 months')->format('Y-m-d\TH:i:s\Z')
            ]
        ];
        if ($plaid_env === self::ENV_PRODUCTION && $clientTxnId) {
            $parameters['payment_details'] = [
                "account_id" => $accountId,
                "client_transaction_id" => $clientTxnId,
                "amount" => $amount,
                "requires_real_time_balance_refresh" => true
            ];
        }
        $json = self::request($url, $parameters);
        $res = json_decode($json);
        if (!Util::isLive()) {
            Log::debug('Plaid balance txnId ' . $clientTxnId);
            Log::debug('Plaid balance res', (array)$res);
        }
        if (isset($res->error_code)) {
            Log::warn('Plaid get balance error: ' . $res->error_message, (array)$res);
        } elseif (isset($res->risk_reasons) && count($res->risk_reasons)) {
            $res = array_first($res->risk_reasons, function ($r) {
                return $r->reason_code == 'BK07';
            });
            if ($res && $user) {
                SlackService::warning($user->getEmail() . 'check balance failed',
                    [
                        'Load Amount' => $amount,
                        'Error Info' => $res
                    ],
                    SlackService::GROUP_SPENDR_NEG_BALANCE
                );
            }
        } elseif (isset($res->accounts) && count($res->accounts)) {
            $res = array_first($res->accounts, function ($item) use($accountId) {
                return $item->account_id == $accountId;
            });
        }
        return $res;
    }

    public static function sendDecisionReport($clientTxnId, $initiated, $env = null)
    {
        [$url, $secret] = self::getUrlAndSecret('signal/decision/report', $env);

        $parameters = [
            "client_id" => Util::getKmsParameter('plaid_spendr_client_id'),
            "secret" => $secret,
            "client_transaction_id" => $clientTxnId,
            "initiated" => $initiated
        ];

        $json = self::request($url, $parameters);
        $res = json_decode($json);
        if (!Util::isLive()) {
            Log::debug('Plaid return report', [$clientTxnId, $initiated]);
            Log::debug('Plaid return report res', (array)$res);
        }
        return $res;
    }

    public static function sendReturnReport($clientTxnId, $returnCode, $env = null)
    {
        [$url, $secret] = self::getUrlAndSecret('signal/return/report', $env);

        $parameters = [
            "client_id" => Util::getKmsParameter('plaid_spendr_client_id'),
            "secret" => $secret,
            "client_transaction_id" => $clientTxnId,
            "return_code" => $returnCode
        ];

        $json = self::request($url, $parameters);
        $res = json_decode($json);
        if (!Util::isLive()) {
            Log::debug('Plaid return report', [$clientTxnId, $returnCode]);
            Log::debug('Plaid return report res', (array)$res);
        }
        return $res;
    }

    public static function getAccountInfoArray(UserCard $uc, $env = null): array
    {
        $token = $uc->getPlaidAccessToken();
        if (!$token) {
            throw PortalException::temp('Unknown access token!');
        }

        try {
            $plaid = self::initPlaid($env);
            $resp = $plaid->auth->get($token);
        } catch (PlaidRequestException $e) {
            $error = 'Failed to get account info: ' . $e->getMessage();
            if (!$e->getMessage() &&
                $e->getResponse() &&
                isset($e->getResponse()->error_message) &&
                $e->getResponse()->error_message
            ) {
                if ($e->getResponse()->error_message === self::ERROR_NO_AUTH_ACCOUNTS) {
                    $error = 'No valid checking or savings accounts. Please try linking another institution.';
                } else {
                    $error = $e->getResponse()->error_message;
                }
            }
            throw PortalException::temp($error, (array)$e->getResponse());
        }

        if (!$resp) {
            throw PortalException::temp('Empty account info!');
        }

        $info = Util::o2j($resp);
        if (!isset($info['item']['institution_id'])) {
            $info['item']['institution_id'] = null;
        }
        return $info;
    }

    public static function syncAccountInfo(UserCard $uc, string $accountId = null, bool $returnSensitive = false, $env = null)
    {
        $accessToken = $uc->getPlaidAccessToken();
        if (!$accessToken) {
            return null;
        }
        $info = self::getAccountInfoArray($uc, $env);
        $account = null;
        foreach (($info['accounts'] ?? []) as $item) {
            $hasAch = array_first($info['numbers']['ach'], function ($ach) use ($item) {
                return $ach['account_id'] == $item['account_id'];
            });
            if ($hasAch && (!$accountId || ($item['account_id'] ?? '') === $accountId)) {
                $account = $item;
                break;
            }
        }
        if ( ! $account) {
            return new FailedResponse('Invalid account info while syncing with Plaid.');
        }
        if (!isset($info['item']['institution_id'])) {
            $info['item']['institution_id'] = null;
        }
        $meta = Util::meta($uc);
        $new = [
            'account_id' => $account['account_id'],
            'item_id' => $info['item']['item_id'] ?? null,
            'institution_id' => $info['item']['institution_id'] ?? null,
            'institution_name' => $info['item']['institution_name'] ?? null,
            'name' => $account['name'] ?? null,
            'mask' => $account['mask'] ?? null,
            'subtype' => $account['subtype'] ?? null,
            'type' => $account['type'] ?? null,
            'verification_status' => $account['verification_status'] ?? $meta['verification_status'] ?? null,
        ];
        Util::updateMeta($uc, $new, false);
        Log::info('Update user card meta', [
            'card' => $uc->getHash(),
            'accountId' => $accountId,
            'preMeta' => $meta,
            'newMeta' => $new
        ]);

        $new['sensitive'] = null;
        $numbers = $info['numbers']['ach'] ?? [];
        foreach ($numbers as $number) {
            $nid = $number['account_id'] ?? null;
            if ($nid && $nid === $new['account_id']) {
                $new['sensitive'] = $number;
                break;
            }
        }

        $clientId = Util::getKmsParameter('plaid_spendr_client_id');
        $aesKey = Util::getDecryptedParameter('aes_key');
        $appends = [];
        foreach ($numbers as $number) {
            $nid = $number['account_id'] ?? null;
            if ($nid) {
                if (!empty($number['account']) && !empty($number['routing'])) {
                    $hash = Util::hash(implode('_', [
                        $number['routing'],
                        $number['account'],
                        $clientId,
                        $aesKey,
                    ]));
                } else {
                    $hash = Uuid::uuid4();
                }
                $appends[$nid] = [
                    'mask' => Util::maskPan($number['account'] ?? '', 4, true),
                    'non_deposited_routing' => UserCardService::isNonDepositedRouting($number['routing'] ?? ''),
                    'routing_mask' => Util::maskPan($number['routing'] ?? '', 4, true),
                    'account_hash' => $hash,
                    'is_tokenized_account_number' => $number['is_tokenized_account_number'] ?? false
                ];
            }
        }

        foreach ($info['accounts'] as $i => $_) {
            unset($info['accounts'][$i]['persistent_account_id']);

            $nid = $_['account_id'] ?? null;
            if (isset($_['account_id'], $appends[$nid])) {
                $info['accounts'][$i] = array_merge($info['accounts'][$i], $appends[$nid]);
            }
        }
        if (isset($appends[$new['account_id']])) {
            $new = array_merge($new, $appends[$new['account_id']]);
        }

        if (!empty($new['sensitive']['routing'])) {
            $uc->setRoutingNumber(SSLEncryptionService::encrypt($new['sensitive']['routing']));
        }
        if (!empty($new['sensitive']['account'])) {
            $uc->setAccountNumber(SSLEncryptionService::encrypt($new['sensitive']['account']));
        }
        $uc->persist();

        if (!$returnSensitive) {
            foreach ($info['accounts'] as $i => $_) {
                unset($info['accounts'][$i]['persistent_account_id']);
            }
            unset($new['sensitive'], $info['numbers']);
        }

        $info['extracted'] = $new;

        return $info;
    }

    public static function getInstitution(UserCard $uc, $env = null)
    {
        $institutionId = Util::meta($uc, 'institution_id');
        $institution = null;
        try {
            $resp = null;
            $plaid = self::initPlaid($env);
            if ($institutionId) {
                $resp = $plaid->institutions->get($institutionId, ['US']);
                if (isset($resp->institution)) {
                    $institution = $resp->institution;
                }
            } else {
                $routing = SSLEncryptionService::tryToDecrypt($uc->getRoutingNumber());
                if ($routing) {
                    $resp = $plaid->institutions->list(1, 0, ['US'], [
                        'routing_numbers' => [$routing]
                    ]);
                    if (isset($resp->institutions) && count($resp->institutions)) {
                        $institution = $resp->institutions[0];
                    }
                }
            }
            if (Util::isStaging()) {
                Log::info('Plaid institution info', [
                    'res' => $resp
                ]);
            }
        } catch (PlaidRequestException $e) {
            throw PortalException::temp($e->getMessage(), (array)$e->getResponse());
        }

        return $institution ? Util::o2j($institution) : null;
    }

    public static function sandboxSetVerificationStatus(UserCard $uc, string $status, string $accountId = null)
    {
        $accessToken = $uc->getPlaidAccessToken();
        $accountId = $accountId ?? Util::meta($uc, 'account_id');
        if (!$accessToken || !$accountId) {
            throw PortalException::temp('Invalid access token or account id!');
        }
        $plaid = self::initPlaid();
        $resp = $plaid->sandbox->setVerificationStatus(
            $accessToken,
            $accountId,
            $status,
        );
        return Util::o2j($resp);
    }
}
