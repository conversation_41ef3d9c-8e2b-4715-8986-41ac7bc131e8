<?php

namespace SpendrBundle\Services;


use Carbon\Carbon;
use ClfB<PERSON>le\Controller\BaseControllerTrait;
use Clf<PERSON><PERSON>le\Entity\Account;
use Clf<PERSON><PERSON>le\Entity\Transaction;
use Clf<PERSON><PERSON>le\Entity\TransactionStatus;
use C<PERSON><PERSON><PERSON>le\Entity\TransactionType;
use CoreB<PERSON>le\Entity\BaseState;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserCardBalance;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Entity\UserCardTransaction;
use CoreBundle\Exception\FailedException;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Service;
use CoreBundle\Utils\Util;
use DateTime;
use SalexUserBundle\Entity\User;
use SpendrB<PERSON>le\Controller\Admin\MembersControllerTrait;
use SpendrBundle\Entity\Location;
use SpendrBundle\Entity\RewardTarget;
use SpendrBundle\Entity\SpendrMerchant;
use SpendrBundle\Entity\SpendrPayToken;
use SpendrBundle\Entity\SpendrReferConfig;
use SpendrBundle\Entity\SpendrReferral;
use SpendrBundle\Entity\SpendrReward;
use SpendrBundle\Entity\SpendrTip;
use SpendrBundle\Entity\Terminal;
use SpendrBundle\Services\MQTT\PublishService;
use SpendrBundle\Services\Pay\PayService;
use SpendrBundle\SpendrBundle;
use Symfony\Component\HttpFoundation\Request;

class TransactionService
{
	use BaseControllerTrait;
	use MembersControllerTrait;

	public const transactionExpiredTime = 10; // min
	public const fromQrcode = 'qrcode';
	public const fromPush = 'push';

	public const PAYMENT_CACHE_KEY = 'SpendrTxnPayment_';

	public static function beforePay(
		User $user,
		$token,
		$timezone = null,
		$tipAmount = 0,
		$isRequest = false,
		$isRestrictAreaTxn = false
	) {
		$isCanTransaction = ConsumerService::isCanTransaction($user, 'app');
		if (is_string($isCanTransaction)) {
			return $isCanTransaction;
		}

		$t = self::checkPendingTransaction($user, $token);
		if (is_string($t)) {
			return $t;
		}

		if (!$t->getConsumer()) {
			return 'Missing data or abnormal data, please scan the QRCode again or ask the merchant to push it again.';
		}

		$amount = $t->getAmount();
		if ($amount <= 0) {
			return 'Invalid amount.';
		}

		$consumerDummy = UserService::getDummyCard($user);
		$balance = $consumerDummy ? $consumerDummy->getBalance() : 0;

		$dailySpendAmount = TransactionService::getConsumerSpendDailyAmount($user, $timezone);
		$remainingAmount = UserCard::CONSUMER_DAILY_MAX_SPEND_LIMIT - $dailySpendAmount;
		if ($amount > $remainingAmount) {
			$limitText = Money::format(
				UserCard::CONSUMER_DAILY_MAX_SPEND_LIMIT,
				'USD',
				false
			);
			$remainingAmountText = Money::format($remainingAmount, 'USD', false);
			return 'Your daily payment limit is ' . $limitText
				. ', and the current remaining amount of ' . $remainingAmountText . ' is not enough to pay!';
		}

		if ($isRestrictAreaTxn || $t->isFromPayApi()) {
			if ($balance < 0) {
				return 'Your Spendr balance is negative and cannot be paid!';
			}
		} else {
			if ($balance < $amount) {
				return 'Your Spendr balance is insufficient for the payment!';
			}
		}

		if ($tipAmount > 0 || $t->getTip()) {
		    return TipService::handleTipOfTransaction($t, $balance, $tipAmount, $isRequest, $isRestrictAreaTxn);
        }

		return $t;
	}

	public static function pay(Transaction $t, $appId)
	{
		$merchant = $t->getMerchant();
		$merchantAdmin = $merchant->getAdminUser();
		$merchantDummy = UserService::getDummyCard($merchantAdmin);

		$consumer = $t->getConsumer();
		$consumerDummy = UserService::getDummyCard($consumer);
		$balance = $consumerDummy->getBalance();

		$amount = $t->getAmount();

		$t->setConsumerBalanceBefore($balance)
			->setProducerBalanceBefore($merchantDummy->getBalance())
			->setUserCard($consumerDummy);

		/** @var TransactionStatus $completed */
		$completed = TransactionStatus::get(TransactionStatus::NAME_COMPLETED);
		$t->setStatus($completed);

		$txnTime = Carbon::now();
		$comment = sprintf(
			'In transaction %s(ID: %s) at %s. Amount: %s',
			$t->getToken(),
			$t->getId(),
			$txnTime,
			Money::format($amount, 'USD')
		);
        $tip = $t->getTip();
        if ($tip) {
            $comment .= sprintf(', Tip Amount: %s, Total Amount: %s',
                Money::format($tip->getAmount(), 'USD'),
                Money::format($amount + $tip->getAmount(), 'USD')
            );
        }

		// Change consumer balance
		$consumerDummy->updatePrivacyBalanceBy(-$amount, UserCardBalance::TYPE_TRANSACTION, $comment, false, $t);

		// Change merchant balance
		$merchantDummy->updatePrivacyBalanceBy($amount, UserCardBalance::TYPE_TRANSACTION, $comment, false, $t);

		// Change location balance
		$locationDummy = null;
		if (LocationService::beforeChangeLocationBalance(
			$t->getLocation(),
			UserCardBalance::TYPE_TRANSACTION,
			$t->getId()
		)) {
			$locationDummy = UserService::getDummyCard($t->getLocation()->getAdminUser());
			$locationDummy->updatePrivacyBalanceBy($amount, UserCardBalance::TYPE_TRANSACTION, $comment, false, $t);
		}

        if ($tip) {
            $tip->setStatus(SpendrTip::STATUS_PAID)
                ->setPaidAt($txnTime)
                ->persist();
            // Change consumer balance
            $consumerDummy->updatePrivacyBalanceBy(-$tip->getAmount(), UserCardBalance::TYPE_TIP, $comment, false, $tip);
            // Change merchant balance
            $merchantDummy->updatePrivacyBalanceBy($tip->getAmount(), UserCardBalance::TYPE_TIP, $comment, false, $tip);
			// Change location balance
			if ($locationDummy) {
				$locationDummy->updatePrivacyBalanceBy($tip->getAmount(), UserCardBalance::TYPE_TIP, $comment, false, $tip);
			}
		}

		$t->setTxnTime($txnTime);
		// Charge transaction fee
		FeeService::handleTransactionFee($t);

		$t->setConsumerBalanceAfter($consumerDummy->getBalance())
			->setProducerBalanceAfter($merchantDummy->getBalance());

		$em = Util::em();
		$em->persist($consumerDummy);
		$em->persist($merchantDummy);
		if ($locationDummy) {
			$em->persist($locationDummy);
		}
		$em->persist($t);
		$em->flush();

		$result = self::getPayResult($t);

		self::afterPay($t, $result, $appId, $merchantDummy->getBalance());
		return $result;
	}

	public static function afterPay(Transaction $t, $payResult, $appId, $merchantBalance)
	{
		$consumer = $t->getConsumer();
		$employee = $t->getLocationEmployee();

		// display payment results in slack
        $data = [
            'Transaction Token' => $t->getToken(),
            'Consumer ID' => $t->getConsumer()->getId(),
            'Amount' => Money::format($t->getAmount(), 'USD'),
            'Spendr Fee' => Money::format($t->getSpendrFee(), 'USD'),
            'Tern Fee' => Money::format($t->getTernFee(), 'USD'),
        ];
        if ($t->getTip()) {
            $data['Tip Amount'] = Money::format($t->getTip()->getAmount(), 'USD');
            $data['Tip Fee'] = Money::format($t->getTip()->getSpendrFee(), 'USD');
            $data['Spendr Fee'] = Money::format($t->getSpendrFee() + $t->getTip()->getSpendrFee(), 'USD');
        }
        SlackService::tada(
            $consumer->getSignature() . ' has paid for the transaction ' . $t->getToken() . ' ! ',
            $data
        );

		// send payment result to merchant and consumer
		try {
			PublishService::pushByUserToken(
				$consumer->getId(),
				PublishService::TOPIC_SEND_PAYMENT_RESULT_TO_MERCHANT,
				$employee,
				[
					'success' => true,
					'message' => '',
					'data' => $payResult,
				],
				$t->getToken()
			);
			PublishService::pushByUserToken(
				$consumer->getId(),
				PublishService::TOPIC_SEND_PAYMENT_RESULT_TO_CONSUMER,
				$consumer,
				[
					'success' => true,
					'message' => '',
					'data' => $payResult,
				],
				$t->getToken()
			);
		} catch (FailedException $e) {
			Log::error($e->getFullMessage(), [$e->data]);
		}

        // earn spend over reward
        $purchases = self::getConsumerTotalSpendAmount($t->getConsumer());
        $overs = SpendrReward::findListByType(UserCardLoad::EARN_TYPE_SPEND_OVER);
        if (count($overs)) {
            /** @var SpendrReward $over */
            foreach ($overs as $over) {
                if ($over && $over->getAmount() && $over->getSpendAmount()) {
                    $notEarnedBefore = LoadService::beforeEarnLoad($over, $consumer);
                    if (
                        $purchases >= $over->getSpendAmount() &&
                        $purchases - $t->getAmount() < $over->getSpendAmount() &&
                        $notEarnedBefore
                    ) {
                        LoadService::earnLoad($consumer, $over);
                    }
                }
            }
        }

        // referral reward
        $refer = SpendrReferConfig::findOne();
        if (
            $refer &&
            ($refer->getGiveReward() > 0 || $refer->getGetReward() > 0) &&
            $refer->getGiveSpent() > 0 &&
            $purchases >= $refer->getGiveSpent()
        ) {
            // if have referral record
            /** @var SpendrReferral $referRecord */
            $referRecord = SpendrReferral::findByInvitee($consumer);
            if ($referRecord && $referRecord->getStatus() === SpendrReferral::STATUS_REFERRED) {
                $inviter = $referRecord->getInviter();
                $total = SpendrReferral::totalReferred($inviter, SpendrReferral::STATUS_FINISHED);
                if ($total < $refer->getMaxCount()) {
                    $referRecord->setStatus(SpendrReferral::STATUS_FINISHED)
                        ->persist();
                    BrazeService::userTrack(null, [
                        'external_id' => BrazeService::getExternalPrefix() . $inviter->getId(),
                        'name' => 'Refer Finished',
                        'time' => Carbon::now()->format('Y-m-d\TH:i:s\Z'),
                        'properties' => [
                            'refer_finished' => $total
                        ]
                    ]);
                    // invitee earn give reward
                    if ($refer->getGiveReward() > 0 && LoadService::beforeReferralLoad($consumer)) {
                        LoadService::referralLoad($consumer, $refer);
                    }
                    // inviter earn get reward
                    if ($refer->getGetReward() > 0 && LoadService::beforeReferralLoad($inviter)) {
                        LoadService::referralLoad($inviter, $refer, true);
                    }
                    SegmentService::track([
                        'userId' => BrazeService::getExternalPrefix() . $consumer->getId(),
                        'event' => 'Referred User Finished',
                        'properties' => [
                            'userId' => BrazeService::getExternalPrefix() . $consumer->getId(),
                            'date' => Carbon::now()->format('Y-m-d\TH:i:s\Z'),
                            'relatedUserId' => BrazeService::getExternalPrefix() . $inviter->getId()
                        ]
                    ]);
                    SegmentService::track([
                        'userId' => BrazeService::getExternalPrefix() . $inviter->getId(),
                        'event' => 'User Referred Finished',
                        'properties' => [
                            'userId' => BrazeService::getExternalPrefix() . $inviter->getId(),
                            'date' => Carbon::now()->format('Y-m-d\TH:i:s\Z'),
                            'relatedUserId' => BrazeService::getExternalPrefix() . $consumer->getId()
                        ]
                    ]);
                }
            }
        }

        // Earn spend reward
        $spends = SpendrReward::findListByType(UserCardLoad::EARN_TYPE_SPEND_REWARD);
        if (count($spends)) {
            /** @var SpendrReward $spend */
            foreach ($spends as $spend) {
                // if spend reward is out of date, inactive it
                if ($spend->getEndAt() && Carbon::now()->isAfter(Carbon::parse($spend->getEndAt()))) {
                    $spend->setStatus(SpendrReward::STATUS_INACTIVE)->persist();
                    continue;
                }
                if ($spend && $spend->getAmount() && $spend->getSpendAmount()) {
                    // if not set target consumers, all consumers can earn; if set, only the target consumers can earn
                    if (!RewardTarget::getCountBy($spend) || RewardTarget::getCountBy($spend, $consumer)) {
                        $spendOver = self::canEarnSpendRewardAmount($t, $spend);
                        $notEarnedBefore = LoadService::beforeEarnLoad($spend, $consumer);
                        if ($spendOver && $notEarnedBefore) {
                            LoadService::earnLoad($consumer, $spend);
                        }
                    }
                }
            }
        }

        $attrs = BrazeService::getUserAttributes($consumer);
        BrazeService::userTrack(null, null, [
            'external_id' => BrazeService::getExternalPrefix() . $consumer->getId(),
            'app_id' => $appId,
            'product_id' => $t->getLocation()->getName(),
            'currency' => 'USD',
            'price' => Money::formatAmountToNumber($t->getAmount(), 'USD'),
            'time' => Carbon::now()->format('Y-m-d\TH:i:s\Z')
        ], $attrs);
        SegmentService::track(null, [
            'userId' => BrazeService::getExternalPrefix() . $consumer->getId(),
            'traits' => $attrs
        ]);

		MerchantService::updateBrazeMerchantBalance($t->getMerchant()->getId(), $merchantBalance);

        PayService::sendWebhook($t);
	}

	public static function getPayResult(Transaction $t, $timezone = null)
	{
        Util::$platform = Platform::spendr();
		$result = $t->toApiArray(true);

		$result['date'] = Util::formatDateTime(
			$t->getTxnTime() ? $t->getTxnTime() : $t->getDateTime(),
			Util::DATE_TIME_FORMAT,
			$timezone ?? Util::timezone()
		);
		return $result;
	}

	// The app side of the consumer does not support transaction cancellation for the time being
//	public static function beforeDecline(User $user, $token)
//	{
//		$isCanTransaction = ConsumerService::isCanTransaction($user, 'app');
//		if (is_string($isCanTransaction)) {
//			return $isCanTransaction;
//		}
//
//		$t = self::checkPendingTransaction($user, $token);
//		if (is_string($t)) {
//			return $t;
//		}
//
//		if (!$t->getConsumer()) {
//			return 'Missing data or abnormal data, please scan the QRCode again or ask the merchant to push it again.';
//		}
//
//		return $t;
//	}
//
//	public static function decline(Transaction $t)
//	{
//		/** @var TransactionStatus $cancelled */
//		$cancelled = TransactionStatus::get(TransactionStatus::NAME_CANCELLED);
//		$t->setStatus($cancelled);
//
//		$em = Util::em();
//		$em->persist($t);
//		$em->flush();
//
//		self::afterDecline();
//
//		return $t;
//	}
//
//	public static function afterDecline()
//	{
//
//	}

	public static function beforeUpdate($token)
	{
		$rs = TransactionService::getNotExpiredTransactionsByToken($token);
		if (!$rs) {
			return 'Unknown transaction or outdated!';
		}
		/** @var Transaction $t */
		$t = $rs[0];
		if ($t->getStatus()->getName() !== TransactionStatus::NAME_PENDING) {
			return 'Transaction has been completed or cancelled!';
		}

		return $t;
	}

	public static function rescan(Transaction $t)
	{
		$t->setConsumer(null)
			->setAccount(null);
		Util::em()->persist($t);
		Util::em()->flush();

		Util::updateMeta($t, [
			'userGetTxnFrom' => null
		]);

		self::afterUpdate($t, PublishService::TOPIC_SEND_RESCAN_RESULT_TO_MERCHANT);
	}

	public static function cancel(Transaction $t)
	{
		/** @var TransactionStatus $cancelled */
		$cancelled = TransactionStatus::get(TransactionStatus::NAME_CANCELLED);
		$t->setStatus($cancelled);
		Util::em()->persist($t);
		Util::em()->flush();

		self::afterUpdate($t, PublishService::TOPIC_SEND_CANCEL_RESULT_TO_MERCHANT);

        PayService::sendWebhook($t);
	}

	public static function afterUpdate(Transaction $t, $topicName)
	{
		$employee = $t->getLocationEmployee();

		// send rescan response to merchant
		try {
			PublishService::pushByUserToken(
				$employee->getId(),
				$topicName,
				$employee,
				[
					'success' => true,
					'message' => '',
					'data' => self::getPayResult($t),
				],
				$t->getToken()
			);
		} catch (FailedException $e) {
			Log::error($e->getFullMessage(), [$e->data]);
		}
	}

	public static function beforeRefund($token, $refundAmount, $isQueue = false)
	{
		if (!$token || $refundAmount <= 0 ) {
			return 'Invalid parameters.';
		}

		/** @var Transaction $oldTransaction */
		$oldTransaction = TransactionService::getCompleteTransactionByToken($token);

		if (!$oldTransaction) {
			return "The transaction cannot be found or cannot be refunded.";
		}

		if ($oldTransaction->getRefundFrom()) {
			return 'Transaction was already be refunded!';
		}

		$merchant = $oldTransaction->getMerchant();
		if (!$merchant || ($merchant && !$merchant->isActive())) {
			return 'Invalid merchant.';
		}

		$location = $oldTransaction->getLocation();
		if (!$location || ($location && $location->getStatus() !== Location::STATUS_ACTIVE)) {
			return 'Invalid location.';
		}

		$consumer = $oldTransaction->getConsumer();
		/** @var Account $account */
		$consumerAccount = $oldTransaction->getAccount();
		$accountStatusInactive = $consumerAccount ? $consumerAccount->getStatus()->isInactive() : false;
		if (!$consumer || !$consumer->isActive() || $accountStatusInactive) {
		  	return 'The consumer account is inactive!';
		}

		$amount = $oldTransaction->getAmount();
		$maxRefundAmount = $amount;

		if ($maxRefundAmount < $refundAmount) {
			$maxRefundAmountText = Money::format($maxRefundAmount, 'USD', false);
			return 'The refund amount cannot be greater than the max refund amount(' . $maxRefundAmountText . ')!';
		}

		$merchantDummy = UserService::getDummyCard($merchant->getAdminUser());
		$merchantBalance = $merchantDummy ? $merchantDummy->getBalance() : 0;
		if ($merchantBalance < $refundAmount) {
			return 'The merchant balance is insufficient to process the refund';
		}

		if (MerchantService::isLocationHasDummyMerchant($merchant)) {
			$locationDummy = UserService::getDummyCard($location->getAdminUser());
			$locationBalance = $locationDummy ? $locationDummy->getBalance() : 0;
			if ($locationBalance < $refundAmount) {
				return 'The location balance is insufficient to process the refund';
			}
		}

		//// restrict: future
		// $restrictAreaLoad = $oldTransaction->getRestrictLoad();
		// if ($restrictAreaLoad) {
		// 	$consumerDummy = UserService::getDummyCard($consumer);
		// 	if (self::needWithdrawRefundAmount($consumerDummy->getBalance(), $refundAmount)) {
		// 		$checkRes = self::beforewithdrawRefundAmountForConsumer($restrictAreaLoad, $consumer, $isQueue);
		// 		if (is_string($checkRes)) {
		// 			return 'The consumer’s data is abnormal and cannot be refunded.';
		// 		}
		// 	}
		// }

		return $oldTransaction;
	}

	public static function refundAction(
		Transaction $oldTransaction,
		$refundAmount,
		$employeeId,
		$terminalId,
		$timezone
	){
		$merchant = $oldTransaction->getMerchant();
		$consumer = $oldTransaction->getConsumer();
		$location = $oldTransaction->getLocation();
		$terminal = Terminal::find($terminalId);
		$consumerAccount = $oldTransaction->getAccount();
		$employee = User::find($employeeId);

		$merchantAdmin = $merchant->getAdminUser();
		$merchantDummy = UserService::getDummyCard($merchantAdmin);
		$merchantBalance = $merchantDummy ? $merchantDummy->getBalance() : 0;
		$consumerDummy = UserService::getDummyCard($consumer);
		$consumerBalance = $consumerDummy ? $consumerDummy->getBalance() : 0;

		$refundTransaction = new Transaction();
		$refundTransaction->setType(TransactionType::get(TransactionType::NAME_REFUND))
			->setAmount($refundAmount)
			->setMerchant($merchant)
			->setLocationEmployee($employee)
			->setLocation($location)
			->setTerminal($terminal)
			->setProducerBalanceBefore($merchantBalance)
			->setConsumer($consumer)
			->setAccount($consumerAccount)
			->setConsumerBalanceBefore($consumerBalance)
			->setUserCard($consumerDummy);
		// Pay money.
		$comment = sprintf(
			'In refund transaction %s at %s. Refund amount: %s',
			$refundTransaction->getToken(),
			Carbon::now(),
			Money::format($refundAmount, 'USD')
		);

		// Change consumer balance
		// If the refund amount plus the current balance is greater than the balance limit, add directly
		$consumerDummy->updatePrivacyBalanceBy(
			$refundAmount,
			UserCardBalance::TYPE_REFUND,
			$comment,
			false,
			$refundTransaction,
			);

		// Change merchant balance
		// If the merchant does not have enough balance they can drive their account into the negative
		$merchantDummy->updatePrivacyBalanceBy(
			-$refundAmount,
			UserCardBalance::TYPE_REFUND,
			$comment,
			false,
			$refundTransaction,
			);

		// Change location balance
		$locationDummy = null;
		if (LocationService::beforeChangeLocationBalance(
			$location,
			UserCardBalance::TYPE_REFUND,
			$refundTransaction->getId(),
		)) {
			$locationDummy = UserService::getDummyCard($location->getAdminUser());
			$locationDummy->updatePrivacyBalanceBy(-$refundAmount, UserCardBalance::TYPE_REFUND, $comment, false, $refundTransaction);
		}

		$oldTransaction->setRefundFrom($refundTransaction);
		Util::em()->persist($refundTransaction);

		FeeService::handleRefundTransactionFee($oldTransaction, $refundTransaction);

		$consumerBalanceAfter = $consumerDummy->getBalance();
		$refundTransaction->setConsumerBalanceAfter($consumerBalanceAfter)
			->setProducerBalanceAfter($merchantDummy->getBalance())
			->setStatus(TransactionStatus::get(TransactionStatus::NAME_COMPLETED));

		Util::em()->persist($oldTransaction);
		Util::em()->persist($consumerDummy);
		Util::em()->persist($merchantDummy);
		if ($locationDummy) {
			Util::em()->persist($locationDummy);
		}
		Util::em()->flush();

		// $restrictAreaLoad = $oldTransaction->getRestrictLoad();
		// if ($restrictAreaLoad) {
		// 	self::withdrawRefundAmountForConsumer(
		// 		$consumer,
		// 		$consumerBalance,
		// 		$consumerBalanceAfter,
		// 		$refundAmount,
		// 		$refundTransaction,
		// 		$employee,
		// 		$restrictAreaLoad
		// 	);
		// }

		self::afterRefund($refundTransaction, $oldTransaction, $employee, $consumer, $timezone, $merchantDummy->getBalance());
	}

	public static function afterRefund(Transaction $refundTxn, Transaction $oldTxn, User $employee, User $consumer, $timezone, $merchantBalance)
	{
		SlackService::tada(
			'Successfully refunded the transaction ' . $refundTxn->getToken()
			. ' to consumer ' . $consumer->getSignature(),
			[
				'Transaction Token' => $refundTxn->getToken(),
				'Consumer ID' => $refundTxn->getConsumer()->getId(),
				'Amount' => Money::format($refundTxn->getAmount(), 'USD'),
				'Refund Fee' => Money::format($refundTxn->getRefundFee(), 'USD'),
			]
		);

		$data = self::getPayResult($refundTxn, $timezone);
		// send refund response to merchant
		try {
			PublishService::pushByUserToken(
				$employee->getId(),
				PublishService::TOPIC_SEND_REFUND_RESULT_TO_MERCHANT,
				$employee,
				[
					'success' => true,
					'message' => '',
					'data' => $data,
				],
				$oldTxn->getToken()
			);
		} catch (FailedException $e) {
			Log::error($e->getFullMessage(), [$e->data]);
		}

		// offset pending balance
		LoadService::offsetPendingFee($refundTxn->getAmount(), UserCard::OFFSET_PENDING_FEE_BY_REFUND_TXN, null, $refundTxn);

		MerchantService::updateBrazeMerchantBalance($oldTxn->getMerchant()->getId(), $merchantBalance);

        PayService::sendWebhook($oldTxn);
	}

	//// restirct: future
	// private static function needWithdrawRefundAmount($consumerBalance, $refundAmount)
	// {
	// 	$consumerBalanceAfter = $consumerBalance - $refundAmount;
	// 	if ($consumerBalanceAfter <= 0) {
	// 		return false;
	// 	}

	// 	return true;
	// }

	//// restirct: future
	// public static function beforewithdrawRefundAmountForConsumer(UserCardLoad $restrictAreaLoad, User $consumer, $isQueue = false)
	// {
	// 	$userCardTxn = UserCardTransaction::findByTranId($restrictAreaLoad->getTransactionNo());
	// 	if (!$userCardTxn) {
	// 		return 'The consumer’s data is abnormal and cannot be refunded.';
	// 	}

	// 	$bankCard = $userCardTxn->getUserCard();
	// 	if (!$bankCard) {
	// 		return 'The consumer’s data is abnormal and cannot be refunded.';
	// 	}

	// 	if (!$isQueue) {
	// 		$verifyPlaidCard = UserCardService::verifyPlaidCard($bankCard, $consumer);
	// 		if (is_string($verifyPlaidCard)) {
	// 			return $verifyPlaidCard;
	// 		}
	// 	}

	// 	return true;
	// }

	//// restrict: future
	// private static function withdrawRefundAmountForConsumer(
	// 	User $consumer,
	// 	$consumerBalanceBefore,
	// 	$consumerBalanceAfter,
	// 	$refundAmount,
	// 	Transaction $refundTransaction,
	// 	$employee,
	// 	UserCardLoad $restrictAreaLoad
	// ) {
	// 	if ($consumerBalanceAfter <= 0) {
	// 		SlackService::info('User ' . $consumer->getId() . '’s balance is negative and does not need to withdrawal.', [
	// 			'current balance' => Money::formatUSD($consumerBalanceAfter),
	// 			'refund txn' => $refundTransaction->getId()
	// 		], [SlackService::MENTION_TRACY]);
	// 		return false;
	// 	}

	// 	if ($consumerBalanceBefore >= 0) {
	// 		$withdrawAmount = $refundAmount;
	// 	} else {
	// 		$withdrawAmount = $consumerBalanceAfter;
	// 	}

	// 	$userCardTxn = UserCardTransaction::findByTranId($restrictAreaLoad->getTransactionNo());
	// 	$bankCard = $userCardTxn->getUserCard();

	// 	$unload = self::generateUnload($consumer, $withdrawAmount, $bankCard, $employee, $refundTransaction->getId());

	// 	if (is_string($unload)) {
	// 		// load back to merchant
	// 	}

	// 	$loadRes = Service::loadCard($unload, true);
	// 	LoadService::afterLoad($unload, $loadRes);
	// 	if (is_string($loadRes)) {
	// 		// load back to merchant
	// 	}
	// }

	//// restirct: future
	// private static function loadBackToMerchant()
	// {

	// }

	public static function checkPendingTransaction(User $user, $token)
	{
		$rs = self::getNotExpiredTransactionsByToken($token);
		if (!$rs) {
			return 'Unknown transaction or outdated!';
		}

		/** @var Transaction $t */
		$t = $rs[0];
		if ($t->getStatus()->getName() !== TransactionStatus::NAME_PENDING) {
			return 'Transaction has been completed or canceled!';
		}

		$merchant = $t->getMerchant();
		if (!$merchant || ($merchant && !$merchant->isActive())) {
			return 'Unknown transaction merchant!';
		}

		$location = $t->getLocation();
		if (!$location || ($location && $location->getStatus() !== Location::STATUS_ACTIVE)) {
			return 'Unknown transaction location!';
		}

		$terminal = $t->getTerminal();
		if (!$terminal) {
			return 'Unknown transaction terminal!';
		}

		$employee = $t->getLocationEmployee();
		if (Util::eq($user->getId(), $employee->getId())) {
			return 'Invalid transaction! It is created by yourselves.';
		}

		if ($merchant instanceof SpendrMerchant && !$user->isSpendrConsumer()) {
			return 'You are not permitted for this transaction!';
		}

		$consumer = $t->getConsumer();
		if ($consumer && $consumer->getId() !== $user->getId()) {
			return 'You are not the consumer of this transaction!';
		}

		return $t;
	}

    public static function renderReceipt(Transaction $txn, $target = 'consumer', $timezone = null)
    {
    	$dateTime = Util::formatDateTime(
			$txn->getTxnTime() ? $txn->getTxnTime() : $txn->getDateTime(),
			Util::DATE_TIME_FORMAT,
			$timezone ? $timezone : Util::timezone()
		);

		$showBankFundsAndRewardFunds = $txn->needShowBankFundsAndRewardFunds();
		$bankFunds = 0;
		$rewardFunds = 0;
		if ($showBankFundsAndRewardFunds) {
			$bankFunds = $txn->getBankFunds();
			$rewardFunds = $txn->getRewardFunds();
		}

        return Util::render('@Spendr/Merchant/receipt.html.twig', [
            'txn' => $txn,
            'isRefund' => $txn->isRefund(),
            'target' => $target,
			'dateTime' => $dateTime,
			'isOnline' => $txn->isFromPayApi(),
			'showBankFundsAndRewardFunds' => $showBankFundsAndRewardFunds,
			'bankFunds' => $bankFunds,
			'rewardFunds' => $rewardFunds,
            'disclaimer' => $txn->getDisclaimer()
        ]);
    }

	public static function getLatestPendingTransactionByUserId(Request $request, User $user)
	{
		$expr = Util::expr();
		$transactions = Util::em()->getRepository(Transaction::class)
			->createQueryBuilder('t')
			->where($expr->eq('t.consumer', ':consumer'))
			->andWhere($expr->in('t.status', ':status'))
			->andWhere($expr->in('t.type', ':type'))
			->andWhere('t.dateTime <= :now')
			->andWhere('t.dateTime > :endTime')
			->setParameter('consumer', $user)
			->setParameter('status', TransactionStatus::gets([
				TransactionStatus::NAME_PENDING
			]))
			->setParameter('type', TransactionType::gets([
				TransactionType::NAME_PURCHASE
			]))
			->setParameter('now', Carbon::now())
			->setParameter('endTime', Carbon::now()->subMinutes(self::transactionExpiredTime))
			->orderBy('t.dateTime', 'desc')
			->setMaxResults(1)
			->getQuery()
			->getResult();
		if ($transactions && $transactions[0])
		{
			return $transactions[0];
		}
		return null;
	}

	public static function getNotExpiredTransactionsByToken($token)
	{
		if (!$token) {
			return null;
		}
		$expr = Util::expr();
		return Util::em()->getRepository(Transaction::class)
			->createQueryBuilder('t')
			->where($expr->eq('t.token', ':token'))
			->andWhere('t.dateTime <= :now')
			->andWhere('t.dateTime > :endTime')
			->setParameter('token', $token)
			->setParameter('now', Carbon::now())
			->setParameter('endTime', Carbon::now()->subMinutes(self::transactionExpiredTime))
			->getQuery()
			->getResult();
	}

	public static function getCompleteTransactionByToken($token)
	{
		if (!$token) {
			return null;
		}
		$expr = Util::expr();
		$transactions = Util::em()->getRepository(Transaction::class)
			->createQueryBuilder('t')
			->where($expr->eq('t.token', ':token'))
			->andWhere($expr->in('t.status', ':status'))
			->andWhere($expr->in('t.type', ':type'))
			->setParameter('token', $token)
			->setParameter('status', TransactionStatus::gets([
				TransactionStatus::NAME_COMPLETED
			]))
			->setParameter('type', TransactionType::gets([
				TransactionType::NAME_PURCHASE
			]))
			->setMaxResults(1)
			->getQuery()
			->getResult();

		if ($transactions && $transactions[0])
		{
			return $transactions[0];
		}
		return null;
	}

	public static function getConsumerSpendDailyAmount(User $user, $timezone = null)
	{
		if (!$user) {
			return null;
		}

		$expr = Util::expr();

		if (!$timezone) {
			$timezone = Util::timezone();
		}
		$start = Carbon::now($timezone)->startOfDay();
		$end = Carbon::now($timezone)->endOfDay();

		$subQuery = Util::em()->getRepository(Transaction::class)
			->createQueryBuilder('t')
			->where($expr->eq('t.consumer', ':consumer'))
			->andWhere($expr->in('t.status', ':status'))
			->andWhere($expr->in('t.type', ':type'))
			->andWhere('t.dateTime > :start')
			->andWhere('t.dateTime < :end')
			->select('t.id')
			->getDQL();

		$spendAmount = Util::em()->getRepository(Transaction::class)
			->createQueryBuilder('tt')
			->where($expr->eq('tt.id', $expr->any($subQuery)))
			->setParameter('consumer', $user)
			->setParameter('status', TransactionStatus::gets([
				TransactionStatus::NAME_COMPLETED
			]))
			->setParameter('type', TransactionType::gets([
				TransactionType::NAME_PURCHASE
			]))
			->setParameter('start', Util::toUTC($start))
			->setParameter('end', Util::toUTC($end))
			->select('sum(tt.amount)')
			->getQuery()
			->getSingleScalarResult();

		$refundAmount = Util::em()->getRepository(Transaction::class)
			->createQueryBuilder('tt')
			->leftJoin('tt.refundFrom', 'rt')
			->where($expr->eq('tt.id', $expr->any($subQuery)))
			->andWhere($expr->in('rt.status', ':status'))
			->andWhere($expr->in('rt.type', ':refundType'))
			->setParameter('refundType', TransactionType::gets([
				TransactionType::NAME_REFUND
			]))
			->andWhere('rt.dateTime > :start')
			->andWhere('rt.dateTime < :end')
			->setParameter('consumer', $user)
			->setParameter('status', TransactionStatus::gets([
				TransactionStatus::NAME_COMPLETED
			]))
			->setParameter('type', TransactionType::gets([
				TransactionType::NAME_PURCHASE
			]))
			->setParameter('start', Util::toUTC($start))
			->setParameter('end', Util::toUTC($end))
			->select('sum(rt.amount)')
			->getQuery()
			->getSingleScalarResult();

		return (int)$spendAmount - (int)$refundAmount;
	}

	public static function getCurrentMonthTotalAmount() {
		$start = Carbon::now()->startOfMonth()->startOfDay();
		$end = Carbon::now()->endOfMonth()->endOfDay();
		$expr = Util::expr();

		$query = Util::em()->getRepository(Transaction::class)
			->createQueryBuilder('t')
			->join('t.merchant', 'm')
			->join('m.adminUser', 'u')
			->join('u.teams', 'r')
			->where('r.name = :role')
			->setParameter('role', Role::ROLE_SPENDR_MERCHANT_ADMIN)
			->andWhere($expr->in('t.status', ':status'))
			->setParameter('status', TransactionStatus::gets([
				TransactionStatus::NAME_COMPLETED
			]))
			->andWhere('t.dateTime > :start')
			->andWhere('t.dateTime < :end')
			->setParameter('start', $start)
			->setParameter('end', $end)
			->select('SUM(t.amount)');

		$purchaseAmount = (clone $query)->andWhere($expr->in('t.type', ':type'))
			->setParameter('type', TransactionType::gets([
				TransactionType::NAME_PURCHASE
			]))
			->getQuery()
			->getSingleScalarResult();

		$refundAmount = (clone $query)->andWhere($expr->in('t.type', ':type'))
			->setParameter('type', TransactionType::gets([
				TransactionType::NAME_REFUND
			]))
			->getQuery()
			->getSingleScalarResult();

		$amount = (int)$purchaseAmount - (int)$refundAmount;
		return $amount;
	}

	public static function getSpendAmountWithReward($user, SpendrReward $reward)
    {
        $now = Carbon::now();
        $purchases = Util::em()->getRepository(Transaction::class)
            ->createQueryBuilder('t')
            ->join('t.consumer', 'u')
            ->where('u.id = :id')
            ->andWhere('t.status = :status')
            ->andWhere('t.type = :type')
            ->andWhere(Util::expr()->isNull('t.refundFrom'))
            ->setParameter('id', $user->getId())
            ->setParameter('status', TransactionStatus::get(TransactionStatus::NAME_COMPLETED))
            ->setParameter('type', TransactionType::get(TransactionType::NAME_PURCHASE));
        if ($reward->getDateType() === SpendrReward::DATE_TYPE_PER_MONTH) {
            $purchases->andWhere('date_format(t.txnTime, \'%Y-%m\') = :date')
                ->setParameter('date', $now->format('Y-m'));
        } elseif ($reward->getDateType() === SpendrReward::DATE_TYPE_DATE_RANGE) {
            $purchases->andWhere('t.txnTime >= :start')
                ->setParameter('start', $reward->getStartAt())
                ->andWhere('t.txnTime <= :end')
                ->setParameter('end', $reward->getEndAt());
        }
        return $purchases->select('sum(t.amount)')
            ->getQuery()
            ->getSingleScalarResult();
    }

	// If the consumer spend amount between reward setting date range is over $amount
	public static function canEarnSpendRewardAmount(Transaction $t, SpendrReward $reward)
    {
        $purchases = self::getSpendAmountWithReward($t->getConsumer(), $reward);
        if ($purchases >= $reward->getSpendAmount() && $purchases - $t->getAmount() < $reward->getSpendAmount()) {
            return true;
        }
        return false;
    }

	public static function checkTxnStatus(Request $request, $token)
	{
		/** @var Transaction $t */
		$t = Util::em()->getRepository(Transaction::class)
			->findOneBy([
				'token' => $token
			]);
		if (!$t) {
			return 'Unknown transaction!';
		}

		$status = $t->getStatus()->getName();
		if ($status === TransactionStatus::NAME_PENDING) {
			$dateTime = $t->getDateTime();
			if (
			!(
				($dateTime <= Carbon::now())
				&& ($dateTime > Carbon::now()->subMinutes(TransactionService::transactionExpiredTime))
			)
			) {
				return 'The transaction has expired!';
			}
		}

		$data = self::getPayResult($t);
        $pt = SpendrPayToken::findBy($t->getConsumer(), $t->getMerchant()->getGroup());
        if ($pt) {
            $data['consumerToken'] = $pt->getToken();
        }

		$data['refundTransaction'] = null;
		if ($t->getRefundFrom()) {
			$refundTxn = $t->getRefundFrom();
			if (
				$refundTxn->getStatus()
				&& $refundTxn->getStatus()->getName() === TransactionStatus::NAME_COMPLETED
			) {
				$refundTxnData = $t->getRefundFrom()->toApiArray();
				$refundTxnData['date'] = Util::formatDateTime(
					$refundTxn->getTxnTime() ? $refundTxn->getTxnTime() : $refundTxn->getDateTime(),
					Util::DATE_TIME_FORMAT,
					Util::timezone()
				);
				$data['refundTransaction'] = $refundTxnData;
			}
		}

		return $data;
	}

	public static function getConsumerTotalSpendAmount(
		User $consumer,
		SpendrMerchant $merchant = null,
		$start = null,
		$end = null
	) {
		if (!$consumer) {
			return null;
		}

		$spendQuery = self::getTranctionQuery(
			$consumer,
			$merchant,
			[TransactionType::NAME_PURCHASE],
			[TransactionStatus::NAME_COMPLETED],
			$start,
			$end
		);

		$refundQuery = self::getTranctionQuery(
			$consumer,
			$merchant,
			[TransactionType::NAME_REFUND],
			[TransactionStatus::NAME_COMPLETED],
			$start,
			$end
		);

		$spendAmount = $spendQuery->select('sum(t.amount)')
			->getQuery()
			->getSingleScalarResult();

		$refundAmount = $refundQuery->select('sum(t.amount)')
			->getQuery()
			->getSingleScalarResult();

		return (int)$spendAmount - (int)$refundAmount;
	}

	public static function getTranctionQuery(
		User $consumer = null,
		SpendrMerchant $merchant = null,
		$types = [],
		$statuses = [],
		DateTime $start = null,
		DateTime $end = null,
		Location $location = null
	) {
		$expr = Util::expr();

		$query = Util::em()->getRepository(Transaction::class)
			->createQueryBuilder('t')
			->join('t.merchant', 'm')
			->leftJoin('t.consumer', 'u')
			->where('m.id >= :firstMerchantId')
			->setParameter('firstMerchantId', SpendrBundle::FIRST_MERCHANT_ID);

		if ($consumer) {
			$query->andWhere($expr->eq('t.consumer', ':consumer'))
				->setParameter('consumer', $consumer);
		}

		if ($merchant) {
			$query = MerchantService::generateMergeMerchantQuery($query, 't.merchant', $merchant);
		}

		if ($location) {
			$query->andWhere($expr->eq('t.location', ':location'))
				->setParameter('location', $location);
		}

		if ($types) {
			$query->andWhere($expr->in('t.type', ':types'))
				->setParameter('types', TransactionType::gets($types));
		}

		if ($statuses) {
			$query->andWhere($expr->in('t.status', ':statuses'))
				->setParameter('statuses', TransactionStatus::gets($statuses));
		}

		if ($start) {
			$query->andWhere('t.dateTime > :start')
				->setParameter('start', Util::toUTC($start));
		}

		if ($end) {
			$query->andWhere('t.dateTime < :end')
				->setParameter('end', Util::toUTC($end));
		}

		return $query;
	}
}
