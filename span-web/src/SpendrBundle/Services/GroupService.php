<?php

namespace SpendrBundle\Services;


use CoreBundle\Entity\Address;
use CoreBundle\Entity\Country;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\State;
use CoreBundle\Exception\FailedException;
use CoreBundle\Services\UserService;
use CoreBundle\Utils\Util;
use SalexUserBundle\Entity\User;
use SpendrBundle\Entity\SpendrGroup;
use SpendrBundle\Entity\SpendrMerchant;
use SpendrBundle\SpendrBundle;
use Symfony\Component\HttpFoundation\Request;


class GroupService
{
    public static function saveDetails(Request $request)
    {
        $em = Util::em();
        $name = $request->get('Name');
        $contactName = $request->get('Contact Name');
        $contactEmail = $request->get('Contact Email');
        $contactPhone = $request->get('Contact Phone');
        $status = $request->get('Status');
        $id = $request->get('ID');
        $merchantIds = $request->get('groupMerchantIds');

		$role = Role::ROLE_SPENDR_GROUP_ADMIN;

        $forCheckRoles = array_merge([$role], SpendrBundle::getAdminRoles());

        if ($id) {
            $group = $em->getRepository(SpendrGroup::class)
                ->find($id);
            if (!$group) {
                throw new FailedException('Invalid group!');
            }

            $user = $group->getAdminUser() ? $group->getAdminUser() : null;
            if ($user && $contactEmail !== $user->getEmail()) {
                $another = User::findPlatformUserByEmail($contactEmail, $forCheckRoles);
                if ($another) {
                    throw new FailedException('The email address has been registered by another group!');
                }
            }

            $group->setStatus($status);

        } else {
            $another = $em->getRepository(SpendrGroup::class)
                ->findOneBy([
                    'name' => $name,
                ]);
            if ($another) {
                throw new FailedException('The group name has been registered by another group!');
            }

            $another = User::findPlatformUserByEmail($contactEmail, $forCheckRoles);
            if ($another) {
                throw new FailedException('The email address has been registered by another group!');
            }

            $group = new SpendrGroup();
            // $group->setOnboardingStatus(SpendrGroup::ONBOARDING_STATUS_INITIAL);
        }

        $group->setName($name)
			->persist();

        $oldGroupMerchants = $group->getMerchants();
        if ($merchantIds || $oldGroupMerchants) {
            $existMerchantIds = [];
            foreach($oldGroupMerchants as $m) {
                $id = $m->getId();
                if (!in_array($id, $merchantIds)) {
                    $m->setGroup(null)->persist();
                } else {
                    $existMerchantIds[] = $id;
                }
            }
            foreach ($merchantIds as $mid) {
                if (in_array($mid, $existMerchantIds)) {
                    continue;
                }
                $mer = SpendrMerchant::find($mid);
                if ($mer->getGroup()) {
                    continue;
                }
                $mer->setGroup($group)
                    ->persist();
            }
        }

        $user = $group->getAdminUser();
        if (!$user) {
            $user = new User();
            $user->setEnabled(true)
                ->setPlainPassword(Util::generatePassword())
                ->setStatus(User::STATUS_ACTIVE);

            $country = Country::usa();

            $phone = Util::inputPhone($contactPhone, $country);
            $user->setFirstName($contactName)
                ->setMobilephone($phone)
                ->setPhone($phone)
                ->changeEmail($contactEmail, $role)
                ->ensureRole($role)
                ->persist();

            $cp = Util::cardProgram();
            UserService::sendResetPasswordEmail($user, $cp);

            BrazeService::userTrack(null, null, null, [
                'external_id' => BrazeService::getExternalPrefix() . $user->getId(),
                'first_name' => $user->getFirstName(),
                'last_name' => $user->getLastName(),
                'email' => $user->getEmail(),
                'phone' => $user->getMobilephone(),
                'group_name' => $group->getName(),
                'group_role' => $role,
                'email_subscribe' => $user->getStatus() === User::STATUS_ACTIVE ? BrazeService::TYPE_OPTEDIN : BrazeService::TYPE_UNSUBSCRIBED
            ]);
        } else {
            if (
                $contactEmail !== $user->getEmail()
                || $contactPhone !== $user->getMobilephone()
                || $name !== $user->getFirstName()
            ) {
                $resetEmail = false;
                if ($contactEmail !== $user->getEmail()) {
                    $user->changeEmail($contactEmail, $role);
                    $resetEmail = true;
                }
                if ($contactPhone !== $user->getMobilephone()) {
                    $user->changeMobilePhone($contactPhone);
                }
                if ($name !== $user->getFirstName()) {
                    $user->setFirstName($name);
                }

                $user->persist();

                if ($resetEmail) {
                    UserService::sendResetPasswordEmail($user, Util::cardProgram());
                }

                BrazeService::userTrack(null, null, null, [
                    'external_id' => BrazeService::getExternalPrefix() . $user->getId(),
                    'first_name' => $user->getFirstName(),
                    'email' => $user->getEmail(),
                    'phone' => $user->getMobilephone(),
                ]);
            }
        }

        $group->setAdminUser($user)
            ->persist();

        $user->ensureRole($role)
            ->ensureAccessiblePlatformProgram()
            ->persist();

        return $group;
    }

    public static function getAllGroups()
    {
        return Util::em()->getRepository(SpendrGroup::class)
        ->createQueryBuilder('sg')
        ->getQuery()
        ->getResult();
    }
}
