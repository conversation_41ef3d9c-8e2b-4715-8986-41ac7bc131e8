<?php

namespace SpendrBundle\Services;

use Carbon\Carbon;
use CoreBundle\Entity\Role;
use CoreBundle\Exception\FailedException;
use CoreBundle\Utils\Util;
use SalexUserBundle\Entity\User;
use SpendrBundle\Entity\Location;
use SpendrBundle\Entity\LocationEmployee;
use SpendrBundle\Entity\SpendrMerchant;
use SpendrBundle\SpendrBundle;
use UsUnlockedBundle\Services\Privacy\PrivacyAPI;

class LocationService
{
	public static function getLocationsByUser($user)
	{
		if (!$user) {
			return null;
		}
		$data = null;
		$expr = Util::expr();
		$result = Util::em()->getRepository(LocationEmployee::class)
			->createQueryBuilder('le')
			->join('le.location', 'l')
			->where($expr->eq('le.user', ':user'))
			->setParameter('user', $user)
			->andWhere($expr->eq('l.status', ':status'))
			->setParameter('status', Location::STATUS_ACTIVE)
			->getQuery()
			->getResult();
		if ($result) {
			/** @var LocationEmployee $item */
			foreach ($result as $item) {
				$location = $item->getLocation();
				$data[] = [
					'id' => $location->getId(),
					'name' => $location->getName(),
				];
			}
		}
		return $data;
	}

    /**
     * @param bool $loggedIn Verify if there is a logged-in user and the user has the access to the location
     * @param bool $validate Whether to validate the location
     *
     * @return Location|null
     * @throws FailedException
     */
	public static function getCurrentLocation(bool $loggedIn = true, bool $validate = true)
	{
		$request = Util::$request;
		$key = 'x-location';
		if ($request->headers->has($key)) {
			$locationId = $request->headers->get($key);
			if ($validate) {
				$location = self::verifyLocation($locationId, $loggedIn);
				if (is_string($location)) {
					throw new FailedException($location);
				}
			} else {
				if ($locationId) {
					$location = Location::find($locationId);
				} else {
					return null;
				}
			}
		} else {
            if (!$validate) {
                return null;
            }
			throw new FailedException('Invalid location id!');
		}

		return $location;
	}

	public static function verifyLocation($locationId, bool $loggedIn = true, $employeeId = null)
	{
		if (!$locationId) {
			return 'Invalid location id!';
		}

		$location = Location::find($locationId);
		if (!$location) {
			return 'Invalid location!';
		}
		if ($location->getStatus() !== Location::STATUS_ACTIVE) {
			return 'Please activate this location first!';
		}

		if ($loggedIn) {
			if ($employeeId) {
				$user = User::find($employeeId);
			} else {
				$user = Util::user();
			}
			$result = EmployeeService::getLocationEmployee($location, $user);
			if (!$result) {
				return 'The location is not associated with your account!';
			}
		}

		return $location;
	}

	public static function getAllLocations($user, Carbon $start = null, Carbon $end = null)
	{
		$em = Util::em();
		$query = $em->getRepository(Location::class)
			->createQueryBuilder('l')
			->join('l.address', 'a')
			->join('l.merchant', 'm');

        if (
            $user->isMasterAdmin()
            || $user->inTeams(SpendrBundle::getSpendrAdminRoles())
        )
        {
            $testIds = MerchantService::merchantIdsForTestArray();
            if ($testIds) {
                $query->where(Util::expr()->notIn('m.id', ':ids'))
                    ->setParameter('ids', $testIds);
            }
        }
        else if ($user->inTeams(SpendrBundle::getMerchantAdminRoles()))
        {
            $merchant = $user->getSpendrAdminMerchant();

			$query = MerchantService::generateMergeMerchantQuery($query, 'l.merchant', $merchant);

			if ($user->inTeams(SpendrBundle::getMerchantDashboardAdminRoles())) {
				$query->join('l.admins', 'locationAdmin')
				->andWhere('locationAdmin.id = :uid')
				->setParameter('uid', $user->getId());
			}
        }

		return $query->getQuery()
			->getResult();
	}

    // Get locaton list for select
    public static function listLocationsForSelection(
		SpendrMerchant $merchant,
		User $locationAdmin = null,
		$withBalance = false
	) {
        $query = Util::em()->getRepository(Location::class)
            ->createQueryBuilder('l')
            ->where(Util::expr()->eq('l.status', ':status'))
            ->setParameter('status', Location::STATUS_ACTIVE);

		$query = MerchantService::generateMergeMerchantQuery($query, 'l.merchant', $merchant);

		$select = 'l.id as value, l.name as label';

		if ($withBalance && MerchantService::isLocationHasDummyMerchant($merchant)) {
			$query->join('l.adminUser', 'balanceUser')
			->join('balanceUser.cards', 'uc')
			->join('uc.card', 'c')
			->andWhere('c.cardProgram = :cardProgram')
			->setParameter('cardProgram', Util::cardProgram())
			->andWhere('uc.type = :type')
			->setParameter('type', PrivacyAPI::CARD_TYPE_DUMMY);

			$select .= ', uc.balance as balance';
		}

		if ($locationAdmin) {
			$expr = Util::expr();
			$query->join('l.admins', 'locationAdmin')
			->andWhere($expr->eq('locationAdmin.id', ':locationAdminId'))
			->setParameter('locationAdminId', $locationAdmin->getId());
		}

        return $query->orderBy('l.name')
            ->distinct()
            ->select($select)
            ->getQuery()
            ->getArrayResult();
    }

	public static function createLocationBalanceUser(SpendrMerchant $merchant, Location $location, $special = false)
	{
		if (MerchantService::isLocationHasDummyMerchant($merchant) || $special) {
			// continue
		} else {
			return;
		}

		$user = $location->getAdminUser();
		if (!$user) {
			$address = $location->getAddress();
			$email = 'tracy+spendr_location_balance_admin_' . $merchant->getId() . '_' . $location->getId() . '@ternitup.com';
			$role = Role::ROLE_SPENDR_LOCATION_BALANCE_ADMIN;

			$user = new User();
            $user->setEnabled(true)
                ->setPlainPassword(Util::generatePassword())
                ->setStatus(User::STATUS_ACTIVE)
                ->setSource('spendr_create_location');

			$user->setFirstName($location->getName())
				->setLastName('Location Balance Admin')
				->setMobilephone(Util::inputPhone($address->getPhone(), $address->getCountry()))
				->setAddress($address->getAddress1())
				->setCountry($address->getCountry())
				->setState($address->getState())
				->setCity($address->getCity())
				->setZip($address->getPostalCode())
				->changeEmail($email, SpendrBundle::getRoles())
				->ensureRole($role)
				->persist();

			$location->setAdminUser($user)
			->persist();
			$user->ensureAccessiblePlatformProgram()
            ->persist();

			UserService::getDummyCard($user);
		}

		return $user;
	}

	public static function beforeChangeLocationBalance(Location $location, $txnType, $txnId)
	{
		if ($location
			&& $location->getMerchant()
			&& MerchantService::isLocationHasDummyMerchant($location->getMerchant())
		) {
			if ($location->getAdminUser()) {
				return true;
			} else {
				SlackService::alert('Change location balance: Invalid location or location balance account.', [
					'transactionId' => $txnId,
					'transactionType' => $txnType,
					'locationId' => $location ? $location->getId() : null,
					'locationAdminId' => $location && $location->getAdminUser() ? $location->getAdminUser()->getId() : null,
					'locationStatus' => $location->getStatus()
				], [SlackService::MENTION_TRACY]);
				return false;
			}
		} else {
			return false;
		}
	}

	public static function getLocationDummyByLocationId($locationId)
	{
		/** @var Location $location */
		$location = Location::find($locationId);
		$locationAdmin = $location ? $location->getAdminUser() : null;
		$locationDummy = null;
		if ($locationAdmin) {
			$locationDummy = UserService::getDummyCard($locationAdmin);
		}
		return $locationDummy;
	}

	public static function getValidLocationIdsByLocationIds(SpendrMerchant $merchant, $locationIds) {
	    $query = Util::em()->getRepository(Location::class)
            ->createQueryBuilder('l')
            ->where('l.status = :status')
            ->setParameter('status', Location::STATUS_ACTIVE)
            ->andWhere(Util::expr()->in('l.id', ':ids'))
            ->setParameter('ids', $locationIds);

		$query = MerchantService::generateMergeMerchantQuery($query, 'l.merchant', $merchant);
		
        $ids = $query->select('l.id')
            ->getQuery()
            ->getArrayResult();
        if ($ids) {
            $ids = array_map(function ($item) { return $item['id']; }, $ids);
        }
	    return $ids;
    }

    public static function getLocationQueryOfMerchant(SpendrMerchant $merchant)
    {
        $query = Util::em()->getRepository(Location::class)
            ->createQueryBuilder('l')
            ->where('l.status = :status')
			->setParameter('status', Location::STATUS_ACTIVE)
            ->orderBy('l.name');

		$query = MerchantService::generateMergeMerchantQuery($query, 'l.merchant', $merchant);

		return $query;
    }
}
