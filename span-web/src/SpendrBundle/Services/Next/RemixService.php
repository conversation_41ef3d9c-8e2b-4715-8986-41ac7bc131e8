<?php

namespace SpendrBundle\Services\Next;

use Api<PERSON>undle\Services\ApiRunner;
use CoreBundle\Entity\ExternalInvoke;
use CoreBundle\Entity\UserToken;
use CoreBundle\Utils\Util;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use PortalBundle\Exception\PortalException;
use Symfony\Component\HttpFoundation\Response;

class RemixService
{
    public const TOKEN = 'spendr_legacy';

    public function request($method, $endpoint, $params = [], $saveEi = false)
    {
        $originMethod = strtoupper($method);
        if (in_array($originMethod, ['FORM', 'JSON', 'MULTIPART'])) {
            $method = 'POST';
        }

        $context = $params;
        $ei = ExternalInvoke::create('remix_' . $method . '_' . $endpoint, $context, null, $saveEi);

        $client = new Client();
        $url = Util::getConfigKey('spendr2_url') . $endpoint;

        $ut = UserToken::find(self::TOKEN, UserToken::SCOPE_API);

        if (!$ut) {
            throw PortalException::create('Failed to find the Spendr legacy API caller.');
        }
        UserToken::updateUseTime($ut);
//        $debug = [];
        $debug = null;
        $options = [
            'headers' => ApiRunner::createSignatureHeaders($ut, $url, $method, $params, $debug),
        ];
        if ($params) {
            if ($originMethod === 'GET') {
                $options['query'] = $params;
            } else if ($originMethod === 'FORM') {
                $options['form_params'] = $params;
            } else if ($originMethod === 'JSON') {
                $options['json'] = $params;
            } else if ($originMethod === 'MULTIPART') {
                $body = [];
                foreach ($params as $key => $item) {
                    if (is_string($item) && is_file($item)) {
                        $body[] = [
                            'name' => 'file',
                            'contents' => fopen($item, 'r'),
                            'filename' => basename($item)
                        ];
                    } else {
                        $body[] = [
                            'name' => $key,
                            'contents' => $item,
                        ];
                    }
                }
                $options['multipart'] = $body;
            }
        }
//        if ($debug) {
//            $debug['method'] = $method;
//            $debug['url'] = $url;
//            $debug['options'] = $options;
//            Util::updateMeta($ei, [
//                'debug' => $debug,
//            ]);
//        }
        try {
            $response = $client->request($method, $url, $options);
        } catch (RequestException $exception) {
            $response = $exception->getResponse();
            $msg = $exception->getMessage();
            $rawContent = null;
            if ($response) {
                $body = $response->getBody();
                $rawContent = $body ? $body->getContents() : '{}';
                $content = Util::s2j($rawContent ?: '{}') ?? [];
                $msg = $content['message'] ??
                       $content['msg'] ??
                       $msg;
            }
            $ei->fail($rawContent, $msg)
                ->persist();
            throw PortalException::create('Next API error: ' . $msg);
        }

        $rawContent = $response->getBody()->getContents();
        if ($originMethod === 'GET' && starts_with($endpoint, '/api/resource/attachments')) {
            $content = new Response($rawContent, Response::HTTP_OK, $response->getHeaders());
        } else {
            $content = Util::s2j($rawContent) ?? [];
        }

        return [$ei, $content];
    }

    public function fuzzyMatchName(string $firstName, string $lastName, array $fullNames = [], $lastNameMinTreshold = 0.6)
    {
        $url = '/api/resource/util/fuzzy-match?';
        $url .= http_build_query(compact('firstName', 'lastName', 'lastNameMinTreshold'));
        foreach ($fullNames as $fn) {
            $url .= '&fullNames=' . urlencode($fn);
        }
        return ExternalInvoke::throwIfFailed(
            $this->request(
                'GET',
                $url,
            )
        );
    }

    public function payResetGroupAccess(string $access)
    {
        if (!$access) {
            return ExternalInvoke::tempFailedArray();
        }
        return ExternalInvoke::throwIfFailed(
            $this->request(
                'POST',
                '/api/resource/pay/merchant/reset-access?access=' . $access,
            )
        );
    }

    public function attachmentUpload($filePath, $targetFolder = 'public')
    {
        return ExternalInvoke::throwIfFailed(
            $this->request(
                'MULTIPART',
                '/api/resource/attachments',
                [
                    'folder' => $targetFolder,
                    'file' => $filePath
                ]
            )
        );
    }

    public function attachmentDownload(string $path)
    {
        $url = '/api/resource/attachments?';
        $url .= http_build_query(compact('path'));
        return ExternalInvoke::throwIfFailed(
            $this->request(
                'GET',
                $url,
            )
        );
    }
}
