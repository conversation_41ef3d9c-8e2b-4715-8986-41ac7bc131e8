<?php

namespace SpendrBundle\Services\Bank;

use CoreBundle\Utils\Util;
use SpendrBundle\Entity\SpendrTransaction;

class BankLedgerService
{
	public static function checkIfImportedBefore($type, $amountType = 'credit', $status = [], $batchId = null)
	{
		if (($type === 'ach_batch') && $batchId) {
			$descriptionLLC = ImportTransactionsService::DESC_SPENDR_LLC_ACH . ' ' . $batchId;
			$descriptionINC = ImportTransactionsService::DESC_SPENDR_INC_ACH . ' ' . $batchId;
		} else {
			return false;
		}

		if (($amountType !== 'credit') && ($amountType !== 'debit')) {
			return false;
		}

		if (!$status) {
			$status = [
				SpendrTransaction::STATUS_PENDING,
				SpendrTransaction::STATUS_COMPLETED
			];
		}

		$expr = Util::expr();
		$query = Util::em()->getRepository(SpendrTransaction::class)
			->createQueryBuilder('st')
			->where($expr->orX(
				$expr->eq('st.description', ':descLLC'),
				$expr->eq('st.description', ':descINC')
			))
			->setParameter('descLLC', $descriptionLLC)
			->setParameter('descINC', $descriptionINC)
			->andWhere(Util::expr()->in('st.status', ':statuses'))
			->setParameter('statuses', $status);
		if ($amountType === 'credit') {
			$query->andWhere('st.credit is not null');
		} else if ($amountType === 'debit') {
			$query->andWhere('st.debit is not null');
		}
		$records = $query->setMaxResults(1)
			->getQuery()
			->getResult();
		if (count($records)) {
			return true;
		}
		return false;
	}
}
