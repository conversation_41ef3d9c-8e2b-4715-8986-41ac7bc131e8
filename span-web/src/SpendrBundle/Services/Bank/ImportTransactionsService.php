<?php

namespace SpendrBundle\Services\Bank;

use Carbon\Carbon;
use CoreBundle\Entity\AchBatch;
use CoreBundle\Entity\AchTransactions;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserCardBalance;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Entity\UserCardTransaction;
use CoreBundle\Entity\UserFeeHistory;
use CoreBundle\Utils\Excel;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Queue;
use CoreBundle\Utils\Util;
use SalexUserBundle\Entity\User;
use SpendrBundle\Entity\SpendrTransaction;
use SpendrBundle\Services\FeeService;
use SpendrBundle\Services\LoadService;
use SpendrBundle\Services\SlackService;
use SpendrBundle\Services\UserService;
use SpendrBundle\SpendrBundle;

class ImportTransactionsService
{
//	CONST DESC_CREDIT_TO_PARTNER_BALANCE = [
//		"REFUND OF NOV SRV CHRG",
//		"REFUND OF DEC SRV CHRG",
//		"Spendr LLC spendr tes",
//		"RET ACH - UTL",
//	];

	CONST DESC_BANK_SERVICE_CHARGE = "SERVICE CHARGE"; // monthly account fee + wire fee + ACH return fee
	CONST DESC_BANK_SPENDR_ACH_FEE = "Spendr ACH Fees";
	CONST DESC_BANK_SPENDR_ACH_FEE_JULY_2022 = "ACH FEES July 2022";

	CONST DESC_DEBIT_FROM_PARTNER_BALANCE = [
		self::DESC_BANK_SERVICE_CHARGE,
		self::DESC_BANK_SPENDR_ACH_FEE,
//		"Spendr LLC test XXXXX8024",
//		"Spendr LLC spendr tes XXXXX8024",
	];

	CONST DESC_FILTER_OUTER = [
		'ACH Manager (Pending)',
		'SPENDR ACH 1',
	];

	const DESC_SPENDR_LLC = 'SPENDR LLC';
	const DESC_SPENDR_INC = 'SPENDR INC';

	const DESC_SPENDR_LLC_ACH = self::DESC_SPENDR_LLC . ' ACH';
	const DESC_SPENDR_INC_ACH = self::DESC_SPENDR_INC . ' ACH';


    public static function import($path)
    {
		$em = Util::em();
        $tz = Util::tzUTC();

        Log::debug('Bank ledger import: Start to read ' . $path);
        $excel = Excel::openExcel($path);
        $sheet = $excel->getActiveSheet();
        $rows = $sheet->toArray();
        $imported = [];
        $duplicated = [];
      	$errorRows = [];
		$loadRows = [];
		$unloadRows = [];
		$partnerLoadRows = [];
		$debitFromPartnerRows = [];
		$debitFromBankRows = [];
		$bankServiceChargeRows = [];
		$bankAchFeeRows = [];
		$invalidRows = [];
        $signatures = [];
        $now = new \DateTime();
        $user = Util::user();

        Log::debug('Bank ledger import: Start to iterate the records');
        $count = count($rows);
        foreach ($rows as $i => $row) {
            Log::debug('Bank ledger import: loop start' . $i . '/' . $count);
            if ($i <= 0) {
                continue;
            }
            $signature = sha1('spendr_' . implode('_', $row));
            if (isset($signatures[$signature])) {
                $duplicated[$i] = $row;
                continue;
            }
            $signatures[$signature] = true;

            $old = self::checkIfImportedBefore($signature);
            if ($old) {
                $duplicated[$i] = $row;
                continue;
            }

			$desc = trim($row[6]);
            if (in_array($desc, self::DESC_FILTER_OUTER)) {
				$invalidRows[$i] = $row;
            	continue;
			}

			$debit = Money::normalizeAmountOrNull($row[2]);
			$credit = Money::normalizeAmountOrNull($row[3]);

			$date = empty($row[5]) ? null : Carbon::parse($row[5], $tz);

			$hasAchLoads = false;
			$havePrefundLoads = false;
			$addPrefundBalanceStatus = '';
			$status = null;
            if (self::checkIfNeedProcessing($desc, $credit, $debit)) {
				if (self::checkAchBatchRow($desc, $credit, $debit)) {
					$status = SpendrTransaction::STATUS_PENDING;
					if ($credit) {
						$loadRows[$i] = $row;
						$hasAchLoads = true;

						if (self::hasPrefundLoads($desc, $credit)) {
							$havePrefundLoads = true;
							$addPrefundBalanceStatus = 'pending';
						}
					} else if ($debit) {
						$unloadRows[$i] = $row;
					}
				} else if (self::checkIfPartnerLoadDesc($desc) && $credit) {
					$status = SpendrTransaction::STATUS_PENDING;
					$partnerLoadRows[$i] = $row;
				} else if (
					self::checkBankServiceCharge($desc, $debit, $date)
					&& !self::checkIfServiceChargeImportedBefore($date)
				) {
					$status = SpendrTransaction::STATUS_PENDING;
					$bankServiceChargeRows[$i] = $row;
				} else if (
					self::checkBankAchLoadFee($desc, $debit, $date)
					&& !self::checkIfBankAchLoadFeeImportedBefore($date, $debit)
				) {
					$status = SpendrTransaction::STATUS_PENDING;
					$bankAchFeeRows[$i] = $row;
				}
			}

            $st = new SpendrTransaction();
            $st->setAccount($row[0])
                ->setChkRef($row[1])
                ->setDebit($debit)
                ->setCredit($credit)
                ->setBalance(Money::normalizeAmountOrNull($row[4]))
                ->setDate($date)
                ->setDescription($desc)
                ->setSignature($signature)
                ->setImportAt($now)
                ->setImportBy($user)
				->setStatus($status);

            if ($hasAchLoads) {
				$st->setMeta(Util::j2s([
					'havePrefundLoads' => $havePrefundLoads,
					'addPrefundBalanceStatus' => $addPrefundBalanceStatus,
				]));
			}

            $em->persist($st);
			$em->flush();
            $imported[$i] = $row;

            Log::debug('Bank ledger import: loop end ' . $i . '/' . $count);
        }
        $em->flush();
        Log::debug('Bank ledger import: Completed persisting the new records');

        return [
        	$imported,
			$duplicated,
			$errorRows,
			$invalidRows,
			$loadRows,
			$unloadRows,
			$partnerLoadRows,
			$bankServiceChargeRows,
			$bankAchFeeRows,
		];
    }

	public static function checkIfImportedBefore($signature)
	{
		$em = Util::em();
		$repo = $em->getRepository(SpendrTransaction::class);
		$query = $repo->createQueryBuilder('st')
			->where('st.signature = :signature')
			->setMaxResults(1);
		Util::disableSoftDeletable();
		$old = (clone $query)->setParameter('signature', $signature)
			->getQuery()
			->getOneOrNullResult();
		Util::enableSoftDeletable();
		return $old;
	}

	public static function checkIfNeedProcessing($description, $credit = null, $debit = null)
	{
		if (!$description) {
			return false;
		}

		if (!$credit && !$debit) {
			return false;
		}

		$partnerLoad = self::checkIfPartnerLoadDesc($description);
		$achBatch = self::getAchBatch($description);
		$debitFromPartner = in_array($description, self::DESC_DEBIT_FROM_PARTNER_BALANCE);

		if (
			($partnerLoad && $credit)
			|| (
				$achBatch
				&& ($credit || $debit)
			)
			|| ($debitFromPartner && $debit)
		) {
			return true;
		}

		return false;
	}

	// partner loads: the first part and the last part will always be the same. The middle #'s are bound to change
	public static function checkIfPartnerLoadDesc($description, $type = 'new')
	{
		$prefix = Util::startsWith($description, 'Wire In');
		$oldSuffix = Util::endsWith($description, self::DESC_SPENDR_LLC);
		$newSuffix = Util::endsWith($description, self::DESC_SPENDR_INC);
		if ($type === 'all') {
			return $prefix && ($oldSuffix || $newSuffix);
		} else {
			return $prefix && $newSuffix;
		}
	}

	public static function checkIfNeedDebitFromPartner($description)
	{
		if (!$description) {
			return false;
		}

		// temp for test
		// These we initial test. We need to credit  partner but will not see this again
		if (in_array($description, self::DESC_DEBIT_FROM_PARTNER_BALANCE)) {
			return true;
		}

		return false;
	}

	public static function checkBankServiceCharge($description, $debit, \DateTime $date)
	{
		if (!$description || !$debit || !$date) {
			return false;
		}

		if ($description !== self::DESC_BANK_SERVICE_CHARGE) {
			return false;
		}

		$year = (int)($date->format("Y"));
		if (
			($year < SpendrBundle::SYSTEM_LAUNCH_YEAR)
			|| ($date > Carbon::now())
		) {
			return false;
		}

		$remainingAmount = $debit;
		$importMonthlyAccountFee = 0;
		$importWireFee = 0;
		$importReturnedLoadFee = 0;

		$calculateMonthlyAccountFee = FeeService::calculate(FeeService::TO_BANK_SPENDR_MONTHLY_ACCOUNT_FEE);
		$calculateMonthlyAccountFee = (int)$calculateMonthlyAccountFee;
		if (($calculateMonthlyAccountFee <= 0) || ($debit < $calculateMonthlyAccountFee)) {
			return false;
		}

		$calculateWireFee = FeeService::calculateBankWireFee(
			Carbon::createFromTimestamp($date->getTimestamp())->startOfMonth(),
			Carbon::createFromTimestamp($date->getTimestamp())->endOfMonth(),
		);
		$calculateReturnedLoadFee = FeeService::calculateBankReturnedLoadFee(
			Carbon::createFromTimestamp($date->getTimestamp())->startOfMonth(),
			Carbon::createFromTimestamp($date->getTimestamp())->endOfMonth()
		);
		$calculatedTotalAmount = $calculateMonthlyAccountFee + $calculateWireFee + $calculateReturnedLoadFee;

		$importMonthlyAccountFee = $calculateMonthlyAccountFee;
		$remainingAmount -= $importMonthlyAccountFee;

		if ($calculateWireFee && ($remainingAmount > 0)) {
			if ($remainingAmount >= $calculateWireFee ) {
				$importWireFee = $calculateWireFee;
				$remainingAmount -= $importWireFee;
			}
		}

		if ($remainingAmount > 0) {
			$importReturnedLoadFee = $remainingAmount;
		}

		$diff = (int)$debit - (int)$calculatedTotalAmount;

		if ($diff !== 0) {
			$importAmountText = Money::format($debit, 'USD');
			$calculateAmountText = Money::format($calculatedTotalAmount, 'USD');
			SlackService::warning(
				'Bank ledger. ' . self::DESC_BANK_SERVICE_CHARGE . '. The imported value is not the same as '.
				'the value calculated by the system. Imported value: ' . $importAmountText .
				'. Calculated value: ' . $calculateAmountText,
				[
					'Imported value details' => [
						'Monthly account fee' => Money::format($importMonthlyAccountFee, 'USD'),
						'Wire fee' => Money::format($importWireFee, 'USD'),
						'Return fee' => Money::format($importReturnedLoadFee, 'USD'),
					],
					'Calculated value details' => [
						'Monthly account fee' => Money::format($calculateMonthlyAccountFee, 'USD'),
						'Wire fee' => Money::format($calculateWireFee, 'USD'),
						'Return fee' => Money::format($calculateReturnedLoadFee, 'USD'),
					]
				],
				[
					SlackService::MENTION_TRACY,
					SlackService::MENTION_SPENDR_LUCAS,
					SlackService::MENTION_SPENDR_AUSTIN_SCHNEIDER,
					SlackService::MENTION_SPENDR_JOEL
				]
			);
		}

		if ($debit >= $calculateMonthlyAccountFee) {
			return true;
		}

		return false;
	}

	public static function checkIfServiceChargeImportedBefore(\DateTime $date, SpendrTransaction $txn = null)
	{
		$em = Util::em();
		$repo = $em->getRepository(SpendrTransaction::class);
		$year = $date->format('Y');
		$month = $date->format('m');
		$query = $repo->createQueryBuilder('st')
			->where('Year(st.date) = :year')
			->setParameter('year', $year)
			->andWhere('Month(st.date) = :month')
			->setParameter('month', $month)
			->andWhere('st.description = :desc')
			->setParameter('desc', self::DESC_BANK_SERVICE_CHARGE)
			->andWhere(Util::expr()->in('st.status', ':statuses'))
			->setParameter('statuses', [
				SpendrTransaction::STATUS_PENDING,
				SpendrTransaction::STATUS_COMPLETED
			]);
		if ($txn) {
			$query->andWhere('st.id != :txnId')
				->setParameter('txnId', $txn->getId());
		}
		return $query->setMaxResults(1)
			->getQuery()
			->getOneOrNullResult();
	}

	public static function checkBankAchLoadFee($description, $debit, \DateTime $date)
	{
		if (!$description || !$debit || !$date) {
			return false;
		}

		if ($description !== self::DESC_BANK_SPENDR_ACH_FEE) {
			return false;
		}

		$systemOnlineYear = 2022;
		$year = (int)($date->format("Y"));
		if (
			($year < $systemOnlineYear)
			|| ($date > Carbon::now())
		) {
			return false;
		}

		// hard code: to bank setup fee, it's a one-time ACH setup fee
		if (
			$debit === 250000
			&& (int)($date->format('m')) === 5
			&& $year === $systemOnlineYear
		) {
			return true;
		}

		$calculatedAmount = FeeService::calculateBankLoadFee(
			Carbon::createFromTimestamp($date->getTimestamp())->startOfMonth(),
			Carbon::createFromTimestamp($date->getTimestamp())->endOfMonth()
		);

		$diff = $debit - $calculatedAmount;

		if ($diff !== 0) {
			$debitText = Money::format($debit, 'USD');
			$calculateText = Money::format($calculatedAmount, 'USD');
			SlackService::warning(
				'Bank ledger. ' . self::DESC_BANK_SPENDR_ACH_FEE . '. The imported value is not the same as '.
				'the value calculated by the system. Imported value: ' . $debitText .
				'. Calculated value: ' . $calculateText,
				null,
				[
					SlackService::MENTION_TRACY,
					SlackService::MENTION_SPENDR_LUCAS,
					SlackService::MENTION_SPENDR_AUSTIN_SCHNEIDER,
					SlackService::MENTION_SPENDR_JOEL
				]
			);
		}

		if ($debit >= 0) {
			return true;
		}

		return false;
	}

	public static function checkIfBankAchLoadFeeImportedBefore(\DateTime $date, $debit, SpendrTransaction $txn = null)
	{
		$em = Util::em();
		$year = $date->format('Y');
		$month = $date->format('m');
		$repo = $em->getRepository(SpendrTransaction::class);
		$query = $repo->createQueryBuilder('st')
			->where('Year(st.date) = :year')
			->setParameter('year', $year)
			->andWhere('Month(st.date) = :month')
			->setParameter('month', $month)
			->andWhere('st.description = :desc')
			->setParameter('desc', self::DESC_BANK_SPENDR_ACH_FEE)
//			->andWhere('st.debit = :debit')
//			->setParameter('debit', $debit)
			->andWhere(Util::expr()->in('st.status', ':statuses'))
			->setParameter('statuses', [
				SpendrTransaction::STATUS_PENDING,
				SpendrTransaction::STATUS_COMPLETED
			]);
		if ($txn) {
			$query->andWhere('st.id != :txnId')
				->setParameter('txnId', $txn->getId());
		}
		return $query->getQuery()
			->getOneOrNullResult();
	}

	// Because here it is not known which order failed, so if it is not equal, the import is not allowed
	public static function compareAchBatchTxnAmount($description, $txnType = 'load', $amount = null)
	{
		if (!$description || !$amount) {
			return false;
		}

		if ($txnType !== 'load' && $txnType !== 'unload') {
			return false;
		}

		$achBatch = self::getAchBatch($description);

		if ($achBatch) {
			$txnAmount = self::getAchBatchTxnData($description, 'amount', $txnType);
			if ($txnAmount === $amount) {
				return true;
			}
		}

		return false;
	}

	public static function checkAchBatchRow($description, $credit, $debit)
	{
		if (!$description) {
			return false;
		}

		if ((!$credit && !$debit) || ($credit && $debit)) {
			return false;
		}

		$achBatch = self::getAchBatch($description);
		if (!$achBatch) {
			return false;
		}

		if (self::checkAchBatchIfImportedBefore($description, $credit, $debit)) {
			return false;
		}

		if ($credit) {
			if (!self::compareAchBatchTxnAmount($description, 'load', $credit)) {
				return false;
			}
		}

		if ($debit) {
			if (!self::compareAchBatchTxnAmount($description, 'unload', $debit)) {
				return false;
			}
		}

		return true;
	}

	public static function checkAchBatchIfImportedBefore($description, $credit = null, $debit = null)
	{
		if (!$description) {
			return false;
		}
		if (!$credit && !$debit) {
			return false;
		}
		$achBatch = self::getAchBatch($description);
    	if ($achBatch) {
			$batchId = $achBatch->getId();
			if ($credit) {
				return self::getBankLedgerRecord($description, 'achBatch', $credit, null, $batchId);
			} else if($debit) {
				return self::getBankLedgerRecord($description, 'achBatch', null, $debit, $batchId);
			}
		}
    	return false;
	}

	public static function getBankLedgerRecord($description, $type, $credit = null, $debit = null, $batchId = null)
	{
		if (!$description) {
			return false;
		}
		if (!$credit && !$debit) {
			return false;
		}
		if ($credit && $debit) {
			return false;
		}
		if ($type === 'achBatch' && !$batchId) {
			return false;
		}
		
		$query = Util::em()->getRepository(SpendrTransaction::class)
			->createQueryBuilder('st')
			->where(Util::expr()->in('st.status', ':statuses'))
			->setParameter('statuses', [
				SpendrTransaction::STATUS_PENDING,
				SpendrTransaction::STATUS_COMPLETED
			]);

		$expr = Util::expr();
		if ($type === 'achBatch' && $batchId) {
			$query->andWhere(
				$expr->orX(
					$expr->eq('st.description', ':descLLC'),
					$expr->eq('st.description', ':descINC')
				)
			)->setParameter('descLLC', self::DESC_SPENDR_LLC_ACH . ' ' . $batchId)
			->setParameter('descINC', self::DESC_SPENDR_INC_ACH . ' ' . $batchId);
		}
		if ($credit) {
			$query->andWhere('st.credit is not null');
		} else if ($debit) {
			$query->andWhere('st.debit is not null');
		}
		$records = $query->setMaxResults(1)
			->getQuery()
			->getResult();
		if (count($records)) {
			return true;
		}
		return false;
	}

	public static function hasPrefundLoads($description, $credit)
	{
		$achBatch = self::getAchBatch($description);
		if (!$achBatch || !$credit) {
			return false;
		}

		$prefundAmount = self::getAchBatchTxnData($description, 'amount', 'load', 'prefund');
		if ($prefundAmount > 0) {
			return true;
		}

		return false;
	}

	public static function beforeHandleRow(SpendrTransaction $t, $type = null)
	{
		$credit = (int)($t->getCredit());
		$debit = (int)($t->getDebit());
		if (in_array($type, ['load', 'partnerLoad'])) {
			if ($credit <= 0) {
				return 'Invalid amount.';
			}
		}

		if (in_array($type, ['unload'])) {
			if ($debit <= 0) {
				return 'Invalid amount.';
			}
		}

		// check if processed before
		$partOfComment = 'imported spendr transaction ' . $t->getSignature();
		$ucbs = self::checkIfHandledBefore($partOfComment);

		if ($ucbs) {
			return 'The transaction has already been processed.';
		}

		if (in_array($type, ['load', 'unload'])) {
			$description = $t->getDescription();
			$achBatch = self::getAchBatch($description);
			if (!$achBatch) {
				return 'Invalid batch record.';
			}
		}

		$dummy = UserService::getSpendrBalanceDummyCard('spendr');
		if (!$dummy) {
			return 'Invalid partner dummy card.';
		}

		return true;
	}

	public static function handleLoadRows()
	{
		$dummy = UserService::getSpendrBalanceDummyCard('spendr');
		if (!$dummy) {
			return null;
		}

		$ts = self::getSpendrTransactions(
			'load',
			SpendrTransaction::STATUS_PENDING
		);
		if (!$ts) {
			return null;
		}

		$totalAmount = 0;
		/** @var SpendrTransaction $t */
		foreach ($ts as $t) {
			$checkRes = self::beforeHandleLoadRow($t);
			if (is_string($checkRes)) {
				SlackService::warning('Bank ledger - check before handle load row: ' . $checkRes . '.', [
					'Bank Ledger Id' => $t->getId(),
					'Status' => $t->getStatus(),
				]);
				continue;
			}

			Queue::spendrProcessBankLedger($t->getId(), 'load');

			$credit = (int)($t->getCredit());
			$prefundAmount = self::calculatePrefundAmount($t);
			// todo: Make sure that the credit amount of the bank ledger is the same as the batch value
			$amount = $credit - (int)$prefundAmount;
			$totalAmount += $amount;
		}

		return $totalAmount;
	}

	public static function beforeHandleLoadRow(SpendrTransaction $t)
	{
		return self::beforeHandleRow($t, 'load');
	}

	private static function calculatePrefundAmount(SpendrTransaction $t)
	{
		$description = $t->getDescription();

		$prefundAmount = 0;
		if (
			Util::meta($t, 'havePrefundLoads')
			&& Util::meta($t, 'addPrefundBalanceStatus') === 'pending'
		) {
			$prefundAmount = self::getAchBatchTxnData(
				$description,
				'amount',
				'load',
				'prefund'
			);
		}
		return $prefundAmount;
	}

	public static function handleLoadRow(SpendrTransaction $t)
	{
		$credit = (int)($t->getCredit());

		$prefundAmount = self::calculatePrefundAmount($t);
		if ($prefundAmount) {
			Util::updateMeta($t, [
				'prefundLoadAmount' => $prefundAmount
			]);
		}

		Queue::spendrBankLedgerAddBatchAmountToPartner($t->getId());


		$description = $t->getDescription();
		self::updateInstantLoadOrUnloadStatus($description, 'load');

		$t->setStatus(SpendrTransaction::STATUS_COMPLETED);
		Util::em()->persist($t);
		Util::flush();
	}

	public static function addBatchAmountToPartner(SpendrTransaction $t)
	{
		$credit = (int)($t->getCredit());

		$dummy = UserService::getSpendrBalanceDummyCard('spendr');

		$prefundAmount = Util::meta($t, 'prefundLoadAmount');

		// todo: Make sure that the credit amount of the bank ledger is the same as the batch value
		$amount = $credit - (int)$prefundAmount;

		$totalText = $prefundAmount ?
			' Total Amount: ' . Money::format($credit, 'USD') : null;
		$prefundText = $prefundAmount ?
			', Prefund Amount: ' . Money::format($prefundAmount, 'USD') : null;
		$text = $totalText . $prefundText;

		$comment = sprintf(
			'Bank ledger. Add %s to partner balance through imported spendr transaction %s at %s.' . $text,
			Money::format($amount, 'USD'),
			$t->getSignature(),
			Carbon::now(),
		);
		$dummy->updatePrivacyBalanceBy($amount, UserCardBalance::TYPE_IMPORT, $comment, false, $t);

		SlackService::tada($comment);
	}

	public static function handleUnloadRows()
	{
		$ts = self::getSpendrTransactions(
			'unload',
			SpendrTransaction::STATUS_PENDING
		);
		if (!$ts) {
			return null;
		}

		$totalAmount = 0;
		/** @var SpendrTransaction $t */
		foreach ($ts as $t) {
			$checkRes = self::beforeHandleUnloadRow($t);
			if (is_string($checkRes)) {
				SlackService::warning('Bank ledger - check before handle unload row: ' . $checkRes . '.', [
					'Bank Ledger Id' => $t->getId(),
					'Status' => $t->getStatus(),
				]);
				continue;
			}

			Queue::spendrProcessBankLedger($t->getId(), 'unload');

			$amount = (int)($t->getDebit());
			$totalAmount += $amount;
		}

		return $totalAmount;
	}

	public static function beforeHandleUnloadRow(SpendrTransaction $t)
	{
		return self::beforeHandleRow($t, 'unload');
	}

	public static function handleUnloadRow(SpendrTransaction $t)
	{
		$description = $t->getDescription();
		$achBatch = self::getAchBatch($description);

		self::updateInstantLoadOrUnloadStatus($description, 'unload');
		$t->setStatus(SpendrTransaction::STATUS_COMPLETED);
		Util::em()->persist($t);
		Util::flush();

		$comment = sprintf(
			'Bank ledger. Executed unload transaction with batch ID %s through imported spendr transaction %s at %s.',
			$achBatch->getId(),
			$t->getSignature(),
			Carbon::now(),
		);
		SlackService::tada($comment);
	}

	public static function handlePartnerLoadRows()
	{
		$dummy = UserService::getSpendrBalanceDummyCard('spendr');
		if (!$dummy) {
			return null;
		}

		$ts = self::getSpendrTransactions(
			'partner_load',
			SpendrTransaction::STATUS_PENDING
		);
		if (!$ts) {
			return null;
		}

		$totalAmount = 0;
		/** @var SpendrTransaction $t */
		foreach ($ts as $t) {
			$checkRes = self::beforeHandlePartnerLoadRow($t);
			if (is_string($checkRes)) {
				SlackService::warning('Bank ledger - check before handle partner load row: ' . $checkRes . '.', [
					'Bank Ledger Id' => $t->getId(),
					'Status' => $t->getStatus(),
				]);
				continue;
			}

			Queue::spendrProcessBankLedger($t->getId(), 'partnerLoad');

			$amount = (int)($t->getCredit());
			$totalAmount += $amount;
		}

		return $totalAmount;
	}

	public static function beforeHandlePartnerLoadRow(SpendrTransaction $t)
	{
		return self::beforeHandleRow($t, 'partnerLoad');
	}

	public static function handlePartnerLoadRow(SpendrTransaction $t)
	{
		$dummy = UserService::getSpendrBalanceDummyCard('spendr');
		$amount = (int)($t->getCredit());
		$comment = sprintf(
			'Bank ledger. Add %s to partner balance through imported spendr transaction %s at %s.',
			Money::format($amount, 'USD'),
			$t->getSignature(),
			Carbon::now(),
		);
		$dummy->updatePrivacyBalanceBy($amount, UserCardBalance::TYPE_IMPORT, $comment, false, $t);

		$t->setStatus(SpendrTransaction::STATUS_COMPLETED);
		Util::em()->persist($t);
		Util::flush();

		SlackService::tada($comment);
	}

	public static function handleBankServiceChargeRows()
	{
		$dummy = UserService::getSpendrBalanceDummyCard('spendr');
		if (!$dummy) {
			return null;
		}

		$ts = self::getSpendrTransactions(
			'bank_service_charge',
			SpendrTransaction::STATUS_PENDING
		);
		if (!$ts) {
			return null;
		}

		$totalAmount = 0;
		/** @var SpendrTransaction $t */
		foreach ($ts as $t) {
			$checkRes = self::beforeHandleBankServiceChargeRow($t);
			if (is_string($checkRes)) {
				SlackService::warning('Bank ledger - check before handle bank service charge row: ' . $checkRes . '.', [
					'Bank Ledger Id' => $t->getId(),
					'Status' => $t->getStatus(),
				]);
				continue;
			}

			Queue::spendrProcessBankLedger($t->getId(), 'bankServiceCharge');

			$debit = (int)($t->getDebit());
			$totalAmount += $debit;
		}

		return $totalAmount;
	}

	public static function beforeHandleBankServiceChargeRow(SpendrTransaction $t)
	{
		$debit = (int)($t->getDebit());
		if ($debit <= 0) {
			return 'Invalid amount.';
		}

		// check if process before
		$partOfComment = 'imported spendr transaction ' . $t->getSignature();
		$ucbs = self::checkIfHandledBefore($partOfComment);

		if ($ucbs && !Util::meta($ucbs[0], 'oldData')) {
			return 'The transaction has already been processed(userCardBalance).';
		}

		$description = $t->getDescription();
		if (!self::checkBankServiceCharge($description, $debit, $t->getDate())) {
			return 'Check failed.';
		}

		if (self::checkIfServiceChargeImportedBefore($t->getDate(), $t)) {
			return 'The transaction has been imported before.';
		}

		$dummy = UserService::getSpendrBalanceDummyCard('spendr');
		$feeName = FeeService::TO_BANK_SPENDR_MONTHLY_ACCOUNT_FEE;
		$spendrAdmin = $dummy->getUser();
		if (FeeService::hasFeeHistory($spendrAdmin, $feeName, $t)) {
			return 'The transaction has already been processed(feeHistory).';
		}

		if ($t->getStatus() !== SpendrTransaction::STATUS_PENDING) {
			return 'The current status cannot be processed.';
		}

		if (!$dummy) {
			return 'Invalid partner dummy card.';
		}

		return true;
	}

	public static function handleBankServiceChargeRow(SpendrTransaction $t)
	{
		$debit = (int)($t->getDebit());
		$feeName = FeeService::TO_BANK_SPENDR_MONTHLY_ACCOUNT_FEE;
		$dummy = UserService::getSpendrBalanceDummyCard('spendr');
		$spendrAdmin = $dummy->getUser();

		$monthlyAccountFee = FeeService::calculate($feeName);

		$comment = sprintf(
			'Bank ledger. Deduct %s from partner balance through imported spendr transaction %s at %s.',
			Money::format($debit, 'USD'),
			$t->getSignature(),
			Carbon::now(),
		);
		$dummy->updatePrivacyBalanceBy(-$debit, UserCardBalance::TYPE_IMPORT, $comment, false, $t);

		$t->setStatus(SpendrTransaction::STATUS_COMPLETED);
		Util::em()->persist($t);

		SlackService::tada($comment);

		self::createBankFeeHistoryRecord(FeeService::TO_BANK_SPENDR_MONTHLY_ACCOUNT_FEE, $monthlyAccountFee, $t, $spendrAdmin);

		$calculatedWireFee = FeeService::calculateBankWireFee(
			Carbon::createFromTimestamp($t->getDate()->getTimestamp())->startOfMonth(),
			Carbon::createFromTimestamp($t->getDate()->getTimestamp())->endOfMonth()
		);

		if (
			($calculatedWireFee > 0)
			&& (($debit - $monthlyAccountFee - $calculatedWireFee) >= 0)
		) {
			self::createBankFeeHistoryRecord(FeeService::TO_BANK_SPENDR_WIRE_FEE, $calculatedWireFee, $t, $spendrAdmin);
		}
		if (($debit - $monthlyAccountFee - $calculatedWireFee) > 0) {
			$returnFee = $debit - $monthlyAccountFee - $calculatedWireFee;
			self::createBankFeeHistoryRecord(FeeService::TO_BANK_SPENDR_ACH_RETURN_FEE, $returnFee, $t, $spendrAdmin);
		}
		Util::flush();
	}

	private static function createBankFeeHistoryRecord($feeName, $fee, SpendrTransaction $t, User $spendrAdmin)
	{
		$comment = sprintf(
			'Charge "%s" to partner balance through imported spendr transaction %s at %s. Amount: %s',
			strtolower($feeName),
			$t->getSignature(),
			Carbon::now(),
			Money::format($fee, 'USD')
		);
		UserFeeHistory::create($spendrAdmin, $fee, $feeName, $t, $comment, Platform::spendr());
		SlackService::tada($comment);
	}

	public static function handleBankAchLoadFeeRows()
	{
		$dummy = UserService::getSpendrBalanceDummyCard('spendr');
		if (!$dummy) {
			return null;
		}

		$ts = self::getSpendrTransactions(
			'bank_ach_load_fee',
			SpendrTransaction::STATUS_PENDING
		);
		if (!$ts) {
			return null;
		}

		$totalAmount = 0;
		/** @var SpendrTransaction $t */
		foreach ($ts as $t) {
			$checkRes = self::beforeHandleBankAchLoadFeeRow($t);
			if (is_string($checkRes)) {
				SlackService::warning('Bank ledger - check before handle ach load fee row: ' . $checkRes . '.', [
					'Bank Ledger Id' => $t->getId(),
					'Status' => $t->getStatus(),
				]);
				continue;
			}

			Queue::spendrProcessBankLedger($t->getId(), 'bankAchLoadFee');

			$debit = (int)($t->getDebit());
			$totalAmount += $debit;
		}

		return $totalAmount;
	}

	public static function beforeHandleBankAchLoadFeeRow(SpendrTransaction $t)
	{
		$debit = (int)($t->getDebit());
		if($debit <= 0) {
			return 'Invalid amount.';
		}

		$dummy = UserService::getSpendrBalanceDummyCard('spendr');
		if (!$dummy) {
			return null;
		}

		// check if process before
		$partOfComment = 'imported spendr transaction ' . $t->getSignature();
		$ucbs = self::checkIfHandledBefore($partOfComment);

		if($ucbs) {
			return 'The transaction has already been processed(userCardBalance).';
		}

		$description = $t->getDescription();
		if (!self::checkBankAchLoadFee($description, $debit, $t->getDate())) {
			return 'Check failed.';
		}

		$recordHistory = self::checkIfBankAchLoadFeeImportedBefore($t->getDate(), $t->getDebit(), $t);
		if ($recordHistory) {
			// hard code for correct data
			if (Util::isLive() && (int)($t->getId()) === 190) {
				if (Util::meta($recordHistory, 'achSetupFee')) {
					return 'The transaction has been imported before.';
				}
			} else {
				if (!Util::meta($recordHistory, 'achSetupFee')) {
					return 'The transaction has been imported before.';
				}
			}
		}

		$feeName = FeeService::TO_BANK_CONSUMER_LOAD_FEE;

		if (Util::meta($t, 'achSetupFee')) {
			$feeName = FeeService::TO_BANK_SPENDR_ACH_SETUP_FEE;
			$ufhs = Util::em()->getRepository(UserFeeHistory::class)
				->createQueryBuilder('ufh')
				->andWhere(Util::expr()->eq('ufh.feeName', ':feeName'))
				->setParameter('feeName', $feeName)
				->andWhere(Util::expr()->like('ufh.meta', ':spendrMeta'))
				->setParameter('spendrMeta',  '%"spendrMeta":true%')
				->getQuery()
				->getResult();
			if (count($ufhs) > 0) {
				return 'The transaction has already been processed(feeHistory).';
			}
		}

		$spendrAdmin = $dummy->getUser();
		if (FeeService::hasFeeHistory($spendrAdmin, $feeName, $t)) {
			return 'The transaction has already been processed(feeHistory).';
		}

		return true;
	}

	public static function handleBankAchLoadFeeRow(SpendrTransaction $t)
	{
		$debit = (int)($t->getDebit());
		$feeName = FeeService::TO_BANK_CONSUMER_LOAD_FEE;
		$dummy = UserService::getSpendrBalanceDummyCard('spendr');
		$spendrAdmin = $dummy->getUser();

		$comment = sprintf(
			'Bank ledger. %s. Deduct %s from partner balance through imported spendr transaction %s at %s.',
			$feeName,
			Money::format($debit, 'USD'),
			$t->getSignature(),
			Carbon::now(),
		);
		$dummy->updatePrivacyBalanceBy(-$debit, UserCardBalance::TYPE_IMPORT, $comment, false, $t);

		$t->setStatus(SpendrTransaction::STATUS_COMPLETED);
		Util::em()->persist($t);

		self::createBankFeeHistoryRecord($feeName, $debit, $t, $spendrAdmin);
		Util::flush();
	}

	// the description such as "SPENDR LLC ACH 291"
	public static function getAchBatch($description)
	{
		$descLLC = Util::startsWith($description, self::DESC_SPENDR_LLC_ACH);
		$descINC = Util::startsWith($description, self::DESC_SPENDR_INC_ACH);
		if (!$descLLC && !$descINC) {
			return null;
		}

		if ($descLLC) {
			$batchId = trim(substr($description, strlen(self::DESC_SPENDR_LLC_ACH)));
			if (!$batchId || !is_numeric($batchId)) {
				return null;
			}
		}

		if ($descINC) {
			$batchId = trim(substr($description, strlen(self::DESC_SPENDR_INC_ACH)));
			if (!$batchId || !is_numeric($batchId)) {
				return null;
			}
		}

		/** @var AchBatch $achBatch */
		$achBatch = Util::em()->getRepository(AchBatch::class)
			->findOneBy([
				'id' => $batchId,
				'program' => AchBatch::ACH_BATCH_PROGRAM_SPENDR,
			]);
		if (
			!$achBatch
			|| (
				$achBatch
				&& ($achBatch->getBatchStatus() === UserCardTransaction::STATUS_LL_CANCELED)
			)
		) {
			return null;
		}

		return $achBatch;
	}

	public static function getUctIdsQuery()
	{
		return Util::em()->getRepository(AchTransactions::class)
			->createQueryBuilder('acht')
			->where(Util::expr()->eq('acht.batchId', ':batchId'))
			->select('acht.tranId')
			->distinct()
			->getDQL();
	}

	public static function getAchBatchTxnData($description, $dataType = 'amount', $txnType = 'all', $loadType = 'all')
	{
		if ($dataType !== 'amount' && $dataType !== 'list') {
			return null;
		}
		if ($txnType !== 'all' && $txnType !== 'load' && $txnType !== 'unload') {
			return null;
		}
		if ($loadType !== 'all' && $loadType !== 'instant' && $loadType !== 'prefund') {
			return null;
		}

		$achBatch = self::getAchBatch($description);
		if (!$achBatch) {
			return null;
		}

		$expr = Util::expr();

		$subQuery = Util::em()->getRepository(AchTransactions::class)
			->createQueryBuilder('acht')
			->where($expr->eq('acht.batchId', ':batchId'))
			->select('acht.tranId')
			->distinct();

		$meta = null;
		if ($loadType === 'instant') {
			$meta = LoadService::LOAD_TYPE_INSTANT;
		} else if ($loadType === 'prefund') {
			$meta = LoadService::LOAD_TYPE_PREFUND;
		}

		$type = null;
		$roles = [
			Role::ROLE_SPENDR_CONSUMER,
			Role::ROLE_SPENDR_PROGRAM_OWNER
		];
		if ($txnType === 'load') {
			$type = UserCardLoad::TYPE_LOAD_CARD;
		} else if ($txnType === 'unload') {
			$type = UserCardLoad::TYPE_UNLOAD;
			$roles = array_merge(
				$roles,
				SpendrBundle::getMerchantBalanceAdminRoles()
			);
		}

		$query = Util::em()->getRepository(UserCardLoad::class)
			->createQueryBuilder('ucl')
			->join('ucl.userCard', 'uc')
			->join('uc.user', 'u')
			->join('u.teams', 't')
			->join('uc.card', 'c')
			->join('c.cardProgram', 'cp')
			->where($expr->eq('ucl.transactionNo', $expr->any($subQuery->getDQL())))
			->andWhere($expr->in('t.name', ':roles'))
			->setParameter('roles', $roles)
			->andWhere($expr->eq('cp.name',':name'))
			->setParameter('name', CardProgram::NAME_SPENDR)
			->setParameter('batchId', $achBatch->getId());

		if ($meta) {
			$query->andWhere('ucl.loadType = :loadType')
				->setParameter('loadType', $meta);
		}

		if ($type) {
			$query->andWhere('ucl.type = :type')
				->setParameter('type', $type);
		}

		if ($dataType === 'amount') {
			$amount = (clone $query)->select('sum(ucl.initialAmount)')
				->getQuery()
				->getSingleScalarResult();
			Log::debug('prefund amount: ', [$amount]);
			return (int)$amount;
		} else if ($dataType === 'list') {
			return (clone $query)->getQuery()->getResult();
		}

		return null;
	}

	/**
	 * Include all ach load
	 *
	 * todo: Here it is possible to optimize the code to avoid too many transactions in a batch file in the future
	 */
	public static function updateInstantLoadOrUnloadStatus($description, $txnType)
	{
		if ($txnType !== 'load' && $txnType !== 'unload') {
			return null;
		}
		$achBatch = self::getAchBatch($description);
		if (!$achBatch) {
			return null;
		}
		$uctIdsQuery = self::getUctIdsQuery();
		$expr = Util::expr();

		$types = [];
		if ($txnType === 'load') {
			$types = [UserCardLoad::TYPE_LOAD_CARD];
		} else if ($txnType === 'unload') {
			$types = [UserCardLoad::TYPE_UNLOAD];
		}

		$loads = Util::em()->getRepository(UserCardLoad::class)
			->createQueryBuilder('ucl')
			->join('ucl.userCard', 'uc')
			->join('uc.user', 'u')
			->join('u.teams', 't')
			->join('uc.card', 'c')
			->join('c.cardProgram', 'cp')
			->where($expr->eq('ucl.transactionNo', $expr->any($uctIdsQuery)))
//			->andWhere($expr->notLike('ucl.meta', ':meta'))
//			->setParameter('meta', '%"' . LoadService::LOAD_TYPE_PREFUND . '":true%')
			->andWhere($expr->in('t.name', ':roles'))
			->setParameter('roles', array_merge(
				SpendrBundle::getConsumerRoles(),
				SpendrBundle::getMerchantBalanceAdminRoles(),
				[Role::ROLE_SPENDR_PROGRAM_OWNER]
			))
			->andWhere(Util::expr()->eq('cp.name',':name'))
			->setParameter('name', CardProgram::NAME_SPENDR)
			->andWhere($expr->in('ucl.type', ':types'))
			->setParameter('types', $types)
			->setParameter('batchId', $achBatch->getId())
			->getQuery()
			->getResult();

		if (!$loads) {
			return null;
		}

		/** @var UserCardLoad $load */
		foreach ($loads as $load) {
			//use queue: Because multiple functions or queues may change status
			Queue::spendrSettleLoad($load->getId());
		}

		return true;
	}

//	/**
//	 * @param SpendrTransaction $st
//	 * @return SpendrTransaction
//	 * @throws \Throwable
//	 */
//	public static function rollbackBalance(SpendrTransaction $st)
//	{
//		if (!$st) {
//			return null;
//		}
//
//		$status = $st->getStatus();
//
//		if (!$status || ($status && $status !== SpendrTransaction::STATUS_COMPLETED)) {
//			return null;
//		}
//
//		$ucbs = self::checkIfHandledBefore('Rollback spendr transaction ' . $st->getSignature());
//		if ($ucbs) {
//			return null;
//		}
//
//		$credit = (int)($st->getCredit());
//		$debit = (int)($st->getDebit());
//		$desc = $st->getDescription();
//		$amount = 0;
//		$role = null;
//		if ($credit && self::checkIfNeedCreditToPartner($desc)) {
//			$amount = $credit;
//			$role = 'spendr';
//		}
//		if ($debit && self::checkIfNeedDebitFromPartner($desc)) {
//			$amount = $debit;
//			$role = 'spendr';
//		}
//		if ($debit && self::checkIfNeedDebitFromBank($desc)) {
//			$amount = $debit;
//			$role = 'bank';
//		}
//
//		if ($amount > 0) {
//			$dummy = UserService::getSpendrBalanceDummyCard($role);
//			if ($dummy) {
//				if ($credit) {
//					$amount = -$amount;
//				}
//				$comment = sprintf(
//					'Rollback spendr transaction %s, add %s to %s balance at %s.',
//					$st->getSignature(),
//					Money::format($amount, 'USD'),
//					$role === 'spendr' ? 'partner' : $role,
//					Carbon::now(),
//				);
//				$dummy->updatePrivacyBalanceBy($amount, UserCardBalance::TYPE_IMPORT, $comment, false, $st);
//
//				SlackService::tada($comment);
//			}
//		}
//
//		return $st;
//	}

	public static function getSpendrTransactions($type, $status)
	{
		if (!$type || !$status) {
			return null;
		}
		if (!in_array($type, [
			'load',
			'unload',
			'partner_load',
			'debit_from_bank',
			'bank_service_charge',
			'bank_ach_load_fee',
		])) {
			return null;
		}

		$expr = Util::expr();
		$query = Util::em()->getRepository(SpendrTransaction::class)
			->createQueryBuilder('st')
			->where('st.status = :status')
			->setParameter('status', $status);

		if ($type === 'load' || $type === 'unload') {
			if ($type === 'load') {
				$query->andWhere('st.credit > 0');
			} else if ($type === 'unload') {
				$query->andWhere('st.debit > 0');
			}
			$query->andWhere($expr->orX(
				$expr->like('st.description', ':descLLC'),
				$expr->like('st.description', ':descINC')
			))->setParameter('descLLC', self::DESC_SPENDR_LLC_ACH . '%')
			->setParameter('descINC', self::DESC_SPENDR_INC_ACH . '%');
		} else if ($type === 'partner_load') {
			$query->andWhere('st.credit > 0')
				->andWhere($expr->like('st.description', ':desc'),)
				// ->setParameter('desc', 'Wire In%' . self::DESC_SPENDR_LLC);
				->setParameter('desc', 'Wire In%' . self::DESC_SPENDR_INC);
		} else if ($type === 'bank_service_charge') {
			$query->andWhere('st.debit > 0')
				->andWhere($expr->eq('st.description', ':desc'))
				->setParameter('desc', self::DESC_BANK_SERVICE_CHARGE);
		} else if ($type === 'bank_ach_load_fee') {
			$query->andWhere('st.debit > 0')
				->andWhere($expr->eq('st.description', ':desc'))
				->setParameter('desc', self::DESC_BANK_SPENDR_ACH_FEE);
		}

		return $query->getQuery()
			->getResult();
	}

	// todo: repeat
	public static function checkIfHandledBefore($partOfComment)
	{
		return Util::em()->getRepository(UserCardBalance::class)
			->createQueryBuilder('ucb')
			->join('ucb.userCard', 'uc')
			->join('uc.card', 'cpct')
            ->where(Util::expr()->eq('cpct.cardProgram',':cardProgram'))
            ->setParameter('cardProgram', CardProgram::spendr())
			->andWhere(Util::expr()->like('ucb.comment', ':comment'))
			->setParameter('comment', '%' . $partOfComment . '%')
			->orderBy('ucb.createdAt', 'desc')
			->setMaxResults(1)
			->getQuery()
			->getResult();
	}
}
