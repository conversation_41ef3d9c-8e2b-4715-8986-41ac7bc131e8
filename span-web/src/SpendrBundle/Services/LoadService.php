<?php

namespace SpendrBundle\Services;

use ApiBundle\Entity\Coin;
use Carbon\Carbon;
use ClfBundle\Entity\Transaction;
use ClfB<PERSON>le\Entity\TransactionStatus;
use CoreBundle\Entity\AchBatch;
use CoreBundle\Entity\AchTransactions;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\EntitySignature;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserCardBalance;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Entity\UserCardTransaction;
use CoreBundle\Entity\UserFeeHistory;
use CoreBundle\Exception\FailedException;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Queue;
use CoreBundle\Utils\Service;
use CoreBundle\Utils\Util;
use Ramsey\Uuid\Uuid;
use SalexUserBundle\Entity\User;
use SalexUserBundle\Entity\UserConfig;
use SpendrBundle\Controller\Mobile\Consumer\DepositControllerTrait;
use SpendrBundle\Entity\Location;
use SpendrBundle\Entity\PromotionCode;
use SpendrBundle\Entity\SpendrAutoWithdrawalHistory;
use SpendrBundle\Entity\SpendrMerchant;
use SpendrBundle\Entity\SpendrReferConfig;
use SpendrBundle\Entity\SpendrReferral;
use SpendrBundle\Entity\SpendrReward;
use SpendrBundle\Services\ACH\SpendrACHService;
use SpendrBundle\Services\ACH\SpendrWebhookService;
use SpendrBundle\Services\MQTT\PublishService;
use SpendrBundle\SpendrBundle;
use Symfony\Component\HttpFoundation\Request;
use UsUnlockedBundle\Services\Privacy\PrivacyAPI;

class LoadService
{
	use DepositControllerTrait;

	public const LOAD_TYPE_INSTANT = 'instantLoad';
	public const LOAD_TYPE_PREFUND = 'prefundLoad';
	public const LOAD_TYPE_PROMOTION = 'promotionLoad';
	public const UNLOAD_TYPE_PROMOTION = 'promotionUnload';// Cancel from promotion load
	public const LOAD_TYPE_ROLLBACK = 'spendrRollbackLoad'; // In general, it is used to roll back transactions that have been batched
	public const LOAD_TYPE_MANUALLY = 'manuallyLoad';

	public const SPLIT_LOAD_TYPE_MANUAL_LINK = 'manual_link';

	public const LOAD_STATUS_ERROR_MSG_BEFORE_TXN = 'Your current deposit status is not Instant and payment cannot be made.';

	public static function isLoad(UserCardLoad $load)
	{
		return $load->getType() === UserCardLoad::TYPE_LOAD_CARD;
	}

	public static function isUnload(UserCardLoad $load)
	{
		return $load->getType() === UserCardLoad::TYPE_UNLOAD;
	}

	public static function isInstantLoad(UserCardLoad $load)
	{
		return self::isLoad($load)
			&& Util::meta($load, self::LOAD_TYPE_INSTANT);
	}

	public static function isPrefundLoad(UserCardLoad $load)
	{
		return self::isLoad($load)
			&& Util::meta($load, self::LOAD_TYPE_PREFUND);
	}

	public static function isPromotionLoad(UserCardLoad $load)
	{
		return self::isLoad($load)
			&& Util::meta($load, self::LOAD_TYPE_PROMOTION);
	}

	public static function isManuallyLoad(UserCardLoad $load)
	{
		return self::isPromotionLoad($load)
			&& !Util::meta($load, 'promoType');
	}

	public static function isManuallyPromotionLoad(UserCardLoad $load)
	{
		return self::isManuallyLoad($load)
			&& Util::meta($load, 'isPromotion');
	}

	public static function isManuallyNonPromotionLoad(UserCardLoad $load)
	{
		return self::isManuallyLoad($load)
			&& !Util::meta($load, 'isPromotion');
	}

	public static function isReferralPromotionLoad(UserCardLoad $load)
    {
        return self::isPromotionLoad($load)
            && Util::meta($load, 'isPromotion')
            && Util::meta($load, 'promoType') === PromotionService::PROMOTION_TYPE_EARN
            && Util::meta($load, 'customMessage') === UserCardLoad::EARN_TYPE_REFERRAL;
    }

	public static function isEarnPromotionLoad(UserCardLoad $load)
	{
		return self::isPromotionLoad($load)
			&& Util::meta($load, 'isPromotion')
			&& Util::meta($load, 'promoType') === PromotionService::PROMOTION_TYPE_EARN;
	}

	public static function isRewardPromotionLoad(UserCardLoad $load)
	{
		return self::isPromotionLoad($load)
			&& Util::meta($load, 'isPromotion')
			&& Util::meta($load, 'promoType') === PromotionService::PROMOTION_TYPE_PROMO_CODE;
	}

	// for correct error load
	public static function isRollbackLoad(UserCardLoad $load)
	{
		return self::isLoad($load)
			&& Util::meta($load, self::LOAD_TYPE_ROLLBACK);
	}

	public static function isManualLinkSplitLoad(UserCardLoad $load)
	{
		return (self::isInstantLoad($load) || self::isPrefundLoad($load))
			&& Util::meta($load, 'splitLoadType') === self::SPLIT_LOAD_TYPE_MANUAL_LINK;
	}

	public static function isLoadForUnloadFailed(UserCardLoad $load)
	{
		return self::isLoad($load)
			&& Util::meta($load, 'Spendr unload failed');
	}

	public static function isUnloadForLoadFailed(UserCardLoad $load)
	{
		return self::isUnload($load)
			&& Util::meta($load, 'Spendr load failed');
	}

	public static function isRestrictLoad(UserCardLoad $load)
	{
		return (self::isInstantLoad($load) || self::isPrefundLoad($load))
			&& Util::meta($load, 'restrictLoad');
	}

	public static function isRestrictLoadForTransaction(UserCardLoad $load)
	{
		return self::isInstantLoad($load) 
		&& Util::meta($load, 'restrictLoad') 
		&& Util::meta($load, 'restrictLoadForTransactionId');
	}

	public static function isRestrictLoadForMakeWhole(UserCardLoad $load)
	{
		return self::isPrefundLoad($load) 
		&& Util::meta($load, 'restrictLoad') 
		&& Util::meta($load, 'restrictLoadForMakeWhole');
	}

	public static function isReturnedByBankLoad(UserCardLoad $load, $returnedCode = null)
	{
		$res = $load->getLoadStatus() === UserCardLoad::LOAD_STATUS_ERROR
			&& Util::meta($load, 'returnedByBank');

		if ($returnedCode) {
			return $res && Util::meta($load, 'returnedCode') === $returnedCode;
		}
		return $res;
	}

	public static function isMerchantAutoWithdrawal(UserCardLoad $load)
	{
		return self::isUnload($load) && Util::meta($load, 'isAutoWithdrawal');
	}
	
	public static function isMerchantManuallyLoad(UserCardLoad $load)
	{
		return self::isLoad($load)
			&& Util::meta($load, self::LOAD_TYPE_MANUALLY);
	}

	public static function beforePartnerLoadOrUnload($amount, $type)
	{
		if (!in_array($type, ['load', 'unload'])) {
			return 'Invalid type!';
		}

		if (!$amount) {
			return 'Invalid amount!';
		}

		$partner = UserService::getSpendrBalanceAdminAccount('spendr');
		if (!$partner) {
			return 'Unknown Partner admin!';
		}

		$dummy = UserService::getDummyCard($partner);
		if (!$dummy) {
			return 'Unknown Partner dummy card!';
		}

		if ($type === 'unload') {
			if ($dummy && $dummy->getBalance() < $amount) {
				return new FailedResponse('Insufficient balance, cannot withdraw!');
			}
		}

		/** @var UserCard $realCard */
		$realCard = UserService::getUserCurrentBoundCard($partner);
		if (!$realCard->getAccountNumber() || !$realCard->getRoutingNumber()) {
			return 'No bank card bound!';
		}

		return true;
	}

	/**
	 * @param Request $request
	 * @param User $user
	 * @return FailedResponse|SuccessResponse
	 * @throws \PortalBundle\Exception\PortalException
	 */
	public static function partnerLoad(Request $request, User $user)
	{
		$amount = $request->get('amount');
		if (!$amount) {
			return new FailedResponse('Invalid parameter!');
		}

		$loadAmount = Money::normalizeAmount($amount, 'USD');

		$checkRes = self::beforePartnerLoadOrUnload($loadAmount, 'load');
		if (is_string($checkRes)) {
			return new FailedResponse($checkRes);
		}

		$partner = UserService::getSpendrBalanceAdminAccount('spendr');
		$dummy = UserService::getDummyCard($partner);
		/** @var UserCard $realCard */
		$realCard = UserService::getUserCurrentBoundCard($partner);

		$config = $partner->ensureConfig();
		if ($config && $config->getBankAccountId()) {
			$bankAccountId = $config->getBankAccountId();
		} else {
			$bankAccountId = Uuid::uuid4();
			$config->setBankAccountId($bankAccountId)
				->setPlatform(UserConfig::PLATFORM_SPENDR)
				->setReferCode(Uuid::uuid4())
				->persist();
		}

		if (!$dummy->getInitializedAt()) {
			$dummy->setInitializedAt(new \DateTime())->persist();
		}

		$load = $dummy->ensureLoad();
		if ($load->getInitializedAt()) {
			$msg = 'This load request had been initialized. Please start a new request from beginning!';
			return new FailedResponse($msg);
		}

		$transaction = SpendrACHService::createPayment($realCard,'debit', $loadAmount, $bankAccountId);

		$load->setInitialAmount($loadAmount)
			->setPayCurrency('USD')
			->setPayAmount($loadAmount)
			->setLoadFeeUSD(0)
			->setTransactionNo($transaction->getTranId())
			->setInitializedAt($transaction->getCreatedAt())
			->setLoadStatus(UserCardLoad::LOAD_STATUS_INITIATED)
			->setMeta(Util::j2s([
				LoadService::LOAD_TYPE_PREFUND => true
			]));
		Util::persist($load);

		return new SuccessResponse(
			$load->toAppApiArray(),
			'The transaction is successfully created, and your funds will arrive in about 1-3 business days.'
		);
	}

	/**
	 * @param Request $request
	 * @param User $user
	 * @return FailedResponse|SuccessResponse
	 * @throws \PortalBundle\Exception\PortalException
	 */
	public static function partnerUnload(Request $request, User $user)
	{
		$amount = $request->get('amount');
		if (!$amount) {
			return new FailedResponse('Invalid parameter!');
		}

		$unloadAmount = Money::normalizeAmount($amount, 'USD');

		$checkRes = self::beforePartnerLoadOrUnload($unloadAmount, 'unload');
		if (is_string($checkRes)) {
			return new FailedResponse($checkRes);
		}

		$partner = UserService::getSpendrBalanceAdminAccount('spendr');
		$dummy = UserService::getDummyCard($partner);
		/** @var UserCard $realCard */
		$realCard = UserService::getUserCurrentBoundCard($partner);
		$config = $partner->ensureConfig();

		if ($config && $config->getBankAccountId()) {
			$bankAccountId = $config->getBankAccountId();
		} else {
			$bankAccountId = Uuid::uuid4();
			$config->setBankAccountId($bankAccountId)
				->setPlatform(UserConfig::PLATFORM_SPENDR)
				->setReferCode(Uuid::uuid4())
				->persist();
		}

		$unload = $dummy->ensureUnload();
		if ($unload->getInitializedAt()) {
			$msg = 'This load request had been initialized. Please start a new request from beginning!';
			return new FailedResponse($msg);
		}

		$transaction = SpendrACHService::createPayment(
			$realCard,
			'credit',
			$unloadAmount,
			$bankAccountId,
			UserCardTransaction::STATUS_INITIATED
		);

		$unload->setTransactionNo($transaction->getTranId())
			->setInitialAmount($unloadAmount)
			->setPayCurrency('USD')
			->setPayAmount($unloadAmount)
			->setInitializedAt(new \DateTime())
			->setCreateBy($user->getId())
			->setReceivedCurrency('USD')
			->setReceivedAmount($unload->getPayAmount())
			->setLoadStatus(UserCardLoad::LOAD_STATUS_RECEIVED)
			->setMeta(Util::j2s([]))
			->persist();

//		$unload->updateLoadAmountWhenReceived();

		Queue::spendrLoad($unload->getId());

		$data = [
			'id' => $unload->getId(),
			'status' => $unload->getLoadStatus(),
			'transactionNo' => $transaction->getTranId(),
			'unLoadAmount' => $unload->getInitialCoin()->toApiArray(),
			'payAmount' => Coin::create($unload->getPayAmount(), $unload->getPayCurrency())->toApiArray(),
			'unloadFee' => $unload->getUnloadFeeUSD()
		];

		return new SuccessResponse($data, 'The transaction is successfully created and added to the load queue.');
	}

	public static function beforeMerchantUnload(
		SpendrMerchant $merchant,
		$amount,
		UserCard $bankCard,
		$locationId = null,
		UserCardLoad $unload = null
	) {
		$merchantBalanceAdmin = $merchant->getAdminUser();
		if (!$merchantBalanceAdmin) {
			return 'Unknown merchant admin.';
		}

		if (!$amount) {
			return 'Invalid amount!';
		}
		if (!$bankCard
            || $bankCard->getType() === PrivacyAPI::CARD_TYPE_DUMMY
            || $bankCard->getStatus() !== UserCard::STATUS_ACTIVE
            || !$bankCard->getAccountNumber()
            || !$bankCard->getRoutingNumber()
        ) {
		    return 'Invalid bank account.';
        }

		if (!$merchant->isActive()) {
			return 'The merchant status is not activated and cannot withdraw!';
		}

		$merchantBalance = MerchantService::getMerchantBalance($merchant, null, 'merge');
		if ($merchantBalance < $amount) {
			return 'Insufficient balance, cannot withdraw!';
		}

		if (MerchantService::isLocationHasDummyMerchant($merchant)) {
			if (!$locationId) {
				return new FailedResponse('Invalid location.');
			}
			$location = Location::find($locationId);
			if (!$location) {
				return new FailedResponse('Invalid location!');
			}
			if (!$location->isActive()) {
				return 'The location status is not activated and cannot withdraw!';
			}

			$locationMerchantId = $location->getMerchant()->getId();
			if ($locationMerchantId !== $merchant->getId()) {
				$needMergeOtherMerchant = MerchantService::needMergeOtherMerchant($merchant, 'object');
				if ($needMergeOtherMerchant) {
					if ($locationMerchantId !== $needMergeOtherMerchant->getId()) {
						return new FailedResponse('The location does not belong to the merchant!');
					}
					$otherMerchantBalance = MerchantService::getMerchantBalance($merchant, $needMergeOtherMerchant, 'other');
					if ($otherMerchantBalance < $amount) {
						return 'Insufficient balance, cannot withdraw.';
					}
				} else {
					return new FailedResponse('The location does not belong to the merchant.');
				}
			}

			$unloadId = $unload ? $unload->getId() : null;
			if (!LocationService::beforeChangeLocationBalance($location, UserCardBalance::TYPE_UNLOAD_CARD, $unloadId)) {
				return 'Invalid location or location balance account.';
			}
			$locationBalanceAdmin = $location->getAdminUser();
			$locationDummy = UserService::getDummyCard($locationBalanceAdmin);
			if (!$locationDummy || ($locationDummy && $locationDummy->getBalance() < $amount)) {
				return 'Insufficient location balance, cannot withdraw!';
			}
		} else {
			if ($locationId) {
				return new FailedResponse('The location cannot change the balance.');
			}
		}

		return true;
	}

	/**
	 * @param Request $request
	 * @param SpendrMerchant $merchant
	 * @param User $user
	 * @return SuccessResponse|FailedResponse
	 * @throws \PortalBundle\Exception\PortalException
	 * @throws \Throwable
	 */
	public static function merchantUnload(Request $request, SpendrMerchant $merchant, User $user)
	{
		if (
			!$user->isMasterAdmin()
			&& !$user->inTeams(
				array_merge(
					SpendrBundle::getMerchantBalanceAdminRoles(),
					SpendrBundle::getSpendrEmployeeComplianceRoles(),
					SpendrBundle::getMerchantDashboardTopLevelAdminRoles(),
					[
						Role::ROLE_SPENDR_MERCHANT_MANAGER_ADMIN,
						Role::ROLE_SPENDR_MERCHANT_ACCOUNTANT_ADMIN,
					]
				)
			)
		) {
			return new FailedResponse('You do not have permission to perform this operation.');
		}
		if ($request->get('Amount', 0) < 0) {
		    return new FailedResponse('Invalid amount.');
        }
		if (!$request->get('bankId')) {
		    return new FailedResponse('Invalid bank account.');
        }

		$locationId = $request->get('locationId');
		$unloadAmount = Money::normalizeAmount($request->get('Amount', 0), 'USD');
		$bankCard = UserCard::find($request->get('bankId'));

		$checkRes = self::beforeMerchantUnload($merchant, $unloadAmount, $bankCard, $locationId);
		if (is_string($checkRes)) {
			return new FailedResponse($checkRes);
		}

		$data = self::generateUnloadTxn($merchant, $locationId, $bankCard, $unloadAmount);
		if (is_string($data)) {
			return new FailedResponse($data);
		}

		return new SuccessResponse(
			$data,
			'The transaction is successfully created and added to the load queue.'
		);
	}

	public static function generateUnloadTxn(
		SpendrMerchant $merchant, 
		$locationId, 
		UserCard $bankCard, 
		$unloadAmount,
		SpendrAutoWithdrawalHistory $autoWithdrawalHistory = null
	){
		$merchantBalanceAdmin = MerchantService::getMerchantBalanceAdmin($merchant, $locationId);
		$dummy = UserService::getDummyCard($merchantBalanceAdmin);

		$config = $merchantBalanceAdmin->ensureConfig();

		$unload = $dummy->ensureunLoad();
		if ($unload->getInitializedAt()) {
			$msg = 'This load request had been initialized. Please start a new request from beginning!';
			return $msg;
		}

		$transaction = SpendrACHService::createPayment(
			$bankCard,
			'credit',
			$unloadAmount,
			$config->getBankAccountId(),
			UserCardTransaction::STATUS_INITIATED
		);

		$meta = $locationId ? ['needDeductFromLocation' => $locationId] : [];
	
		if ($autoWithdrawalHistory) {
			$autoWithdrawalHistory->setUnload($unload);

			$meta['isAutoWithdrawal'] = true;
		}

		$unload->setTransactionNo($transaction->getTranId())
			->setInitialAmount($unloadAmount)
			->setPayCurrency('USD')
			->setPayAmount($unloadAmount)
			->setInitializedAt(new \DateTime())
			// ->setCreateBy($user->getId())
			->setReceivedCurrency('USD')
			->setReceivedAmount($unloadAmount)
			->setLoadStatus(UserCardLoad::LOAD_STATUS_RECEIVED)
			->setMeta(Util::j2s($meta))
			->persist();

//		FeeService::chargeMerchantWithdrawalFeeToTern($merchantBalanceAdmin, $unload);

		Queue::spendrLoad($unload->getId());

		$loadStatus = $unload->getLoadStatus();
		$data = [
			'id' => $unload->getId(),
			'status' => $loadStatus === UserCardLoad::LOAD_STATUS_RECEIVED ? 'Queued' : $loadStatus,
			'transactionNo' => $transaction->getTranId(),
			'unLoadAmount' => $unload->getInitialCoin()->toApiArray(),
			'payAmount' => Coin::create($unload->getPayAmount(), $unload->getPayCurrency())->toApiArray(),
			'unloadFee' => $unload->getUnloadFeeUSD()
		];

		return $data;
	}

	public static function beforeConsumerManuallyLoad(
		User $user,
		$amount
	){
		$isCanTxn = ConsumerService::isCanTransaction($user);
		if (is_string($isCanTxn)) {
			return $isCanTxn;
		}

		if ($amount <= 0) {
			return 'Invalid amount.';
		}

		$spendrConsumerLoadStatus = ConsumerService::getConsumerLoadStatus($user);
		if ($spendrConsumerLoadStatus === ConsumerService::LOAD_STATUS_FREEZE) {
			return 'The account has been frozen and cannot be deposited.';
		}

		$spendrDummy = UserService::getSpendrBalanceDummyCard('spendr');
		if (!$spendrDummy || ($spendrDummy && ($spendrDummy->getBalance() < $amount))) {
			return 'The partner balance is insufficient and cannot be loaded.';
		}

		$consumerDummy = UserService::getDummyCard($user);
		$consumerBalance = (int)($consumerDummy->getBalance());
		$maxBalance = $consumerDummy->getCard()->getMaxBalance(true);
		if ($amount + $consumerBalance > $maxBalance) {
			$maxBalanceText = Money::format($maxBalance, 'USD', false);
			return 'Cannot exceed maximum balance limit: ' . $maxBalanceText . '.';
		}

		$pendingMakeWholeAmount = ConsumerService::calculatePendingMakeWholeLoadAmount($user);
		if ($pendingMakeWholeAmount > 0) {
			return 'This account currently has a load for "make whole" in progress,'
			. ' and the manual load function is not allowed to be used for this user for the time being.';
		}

		return true;
	}

	/**
	 * @param Request $request
	 * @param User $user
	 * @param User $currentUser
	 * @return FailedResponse|SuccessResponse
	 * @throws \PortalBundle\Exception\PortalException
	 */
	public static function consumerManuallyLoad(Request $request, User $user, User $currentUser)
	{
		$amount = Money::normalizeAmount($request->get('loadAmount', 0), 'USD');
		$isPromotion = $request->get('isPromotion', false);
		$customMessage = $request->get('customMessage', null);

		$checkRes = self::beforeConsumerManuallyLoad($user, $amount);
		if (is_string($checkRes)) {
			return new FailedResponse($checkRes);
		}

		$cacheKey = "promotion_load:{$user->getId()}";
		if (Data::has($cacheKey)) {
			return new FailedResponse('You have promotion load successfully within the last 2 minutes. Please reload data.');
		}

		Data::set($cacheKey, $amount, false, 120);

		$consumerDummy = UserService::getDummyCard($user);
		if (!$consumerDummy->getInitializedAt()) {
			$consumerDummy->setInitializedAt(new \DateTime())->persist();
		}

		$load = $consumerDummy->ensureLoad();
		if ($load->getInitializedAt()) {
			return new FailedResponse('This load request had been initialized. Please start a new request from beginning.');
		}
		$load->setCreateBy($currentUser->getId())
			->setInitialAmount($amount)
			->setPayCurrency('USD')
			->setPayAmount($amount)
			->setLoadFeeUSD(0)
			->setInitializedAt(new \DateTime())
			->setReceivedCurrency('USD')
			->setReceivedAmount($amount)
			->setLoadStatus(UserCardLoad::LOAD_STATUS_RECEIVED)
            ->setLoadType(LoadService::LOAD_TYPE_PROMOTION)
			->setMeta(Util::j2s([
				LoadService::LOAD_TYPE_PROMOTION => true,
				'isPromotion' => $isPromotion,
				'customMessage' => $customMessage
			]));

		Util::persist($load);

//		$load->updateLoadAmountWhenReceived();

		Queue::spendrLoad($load->getId());

		$reason = 'The transaction is successfully created and added to the load queue.';
		$data = [
			'id' => $load->getId(),
			'status' => $load->getLoadStatus(),
			'loadAmount' => $load->getInitialCoin()->toApiArray(),
			'fee' => 0,
			'payAmount' => Coin::create($load->getPayAmount(), $load->getPayCurrency())->toApiArray(),
			'isPromotion' => Util::meta($load, 'isPromotion'),
			'customMessage' => Util::meta($load, 'customMessage'),
			'reason' => $reason
		];
		return new SuccessResponse($data, $reason);
	}

	public static function afterConsumerManuallyLoad(UserCardLoad $load, $loadResult)
	{
		$user = $load->getUserCard()->getUser();
		if (is_string($loadResult)) {
			$cacheKey = "promotion_load:{$user->getId()}";
			Data::del($cacheKey);
			return false;
		}

		$isPromotion = Util::meta($load, 'isPromotion');
		$customMessage = Util::meta($load, 'customMessage');
		if ($isPromotion) {
            $attrs = BrazeService::getUserAttributes($user);
			BrazeService::userTrack(null, [
				'external_id' => BrazeService::getExternalPrefix() . $user->getId(),
				'name' => 'Promotion Load Received',
				'time' => Carbon::now()->format('Y-m-d\TH:i:s\Z'),
				'properties' => [
					'descriptor_name' => $customMessage,
					'amount' => Money::formatAmountToNumber($load->getInitialAmount())
				]
			], null, $attrs);
			SegmentService::track([
			    'userId' => BrazeService::getExternalPrefix() . $user->getId(),
                'event' => 'Manual Promo Load',
                'properties' => [
                    'userId' => BrazeService::getExternalPrefix() . $user->getId(),
                    'manualPromoName' => $customMessage,
                    'amount' => Money::formatAmountToNumber($load->getInitialAmount())
                ]
            ], [
                'userId' => BrazeService::getExternalPrefix() . $user->getId(),
                'traits' => $attrs
            ]);
		}

		return true;
	}

	public static function beforeConsumerUnload(User $user, $unloadAmount)
	{
		$rs = ConsumerService::isCanTransaction($user);
		if (is_string($rs)) {
			return $rs;
		}

		$dummy = UserService::getDummyCard($user);
		if ($dummy->getBalance() < $unloadAmount) {
			return "You don't have enough balance to unload!";
		}

		return true;
	}

	public static function afterConsumerUnload(UserCardLoad $load, $loadResult)
	{
		if (is_string($loadResult)) {
			return false;
		}

		$user = $load->getUserCard()->getUser();
		$attrs = BrazeService::getUserAttributes($user);
		BrazeService::userTrack(null, null, null, $attrs);
		SegmentService::track(null, [
            'userId' => BrazeService::getExternalPrefix() . $user->getId(),
            'traits' => $attrs
        ]);
		return true;
	}

	public static function beforeEarnLoad(SpendrReward $config, User $user)
	{
        if (!$config || !$config->getAmount()) return false;
        $earnType = $config->getType();
		$isCanTransaction = ConsumerService::isCanTransaction($user, 'app');
		if (is_string($isCanTransaction)) {
			SlackService::warning($isCanTransaction, [
				'User' => $user->getSignature(),
				'Earn Type' => $earnType
			]);
			return false;
		}

		$currentUserEarned = ConsumerService::countEarnedRecord($config, $user);

		if ($earnType === UserCardLoad::EARN_TYPE_BANK_LINK) {
		    $countLimit = $config->getCountLimit() ?? 0;
		    if (!$countLimit) return !$currentUserEarned;
			$totalEarned = ConsumerService::countEarnedRecord($config);
			return !$currentUserEarned && $totalEarned < $countLimit;
		} else if ($earnType === UserCardLoad::EARN_TYPE_SPEND_OVER) {
            if (!$config->getSpendAmount()) {
                return false;
            }
			return !$currentUserEarned;
		} else if ($earnType === UserCardLoad::EARN_TYPE_SPEND_REWARD) {
            if (!$config->getSpendAmount()) {
                return false;
            }
            return !$currentUserEarned;
        }

		return false;
	}

	public static function earnLoad(User $user, SpendrReward $reward)
	{
		$dummy = UserService::getDummyCard($user);
		$load = $dummy->ensureLoad();
		if ($load->getInitializedAt()) {
			return 'This load request had been initialized. Please start a new request from beginning.';
		}
		$load->setInitialAmount($reward->getAmount())
			->setPayCurrency('USD')
			->setPayAmount($reward->getAmount())
			->setLoadFeeUSD(0)
			->setInitializedAt(new \DateTime())
			->setReceivedCurrency('USD')
			->setReceivedAmount($reward->getAmount())
			->setLoadStatus(UserCardLoad::LOAD_STATUS_RECEIVED)
            ->setReward($reward)
            ->setPromoType(PromotionService::PROMOTION_TYPE_EARN)
            ->setLoadType(LoadService::LOAD_TYPE_PROMOTION)
            ->setEarnType($reward->getType())
			->setMeta(Util::j2s([
				LoadService::LOAD_TYPE_PROMOTION => true,
				'isPromotion' => true,
				'promoType' => PromotionService::PROMOTION_TYPE_EARN,
				'customMessage' => $reward->getType(),
                'rewardTitle' => $reward->getTitle()
			]));

		Util::persist($load);
//		$load->updateLoadAmountWhenReceived();

		Queue::spendrLoad($load->getId());

		return true;
	}

	public static function afterEarnLoad(UserCardLoad $load, $loadResult)
	{
		if (is_string($loadResult)) {
			return false;
		}

		$user = $load->getUserCard()->getUser();
		$customMessage = Util::meta($load, 'rewardTitle');
		if (!$customMessage) {
            $customMessage = Util::meta($load, 'customMessage');
        }

        $attrs = BrazeService::getUserAttributes($user);
		BrazeService::userTrack(null, [
			'external_id' => BrazeService::getExternalPrefix() . $user->getId(),
			'name' => $customMessage,
			'time' => Carbon::now()->format('Y-m-d\TH:i:s\Z'),
			'properties' => [
				'descriptor_name' => $customMessage,
				'amount' => Money::formatAmountToNumber($load->getInitialAmount())
			]
		], null, $attrs);

        $reward = $load->getReward();
        if (!$reward) return true;

        $event = 'Earn Reward';
        if ($reward->getType() === UserCardLoad::EARN_TYPE_SPEND_OVER) {
            $event = 'Milestone Reward';
        } elseif ($reward->getType() === UserCardLoad::EARN_TYPE_SPEND_REWARD) {
            $event = 'Spend Reward';
        } elseif ($reward->getType() === UserCardLoad::EARN_TYPE_BANK_LINK) {
            $event = 'Bank Link Reward';
        }
        SegmentService::track([
            'userId' => BrazeService::getExternalPrefix() . $user->getId(),
            'event' => $event,
            'properties' => [
                'userId' => BrazeService::getExternalPrefix() . $user->getId(),
                'rewardName' => $customMessage,
                'amount' => Money::formatAmountToNumber($load->getInitialAmount())
            ]
        ], [
            'userId' => BrazeService::getExternalPrefix() . $user->getId(),
            'traits' => $attrs
        ]);

		return true;
	}

	public static function beforeRewardLoad(User $user, $promoCode)
	{
		$isCanTransaction = ConsumerService::isCanTransaction($user, 'app');
		if (is_string($isCanTransaction)) {
			return $isCanTransaction;
		}

	  	if (!$promoCode) {
	  		return 'Invalid promo code.';
	  	}

	  	$promo = RewardService::getPromoByPromoCode($promoCode);
		if (!$promo) {
		  	return 'The promo code does not exist or is not available.';
		}

		/** @var PromotionCode $promo */
		$promo = $promo[0];
		$dummy = UserService::getDummyCard($user);
		if (RewardService::getRewardsQuery($dummy)
			->andWhere('ucl.promoCode = :prom')
			->setParameter('prom', $promo)
			->select('count(distinct ucl)')
			->getQuery()
			->getSingleScalarResult()) {
			return 'You have Claimed the promo code.';
		}

		$start = Carbon::now()->isAfter(Carbon::instance($promo->getStartAt()));
		$end = Carbon::now()->isAfter(Carbon::instance($promo->getEndAt()));
		if (!$start) {
		  	return 'The promo code does not exist or is not available.';
		}
		if (
		  	$promo->getBudget() <= $promo->getRedeem() * $promo->getPromoCodeAmount()
		  	|| $end
		) {
		  	$promo->setStatus(PromotionCode::STATUS_COMPLETED)->persist();
		  	return 'The promo code does not exist or is not available.';
		}

		return $promo;
	}

	public static function afterRewardLoad(UserCardLoad $load, $loadResult)
	{
		$user = $load->getUserCard()->getUser();
		if (is_string($loadResult)) {
			$key = "claim_promo_code:{$user->getId()}";
			Data::del($key);
		} else {
			$promo = $load->getPromoCode();
			$promo->setRedeem($promo->getRedeem() + 1);
			if ($promo->getRedeem() * $promo->getPromoCodeAmount() >= $promo->getBudget()) {
				$promo->setStatus(PromotionCode::STATUS_COMPLETED);
			}
			$promo->persist();
		}

        $attrs = BrazeService::getUserAttributes($user);
        BrazeService::userTrack(null, null, null, $attrs);
        SegmentService::track(null, [
            'userId' => BrazeService::getExternalPrefix() . $user->getId(),
            'traits' => $attrs
        ]);
	}

	public static function beforeReferralLoad(User $user)
    {
        $isCanTransaction = ConsumerService::isCanTransaction($user, 'app');
        if (is_string($isCanTransaction)) {
            SlackService::warning('Failed to push referral reward to consumer', [
                'Consumer' => $user->getSignature(),
                'Reason' => $isCanTransaction
            ]);
            return false;
        }

//        $earnCount = ConsumerService::countReferralEarned($user);
//        return !$earnCount;
        return true;
    }

    public static function referralLoad(User $user, SpendrReferConfig $config, $isGet = false)
    {
        $dummy = UserService::getDummyCard($user);
        $load = $dummy->ensureLoad();
        if ($load->getInitializedAt()) {
            return 'This load request had been initialized. Please start a new request from beginning.';
        }
        $amount = $isGet ? $config->getGetReward() : $config->getGiveReward();
        $load->setReferConfig($config)
            ->setInitialAmount($amount)
            ->setPayCurrency('USD')
            ->setPayAmount($amount)
            ->setLoadFeeUSD(0)
            ->setInitializedAt(new \DateTime())
            ->setReceivedCurrency('USD')
            ->setReceivedAmount($amount)
            ->setLoadStatus(UserCardLoad::LOAD_STATUS_RECEIVED)
            ->setPromoType(PromotionService::PROMOTION_TYPE_EARN)
            ->setLoadType(LoadService::LOAD_TYPE_PROMOTION)
            ->setEarnType(UserCardLoad::EARN_TYPE_REFERRAL)
            ->setMeta(Util::j2s([
                LoadService::LOAD_TYPE_PROMOTION => true,
                'isPromotion' => true,
                'promoType' => PromotionService::PROMOTION_TYPE_EARN,
                'customMessage' => UserCardLoad::EARN_TYPE_REFERRAL
            ]));

        Util::persist($load);

        Queue::spendrLoad($load->getId());

        return true;
    }

    public static function afterReferralLoad(UserCardLoad $load)
    {
        $referId = Util::meta($load, 'referId');
        if (!$referId) return;
        /** @var SpendrReferral $refer */
        $refer = Util::em()->find(SpendrReferral::class, $referId);
        if (!$refer) return;

        $user = $load->getUserCard()->getUser();
        $customMessage = Util::meta($load, 'rewardTitle');
        if (!$customMessage) {
            $customMessage = Util::meta($load, 'customMessage');
        }

        $attrs = BrazeService::getUserAttributes($user);
        BrazeService::userTrack(null, [
            'external_id' => BrazeService::getExternalPrefix() . $user->getId(),
            'name' => $customMessage,
            'time' => Carbon::now()->format('Y-m-d\TH:i:s\Z'),
            'properties' => [
                'descriptor_name' => $customMessage,
                'amount' => Money::formatAmountToNumber($load->getInitialAmount())
            ]
        ], null, $attrs);

        SegmentService::track([
            'userId' => BrazeService::getExternalPrefix() . $user->getId(),
            'event' => 'Referral Reward',
            'properties' => [
                'userId' => BrazeService::getExternalPrefix() . $user->getId(),
                'amount' => Money::formatAmountToNumber($load->getInitialAmount()),
                'relatedUserId' => BrazeService::getExternalPrefix() . (
                    $user->getId() === $refer->getInviter()->getId() ?
                        $refer->getInvitee()->getId() : $refer->getInviter()->getId()
                    ),
            ]
        ], [
            'userId' => BrazeService::getExternalPrefix() . $user->getId(),
            'traits' => $attrs
        ]);
    }

	public static function beforeCancelLoad(UserCardLoad $load)
	{
		if (
			LoadService::isLoadForUnloadFailed($load)
			|| LoadService::isUnloadForLoadFailed($load)
			|| LoadService::isRollbackLoad($load)
		) {
			return 'This transaction is a refund load and cannot be cancelled.';
		}

		if (LoadService::isManuallyLoad($load)) {
			return self::beforeCancelManuallyLoad($load);
		} else if (LoadService::isEarnPromotionLoad($load) || LoadService::isRewardPromotionLoad($load)) {
			return self::beforeCancelRewardLoad($load);
		} else {
			return self::beforeCancelAchLoad($load);
		}
	}

	protected static function beforeCancelAchLoad(UserCardLoad $load)
	{
		/** @var UserCardTransaction $ucTxn */
		$ucTxn = Util::em()->getRepository(UserCardTransaction::class)
			->findOneBy([
				'tranId' => $load->getTransactionNo()
			]);

		$achTxn = Util::em()->getRepository(AchTransactions::class)
			->findOneBy([
				'tranId' => $load->getTransactionNo()
			]);

		if (!$ucTxn || !$achTxn) {
			return 'Missing ACH transaction data or abnormal data.';
		}

		$uctStatus = $ucTxn ? $ucTxn->getAccountStatus() : null;

		if ($load->getLoadStatus() === UserCardLoad::LOAD_STATUS_ERROR) {
			if ($ucTxn) {
				if ($uctStatus === UserCardTransaction::STATUS_LL_CANCELED) {
					return 'The transaction has been cancelled.';
				}
				if ($uctStatus === UserCardTransaction::STATUS_LL_RETURNED) {
					return 'The transaction was returned by the bank and cannot be cancelled.';
				}
			}
			return 'The transaction cannot be cancelled.';
		}

		if ($uctStatus === UserCardTransaction::STATUS_LL_PROCESSING) {
			return 'The transaction is already being processed and cannot be cancelled.';
		}

		if ($uctStatus === UserCardTransaction::STATUS_LL_SETTLED) {
			return 'The transaction has been settled and cannot be cancelled.';
		}

		if (!in_array($uctStatus, [
			UserCardTransaction::STATUS_INITIATED,
			UserCardTransaction::STATUS_LL_RECEIVED
		])
		) {
			return 'The transaction cannot be cancelled.';
		}

		return true;
	}

	public static function beforeCancelManuallyLoad(UserCardLoad $load)
	{
		if (
			!LoadService::isManuallyLoad($load)
			|| $load->getLoadStatus() !== UserCardLoad::LOAD_STATUS_LOADED
			|| !$load->getUserCard()->getUser()->inTeams(SpendrBundle::getConsumerRoles())
		) {
			return 'The transaction cannot be cancelled.';
		}

		return true;
	}

	public static function beforeCancelRewardLoad(UserCardLoad $load)
	{
		if (
			(!LoadService::isRewardPromotionLoad($load) && !LoadService::isEarnPromotionLoad($load))
			|| $load->getLoadStatus() !== UserCardLoad::LOAD_STATUS_LOADED
			|| !$load->getUserCard()->getUser()->inTeams(SpendrBundle::getConsumerRoles())
		) {
			return 'The transaction cannot be revoked.';
		}

		return true;
	}

	public static function cancelLoad(UserCardLoad $load, string $cancelReason)
	{
		if (LoadService::isManuallyLoad($load)) {
			self::cancelManuallyLoad($load, $cancelReason);
		} else if (LoadService::isEarnPromotionLoad($load) || LoadService::isRewardPromotionLoad($load)) {
			self::cancelEarnLoad($load, $cancelReason);
		} else {
			self::cancelAchLoad($load, $cancelReason);
		}
	}

	public static function cancelAchLoad(UserCardLoad $load, string $cancelReason)
	{
		$ucTxn = UserCardTransaction::findByTranId($load->getTransactionNo());
		$achTxn = AchTransactions::findTransactionByTranId($load->getTransactionNo());

		$ucTxn->setAccountStatus(UserCardTransaction::STATUS_LL_CANCELED)
			->persist();

		$achTxn->setTranStatus(UserCardTransaction::STATUS_LL_CANCELED)
			->persist();

		SpendrWebhookService::spendrTransactionWebhook($achTxn, $cancelReason);
	}

	public static function cancelManuallyLoad(UserCardLoad $load, $cancelReason)
	{
		$dummy = $load->getUserCard();
		$unload = $dummy->ensureUnload();

		$unloadAmount = $load->getLoadAmount();

		$unload->setInitialAmount($unloadAmount)
			->setPayCurrency('USD')
			->setPayAmount($unloadAmount)
			->setInitializedAt(new \DateTime())
			->setReceivedCurrency('USD')
			->setReceivedAmount($unload->getPayAmount())
			->setLoadStatus(UserCardLoad::LOAD_STATUS_RECEIVED)
            ->setLoadType(LoadService::UNLOAD_TYPE_PROMOTION)
			->persist();

		Util::updateMeta($unload, [
            LoadService::UNLOAD_TYPE_PROMOTION => true,
			'Spendr load failed' => $load->getId(),
			'returnInstantLoadFundsToPartnerBalance' => true
		]);

		$slackMsg = '(Promotion load)Load canceled. Load ID: ' . $load->getId()
			. ', reason: ' . $cancelReason . '. And added an unload for the consumer. And added to the load queue.';
		SlackService::info($slackMsg, [
			'Consumer' => $load->getUserCard()->getUser()->getSignature(),
			'Amount' => Money::format($load->getLoadAmount(), 'USD'),
		]);

		$load->setLoadStatus(UserCardLoad::LOAD_STATUS_ERROR)
			->setError($cancelReason)
			->persist();

//		$unload->updateLoadAmountWhenReceived();

		Queue::spendrLoad($unload->getId());
	}

	public static function cancelEarnLoad(UserCardLoad $load, $cancelReason)
	{
		$uc = $load->getUserCard();
		$unload = $uc->ensureUnload();

		$unloadAmount = $load->getLoadAmount();
		$promoType = Util::meta($load, 'promoType');

		$unload->setInitialAmount($unloadAmount)
			->setPromoCode($load->getPromoCode())
			->setPayCurrency('USD')
			->setPayAmount($unloadAmount)
			->setInitializedAt(new \DateTime())
			->setReceivedCurrency('USD')
			->setReceivedAmount($unload->getPayAmount())
			->setLoadStatus(UserCardLoad::LOAD_STATUS_RECEIVED)
            ->setLoadType(LoadService::UNLOAD_TYPE_PROMOTION)
            ->setPromoType($promoType ?? 'Promo Revoke')
            ->setEarnType($promoType === PromotionService::PROMOTION_TYPE_EARN ? Util::meta($load, 'customMessage') : null)
			->persist();

		$metaData = [
            LoadService::UNLOAD_TYPE_PROMOTION => true,
			'promoType' => $promoType ?? 'Promo Revoke',
			'customMessage' => 'Promo Revoke ' . Util::meta($load, 'customMessage'),
			'Spendr load failed' => $load->getId(),
			'returnInstantLoadFundsToPartnerBalance' => true
		];
		Util::updateMeta($unload, $metaData);

		$slackMsg = 'Promo load revoked. Load ID: ' . $load->getId() . ', reason: ' . $cancelReason
			. '. And added an unload for the consumer. And added to the load queue.';
		SlackService::info($slackMsg, [
			'Consumer' => $load->getUserCard()->getUser()->getSignature(),
			'Amount' => Money::format($load->getLoadAmount(), 'USD'),
			'Load transaction status' => UserCardTransaction::STATUS_LL_CANCELED
		]);

		$load->setLoadStatus(UserCardLoad::LOAD_STATUS_ERROR)
			->setError($cancelReason)
			->persist();

//		$unload->updateLoadAmountWhenReceived();

		Queue::spendrLoad($unload->getId());
	}

	public static function afterCancelLoad(UserCardLoad $load, $cancelResult)
	{
		// todo: push result to related user?
	}







	public static function isSpendrSpecialLoad(UserCardLoad $load) {
		if (
			LoadService::isInstantLoad($load)
			|| LoadService::isPrefundLoad($load)
			|| LoadService::isPromotionLoad($load)
			|| LoadService::isRollbackLoad($load)
			|| LoadService::isMerchantManuallyLoad($load)
		) {
			return true;
		} else {
			return false;
		}
	}

	public static function isSpendrSpecialUnload(UserCardLoad $load) {
		$returnInstantLoadFundsToPartnerBalance = Util::meta($load, 'returnInstantLoadFundsToPartnerBalance');
		$returnInstantLoadFunds = Util::meta($load, 'returnInstantLoadFunds');
		$returnPrefundLoadFunds = Util::meta($load, 'returnPrefundLoadFunds');

		if (
			!$returnInstantLoadFundsToPartnerBalance
			&& !$returnInstantLoadFunds
			&& !$returnPrefundLoadFunds
		) {
			return false;
		} else {
			return true;
		}
	}

	/**
	 * @param UserCardLoad $load
	 * @param $amount
	 * @return bool|string
	 * @throws \Throwable
	 */
	public static function handleLoadTxnBalanceUpdate(UserCardLoad $load, $amount)
	{
		if (!self::isSpendrSpecialLoad($load)) {
			return 'The load data type is abnormal and cannot be processed.';
		}

		$promotionLoad = LoadService::isPromotionLoad($load) ? true : false;

		if (LoadService::isInstantLoad($load) || $promotionLoad) {
			return self::instantLoadFundsFromPartnerBalance($load, $amount, $promotionLoad);
		} else if (LoadService::isRollbackLoad($load)) {
			return self::rollbackLoadFunds($load, $amount);
		} else if (LoadService::isMerchantManuallyLoad($load)) {
			return self::merchantManuallyLoadBalanceChange($load, $amount);
		} else {
			return self::prefundLoadFunds($load, $amount);
		}
	}

	public static function handleUnloadTxnBalanceUpdate(UserCardLoad $unload, $amount)
	{
		if (!self::isSpendrSpecialUnload($unload)) {
			return 'The load data type is abnormal and cannot be processed.';
		}

		$returnInstantLoadFundsToPartnerBalance = Util::meta($unload, 'returnInstantLoadFundsToPartnerBalance');
		$returnInstantLoadFunds = Util::meta($unload, 'returnInstantLoadFunds');
		$returnPrefundLoadFunds = Util::meta($unload, 'returnPrefundLoadFunds');

		if ($returnInstantLoadFundsToPartnerBalance || $returnInstantLoadFunds) {
			$res = self::returnInstantLoadFunds($unload, $amount);
		} else {
			$res = self::returnPrefundLoadFunds($unload, $amount);
		}

		return $res;
	}

	/**
	 * @param UserCardLoad $load
	 * @param $amount
	 * @param bool $isPromotion
	 * @return bool|string
	 * @throws \PortalBundle\Exception\PortalException
	 */
	private static function instantLoadFundsFromPartnerBalance(UserCardLoad $load, $amount, $isPromotion = false)
	{
		if (!$load || !$amount) {
			return 'Invalid parameters.';
		}

		$dummy = $load->getUserCard();
		if (!$dummy || !$dummy->isDummy()) {
			return 'Incorrect account dummy card.';
		}

		$user = $dummy->getUser();
		if (!$user->inTeams(SpendrBundle::getConsumerRoles())) {
			return 'You do not have permission to perform this operation.';
		}

		$spendrDummy = UserService::getSpendrBalanceDummyCard('spendr');
		if (!$spendrDummy || ($spendrDummy && ($spendrDummy->getBalance() < $amount))) {
			return 'The partner balance is insufficient and cannot be loaded.';
		}

		$loadBy = null;
		if ($load->getCreateBy()) {
			$loadByUser = User::find($load->getCreateBy());
			if ($loadByUser) {
				$loadBy = $loadByUser->getSignature();
			}
		}

		$comment = sprintf(
			'Load ID: %s. %sInstantly load funds from partner balance to the consumer %s at %s. Load by: %s. Amount: %s.',
			$load->getId(),
			$isPromotion ? 'Promotion Load. ' : null,
			$user->getSignature(),
			Carbon::now(),
			$loadBy,
			Money::format($amount, 'USD'),
		);

		$spendrDummy->updatePrivacyBalanceBy(-$amount, UserCardBalance::TYPE_LOAD_CARD, $comment, false, $load);
		$dummy->updatePrivacyBalanceBy($amount, UserCardBalance::TYPE_LOAD_CARD, $comment, false, $load);

		$spendrDummy->persist();
		$dummy->persist();

		SlackService::tada($comment);

		if ($isPromotion) {
			self::offsetPendingFee($amount, UserCard::OFFSET_PENDING_FEE_BY_PROMOTION_LOAD, $load);
		}

		return true;
	}

	public static function offsetPendingFee($amount, $type, UserCardLoad $load = null, Transaction $refundTxn = null)
	{
		if (!in_array($type, [
			UserCard::OFFSET_PENDING_FEE_BY_PREFUND_LOAD,
			UserCard::OFFSET_PENDING_FEE_BY_PROMOTION_LOAD,
			UserCard::OFFSET_PENDING_FEE_BY_REFUND_TXN
		]) || !$amount) {
			return false;
		}

		if (in_array($type, [
			UserCard::OFFSET_PENDING_FEE_BY_PREFUND_LOAD,
			UserCard::OFFSET_PENDING_FEE_BY_PROMOTION_LOAD,
		]) && !$load) {
			return false;
		}

		if ($type === UserCard::OFFSET_PENDING_FEE_BY_REFUND_TXN && !$refundTxn) {
			return false;
		}

		if ($load) {
			$consumerDummy = $load->getUserCard();
		} else {
			$consumer = $refundTxn->getConsumer();
			$consumerDummy = UserService::getDummyCard($consumer);
		}

		// if pending balance, add back to partner balance
		$pendingBalances = Util::s2j($consumerDummy->getPendingFees());

		$total = 0;
		foreach ($pendingBalances as $key=>$pb) {
			if (
				$pb['type'] === UserCard::PENDING_FEE_BACK_TO_PARTNER
				|| $pb['type'] === UserCard::PENDING_ACH_RETURN_FEE_BACK_TO_PARTNER
			) {
				if ($amount >= $pb['amount']) {
					$amount -= $pb['amount'];
					$total += $pb['amount'];
					unset($pendingBalances[$key]);
					continue;
				} else {
					$pendingBalances[$key]['amount'] -= $amount;
					$total += $amount;
					break;
				}
			}
		}

		if ($total) {
			$spendrDummy = UserService::getSpendrBalanceDummyCard('spendr');

			if ($load) {
				$object = $load;
				$loadId = $load->getId();
				$text = '%s. Load ID: ' . $loadId . '. ' . $type . ' load. Back pending fee to the partner %s at %s. Amount: %s.';
			} else {
				$object = $refundTxn;
				$txnId = $refundTxn->getId();
				$text = '%s. Txn ID: ' . $txnId . '. Back pending fee to the partner %s at %s. Amount: %s.';
			}

			$spendrComment = sprintf(
				$text,
				UserCard::PENDING_FEE_BACK_TO_PARTNER,
				$spendrDummy->getUser()->getSignature(),
				Carbon::now(),
				Money::format($total, 'USD'),
			);

			$spendrDummy->updatePrivacyBalanceBy(
				$total,
				UserCard::PENDING_FEE_BACK_TO_PARTNER,
				$spendrComment,
				false,
				$object
			);
			$spendrDummy->persist();

			SlackService::tada($spendrComment);

			$consumerDummy->setPendingFees(Util::j2s($pendingBalances))
				->persist();

			$consumerDummy->getUser()->addNote($spendrComment);
		}

		return $total;
	}

	private static function prefundLoadFunds(UserCardLoad $load, $amount)
	{
		if (!$load || !$amount) {
			return 'Invalid parameters.';
		}

		$dummy = $load->getUserCard();
		if (!$dummy || !$dummy->isDummy()) {
			return 'Incorrect account dummy card.';
		}

		$user = $dummy->getUser();
		if (!$user->inTeams([
			Role::ROLE_SPENDR_CONSUMER,
			Role::ROLE_SPENDR_PROGRAM_OWNER
		])) {
			return 'You do not have permission to perform this operation.';
		}

		$isConsumer = $user->inTeams(SpendrBundle::getConsumerRoles());
		$isPartner = $user->inTeams([Role::ROLE_SPENDR_PROGRAM_OWNER]);

		$comment = sprintf(
			'%sLoad ID: %s. Prefund load. Add funds to the user %s at %s. Amount: %s.',
			$isConsumer ? '(Consumer load) ' : ($isPartner ? '(Partner load) ' : null),
			$load->getId(),
			$user->getSignature(),
			Carbon::now(),
			Money::format($amount, 'USD'),
		);

		// add to user balance
		$dummy->updatePrivacyBalanceBy(
			$amount,
			UserCardBalance::TYPE_LOAD_CARD,
			$comment,
			false,
			$load
		);
		$dummy->persist();
		SlackService::tada($comment);

		// if pending balance, also add to partner balance
		if ($isConsumer) {
			self::offsetPendingFee($amount, UserCard::OFFSET_PENDING_FEE_BY_PREFUND_LOAD, $load);
		}

		return true;
	}

	/**
	 * @param UserCardLoad $load
	 * @param $amount
	 * @return bool|string
	 * @throws \PortalBundle\Exception\PortalException
	 */
	private static function rollbackLoadFunds(UserCardLoad $load, $amount)
	{
		if (!$load || !$amount) {
			return 'Invalid parameters.';
		}

		$consumerDummy = $load->getUserCard();
		if (!$consumerDummy || !$consumerDummy->isDummy()) {
			return 'Incorrect account dummy card.';
		}

		$consumer = $consumerDummy->getUser();
		if (!$consumer->inTeams(SpendrBundle::getConsumerRoles())) {
			return 'You do not have permission to perform this operation.';
		}

		$rollbackUnloadId = Util::meta($load, 'Spendr rollback unload');
		if (!$rollbackUnloadId) {
			return 'Invalid rollback unload id.';
		}

		$rollbackUnload = UserCardLoad::find($rollbackUnloadId);
		if (!$rollbackUnload) {
			return 'Invalid rollback unload.';
		}

		$consumerComment = sprintf(
			'Load ID: %s. Rollback load. Rollback funds from partner balance to the consumer %s at %s. Amount: %s.',
			$load->getId(),
			$consumer->getSignature(),
			Carbon::now(),
			Money::format($amount, 'USD'),
		);
		$consumerDummy->updatePrivacyBalanceBy($amount, UserCardBalance::TYPE_LOAD_CARD, $consumerComment, false, $load);
		$consumerDummy->persist();

		$partnerAmount = $amount;
		$pendingAmount = null;
		$pendingFees = Util::s2j($consumerDummy->getPendingFees());
		if ($pendingFees) {
			foreach ($pendingFees as $key=>$pf) {
				if (
					$pf['type'] === UserCard::PENDING_FEE_BACK_TO_PARTNER
					&& $pf['load'] === $rollbackUnloadId
					&& ($pf['amount'] > 0)
				) {
					$pendingAmount = $pf['amount'];
					$partnerAmount = $amount - $pendingAmount;
					unset($pendingFees[$key]);
					break;
				}
			}
		}

		$partnerComment = null;
		if ($partnerAmount > 0) {
			$partnerDummy = UserService::getSpendrBalanceDummyCard('spendr');
			$partnerComment = sprintf(
				'Load ID: %s. Rollback load. Rollback funds from partner balance to the consumer %s at %s.' .
				' Amount: %s. Pending Amount: %s. Actually deducted: %s.',
				$load->getId(),
				$consumer->getSignature(),
				Carbon::now(),
				Money::format($amount, 'USD'),
				Money::format($pendingAmount, 'USD'),
				Money::format($partnerAmount, 'USD'),
			);

			$partnerDummy->updatePrivacyBalanceBy(-$partnerAmount, UserCardBalance::TYPE_LOAD_CARD, $partnerComment, false, $load);
			$partnerDummy->persist();

			if ((int)$partnerAmount !== (int)$amount) {
				$consumerDummy->setPendingFees(Util::j2s($pendingFees))
					->persist();
				$consumer->addNote($partnerComment);
			}
		}

		SlackService::eyes($consumerComment);
		if ($partnerComment) {
			SlackService::eyes($partnerComment);
		}

		return true;
	}

	/**
	 * @param UserCardLoad $load
	 * @param $amount
	 * @return bool|string
	 * @throws \Throwable
	 */
	public static function returnInstantLoadFunds(UserCardLoad $load, $amount)
	{
		if (!$load || !$amount) {
			return 'Invalid parameters.';
		}

		$consumerDummy = $load->getUserCard();
		if (!$consumerDummy || !$consumerDummy->isDummy()) {
			return 'Incorrect account dummy card.';
		}

		$user = $consumerDummy->getUser();
		if (!$user->inTeams(SpendrBundle::getConsumerRoles())) {
			return 'You do not have permission to perform this operation.';
		}

		$isCancelled = Util::meta($load, 'returnInstantLoadFundsToPartnerBalance');
		$isReturned = Util::meta($load, 'returnInstantLoadFunds');

		if (!$isCancelled && !$isReturned) {
			return 'The transaction cannot return funds.';
		}

		$consumerBalance = $consumerDummy->getBalance();

		$text = null;
		if ($isCancelled) {
			$text = 'Canceled. ';
		} else if ($isReturned) {
			$text = 'Returned. ';
		}

		$consumerComment = sprintf(
			$msg = 'Unload ID: %s. %sFailed to pull funds, deduct funds from the consumer %s at %s. Amount: %s.',
			$load->getId(),
			$text,
			$user->getSignature(),
			Carbon::now(),
			Money::format($amount, 'USD'),
		);

		$consumerDummy->updatePrivacyBalanceBy(
			-$amount,
			UserCardBalance::TYPE_UNLOAD_CARD,
			$consumerComment,
			false,
			$load
		);
		$consumerDummy->persist();
		SlackService::tada($consumerComment);

		$spendrDummy = UserService::getSpendrBalanceDummyCard('spendr');

		$pendingAmount = 0;
		$processedAmount = 0;
		$spendrComment = null;

		if ($isReturned) {
			// process: 1. bank ledger(spendrBalance + loadAmount); 2. return(spendrBalance - loadAmount + consumerBalance);
			if ($consumerBalance < $amount) {
				if ($consumerBalance <= 0) {
					$processedAmount = $amount;
				} else {
					$processedAmount = $amount - $consumerBalance;
				}
				$pendingAmount = $processedAmount;

				$spendrComment = sprintf(
					$msg = 'Unload ID: %s. Returned by bank. Failed to pull funds, ' .
						'deduct funds from the partner %s at %s. Load amount: %s. Deducted amount: %s. Pending amount: %s',
					$load->getId(),
					$spendrDummy->getUser()->getSignature(),
					Carbon::now(),
					Money::format($amount, 'USD'),
					Money::format($processedAmount, 'USD'),
					Money::format($pendingAmount, 'USD'),
				);

				$spendrDummy->updatePrivacyBalanceBy(
					-$processedAmount,
					UserCardBalance::TYPE_UNLOAD_CARD,
					$spendrComment,
					false,
					$load
				);
				$spendrDummy->persist();
			}
		}

		if ($isCancelled) {
			if ($consumerBalance < $amount) {
				if ($consumerBalance <= 0) {
					$processedAmount = 0;
					$pendingAmount = $amount;
				} else {
					$processedAmount = $consumerBalance;
					$pendingAmount = $amount - $consumerBalance;
				}
			} else {
				$processedAmount = $amount;
			}

			$spendrComment = sprintf(
				$msg = 'Unload ID: %s. Canceled. Failed to pull funds, return funds to the partner %s at %s. ' .
					'Funds should be added: %s. Funds actually added: %s. Pending amount: %s',
				$load->getId(),
				$spendrDummy->getUser()->getSignature(),
				Carbon::now(),
				Money::format($amount, 'USD'),
				Money::format($processedAmount, 'USD'),
				Money::format($pendingAmount, 'USD'),
			);

			if ($processedAmount > 0) {
				$spendrDummy->updatePrivacyBalanceBy(
					$processedAmount,
					UserCardBalance::TYPE_UNLOAD_CARD,
					$spendrComment,
					false,
					$load
				);
				$spendrDummy->persist();
			}
		}

		if ($spendrComment) {
			SlackService::tada($spendrComment);
		}

		if ($pendingAmount) {
			$consumerDummy->updatePendingFee(
				UserCard::PENDING_FEE_BACK_TO_PARTNER,
				'load',
				$load->getId(),
				$pendingAmount
			);
		}

		return true;
	}

	public static function returnPrefundLoadFunds(UserCardLoad $load, $amount) {
		if (!$load || !$amount) {
			return 'Invalid parameters.';
		}

		$dummy = $load->getUserCard();
		if (!$dummy || !$dummy->isDummy()) {
			return 'Incorrect account dummy card.';
		}

		$user = $dummy->getUser();
		if (!$user->inTeams([
			Role::ROLE_SPENDR_CONSUMER,
			Role::ROLE_SPENDR_PROGRAM_OWNER
		])) {
			return 'You do not have permission to perform this operation.';
		}

		$returnPrefundLoadFunds = Util::meta($load, 'returnPrefundLoadFunds');

		if (!$returnPrefundLoadFunds) {
			return 'The transaction cannot return funds.';
		}

		$isConsumer = $user->inTeams(SpendrBundle::getConsumerRoles());
		$isPartner = $user->inTeams([Role::ROLE_SPENDR_PROGRAM_OWNER]);

		$comment = sprintf(
			$msg = '%sUnload ID: %s. Failed to pull funds, deduct funds from the user %s at %s. Amount: %s.',
			$isConsumer ? '(Consumer unload) ' : ($isPartner ? '(Partner unload) ' : null),
			$load->getId(),
			$user->getSignature(),
			Carbon::now(),
			Money::format($amount, 'USD'),
		);

		$dummy->updatePrivacyBalanceBy(-$amount, UserCardBalance::TYPE_UNLOAD_CARD, $comment, false, $load);
		$dummy->persist();

		SlackService::eyes($comment);

		return true;
	}

	public static function removeManuallyChangeLoadStatusInfo(UserCardLoad $load)
	{
		$user = $load->getUserCard()->getUser();
		$manuallyChangedLoadStatus = Util::meta($user, 'manuallyChangeLoadStatusTo');
		$manuallyChangedLoadStatusAt = Util::meta($user, 'manuallyChangeLoadStatusAt');
		if (!$manuallyChangedLoadStatus && !$manuallyChangedLoadStatusAt) {
			return null;
		}

		$remove = false;

		$achReturnLoadStatus = ConsumerService::calculateLoadStatusByAchReturnTimes($user);
		if ($achReturnLoadStatus === ConsumerService::LOAD_STATUS_FREEZE) {
			$remove = true;
		} else {
			if ($manuallyChangedLoadStatus === ConsumerService::LOAD_STATUS_INSTANT) {
				$remove = true;
			} else if ($manuallyChangedLoadStatus === ConsumerService::LOAD_STATUS_PREFUND) {
				$remove = false;
			}
		}

		if ($remove) {
			Util::updateMeta($user, [
				'manuallyChangeLoadStatusTo' => null,
				'manuallyChangeLoadStatusAt' => null
			]);
	
			$currentLoadStatus = ConsumerService::getConsumerLoadStatus($user);
	
			$desc = 'The transaction with Load id ' . $load->getId() . ' was returned by the bank, '.
				'so cancel the setting of the manually changed load status. '.
				'Load status that was manually changed before: ' . $manuallyChangedLoadStatus .
				'. Current load status: ' . $currentLoadStatus . '.';
			$user->addNote($desc, true, Util::ADMIN_ID);
			SlackService::info($desc);
		}
	}

	public static function getLoadFeeHistory(UserCardLoad $load = null)
	{
		$fees = [
			'Estimated Tern Fee' => null,
			'Estimated Bank Fee' => null,
			'Spendr ACH Return Fee' => null,
			'Estimated Bank ACH Return Fee' => null,
			'Estimated Merchant Withdraw Fee' => null,
			'Estimated Partner Withdraw Fee' => null,
		];
		if (
			self::isInstantLoad($load)
			|| self::isPrefundLoad($load)
		) {
			$achTxn = AchTransactions::findTransactionByTranId($load->getTransactionNo());
			$batch = null;
			if ($achTxn->getBatchId()) {
				$batch = AchBatch::find($achTxn->getBatchId());
			}
			if ($achTxn->getTranStatus() !== UserCardTransaction::STATUS_LL_CANCELED) {
				$fee = FeeService::consumerLoadFeeToBank($load->getInitialAmount());
				$text = null;
				if ($batch && $batch->getProcessType() === AchBatch::ACH_BATCH_PROCESS_TYPE_SAME_DAY) {
					$fee += FeeService::achSameDayFeeToBank();
					$text = ' (Same day ACH)';
				}
				$fees['Estimated Bank Fee'] = Money::format($fee, 'USD', false) . $text;
				$fees['Estimated Tern Fee'] = Money::format((int)(FeeService::calculateTernFeeByLoad()), 'USD', false);
			}
		}
		if ($load->getType() === UserCardLoad::TYPE_UNLOAD) {
			if ($load->getUserCard()->getUser()->inTeams(SpendrBundle::getMerchantBalanceAdminRoles())) {
				$fees['Estimated Merchant Withdraw Fee'] = Money::format((int)(FeeService::calculateTernFeeByLoad()), 'USD', false);
			}
			if ($load->getUserCard()->getUser()->inTeams([Role::ROLE_SPENDR_PROGRAM_OWNER])) {
				$fees['Estimated Partner Withdraw Fee'] = Money::format((int)(FeeService::calculateTernFeeByLoad()), 'USD', false);
			}
		}
		if (
			Util::meta($load, 'returnedByBank')
//			&& Util::meta($load, 'returnedCode') === SpendrAchService::RETURN_CODE_INSUFFICIENT_FUNDS
		) {
			$fee = FeeService::toBankAchReturnFee();
			$fees['Estimated Bank ACH Return Fee'] = Money::format($fee, 'USD', false);
		}
		$id = $load->getId();
		$rc = new \ReflectionClass($load);
		$entityName = $rc->getName();

		$expr = Util::expr();

		$feeHistory = Util::em()->getRepository(UserFeeHistory::class)
			->createQueryBuilder('ufh')
			->where($expr->eq('ufh.entity', ':entity'))
			->setParameter('entity', $entityName)
			->andWhere($expr->eq('ufh.entityId', ':entityId'))
			->setParameter('entityId', $id)
			->andWhere($expr->like('ufh.meta', ':spendrMeta'))
			->setParameter('spendrMeta',  '%"spendrMeta":true%')
			->andWhere($expr->eq('ufh.feeName', ':feeName'))
			->setParameter('feeName', FeeService::TO_SPENDR_CONSUMER_ACH_RETURN_FEE)
			->setMaxResults(1)
			->getQuery()
			->getOneOrNullResult();
		if (!$feeHistory) {
			return $fees;
		}

		$amount = Money::format($feeHistory->getAmount(), 'USD', false);
		$fees['Spendr ACH Return Fee'] = $amount;
		return $fees;
	}

	public static function getTxnFeeHistory(Transaction $txn = null)
	{
		$fees = [
			'Spendr Fee' => null,
			'Estimated Tern Fee' => null,
			'Refund Fee' => null,
		];
		if ($txn->getStatus()->getName() === TransactionStatus::NAME_COMPLETED) {
			$fees['Estimated Tern Fee'] = FeeService::calculateTernFeeByLoad();
		}
		$id = $txn->getId();
		$rc = new \ReflectionClass($txn);
		$entityName = $rc->getName();

		$expr = Util::expr();

		$feeHistories = Util::em()->getRepository(UserFeeHistory::class)
			->createQueryBuilder('ufh')
			->where($expr->eq('ufh.entity', ':entity'))
			->setParameter('entity', $entityName)
			->andWhere($expr->eq('ufh.entityId', ':entityId'))
			->setParameter('entityId', $id)
			->andWhere($expr->like('ufh.meta', ':spendrMeta'))
			->setParameter('spendrMeta',  '%"spendrMeta":true%')
			->andWhere($expr->in('ufh.feeName', ':feeNames'))
			->setParameter('feeNames', [
				FeeService::TO_SPENDR_TRANSACTION_FEE,
				FeeService::TO_MERCHANT_REFUND_TRANSACTION_FEE
			])
			->getQuery()
			->getResult();
		if (!$feeHistories) {
			return $fees;
		}

		/** @var UserFeeHistory $feeHistory */
		foreach ($feeHistories as $feeHistory) {
			$amount = Money::format($feeHistory->getAmount(), 'USD', false);
			if ($feeHistory->getFeeName() === FeeService::TO_SPENDR_TRANSACTION_FEE) {
				$fees['Spendr Fee'] = $amount;
			} else if ($feeHistory->getFeeName() === FeeService::TO_MERCHANT_REFUND_TRANSACTION_FEE) {
				$fees['Refund Fee'] = $amount;
			}
		}

		return $fees;
	}

	public static function LoadCard(UserCardLoad $load, $times = 3)
	{
		if ($times === 0) {
			return false;
		}

		if ($load->getLoadStatus() !== UserCardLoad::LOAD_STATUS_RECEIVED) {
			return false;
		}

		$meta = ['entity' => (array)(new EntitySignature($load))];
		$meta = Util::j2s($meta);
		if ($load->getType() === UserCardLoad::TYPE_LOAD_CARD) {
			$type = $load->getType();
		} else if ($load->getType() === UserCardLoad::TYPE_UNLOAD) {
			$type = UserCardBalance::TYPE_UNLOAD_CARD;
		} else {
			return false;
		}
		$ucbs = Util::em()->getRepository(UserCardBalance::class)
			->findBy([
				'meta' => $meta,
				'type' => $type
			]);
		if (count($ucbs)) {
			return false;
		}

		$times = $times - 1;
		$currentTimes = abs($times - 3);
		$msg = 'Start load card. Load ID: ' . $load->getId() . ', current times: ' . $currentTimes;
		$data = [
			'Load status' => $load->getLoadStatus()
		];
		if ($currentTimes > 1) {
			SlackService::info($msg, $data, [
				SlackService::MENTION_TRACY,
			]);
		} else {
			SlackService::eyes($msg, $data);
		}

		$res = Service::loadCard($load, true);
		if (is_string($res)) {
			SlackService::eyes('Error reason: ' . $res);
			self::loadCard($load, $times);
		}
		return true;
	}

	public static function afterLoad(UserCardLoad $load, $loadResult, $isRestrictAreaLoad = false)
	{
		$user = $load->getUserCard()->getUser();

		if (is_string($loadResult)) {
			SlackService::warning(
				'Load queue. Load error. Load ID: ' . $load->getId() . '.',
				[
					'Error' => $loadResult,
					'Load status' => $load->getLoadStatus(),
					'User' => $user->getId()
				],
				SlackService::GROUP_SPENDR_NEG_BALANCE
			);

			Util::updateMeta($load, [
				'errorByCheck' => true
			]);
		}

		self::updateLoadInfoWhenLoaded($load);

		if ($load->getType() === UserCardLoad::TYPE_LOAD_CARD) {
			if ($user->isSpendrConsumer()) {
				if (self::isInstantLoad($load)) {
					self::afterInstantLoad($load, $loadResult);
				} else if (self::isPrefundLoad($load)) {
					self::afterPrefundLoadSettled($load, $loadResult);
				} else if (self::isManuallyLoad($load)) {
					self::afterConsumerManuallyLoad($load, $loadResult);
				} else if (LoadService::isReferralPromotionLoad($load)) {
                    self::afterReferralLoad($load);
                } else if (self::isEarnPromotionLoad($load)) {
					self::afterEarnLoad($load, $loadResult);
				} else if (self::isRewardPromotionLoad($load)) {
					self::afterRewardLoad($load, $loadResult);
				} else if (LoadService::isLoadForUnloadFailed($load)) {
					self::afterLoadForUnloadFailed($load);
				}
			} else if ($user->isSpendrMerchantBalanceAdmin()) {
                self::afterMerchantLoad($load, $loadResult, $user);
            } else {
				return false;
			}
		} else if ($load->getType() === UserCardLoad::TYPE_UNLOAD) {
			if ($user->isSpendrConsumer()) {
				if (LoadService::isUnloadForLoadFailed($load)) {
					self::afterUnloadForLoadFailed($load);
				} else {
					self::afterConsumerUnload($load, $loadResult);
				}
			} else if ($user->isSpendrMerchantBalanceAdmin()) {
                self::afterMerchantLoad($load, $loadResult, $user);
            } else {
				return false;
			}
		} else {
			return false;
		}

		return true;
	}

    /**
     * Do other handle after merchant load
     * @param UserCardLoad $load
     * @param $loadResult
     * @param User $user
     */
    protected static function afterMerchantLoad(UserCardLoad $load, $loadResult, User $user)
    {
        if (!is_string($loadResult)) {
			$merchant = SpendrMerchant::findByAdminUser($user);
            $merchantDummy = $load->getUserCard();

            // Update merchant braze user balance
            MerchantService::updateBrazeMerchantBalance($merchant->getId(), $merchantDummy->getBalance());
		}
    }

	public static function updateLoadInfoWhenLoaded(UserCardLoad $load)
	{
		if (
			$load->getLoadStatus() === UserCardLoad::LOAD_STATUS_LOADED
			&& $load->getStatus() === UserCardLoad::STATUS_COMPLETED
		) {
			$load->setCompletedAt(new \DateTime())
				->setLoadAmount($load->getInitialAmount())
				->persist();

			// for instant load and withdraw
			if ($load->getTransactionNo()) {
				$uct = UserCardTransaction::findByTranId($load->getTransactionNo());
				$ach = AchTransactions::findTransactionByTranId($load->getTransactionNo());
				if ($uct->getAccountStatus() === UserCardTransaction::STATUS_INITIATED) {
					$uct->setAccountStatus(UserCardTransaction::STATUS_LL_RECEIVED)
						->persist();
				}
				if ($ach->getTranStatus() === UserCardTransaction::STATUS_INITIATED) {
					$ach->setTranStatus(UserCardTransaction::STATUS_LL_RECEIVED)
						->persist();
				}
			}
		}
	}

	protected static function afterLoadForUnloadFailed(UserCardLoad $load)
	{
		self::afterReturnLoad($load);
	}

	protected static function afterUnloadForLoadFailed(UserCardLoad $load)
	{
		self::afterReturnLoad($load);
	}

	protected static function afterReturnLoad(UserCardLoad $load)
	{
//		if ($load && LoadService::isUnloadForLoadFailed($load)) {
//			//
//		}
        $user = $load->getUserCard()->getUser();
        if ($user) {
            $attrs = BrazeService::getUserAttributes($user);
            BrazeService::userTrack(null, null, null, $attrs);
            SegmentService::track(null, [
                'userId' => BrazeService::getExternalPrefix() . $user->getId(),
                'traits' => $attrs
            ]);
        }
	}

	public static function changeLocationBalanceWhenMerchantLoad(UserCardLoad $load, $amount)
	{
		$uc = $load->getUserCard();
  		if (
			$uc->isSpendr()
			&& $uc->getUser()->inTeams(SpendrBundle::getMerchantBalanceAdminRoles())
			&& Util::meta($load, 'needBackToLocation')
		) {
			$locationDummy = LocationService::getLocationDummyByLocationId(Util::meta($load, 'needBackToLocation'));
			if ($locationDummy) {
				$locationDummy->updatePrivacyBalanceBy($amount, UserCardBalance::TYPE_LOAD_CARD, 'Load id: ' . $load->getId(), false, $load);
				$locationDummy->persist();
			}
		}
	}

	public static function changeLocationBalanceWhenMerchantUnload(UserCardLoad $unload, $amount)
	{
		$uc = $unload->getUserCard();
		if (
			$uc->isSpendr()
			&& $uc->getUser()->inTeams(SpendrBundle::getMerchantBalanceAdminRoles())
			&& Util::meta($unload, 'needDeductFromLocation')
		) {
			$locationDummy = LocationService::getLocationDummyByLocationId(Util::meta($unload, 'needDeductFromLocation'));
			if ($locationDummy) {
				$locationDummy->updatePrivacyBalanceBy($amount, UserCardBalance::TYPE_UNLOAD_CARD, 'Unload id: ' . $unload->getId(), false, $unload);
				$locationDummy->persist();
			}
		}
	}

	public static function getConsumerCommonLoadCount(User $user)
    {
        return Util::em()->getRepository(UserCardLoad::class)
            ->createQueryBuilder('ucl')
            ->join('ucl.userCard', 'uc')
            ->join('uc.card', 'cpct')
            ->where('uc.user = :user')
            ->andWhere('ucl.type = :type')
            ->andWhere('ucl.status = :status')
            ->andWhere('ucl.loadStatus = :loadStatus')
            ->andWhere('ucl.loadType != :loadType')
            ->andWhere(Util::expr()->eq('cpct.cardProgram',':cardProgram'))
            ->setParameter('user', $user)
            ->setParameter('type', UserCardLoad::TYPE_LOAD_CARD)
            ->setParameter('status', UserCardLoad::STATUS_COMPLETED)
            ->setParameter('loadStatus', UserCardLoad::LOAD_STATUS_LOADED)
            ->setParameter('loadType', LoadService::LOAD_TYPE_PROMOTION)
            ->setParameter('cardProgram', CardProgram::spendr())
            ->select('count(ucl)')
            ->getQuery()
            ->getSingleScalarResult();
    }

	public static function beforeMerchantManuallyLoad(
		SpendrMerchant $merchant,
		$amount
	){
		if ($amount <= 0) {
		    return 'Invalid amount.';
        }

		if (!$merchant->isActive()) {
			return 'The merchant status is not activated and cannot withdraw!';
		}

		$spendrDummy = UserService::getSpendrBalanceDummyCard('spendr');
		if (!$spendrDummy || ($spendrDummy && ($spendrDummy->getBalance() < $amount))) {
			return 'The partner balance is insufficient and cannot be loaded.';
		}

		if (!Util::meta($merchant, 'needManuallyLoad')) {
			return 'It is not allowed to perform this operation on this merchant.';
		}

		if (
			((Util::isStaging() || Util::isLocal()) && $merchant->getId() !== 61)
			|| (Util::isLive() && $merchant->getId() !== 60)
		) {
			return 'The merchant is currently unable to perform this operation.';
		}

		return true;
	}

	// todo: In the future, the corresponding amount needs to be added to each location simultaneously
	public static function merchantManuallyLoad(
		SpendrMerchant $merchant,
		$amount,
		$customMessage,
		User $currentAdmin = null
	){
		$checkRes = self::beforeMerchantManuallyLoad($merchant, $amount);
		if (is_string($checkRes)) {
			return $checkRes;
		}

		$merchantDummy = UserService::getDummyCard($merchant->getAdminUser());

		$load = $merchantDummy->ensureLoad();
		if ($load->getInitializedAt()) {
			return 'This load request had been initialized. Please start a new request from beginning.';
		}
		$load->setCreateBy($currentAdmin->getId())
			->setInitialAmount($amount)
			->setPayCurrency('USD')
			->setPayAmount($amount)
			->setInitializedAt(new \DateTime())
			->setReceivedCurrency('USD')
			->setReceivedAmount($amount)
			->setLoadStatus(UserCardLoad::LOAD_STATUS_RECEIVED)
			->setMeta(Util::j2s([
				LoadService::LOAD_TYPE_MANUALLY => true,
				'customMessage' => $customMessage
			]));

		Util::persist($load);

		Queue::spendrLoad($load->getId());
		
		return [
			'load' => $load,
			'msg' => 'The transaction is successfully created and added to the load queue.'
		];
	}

	public static function afterMerchantManuallyLoad(UserCardLoad $load, $loadResult)
	{
		// nothing need to do for now
	}

	public static function merchantManuallyLoadBalanceChange(UserCardLoad $load, $amount)
	{
		$merchantDummy = $load->getUserCard();
		if (!$merchantDummy || !$merchantDummy->isDummy()) {
			return 'Incorrect merchant dummy card.';
		}

		$merchantBalanceAdmin = $merchantDummy->getUser();
		if (!$merchantBalanceAdmin->inTeams(SpendrBundle::getMerchantBalanceAdminRoles())) {
			return 'You do not have permission to perform this operation.';
		}


		// todo - start: improve this logic in the future -----------
		$merchant = SpendrMerchant::findByAdminUser($merchantBalanceAdmin);
		if (Util::isLive() && $merchant->getId() === 60) {
			$locationId = 63;
		} else if ((Util::isStaging() || Util::isLocal()) && $merchant->getId() === 61) {
			$locationId = 31;
		} else {
			return 'The merchant is currently unable to perform this operation!';
		}
		/** @var Location $location */
		$location = Location::find($locationId);
		if (!$location) {
			return 'Invalid location.';
		}
		$locationDummy = UserService::getDummyCard($location->getAdminUser());
		if (!$locationDummy) {
			return 'Invalid location dummy card.';
		}
		// todo - end: improve this logic in the future -----------


		$loadBy = null;
		if ($load->getCreateBy()) {
			$loadByUser = User::find($load->getCreateBy());
			if ($loadByUser) {
				$loadBy = $loadByUser->getSignature();
			}
		}

		$comment = sprintf(
			'Load ID: %s. Manually load funds from partner balance to the merchant %s at %s. Load by: %s. Amount: %s.',
			$load->getId(),
			$merchantBalanceAdmin->getSignature(),
			Carbon::now(),
			$loadBy,
			Money::format($amount, 'USD'),
		);


		$partnerDummy = UserService::getSpendrBalanceDummyCard('spendr');

		$partnerDummy->updatePrivacyBalanceBy(-$amount, UserCardBalance::TYPE_LOAD_CARD, $comment, false, $load);
		$merchantDummy->updatePrivacyBalanceBy($amount, UserCardBalance::TYPE_LOAD_CARD, $comment, false, $load);
		$locationDummy->updatePrivacyBalanceBy($amount, UserCardBalance::TYPE_LOAD_CARD, $comment, false, $load);

		$partnerDummy->persist();
		$merchantDummy->persist();
		$locationDummy->persist();

		SlackService::tada($comment);

		return true;
	}
}
