<?php

namespace SpendrBundle\Services;


use Carbon\Carbon;
use CoreB<PERSON>le\Entity\CardProgram;
use Core<PERSON><PERSON>le\Entity\CardProgramCardType;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserCardBalance;
use CoreBundle\Services\SSLEncryptionService;
use CoreBundle\Utils\Util;
use phpDocumentor\Reflection\Types\Integer;
use SalexUserBundle\Entity\User;
use SpendrBundle\Entity\MerchantAdmin;
use SpendrBundle\Entity\SpendrMerchant;
use SpendrBundle\SpendrBundle;
use UsUnlockedBundle\Services\Privacy\PrivacyAPI;
use UsUnlockedBundle\Services\UserService as UsuUserService;
use CoreBundle\Utils\Log;
use Ramsey\Uuid\Uuid;

class UserService
{
	const SPENDR_BALANCE_ADMIN_EMAIL = '<EMAIL>';
	const SPENDR_TERN_BALANCE_ADMIN_EMAIL = '<EMAIL>';
	const SPENDR_BANK_BALANCE_ADMIN_EMAIL = '<EMAIL>';
	const SPENDR_BALANCE_ADMIN_EMAIL_FOR_TEST = '<EMAIL>';
	const SPENDR_TERN_BALANCE_ADMIN_EMAIL_FOR_TEST = '<EMAIL>';
	const SPENDR_BANK_BALANCE_ADMIN_EMAIL_FOR_TEST = '<EMAIL>';

	const SPENDR_BALANCE_ADMIN_EMAILS = [
		self::SPENDR_BALANCE_ADMIN_EMAIL,
		self::SPENDR_TERN_BALANCE_ADMIN_EMAIL,
		self::SPENDR_BANK_BALANCE_ADMIN_EMAIL
	];

	const SPENDR_BALANCE_ADMIN_EMAILS_FOR_TEST = [
		self::SPENDR_BALANCE_ADMIN_EMAIL_FOR_TEST,
		self::SPENDR_TERN_BALANCE_ADMIN_EMAIL_FOR_TEST,
		self::SPENDR_BANK_BALANCE_ADMIN_EMAIL_FOR_TEST
	];

	public static function isSpendrPartnerAdmin(User $user, $forTest = false)
	{
		$email = !$forTest ? self::SPENDR_BALANCE_ADMIN_EMAIL : self::SPENDR_BALANCE_ADMIN_EMAIL_FOR_TEST;
		return $user->inTeams([Role::ROLE_SPENDR_PROGRAM_OWNER])
			&& ($user->getEmail() === $email) ? true : false;
	}

	public static function getUserByMobilePhone($phone, $role = Role::ROLE_SPENDR_CONSUMER)
	{
		if (!$phone) {
			return null;
		}
		return Util::em()->getRepository(User::class)
			->createQueryBuilder('u')
			->join('u.teams', 't')
			->where(Util::expr()->in('t.name', ':roles'))
			->andWhere('u.mobilephone = :phone')
			->setParameter('roles', [
				$role,
			])
			->setParameter('phone', $phone)
			->setMaxResults(1)
			->getQuery()
			->getResult();
	}

	/**
	 * @param User $user
	 * @return \CoreBundle\Entity\UserCard
	 * @throws \PortalBundle\Exception\PortalException
	 */
	public static function getDummyCard(User $user)
	{
//        Log::debug('Get Dummy Card');
        return UsuUserService::getDummyCard($user, CardProgram::spendr());
	}

	/**
	 * @param $userType
	 * @param bool $forTest
	 * @return \CoreBundle\Entity\UserCard|null
	 * @throws \PortalBundle\Exception\PortalException
	 */
	public static function getSpendrBalanceDummyCard($userType, $forTest = false)
	{
		if (!$userType || !in_array($userType, [
				'spendr', 'tern', 'bank'
			])) {
			return null;
		}
		$admin = self::getSpendrBalanceAdminAccount($userType, $forTest);
		$dummy = null;
		if ($admin) {
			$dummy = self::getDummyCard($admin);
			if (!$dummy->getInitializedAt()) {
				$dummy->setInitializedAt(new \DateTime());
				Util::persist($dummy);
			}
		}
		return $dummy;
	}

	/**
	 * @param $userType
	 * @param bool $forTest
	 * @return mixed|User|null
	 * @throws \PortalBundle\Exception\PortalException
	 */
	public static function getSpendrBalanceAdminAccount($userType, $forTest = false)
	{
		if (!$userType
			|| !in_array($userType, [
				'spendr', 'tern', 'bank'
			])
		) {
			return null;
		}

		if ($userType === 'tern') {
			$role = Role::ROLE_ADMIN;
			$email = !$forTest ? self::SPENDR_TERN_BALANCE_ADMIN_EMAIL : self::SPENDR_TERN_BALANCE_ADMIN_EMAIL_FOR_TEST;
			$firstName = 'Tern balance admin';
		} else if ($userType === 'spendr') {
			$role = Role::ROLE_SPENDR_PROGRAM_OWNER;
			$email = !$forTest ? self::SPENDR_BALANCE_ADMIN_EMAIL : self::SPENDR_BALANCE_ADMIN_EMAIL_FOR_TEST;
			$firstName = 'Spendr balance admin';
		} else {
			$role = Role::ROLE_SPENDR_BANK;
			$email = !$forTest ? self::SPENDR_BANK_BALANCE_ADMIN_EMAIL : self::SPENDR_BANK_BALANCE_ADMIN_EMAIL_FOR_TEST;
			$firstName = 'Bank balance admin';
		}

		$users = Util::em()->getRepository(User::class)
			->createQueryBuilder('u')
			->join('u.teams', 't')
			->where(Util::expr()->in('t.name', ':roles'))
			->setParameter('roles', [$role])
			->andWhere('u.email = :email')
			->setParameter('email', $email)
			->setMaxResults(1)
			->getQuery()
			->getResult();

		$user = null;

		if ($users) {
			$user = $users[0];
		}

		if (!$user) {
			$user = new User();
			$user->setEnabled(true)
				->setPlainPassword(Util::generatePassword('SPENDR'))
				->setStatus(User::STATUS_ACTIVE)
                ->setSource('spendr_balance_admin')
				->setEmail($email)
				->setEmailVerified(true)
				->setFirstName($firstName)
				->setLastName('Spendr')
				->setUsername($email)
				->ensureRole($role);
			Util::persist($user);
			$user->ensureAccessiblePlatformProgram();
			Util::persist($user);

			$dummy = self::getDummyCard($user);

			SlackService::alert($firstName . ' could not be found, and recreated!!!',
				[
					'Admin Type' => $firstName,
					'New User ID' => $user->getId(),
					'New Dummy Card ID' => $dummy->getId()
				],
				[
					SlackService::MENTION_TRACY,
					SlackService::MENTION_JERRY
				]
			);
		}

//		if ($userType === 'spendr' || $userType === 'bank') {
//			self::bindRealBankCard($user, $userType);
//		}

		return $user;
	}

	/**
	 * spendr does not need to bind a real bank card for the time being
	 *
	 * @param User $admin
	 * @param $userType
	 * @return \CoreBundle\Entity\UserCard|null
	 * @throws \PortalBundle\Exception\PortalException
	 */
	public static function bindRealBankCard(User $admin, $userType)
	{
		if (!$admin || !in_array($userType, ['spendr', 'bank'])) {
			return null;
		}

		$dummy = self::getDummyCard($admin);

		if ($dummy->getAccountNumber() && $dummy->getDbaNo()) {
			return $dummy;
		}

		$accountNumber = '';
		$routingNumber = '';

		if (!Util::isLive() || Util::isDev()) {
//			if ($userType === 'spendr') {
				$accountNumber = 'tracytest123';
				$routingNumber = '*********';
//			} else if ($userType === 'bank') {
//
//			}
		}

		if (!$accountNumber || !$routingNumber) {
			return $dummy;
		}

		$encryptedAcctNum = SSLEncryptionService::encrypt($accountNumber);
		$encryptedRoutingNum = SSLEncryptionService::encrypt($routingNumber);

		$dummy->setAccountNumber($encryptedAcctNum)
			->setRoutingNumber($encryptedRoutingNum)
			->persist();

		return $dummy;
	}

	public static function getCurrentMerchantBalanceAdmin(User $user)
	{
		/** @var SpendrMerchant $merchant */
		$merchant = $user->getSpendrAdminMerchant();
		$balanceAdmin = $merchant->getAdminUser();
		if ($balanceAdmin) {
			$merchantAdmin = Util::em()->getRepository(MerchantAdmin::class)
				->findOneBy([
					'user' => $balanceAdmin,
					'merchant' => $merchant
				]);
			if (!$merchantAdmin) {
				$merchantAdmin = new MerchantAdmin();
				$merchantAdmin->setUser($balanceAdmin)
					->setMerchant($merchant);
				Util::persist($merchantAdmin);
			}
		}
		return $balanceAdmin;
	}

	public static function userIdsForTestArray()
	{
		if (Util::isLive()) {
			return [
				*********,
				*********,
				*********,
				*********,
				*********,
				*********,
				*********,
				*********,
				*********,
				*********,
				*********,
			];
		} else {
			return null;
		}
	}

	public static function isUserForTest(User $user)
	{
		if (Util::isLive()) {
			$testArray = self::userIdsForTestArray();

			if ($testArray && in_array($user->getId(), $testArray)) {
				return true;
			} else {
				return false;
			}
		} else {
			return false;
		}
	}

	// for merchant & partner & consumer
	public static function getUserRealCards(User $user)
	{
		if (!$user->inTeams([
			Role::ROLE_SPENDR_MERCHANT_ADMIN,
			Role::ROLE_SPENDR_PROGRAM_OWNER,
			Role::ROLE_SPENDR_CONSUMER
		])) {
			return null;
		}

		$cards = Util::em()->getRepository(UserCard::class)
			->createQueryBuilder('uc')
			->join('uc.card', 'cpct')
			->where(Util::expr()->eq('cpct.cardProgram',':cardProgram'))
			->setParameter('cardProgram', CardProgram::spendr())
			->andWhere('uc.user = :user')
			->setParameter('user', $user)
			->andWhere('uc.type != :type')
			->setParameter('type', PrivacyAPI::CARD_TYPE_DUMMY)
			->andWhere('uc.status = :status')
			->setParameter('status', UserCard::STATUS_ACTIVE)
			->orderBy('uc.id', 'desc')
			->getQuery()
			->getResult();

		return $cards ? $cards : null;
	}

    public static function isForeignCard(UserCard $uc)
    {
        $user = $uc->getUser();
        $uid = (int)Util::getConfigKey('spendr_common_user');
        return (int)($user->getId()) === $uid;
    }

    public static function ensureBankAccountId(UserCard $uc = null)
    {
        if (!$uc) {
            return Uuid::uuid4()->toString();
        }
        $meta = Util::meta($uc, 'plaidBankAccountId');
        if ($meta) {
            return $meta;
        }
        if (self::isForeignCard($uc)) {
            $id = Uuid::uuid4()->toString();
            Util::updateMeta($uc, [
                'plaidBankAccountId' => $id,
            ]);
            return $id;
        }
        return $uc->getUser()->ensureBankAccountId();
    }

    // for merchant & partner
	public static function getUserPendingCard(User $user)
    {
        if (!$user->inTeams([
            Role::ROLE_SPENDR_MERCHANT_ADMIN,
            Role::ROLE_SPENDR_PROGRAM_OWNER,
            Role::ROLE_SPENDR_CONSUMER
        ])) {
            return null;
        }
        $cards = Util::em()->getRepository(UserCard::class)
            ->createQueryBuilder('uc')
            ->join('uc.card', 'cpct')
            ->where(Util::expr()->eq('cpct.cardProgram',':cardProgram'))
            ->setParameter('cardProgram', CardProgram::spendr())
            ->andWhere('uc.user = :user')
            ->setParameter('user', $user)
            ->andWhere('uc.type = :type')
            ->setParameter('type', UserCard::LL_TYPE_PLAID)
            ->andWhere('uc.status = :status')
            ->setParameter('status', UserCard::STATUS_PENDING)
            ->andWhere('uc.meta like :meta')
            ->setParameter('meta', '%"verification_status":"pending_manual_verification"%')
            ->setMaxResults(1)
            ->orderBy('uc.id', 'desc')
            ->getQuery()
            ->getResult();

        return $cards ? $cards[0] : null;
    }

	// for merchant & partner
	public static function getUserCurrentBoundCard(User $user)
	{
		if (!$user->inTeams([
			Role::ROLE_SPENDR_MERCHANT_ADMIN,
			Role::ROLE_SPENDR_PROGRAM_OWNER
		])) {
			return null;
		}

		$cards = Util::em()->getRepository(UserCard::class)
			->createQueryBuilder('uc')
			->join('uc.card', 'cpct')
			->where(Util::expr()->eq('cpct.cardProgram',':cardProgram'))
			->setParameter('cardProgram', CardProgram::spendr())
			->andWhere('uc.user = :user')
			->setParameter('user', $user)
			->andWhere('uc.type != :type')
			->setParameter('type', PrivacyAPI::CARD_TYPE_DUMMY)
			->andWhere('uc.status = :status')
			->setParameter('status', UserCard::STATUS_ACTIVE)
			->setMaxResults(1)
			->orderBy('uc.id', 'desc')
			->getQuery()
			->getResult();

		return $cards ? $cards[0] : null;
	}

	// for merchant & partner
	public static function getUserCurrentBoundCardInfo(User $user)
	{
		if (!$user->inTeams([
			Role::ROLE_SPENDR_MERCHANT_ADMIN,
			Role::ROLE_SPENDR_PROGRAM_OWNER
		])) {
			return null;
		}

		/** @var UserCard $uc */
		$uc = self::getUserCurrentBoundCard($user);

		if (!$uc) {
			return null;
		}

		$accountNumber = $uc->getAccountNumber() ? SSLEncryptionService::tryToDecrypt($uc->getAccountNumber()) : '';
		$mask = Util::meta($uc, 'mask');
		if ($uc->getPlaidAccessToken() && $mask) {
			$accountNumber = '***' . $mask;
		}

		return [
			'bankName' => $uc->getBankName(),
			'accountNum' => $accountNumber,
			'routingNum' => $uc->getRoutingNumber() ? SSLEncryptionService::tryToDecrypt($uc->getRoutingNumber()) : null,
            'pendingCard' => !empty(self::getUserPendingCard($user))
		];
	}

    // for merchant & partner
	public static function saveCardInfo($user, $token, $uc = null)
    {
        if (!$user->inTeams([
            Role::ROLE_SPENDR_MERCHANT_ADMIN,
            Role::ROLE_SPENDR_PROGRAM_OWNER,
            Role::ROLE_SPENDR_CONSUMER
        ])) {
            return 'User role not matched.';
        }
        $info = PlaidService::getAccountInformation($token);
        if (!$info || !isset($info->accounts) || !isset($info->numbers)) {
            return 'No bank account information.';
        }
        $accountId = $info->accounts[0]->account_id;
        $bankName = $info->accounts[0]->name;
        $mask = $info->accounts[0]->mask;
        $subtype = $info->accounts[0]->subtype;
        $account = array_first($info->numbers->ach, function ($item) use ($accountId) {
            return $item->account_id == $accountId;
        });
        $accountNum = $account ? $account->account : null;
        $routingNum = $account ? $account->routing : null;
        $institutionId = $info->item->institution_id;
        $itemId = $info->item->item_id;
        if (!$accountId || !$bankName || !$accountNum || !$routingNum) {
            return 'No bank account information.';
        }

        // Save bank account info and plaid access token
        $encryptedAcctNum = SSLEncryptionService::encrypt($accountNum);
        $encryptedRouting = SSLEncryptionService::encrypt($routingNum);
//		$encryptedAcctNum = $accountNum;
//		$encryptedRouting = $routingNum;

        // Inactive the current card
        /** @var UserCard $activeCard */
        $activeCard = UserService::getUserCurrentBoundCard($user);
        if ($activeCard) {
            $activeCard->setStatus(UserCard::STATUS_INACTIVE)
                ->persist();
        }

        $meta = [
            'account_id' => $accountId,
            'mask' => $mask,
            'routing_mask' => substr($routingNum, -4),
            'institution_id' => $institutionId,
            'item_id' => $itemId,
            'subtype' => $subtype,
            'manual_link' => $uc && $uc->getStatus() == UserCard::STATUS_PENDING
        ];

        // Create the linked card as active card
        // Create the linked card as active card
        if (!$uc) {
            $uc = new UserCard();
            $uc->setUser($user)
                ->setCard(CardProgramCardType::getForCardProgram(CardProgram::spendr()))
                ->setType(UserCard::LL_TYPE_PLAID)
                ->setPlaidAccessToken($token)
				->setHash(Uuid::uuid4());
        }
        $uc->setBankName($bankName)
            ->setAccountNumber($encryptedAcctNum)
            ->setRoutingNumber($encryptedRouting)
            ->setInitializedAt(new \DateTime())
            ->setStatus(UserCard::STATUS_ACTIVE)
            ->setCurrency('USD')
            ->setMeta(Util::j2s($meta))
            ->persist();

        return [
            'bankName' => $bankName,
            'accountNum' => '***' . $mask,
            'routingNum' => $routingNum,
            'pendingCard' => false
        ];
    }

	public static function getNegativeMemberData($type = 'amount', int $max = null)
	{
		$query = Util::em()->getRepository(User::class)
			->createQueryBuilder('u')
			->join('u.teams', 't')
			->join('u.cards', 'uc')
			->join('uc.card', 'c')
			->where(Util::expr()->in('t.name', ':roles'))
			->setParameter('roles', SpendrBundle::getConsumerRoles())
			->andWhere('c.cardProgram = :cardProgram')
			->setParameter('cardProgram', CardProgram::spendr())
			->andWhere('uc.type = :type')
			->setParameter('type', PrivacyAPI::CARD_TYPE_DUMMY)
			->andWhere('uc.balance < 0');

		if ($max) {
			$query->setMaxResults($max)
				->orderBy('uc.balance', 'asc');
		}

		if ($type === 'amount') {
			$result = $query->select('count(distinct u) as total, sum(uc.balance) as totalAmount')
				->distinct()
				->getQuery()
				->getSingleResult();
		} else {
			$result = $query->distinct()->getQuery()->getResult();
		}

		return $result;
	}

	public static function getUserNegativeDayCounter(User $user)
	{
		$ucbs = Util::em()->getRepository(UserCardBalance::class)
			->createQueryBuilder('ucb')
			->join('ucb.userCard', 'uc')
			->join('uc.card', 'c')
			->where('uc.user = :user')
			->setParameter('user', $user)
			->andWhere('c.cardProgram = :cardProgram')
			->setParameter('cardProgram', CardProgram::spendr())
			->andWhere('uc.type = :type')
			->setParameter('type', PrivacyAPI::CARD_TYPE_DUMMY)
			->andWhere('uc.balance < 0')
			->andWhere('ucb.currentBalance < 0')
			->orderBy('ucb.id', 'desc')
			->setMaxResults(1)
			->getQuery()
			->getResult();

		if (!$ucbs) {
			return null;
		}

		/** @var UserCardBalance $ucb */
		$ucb = $ucbs[0];
		return Carbon::now()->diffInDays($ucb->getCreatedAt());
	}

	private static function lastWeekNegativeConsumerQuery()
	{
		$start = Carbon::now()->subWeek()->startOfWeek()->addDay();
		$end = Carbon::now()->subWeek()->endOfWeek()->addDay();

		return Util::em()->getRepository(UserCardBalance::class)
			->createQueryBuilder('ucb')
			->join('ucb.userCard', 'uc')
			->join('uc.card', 'c')
			->join('uc.user', 'u')
			->join('u.teams', 't')
			->where('c.cardProgram = :cardProgram')
			->setParameter('cardProgram', CardProgram::spendr())
			->andWhere(Util::expr()->in('t.name', ':roles'))
			->setParameter('roles', SpendrBundle::getConsumerRoles())
			->andWhere('uc.type = :type')
			->setParameter('type', PrivacyAPI::CARD_TYPE_DUMMY)
			->andWhere('ucb.createdAt >= :start')
			->andWhere('ucb.createdAt <= :end')
			->setParameter('start', Util::timeUTC($start))
			->setParameter('end', Util::timeUTC($end));
	}

	public static function totalNewNegativeUserLastWeek()
	{
		$start = Carbon::now()->subWeek()->startOfWeek()->addDay();
		$end = Carbon::now()->subWeek()->endOfWeek()->addDay();
		$expr = Util::expr();

		// before
		$minIdQuery = Util::em()->getRepository(UserCardBalance::class)
			->createQueryBuilder('ucbMin')
			->where('ucbMin.userCard = ucb.userCard')
			->andWhere('ucbMin.createdAt >= :start')
			->select('min(ucbMin.id)')
			->getDQL();

		$totalPrePositiveUserQuery = self::lastWeekNegativeConsumerQuery()
			->andWhere('ucb.previousBalance >= 0')
			->andWhere(Util::expr()->eq('ucb.id', Util::expr()->any($minIdQuery)));

		// after
		$maxIdQuery = Util::em()->getRepository(UserCardBalance::class)
			->createQueryBuilder('ucbMax')
			->where('ucbMax.userCard = ucbCur.userCard')
			->andWhere('ucbMax.createdAt <= :end')
			->select('max(ucbMax.id)')
			->getDQL();

		$totalCurNegativeUser = Util::em()->getRepository(UserCardBalance::class)
			->createQueryBuilder('ucbCur')
			->join('ucbCur.userCard', 'ucCur')
			->where('ucbCur.currentBalance < 0')
			->andWhere($expr->in(
				'ucCur.id',
				(clone $totalPrePositiveUserQuery)
					->select('uc.id')
					->getDQL()
			))
			->andWhere(Util::expr()->eq('ucbCur.id', Util::expr()->any($maxIdQuery)))
			->setParameter('cardProgram', CardProgram::spendr())
			->setParameter('roles', SpendrBundle::getConsumerRoles())
			->setParameter('type', PrivacyAPI::CARD_TYPE_DUMMY)
			->setParameter('start', Util::timeUTC($start))
			->setParameter('end', Util::timeUTC($end))
			->select('count(distinct ucbCur) total')
			->getQuery()
			->getSingleScalarResult();

		return (int)$totalCurNegativeUser;
	}

	// Sum of negative balances at the end of the week - Sum of initial balances of the week = Sum of growing negative balances of the week
	public static function totalNewNegativeBalanceLastWeek()
	{
		$start = Carbon::now()->subWeek()->startOfWeek()->addDay();
		$end = Carbon::now()->subWeek()->endOfWeek()->addDay();
		$expr = Util::expr();

		// Sum of negative balances at the end of the week
		$maxIdQuery = Util::em()->getRepository(UserCardBalance::class)
			->createQueryBuilder('ucbMax')
			->where('ucbMax.userCard = ucb.userCard')
			->andWhere('ucbMax.createdAt <= :end')
			->select('max(ucbMax.id)')
			->getDQL();

		$totalLastWeekFinalNegativeBalanceQuery = self::lastWeekNegativeConsumerQuery()
			->andWhere('ucb.currentBalance < 0')
			->andWhere(Util::expr()->eq('ucb.id', Util::expr()->any($maxIdQuery)));

		$totalLastWeekFinalNegativeBalance = (clone $totalLastWeekFinalNegativeBalanceQuery)
			->select('sum(ucb.currentBalance)')
			->getQuery()
			->getSingleScalarResult();

		// Sum of initial balances of the week
		$minIdQuery = Util::em()->getRepository(UserCardBalance::class)
			->createQueryBuilder('ucbMin')
			->where('ucbMin.userCard = ucbPre.userCard')
			->andWhere('ucbMin.createdAt >= :start')
			->select('min(ucbMin.id)')
			->getDQL();

		$totalPreBalance = Util::em()->getRepository(UserCardBalance::class)
			->createQueryBuilder('ucbPre')
			->join('ucbPre.userCard', 'ucPre')
			->where($expr->in(
				'ucPre.id',
				(clone $totalLastWeekFinalNegativeBalanceQuery)
				->select('uc.id')
				->getDQL()
			))
			->andWhere(Util::expr()->eq('ucbPre.id', Util::expr()->any($minIdQuery)))
			->setParameter('cardProgram', CardProgram::spendr())
			->setParameter('roles', SpendrBundle::getConsumerRoles())
			->setParameter('type', PrivacyAPI::CARD_TYPE_DUMMY)
			->setParameter('start', Util::timeUTC($start))
			->setParameter('end', Util::timeUTC($end))
			->select('sum(ucbPre.previousBalance)')
			->getQuery()
			->getSingleScalarResult();

		if ($totalPreBalance >= 0) {
			$balance = $totalLastWeekFinalNegativeBalance;
		} else {
			$balance = $totalLastWeekFinalNegativeBalance + $totalPreBalance;
		}

		return $balance;
	}

	// The number of users with negative balance at the beginning of this week, calculate the users with positive balance this week through the previous value
	public static function turnedPositiveUserDataLastWeek()
	{
		$start = Carbon::now()->subWeek()->startOfWeek()->addDay();
		$end = Carbon::now()->subWeek()->endOfWeek()->addDay();
		$expr = Util::expr();

		// before
		$minIdQuery = Util::em()->getRepository(UserCardBalance::class)
			->createQueryBuilder('ucbMin')
			->where('ucbMin.userCard = ucb.userCard')
			->andWhere('ucbMin.createdAt >= :start')
			->select('min(ucbMin.id)')
			->getDQL();

		$totalPreNegativeUserQuery = self::lastWeekNegativeConsumerQuery()
			->andWhere('ucb.previousBalance < 0')
			->andWhere(Util::expr()->eq('ucb.id', Util::expr()->any($minIdQuery)));

		// after
		$maxIdQuery = Util::em()->getRepository(UserCardBalance::class)
			->createQueryBuilder('ucbMax')
			->where('ucbMax.userCard = ucbCur.userCard')
			->andWhere('ucbMax.createdAt <= :end')
			->select('max(ucbMax.id)')
			->getDQL();

		$curPositiveUserQuery = Util::em()->getRepository(UserCardBalance::class)
			->createQueryBuilder('ucbCur')
			->join('ucbCur.userCard', 'ucCur')
			->where('ucbCur.currentBalance >= 0')
			->andWhere($expr->in(
				'ucCur.id',
				(clone $totalPreNegativeUserQuery)
					->select('uc.id')
					->getDQL()
			))
			->andWhere(Util::expr()->eq('ucbCur.id', Util::expr()->any($maxIdQuery)))
			->setParameter('cardProgram', CardProgram::spendr())
			->setParameter('roles', SpendrBundle::getConsumerRoles())
			->setParameter('type', PrivacyAPI::CARD_TYPE_DUMMY)
			->setParameter('start', Util::timeUTC($start))
			->setParameter('end', Util::timeUTC($end));

		$totalCurPositiveUser = (clone $curPositiveUserQuery)
			->select('count(distinct ucbCur) total')
			->getQuery()
			->getSingleScalarResult();

		// todo: optimize sql
		$allPreNegativeUserData = (clone $totalPreNegativeUserQuery)
			->select('u.id as userId, uc.id as userCardId, ucb.previousBalance')
			->getQuery()
			->getResult();

		$allCurPositiveUserData = (clone $curPositiveUserQuery)
			->select('ucCur.id as userCardId, ucbCur.currentBalance')
			->setMaxResults(20)
			->getQuery()
			->getResult();

		$preNegativeData = [];
		if ($allCurPositiveUserData && $allPreNegativeUserData) {
			foreach ($allCurPositiveUserData as $positive) {
				if (count($preNegativeData) === 20) {
					break;
				}
				foreach ($allPreNegativeUserData as $negative) {
					if ($negative['userCardId'] === $positive['userCardId']) {
						$preNegativeData[] = [
							'userId' => $negative['userId'],
							'previousBalance' => $negative['previousBalance']
						];
					}
				}
			}
		}

		Util::usort($preNegativeData, [
			'previousBalance' => 'asc',
		]);

		return [
			'total' => (int)$totalCurPositiveUser,
			'data' => $preNegativeData
		];
	}
}
