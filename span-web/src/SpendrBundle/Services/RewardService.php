<?php


namespace SpendrBundle\Services;


use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Util;
use SpendrBundle\Entity\PromotionCode;

class RewardService
{
	public static function getRewardsQuery(UserCard $dummy)
	{
		return Util::em()->getRepository(UserCardLoad::class)
			->createQueryBuilder('ucl')
			->where('ucl.userCard = :uc')
			->andWhere('ucl.type = :type')
			->andWhere('ucl.status = :status')
			->andWhere('ucl.loadStatus = :loadStatus')
            ->andWhere('ucl.loadType = :loadType')
			->setParameter('uc', $dummy)
			->setParameter('type', UserCardLoad::TYPE_LOAD_CARD)
			->setParameter('status', UserCardLoad::STATUS_COMPLETED)
			->setParameter('loadStatus', UserCardLoad::LOAD_STATUS_LOADED)
            ->setParameter('loadType', LoadService::LOAD_TYPE_PROMOTION);
	}

	/**
	 * @param UserCardLoad $load
	 * @return array
	 */
	public static function getRowData(UserCardLoad $load)
	{
		$tz = Util::timezone();
		$amount = $load->getInitialAmountText(false);
		$code = $load->getPromoCode() ? $load->getPromoCode() : null;
		$list = [
			[
				'title' => 'Reward Source',
				'value' => 'Spendr'
			],
			[
				'title' => 'Reward Total',
				'value' => $amount,
			],
			[
				'title' => 'Time of Reward',
				'value' => Util::formatDateTime(
					$load->getInitializedAt(),
					Util::DATE_TIME_FORMAT,
					$tz
				)
			]
		];
		if ($code) {
			array_unshift($list, [
				'title' => 'Promo Code',
				'value' => $code->getPromoCode()
			]);
		}
		return [
			'id' => $load->getId(),
			'title' => Util::meta($load, 'rewardTitle') ?? (Util::meta($load, 'customMessage') ?? 'Promo Load'),
			'type' => 'Reward',
			'promo_code' => $code ? $code->getPromoCode() : null,
			'typeDisplayName' => 'Reward',
			'amountText' => $amount,
			'date' => Util::formatDateTime(
				$load->getInitializedAt(),
				Util::DATE_FORMAT,
				$tz
			),
            'descriptor_name' => $code ? $code->getName() : '',
            'amount' => $code ? Money::formatAmountToNumber($code->getPromoCodeAmount()) : 0,
			'displayList' => $list
		];
	}

	public static function getPromoByPromoCode($promoCode)
	{
		return Util::em()->getRepository(PromotionCode::class)
			->createQueryBuilder('p')
			->orderBy('p.id', 'desc')
			->where('p.promoCode = :code')
			->andWhere('p.status = :status')
			->setParameter('code', $promoCode)
			->setParameter('status', PromotionCode::STATUS_ACTIVE)
			->getQuery()
			->getResult();
	}
}
