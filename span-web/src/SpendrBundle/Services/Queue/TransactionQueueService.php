<?php

namespace SpendrBundle\Services\Queue;

use Clf<PERSON><PERSON>le\Entity\Transaction;
use ClfB<PERSON>le\Entity\TransactionStatus;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Log;
use PortalBundle\Exception\PortalException;
use SpendrBundle\Services\ChangeBalanceService;
use SpendrBundle\Services\LoadService;
use SpendrBundle\Services\SlackService;
use SpendrBundle\Services\TransactionService;

class TransactionQueueService
{
    public static function checkBeforeRestrictAreaTxn($msg)
    {
        $txnToken = $msg['txnToken'] ?? null;
		$loadId = $msg['loadId'] ?? null;
		$appId = $msg['appId'] ?? null;
		$timezone = $msg['timezone'] ?? null;

        $data = [
            'success' => false,
            'error' => '',
            'load' => null,
            'txn' => null,
            'userId' => null,
        ];

        // check load --- start
		$load = UserCardLoad::find($loadId);
		if (!$load) {
            $data['error'] = 'Load not found';
			return $data;
		}
        $data['load'] = $load;

		$user = $load && $load->getUserCard() ? $load->getUserCard()->getUser() : null;
		if (!$user) {
            $data['error'] = 'Invalid user';
			return $data;
		}
        $data['userId'] = $user->getId();

		$errorMsg = LoadQueueService::checkLoad($load, $user);
		SlackService::prepareForPlatform(Platform::spendr());
		if (is_string($errorMsg)) {
            $data['error'] = $errorMsg;
			return $data;
		}
		// check load --- end

        // check txn --- start
        /** @var Transaction $t */
        $t = Transaction::findByToken($txnToken);
        if (!$t) {
			Log::debug(
				'=== Consumer Transaction(Restrict area): Transaction not found.',
				['txnToken' => $txnToken]
			);
            $data['error'] = 'Transaction not found';
			return $data;
		}
        $data['txn'] = $t;

        $txnId = $t->getId();
		$consumer = $t->getConsumer();
        if (!$consumer) {
			Log::debug(
				'=== Consumer Transaction(Restrict area): No consumer is bound.',
				[
					'txnToken' => $txnToken,
					'txnId' => $txnId
				]
			);
            $data['error'] = 'Transaction: No consumer is bound';
			return $data;
		}
		// check txn --- end

        $checkRes = TransactionService::beforePay($consumer, $txnToken, $timezone, 0, false, true);
        if (is_string($checkRes)) {
        	Log::debug('=== Consumer Transaction(Restrict area): ' . $checkRes, [
        		'txnId' => $txnId,
				'txnToken' => $txnToken
			]);
            $data['error'] = $checkRes;
        	return $data;
        }

        $data['success'] = true;
        return $data;
    }

    public static function afterRestrictAreaTxnFinished($txnToken, $loadId)
    {
		$transaction = Transaction::findByToken($txnToken);
		if (
			$transaction->getStatus()->getName() === TransactionStatus::NAME_COMPLETED
			&& $transaction->getTxnTime()
		) {
            Log::debug('=== Consumer Transaction(Restrict area): Successed.', [
				'txnId' => $transaction->getId(),
				'txnToken' => $txnToken
			]);
		} else {
            self::cancelLoad($transaction, $loadId);
		}
    }

    private static function cancelLoad($transaction, $loadId)
    {
        $load = UserCardLoad::find($loadId);
        $userId = $transaction->getConsumer()->getId();
        $checkRes = LoadService::beforeCancelLoad($load);
        if (is_string($checkRes)) {
            SlackService::alert('Spendr Cancel Restrict Load error: ' . $checkRes . '.', [
                'LoadId' => $load->getId(),
                'LoadStatus' => $load->getLoadStatus(),
                'User' => $userId,
            ], SlackService::GROUP_DEVS);
            return false;
        }

        try {
            LoadService::cancelLoad($load, 'Restricted area transaction failed');

            Log::debug('Spendr Cancel Restrict Load: Finished.', [
                'LoadId' => $load->getId(),
                'LoadStatus' => $load->getLoadStatus(),
                'User' => $userId,
            ]);
        } catch (PortalException $exception) {
            Log::exception($exception->getMessage(), $exception);
        } catch (\Throwable $throwable) {
            SlackService::exception(
                'Failed to cancel restrict load ' . $load->getId() . ': ' . $throwable->getMessage(),
                $throwable,
                [
                    'load' => $load->getId(),
                ],
                true,
                SlackService::GROUP_DEVS
            );
        }
    }
}