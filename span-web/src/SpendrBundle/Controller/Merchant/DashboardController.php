<?php


namespace Spendr<PERSON><PERSON>le\Controller\Merchant;

use Carbon\Carbon;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Entity\UserFeeHistory;
use CoreBundle\Response\DeniedResponse;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Util;
use PortalBundle\Exception\DeniedException;
use SalexUserBundle\Entity\User;
use SpendrBundle\Entity\SpendrMerchant;
use SpendrBundle\Entity\SpendrTransaction;
use SpendrBundle\Entity\Terminal;
use SpendrBundle\Services\FeeService;
use SpendrBundle\Services\LoadService;
use SpendrBundle\Services\MerchantService;
use SpendrBundle\Services\SlackService;
use SpendrBundle\Services\UserService;
use SpendrBundle\SpendrBundle;
use SpendrBundle\Traits\TransactionTrait;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use ClfBundle\Entity\Transaction;
use CoreBundle\Utils\Money;
use SpendrBundle\Entity\Location;
use SpendrBundle\Services\LocationAdminService;
use SpendrBundle\Services\LocationService;
use UsUnlockedBundle\Services\Privacy\PrivacyAPI;

class DashboardController extends BaseController
{
	use TransactionTrait;

	/**
	 * @Route("/admin/spendr/merchant/dashboard/summaryData", methods={"GET"})
	 * @param Request $request
	 * @return SuccessResponse
	 * @throws \Doctrine\ORM\NoResultException
	 * @throws \Doctrine\ORM\NonUniqueResultException
	 * @throws \PortalBundle\Exception\PortalException
	 */
    public function summaryData(Request $request)
    {
        $result['quick'] = [
			'totalTerminals' => null,
			'totalConsumers' => null,
			'totalLocations' => null,
			'totalMerchantRevenue' => null,
			'estimatedTernFees' => null,
			'spendrBalance' => null,
			'spendrRevenue' => null,
//			'spendrGrossProfit' => null,
			'bankFees' => null,
			'totalMerchants' => null,
			'locations' => null,
			'totalTransactions' => null,
			'partnerBoundCard' => null,
            'partnerPendingCard' => false,
			'totalNegativeMembers' => null,
			'totalNegativeMemberBalance' => null,
			'totalLocationBalance' => null,
			'isLocationHasDummyMerchant' => false,
		];

		$start = $request->get('start') ? Carbon::parse($request->get('start')) : null;
		$end = $request->get('end') ? Carbon::parse($request->get('end')) : null;

		$result['quick']['totalTransactions'] = $this->getTransactionData($request, $this->user, 'all', 'all', 'count');
		$result['quick']['totalMerchantRevenue'] = $this->traitGetMerchantTotalRevenue($this->user, $request);

		if (!$this->user->inTeam(Role::ROLE_SPENDR_BANK)) {
			$totalConsumers = (clone $this->getConsumerQuery($request))->select('count(distinct t)')
				->groupBy('u.id')
				->getQuery()
				->getResult();
			$totalLocation = (clone $this->getLocationQuery($request))->select('count(distinct l)')
				->getQuery()
				->getSingleScalarResult();
			$result['quick']['totalTerminals'] = $this->getTotalTerminals($request);
			$result['quick']['totalConsumers'] = count($totalConsumers);
			$result['quick']['totalLocations'] = $totalLocation;
		}

		if ($this->user->isMasterAdmin()) {
			$result['quick']['estimatedTernFees'] = FeeService::estimatedTernFees($start, $end);
		}

        if (
        	$this->user->isMasterAdmin()
			|| $this->user->inTeams(SpendrBundle::getSpendrAdminRoles())
		) {
        	$spendrDummy = UserService::getSpendrBalanceDummyCard('spendr');
			$result['quick']['spendrBalance'] = $spendrDummy ? $spendrDummy->getBalance() : null;
			$result['quick']['spendrRevenue'] = FeeService::calculateSpendrRevenue($start, $end);
//			$result['quick']['spendrGrossProfit'] = FeeService::calculateSpendrGrossProfit($start, $end);
			$result['quick']['totalMerchants'] = $this->getTotalMerchants($request);
			$result['quick']['bankFees'] = FeeService::getCurrentBankFees($start, $end);
			$negativeMemberData = UserService::getNegativeMemberData();
			$result['quick']['totalNegativeMembers'] = $negativeMemberData && $negativeMemberData['total']
				? $negativeMemberData['total'] : null;
			$result['quick']['totalNegativeMemberBalance'] = $negativeMemberData && $negativeMemberData['totalAmount']
				? $negativeMemberData['totalAmount'] : null;
		}

        if (
        	$this->user->isMasterAdmin()
			|| $this->user->inTeams([Role::ROLE_SPENDR_PROGRAM_OWNER])
		) {
        	$partner = UserService::getSpendrBalanceAdminAccount('spendr');
        	$result['quick']['partnerBoundCard'] = UserService::getUserCurrentBoundCardInfo($partner);
		}

        if ($this->user->inTeams(SpendrBundle::getMerchantAdminRoles())) {
			$result['quick']['locations'] = $this->getLocations($request);

			$merchant = $this->user->getSpendrAdminMerchant();
			if (MerchantService::isLocationHasDummyMerchant($merchant)) {
				$result['quick']['isLocationHasDummyMerchant'] = true;
				$totalLocationBalance = (clone $this->getLocationQuery($request))
				->select('sum(uc.balance)')
				->getQuery()
				->getSingleScalarResult();
				$result['quick']['totalLocationBalance'] = $totalLocationBalance;
			}
		}

        return new SuccessResponse($result);
    }

//	/**
//	 * @Route("/admin/spendr/merchant/dashboard/check-summary-data", methods={"GET"})
//	 * @return SuccessResponse
//	 * @throws \PortalBundle\Exception\PortalException
//	 */
//    public function checkSummaryData()
//	{
//		$this->authSuperAdmin();
//
//		$ternFee = $this->calculateTernFee() ?: [];
//		$spendrFee = $this->calculateSpendrFee() ?: [];
//		$bankFee = $this->calculateBankFee() ?: [];
//		$merchantRevenue = $this->calculateMerchantRevenue() ?: [];
//
//		$data = array_merge($ternFee, $spendrFee, $bankFee, $merchantRevenue);
//		SlackService::eyes('Check Summary Data', $data);
//
//		return new SuccessResponse($data);
//	}

//	protected function calculateTernFee()
//	{
//		$ternDummy = UserService::getSpendrBalanceDummyCard('tern');
//		$balance = $ternDummy ? $ternDummy->getBalance() : null;
//
//		$feeAmount = $this->getFeeData(SpendrBundle::getRoles(), FeeService::ternFeeNames(), 'amount');
//
//		$loadCount = $this->getLoadData(
//			SpendrBundle::getConsumerRoles(),
//			UserCardLoad::TYPE_LOAD_CARD,
//			'count'
//		);
//
//		$transactionCount = $this->em->getRepository(Transaction::class)
//			->createQueryBuilder('t')
//			->join('t.merchant', 'm')
//			->join('m.adminUser', 'u')
//			->join('u.teams', 'r')
//			->where('r.name = :role')
//			->setParameter('role', Role::ROLE_SPENDR_MERCHANT_ADMIN)
//			->select('count(distinct t)')
//			->getQuery()
//			->getSingleScalarResult();
//
//		$merchantWithdrawCount = $this->getLoadData(
//			SpendrBundle::getMerchantBalanceAdminRoles(),
//			UserCardLoad::TYPE_UNLOAD,
//			'count'
//		);
//
//		// todo: TO_TERN_MERCHANT_MONTHLY_ON_FILE_FEE
//		$should = ($loadCount + $transactionCount + $merchantWithdrawCount) * 10;
//
//		return [
//			'tern' => [
//				'ternBalance' => Money::format($balance, 'USD', false),
//				'ternFeeByCalculate' => Money::format($should, 'USD', false),
//				'ternUfhFee' => Money::format($feeAmount, 'USD', false),
//				'diff' => Money::format($feeAmount - $balance, 'USD', false)
//			]
//		];
//	}

//	protected function calculateSpendrFee() {
//		$expr = Util::expr();
//
//		$spendrDummy = UserService::getSpendrBalanceDummyCard('spendr');
//		$balance = $spendrDummy ? $spendrDummy->getBalance() : null;
//
//		$spendrRevenue = FeeService::calculateSpendrGrossProfit();
//
//		$partnerLoadAmount = $this->em->getRepository(SpendrTransaction::class)
//			->createQueryBuilder('st')
//			->where('st.status = :status')
//			->setParameter('status', SpendrTransaction::STATUS_COMPLETED)
//			->andWhere('st.credit > 0')
//			->andWhere($expr->like('st.description', ':desc'),)
//			->setParameter('desc', 'Wire In%SPENDR LLC')
//			->select('sum(st.credit)')
//			->getQuery()
//			->getSingleScalarResult();
//
//		return [
//			'partner' => [
//				'partnerBalance' => Money::format($balance, 'USD', false),
//				'partnerRevenue' => Money::format($spendrRevenue, 'USD', false),
//				'partnerLoadAmount' => Money::format($partnerLoadAmount, 'USD', false),
//				'diff' => Money::format(
//					$balance - ($spendrRevenue + $partnerLoadAmount),
//					'USD',
//					false
//				)
//			]
//		];
//	}

//	protected function calculateBankFee() {
//    	$bankFee = FeeService::calculateBankFee();
//		$spendrDummy = UserService::getSpendrBalanceDummyCard('bank');
//		$balance = $spendrDummy ? $spendrDummy->getBalance() : null;
//		return [
//			'bank' => [
//				'bankBalance' => Money::format($balance, 'USD', false),
//				'bankFee' => Money::format($bankFee, 'USD', false),
//				'diff' => Money::format($bankFee - $balance, 'USD', false),
//			]
//		];
//	}

//	protected function calculateMerchantRevenue() {
//    	$merchantRevenue = $this->traitGetMerchantTotalRevenue($this->user, null, true);
//
//    	$query = $this->em->getRepository(SpendrMerchant::class)
//			->createQueryBuilder('sm');
//		$testIds = MerchantService::merchantIdsForTestArray();
//    	if ($testIds) {
//    		$query->where(Util::expr()->notIn('sm.id', ':ids'))
//				->setParameter('ids', $testIds);
//		}
//		$merchants = $query->getQuery()
//			->getResult();
//
//    	$merchantBalance = 0;
//    	if ($merchants) {
//    		/** @var SpendrMerchant $merchant */
//			foreach ($merchants as $merchant) {
//				if (!$merchant->getAdminUser()) {
//					continue;
//				}
//				$dummy = UserService::getDummyCard($merchant->getAdminUser());
//				$balance = $dummy->getBalance();
//				$merchantBalance += $balance;
//			}
//		}
//
//		$merchantUnload = $this->getLoadData(
//			SpendrBundle::getMerchantBalanceAdminRoles(),
//			UserCardLoad::TYPE_UNLOAD,
//			'amount',
//			[
//				UserCardLoad::LOAD_STATUS_LOADED,
//			]
//		);
//
//		return [
//			'merchant' => [
//				'merchantBalance' => Money::format($merchantBalance, 'USD', false),
//				'merchantRevenue' => Money::format($merchantRevenue, 'USD', false),
//				'merchantUnloadAmount' => Money::format($merchantUnload, 'USD', false),
//				'diff' => Money::format(
//					$merchantRevenue - $merchantUnload - $merchantBalance,
//					'USD',
//					false
//				),
//			]
//		];
//	}

	protected function getLoadData($roles, $type, $dataType = 'count', $status = [])
	{
		$expr = Util::expr();
		$select = $dataType === 'count' ? 'count(distinct ucl)' : 'sum(ucl.initialAmount)';

		$query = $this->em->getRepository(UserCardLoad::class)
			->createQueryBuilder('ucl')
			->join('ucl.userCard', 'uc')
			->join('uc.user', 'u')
			->join('u.teams', 't')
			->join('uc.card', 'c')
			->join('c.cardProgram', 'cp')
			->where($expr->in('t.name', ':roles'))
			->setParameter('roles', $roles)
			->andWhere($expr->eq('cp.name',':name'))
			->setParameter('name', CardProgram::NAME_SPENDR)
			->andWhere($expr->eq('ucl.type', ':type'))
			->setParameter('type', $type);
		if ($status) {
			$query->andWhere($expr->in('ucl.loadStatus', ':status'))
				->setParameter('status', $status);
		}
		return $query->select($select)
			->getQuery()
			->getSingleScalarResult();
	}

	protected function getFeeData($roles, $feeNames, $dataType = 'count')
	{
		$expr = Util::expr();
		$select = $dataType === 'count' ? 'count(distinct ufh)' : 'sum(ufh.amount)';

		return $this->em->getRepository(UserFeeHistory::class)
			->createQueryBuilder('ufh')
			->join('ufh.user', 'u')
			->join('u.teams', 't')
			->where($expr->in('t.name', ':roles'))
			->setParameter('roles', $roles)
			->andWhere($expr->in('ufh.feeName', ':feeNames'))
			->setParameter('feeNames', $feeNames)
			->andWhere($expr->like('ufh.meta', ':meta'))
			->setParameter('meta',  '%"spendrMeta":true%')
			->select($select)
			->getQuery()
			->getSingleScalarResult();
	}

    protected function getConsumerQuery(Request $request)
    {
		$user = $this->user;
		$expr = Util::expr();
		if ($user->inTeams(SpendrBundle::getMerchantAdminRoles())) {
			$query = $this->em->getRepository(Transaction::class)
				->createQueryBuilder('t')
				->join('t.merchant', 'm')
				->join('t.consumer', 'u');
			
			$query = MerchantService::generateMergeMerchantQuery($query, 't.merchant', $user->getSpendrAdminMerchant());

			$locationId = $request->get('locationId', 'all');
			if ($locationId === 'all') {
				if ($user->inTeams(SpendrBundle::getMerchantDashboardAdminRoles())) {
					$query->join('t.location', 'l')
					->join('l.admins', 'locationAdmin')
					->andWhere('locationAdmin.id = :uid')
					->setParameter('uid', $user->getId());
				}
			} else {
				if (is_numeric($locationId)) {
					$location = Location::find($locationId);
					$query->andWhere('t.location = :location')
						->setParameter('location', $location);
				}
			}
		} else {
			$query = $this->em->getRepository(User::class)
				->createQueryBuilder('u')
				->join('u.teams', 't')
				->where($expr->in('t.name', ':roles'))
				->setParameter('roles', SpendrBundle::getConsumerRoles());
		}

		$query->join('u.cards', 'uc')
			->join('uc.card', 'c')
			->andWhere('c.cardProgram = :cardProgram')
			->setParameter('cardProgram', Util::cardProgram());

        return $this->queryByDateRange($query, 'u.createdAt', $request);
    }

	// todo: optimize logic
    protected function getLocationQuery(Request $request, $list = false)
    {
        $query = $this->em->getRepository(Location::class)
            ->createQueryBuilder('l')
            ->join('l.address', 'a')
            ->join('l.merchant', 'm');

		$testIds = MerchantService::merchantIdsForTestArray();
		if ($testIds) {
			$query->where(Util::expr()->notIn('m.id', ':ids'))
				->setParameter('ids', $testIds);
		}

		$user = $this->user;

		if ($user->inTeams(SpendrBundle::getMerchantAdminRoles())) {
			$merchant = $user->getSpendrAdminMerchant();
			$query = MerchantService::generateMergeMerchantQuery($query, 'l.merchant', $merchant);

			$locationId = $request->get('locationId', 'all');
			if (!$list) {
				if ($locationId) {
					if ($locationId !== 'all' && is_numeric($locationId)) {
						$query->andWhere('l.id = :id')
							->setParameter('id', $locationId);
					}

					if (MerchantService::isLocationHasDummyMerchant($merchant)) {
						$query->join('l.adminUser', 'balanceUser')
						->join('balanceUser.cards', 'uc')
						->join('uc.card', 'c')
						->andWhere('c.cardProgram = :cardProgram')
						->setParameter('cardProgram', Util::cardProgram())
						->andWhere('uc.type = :type')
						->setParameter('type', PrivacyAPI::CARD_TYPE_DUMMY);
					}
				}
			}

			if ($list || $locationId === 'all') {
				if ($user->inTeams(SpendrBundle::getMerchantDashboardAdminRoles())) {
					$query->join('l.admins', 'u')
					->andWhere('u.id = :uid')
					->setParameter('uid', $user->getId());
				}
			}
		}

		return $this->queryByDateRange($query, 'l.createdAt', $request);
    }

    protected function getLocations(Request $request) {
    	$locations = (clone $this->getLocationQuery($request, true))
			->getQuery()
			->getResult();
    	$data = [];
    	$data[] = [
    		'label' => 'All',
			'value' => 'all'
		];
    	if ($locations) {
    		/** @var Location $location */
			foreach ($locations as $location) {
				$data[] = [
					'label' => $location->getName(),
					'value' => $location->getId(),
				];
			}
		}
    	return $data;
	}

    protected function getTotalMerchants(Request $request)
	{
    	$query = $this->em->getRepository(SpendrMerchant::class)
			->createQueryBuilder('sm')
			->join('sm.adminUser', 'u');

		$testIds = MerchantService::merchantIdsForTestArray();
		if ($testIds) {
			$query->where(Util::expr()->notIn('sm.id', ':ids'))
				->setParameter('ids', $testIds);
		}

    	$query = $this->queryByDateRange($query, 'sm.createdAt', $request);

    	return $query->select('count(distinct sm)')
			->getQuery()
			->getSingleScalarResult();
	}

	protected function getTotalTerminals(Request $request)
	{
		$query = $this->em->getRepository(Terminal::class)
			->createQueryBuilder('t')
			->join('t.location', 'l')
			->join('l.merchant', 'm');

		$testIds = MerchantService::merchantIdsForTestArray();
		if ($testIds) {
			$query->where(Util::expr()->notIn('m.id', ':ids'))
				->setParameter('ids', $testIds);
		}

		if ($this->user->inTeams(SpendrBundle::getMerchantAdminRoles())) {
			$merchant = $this->user->getSpendrAdminMerchant();
			
			$query = MerchantService::generateMergeMerchantQuery($query, 'l.merchant', $merchant);

			$locationId = $request->get('locationId', 'all');
			if ($locationId === 'all') {
				if ($this->user->inTeams(SpendrBundle::getMerchantDashboardAdminRoles())) {
					$query
					->join('l.admins', 'locationAdmin')
					->andWhere('locationAdmin.id = :uid')
					->setParameter('uid', $this->user->getId());
				}
			} else {
				if (is_numeric($locationId)) {
					$location = Location::find($locationId);
					$query->andWhere('t.location = :location')
						->setParameter('location', $location);
				}
			}
		}

		$query = $this->queryByDateRange($query, 't.createdAt', $request);

		return $query->select('count(distinct t)')
			->getQuery()
			->getSingleScalarResult();
	}

	/**
	 * @Route("/admin/spendr/merchant/withdraw/prepare", methods={"GET"})
	 *
	 * @param Request $request
	 * @return SuccessResponse
	 * @throws \PortalBundle\Exception\PortalException
	 */
	public function prepareWithdrawData(Request $request)
	{
		$data = [
			'balance' => 0,
			'locations' => [],
			'needSelectLocation' => false,
		];
		if ($this->user->inTeams(SpendrBundle::getMerchantAdminRoles())) {
			$merchant = $this->user->getSpendrAdminMerchant();

			$merchantBalance = MerchantService::getMerchantBalance($merchant, null, 'merge');

			if (MerchantService::isLocationHasDummyMerchant($merchant)) {
				$locationAdmin = $this->user->inTeams(SpendrBundle::getMerchantBalanceAdminRoles()) ? null : $this->user;
				$data['locations'] = [];
				$locations = LocationService::listLocationsForSelection($merchant, $locationAdmin, true);
				if ($locations) {
					foreach ($locations as $location) {
						$location['balance'] = (int)$location['balance'];
						$data['locations'][] = $location;
					}
				}
				$data['needSelectLocation'] = true;
				if ($this->user->inTeams(SpendrBundle::getMerchantBalanceAdminRoles())) {
					$data['balance'] = $merchantBalance;
				}
			} else {
				$data['balance'] = $merchantBalance;
			}
		}
		return new SuccessResponse($data);
	}

	/**
	 * @Route("/admin/spendr/merchant/withdraw", methods={"POST"})
	 *
	 * @param Request $request
	 * @return FailedResponse|SuccessResponse
	 * @throws DeniedException
	 * @throws \PortalBundle\Exception\PortalException
	 * @throws \Throwable
	 */
	public function merchantWithdraw(Request $request)
	{
		if (SpendrBundle::isSpendrAdminLoggedInAs()) {
			return new DeniedResponse();
		}

		$roles = SpendrBundle::getMerchantAdminRoles();
		$key = array_search(Role::ROLE_SPENDR_MERCHANT_OTHER_ADMIN, $roles);
		unset($roles[$key]);
		$this->authRoles($roles);

		$merchant = $this->user->getSpendrAdminMerchant();
		return LoadService::merchantUnload($request, $merchant, $this->user);
	}

	/**
	 * @Route("/admin/spendr/merchant/dashboard/balance-chart")
	 *
	 * @param Request $request
	 * @return SuccessResponse
	 */
	public function getBalanceChart(Request $request)
	{
		$res = [
//			'chartData' => [],
//			'chartAvg' => [],
//			'chartCount' => [],
			'balance' => 0
		];

		$period = $request->get('period');
		$type = $request->get('type');

		$res['balance'] = $this->getProgramBalance($type);

		return new SuccessResponse($res);
	}

	protected function getProgramBalance($type) {
		$balance = 0;
		if ($type === 'merchantBalance') {
			if ($this->user->inTeams(SpendrBundle::getMerchantAdminRoles())) {
				$merchant = $this->user->getSpendrAdminMerchant();
				$merchantBalance = MerchantService::getMerchantBalance($merchant, null, 'merge');

				if (MerchantService::isLocationHasDummyMerchant($merchant)) {
					if ($this->user->inTeams(SpendrBundle::getMerchantBalanceAdminRoles())) {
						$balance = $merchantBalance;
					}
				} else {
					$balance = $merchantBalance;
				}
			}
		}
		if ($type === 'partnerBalance') {
			if ($this->user->inTeams([
				Role::ROLE_MASTER_ADMIN,
				Role::ROLE_SPENDR_PROGRAM_OWNER
			])) {
				$dummy = UserService::getSpendrBalanceDummyCard('spendr');
				$balance = $dummy ? $dummy->getBalance() : 0;
			}
		}
		return $balance;
	}
}
