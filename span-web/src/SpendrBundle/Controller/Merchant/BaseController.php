<?php

namespace Spendr<PERSON><PERSON>le\Controller\Merchant;

use CoreBundle\Controller\ListControllerTrait;
use CoreBundle\Entity\Role;
use PortalBundle\Exception\DeniedException;
use SpendrBundle\Entity\SpendrMerchant;
use SpendrBundle\Services\MerchantService;
use Spendr<PERSON><PERSON>le\SpendrBundle;

class BaseController extends \SpendrBundle\Controller\BaseController
{
    use ListControllerTrait;

    protected ?SpendrMerchant $currentMerchant = null;

    /**
     * BaseController constructor.
     */
    public function __construct()
    {
        parent::__construct();

        if ($this->protected) {
            $this->user = $this->authSuperOrOtherAdmins();
        }
    }

	public function authSuperOrOtherAdmins()
	{
		return $this->authSuperAdminOrAdmins(
			array_merge(
				[
					Role::ROLE_SPENDR_BANK
				],
				SpendrBundle::getSpendrAdminRoles(),
				SpendrBundle::getMerchantAdminRoles()
			)
		);
	}

	public function authAdminsWithoutBank()
	{
		return $this->authSuperAdminOrAdmins(
			array_merge(
				SpendrBundle::getSpendrAdminRoles(),
				SpendrBundle::getMerchantAdminRoles()
			)
		);
	}

	public function authTopLevelAdmins()
    {
        return $this->authSuperAdminOrAdmins([
            Role::ROLE_SPENDR_PROGRAM_OWNER,
            Role::ROLE_SPENDR_COMPLIANCE
        ]);
    }

	public function forbiddenSpendrEmployeeCS()
	{
		if (
			$this->user->inTeams(SpendrBundle::getSpendrEmployeeCSRoles())
			|| SpendrBundle::isSpendrEmployeeCSLoggedInAs()
		) {
			throw new DeniedException();
		}
	}

    public function getCurrentMerchant()
    {
        if ( ! $this->currentMerchant) {
            $this->currentMerchant = $this->user->getSpendrAdminMerchant();
        }
        return $this->currentMerchant;
    }

    public function getCurrentMerchantPrimaryAdmin()
    {
        return $this->getCurrentMerchant()->getAdminUser();
    }
}
