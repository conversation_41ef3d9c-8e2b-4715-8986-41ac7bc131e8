<?php


namespace Spendr<PERSON><PERSON>le\Controller\Merchant;

use CoreBundle\Entity\Role;
use CoreBundle\Response\DeniedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\QueryListParams;
use CoreBundle\Utils\Util;
use Doctrine\ORM\QueryBuilder;
use SpendrB<PERSON>le\SpendrBundle;
use SpendrBundle\Traits\TransactionTrait;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use SpendrBundle\Entity\Location;
use CoreBundle\Entity\Address;
use CoreBundle\Entity\Country;
use CoreBundle\Entity\State;
use SpendrBundle\Entity\SpendrMerchant;
use PortalBundle\Exception\PortalException;
use SpendrBundle\Services\LocationService;
use SpendrBundle\Services\MerchantService;
use SpendrBundle\Services\UserService;

class LocationsController extends BaseController
{
	public function __construct()
	{
		parent::__construct();

		$this->authAdminsWithoutBank();
	}

	use TransactionTrait;

	/**
	 * @Route("/admin/spendr/merchant/locations/list/{page}/{limit}", methods={"GET"}, defaults={"page" = 1,"limit" = 10})
	 * @param Request $request
	 * @param int $page
	 * @param int $limit
	 *
	 * @return SuccessResponse
	 * @throws \Doctrine\ORM\NoResultException
	 * @throws \Doctrine\ORM\NonUniqueResultException
	 */
    public function search(Request $request, $page = 1, $limit = 10)
    {
        $resp = $this->traitSearch($request, [], $page, $limit);
        $result = $resp->getResult();
        $query = $this->query($request);
        $total = (int)((clone $query)->select('count(distinct l)')
                ->getQuery()
                ->getSingleScalarResult());

        $totalRevenue = (int)($this->traitGetMerchantTotalRevenue($this->user, $request));
		$averageRevenue = round(Util::percent($totalRevenue, $total), 2);

        $merchant = $this->user->getSpendrAdminMerchant();
        $result['quick'] = [
            'total' => $total,
            'totalRevenue' => $totalRevenue,
            'averageRevenue' => $averageRevenue,
            'showBalance' => $merchant && MerchantService::isLocationHasDummyMerchant($merchant),
		];
        return new SuccessResponse($result);
    }

    protected function query(Request $request)
    {
        $merchant = $this->user->getSpendrAdminMerchant();
        $query = $this->em->getRepository(Location::class)
            ->createQueryBuilder('l')
            ->join('l.address', 'a')
            ->join('l.merchant', 'm');
        
        $query = MerchantService::generateMergeMerchantQuery($query, 'l.merchant', $merchant);

        if ($this->user->inTeams(SpendrBundle::getMerchantDashboardAdminRoles())) {
            $query->join('l.admins', 'locationAdmin')
            ->andWhere('locationAdmin.id = :uid')
            ->setParameter('uid', $this->user->getId());
        }
        return $query;
    }

    protected function queryListParams(QueryBuilder $query, Request $request)
    {
        $params = new QueryListParams($query, $request, 'l', 'count(distinct l)');
        $params->distinct = true;
        $params->orderBy = [
            'l.id' => 'desc',
        ];

		$keyword = $request->get('keyword');
		$keyword = addslashes($keyword);
		$like = '%' . $keyword . '%';
		$expr = Util::expr();
		$params->searchQueryCb = function (QueryBuilder $query) use ($expr, $like) {
			$query->andWhere($expr->orX(
				$expr->like('l.name', ':like'),
				$expr->like('a.address1', ':like'),
				))->setParameter('like', $like);
		};
        return $params;
    }

    /**
     * @param Location $entity
     *
     * @return array
     */
    protected function getRowData($entity)
    {
        $address = $entity->getAddress();

        $data = [
            'Location ID' => $entity->getId(),
            'Name' => $entity->getName(),
            'Location Name' => $entity->getName(),
            'Location Address' => $address ? $address->getAddress1() : '',
            'Address' => $address ? $address->getAddress1() : '',
            'Phone' =>  $address ? $address->getPhone(): '',
            'CountryId' => $address ? $address->getCountry()->getId() : null,
            'StateId' => $address ? $address->getState()->getId() : null,
            'Country' => $address ? $address->getCountry()->getName() : '',
            'State/Province' => $address ? $address->getState()->getName(): '',
            'City' => $address ? $address->getCity() : '',
            'Zip' =>  $address ? $address->getPostalCode() : '',
            'Business Hours' => $entity->getHours(),
            'Business Hours Saturday & Sunday' => $entity->getHoursWeekend(),
            'Status' => $entity->getStatus(),
            '# Of Employees' => $entity->getEmployees() ? count($entity->getEmployees()): 0,
			'Latitude' => $address ? $address->getLatitude() : '',
			'Longitude' => $address ? $address->getLongitude() : '',
            'Balance' => null,
        ];

        if ($entity->getAdminUser()) {
            $dummy = UserService::getDummyCard($entity->getAdminUser());
            $data['Balance'] = Money::formatUSD($dummy->getBalance(), false);
        }

        return $data;
    }

	/**
	 * @Route("/admin/spendr/merchant/locations/save", methods={"POST"})
	 * @param Request $request
	 *
	 * @return SuccessResponse|DeniedResponse
	 * @throws PortalException
	 */
    public function saveAction(Request $request)
    {
		if (
            SpendrBundle::isSpendrAdminLoggedInAs()
			|| $this->user->inTeams([
				Role::ROLE_SPENDR_MERCHANT_ACCOUNTANT_ADMIN,
				Role::ROLE_SPENDR_MERCHANT_OTHER_ADMIN
			])
		) {
			return new DeniedResponse();
		}

        $locationId = $request->get('Location ID');
        $isNew = false;
        $user = $this->user;
        $merchant = $user->getSpendrAdminMerchant();
        $data = $request->request->all();
        if ($locationId) {
            $location = Location::find($locationId);
            if (!$location) {
              throw PortalException::temp('Unknown location!');
            }
            $location->setStatus($data['Status']);
            $address = $location->getAddress();
        } else {
            if ($merchant->getOnboardingStatus() !== SpendrMerchant::ONBOARDING_STATUS_APPROVED) {
                throw PortalException::temp("Merchant isn't approved, you can't create location!");
            }
            $isNew = true;
            $location = new Location();
            $address = new Address();
            $location->setMerchant($user->getSpendrAdminMerchant())
                     ->setStatus(Location::STATUS_ACTIVE);
        }
        $address->setCountry(Country::find($data['CountryId']))
                ->setState(State::find($data['StateId']))
                ->setCity($data['City'])
                ->setPhone($data['Phone'])
                ->setPostalCode($data['Zip'])
                ->setAddress1($data['Address'])
                ->updateGeoLocation();

        $location->setName($data['Name'])
                  ->setAddress($address)
                  ->setHours($request->get('Business Hours'))
                  ->setHoursWeekend($request->get('Business Hours Saturday & Sunday'));
        Util::persist($address);
        Util::persist($location);

        if ($isNew && $user->inTeams(SpendrBundle::getMerchantDashboardAdminRoles())) {
            $user->addLocation($location);
            Util::persist($user);
        }

        LocationService::createLocationBalanceUser($merchant, $location);

        return new SuccessResponse($this->getRowData($location));
    }

    /**
     * @Route("/admin/spendr/merchant/locations/export", methods={"POST"})
     * @param Request $request
     *
     * @return SuccessResponse|DeniedResponse
     */
    public function export(Request $request)
    {
		if (SpendrBundle::notAllowedExport()) {
			return new DeniedResponse();
		}
        $fields = [
            'Location ID' => 10,
            'Location Name' => 10,
            'Location Address' => 10,
            'Phone' => 10,
            'Country' => 10,
            'State/Province' => 20,
            'City' => 30,
            'Zip' => 20,
            '# Of Employees' => 20,
            'Status' => 20,
        ];
        return $this->commonExport($request, 'Spendr Merchant Locations', $fields);
    }
}
