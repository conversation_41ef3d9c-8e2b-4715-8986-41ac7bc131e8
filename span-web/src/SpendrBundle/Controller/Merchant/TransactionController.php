<?php

namespace SpendrB<PERSON>le\Controller\Merchant;


use Carbon\Carbon;
use ClfBundle\Entity\Transaction;
use Clf<PERSON><PERSON>le\Entity\TransactionStatus;
use Clf<PERSON><PERSON>le\Entity\TransactionType;
use CoreBundle\Entity\Role;
use CoreBundle\Response\DeniedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\QueryListParams;
use CoreBundle\Utils\Util;
use Doctrine\ORM\QueryBuilder;
use SpendrBundle\Entity\Location;
use SpendrBundle\Entity\SpendrMerchant;
use SpendrBundle\Entity\SpendrTip;
use SpendrB<PERSON>le\Entity\TippingEntity;
use SpendrBundle\Services\FeeService;
use SpendrBundle\Services\LocationService;
use SpendrBundle\Services\MerchantService;
use SpendrBundle\Services\Pay\PayService;
use Spendr<PERSON>undle\SpendrBundle;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class TransactionController extends BaseController
{
	public function __construct()
	{
		parent::__construct();

		$this->authAdminsWithoutBank();
	}

	/**
	 * @Route("/admin/spendr/merchant/transactions/list/{page}/{limit}", methods={"GET"}, defaults={"page" = 1,"limit" = 10})
	 *
	 * @param Request $request
	 * @param int $page
	 * @param int $limit
	 * @return SuccessResponse
	 * @throws \Doctrine\ORM\NoResultException
	 * @throws \Doctrine\ORM\NonUniqueResultException
	 */
	public function search(Request $request, $page = 1, $limit = 10)
	{
		$resp = $this->traitSearch($request, [], $page, $limit);
		$result = $resp->getResult();

		$query = $this->getResetParameterizedQuery();

		$total = (int)((clone $query)->select('count(distinct t)')
			->getQuery()
			->getSingleScalarResult());

		$purchaseData = $this->getStatisticData(clone $query);
		$totalPurchase = (int)$purchaseData['total'];
		$totalPurchaseAmount = (int)$purchaseData['totalAmount'];

		$refundData = $this->getStatisticData(clone $query, [TransactionType::NAME_REFUND]);
		$totalRefund = (int)$refundData['total'];
		$totalRefundAmount = (int)$refundData['totalAmount'];

        $spendrFee = (int)((clone $query)->select('sum(t.spendrFee)')
			->getQuery()
			->getSingleScalarResult());

		$ternFee = null;
		$spendrRevenue = null;
		$showTip = true;
		if (
			$this->user->isMasterAdmin()
			|| $this->user->inTeams(SpendrBundle::getSpendrAdminRoles())
		) {
			$ternFee = $this->getTernFee(clone $query);
			// todo
			$refundFee = (int)((clone $query)->select('sum(t.refundFee)')
				->getQuery()
				->getSingleScalarResult());
			$spendrRevenue = $spendrFee - $refundFee;
		} else {
            $tipping = TippingEntity::findOneByMerchant();
            if (!$tipping || !$tipping->getEnable()) {
                $showTip = false;
            } else {
                $merchant = $this->user->getSpendrAdminMerchant();
                $tipping = TippingEntity::findOneByMerchant($merchant);
                if (!$tipping || !$tipping->getChangeable()) {
                    $showTip = false;
                }
            }
        }

		// tip kpi of txn
		$totalTip = null;
		$totalTipAmount = null;
		$totalTipSpendrFee = null;
		if ($showTip) {
            $tipRes = (clone $query)->andWhere('st.status = :stStatus')
                ->setParameter('stStatus', SpendrTip::STATUS_PAID)
                ->select('count(distinct st) total, sum(st.amount) tipAmount, sum(st.spendrFee) spendrFee')
                ->getQuery()
                ->getSingleResult();
            $totalTip = (int)$tipRes['total'];
            $totalTipAmount = (int)$tipRes['tipAmount'];
            $totalTipSpendrFee = (int)$tipRes['spendrFee'];
        }

		$result['quick'] = [
			'total' => $total,
			'totalPurchase' => $totalPurchase,
			'totalRefund' => $totalRefund,
			'totalPurchaseAmount' => Money::format($totalPurchaseAmount, 'USD', false),
			'totalRefundAmount' => Money::format($totalRefundAmount, 'USD', false),
			'spendrFee' => Money::format($spendrFee, 'USD', false),
			'ternFee' => Money::format($ternFee, 'USD', false),
			'spendrRevenue' => Money::format($spendrRevenue, 'USD', false),
            'showTip' => $showTip,
            'totalTips' => $totalTip,
            'totalTipsAmount' => Money::format($totalTipAmount, 'USD', false),
            'totalTipsSpendrFee' => Money::format($totalTipSpendrFee, 'USD', false),
		];
		return new SuccessResponse($result);
	}

	protected function query(Request $request)
	{
		$query = $this->em->getRepository(Transaction::class)
			->createQueryBuilder('t')
			->join('t.merchant', 'm')
			->join('m.adminUser', 'u')
			->join('u.teams', 'r')
            ->leftJoin('t.tip', 'st')
			->leftJoin('t.location', 'l')
			->leftJoin('t.locationEmployee', 'le')
			->leftJoin('t.terminal', 'te')
			->leftJoin('t.consumer', 'c')
			->where('r.name = :role')
			->setParameter('role', Role::ROLE_SPENDR_MERCHANT_ADMIN);

		if ($this->user->inTeams(SpendrBundle::getMerchantAdminRoles()))
		{
			$merchant = $this->user->getSpendrAdminMerchant();

			$query = MerchantService::generateMergeMerchantQuery($query, 't.merchant', $merchant);

			if ($this->user->inTeams(SpendrBundle::getMerchantDashboardAdminRoles())) {
				$query->join('l.admins', 'locationAdmin')
				->andWhere('locationAdmin.id = :uid')
				->setParameter('uid', $this->user->getId());
			}
		}

        $type = $request->get('txn_type', '');
        if ($type) {
            $query->join('t.type', 'type')
                ->andWhere('type.name = :type');

            if (str_ends_with($type, 'Payment')) {
                $query->setParameter('type', TransactionType::NAME_PURCHASE);
            } else if (str_ends_with($type, 'Refund')) {
                $query->setParameter('type', TransactionType::NAME_REFUND);
            } else {
                $query->setParameter('type', 'Unknown');
            }

            $expr = $query->expr();
            if (str_starts_with($type, 'Online ')) {
                $query->andWhere($expr->like('te.deviceId', ':deviceId'));
            } else {
                $query->andWhere($expr->orX(
                    $expr->isNull('t.terminal'),
                    $expr->isNull('te.deviceId'),
                    $expr->notLike('te.deviceId', ':deviceId')
                ));
            }
            $query->setParameter('deviceId', PayService::DUMMY_TERMINAL_PREFIX . '%');
        }

		return $this->queryByDateRange($query, 't.dateTime', $request);
	}

	protected function queryListParams(QueryBuilder $query, Request $request)
	{
		$params = new QueryListParams($query, $request, 't', 'count(distinct t)');
		$params->distinct = true;
		$params->orderBy = [
			't.id' => 'desc',
		];
		$params->searchFields = [
			't.status',
			't.type',
			'l.name',
			'l.id',
			'c.id',
		];
		return $params;
	}

	protected function getStatisticData(
		QueryBuilder $query,
		$txnTypes = [TransactionType::NAME_PURCHASE],
		$txnStatuses = [TransactionStatus::NAME_COMPLETED]
	) {
		$expr = Util::expr();
		return $query->andWhere($expr->in('t.status', ':statuses'))
			->setParameter('statuses', TransactionStatus::gets($txnStatuses))
			->andWhere($expr->in('t.type', ':types'))
			->setParameter('types', TransactionType::gets($txnTypes))
			->select('count(distinct t) total, sum(t.amount) totalAmount')
			->getQuery()
			->getSingleResult();
	}

	protected function getTernFee(QueryBuilder $query)
	{
		$fee = FeeService::calculate(FeeService::TO_TERN_TRANSACTION_FEE);
		if (!$fee) {
			return null;
		}

		$r = $this->getStatisticData($query, [
			TransactionType::NAME_PURCHASE,
			TransactionType::NAME_REFUND
		]);
		$total = (int)$r['total'];
		return $total ? $fee * $total : null;
	}

	/**
	 * @param Transaction $entity
	 * @return array
	 */
	protected function getRowData($entity)
	{
		$data = [
			'Transaction ID' => $entity->getId(),
			'Merchant Name' => $entity->getMerchant() ? $entity->getMerchant()->getName() : null,
			'Location Name' => $entity->getLocation() ? $entity->getLocation()->getName() : null,
			'Clerk Name' => $entity->getLocationEmployee() ? $entity->getLocationEmployee()->getName() : null,
			'Terminal' => $entity->getTerminal() ? $entity->getTerminal()->getName() : null,
			'Consumer ID' => $entity->getConsumer() ? $entity->getConsumer()->getId() : null,
			'First Name' => $entity->getConsumer() ? $entity->getConsumer()->getFirstName() : null,
			'Last Name' => $entity->getConsumer() ? $entity->getConsumer()->getLastName() : null,
			'Payment Amount' => Money::format($entity->getAmount(), 'USD', false),
            'Tip Amount' => $entity->getTip() ? Money::format($entity->getTip()->getAmount(), 'USD', false) : null,
            'Tip Spendr Fee' => $entity->getTip() && $entity->getTip()->getSpendrFee()
                ? Money::format($entity->getTip()->getSpendrFee(), 'USD', false) : null,
			'Spendr Fee' => $entity->getSpendrFee() ? Money::format($entity->getSpendrFee(), 'USD', false) : null,
			'Refund Fee' => $entity->getRefundFee()
				? Money::format($entity->getRefundFee(), 'USD', false) : null,
			'Merchant Balance Before' => $entity->getProducerBalanceBefore()
				? Money::format($entity->getProducerBalanceBefore(), 'USD', false) : null,
			'Merchant Balance After' => $entity->getProducerBalanceAfter()
				? Money::format($entity->getProducerBalanceAfter(), 'USD', false) : null,
			'Date & Time' => Util::formatDateTime(
				$entity->getDateTime(),
				Util::DATE_TIME_FORMAT,
				$this->tz
			),
			'Transaction Type' => $entity->getTypeDisplayName(),
			'Status' => $entity->getStatus()->getName(),
			'Refund From ID' => $entity->getRefundFrom() ? $entity->getRefundFrom()->getId() : null,
			// for export
			'Date Time' => Util::formatDateTime(
				$entity->getDateTime(),
				'm/d/Y g:i:s A',
				$this->tz
			),
            'Transaction Time' => Util::formatDateTime(
                $entity->getTxnTime(),
                'm/d/Y g:i:s A',
                $this->tz
            ),
		];

		if ($this->user->isMasterAdmin() || $this->user->inTeams(SpendrBundle::getSpendrAdminRoles()))
		{
			$data['Estimated Tern Fee'] = $entity->getTernFee()
				? Money::format($entity->getTernFee(), 'USD', false) : null;
		}

		return $data;
	}

	/**
	 * @Route("/admin/spendr/merchant/transactions/export", methods={"POST"})
	 * @param Request $request
	 *
	 * @return SuccessResponse|DeniedResponse
	 */
	public function export(Request $request)
	{
		if (SpendrBundle::notAllowedExport()) {
			return new DeniedResponse();
		}

        $isMasterOrSpendr = $this->user->isMasterAdmin() || $this->user->inTeams(SpendrBundle::getSpendrAdminRoles());

		$fields = [
			'Transaction ID' => 20,
			'Date Time' => 20,
        ];
        if ($isMasterOrSpendr) {
            $fields['Transaction Time'] = 20;
        }
        $fields = array_merge($fields, [
			'First Name' => 20,
			'Last Name' => 20,
			'Consumer ID' => 20,
			'Payment Amount' => 20,
			'Tip Amount' => 20,
			'Tip Spendr Fee' => 20,
			'Spendr Fee' => 10,
		]);
		if ($isMasterOrSpendr) {
			$fields['Estimated Tern Fee'] = 20;
		}
		$fields = array_merge($fields, [
			'Refund Fee' => 10,
			'Merchant Balance Before' => 20,
			'Merchant Balance After' => 20,
			'Merchant Name' => 20,
			'Location Name' => 30,
			'Clerk Name' => 30,
			'Terminal' => 20,
			'Refund From ID' => 20,
			'Transaction Type' => 20,
			'Status' => 10,
		]);
		return $this->commonExport($request, 'Spendr_Merchant_Transactions', $fields);
	}

	public function getSearchFilters()
	{
		$locations = LocationService::getAllLocations($this->user);
		$data = [];
		$data[] = [
			'label' => 'All',
			'value' => 'all'
		];
		if ($locations) {
			/** @var Location $location */
			foreach ($locations as $location) {
				$data[] = [
					'label' => $location->getName(),
					'value' => $location->getId(),
				];
			}
		}
		return [
			'locations' => $locations,
		];
	}

	/**
	 * @Route("/admin/spendr/merchant/transactions/filters")
	 * @return SuccessResponse
	 */
	public function searchFiltersAction()
	{
		$all = $this->getSearchFilters();
		$data = Util::allToFilterOptions($all, [
			'locations' => null,
		]);
        $data['types'] = Util::toFilterOptions([
            'Payment',
            'Refund',
            'Online Payment',
            'Online Refund',
        ]);
		return new SuccessResponse($data);
	}
}
