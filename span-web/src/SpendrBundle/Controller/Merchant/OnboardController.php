<?php

namespace Spendr<PERSON><PERSON>le\Controller\Merchant;

use ApiBundle\Services\ApiRunner;
use Carbon\Carbon;
use CoreBundle\Entity\Email;
use CoreBundle\Entity\ExternalInvoke;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserCard;
use CoreBundle\Response\DeniedResponse;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Services\SSLEncryptionService;
use CoreBundle\Services\UserService;
use CoreBundle\Utils\Security;
use CoreBundle\Utils\Util;
use MobileBundle\Controller\DefaultController;
use PortalBundle\Exception\DeniedException;
use PortalBundle\Exception\PortalException;
use SalexUserBundle\Entity\User;
use SpendrBundle\Entity\Location;
use SpendrBundle\Entity\SpendrMerchant;
use SpendrBundle\Services\BrazeService;
use SpendrBundle\Services\LocationService;
use SpendrBundle\Services\MerchantService;
use SpendrBundle\SpendrBundle;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use SpendrBundle\Services\SlackService;
use UsUnlockedBundle\Services\IDologyService;

class OnboardController extends BaseController
{
    public $protected = false;

    public function getAccessibleRoles()
	{
		return [
			Role::ROLE_SPENDR_PROGRAM_OWNER,
            Role::ROLE_SPENDR_MERCHANT_ADMIN,
			Role::ROLE_SPENDR_MERCHANT_MASTER_ADMIN,
			Role::ROLE_SPENDR_MERCHANT_OPERATOR_ADMIN
		];
	}

	/**
	 * @Route("/spendr/merchant/onboard")
	 * @param Request $request
	 *
	 * @return Response
	 */
	public function index(Request $request) {
		$user = $this->getUser();

		if (!$user) {
			$user = ApiRunner::getNoneInvoker();
		}

		$content = DefaultController::themeContent($user, $this->platform);
		return new Response($content);
	}

//	/**
//	 * @Route("/spendr/merchant/onboard/init/{businessType}")
//	 * @param Request $request
//	 *
//	 * @param $businessType
//	 * @return Response
//	 */
//	public function initIndex(Request $request, $businessType) {
//		if (
//			!$businessType
//			|| ($businessType && !in_array($businessType, ['cannabis', 'non-cannabis']))
//		) {
//			return new DeniedResponse('Invalid link.');
//		}
//		$user = $this->getUser();
//		if (!$user) {
//			$user = ApiRunner::getNoneInvoker();
//		}
//
//		$content = DefaultController::themeContent($user, $this->platform);
//		return new Response($content);
//	}

	/**
	 * @Route("/spendr/merchant/onboard/prepare")
	 * @param Request $request
	 *
	 * @return Response
	 */
	public function prepare(Request $request)
	{
		$recaptchaKey = Util::getParameter('recaptcha_frontend', true);
		return new SuccessResponse($recaptchaKey);
	}

	/**
	 * @Route("/spendr/merchant/onboard/detail", methods={"POST"})
	 * @param Request $request
	 *
	 * @return Response
	 * @throws PortalException
	 * @throws \CoreBundle\Exception\FailedException
	 */
    public function detail(Request $request)
	{
    	$this->validateUser($request, 'spendr_merchant_onboard_save');

		$merchant = MerchantService::saveDetails($request);
        $data = $this->getMerchantDetailsData($merchant);
        return new SuccessResponse($data);
    }

    protected function sendNewMerchantEmailToSpendr() {
		Email::sendWithTemplate(
			[
				'<EMAIL>',
			],
			Email::TEMPLATE_SIMPLE_LAYOUT,
			[
				'body' => 'A new merchant application has been submitted. Log into the Admin Dashboard to begin the onboarding process.',
				'Product_Team' => $this->platform->getName(),
				'subject' => 'New Merchant Application Submitted',
				'_cc' => [
					'<EMAIL>',
					'<EMAIL>',
					'<EMAIL>',
				],
				'to_spendr' => true
			]
		);
	}

	/**
	 * @Route("/spendr/merchant/onboard/resend-invitation/{merchant}", methods={"POST"})
	 * @param Request $request
	 * @param SpendrMerchant $merchant
	 *
	 * @return Response
	 * @throws DeniedException
	 */
    public function resend(Request $request, SpendrMerchant $merchant)
	{
		$this->validateUser($request, 'spendr_merchant_onboard_resend_email');

		if ($merchant->getOnboardingStatus() === SpendrMerchant::ONBOARDING_STATUS_INITIAL) {
            UserService::sendResetPasswordEmail($merchant->getAdminUser());
        }
        return new SuccessResponse();
    }

	/**
	 * @Route("/spendr/merchant/onboard/change-email/{merchant}", methods={"POST"})
	 * @param Request $request
	 * @param SpendrMerchant $merchant
	 *
	 * @return Response
	 * @throws PortalException
	 */
    public function changeEmail(Request $request, SpendrMerchant $merchant)
	{
    	$this->validateUser($request, 'spendr_merchant_onboard_change_email');

		if ($merchant->getOnboardingStatus() === SpendrMerchant::ONBOARDING_STATUS_INITIAL) {
            $email = $request->get('email');
            $admin = $merchant->getAdminUser();
            $admin->changeEmail($email, SpendrBundle::getMerchantAdminRoles())
                ->persist();

            $address = $merchant->getAddress();
            $address->setEmail($email);
            Util::persist($address);

            BrazeService::userTrack(null, null, null, [
                'external_id' => BrazeService::getExternalPrefix() . $admin->getId(),
                'email' => $admin->getEmail()
            ]);

            UserService::sendResetPasswordEmail($admin);
        }
        return new SuccessResponse([
            'id' => $merchant->getId(),
            'email' => $merchant->getAdminUser()->getEmail(),
        ]);
    }

    protected function validateUser(Request $request, $recaptchaAction = null) {
		$user = $this->getUser();
		if ($user) {
			$this->authSuperAdminOrAdmins($this->getAccessibleRoles());
		} else {
			$userToken = $this->getApiUserToken();
			if (!$userToken) {
				throw new DeniedException();
			}
			$user = $userToken->getUser();
			if (!ApiRunner::isNoneInvoker($user)) {
				throw new DeniedException();
			}
			if ($recaptchaAction) {
				Security::recaptchaCheck($request, $recaptchaAction);
			}
		}
		return $user;
	}

    protected function validateMerchant(SpendrMerchant $merchant)
    {
        $user = $this->authSuperOrOtherAdmins();
        $userMerchant = $user->getSpendrAdminMerchant();
        if ($user->inTeams(SpendrBundle::getMerchantAdminRoles()) && !Util::eq($merchant, $userMerchant)) {
            throw new DeniedException();
        }
    }

	/**
	 * @Route("/spendr/merchant/{merchant}/onboard/data", methods={"GET"})
	 * @param Request $request
	 * @param SpendrMerchant $merchant
	 *
	 * @return Response
	 * @throws DeniedException
	 * @throws PortalException
	 */
    public function data(Request $request, SpendrMerchant $merchant) {
    	$user = $this->getUser();
    	if ($user && $user->inTeams([Role::ROLE_SPENDR_MERCHANT_OTHER_ADMIN])) {
    		return new DeniedResponse();
		}
        $this->validateMerchant($merchant);
        $data = $this->getMerchantDetailsData($merchant);
        return new SuccessResponse($data);
    }

    private function getMerchantDetailsData (SpendrMerchant $merchant)
    {
        $data = $merchant->getDetailsArray();
        $merchantAdmin = $merchant->getAdminUser();
        $data['bankName'] = '';
        $data['accountNum'] = '';
        $data['routing'] = '';
        $data['pendingCard'] = false;
        $uc = MerchantService::getActiveRealCard($merchantAdmin);
        if ($uc) {
            $accountNumber = $uc->getAccountNumber() ? SSLEncryptionService::tryToDecrypt($uc->getAccountNumber()) : '';
            $meta = Util::meta($uc);
            if ($uc->getPlaidAccessToken() != null && $meta && isset($meta['mask'])) {
                $accountNumber = '***' . $meta['mask'];
            }
            $data['bankName'] =$uc->getBankName();
            $data['accountNum'] = $accountNumber;
            $data['routing'] = $uc->getRoutingNumber() ? SSLEncryptionService::tryToDecrypt($uc->getRoutingNumber()) : '';
        }
        if (MerchantService::getVerifyPendingCard($merchantAdmin)) {
            $data['pendingCard'] = true;
        }

        $data['representatives'] = Util::meta($merchant, 'representatives') ?? [];

		$docs = Util::meta($merchant, 'docs');
		$data['docs'] = [];
		if ($docs && (count($docs) > 0)) {
			foreach ($docs as $k=>$v) {
				$data['docs'][$k] = array_values($v);
			}
		}

        $data['deniedMessage'] = Util::meta($merchant, 'bankDeniedMessage');
		$data['isActive'] = $merchant->isActive();
        return $data;
    }

	/**
	 * @Route("/spendr/merchant/{merchant}/onboard/business-type", methods={"POST"})
	 * @param Request $request
	 * @param SpendrMerchant $merchant
	 *
	 * @return Response
	 * @throws DeniedException
	 * @throws PortalException
	 */
    public function businessType(Request $request, SpendrMerchant $merchant) {
		$this->authSuperAdminOrAdmins($this->getAccessibleRoles());

		$this->validateMerchant($merchant);
        $merchant->setBusinessType($request->get('type'))
            ->persist();
        $data = $this->getMerchantDetailsData($merchant);
        return new SuccessResponse($data);
    }

	/**
	 * @Route("/spendr/merchant/{merchant}/onboard/representative", methods={"POST"})
	 * @param Request $request
	 * @param SpendrMerchant $merchant
	 *
	 * @return Response
	 * @throws DeniedException
	 * @throws PortalException
	 */
    public function representative(Request $request, SpendrMerchant $merchant) {
		$this->authSuperAdminOrAdmins($this->getAccessibleRoles());

		$this->validateMerchant($merchant);

        $reps = $request->request->all('reps');
        foreach ($reps as $i => $rep) {
            $user = new User();
            $user->setFirstName($rep['First Name'] ?? '')
                ->setLastName($rep['Last Name'] ?? '')
                ->setEmail($rep['Email'] ?? '')
                ->setMobilephone($rep['Phone'] ?? '')
                ->setAddress($rep['Street Address'] ?? '')
                ->setCity($rep['City'] ?? '')
                ->setZip($rep['Postal Code'] ?? '')
                ->setSource('spendr_representative')
                ->setCountryid($rep['CountryId'] ?? null)
                ->setStateid($rep['State'] ?? null);

            $oldHashKey = $rep['Hash Key'] ?? null;
            $hashKey = IDologyService::ofac($user, true);
            if ($oldHashKey === $hashKey) {
                continue; // Already verified
            }

            /** @var ExternalInvoke $ei */
            list($ei) = IDologyService::ofac($user);
            if ($ei && $ei->isFailed()) {
                return new FailedResponse('OFAC check failed for the representative "' .
                                          $user->getName() . '": ' . $ei->getError());
            }
            $reps[$i]['Hash Key'] = $hashKey;

			if (isset($rep['file'])) {
				$url = $rep['file']['url'] ?? null;
				if ($url) {
					$cleanedUrl = strstr($url, "/attachments");
					$reps[$i]['file']['url'] = $cleanedUrl;
				}
			}
        }

        Util::updateMeta($merchant, 'representatives', false);
        Util::updateMeta($merchant, [
            'representatives' => $reps,
        ]);

        $data = $this->getMerchantDetailsData($merchant);
        return new SuccessResponse($data);
    }

    /**
     * @Route("/spendr/merchant/{merchant}/onboard/docs", methods={"POST"})
     * @param Request        $request
     * @param SpendrMerchant $merchant
     *
     * @return Response
     * @throws DeniedException
     */
    public function docs(Request $request, SpendrMerchant $merchant) {
		$user = $this->getUser();
		if ($user && $user->inTeams([Role::ROLE_SPENDR_MERCHANT_OTHER_ADMIN])) {
			return new DeniedResponse();
		}
        $this->validateMerchant($merchant);

        $type = $request->request->get('type');
        $file = $request->request->all('file');

        $docs = Util::meta($merchant, 'docs') ?? [];
        $docs[$type][$file['id']] = $file;

		foreach($docs as $k => $d) {
			foreach($d as $kChild => $child) {
				if (isset($child['url'])) {
					$url = $child['url'];
					$cleanedUrl = strstr($url, "/attachments");
					$docs[$k][$kChild]['url'] = $cleanedUrl;
				}
			}
		}

        Util::updateMeta($merchant, [
            'docs' => $docs,
        ]);

        $res = [];
		if (count($docs) > 0) {
			foreach ($docs as $k=>$v) {
				$res[$k] = array_values($v);
			}
		}

        return new SuccessResponse($res);
    }

    /**
     * @Route("/spendr/merchant/{merchant}/onboard/docs/remove", methods={"POST"})
     * @param Request        $request
     * @param SpendrMerchant $merchant
     *
     * @return Response
     * @throws DeniedException
     */
    public function removeDoc(Request $request, SpendrMerchant $merchant) {
		$this->authSuperAdminOrAdmins($this->getAccessibleRoles());

		$this->validateMerchant($merchant);

        $type = $request->request->get('type');
        $docId = $request->request->get('docId'); // for multiple

        $docs = Util::meta($merchant, 'docs') ?? [];
        if (isset($docId)) {
			unset($docs[$type][$docId]);
		} else {
			unset($docs[$type]);
		}
        Util::updateMeta($merchant, 'docs', false);
        Util::updateMeta($merchant, [
            'docs' => $docs,
        ]);

		$res = [];
		if (count($docs) > 0) {
			foreach ($docs as $k=>$v) {
				$res[$k] = array_values($v);
			}
		}

        return new SuccessResponse($res);
    }

	/**
	 * @Route("/spendr/merchant/{merchant}/onboard/submit", methods={"POST"})
	 * @param Request $request
	 * @param SpendrMerchant $merchant
	 *
	 * @return Response
	 * @throws DeniedException
	 * @throws PortalException
	 */
    public function submit(Request $request, SpendrMerchant $merchant) {
		$this->authSuperAdminOrAdmins($this->getAccessibleRoles());

		$this->validateMerchant($merchant);

		$preOnboardingStatus = $merchant->getOnboardingStatus();
        if (in_array($preOnboardingStatus, [
            SpendrMerchant::ONBOARDING_STATUS_INITIAL,
            SpendrMerchant::ONBOARDING_STATUS_DENIED,
        ])) {
            $merchant->setOnboardingStatus(SpendrMerchant::ONBOARDING_STATUS_PENDING);
            Util::updateMeta($merchant, 'bankDeniedAt', false);
            Util::updateMeta($merchant, 'bankApprovedAt', false);
            Util::updateMeta($merchant, [
                'bankSubmittedAt' => Util::formatDateTime(
                	Carbon::now(),
					Util::DATE_TIME_FORMAT,
					$this->tz
				),
            ]);
        }

		if ($preOnboardingStatus === SpendrMerchant::ONBOARDING_STATUS_INITIAL) {
			$this->sendNewMerchantEmailToSpendr();
		}

        $data = $this->getMerchantDetailsData($merchant);
        return new SuccessResponse($data);
    }

	/**
	 * @Route("/spendr/merchant/{merchant}/onboard/repeal", methods={"POST"})
	 * @param Request $request
	 * @param SpendrMerchant $merchant
	 *
	 * @return Response
	 * @throws DeniedException
	 * @throws PortalException
	 */
    public function repeal(Request $request, SpendrMerchant $merchant) {
		$this->authSuperAdminOrAdmins($this->getAccessibleRoles());

		$this->validateMerchant($merchant);

        if (in_array($merchant->getOnboardingStatus(), [
            SpendrMerchant::ONBOARDING_STATUS_PENDING,
            SpendrMerchant::ONBOARDING_STATUS_APPROVED,
        ])) {
            $merchant->setOnboardingStatus(SpendrMerchant::ONBOARDING_STATUS_INITIAL);
            Util::updateMeta($merchant, 'bankDeniedAt', false);
            Util::updateMeta($merchant, 'bankApprovedAt', false);
            Util::updateMeta($merchant, 'bankSubmittedAt');
        }

        $data = $this->getMerchantDetailsData($merchant);
        return new SuccessResponse($data);
    }

    /**
     * @Route("/admin/spendr/merchant/all-locations/{merchant}", methods={"GET"})
     * @param SpendrMerchant $merchant
     * @return FailedResponse|SuccessResponse
     * @throws DeniedException
     */
    public function merchantLocations(SpendrMerchant $merchant)
    {
        $this->authSuperAdminOrAdmins($this->getAccessibleRoles());
        $locations = LocationService::listLocationsForSelection($merchant);

        return new SuccessResponse($locations);
    }

    /**
     * @Route("/admin/spendr/merchant/card-bind-location/{merchant}", methods={"POST"})
     * @param SpendrMerchant $merchant
     * @param Request $request
     * @return DeniedResponse|FailedResponse|SuccessResponse
     * @throws DeniedException
     */
    public function cardBindLocation(SpendrMerchant $merchant, Request $request)
    {
        $this->authSuperAdminOrAdmins($this->getAccessibleRoles());
        $this->validateMerchant($merchant);

        $userCard = UserCard::find($request->get('cardId'));
		$locationIds = $request->get('locationIds');
        if (!$userCard || $merchant->getAdminUser() !== $userCard->getUser()) {
            return new FailedResponse('Invalid parameters.');
        }

		$locations = [];
		if ($locationIds) {
			$locations = $this->getLocationsByIds($locationIds);
			if (!$locations) {
				return new FailedResponse("Locations don't exist!");
			}
		}

		$relatedLocationsBefore = $userCard->getSpendrLocations();
		if ($relatedLocationsBefore) {
			/** @var Location $location */
			foreach ($relatedLocationsBefore as $key => $location) {
				$location->setBankCard(null);
				if (($key + 1) === count($relatedLocationsBefore)) {
					$location->persist();
				}
			}
		}

		if ($locations) {
			/** @var Location $location */
			foreach ($locations as $key => $location) {
				$location->setBankCard($userCard);
				if (($key + 1) === count($locations)) {
					$location->persist();
				}
			}
		}

        return new SuccessResponse();
    }

	private function getLocationsByIds($locationIds)
	{
		return Util::em()->getRepository(Location::class)
		->createQueryBuilder('l')
		->where(Util::expr()->in('l.id', ':locationIds'))
		->setParameter('locationIds', $locationIds)
		->distinct()
		->getQuery()
		->getResult();
	}
}
