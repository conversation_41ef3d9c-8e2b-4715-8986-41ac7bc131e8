<?php

namespace Spendr<PERSON><PERSON>le\Controller\Mobile;

use Carbon\Carbon;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserPin;
use CoreBundle\Entity\UserToken;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Services\UserPinService;
use CoreBundle\Utils\Util;
use Doctrine\Common\Util\ClassUtils;
use SalexUserBundle\Entity\User;
use SalexUserBundle\Entity\UserConfig;
use SpendrBundle\Services\LocationService;
use SpendrBundle\Services\Pay\PayService;
use SpendrBundle\Services\UserService;
use SpendrBundle\SpendrBundle;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class LoginController extends BaseController
{
    use RegisterControllerTrait;
    public $protected = false;

    /**
     * @Route("/spendr/m/login/email", methods={"POST"})
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function emailAction(Request $request)
    {
        $verifyMobile = $request->get('verifyMobile', false);
        if ($verifyMobile) {// If if mobile + sms login
            return $this->verifyCodeLogin($request);
        }
        $password = trim($request->get('password'));
        if (!$password) {
            return new FailedResponse('Invalid password!');
        }
        $mobileLogin = $request->get('loginType') == 'mobile_password';
        $user = $this->getEmailUser($request, 'loginRole', $mobileLogin);
        if (ClassUtils::getClass($user) != 'SalexUserBundle\Entity\User') {
            return $user;
        }

		if (!$user->inTeams(SpendrBundle::getMobileLoginRoles())) {
			return new FailedResponse("You don't have permission to log in.");
		}

        if ($user->getEmail() && str_starts_with($user->getEmail(), PayService::DUMMY_USER_PREFIX)) {
            return new FailedResponse('Dummy user is not allowed to login the app!');
        }

        $status = $user->getStatus();
        if ($user->isLocked()) {
            return new FailedResponse('Your account has been locked. Please try again later or contact support.');
        }

        $encoder = Util::getPasswordEncoder();
        if ( ! $encoder->isPasswordValid($user, $password)) {
            return new FailedResponse('Invalid password!');
        }

        $error = null;
        if ($status === User::STATUS_CLOSED) {
            $error = 'This account is '.$status.'! Please contact support if any questions.' . $this->supportMsgInfo();
        } else {
            if ($status === User::STATUS_BANNED) {
                $error = 'This account is '.$status."! Banned Reason:\n".$user->getBannedReason();
            } else {
                if (strtolower($status) === User::STATUS_INACTIVE) {
                    $error = 'Your account has been deactivated, please contact support.';
                }
            }
        }
        if ($error) {
            return new FailedResponse($error);
        }

        if ($user->inTeams([ Role::ROLE_SPENDR_CONSUMER ])) {
            if ($mobileLogin &&
                !Util::meta($user, 'activeRegisterStep') &&
                !Util::meta($user, 'mobilePhoneVerified')
//                && !$user->getEmailVerified()
            ) {
                $message = 'Your phone number has not been verified. Please verify your mobile or ask contact for help.' . $this->supportMsgInfo();
                return new FailedResponse($message, [
                    'type'   => 'verify_mobile',
                    'error'  => $message,
                    'userId' => $user->getId(),
                    'email'  => $user->getEmail()
                ]);
            } elseif (!$mobileLogin && !$user->getEmailVerified()) {
                $message = 'Your email has not been verified.';
                return new FailedResponse($message, [
                    'type'   => 'verify_email',
                    'error'  => $message,
                    'userId' => $user->getId(),
                ]);
            }
        }

        $result = LocationService::getLocationsByUser($user);
        if ($user->inTeams(SpendrBundle::getMerchantEmployeeMobileLoginRoles()) && ( is_null($result) || count($result) === 0 )) {
            return new FailedResponse('You are not associated with any locations.');
        }
        if ($request->get('deviceId')) {
            /** @var UserConfig $config */
            $config = $user->ensureConfig();
            $config->setDeviceId($request->get('deviceId'))
                ->persist();
        }
        $ut = UserToken::instance($user);
        $user->logLogin();
        Util::flush();
        return new SuccessResponse([
            'token' => $ut->getToken(),
        ]);
    }

    /**
     * @Route("/spendr/m/login/sms", methods={"POST"})
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     * @throws \PortalBundle\Exception\PortalException
     */
    public function loginWithSMS(Request $request)
    {
        $mobile = $request->get('mobile');
        if (!$mobile) {
            return new FailedResponse('Please input your phone number.');
        }
        $mobile = Util::formatManualInputPhoneNumber($mobile);
        $mobile = Util::formatFullPhone($mobile);
        $ucs = $this->em->getRepository(User::class)
            ->createQueryBuilder('u')
            ->join('u.teams', 't')
            ->where(Util::expr()->in('t.name', ':roles'))
            ->setParameter('roles', SpendrBundle::getConsumerRoles())
            ->andWhere('u.mobilephone = :phone')
            ->setParameter('phone', $mobile)
            ->setMaxResults(1)
            ->getQuery()
            ->getResult();
        if (!$ucs || !count($ucs)) {
            return new FailedResponse("Your phone number is currently not associated to a Spendr account.", [
                'needSignUp' => true
            ]);
        }
        $user = $ucs[0];
        if ($user->isLocked()) {
            return new FailedResponse('Your account has been locked. Please try again later or contact support.');
        }
        $error = $this->verifyStatus($user);
        if ($error) {
            return new FailedResponse($error);
        }
        UserPinService::create($user, UserPin::PHONE_TYPE_MOBILE, UserPin::MSG_TYPE_SMS);

        return new SuccessResponse($user->toFinalApiArray());
    }

    /**
     * @param $user
     * @return string|null
     */
    protected function verifyStatus($user)
    {
        $error = null;
        $status = $user->getStatus();
        if ($status === User::STATUS_CLOSED) {
            $error = 'This account is '.$status.'! Please contact support if any questions.' . $this->supportMsgInfo();
        } else {
            if ($status === User::STATUS_BANNED) {
                $error = 'This account is '.$status."! Banned Reason:\n".$user->getBannedReason();
            } else {
                if (strtolower($status) === User::STATUS_INACTIVE) {
                    $error = 'Your account has been deactivated, please contact support.';
                }
            }
        }
        return $error;
    }

    protected function verifyCodeLogin(Request $request)
    {
        $userId = trim($request->get('userId'));
        if ($userId) {
            // Verify sms code
            $verifyCode = $request->get('verifyCode');
            if (!$verifyCode) {
                return new FailedResponse('Invalid code!');
            }
            if (!$userId) {
                return new FailedResponse('Invalid user!');
            }

            /** @var User $user */
            $user = $this->em->getRepository(User::class)->find($userId);
            if (!$user || !$user->inTeams(SpendrBundle::getMobileRoles())) {
                return new FailedResponse('Invalid user!');
            }
            if ($user->isLocked()) {
                return new FailedResponse('Your account has been locked. Please try again later or contact support.');
            }
            $error = $this->verifyStatus($user);
            if ($error) {
                return new FailedResponse($error);
            }

            if (!UserPinService::verify($user, $verifyCode, 10)) {
                return new FailedResponse('Invalid code or outdated!');
            }

            $result = LocationService::getLocationsByUser($user);
            if ($user->inTeams(SpendrBundle::getMerchantEmployeeRoles()) && ( is_null($result) || count($result) === 0 )) {
                return new FailedResponse('You are not associated with any locations.');
            }
            $ut = UserToken::instance($user);
            UserToken::updateUseTime($ut);
            $user->logLogin();
            if ($request->get('deviceId')) {
                /** @var UserConfig $config */
                $config = $user->ensureConfig();
                $config->setDeviceId($request->get('deviceId'))
                    ->persist();
            }
            Util::flush();
            return new SuccessResponse([
                'token' => $ut->getToken(),
            ]);
        }
        // send sms verification code
        $user = $this->getEmailUser($request, 'loginRole', true);
        if (ClassUtils::getClass($user) != 'SalexUserBundle\Entity\User') {
            return $user;
        }
        if ($user->inTeams([ Role::ROLE_SPENDR_CONSUMER ]) && !Util::meta($user, 'mobilePhoneVerified')) {
            $message = 'Your phone number has not been verified. Please verify your mobile or ask contact for help.' . $this->supportMsgInfo();
            return new FailedResponse($message, [
                'type'   => 'verify_mobile',
                'error'  => $message,
                'userId' => $user->getId(),
            ]);
        }
        UserPinService::create($user, UserPin::PHONE_TYPE_MOBILE, UserPin::MSG_TYPE_SMS);

        return new SuccessResponse([
            'userId' => $user->getId(),
            'mobile' => $user->getMobilephone()
        ], 'Verification sent success');
    }

    /**
     * @Route("/spendr/m/delete-account/index", methods={"GET"})
     *
     * @return Response
     */
    public function deletePage()
    {
        return $this->render('@Spendr/Login/closeAccount.html.twig', [
            'privacyUrl' => SpendrBundle::getApiDomain() . '/content?key=spendr_privacy'
        ]);
    }

    /**
     * @Route("/spendr/m/delete-account/submit", methods={"POST"})
     * @param Request $request
     *
     * @return Response
     */
    public function deleteAccount(Request $request)
    {
        $mobile = $request->get('mobile');
        $mobile = Util::formatManualInputPhoneNumber($mobile);
        $mobile = Util::formatFullPhone($mobile);
        $password = $request->get('password');
        $ucs = $this->em->getRepository(User::class)
            ->createQueryBuilder('u')
            ->join('u.teams', 't')
            ->where(Util::expr()->in('t.name', ':roles'))
            ->setParameter('roles', SpendrBundle::getConsumerRoles())
            ->andWhere('u.mobilephone = :phone')
            ->setParameter('phone', $mobile)
            ->setMaxResults(1)
            ->getQuery()
            ->getResult();
        $success = false;
        $message = 'Invalid Credentials!';
        if (count($ucs)) {
            /** @var User $user */
            $user = $ucs[0];
            if ($user->getStatus() === User::STATUS_CLOSED) {
                $success = true;
                $message = 'Your account has been deleted already.';
                return $this->render('@Spendr/Login/result.html.twig', compact('success', 'message'));
            }
            $encoder = Util::getPasswordEncoder();
            if ($encoder->isPasswordValid($user, $password)) {
                $dummy = UserService::getDummyCard($user);
                if ($dummy && $dummy->getBalance() < 0) {
                    $success = false;
                    $message = 'Your account has negative balance now! We will retain your account data before you deposit for the negative balance. Contact Support if any questions ' . $this->supportMsgInfo();
                } else {
                    $success = true;
                    if (!Util::meta($user, 'deleteApplyAt')) {
                        $message = 'Apply Success, we will handle your delete apply in 7 work days! If you want to cancel apply please ask support for help ' . $this->supportMsgInfo();
                        Util::updateMeta($user, [
                            'deleteApplyAt' => Carbon::now()
                        ]);
                        $user->setClosureReason($request->get('reason'))->persist();
                    } else {
                        $message = 'Apply In Progressing, we will handle your delete apply in 7 work days! If you want to cancel apply please ask support for help ' . $this->supportMsgInfo();
                    }
                }
            }
        } else {
            $message = 'This mobile is not registered or deleted in Spendr!. Contact Support if any questions ' . $this->supportMsgInfo();
        }

        return $this->render('@Spendr/Login/result.html.twig', compact('success', 'message'));
    }
}
