<?php

namespace Spendr<PERSON><PERSON>le\Controller\Mobile;

use Core<PERSON><PERSON>le\Entity\CardProgram;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserToken;
use CoreBundle\Exception\FailedException;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Security;
use CoreBundle\Utils\Util;
use MobileBundle\MobileBundle;
use PortalBundle\Exception\DeniedException;
use SpendrBundle\Entity\AppVersion;
use SpendrBundle\Services\SlackService;
use SpendrBundle\SpendrBundle;
use Symfony\Component\Security\Core\User\UserInterface;

class BaseController extends \SpendrBundle\Controller\BaseController
{
	/**
	 * BaseController constructor.
	 */
	public function __construct()
	{
		parent::__construct();

		// Request rate limit
		$path = Util::request()->getPathInfo();
		$ip = Security::getClientIp();
        $key = 'spendr_request_times_' . $path . '_' . $ip;
        $times = (int)(Data::get($key) ?: 0);
        Data::set($key, ++$times, false, 60);
        $limitTimes = $this->limitTimes($path);
        if ($times >= $limitTimes) {
            if ($times >= $limitTimes * 2) {
                Data::set($key, ++$times, false, 3600);
            }
            throw new FailedException(
                'Too many attempts. Please try again several minutes later',
                null,
                429
            );
        }

        if (!Util::isCommand()) {
            if ($this->protected) {
                $this->user = $this->getUser();
                if ($this->user
                    && !$this->user->inTeams(SpendrBundle::getMobileRoles())
                ) {
                    throw new DeniedException('Only mobile roles are supported to login the mobile app.');
                }
            }
            $appVersion = MobileBundle::getAppVersion();
            $appBuild = MobileBundle::getAppBuild();
            if ($this->isApp()) {// App request
                $appOS = MobileBundle::getAppOS();
                if (!$appVersion || !$appBuild || !$appOS) {
                    SlackService::warning('Unknown request source', [
                        'headers' => Util::request()->headers->all(),
                        'path' => Util::request()->getPathInfo()
                    ], [
                        SlackService::MENTION_JERRY
                    ]);
                    throw new DeniedException('Unknown request source');
                }
                $em = Util::em();
                /** @var AppVersion $version */
                $version = $em->getRepository(AppVersion::class)
                    ->findOneBy([
                        'cardProgram' => CardProgram::spendr(),
                        'os' => $appOS
                    ]);
                if ($version && $this->needUpgrade($appVersion, $appBuild, $version)) {
                    if ($this->user) {
                        $token = Util::em()->getRepository(UserToken::class)
                            ->findOneBy([
                                'user' => $this->user
                            ]);
                        if ($token) {
                            $em->remove($token);
                            $em->flush();
                        }
                    }
                    throw new FailedException(
                        $version->getDescription() ? $version->getDescription() : 'We have made an important update, please update the app version before using it',
                        [
                            'update version' => true
                        ],
                        401
                    );
                }
            }
            // Web app request
            elseif (
                (!$appVersion || !$appBuild) &&
                Util::request()->headers->get('origin') !== SpendrBundle::getApiDomain() &&
                Util::request()->headers->get('origin') !== SpendrBundle::getAppDomain() &&
                Util::request()->headers->get('origin') !== SpendrBundle::getAdminDomain() &&
                !in_array($path, ['/spendr/m/consumer/addBank', '/spendr/m/delete-account/index', '/spendr/m/delete-account/submit']) &&
                !starts_with($path, '/spendr/m/referral') &&
                !starts_with($path, '/spendr/m/redirect') &&
                !starts_with($path, '/spendr/m/consumer/onboard/')
            ) {
                SlackService::warning('Unknown request source', [
                    'headers' => Util::request()->headers->all(),
                    'path' => Util::request()->getPathInfo()
                ], [
                    SlackService::MENTION_JERRY
                ]);
                throw new DeniedException('Unknown request source');
            }
        }
	}

	protected function limitTimes($path)
    {
        $publicApi = [
            '/spendr/m/country-list',
            '/spendr/m/register',
            '/spendr/m/register/resend-email',
            '/spendr/m/register/verify-code',
            '/spendr/m/login/email',
            '/spendr/m/forgot-password',
            '/spendr/m/forgot-password/verify-code',
            '/spendr/m/forgot-password/reset-password',
        ];
        if (Util::startsWith($path, '/spendr/m/country/') && Util::endsWith($path, 'states')
            || in_array($path, $publicApi)) {
            return 15;
        }

        return 40;
    }

    public function needUpgrade($version, $build, AppVersion $newest)
    {
        $c = explode('.', $version);
        $n = explode('.', $newest->getVersion());
        if ($c[0] < $n[0] ||
            ($c[0] === $n[0] && $c[1] < $n[1]) ||
            ($c[0] === $n[0] && $c[1] === $n[1] && $c[2] < $n[2])
        ) {
            return true;
        } elseif ($build < $newest->getBuild()) {
            return true;
        }

        return false;
    }

	public function getUser(): ?UserInterface
	{
		if (Util::$user) {
			return Util::$user;
		}

		$ut = $this->getApiUserToken();
		if (!$ut || $ut->isExpired()) {
			return null;
		}
		$userId = $ut->decodeUserId();
		$user = $this->em->getRepository(\SalexUserBundle\Entity\User::class)->find($userId);
		if (!Util::eq($user, $ut->getUser())) {
			return null;
		}
		Util::$user = $user;

		return $user;
	}

    public function supportMsgInfo()
    {
        $platform = Util::platform();
        $platformMeta = Util::meta($platform);
        $supportPhone = $platformMeta['customerSupportPhone'] ?? null;
        $supportEmail = $platformMeta['customerSupportEmail'] ?? null;
        $supportMessage = '';
        if ($supportPhone) {
            $supportMessage .= ' Support phone: ' . $supportPhone;
        }
        if ($supportEmail) {
            $supportMessage .= ', email: ' . $supportEmail;
        }
        return $supportMessage;
    }
}
