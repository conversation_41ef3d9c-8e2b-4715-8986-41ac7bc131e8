<?php

namespace SpendrBundle\Controller\Mobile\Consumer;

use ClassesWithParents\F;
use CoreBundle\Response\DeniedResponse;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Util;
use <PERSON>pendr<PERSON><PERSON>le\Entity\Location;
use SpendrBundle\Entity\Product;
use SpendrBundle\Entity\SpendrMerchant;
use SpendrBundle\Services\MerchantService;
use SpendrBundle\SpendrBundle;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class ProductsController extends BaseController
{
	/**
	 * @Route("/spendr/m/consumer/product/list")
	 *
	 * @param Request $request
	 */
	public function indexAction(Request $request)
	{
		$type = $request->get('type', 'all');
		$page = $request->get('page', 1);
		$pageSize = $request->get('pageSize', 6);
		$productType = $request->get('productType');
		$primary = $request->get('primaryEffect');
		$rate = $request->get('rate');

		$expr = Util::expr();
        $query = $this->em->getRepository(Product::class)
            ->createQueryBuilder('p')
            ->where('p.createBy = :userId')
            ->setParameter('userId', $this->user->getId());

        if ($type && $type !== 'all') {
            $query->andWhere($expr->eq('p.type', ':type'))
                ->setParameter('type', $type);
            if ($type == 'Cannabis' && $primary) {
                $query->andWhere('p.primaryEffect = :primary')
                    ->setParameter('primary', $primary);
            }
        }
        if ($productType) {
            $query->andWhere('p.productType = :productType')
                ->setParameter('productType', $productType);
        }
        if ($rate) {
            $query->andWhere('p.rate = :rate')
                ->setParameter('rate', $rate);
        }

		$total = (int)(
		(clone $query)->select('count(distinct p)')
			->getQuery()
			->getSingleScalarResult()
		);

		$result = [
			'total' => $total,
			'data' => [],
            'cannabisType' => Product::CANNABIS_TYPE,
            'productType' => Product::PRODUCT_TYPE,
            'primaryEffect' => Product::PRIMARY_EFFECT,
		];

		$all = $query->setFirstResult(($page - 1) * $pageSize)
			->setMaxResults($pageSize)
			->getQuery()
			->getResult();

		/** @var Product $item */
		foreach ($all as $item) {
			$p = $item->toApiArray();
			if ($item->getPhoto()) {
				$p['photo'] = str_replace("\/", "/", $item->getPhoto());
				$p['photo'] = SpendrBundle::getAppDomain() . $p['photo'];
			}
			$result['data'][] = $p;
		}

		return new SuccessResponse($result);
	}

    /**
     * @Route("/spendr/m/consumer/product/detail/{product}")
     * @param Product $product
     * @return DeniedResponse|FailedResponse|SuccessResponse
     */
	public function detailAction(Product $product)
    {
        if (SpendrBundle::isSpendrAdminLoggedInAs()) {
            return new DeniedResponse();
        }
        if (!$product || $product->getCreateBy() != $this->user->getId()) {
            return new FailedResponse('The product does not exist!');
        }
        $p = $product->toApiArray(true);
        if ($product->getPhoto()) {
            $p['photo'] = str_replace("\/", "/", $product->getPhoto());
            $p['photo'] = SpendrBundle::getAppDomain() . $p['photo'];
        }

        return new SuccessResponse($p);
    }

	/**
	 * @Route("/spendr/m/consumer/product/prepare")
	 *
	 * @return SuccessResponse
	 */
	public function prepareData()
	{
		$data = [
			'merchantBusinessType' => [
				Product::TYPE_CANNABIS,
				Product::TYPE_NON_CANNABIS,
			],
			'plantType' => Product::PLANT_TYPE,
			'primaryEffect' => Product::PRIMARY_EFFECT,
            'cannabisType' => Product::CANNABIS_TYPE,
			'productType' => Product::PRODUCT_TYPE,
		];
		$data['merchants'] = null;

		$query = $this->em->getRepository(Location::class)
            ->createQueryBuilder('l')
            ->join('l.merchant', 'm')
            ->where('l.status = :status')
            ->setParameter('status', Location::STATUS_ACTIVE);

		$merchantIdsForTest = MerchantService::merchantIdsForTestArray();
		if ($merchantIdsForTest) {
			$query->andWhere(Util::expr()->notIn('m.id', ':merchantIdsForTest'))
				->setParameter('merchantIdsForTest', $merchantIdsForTest);
		}

		$merchants = $query->getQuery()
			->getResult();

		if ($merchants) {
			/** @var Location $item */
			foreach ($merchants as $item) {
				$data['merchants'][] = [
					'id' => $item->getId(),
					'name' => $item->getName(),
				];
			}
		}

		return new SuccessResponse($data);
	}

	/**
	 * @Route("/spendr/m/consumer/product/create", methods={"POST"})
	 *
	 * @param Request $request
	 * @return FailedResponse|SuccessResponse
	 */
	public function createAction(Request $request)
	{
        if (SpendrBundle::isSpendrAdminLoggedInAs()) {
            return new DeniedResponse();
        }
		$data = $request->request->all();
		if (!$data['name'] || !$data['productType']) {
			return new FailedResponse('The Product Name and Product Type are required to submit.');
		}
        if (strlen($data['description']) > 180) {
            return new FailedResponse('Description should not exceed 250 characters');
        }
        if (strlen($data['review']) > 250) {
            return new FailedResponse('Review should not exceed 250 characters');
        }

		$merchant = null;
        if ($data['merchantId']) {
            $merchant = $this->em->getRepository(Location::class)
                ->find($data['merchantId']);
            if (!$merchant) {
                return new FailedResponse('Unknown merchant.');
            }
        }

		$type = isset($data['type']) && ((int)($data['type']) === 1) ? Product::TYPE_CANNABIS : Product::TYPE_NON_CANNABIS;

		if ($data['photo']) {
			$data['photo'] = str_replace("\/", "/", $data['photo']);
			$data['photo'] = str_replace([SpendrBundle::getApiDomain(), SpendrBundle::getAppDomain(), SpendrBundle::getAdminDomain()], "", $data['photo']);
		}

		if ($request->get('productId')) {
            $product = $this->em->find(Product::class, $request->get('productId'));
            if (!$product || $product->getCreateBy() != $this->user->getId()) {
                return new FailedResponse('The product does not exist!');
            }
        } else {
            $product = new Product();
        }
		$product->setName($data['name'])
			->setType($type)
			->setPhoto($data['photo'])
			->setLocation($merchant)
            ->setMerchantName($data['merchantName'])
			->setProductType($data['productType'])
			->setDescription($data['description'])
			->setStrainName($data['strainName'])
			->setPlantType($data['plantType'])
			->setPrimaryEffect($data['primaryEffect'])
			->setThc($data['thc'])
			->setCbd($data['cbd'])
			->setReview($data['review'])
			->setRate($data['rate'])
			->persist();

		return new SuccessResponse($product->toApiArray());
	}

    /**
     * @Route("/spendr/m/consumer/product/del/{product}")
     * @param Product $product
     *
     * @return FailedResponse|SuccessResponse
     * @throws \Doctrine\ORM\OptimisticLockException
     */
	public function deleteProduct(Product $product)
    {
        if (SpendrBundle::isSpendrAdminLoggedInAs()) {
            return new DeniedResponse();
        }
        if (!$product || $product->getCreateBy() != $this->user->getId()) {
            return new FailedResponse('The product does not exist!');
        }
        $this->em->remove($product);
        $this->em->flush();
        return new SuccessResponse();
    }
}
