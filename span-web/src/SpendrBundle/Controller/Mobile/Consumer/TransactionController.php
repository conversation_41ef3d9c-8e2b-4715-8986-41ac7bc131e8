<?php

namespace SpendrBundle\Controller\Mobile\Consumer;

use ApiBundle\Controller\Clf\TransactionControllerTrait;
use Carbon\Carbon;
use ClfBundle\Entity\Account;
use ClfBundle\Entity\Transaction;
use Clf<PERSON><PERSON>le\Entity\TransactionStatus;
use Clf<PERSON><PERSON>le\Entity\TransactionType;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserCardBalance;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Entity\UserFeeHistory;
use CoreBundle\Exception\FailedException;
use CoreBundle\Response\DeniedResponse;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\JsonResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Queue;
use CoreBundle\Utils\Util;
use SalexUserBundle\Entity\User;
use SpendrBundle\Entity\Location;
use SpendrBundle\Entity\SpendrMerchant;
use SpendrBundle\Entity\SpendrPayToken;
use SpendrBundle\Entity\TippingEntity;
use SpendrBundle\Services\BrazeService;
use SpendrBundle\Services\ConsumerService;
use SpendrBundle\Services\FeeService;
use SpendrBundle\Services\LoadService;
use SpendrBundle\Services\MerchantService;
use SpendrBundle\Services\MQTT\PublishService;
use SpendrBundle\Services\SlackService;
use SpendrBundle\Services\TipService;
use SpendrBundle\Services\TransactionService;
use SpendrBundle\SpendrBundle;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

use SpendrBundle\Services\UserService;

class TransactionController extends BaseController
{
	use TransactionControllerTrait;
	use DepositControllerTrait;

	/**
	 * @Route("/spendr/m/consumer/transaction/list/deprecated")
	 *
	 * @param Request $request
	 * @return SuccessResponse
	 * @throws \Doctrine\ORM\NoResultException
	 * @throws \Doctrine\ORM\NonUniqueResultException
	 * @throws \PortalBundle\Exception\PortalException
	 */
	public function indexAction(Request $request)
	{
		$page = $request->get('page', 1);
		$pageSize = $request->get('pageSize', 6);
		$date = $request->get('date');
		$endDate = $request->get('endDate') ?? $date;

		$expr = Util::expr();
		$query = $this->em->getRepository(Transaction::class)
			->createQueryBuilder('t')
			->where($expr->eq('t.consumer', ':consumer'))
			->andWhere($expr->in('t.type', ':types'))
			->andWhere($expr->in('t.status', ':status'))
			->setParameter('consumer', $this->user)
			->setParameter('types', TransactionType::gets([
				TransactionType::NAME_LOAD,
				TransactionType::NAME_UNLOAD,
				TransactionType::NAME_PURCHASE,
				TransactionType::NAME_REFUND,
				TransactionType::NAME_REWARD,
			]))
			->setParameter('status', TransactionStatus::gets([
				TransactionStatus::NAME_COMPLETED,
			]));

		if ($date) {
			$start = Carbon::parse($date)->startOfDay();
			$end = Carbon::parse($endDate)->endOfDay();
			$query->andWhere('t.dateTime >= :__startTime')
				->setParameter('__startTime', $start);
			$query->andWhere('t.dateTime <= :__endTime')
				->setParameter('__endTime', $end);
		}

		$total = (int)(
		(clone $query)->select('count(distinct t)')
			->getQuery()
			->getSingleScalarResult()
		);

		$result = [
			'total' => $total,
			'currentBalance' => null,
			'data' => [],
		];

		$all = $query->orderBy('t.dateTime', 'desc')
			->setFirstResult(($page - 1) * $pageSize)
			->setMaxResults($pageSize)
			->getQuery()
			->getResult();

		/** @var Transaction $item */
		foreach ($all as $item) {
			$date = Util::formatDateTime(
				$item->getDateTime(),
				Util::DATE_TIME_FORMAT,
				$this->tz
			);
			$newItem = $item->toApiArray(true);
			$newItem['date'] = $date;
			$result['data'][] = $newItem;
		}

		$userCard = UserService::getDummyCard($this->user);
		$balance = $userCard->getBalance();
		$result['currentBalance'] = [
			'balance' => Money::format($balance, 'USD', false),
			'name' => $this->user->getName(),
		];

		return new SuccessResponse($result);
	}

	/**
	 * @Route("/spendr/m/consumer/transaction/list")
	 *
	 * @param Request $request
	 * @return SuccessResponse
	 * 
	 * @throws \PortalBundle\Exception\PortalException
	 */
	public function indexAllAction(Request $request)
	{
		$total = $this->getTransactionResult($request, 'count');
		$list = $this->getTransactionResult($request, 'list');
		$dummy = UserService::getDummyCard($this->user);

		$result = [
			'total' => $total && $total['total'] ? (int)$total['total'] : 0,
			'currentBalance' => null,
			'data' => [],
		];

		foreach ($list as $r) {
			$data = [];
			$time = $r['date_time'];
			if ($r['data_from'] === 'transaction') {
				$data = $this->handleTransactionData($r);
//				$time = $data['txnTime'] ?: $time;
			} else if ($r['data_from'] === 'load') {
				$data = $this->handleLoadData($r);
			} else if ($r['data_from'] === 'fee') {
				$data = $this->handleFeeData($r);
			}
			if (!$data) {
				continue;
			}
			$date = Util::formatDateTime(
				$time,
				Util::DATE_TIME_FORMAT,
				$this->tz
			);
			$data['date'] = $date;
			$data['dataFrom'] = $r['data_from'];
			$result['data'][] = $data;
		}

		$balance = $dummy ? $dummy->getBalance() : 0;
		$result['currentBalance'] = [
			'balance' => Money::format($balance, 'USD', false),
            'balanceAmount' => $balance,
			'name' => $this->user->getName(),
		];

		return new SuccessResponse($result);
	}

	/**
	 * @param Request $request
	 * @param string $type
	 * @return array
	 * 
	 * @throws \PortalBundle\Exception\PortalException
	 */
	protected function getTransactionResult(Request $request, $type = 'list')
	{
		$page = $request->get('page', 1);
		$pageSize = $request->get('pageSize', 6);
		$date = $request->get('date');
        $endDate = $request->get('endDate') ?? $date;

		$dummy = UserService::getDummyCard($this->user);

		$transactionTypes = TransactionType::gets([
			TransactionType::NAME_PURCHASE,
			TransactionType::NAME_REFUND,
		]);
		$typeIds = [];
		/** @var TransactionType $t */
		foreach ($transactionTypes as $t) {
			$typeIds[] = $t->getId();
		}

		$transactionTypeIdStr = implode(',', $typeIds);
		$transactionStatusId = TransactionStatus::get(TransactionStatus::NAME_COMPLETED)->getId();

		$loadSubSql = '';
		$txnSubSql = '';
		$feeSubSql = '';
		if ($date) {
			$start = Util::timeUTC($date);
			$end = Util::timeUTC($endDate)->addDay();
			$txnSubSql .= ' AND t.date_time >= "' . $start . '"';
			$txnSubSql .= ' AND t.date_time <= "' . $end . '"';
			$loadSubSql .= ' AND ucl.initialized_at >= "' . $start . '"';
			$loadSubSql .= ' AND ucl.initialized_at <= "' . $end . '"';
			$feeSubSql .= ' AND ufh.time >= "' . $start . '"';
			$feeSubSql .= ' AND ufh.time <= "' . $end . '"';
		}

		$txnSql = 'SELECT t.id as id, t.date_time as date_time, "transaction" as data_from
					FROM clf_transaction t
					WHERE t.consumer_id = ' . $this->user->getId() . '
					AND t.type_id in (' . $transactionTypeIdStr . ')
					AND t.status_id in (' . $transactionStatusId . ')
					' . $txnSubSql;

		$loadSql = 'SELECT ucl.id as id, ucl.initialized_at as date_time, "load" as data_from
					FROM user_card_load ucl
					INNER JOIN user_card uc ON ucl.user_card_id = uc.id
					WHERE uc.id = ' . $dummy->getId() . '
					AND ucl.load_status IN (
						"' . UserCardLoad::LOAD_STATUS_LOADED . '",
						"' . UserCardLoad::LOAD_STATUS_RECEIVED . '",
						"' . UserCardLoad::LOAD_STATUS_INITIATED . '",
						"' . UserCardLoad::LOAD_STATUS_ERROR . '"
					)
					AND ucl.meta NOT LIKE "%' . LoadService::LOAD_TYPE_ROLLBACK . '%"
					AND ucl.meta NOT LIKE "%Spendr rollback%"
					AND ucl.initial_amount > 0
					' . $loadSubSql . '
					AND uc.deleted_at IS NULL';

		$meta = "'" . '%"spendrMeta":true%' . "'";
		$feeSql = 'SELECT ufh.id as id, ufh.time as date_time, "fee" as data_from
				   FROM user_fee_history ufh
				   WHERE ufh.user_id = ' . $this->user->getId() . '
				   AND ufh.fee_name IN (
				        "' . FeeService::TO_SPENDR_CONSUMER_ACH_RETURN_FEE . '",
				        "' . FeeService::TO_SPENDR_CONSUMER_MONTHLY_INACTIVITY_FEE . '"
				   )
				   AND ufh.meta like ' . $meta . $feeSubSql;

		if ($type === 'count') {
			$sql = '
				SELECT count(*) as total FROM (
					(' . $txnSql . ') UNION (' . $loadSql . ') UNION (' . $feeSql . ')
				) AS transactions;
			';
		} else {
			$sql = '
				SELECT * FROM (
					(' . $txnSql . ') UNION (' . $loadSql . ') UNION (' . $feeSql . ')
				) AS transactions
				ORDER BY date_time desc
				LIMIT '.($page - 1) * $pageSize.','.$pageSize.';
			';
		}

		$query = Util::em()->getConnection()->executeQuery($sql);

		return $type === 'count' ? $query->fetchAssociative() : $query->fetchAllAssociative();
	}

	protected function handleTransactionData($data)
	{
		if (
			!$data
			|| !is_array($data)
			|| !$data['id']
			|| !$data['data_from']
			|| $data['data_from'] !== 'transaction'
		) {
			return null;
		}

		/** @var Transaction $transaction */
		$transaction = Transaction::find($data['id']);

		if (!$transaction) {
			return null;
		}

		$result = $transaction->toApiArray(true);

		$symbol = '';
		if ($transaction->getType()->getName() === TransactionType::NAME_PURCHASE) {
			$symbol = '-';
		} else if ($transaction->getType()->getName() === TransactionType::NAME_REFUND) {
			$symbol = '+';
		}

		return array_merge($result, [
			'title' => $result['location'],
			'subtitle' => $result['typeDisplayName'],
			'amountText' => $result['amount'] ? $symbol . $result['amount'] : null
		]);
	}

	protected function handleLoadData($data)
	{
		if (
			!$data
			|| !is_array($data)
			|| !$data['id']
			|| !$data['data_from']
			|| $data['data_from'] !== 'load'
		) {
			return null;
		}

		/** @var UserCardLoad $load */
		$load = UserCardLoad::find($data['id']);

		if (!$load) {
			return null;
		}

		return $load->toAppApiArray(true);
	}

	protected function handleFeeData($data)
	{
		if (
			!$data
			|| !is_array($data)
			|| !$data['id']
			|| !$data['data_from']
			|| $data['data_from'] !== 'fee'
		) {
			return null;
		}

		/** @var UserFeeHistory $feeHistory */
		$feeHistory = UserFeeHistory::find($data['id']);

		if (!$feeHistory) {
			return null;
		}

		$amount = Money::formatUSD($feeHistory->getAmount(), false);
		$res = [
			'amount' => $amount,
			'amountText' => $feeHistory->getAmount() ? '-' . $amount : null,
			'title' => 'Return Fee',
			'subtitle' => 'ACH Return Fee',
			'type' => 'ACH Return Fee',
			'typeDisplayName' => 'ACH Return Fee',
			'reason' => 'Fee generated due to deposit failure',
			'notifyMsg' => 'ACH Return Fee',
			'displayList' => [
				[
					'title' => 'Payment Source',
					'value' => 'Spendr Balance'
				], [
					'title' => 'Fee Total',
					'value' => $amount
				], [
					'title' => 'Time',
					'value' => Util::formatDateTime(
						$feeHistory->getTime(),
						Util::DATE_TIME_FORMAT,
						$this->tz
					),
				],
			]
		];
        if ($feeHistory->getFeeName() === FeeService::TO_SPENDR_CONSUMER_MONTHLY_INACTIVITY_FEE) {
            $res = array_merge($res, [
                'title' => 'Inactivity Fee',
                'subtitle' => 'Fee',
                'type' => 'Inactivity Fee',
                'typeDisplayName' => 'Inactivity Fee',
                'reason' => 'Fee charged after 24 months of inactivity',
                'notifyMsg' => 'Inactivity Fee',
            ]);
        }

		return $res;
	}

	public function checkPendingTransaction(Request $request, $token)
	{
		$rs = TransactionService::getNotExpiredTransactionsByToken($token);
		if (!$rs) {
		  return new FailedResponse('Unknown transaction or outdated!');
		}

		/** @var Transaction $t */
		$t = $rs[0];
		if ($t->getStatus()->getName() !== TransactionStatus::NAME_PENDING) {
		  return new FailedResponse('Transaction has been completed or canceled!');
		}

		$merchant = $t->getMerchant();
		if (!$merchant || ($merchant && !$merchant->isActive())) {
		  return new FailedResponse('Unknown transaction merchant!');
		}

		$user = $this->getUser();
		$location = $t->getLocation();
		if (!$location || ($location && $location->getStatus() !== Location::STATUS_ACTIVE)) {
		  return new FailedResponse('Unknown transaction location!');
		}

		$terminal = $t->getTerminal();
		if (!$terminal) {
		  return new FailedResponse('Unknown transaction terminal!');
		}

		$employee = $t->getLocationEmployee();
		if (Util::eq($user->getId(), $employee->getId())) {
		  return new FailedResponse('Invalid transaction! It is created by yourselves.');
		}

		if ($merchant instanceof SpendrMerchant && !$user->isSpendrConsumer()) {
		  return new FailedResponse('You are not permitted for this transaction!');
		}

		$consumer = $t->getConsumer();
		if ($consumer && $consumer->getId() !== $this->user->getId()) {
			return new FailedResponse('You are not the consumer of this transaction!');
		}

	  	return $t;
	}

	/**
	 * @Route("/spendr/m/consumer/transaction/confirm", methods={"GET"})
	 *
	 * @param Request $request
	 * @return JsonResponse
	 * @throws \Doctrine\ORM\OptimisticLockException
	 */
	public function confirmAction(Request $request)
	{
		$isCanTransaction = ConsumerService::isCanTransaction($this->user, 'app');
		if (is_string($isCanTransaction)) {
			return new FailedResponse($isCanTransaction);
		}

		$from = $request->get('from'); // push/qrcode
		if (!$from) {
			return new FailedResponse('Invalid parameters.');
		}
		/** @var Transaction $t */
		$t = $this->checkPendingTransaction($request, $request->get('token'));
		if ($t instanceof JsonResponse) {
			return $t;
		}

		$consumer = $t->getConsumer();
        if (!$consumer) {
            $t->setConsumer($this->user)
				->setAccount($this->getAccount());
            $this->em->persist($t);
            $this->em->flush();

			Util::updateMeta($t, [
				'userGetTxnFrom' => $from
			]);
		}

        $data = $t->toApiArray(true);
        $data['_from_'] = $from;

		try {
			PublishService::pushByUserToken(
				$this->user->getId(),
				PublishService::TOPIC_SEND_SCAN_RESULT_TO_MERCHANT,
				$t->getLocationEmployee(),
				[
					'success' => true,
					'message' => '',
					'data' => $data,
				],
				$t->getToken()
			);
		} catch (FailedException $e) {
			Log::error($e->getFullMessage(), [$e->data]);
		}

		$result = $this->getConsumerTxnData($t);

		$consumerDummy = UserService::getDummyCard($this->user);
		$consumerBalance = $consumerDummy->getBalance();
		$result['consumerCurrentBalance'] = $consumerBalance < 0 ? 0 : $consumerBalance;

		return new SuccessResponse($result);
	}

	/**
	 * @Route("/spendr/m/consumer/transactions/operate", methods={"POST"})
	 * @param Request $request
	 *
	 * @return JsonResponse
	 * @throws \Doctrine\ORM\OptimisticLockException
	 * @throws \PortalBundle\Exception\PortalException
	 * @throws \Throwable
	 */
	public function acceptAction(Request $request)
	{
		if (SpendrBundle::isSpendrAdminLoggedInAs()) {
			return new DeniedResponse();
		}

		$user = $this->user;
		$token = $request->get('token');
		$operation = $request->get('operation');
		$appId = BrazeService::getAppId($request->get('device'));
		$tipAmount = (int)$request->get('tipAmount', 0);
		$t = null;

		if ($operation === 'accept') {
		    if ($tipAmount < 0) {
		        return new FailedResponse('The tip amount should be more than 0 if you want to add it!');
            }

			$isRestrictAreaTxn = ConsumerService::isRestricted($user);

			$userCardId = null;
			$userCard = null;

			$t = TransactionService::beforePay($user, $token, $this->tz, $tipAmount, true, $isRestrictAreaTxn);
			if (is_string($t)) {
			    $data = null;
			    if ($t === 'Your Spendr balance is insufficient for the payment!') {
                    $consumerDummy = UserService::getDummyCard($user);
                    $balance = Money::formatAmountToNumber($consumerDummy ? $consumerDummy->getBalance() : 0);
                    $txn = Transaction::findByToken($token);
                    $data = [
                        'Current Balance' => $balance,
                        'properties' => [
                            'current balance' => $balance,
                            'failed payment amount' => Money::formatAmountToNumber($txn ? $txn->getAmount() : 0)
                        ]
                    ];
                }
				return new FailedResponse($t, $data);
			}

			if ($isRestrictAreaTxn || $t->isFromPayApi()) {
			    $group = $t->getMerchant()->getGroup();
			    if ($group && !SpendrPayToken::findBy($user, $group)) {
			        // generate spendr pay token
                    SpendrPayToken::store($user, $group);
                }
                $loadAmount = $this->calculateRestrictAreaLoadAmount($user, $token, $tipAmount);
                
                $tp = Transaction::TXN_AREA_TYPE_RESTRICT;
                
				// create load
				if ($loadAmount > 0) {
                    $userCardId = $request->get('cardId');
                    if (!$userCardId) {
                        return new FailedResponse('Invalid bank card parameters.');
                    }
                    $userCard = UserCard::find($userCardId);
                    if (!$userCard || ($userCard->getUser() && $userCard->getUser()->getId() !== $user->getId())) {
                        return new FailedResponse('Invalid bank card.');
                    }

                    // only instant status can transaction
                    $loadStatus = ConsumerService::getConsumerLoadStatus($user);
                    if ($loadStatus !== ConsumerService::LOAD_STATUS_INSTANT) {
                        return new FailedResponse(LoadService::LOAD_STATUS_ERROR_MSG_BEFORE_TXN);
                    }

					$txnId = $t->getId();
					$loadResponse = self::traitLoad($userCard, $loadAmount, $user, $txnId);
					$loadId = null;
					if ($loadResponse instanceof SuccessResponse) {
						$loadData = $loadResponse->getResult();
						if (!isset($loadData['loadId'])) {
							return new FailedResponse('Failed to create load, please try again.');
						}
						$loadId = $loadData['loadId'];
					} else {
						return $loadResponse;
					}

					Data::set('spendr_confirm_payment_' . $token, true, false, 600);
					$this->setAreaType($t, $tp, $loadId);

					Log::debug('=== transaction start(Restrict area): add to queue.', [$t->getStatus()->getName()]);
					Queue::spendrRetrictAreaPayTransaction($token, $loadId, $appId, $this->tz);
				} else {
                    Data::set('spendr_confirm_payment_' . $token, true, false, 600);
                    $this->setAreaType($t, $tp);

					Log::debug('=== transaction start(Restrict area - normal): add to queue.', [$t->getStatus()->getName()]);
					Queue::spendrPayTransaction($token, $appId, $this->tz);
				}
			} else {
                Data::set('spendr_confirm_payment_' . $token, true, false, 600);
				$this->setAreaType($t, Transaction::TXN_AREA_TYPE_NON_RESTRICT);
				Log::debug('=== transaction start: add to queue.', [$t->getStatus()->getName()]);
				Queue::spendrPayTransaction($token, $appId, $this->tz);
			}
		} else {
			return new FailedResponse('Invalid transaction type.');
		}

		$result = TransactionService::getPayResult($t);
		Log::debug('=== transaction finished: response. ', [$t->getStatus()->getName()]);
		return new SuccessResponse($result);
	}

	private function setAreaType(Transaction $t, $area, $restrictoadId = null)
	{
		$t->setTxnAreaType($area);
		if ($restrictoadId) {
			$load = UserCardLoad::find($restrictoadId);
			$t->setRestrictLoad($load);
		}

		$this->em->persist($t);
		$this->em->flush();
	}

	protected function calculateRestrictAreaLoadAmount(User $user, $txnToken, $tipAmount)
	{
		$consumerDummy = UserService::getDummyCard($user);
		$consumerBalance = $consumerDummy->getBalance();
		$txn = Transaction::findByToken($txnToken);
		$txnAmount = $txn->getAmount();
		if ($tipAmount) {
			$txnAmount += $tipAmount;
		}
		$loadAmount = 0;
		if ($consumerBalance < 0) {
			$loadAmount += abs($consumerBalance);
			$loadAmount += $txnAmount;
		} else if ($consumerBalance === 0) {
			$loadAmount = $txnAmount;
		} else {
			if ($consumerBalance >= $txnAmount) {
				$loadAmount = 0;
			} else {
				$loadAmount = $txnAmount - $consumerBalance;
			}
		}

		return $loadAmount;
	}

	/**
	 * @Route("/spendr/m/consumer/transaction/latest-pending")
	 *
	 * @param Request $request
	 * @return SuccessResponse
	 */
	public function latestPendingTransaction(Request $request)
	{
		$transaction = TransactionService::getLatestPendingTransactionByUserId($request, $this->user);
        $result = $transaction ? $this->getConsumerTxnData($transaction) : null;

		return new SuccessResponse($result);
	}

	protected function getConsumerTxnData(Transaction $t)
    {
        $result = $t->toApiArray(true);
        $result['subscribePaymentResultTopicUrl'] = PublishService::generateTopicUrl(
            PublishService::TOPIC_SEND_PAYMENT_RESULT_TO_CONSUMER,
            $t->getToken()
        );
        $tip = TippingEntity::findOneByMerchant();
        if (!$tip || !$tip->getEnable()) {
            return $result;
        }
        // If tipping setting is enable
		$merchant = $t->getMerchant();
		$otherMerchant = MerchantService::mergeIntoOtherMerchant($merchant);
		if ($otherMerchant) {
			$merchant = $otherMerchant;
		}

        $tip = TippingEntity::findOneByMerchant($merchant);
        if ($tip && $tip->getEnable() && $tip->getChangeable()) {
            if ($tip->getLocations() && !in_array($t->getLocation()->getId(), $tip->getLocationsArr())) {
                return $result;
            }
            foreach (Util::s2j($tip->getAmounts()) as $tipAmount) {
                $result['tipAmounts'][] = [
                    'amount' => (int)($tip->getFormat() === TipService::TIPS_FORMAT_DOLLAR
                        ? $tipAmount : floor($t->getAmount() * $tipAmount / 100.0))
                ];
            }
        }
        $result['lastLoadCardId'] = ConsumerService::getLastLoadCardId($this->user);

        return $result;
    }

	/**
	 * @Route("/spendr/m/consumer/transaction/status/{token}", methods={"GET"})
	 * @param Request $request
	 * @param         $token
	 *
	 * @return FailedResponse|SuccessResponse
	 */
	public function statusAction (Request $request, $token) {
		$res = TransactionService::checkTxnStatus($request, $token);
		if (is_string($res)) {
			return new FailedResponse($res);
		}
		return new SuccessResponse($res);
	}

}
