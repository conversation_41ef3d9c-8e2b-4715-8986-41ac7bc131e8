<?php

namespace SpendrB<PERSON>le\Controller\Mobile\Consumer;

use Carbon\Carbon;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Queue;
use SpendrBundle\Entity\PromotionCode;
use SpendrBundle\Entity\RewardTarget;
use SpendrB<PERSON>le\Entity\SpendrReferConfig;
use SpendrBundle\Entity\SpendrReferral;
use SpendrBundle\Entity\SpendrReward;
use SpendrBundle\Services\BrazeService;
use SpendrBundle\Services\ConsumerService;
use SpendrBundle\Services\LoadService;
use SpendrBundle\Services\PromotionService;
use SpendrBundle\Services\RewardService;
use SpendrBundle\Services\TransactionService;
use SpendrBundle\Services\UserService;
use SpendrBundle\SpendrBundle;
use Symfony\Component\Routing\Annotation\Route;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Service;
use CoreBundle\Utils\Util;
use SpendrBundle\Services\ChangeBalanceService;
use Symfony\Component\HttpFoundation\Request;

class RewardsController extends BaseController
{
	/**
	 * @Route("/spendr/m/consumer/rewards/list")
	 *
	 * @param Request $request
	 * @return SuccessResponse
	 * @throws \Doctrine\ORM\NoResultException
	 * @throws \Doctrine\ORM\NonUniqueResultException
	 * @throws \PortalBundle\Exception\PortalException
	 */
    public function indexAction(Request $request)
    {
        $page = $request->get('page', 1);
        $pageSize = $request->get('pageSize', 6);
        $dummy = UserService::getDummyCard($this->user);
        $query = $this->getQuery($dummy);
        $total = (int)(
        (clone $query)->select('count(distinct ucl)')
            ->getQuery()
            ->getSingleScalarResult()
        );
        $spend = (int)(
        (clone $query)->select('sum(ucl.initialAmount)')
            ->getQuery()
            ->getSingleScalarResult()
        );

        $result = [
            'total' => $total,
            'total_spend' => Money::format($spend, 'USD', false),
            'data' => [],
        ];

        $all = $query->orderBy('ucl.initializedAt', 'desc')
            ->setFirstResult(($page - 1) * $pageSize)
            ->setMaxResults($pageSize)
            ->getQuery()
            ->getResult();

        /** @var UserCardLoad $item */
        foreach ($all as $item) {
            $result['data'][] = RewardService::getRowData($item);
        }

        return new SuccessResponse($result);
    }

    protected function getQuery(UserCard $dummy)
    {
        return RewardService::getRewardsQuery($dummy);
    }

    /**
     * @Route("/spendr/m/consumer/milestone")
     * @return SuccessResponse
     */
    public function getMilestoneList()
    {
        // mile stone rewards
        $rewards = Util::em()->getRepository(SpendrReward::class)
            ->createQueryBuilder('r')
            ->where('r.status = :status')
            ->setParameter('status', SpendrReward::STATUS_ACTIVE)
            ->andWhere(Util::expr()->in('r.type', ':types'))
            ->setParameter('types', [
                UserCardLoad::EARN_TYPE_BANK_LINK,
                UserCardLoad::EARN_TYPE_SPEND_OVER
            ])
            ->orderBy('r.type')
            ->addOrderBy('r.spendAmount')
            ->getQuery()
            ->getResult();
        $rewards = array_map(function ($reward) {
            return $reward->toAppArray();
        }, $rewards);
        $totalPurchased = TransactionService::getConsumerTotalSpendAmount($this->user);
        $showMilestone = count($rewards) && $totalPurchased < $rewards[count($rewards) - 1]['spendAmount'];

        // spend rewards
        $list = [];
        $show = false;
        $spendRewards = Util::em()->getRepository(SpendrReward::class)->createQueryBuilder('r')
            ->where('r.status = :status')
            ->setParameter('status', SpendrReward::STATUS_ACTIVE)
            ->andWhere('r.type = :type')
            ->setParameter('type', UserCardLoad::EARN_TYPE_SPEND_REWARD)
            ->andWhere('r.amount > 0')
            ->andWhere('r.spendAmount > 0')
            ->orderBy('r.spendAmount')
            ->getQuery()
            ->getResult();
        /** @var SpendrReward $reward */
        foreach ($spendRewards as $reward) {
            if ($reward->getEndAt() && Carbon::now()->isAfter(Carbon::parse($reward->getEndAt()))) {
                $reward->setStatus(SpendrReward::STATUS_INACTIVE)->persist();
                continue;
            }
            if ($reward->getStartAt() && Carbon::now()->isBefore(Carbon::parse($reward->getStartAt()))) {
                continue;
            }
            if (RewardTarget::getCountBy($reward) && !RewardTarget::getCountBy($reward, $this->user)) {
                continue;
            }
            $item = $reward->toAppArray();
            $earned = (bool)ConsumerService::countEarnedRecord($reward, $this->user);
            $purchased = 0;
            if (!$earned) {
                $purchased = (int)TransactionService::getSpendAmountWithReward($this->user, $reward);
                if ($purchased >= $reward->getSpendAmount()) $earned = true;
                else $show = true;
            }
            $item['earned'] = $earned;
            $item['purchased'] = $purchased;
            $list[] = $item;
        }

        return new SuccessResponse([
            'show' => $showMilestone,
            'bank_linked' => count(ConsumerService::getUserBankList($this->user, 'plaid')) > 0,
            'bank_link_type' => UserCardLoad::EARN_TYPE_BANK_LINK,
            'purchased' => $totalPurchased,
            'rewards' => $showMilestone ? $rewards: [],
            'spendRewards'  => $list,
            'showSpendReward' => $show
        ]);
    }

    /**
     * @Route("/spendr/m/consumer/rewards-config")
     * @return SuccessResponse
     * @throws \Doctrine\ORM\Exception\NotSupported
     * @throws \Doctrine\ORM\NoResultException
     * @throws \Doctrine\ORM\NonUniqueResultException
     */
    public function getRewardsSetting()
    {
        $refer = SpendrReferConfig::findOne();
        $referral = $refer ? $refer->toAppArray() : null;
        $count = Util::em()->getRepository(SpendrReferral::class)
            ->createQueryBuilder('r')
            ->where('r.inviter = :inviter')
            ->setParameter('inviter', $this->user)
            ->select('count(r)')
            ->getQuery()
            ->getSingleScalarResult();
        $url = SpendrBundle::getApiDomain() . '/spendr/m/referral?inviterId=' . $this->user->getId();

        return new SuccessResponse([
            'referral' => $referral,
            'referUrl' => $url,
            'referText' => $referral ? sprintf(
                "Earn an extra %s in rewards when you sign up with my link and spend %s. Spendr is free to use and you’ll earn rewards on your purchases:\n%s",
                $referral['getAmount'],
                $referral['spendAmount'],
                $url
            ) : '',
            'maxed' => $count >= ($refer ? $refer->getMaxCount() : 0)
        ]);
    }

    /**
     * @Route("/spendr/m/consumer/rewards/redeem", methods={"POST"})
     *
     * @param Request $request
     * @return SuccessResponse|FailedResponse
     * @throws \PortalBundle\Exception\PortalException
     */
    public function redeemAction(Request $request)
    {
        $code = $request->get('promo_code');
        if (!$code) {
            return new FailedResponse('Please input the promo code.');
        }

        $checkRes = LoadService::beforeRewardLoad($this->user, $code);
        if (is_string($checkRes)) {
        	return new FailedResponse($checkRes);
		}
		/** @var PromotionCode $promo */
        $promo = $checkRes;
        $dummy = UserService::getDummyCard($this->user);
        $userId = $this->user->getId();

        $key = "claim_promo_code:{$userId}";
        if (Data::has($key)) {
            return new FailedResponse('You have promotion load successfully within the last 2 minutes. Please reload data.');
        }
        Data::set($key, $promo->getPromoCodeAmount(), false, 120);
		$load = $dummy->ensureLoad();
		if ($load->getInitializedAt()) {
			$msg = 'This load request had been initialized. Please start a new request from beginning!';
			return new FailedResponse($msg);
		}

        $load->setPromoCode($promo)
            ->setInitialAmount($promo->getPromoCodeAmount())
            ->setPayCurrency('USD')
            ->setPayAmount($promo->getPromoCodeAmount())
            ->setLoadFeeUSD(0)
            ->setInitializedAt(new \DateTime())
            ->setReceivedCurrency('USD')
            ->setReceivedAmount($promo->getPromoCodeAmount())
            ->setLoadStatus(UserCardLoad::LOAD_STATUS_RECEIVED)
            ->setPromoType(PromotionService::PROMOTION_TYPE_PROMO_CODE)
            ->setLoadType(LoadService::LOAD_TYPE_PROMOTION)
            ->setMeta(Util::j2s([
                LoadService::LOAD_TYPE_PROMOTION => true,
                'isPromotion' => true,
                'promoType' => PromotionService::PROMOTION_TYPE_PROMO_CODE,
                'customMessage' => $promo->getName()
            ]));

        Util::persist($load);

        $addToQueue = false;
        $lockKey = ChangeBalanceService::lockKey($userId);
		if ($lockKey) {
			if (Data::lockedHas($lockKey)) {
                $waitRes = ChangeBalanceService::waitOtherTxnsFinished($userId);
                if (!$waitRes) {
                    $addToQueue = true;
                }
            } else {
                $addToQueue = false;
            }
		} else {
            $addToQueue = true;
        }
		
        if ($addToQueue) {
            Queue::spendrPromoCodeRedemption($load->getId());

            return new SuccessResponse(
                RewardService::getRowData($load),
                'This reward will be added to your Spendr balance in a few seconds.'
            );
        } else {
            $res = Service::loadCard($load, true);

            ChangeBalanceService::clearLock($userId);

            LoadService::afterRewardLoad($load, $res);

            $data = RewardService::getRowData($load);
            
            if (is_string($res)) {
                return new FailedResponse($res, $data);
            } else {
                return new SuccessResponse(
                    $data,
                    'This reward has been added to your Spendr balance.'
                );
            }
        }
    }
}
