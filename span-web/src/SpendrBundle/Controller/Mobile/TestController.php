<?php

namespace Spendr<PERSON><PERSON>le\Controller\Mobile;

use Carbon\Carbon;
use Clf<PERSON><PERSON>le\Entity\Transaction;
use Clf<PERSON><PERSON>le\Entity\TransactionStatus;
use Clf<PERSON><PERSON>le\Entity\TransactionType;
use CoreB<PERSON>le\Entity\Attachment;
use CoreB<PERSON>le\Entity\Platform;
use CoreBundle\Entity\UserFeeHistory;
use CoreBundle\Exception\FailedException;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Util;
use Ramsey\Uuid\Uuid;
use SpendrBundle\Services\FeeService;
use SpendrBundle\Services\MerchantService;
use SpendrBundle\Services\MQTT\PublishService;
use SpendrBundle\Services\NameFuzzyMatchService;
use SpendrBundle\Services\Next\RemixService;
use SpendrBundle\Services\SlackService;
use SpendrBundle\Services\TransactionService;
use SpendrBundle\Services\UserService;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class TestController extends \App\Controller\BaseController
{
//	/**
//	 * @Route("/spendr/local-test", methods={"POST"})
//	 *
//	 * @param Request $request
//	 * @return SuccessResponse
//	 */
	public function localTest(Request $request)
	{
		$routingNumber = *********;

//		$userId = *********;
//		$user = User::find($userId);

		return new SuccessResponse([]);
	}


	/**
	 * @Route("/spendr/m/test-mqtt-send")
	 *
	 * @throws FailedException
	 */
	public function mqttSend()
	{
		try {
			PublishService::publish(
                Uuid::uuid4()->toString(),
				'php-mqtt/client/send',
				'test' . time(),
				json_encode(['message' => 'success'])
			);
		} catch (FailedException $e) {
			Log::error($e->getFullMessage());
			throw $e;
		}

		return new SuccessResponse();
	}

    /**
     * @Route("/dev/spendr/next/attachment")
     * @param Request $request
     * @return Response
     */
    public function attachment(Request $request)
    {
        $res = (new RemixService())->attachmentDownload($request->get('path'));
        if ($res instanceof Response) return $res;
        return new FailedResponse($res);
    }

    /**
     * @Route("/dev/spendr/next/attachment-upload")
     * @param Request $request
     * @return mixed
     * @throws \Doctrine\ORM\Exception\NotSupported
     */
    public function uploadAttachmentToRemix(Request $request)
    {
        /** @var Attachment $attachment */
        $attachment = Util::em()->getRepository(Attachment::class)->findOneBy([
            'name' => 'spendr-logo.png'
        ]);
        $res = (new RemixService())->attachmentUpload($attachment->getPath());
        return new SuccessResponse($res['data']);
    }

//	/**
//	 * @Route("/spendr/m/receipt/{txn}/{target}")
//	 * @param Transaction $txn
//	 * @param string $target
//	 * @return Response
//	 */
    public function receipt(Transaction $txn, $target = 'consumer')
    {
        $html = TransactionService::renderReceipt($txn, $target);
        return new Response($html);
    }

    // 1. back wrong spendr fee, from partner to merchant
	// 2. back refund fee, from partner to merchant

//	/**
//	 * @Route("/spendr/admin/correct-refund-fee/spendr-fee/{refundTxnId}")
//	 *
//	 * @param $refundTxnId
//	 * @return FailedResponse|SuccessResponse
//	 * @throws \PortalBundle\Exception\PortalException
//	 * @throws \Throwable
//	 */
    public function correctRefundSpendrFee($refundTxnId)
	{
		if (!$refundTxnId) {
			return new FailedResponse('Invalid parameter.');
		}
		/** @var Transaction $refundTxn */
		$refundTxn = Transaction::find($refundTxnId);

		if (
			($refundTxn->getType()->getName() !== TransactionType::NAME_REFUND)
			|| ($refundTxn->getStatus()->getName() !== TransactionStatus::NAME_COMPLETED)
			|| (!$refundTxn->getSpendrFee())
		) {
			return new FailedResponse('The transaction cannot be corrected.');
		}

		$merchant = $refundTxn->getMerchant();
		$merchantAdmin = $merchant->getAdminUser();
		$merchantDummy = UserService::getDummyCard($merchantAdmin);
		$spendrAdmin = UserService::getSpendrBalanceAdminAccount('spendr');
		$spendrDummy = UserService::getSpendrBalanceDummyCard('spendr');
		// 1. back spendr fee from partner to merchant
//		$refundAmount = $refundTxn->getAmount();

//		$realSpendrFee = FeeService::getRefundTransactionFeeToMerchant($refundAmount);
//		$oldSpendrFee = $refundTxn->getSpendrFee();
//		$difference = $oldSpendrFee - $realSpendrFee;
		$fee = $refundTxn->getSpendrFee();

		$feeName = FeeService::CORRECT_REFUND_SPENDR_FEE;

		if (FeeService::hasFeeHistory($spendrAdmin, $feeName, $refundTxn)) {
			return new FailedResponse('The transaction has already been corrected and cannot be repeated');
		}

		$comment = sprintf(
			'%s. Refund transaction: %s. Partner %s returns the Spendr fee to Merchant %s at %s, amount: %s',
			$feeName,
			$refundTxn->getToken(),
			$spendrAdmin->getSignature(),
			$merchantAdmin->getSignature(),
			Carbon::now(),
			Money::format($fee, 'USD')
		);
		$spendrDummy->updatePrivacyBalanceBy(-$fee, $feeName, $comment, false, $refundTxn);
		$merchantDummy->updatePrivacyBalanceBy($fee, $feeName, $comment, false, $refundTxn);

		$refundTxn->setSpendrFee(null);
		Util::persist($refundTxn);

//		UserFeeHistory::create($spendrAdmin, $fee, $feeName, $refundTxn, $comment, Platform::spendr());
		Util::flush();

		SlackService::tada($comment);

		return new SuccessResponse($refundTxn->toApiArray(true));
	}

//	/**
//	 * @Route("/spendr/admin/correct-refund-fee/refund-fee/{refundTxnId}")
//	 *
//	 * @param $refundTxnId
//	 * @return FailedResponse|SuccessResponse
//	 * @throws \PortalBundle\Exception\PortalException
//	 * @throws \Throwable
//	 */
    public function correctRefundRefundFee($refundTxnId)
	{
		if (!$refundTxnId) {
			return new FailedResponse('Invalid parameter.');
		}
		/** @var Transaction $refundTxn */
		$refundTxn = Transaction::find($refundTxnId);

		if (
			($refundTxn->getType()->getName() !== TransactionType::NAME_REFUND)
			|| ($refundTxn->getStatus()->getName() !== TransactionStatus::NAME_COMPLETED)
			|| ($refundTxn->getRefundFee())
		) {
			return new FailedResponse('The transaction cannot be corrected.');
		}

		$merchant = $refundTxn->getMerchant();
		$merchantAdmin = $merchant->getAdminUser();
		$merchantDummy = UserService::getDummyCard($merchantAdmin);
		$spendrAdmin = UserService::getSpendrBalanceAdminAccount('spendr');
		$spendrDummy = UserService::getSpendrBalanceDummyCard('spendr');
		// 2. back refund fee from partner to merchant
		$fee = FeeService::getRefundTransactionFeeToMerchant($refundTxn->getAmount(), $refundTxn);
		$feeName = FeeService::CORRECT_REFUND_REFUND_FEE_TO_MERCHANT;

		if (!$fee) {
			return new FailedResponse('The transaction cannot be corrected.');
		}

		if (FeeService::hasFeeHistory($spendrAdmin, $feeName, $refundTxn)) {
			return new FailedResponse('The transaction has already been corrected and cannot be repeated');
		}

		$comment = sprintf(
			'%s. Refund transaction: %s. Partner %s returns part of Spendr fee of the payment to Merchant %s at %s, amount: %s',
			$feeName,
			$refundTxn->getToken(),
			$spendrAdmin->getSignature(),
			$merchantAdmin->getSignature(),
			Carbon::now(),
			Money::format($fee, 'USD')
		);
		$spendrDummy->updatePrivacyBalanceBy(-$fee, $feeName, $comment, false, $refundTxn);
		$merchantDummy->updatePrivacyBalanceBy($fee, $feeName, $comment, false, $refundTxn);

		$refundTxn->setRefundFee($fee);
		Util::persist($refundTxn);

//		UserFeeHistory::create($spendrAdmin, $fee, $feeName, $refundTxn, $comment, Platform::spendr());
		Util::flush();

		SlackService::tada($comment);

		return new SuccessResponse($refundTxn->toApiArray(true));
	}

//	/**
//	 * @Route("/spendr/admin/correct-refund-fee/tern-fee/{refundTxnId}")
//	 *
//	 * @param $refundTxnId
//	 * @return FailedResponse|SuccessResponse
//	 * @throws \PortalBundle\Exception\PortalException
//	 * @throws \Throwable
//	 */
	public function correctRefundTernFee($refundTxnId)
	{
		if (!$refundTxnId) {
			return new FailedResponse('Invalid parameter.');
		}
		/** @var Transaction $refundTxn */
		$refundTxn = Transaction::find($refundTxnId);

		if (
			($refundTxn->getType()->getName() !== TransactionType::NAME_REFUND)
			|| ($refundTxn->getStatus()->getName() !== TransactionStatus::NAME_COMPLETED)
			|| (!$refundTxn->getTernFee())
		) {
			return new FailedResponse('The transaction cannot be corrected.');
		}

		$ternAdmin = UserService::getSpendrBalanceAdminAccount('tern');
		$ternDummy = UserService::getSpendrBalanceDummyCard('tern');
		$spendrAdmin = UserService::getSpendrBalanceAdminAccount('spendr');
		$spendrDummy = UserService::getSpendrBalanceDummyCard('spendr');
		// 2. back tern fee from tern to partner
		$fee = $refundTxn->getTernFee();
		$feeName = FeeService::CORRECT_REFUND_TERN_FEE;

		if (!$fee) {
			return new FailedResponse('The transaction cannot be corrected.');
		}

		if (FeeService::hasFeeHistory($ternAdmin, $feeName, $refundTxn)) {
			return new FailedResponse('The transaction has already been corrected and cannot be repeated');
		}

		$comment = sprintf(
			'%s. Refund transaction: %s. Tern %s returns the Tern fee to Partner %s at %s, amount: %s',
			$feeName,
			$refundTxn->getToken(),
			$ternAdmin->getSignature(),
			$spendrAdmin->getSignature(),
			Carbon::now(),
			Money::format($fee, 'USD')
		);
		$ternDummy->updatePrivacyBalanceBy($fee, $feeName, $comment, false, $refundTxn);
		$spendrDummy->updatePrivacyBalanceBy(-$fee, $feeName, $comment, false, $refundTxn);

		$refundTxn->setTernFee(null);
		Util::persist($refundTxn);

//		UserFeeHistory::create($ternAdmin, $fee, $feeName, $refundTxn, $comment, Platform::spendr());
		Util::flush();

		SlackService::tada($comment);

		return new SuccessResponse($refundTxn->toApiArray(true));
	}

//	/**
//	 * @Route("/spendr/admin/correct-txn-fee/spendr-fee/{txnId}")
//	 *
//	 * @param $txnId
//	 * @return FailedResponse|SuccessResponse
//	 * @throws \PortalBundle\Exception\PortalException
//	 * @throws \Throwable
//	 */
	public function correctTxnSpendrFee($txnId)
	{
		if (!$txnId) {
			return new FailedResponse('Invalid parameter.');
		}
		/** @var Transaction $txn */
		$txn = Transaction::find($txnId);

		if (
			!$txn->getMerchant()
			|| (
				$txn->getMerchant()
				&& !in_array($txn->getMerchant()->getId(), MerchantService::merchantIdsForTestArray())
			)
			|| (!in_array($txn->getType()->getName(), [
			    TransactionType::NAME_PURCHASE
            ]))
			|| ($txn->getStatus()->getName() !== TransactionStatus::NAME_COMPLETED)
			|| $txn->getRefundFrom()
			|| !$txn->getSpendrFee()
		) {
			return new FailedResponse('The transaction cannot be corrected.');
		}

		$spendrAdmin = UserService::getSpendrBalanceAdminAccount('spendr');
		$spendrDummy = UserService::getSpendrBalanceDummyCard('spendr');

		$spendrAdminTest = UserService::getSpendrBalanceAdminAccount('spendr', true);
		$spendrDummyTest = UserService::getSpendrBalanceDummyCard('spendr', true);

		// 1. move spendr fee to test spendr balance admin account
		$spendrFee = $txn->getSpendrFee();
		$feeName = FeeService::CORRECT_TRANSACTION_SPENDR_FEE;

		if (FeeService::hasFeeHistory($spendrAdmin, $feeName, $txn)) {
			return new FailedResponse('The transaction has already been corrected and cannot be repeated');
		}

		$comment = sprintf(
			'%s. Transaction: %s. Moved from official Spendr wallet(%s) to Test Spendr wallet(%s) at %s, amount: %s',
			$feeName,
			$txn->getToken(),
			$spendrAdmin->getSignature(),
			$spendrAdminTest->getSignature(),
			Carbon::now(),
			Money::format($spendrFee, 'USD')
		);
		$spendrDummy->updatePrivacyBalanceBy(-$spendrFee, $feeName, $comment, false, $txn);
		$spendrDummyTest->updatePrivacyBalanceBy($spendrFee, $feeName, $comment, false, $txn);

		UserFeeHistory::create($spendrAdmin, $spendrFee, $feeName, $txn, $comment, Platform::spendr());

		Util::flush();

		SlackService::tada($comment);

		return new SuccessResponse($txn->toApiArray(true));
	}


//	/**
//	 * @Route("/spendr/admin/correct-txn-fee/tern-fee/{txnId}")
//	 *
//	 * @param $txnId
//	 * @return FailedResponse|SuccessResponse
//	 * @throws \PortalBundle\Exception\PortalException
//	 * @throws \Throwable
//	 */
	public function correctTxnTernFee($txnId)
	{
		if (!$txnId) {
			return new FailedResponse('Invalid parameter.');
		}
		/** @var Transaction $txn */
		$txn = Transaction::find($txnId);

		if (
			!$txn->getMerchant()
			|| (
				$txn->getMerchant()
				&& !in_array($txn->getMerchant()->getId(), MerchantService::merchantIdsForTestArray())
			)
			|| (!in_array($txn->getType()->getName(), [
                TransactionType::NAME_PURCHASE
            ]))
			|| ($txn->getStatus()->getName() !== TransactionStatus::NAME_COMPLETED)
			|| $txn->getRefundFrom()
			|| !$txn->getTernFee()
		) {
			return new FailedResponse('The transaction cannot be corrected.');
		}

		$ternAdmin = UserService::getSpendrBalanceAdminAccount('tern');
		$ternDummy = UserService::getSpendrBalanceDummyCard('tern');

		$ternAdminTest = UserService::getSpendrBalanceAdminAccount('tern', true);
		$ternDummyTest = UserService::getSpendrBalanceDummyCard('tern', true);

		// 1. move tern fee to test tern balance admin account
		$ternFee = $txn->getTernFee();
		$feeName = FeeService::CORRECT_TRANSACTION_TERN_FEE;

		if (FeeService::hasFeeHistory($ternAdmin, $feeName, $txn)) {
			return new FailedResponse('The transaction has already been corrected and cannot be repeated');
		}

		$comment = sprintf(
			'%s. Transaction: %s. Moved from official Tern wallet(%s) to Test Tern wallet(%s) at %s, amount: %s',
			$feeName,
			$txn->getToken(),
			$ternAdmin->getSignature(),
			$ternAdminTest->getSignature(),
			Carbon::now(),
			Money::format($ternFee, 'USD')
		);
		$ternDummy->updatePrivacyBalanceBy(-$ternFee, $feeName, $comment, false, $txn);
		$ternDummyTest->updatePrivacyBalanceBy($ternFee, $feeName, $comment, false, $txn);

		UserFeeHistory::create($ternAdmin, $ternFee, $feeName, $txn, $comment, Platform::spendr());

		Util::flush();

		SlackService::tada($comment);

		return new SuccessResponse($txn->toApiArray(true));
	}
}
