<?php

namespace SpendrBundle\Controller\Mobile\Merchant;

use CoreBundle\Utils\Util;
use PortalBundle\Exception\DeniedException;
use SpendrBundle\Services\LocationService;
use SpendrBundle\SpendrBundle;

class BaseController extends \SpendrBundle\Controller\Mobile\BaseController
{
	public $verifyMerchant = true;

	/**
	 * BaseController constructor.
	 * @throws DeniedException
	 * @throws \CoreBundle\Exception\FailedException
	 */
	public function __construct()
	{
        parent::__construct();
        if ($this->protected && !Util::isCommand()) {
			$this->user = $this->getUser();
			if ($this->user
				&& !$this->user->inTeams(SpendrBundle::getMerchantEmployeeRoles())
			) {
				throw new DeniedException('Only merchant managers and clerks are supported to login the mobile app.');
			}
			if ($this->verifyMerchant) {
				$this->verifyMerchant();
			}
		}
	}

	/**
	 * @param $roles
	 * @throws DeniedException
	 */
	public function authRoles($roles)
	{
		$user = $this->getUser();

		if (!in_array($user->getTeamName(), $roles)) {
			throw new DeniedException();
		}
	}

	/**
	 * @throws DeniedException
	 * @throws \CoreBundle\Exception\FailedException
	 */
	public function verifyMerchant()
	{
		$merchant = $this->currentMerchant();
		if (!$merchant || ($merchant && !$merchant->isActive())) {
			throw new DeniedException('The merchant is not activated or has been closed.');
		}
	}

	/**
	 * @return \SpendrBundle\Entity\SpendrMerchant
	 * @throws \CoreBundle\Exception\FailedException
	 */
	public function currentMerchant()
	{
		$location = LocationService::getCurrentLocation();
		return $location->getMerchant();
	}
}
