<?php
/**
 * Created by PhpStorm.
 * User: tracy
 * Date: 2021/10/13
 * Time: 10:11
 */

namespace SpendrBundle\Controller\Mobile\Merchant;


use Carbon\Carbon;
use ClfBundle\Controller\BaseControllerTrait;
use Clf<PERSON><PERSON>le\Entity\Account;
use Clf<PERSON><PERSON>le\Entity\Transaction;
use Clf<PERSON><PERSON>le\Entity\TransactionStatus;
use Clf<PERSON><PERSON>le\Entity\TransactionType;
use CoreBundle\Entity\Email;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserCardBalance;
use CoreBundle\Exception\FailedException;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Queue;
use CoreBundle\Utils\Util;
use Doctrine\ORM\QueryBuilder;
use SalexUserBundle\Entity\User;
//use SpendrBundle\Controller\Mobile\WebSocket\PushPaymentController;
use Spendr<PERSON><PERSON>le\Entity\Location;
use SpendrBundle\Entity\Terminal;
use SpendrBundle\Services\ChangeBalanceService;
use SpendrBundle\Services\FeeService;
use SpendrBundle\Services\LocationService;
use SpendrBundle\Services\MQTT\PublishService;
use SpendrBundle\Services\Pay\PayService;
use SpendrBundle\Services\SlackService;
use SpendrBundle\Services\TerminalService;
use SpendrBundle\Services\TransactionService;
use SpendrBundle\Services\UserService;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class TransactionsController extends BaseController
{
	use BaseControllerTrait;

	protected function getAccessibleRoles()
	{
		return [
			Role::ROLE_SPENDR_MERCHANT_MANAGER,
			Role::ROLE_SPENDR_MERCHANT_ASSISTANT_MANAGER,
		];
	}

	/**
	 * @Route("/spendr/m/merchant/transactions")
	 *
	 * @param Request $request
	 * @return FailedResponse|SuccessResponse
	 * @throws \Doctrine\ORM\NoResultException
	 * @throws \Doctrine\ORM\NonUniqueResultException
	 * @throws \PortalBundle\Exception\DeniedException
	 * @throws \CoreBundle\Exception\FailedException
	 */
	public function transactionList(Request $request)
	{
		$this->authRoles($this->getAccessibleRoles());

		$view = $request->get('view', 'report'); // report or list
		$period = $request->get('period');
		$keyword = $request->get('search');
		$clerkUserId = $request->get('clerkUserId');
		$terminalId = $request->get('terminalId');
		$page = $request->get('page', 1);
		$pageSize = $request->get('pageSize', 6);

		$location = LocationService::getCurrentLocation();

		$expr = Util::expr();
		$query = $this->em->getRepository(Transaction::class)
			->createQueryBuilder('t')
			->where($expr->eq('t.location', ':location'))
			->setParameter('location', $location)
			->andWhere($expr->eq('t.status', ':status'))
			->setParameter('status', TransactionStatus::get(
				TransactionStatus::NAME_COMPLETED
			));
		if ($request->get('filterTips', false)) {
		    $query->leftJoin('t.tip', 'st')->andWhere('t.tip is not null');
        }

		if ($clerkUserId) {
			$locationEmployee = User::find($clerkUserId);
			$query->andWhere($expr->eq('t.locationEmployee', ':locationEmployee'))
				->setParameter('locationEmployee', $locationEmployee);
		}

		if ($terminalId) {
			$terminal = Terminal::find($terminalId);
			$query->andWhere($expr->eq('t.terminal', ':terminal'))
				->setParameter('terminal', $terminal);
		} else if ($location) {
            $terminal = PayService::ensureDummyTerminalForApi($location, false);
            if ($terminal) {
                $query->andWhere($expr->neq('t.terminal', ':terminal'))
                    ->setParameter('terminal', $terminal);
            }
        }

		$queryDateRange = null;
		if ($period) {
			// todo: change dateTime to txnTime
			$queryData = $this->queryByDate($query, 't.dateTime', $request);
			$query = $queryData['query'];
			$queryDateRange = $queryData['queryDateRange'];
		}

        if ($keyword) {
            if (is_numeric($keyword)) {
                $keyword = Money::normalizeAmount($keyword, 'USD');
                $query->andWhere(
                    $expr->orX(
                        $expr->eq('t.id', ':txnID'),
                        $expr->eq('t.amount', ':amount')
                    )
                )
                    ->setParameter('txnID', $keyword)
                    ->setParameter('amount', $keyword);
            } else {
                $query->leftJoin('t.consumer', 'c')
                    ->andWhere(
                        $expr->orX(
                            $expr->like('c.firstName', ':firstName'),
                            $expr->like('c.lastName', ':lastName')
                        )
                    )
                    ->setParameter('firstName', '%' . $keyword . '%')
                    ->setParameter('lastName', '%' . $keyword . '%');
            }
        }

		$totalConsumer = (int)(
		(clone $query)->select('count(distinct t.consumer)')
			->getQuery()
			->getSingleScalarResult()
		);

		$result = [
			'numberOfPayments' => 0,
			'totalPayments' => 0,
			'averagePayment' => 0,
			'numberOfCustomers' => $totalConsumer,
			'queryDateRange' => $queryDateRange,
			'data' => [],
		];

		$purchaseQuery = (clone $query)->andWhere('t.type = :type')
			->setParameter('type', TransactionType::get(
				TransactionType::NAME_PURCHASE
			));
		$totalPurchasePayments = (float)(
			(clone $purchaseQuery)->select('sum(t.amount)')
				->getQuery()
				->getSingleScalarResult()
		);
		$result['numberOfPayments'] = (int)(
			(clone $purchaseQuery)->select('count(distinct t)')
			->getQuery()
			->getSingleScalarResult()
		);
        if ($request->get('filterTips', false)) {
            $tipQuery = clone $purchaseQuery;
        } else {
            $tipQuery = (clone $purchaseQuery)->leftJoin('t.tip', 'st')
                ->andWhere('t.tip is not null');
        }
        $totalTips = (float)$tipQuery->select('sum(st.amount)')
            ->getQuery()
            ->getSingleScalarResult();
		$result['totalTips'] = Money::format($totalTips, 'USD', false);

		$refundQuery = (clone $query)->andWhere('t.type = :type')
			->setParameter('type', TransactionType::get(
				TransactionType::NAME_REFUND
			));
		$totalRefundPayments = (float)(
			(clone $refundQuery)->select('sum(t.amount)')
				->getQuery()
				->getSingleScalarResult()
		);

		$result['totalPayments'] = $totalPurchasePayments - $totalRefundPayments;

		if ($result['totalPayments'] && $result['numberOfPayments']) {
			$result['averagePayment'] = round(Util::percent($result['totalPayments'], $result['numberOfPayments']), 2);
		}

		$result['totalPayments'] = Money::format($result['totalPayments'], 'USD', false);
		$result['averagePayment'] = Money::format($result['averagePayment'], 'USD', false);

		$all = $query->orderBy('t.dateTime', 'desc')
			->setFirstResult(($page - 1) * $pageSize)
			->setMaxResults($pageSize)
			->getQuery()
			->getResult();
		/** @var Transaction $item */
		foreach ($all as $item) {
			$date = Util::formatDateTime(
				$item->getDateTime(),
				Util::DATE_TIME_FORMAT,
				$this->tz
			);
			$newItem = $item->toApiArray(true);
			$newItem['date'] = $date;
			$result['data'][] = $newItem;
		}

		return new SuccessResponse($result);
	}

	protected function queryByDate (QueryBuilder $query, $timeField, Request $request) {
		$period  = $request->get('period', 'all');
		if (
			($period != 'custom_range')
			&& ($period != 'today')
			&& ($period != 'week')
			&& ($period != 'month')
			&& ($period != 'half_year')
			&& ($period != 'year')
		) {
			return $query;
		}

		$tz = Util::timezone();
		switch ($period) {
			case 'today':
                $startDate = Carbon::now($tz);
                $endDate = Carbon::now($tz);
                $start = Util::timeUTC($startDate->toDateString());
                $end = Util::timeUTC($endDate->toDateString())->addDay();
				break;
			case 'week':
			    $startDate = Carbon::now($tz)->startOfWeek()->addDay();
			    $endDate = Carbon::now($tz)->endOfWeek()->addDay();
				$start = Util::timeUTC($startDate->toDateString());
				$end = Util::timeUTC($endDate->toDateString())->addDay();
				break;
			case 'month':
			    $startDate = Carbon::now($tz)->startOfMonth();
			    $endDate = Carbon::now($tz)->endOfMonth();
                $start = Util::timeUTC($startDate->toDateString());
                $end = Util::timeUTC($endDate->toDateString())->addDay();
				break;
			case 'half_year':
			    $startDate = Carbon::now($tz)->startOfQuarter()->subQuarterWithoutOverflow();
			    $endDate = Carbon::now($tz)->endOfQuarter();
                $start = Util::timeUTC($startDate->toDateString());
                $end = Util::timeUTC($endDate->toDateString())->addDay();
				break;
			case 'year':
			    $startDate = Carbon::now($tz)->startOfYear();
			    $endDate = Carbon::now($tz)->endOfYear();
                $start = Util::timeUTC($startDate->toDateString());
                $end = Util::timeUTC($endDate->toDateString())->addDay();
				break;
			default:
			    $startDate = Carbon::parse($request->get('startDate'), $tz);
			    $endDate = Carbon::parse($request->get('endDate'), $tz);
                $start = Util::timeUTC($request->get('startDate'));
                $end = Util::timeUTC($request->get('endDate'))->addDay();
				break;
		}

		if ($start) {
			$query->andWhere($timeField . ' >= :__startTime')
				->setParameter('__startTime', $start);
		}
		if ($end) {
			$query->andWhere($timeField . ' < :__endTime')
				->setParameter('__endTime', $end);
		}
		$startString = $startDate->format('F d, Y');
		$endString = $endDate->format('F d, Y');
		return [
			'query' => $query,
			'queryDateRange' => [
				'startString' => $startString,
				'endString' => $endString
			]
		];
	}

	/**
	 * @Route("/spendr/m/merchant/transactions/create", methods={"POST"})
	 * @param Request $request
	 *
	 * @return SuccessResponse|FailedResponse
	 * @throws \Doctrine\ORM\OptimisticLockException
	 * @throws \CoreBundle\Exception\FailedException
	 */
	public function createAction(Request $request)
	{
		$amount = $request->get('amount') ?? null;
		$paymentType = $request->get('paymentType', TransactionService::fromQrcode);

		if (!$amount || $paymentType !== TransactionService::fromQrcode) {
			return new FailedResponse('Invalid parameters.');
		}

		$type = $request->get('type', TransactionType::NAME_PURCHASE);
		$description = $request->get('description', null);

		$employee = $this->user;		
		
		$location = LocationService::getCurrentLocation();
		$merchant = $location->getMerchant();
		if (!$merchant || ($merchant && !$merchant->isActive())) {
			return new FailedResponse('Invalid merchant.');
		}

		$terminal = TerminalService::getCurrentTerminal($location);

		$amount = Money::normalizeAmount($amount, 'USD');

		$transaction = new Transaction();
		$transaction->setType(TransactionType::get($type))
			->setAmount($amount)
			->setMerchant($merchant)
			->setLocationEmployee($employee)
			->setLocation($location)
			->setTerminal($terminal)
			->setOrderDescription($description);
		Util::persist($transaction);
		$this->em->flush();

		$result = $transaction->toApiArray(true);

		$result['subscribeScanResultTopicUrl'] = PublishService::generateTopicUrl(
			PublishService::TOPIC_SEND_SCAN_RESULT_TO_MERCHANT,
			$transaction->getToken()
		);
		$result['subscribeRescanResultTopicUrl'] = PublishService::generateTopicUrl(
			PublishService::TOPIC_SEND_RESCAN_RESULT_TO_MERCHANT,
			$transaction->getToken()
		);
		$result['subscribeCancelResultTopicUrl'] = PublishService::generateTopicUrl(
			PublishService::TOPIC_SEND_CANCEL_RESULT_TO_MERCHANT,
			$transaction->getToken()
		);
		$result['subscribePaymentResultTopicUrl'] = PublishService::generateTopicUrl(
			PublishService::TOPIC_SEND_PAYMENT_RESULT_TO_MERCHANT,
			$transaction->getToken()
		);
		return new SuccessResponse($result);
	}

    /**
     * @Route("/spendr/m/merchant/transaction/update", methods={"POST"})
     *
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function updateAction(Request $request)
    {
        $amount = (float)$request->get('amount', 0);
        if ($amount <= 0) {
            return new FailedResponse('Invalid amount!');
        }
        $amount = Money::normalizeAmount($amount, 'USD');
        $token = $request->get('token') ?? null;
        $txn = TransactionService::beforeUpdate($token);
        if (is_string($txn)) {
            return new FailedResponse($txn);
        }

        $consumer = $txn->getConsumer();
        if (!$consumer || !Data::has('spendr_confirm_payment_' . $token)) {
            $pAmount = $txn->getAmount();
            $txn->setAmount($amount);
            Util::persist($txn);
            $this->em->flush();
            Util::updateMeta($txn, [
                'previousAmount' => $pAmount
            ]);
        } else {
            return new FailedResponse('Consumer has confirmed the payment, you can not change the amount now!');
        }

        $result = $txn->toApiArray(true);
        return new SuccessResponse($result);
    }

	/**
	 * @Route("/spendr/m/merchant/transactions/push", methods={"POST"})
	 * @param Request $request
	 *
	 * @return SuccessResponse|FailedResponse
	 * @throws \Doctrine\ORM\OptimisticLockException
	 * @throws \PortalBundle\Exception\PortalException
	 */
	public function pushPayment(Request $request)
	{
		$paymentType = $request->get('paymentType', TransactionService::fromPush);
		if ($paymentType !== TransactionService::fromPush) {
			return new FailedResponse('Invalid parameters');
		}

		$token = $request->get('token');
		if (!$token) {
			return new FailedResponse('Invalid token.', ['cancelTimer' => true]);
		}

		$phoneNumber = $request->get('phoneNumber');
		if (!$phoneNumber) {
			return new FailedResponse('Invalid phone number.');
		}

		$rs = TransactionService::getNotExpiredTransactionsByToken($token);
		if (!$rs) {
			return new FailedResponse('Unknown transaction or outdated!', ['cancelTimer' => true]);
		}
		$transaction = $rs[0];
		if ($transaction->getStatus()->getName() !== TransactionStatus::NAME_PENDING) {
			return new FailedResponse('Transaction has been completed or cancelled!', ['cancelTimer' => true]);
		}

		$phoneNumber = Util::formatManualInputPhoneNumber($phoneNumber);
		$phoneNumber = Util::formatFullPhone($phoneNumber);

		$consumers = UserService::getUserByMobilePhone($phoneNumber);
		if (!$consumers) {
			return new FailedResponse('Invalid consumer.');
		} else {
			$consumer = $consumers[0];
		}

		$transaction->setConsumer($consumer)
			->setAccount($this->getAccount());
		Util::persist($transaction);
		$this->em->flush();

		Util::updateMeta($transaction, [
			'userGetTxnFrom' => TransactionService::fromPush
		]);

		$result = $transaction->toApiArray(true);

		try {
			PublishService::pushByUserToken(
				$this->user->getId(),
				PublishService::TOPIC_SEND_PAYMENT_TO_CONSUMER,
				$consumer,
				Util::createStandResp($result)
			);
		} catch (FailedException $e) {
			Log::error($e->getFullMessage(), [$e->data]);
		}

		return new SuccessResponse($result);
	}

	protected function pushByWebsocket(User $consumer, $result)
	{
//		// push by websocket
//		/** @var PushPaymentController $ctrl */
//		$ctrl = $this->get('app.spendr.push_payment_ctrl');
//		$ctrl->pushPaymentToConsumer($consumer, $result);
	}

	/**
	 * @Route("/spendr/m/merchant/transactions/cancel/{token}", methods={"GET"})
	 * @param Request $request
	 * @param         $token
	 *
	 * @return FailedResponse|SuccessResponse
	 * @throws \Doctrine\ORM\OptimisticLockException
	 */
	public function cancelAction(Request $request, $token)
	{
		$t = TransactionService::beforeUpdate($token);
		if (is_string($t)) {
			return new FailedResponse($t, ['cancelTimer' => true]);
		}

		Queue::spendrUpdateTransaction($token, 'cancel');

		$result = $t->toApiArray(true);
		return new SuccessResponse($result);
	}

//	/**
//	 * @Route("/spendr/m/merchant/transactions/update-status/{token}", methods={"GET"})
//	 * @param Request $request
//	 * @param         $token
//	 *
//	 * @return FailedResponse|SuccessResponse
//	 */
//	public function updateStatus(Request $request, $token) {
//		$t = TransactionService::beforeUpdate($token);
//		if (is_string($t)) {
//			return new FailedResponse($t);
//		}
//		return new SuccessResponse($t->toApiArray(true));
//	}

	/**
	 * @Route("/spendr/m/merchant/transactions/rescan/{token}", methods={"GET"})
	 * @param Request $request
	 * @param         $token
	 *
	 * @return FailedResponse|SuccessResponse
	 * @throws \Doctrine\ORM\OptimisticLockException
	 */
	public function rescanAction(Request $request, $token)
	{
		$t = TransactionService::beforeUpdate($token);
		if (is_string($t)) {
			return new FailedResponse($t, ['cancelTimer' => true]);
		}

		Queue::spendrUpdateTransaction($token, 'rescan');

		$result = $t->toApiArray(true);
		return new SuccessResponse($result);
	}

	/**
	 * @Route("/spendr/m/merchant/transactions/start/{token}", methods={"GET"})
	 * @param Request $request
	 * @param         $token
	 *
	 * @return FailedResponse|SuccessResponse
	 */
	public function startAction (Request $request, $token) {
		$rs = TransactionService::getNotExpiredTransactionsByToken($token);
		if (!$rs) {
			return new FailedResponse('Unknown transaction or outdated!');
		}
		/** @var Transaction $t */
		$t = $rs[0];
		if ($t->getStatus()->getName() !== TransactionStatus::NAME_PENDING) {
			return new FailedResponse('Transaction has been completed or cancelled!');
		}
		return new SuccessResponse($t->toApiArray(true));
	}

	/**
	 * @Route("/spendr/m/merchant/transactions/status/{token}", methods={"GET"})
	 * @param Request $request
	 * @param         $token
	 *
	 * @return FailedResponse|SuccessResponse
	 */
	public function statusAction (Request $request, $token) {
		$res = TransactionService::checkTxnStatus($request, $token);
		if (is_string($res)) {
			return new FailedResponse($res, ['cancelTimer' => true]);
		}
		return new SuccessResponse($res);
	}

	/**
	 * @Route("/spendr/m/merchant/transactions/scan-status/{token}", methods={"GET"})
	 *
	 * @param Request $request
	 * @param $token
	 * @return FailedResponse|SuccessResponse
	 */
	public function scanStatus (Request $request, $token) {
		/** @var Transaction $t */
		$t = $this->em->getRepository(Transaction::class)
			->findOneBy([
				'token' => $token
			]);
		if (!$t) {
			return new FailedResponse('Unknown transaction!', ['cancelTimer' => true]);
		}

		$status = $t->getStatus()->getName();

		if ($status === TransactionStatus::NAME_COMPLETED) {
			return new FailedResponse('The transaction has been completed!', ['cancelTimer' => true]);
		}

		if ($status === TransactionStatus::NAME_CANCELLED) {
			return new FailedResponse('The transaction has been cancelled!', ['cancelTimer' => true]);
		}

		if ($status === TransactionStatus::NAME_PENDING) {
			$dateTime = $t->getDateTime();
			if (
				!(
					($dateTime <= Carbon::now())
					&& ($dateTime > Carbon::now()->subMinutes(TransactionService::transactionExpiredTime))
				)
			) {
				return new FailedResponse('The transaction has expired!', ['cancelTimer' => true]);
			}
		}

		$scanStatus = false;
		$from = Util::meta($t, 'userGetTxnFrom');
		if (
			$t->getConsumer()
			&& ($from === TransactionService::fromQrcode)
		) {
			$scanStatus = true;
		}

		$data = $t->toApiArray(true);
		$data['_from_'] = $from;
		$data['scanStatus'] = $scanStatus;
		return new SuccessResponse($data);
	}

	/**
	 * @Route("/spendr/m/merchant/transaction/pre-refund-detail/{token}", methods={"GET"})
	 *
	 * @param Request $request
	 * @param $token
	 * @return FailedResponse|SuccessResponse
	 * @throws \PortalBundle\Exception\DeniedException
	 */
	public function preRefundDetailAction (Request $request, $token) {
		$this->authRoles($this->getAccessibleRoles());

		if (!$token) {
			return new FailedResponse('Invalid parameters.');
		}
		$transaction = TransactionService::getCompleteTransactionByToken($token);

		if (!$transaction) {
			return new FailedResponse("The transaction cannot be found or cannot be refunded");
		}

		$result = $transaction->toApiArray(true);
		$result['subscribeRefundResultTopicUrl'] = PublishService::generateTopicUrl(
			PublishService::TOPIC_SEND_REFUND_RESULT_TO_MERCHANT,
			$transaction->getToken()
		);
		return new SuccessResponse($result);
	}

	/**
	 * @Route("/spendr/m/merchant/transaction/refund", methods={"POST"})
	 *
	 * @param Request $request
	 *
	 * @return FailedResponse|SuccessResponse
	 * @throws \Doctrine\ORM\OptimisticLockException
	 * @throws \PortalBundle\Exception\DeniedException
	 * @throws \CoreBundle\Exception\FailedException
	 * @throws \PortalBundle\Exception\PortalException
	 * @throws \Throwable
	 */
	public function refundAction(Request $request)
	{
		$this->authRoles($this->getAccessibleRoles());

		$token = $request->get('token') ?? null;
		$refundAmount = (float)$request->get('amount', 0);
		$refundAmount = Money::normalizeAmount($refundAmount, 'USD');
		$employee = $this->user;
		$location = LocationService::getCurrentLocation();
		$terminal = TerminalService::getCurrentTerminal($location);
		$txn = TransactionService::beforeRefund($token, $refundAmount);
		if (is_string($txn)) {
			return new FailedResponse($txn);
		}

		Queue::spendrRefundTransaction(
			$token,
			$refundAmount,
			$employee->getId(),
			$location->getId(),
			$terminal->getId(),
			$this->tz
		);

		$result = $txn->toApiArray(true);
		return new SuccessResponse($result);
	}

	/**
	 * @Route("/spendr/m/merchant/transaction/types")
	 *
	 * @return SuccessResponse
	 * @throws \PortalBundle\Exception\DeniedException
	 */
	public function transactionTypes ()
	{
		$this->authRoles($this->getAccessibleRoles());

		return new SuccessResponse(TransactionType::allTypes());
	}

    /**
     * @Route("/spendr/m/merchant/transaction/send-receipt/{txn}")
     *
     * @return FailedResponse|SuccessResponse
     */
    public function sendReceipt (Request $request, Transaction $txn)
    {
        $userId = $request->get('userId');
        $target = $request->get('target');
        $user = null;
        if ($userId) {
            $user = User::find($userId);
        } else {
            if ($target === 'consumer') {
                $user = $txn->getConsumer();
            } else if ($target === 'clerk') {
                $user = $txn->getLocationEmployee();
            }
        }
        if (!$user) {
            return new FailedResponse('Unknown ' . $target . ' user to send the receipt.');
        }
        $email = $user->getEmail();
        if (!$email) {
            return new FailedResponse('The ' . $target . ' user does not has email address info.');
        }

        $title = $txn->isRefund() ? 'Refund Receipt' : 'Payment Receipt';
        if ($target !== 'consumer') {
            $title .= ' for ' . $txn->getConsumer()->getName();
        }
        $location = $txn->getLocation() ? $txn->getLocation()->getName() : $txn->getMerchant()->getName();
        $title .= ' (' . Money::formatUSD($txn->getAmount()) . ' @ ' . $location . ')';

        Email::sendWithTemplateToUser($user, Email::TEMPLATE_SIMPLE_LAYOUT, [
            'subject' => $title,
            'body' => TransactionService::renderReceipt($txn, $target, $this->tz),
        ]);

        return new SuccessResponse();
    }
}
