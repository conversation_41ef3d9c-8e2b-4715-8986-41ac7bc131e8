<?php

namespace Spendr<PERSON><PERSON>le\Controller;

use Carbon\Carbon;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\UserToken;
use CoreBundle\Exception\FailedException;
use CoreBundle\Exception\RedirectException;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Util;
use MobileBundle\MobileBundle;
use PortalBundle\Exception\PortalException;
use SalexUserBundle\Entity\User;
use SpendrBundle\SpendrBundle;

class BaseController extends \PortalBundle\Controller\Common\BaseController
{
	/** @var User */
	public $user;

	public $protected = true;

	public $tz;

	/**
	 * BaseController constructor
	 */
	public function __construct()
	{
		parent::__construct();

		$this->tz = Util::timezone();

		if (!Util::$platform) {
			Util::$platform = Platform::get(Platform::NAME_SPENDR);
		}
		$this->platform = Util::$platform;

		if (!Util::isCommand() && !$this->platform->isSpendr()) {
			throw PortalException::tempPage('Access denied to ' . Platform::NAME_SPENDR);
		}

		if (!Util::$cardProgram) {
			Util::$cardProgram = CardProgram::spendr();
		}
		$this->cardProgram = Util::$cardProgram;

		if ($this->protected && !Util::isCommand()) {
			$this->user = $this->getUser();
			if($this->user) {
                if (!$this->user->isActive()) {
                    if ($this->isApp() || MobileBundle::isWebApp()) {
                        $this->setAppTokenExpire();
                        throw new FailedException('Your account is not active anymore. Please contact support or admin.', null, 401);
                    } else {
                        throw new RedirectException('/login');
                    }
                }
            } else {
				if ($this->isApp() || MobileBundle::isWebApp() || $this->request->isXmlHttpRequest()) {
					throw new FailedException('Your login has expired, please log out and log in again.', null, 401);
				} elseif (!in_array($this->request->getPathInfo(), ['/spendr', '/static/spendr/web/'])) {
					throw new RedirectException('/login');
				}
			}
		}
	}

	public function isApp() {
        $env = Util::request()->headers->get('x-environment');
        if ($env) {
            $env = Util::s2j($env) ?? [];
            if (isset($env['web']) && !$env['web']) {
                return true;
            }
        }
        return false;
    }

	protected function setAppTokenExpire()
    {
        if (!$this->user) {
            return;
        }

        Util::em()->createQueryBuilder()
            ->update(UserToken::class, 'ut')
            ->set('ut.expireAt', ':expireAt')
            ->where('ut.user = :user')
            ->setParameter('user', $this->user)
            ->setParameter('expireAt', Carbon::now()->subSecond())
            ->getQuery()
            ->execute();
    }
}
