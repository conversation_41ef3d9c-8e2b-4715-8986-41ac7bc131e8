<?php

namespace Spendr<PERSON><PERSON>le\Controller\API;

use Core<PERSON>undle\Entity\Role;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserToken;
use CoreBundle\Exception\FailedException;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Util;
use FaasB<PERSON>le\Controller\API\BaseControllerTrait;
use PortalBundle\Exception\DeniedException;
use PortalBundle\Exception\PortalException;
use SpendrBundle\Services\BridgeService;
use SpendrBundle\SpendrBundle;
use SpendrBundle\Services\Next\RemixService;

class BaseController extends \ApiBundle\Controller\BaseController
{
    use BaseControllerTrait;

    protected $protected = true;
    protected $uploadKeyPrefix = 'spendr_1_5_server_upload_lock_';

    public function __construct()
    {
        parent::__construct();

        if (!$this->platform || !$this->platform->isSpendr()) {
            throw new FailedException('Invalid platform!');
        }

        if ($this->protected) {
            $this->validateConstruct();
        } else {
            // only for upload features
            $this->validateUploadApi();
        }
    }

    public function validateUserCard($field = 'userCardId')
    {
        $value = $this->validateRequired($field);
        $uc = UserCard::find($value);
        if (!$uc) {
            throw PortalException::temp('Invalid user card!');
        }
        $user = $uc->getUser();
        if (!$user->inTeam(Role::ROLE_SPENDR_CONSUMER)) {
            throw PortalException::temp('Unknown user card!');
        }
        $this->uc = $uc;
        $this->user = $user;
        return $uc;
    }

    public function validateUserCardByHash(bool $required = true, bool $autoCreate = true, string $field = 'bankCardHash')
    {
        if ($required) {
            $bankCardHash = $this->validateRequired($field);
            if (!$bankCardHash) {
                throw PortalException::temp('Invalid card hash!');
            }
        } else {
            $bankCardHash = $this->request->get($field);
            if (!$bankCardHash) {
                return null;
            }
        }

        $uc = BridgeService::ensureUserCard($bankCardHash, $autoCreate);
        if (!$uc) {
            throw PortalException::temp('Bank card not found.');
        }
        $user = $uc->getUser();
        if (!$user->inTeams(SpendrBundle::getRoles())) {
            throw PortalException::temp('Unknown user card!');
        }
        $this->uc = $uc;
        $this->user = $user;
        return $uc;
    }

    public function isInternalDebugMode()
    {
        return $this->request->get('__debug__') === 'spendr_debug_mode_v0f8c35m8fwztm';
    }

    private function validateUploadApi()
    {
        $request = Util::request();
        $host = $request->getHost();
        $referer = $request->headers->get('referer');
        if ($host !== SpendrBundle::getAdminDomain()) {
            throw new DeniedException('Access denied!');
        }
        // todo: judge referer
        $refererShould = '';
        if ($referer !== $refererShould) {
            throw new DeniedException('Access denied!');
        }
        $key = $this->uploadKeyPrefix . $referer;
        if (Data::lockedHas($key)) {
            throw new DeniedException('The operation is too frequent. Please try again later.');
        }
    }

    public function validateRemixCall()
    {
        $ut = Util::$apiToken;
        if ( ! $ut && (Util::isLocal() || Util::isDev())) {
            $invoker = Util::$apiInvoker;
            if ($invoker) {
                $_ut = UserToken::find(RemixService::TOKEN, UserToken::SCOPE_API);
                if ($_ut && Util::eq($invoker, $_ut->getUser())) {
                    $ut = $_ut;
                }
            }
        }
        if ( ! $ut) {
            throw PortalException::temp('Unknown user token in API call!');
        }
        if ($ut->getToken() !== RemixService::TOKEN) {
            throw PortalException::temp('Only internal call allowed!');
        }
    }
}
