<?php

namespace SpendrBundle\Controller\API\Pay;

use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Util;
use OpenApi\Attributes as OA;
use SpendrBundle\Entity\SpendrMerchant;
use SpendrBundle\Services\LocationService;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class MerchantController extends BaseController
{
    #[Route("/api/spendr/pay/merchant/list", name: "api_spendr_pay_merchant_list", methods: ["GET"])]
    #[OA\Get(
        description: "Get merchant list",
        summary: "Merchant list",
        security: [["x-signature" => [], "x-salt" => [], "x-timestamp" => [], "x-access-key" => []]],
        tags: ["Pay"],
    )]
    #[OA\Response(ref: "#/components/schemas/Response", response: 200)]
    #[OA\Response(ref: "#/components/responses/ErrorParamResponse", response: 400)]
    #[OA\Response(ref: "#/components/responses/RequireAuthResponse", response: 401)]
    #[OA\Response(ref: "#/components/responses/AuthFailedResponse", response: 403)]
    public function listAction(Request $request)
    {
        $this->validateParameters(__METHOD__);
        $group = $this->validateGroupAccess($request);
        $all = $this->em->getRepository(SpendrMerchant::class)
            ->createQueryBuilder('sm')
            ->where('sm.group = :group')
            ->setParameter('group', $group)
            ->orderBy('sm.name', 'ASC')
            ->getQuery()
            ->getResult();
        return new SuccessResponse(array_map(function (SpendrMerchant $merchant) {
            return [
                'id' => $merchant->getId(),
                'name' => $merchant->getName(),
                'status' => $merchant->getStatus()->getName(),
            ];
        }, $all));
    }

    #[Route("/api/spendr/pay/merchant/locations", name: "api_spendr_pay_merchant_locations", methods: ["GET"])]
    #[OA\Get(
        description: "Get merchant locations list",
        summary: "Merchant locations",
        security: [["x-signature" => [], "x-salt" => [], "x-timestamp" => [], "x-access-key" => []]],
        tags: ["Pay"],
    )]
    #[OA\QueryParameter(name: 'merchant_id', required: true)]
    #[OA\Response(ref: "#/components/schemas/Response", response: 200)]
    #[OA\Response(ref: "#/components/responses/ErrorParamResponse", response: 400)]
    #[OA\Response(ref: "#/components/responses/RequireAuthResponse", response: 401)]
    #[OA\Response(ref: "#/components/responses/AuthFailedResponse", response: 403)]
    public function locationsAction(Request $request)
    {
        $this->validateParameters(__METHOD__);
        $merchant = $this->validateMerchantAccess($request);
        $rs = LocationService::getLocationQueryOfMerchant($merchant)
            ->getQuery()
            ->getResult();
        return new SuccessResponse(Util::toApiArray($rs));
    }
}
