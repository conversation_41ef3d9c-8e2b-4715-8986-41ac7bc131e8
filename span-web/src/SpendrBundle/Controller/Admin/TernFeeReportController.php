<?php

namespace SpendrBundle\Controller\Admin;

use Carbon\Carbon;
use Clf<PERSON>undle\Entity\Transaction;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Util;
use SpendrBundle\Services\FeeService;
use SpendrBundle\SpendrBundle;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class TernFeeReportController extends BaseController
{
	public function __construct()
	{
		parent::__construct();
		$this->authAdminsWithoutBank();
	}

	/**
	 * @Route("/admin/spendr/fee-report/tern/{page}/{limit}", methods={"GET"}, defaults={"page" = 1,"limit" = 10})
	 * @param Request $request
	 * @param int $page
	 * @param int $limit
	 * @return FailedResponse|SuccessResponse
	 */
	public function index(Request $request, $page = 1, $limit = 10)
	{
		[$start, $end] = $this->getDateRange($request);

		$items = [];
		$txns = $this->getTransaction($start, $end);
		$loads = $this->getLoadTransaction($start, $end);
		$onFileFees = $this->getMonthlyOnFileFee($start, $end);
		$withdrawFees = $this->getWithdrawFee($start, $end);
		$items = array_merge(
			$items,
			$txns['data'],
			$loads['data'],
			$onFileFees['data'],
			$withdrawFees['data'],
		);
		$totalAmount = $txns['totalAmount'] + $loads['totalAmount'] + $onFileFees['totalAmount'] + $withdrawFees['totalAmount'];

		Util::usort($items, [
			'timestamp' => 'desc',
		]);

		$total = count($items);
		$items = array_slice($items, $limit * ($page - 1), $limit);

		return new SuccessResponse([
			'count' => $total,
			'data' => $items,
			'quick' => [
				'total' => $total,
				'totalAmount' => $totalAmount,
				'totalTxnFee' => $txns['totalAmount'],
				'totalLoadFee' => $loads['totalAmount'],
				'totalMonthlyOnFileFee' => $onFileFees['totalAmount'],
				'totalWithdrawFee' => $withdrawFees['totalAmount'],
			]
		]);
	}

	protected function getDateRange(Request $request) {
		$period  = $request->get('period', 'all');
		$start = $request->get('start') ? Carbon::parse($request->get('start')) : null;
		$end = $request->get('end') ? Carbon::parse($request->get('end'))->endOfDay() : null;

		if (
			($period != 'custom_range')
			&& ($period != 'today')
			&& ($period != 'week')
			&& ($period != 'month')
			&& ($period != 'quarter')
			&& ($period != 'year')
		) {
			return null;
		}

		return [$start, $end];
	}

	protected function getTransaction(Carbon $start = null, Carbon $end = null)
	{
		$data = [
			'data' => [],
			'totalAmount' => 0
		];

		$fee = FeeService::calculate(FeeService::TO_TERN_TRANSACTION_FEE);
		if (!$fee) {
			return $data;
		}

		$txns = FeeService::calculateTernTxnFee($start, $end, null, 'list');
		if (!$txns) {
			return $data;
		}

		/** @var Transaction $txn */
		foreach ($txns as $txn) {
			$feeTime = $txn->getTxnTime() ? $txn->getTxnTime() : $txn->getDateTime();
			$data['data'][] = [
				'Transaction' => 'Transaction Fee',
				'Date & Time' => Util::formatDateTime($feeTime, Util::DATE_TIME_FORMAT, $this->tz),
				'Transaction ID' => $txn->getId(),
				'Amount Value' => $fee,
				'Amount' => Money::format($fee, 'USD', false),
				'timestamp' => $feeTime->getTimestamp(),
			];
			$data['totalAmount'] += $fee;
		}

		return $data;
	}

	protected function getLoadTransaction(Carbon $start = null, Carbon $end = null)
	{
		$data = [
			'data' => [],
			'totalAmount' => 0
		];

		$fee = FeeService::calculate(FeeService::TO_TERN_CONSUMER_LOAD_FEE);
		if (!$fee) {
			return $data;
		}

		$loads = FeeService::calculateTernLoadFee($start, $end, 'list');
		if (!$loads) {
			return $data;
		}

		/** @var UserCardLoad $load */
		foreach ($loads as $load) {
			$feeTime = $load->getInitializedAt();
			$data['data'][] = [
				'Transaction' => 'Consumer Load Fee',
				'Date & Time' => Util::formatDateTime($feeTime, Util::DATE_TIME_FORMAT, $this->tz),
				'Transaction ID' => $load->getId(),
				'Amount Value' => $fee,
				'Amount' => Money::format($fee, 'USD', false),
				'timestamp' => $feeTime->getTimestamp(),
			];
			$data['totalAmount'] += $fee;
		}

		return $data;
	}

	protected function getWithdrawFee(Carbon $start = null, Carbon $end = null)
	{
		$data = [
			'data' => [],
			'totalAmount' => 0
		];

		$fee = FeeService::calculate(FeeService::TO_TERN_MERCHANT_WITHDRAWAL_FEE);
		if (!$fee) {
			return $data;
		}

		$loads = FeeService::calculateTernWithdrawFee($start, $end, 'list');
		if (!$loads) {
			return $data;
		}

		/** @var UserCardLoad $load */
		foreach ($loads as $load) {
			$feeTime = $load->getInitializedAt();
			$user = $load->getUserCard()->getUser();
			$text = null;
			if ($user->inTeams(SpendrBundle::getMerchantBalanceAdminRoles())) {
				$text = 'Merchant ';
			} else if ($user->inTeams([Role::ROLE_SPENDR_PROGRAM_OWNER])) {
				$text = 'Partner ';
			}
			$data['data'][] = [
				'Transaction' => $text . 'Withdraw Fee',
				'Date & Time' => Util::formatDateTime($feeTime, Util::DATE_TIME_FORMAT, $this->tz),
				'Transaction ID' => $load->getId(),
				'Amount Value' => $fee,
				'Amount' => Money::format($fee, 'USD', false),
				'timestamp' => $feeTime->getTimestamp(),
			];
			$data['totalAmount'] += $fee;
		}
		return $data;
	}

	protected function getMonthlyOnFileFee(Carbon $start = null, Carbon $end = null)
	{
		$data = [
			'data' => [],
			'totalAmount' => 0
		];

		$fee = FeeService::calculate(FeeService::TO_TERN_MERCHANT_MONTHLY_ON_FILE_FEE);
		if (!$fee) {
			return $data;
		}

		$feeRecords = FeeService::calculateTernMonthlyOnFileFee($start, $end, 'list');

		if (!$feeRecords) {
			return $data;
		}

		$data['data'] = $feeRecords;
		$data['totalAmount'] = count($data['data']) * $fee;

		return $data;
	}
}
