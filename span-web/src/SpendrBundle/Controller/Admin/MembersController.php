<?php


namespace SpendrBundle\Controller\Admin;


use ApiBundle\Entity\Coin;
use Carbon\Carbon;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\Country;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Entity\UserCardTransaction;
use CoreBundle\Response\DeniedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Utils\Bundle;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\QueryListParams;
use CoreBundle\Utils\Queue;
use CoreBundle\Utils\Util;
use Doctrine\ORM\QueryBuilder;
use LeafLinkBundle\Controller\AchServiceController;
use PortalBundle\Exception\PortalException;
use PortalBundle\Util\RegisterStep;
use SalexUserBundle\Entity\User;
use SpendrBundle\Controller\Mobile\RegisterControllerTrait;
use SpendrBundle\Services\ACH\SpendrACHService;
use SpendrBundle\Services\BrazeService;
use SpendrBundle\Services\ConsumerService;
use SpendrBundle\Services\FeeService;
use SpendrBundle\Services\LoadService;
use SpendrBundle\Services\SegmentService;
use SpendrBundle\Services\UserCardService;
use SpendrBundle\SpendrBundle;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use UsUnlockedBundle\Services\IDologyService;
use SpendrBundle\Services\UserService;
use UsUnlockedBundle\Services\Privacy\PrivacyAPI;

class MembersController extends BaseController
{
    use RegisterControllerTrait;
    use MembersControllerTrait;

	public function __construct()
	{
		parent::__construct();

		$this->authAdminsWithoutBank();
	}

	protected function getInvolvedRoles()
	{
		return Bundle::getMemberRoles();
	}

	/**
	 * @Route("/admin/spendr/members/list/{page}/{limit}", methods={"GET"}, defaults={"page" = 1,"limit" = 10})
	 * @param Request $request
	 * @param int $page
	 * @param int $limit
	 *
	 * @return SuccessResponse
	 * @throws \Doctrine\ORM\NoResultException
	 * @throws \Doctrine\ORM\NonUniqueResultException
	 */
    public function search(Request $request, $page = 1, $limit = 10)
    {
        $resp = $this->traitSearch($request, [], $page, $limit);
        $result = $resp->getResult();

        $query = $this->query($request);
        $total = (clone $query)->select('count(distinct u)')
            ->getQuery()
            ->getSingleScalarResult();

        $expr = Util::expr();
        $subQuery = clone $query;
        $balanceQuery = $this->em->getRepository(UserCard::class)
            ->createQueryBuilder('uuc')
            ->where($expr->eq('uuc.user', $expr->any($subQuery->getDQL())))
            ->select('sum(uuc.balance)');

        $balance = Util::moveQueryParameters($balanceQuery, $subQuery)
            ->getQuery()
            ->getSingleScalarResult();

		$averageMemberAge = (clone $query)
			->select('AVG(TIMESTAMPDIFF(YEAR,u.birthday,CURRENT_DATE())) age')
			->andWhere('u.birthday is not null')
			->getQuery()
			->getSingleScalarResult();

        $result['quick'] = [
            'total' => (int)$total,
            'totalBalance' => (int)$balance,
            'averageBalance' => round(Util::percent($balance, $total)),
			'averageMemberAge' => $averageMemberAge ? round($averageMemberAge) : null
        ];

        return new SuccessResponse($result);
    }

    protected function query(Request $request)
    {
        $query = $this->em->getRepository(User::class)
            ->createQueryBuilder('u')
            ->join('u.teams', 't')
            ->join('u.cards', 'uc')
            ->join('uc.card', 'c')
            ->where(Util::expr()->in('t.name', ':roles'))
            ->andWhere('c.cardProgram = :cardProgram')
            ->setParameter('cardProgram', Util::cardProgram())
            ->setParameter('roles', $this->getInvolvedRoles());

//		$negativeBalanceMember = $request->get('negativeBalanceMember', 'all');
//        if ($negativeBalanceMember === 'negative'){
//        	$query->andWhere('uc.type = :type')
//				->setParameter('type', PrivacyAPI::CARD_TYPE_DUMMY)
//				->andWhere('uc.balance < 0');
//		}
        $expr = Util::expr();
        if ($request->get('balance', 'all') !== 'all'){
            $query->andWhere('uc.type = :type')
                ->setParameter('type', PrivacyAPI::CARD_TYPE_DUMMY)
                ->andWhere('uc.balance ' . ($request->get('balance') === 'active' ? '>=' : '<') . ' 0');
        }

        $loadStatus = $request->get('loadStatus', 'all');
        if ($loadStatus !== 'all') {
            $counting = null;
            $returnIds = [];
            if ($loadStatus === ConsumerService::LOAD_STATUS_INSTANT) {
                $counting = 'total >= 3';
            } elseif ($loadStatus === ConsumerService::LOAD_STATUS_PREFUND) {
                $counting = 'total = 3';
            } elseif ($loadStatus === ConsumerService::LOAD_STATUS_FREEZE) {
                $counting = 'total >= 4';
            }
            $returnQuery = Util::em()->getRepository(UserCardLoad::class)
                ->createQueryBuilder('ucl')
                ->join('ucl.userCard', 'uc')
                ->join('uc.user', 'u')
                ->join('u.teams', 't')
                ->where($expr->in('t.name', ':roles'))
                ->setParameter('roles', SpendrBundle::getConsumerRoles())
                ->andWhere('ucl.type = :type')
                ->setParameter('type', UserCardLoad::TYPE_LOAD_CARD)
                ->andWhere('ucl.loadStatus = :loadStatus')
                ->setParameter('loadStatus', UserCardLoad::LOAD_STATUS_ERROR)
                ->andWhere($expr->like('ucl.meta', ':returnedByBank'))
                ->setParameter('returnedByBank', '%"returnedByBank":true%')
                ->andWhere($expr->like('ucl.meta', ':returnedCode'))
                ->setParameter('returnedCode', '%"returnedCode":"' . SpendrACHService::RETURN_CODE_INSUFFICIENT_FUNDS . '"%')
                ->select('count(ucl) total, u.id uid');
            if ($counting) $returnQuery->having($counting);
            $returns = $returnQuery->groupBy('u.id')
                ->getQuery()
                ->getResult();
            if ($returns && count($returns)) {
                $returnIds = array_map(function ($item) { return $item['uid']; }, $returns);
            }
            if ($loadStatus === ConsumerService::LOAD_STATUS_INSTANT) {
                $query->andWhere($expr->orX(
                    $expr->andX(
                        $expr->like('u.meta', ':manualTo'),
                        $expr->like('u.meta', ':manualAt')
                    ),
                    $expr->andX(
                        $expr->notLike('u.meta', ':manualTo1'),
                        $expr->eq('uc.type', ':type'),
                        $expr->gte('uc.balance', '0'),
                        $expr->notIn('u.id', ':returns')
                    )
                ))
                    ->setParameter('manualTo', '%"manuallyChangeLoadStatusTo":"' . $loadStatus . '"%')
                    ->setParameter('manualAt', '%manuallyChangeLoadStatusAt%')
                    ->setParameter('manualTo1', '%"manuallyChangeLoadStatusTo"%')
                    ->setParameter('type', PrivacyAPI::CARD_TYPE_DUMMY)
                    ->setParameter('returns', $returnIds);
            } elseif ($loadStatus === ConsumerService::LOAD_STATUS_PREFUND) {
                $query->andWhere($expr->orX(
                    $expr->andX(
                        $expr->like('u.meta', ':manualTo'),
                        $expr->like('u.meta', ':manualAt')
                    ),
                    $expr->andX(
                        $expr->notLike('u.meta', ':manualTo1'),
                        $expr->eq('uc.type', ':type'),
                        $expr->lt('uc.balance', '0')
                    ),
                    $expr->in('u.id', ':returns')
                ))
                    ->setParameter('manualTo', '%"manuallyChangeLoadStatusTo":"' . $loadStatus . '"%')
                    ->setParameter('manualAt', '%manuallyChangeLoadStatusAt%')
                    ->setParameter('manualTo1', '%"manuallyChangeLoadStatusTo"%')
                    ->setParameter('type', PrivacyAPI::CARD_TYPE_DUMMY)
                    ->setParameter('returns', $returnIds);
            } elseif ($loadStatus === ConsumerService::LOAD_STATUS_FREEZE) {
                $query->andWhere($expr->in('u.id', ':returns'))
                    ->setParameter('returns', $returnIds)
                    ->andWhere($expr->notLike('u.meta', ':manualTo'))
                    ->setParameter('manualTo', '%"manuallyChangeLoadStatusTo"%');
            }
        }

        $status = $request->get('status', 'all');
        if ($status === RegisterStep::ACTIVE) {// active
            $query->andWhere($expr->orX(
                $expr->eq('u.register_step', ':step'),
                $expr->andX(
                    $expr->eq('u.register_step', ':step1'),
                    $expr->like('u.meta', ':mobileVerified')
                )
            ))
                ->setParameter('step', $status)
                ->setParameter('step1', RegisterStep::EMAIL)
                ->setParameter('mobileVerified', '%"mobilePhoneVerified":true%');
        } elseif ($status === RegisterStep::INACTIVE || $status === RegisterStep::EMAIL) {// inactive|onboard
            $query->andWhere('u.register_step = :step')
                ->setParameter('step', $status);
        } elseif ($status === 'apply_delete') {
            $query->andWhere('u.meta like :meta')
                ->setParameter('meta', '%"deleteApplyAt"%')
                ->andWhere('u.status <> :closed')
                ->setParameter('closed', User::STATUS_CLOSED);
        }

        return $query;
    }

    protected function queryListParams(QueryBuilder $query, Request $request)
    {
        $params = new QueryListParams($query, $request, 'u', 'count(distinct u)');
        $params->distinct = true;
        $params->orderBy = [
            'u.id' => 'desc',
        ];
//        $params->searchFields = [
//            'u.id',
//            'u.name',
//            'u.email',
//			'u.mobilephone'
//        ];

		$keyword = $request->get('keyword');
		$like = '%' . $keyword . '%';
		$expr = Util::expr();
        $params->searchQueryCb = function (QueryBuilder $query) use ($expr, $like) {
			$query->andWhere($expr->orX(
				$expr->like('u.id', ':like'),
				$expr->like(
					$expr->concat(
						'u.firstName',
						$expr->concat($expr->literal(' '), 'u.lastName')
					),
					":like"
				),
				$expr->like('u.email', ':like'),
				$expr->like("REPLACE(u.mobilephone, '-', '')", ':like'),
			))->setParameter('like', $like);
		};
        return $params;
    }

	/**
	 * @param User $entity
	 *
	 * @return array
	 */
    protected function getRowData($entity)
    {
		$returnedTimes = FeeService::getConsumerAchReturnTimes($entity);
		$loadStatus = ConsumerService::getConsumerLoadStatus($entity);
		$isReactiveBefore = Util::meta($entity, 'reactiveLoadStatusDate');
		$isCanReactiveLoadStatus = $loadStatus === ConsumerService::LOAD_STATUS_FREEZE && !$isReactiveBefore;

		$manuallyChangeLoadStatusTo = Util::meta($entity, 'manuallyChangeLoadStatusTo');
		$manuallyChangeLoadStatusAt = Util::meta($entity, 'manuallyChangeLoadStatusAt');
		$loadStatusText = null;
		if ($manuallyChangeLoadStatusTo && $manuallyChangeLoadStatusAt) {
			$loadStatusText = '(Manually)';
		}
        if ($entity->getRegisterStep() == RegisterStep::EMAIL && Util::meta($entity, 'mobilePhoneVerified')) {
            $status = 'Active';
        } else {
            $status = ConsumerService::text($entity->getRegisterStep());
        }
        $dummy = UserService::getDummyCard($entity);
        return [
            'User ID' => $entity->getId(),
            'First Name' => $entity->getFirstName(),
            'Last Name' => $entity->getLastName(),
            'Email' => $entity->getEmail(),
            'Phone' => $entity->getMobilephone(),
            'Active Balance' => $this->user->inTeam(Role::ROLE_SPENDR_BANK)
                ? null : Money::format($dummy->getBalance(), 'USD', false),
            'Status' => $status,
			'Load Status' => $loadStatus . $loadStatusText,
			'Accept T&Cs At' => $entity->getBirthday() ? Util::formatDateTime($entity->getCreatedAt(), Util::DATE_TIME_FORMAT, $this->tz) : '',
			'Last Login' => Util::formatDateTime($entity->getLastLogin(), Util::DATE_TIME_FORMAT, $this->tz),
			'Email Verified' => $entity->getEmailVerified(),
			'isCanReactiveLoadStatus' => $isCanReactiveLoadStatus,
			'ACH Return Times (' . AchServiceController::RETURN_CODE_R01 . ')' => $returnedTimes,
            'changeManualCardStatus' => ConsumerService::hasManuallyLinkedCards($entity) > 0,
            'Manual Prefund' => Util::meta($entity, 'prefund_manual_link') ?? false,
            'Identity Match' => !Util::meta($entity, 'disableIdentityMatch'),
            'Delete Apply At' => Util::meta($entity, 'deleteApplyAt') ?
                Util::formatDateTime(
                    Util::meta($entity, 'deleteApplyAt'),
                    Util::DATE_TIME_FORMAT,
                    $this->tz
                ) : null,
            'Delete Reason' => $entity->getClosureReason()
        ];
    }

    protected function validateUser(User $user = null)
    {
        if (!$user || !$user->inTeams($this->getInvolvedRoles())) {
            throw PortalException::temp('Unknown user!');
        }
    }

	/**
	 * @Route("/admin/spendr/members/{user}/detail")
	 * @param User $user
	 *
	 * @return SuccessResponse
	 * @throws PortalException
	 */
    public function detailAction(User $user)
    {
        $this->validateUser($user);
        $data = $this->getRowData($user);
        return new SuccessResponse($data);
    }

	/**
	 * @Route("/admin/spendr/members/save", methods={"POST"})
	 * @param Request $request
	 *
	 * @return SuccessResponse|FailedResponse
	 * @throws PortalException
	 */
    public function saveAction(Request $request)
    {
    	if ($this->isViewOnlyRole()) {
    		return new DeniedResponse();
		}

        $uid = $request->get('User ID');
        $email = $request->get('Email');
        $phone = $request->get('Phone');
        if ($phone && strlen($phone) < 10) {
            return new FailedResponse('Invalid phone number');
        }
        $newUser = false;
        if ($uid) {
            $user = User::find($uid);
            $this->validateUser($user);
        } else {
            $user = User::findPlatformUserByEmail($email, $this->getInvolvedRoles());
            if ($user) {
                throw PortalException::temp('Duplicated accounts with the same email address!');
            }
            $newUser = true;
            $user = new User();
            $user->setEnabled(true)
                ->setPlainPassword(Util::generatePassword())
                ->setStatus(User::STATUS_ACTIVE)
                ->setSource('spendr_member');
        }

        // Format mobile phone
        $mobile = $request->get('Phone');
        $mobile = Util::formatManualInputPhoneNumber($mobile);
        $mobile = Util::formatFullPhone($mobile);
        $country = Util::parseCountryFromPhoneNumber($mobile);

        $user->setFirstName($request->get('First Name'))
            ->setLastName($request->get('Last Name'))
            ->setCountry($country ? $country : Country::usa())
            ->changeMobilePhone($mobile, $this->getInvolvedRoles())
            ->changeEmail($email, $this->getInvolvedRoles())
			->setUsername($email . '_spendr')
            ->clearRoles($this->getInvolvedRoles())
            ->ensureRole($this->getInvolvedRoles())
            ->persist();

        if ($newUser) {
//            $cardType = CardProgramCardType::getForCardProgram(Util::cardProgram());
//            $uc = CardService::create($user, $cardType);
			$uc = UserService::getDummyCard($user);

            if (!Util::isLive()) {
                $sandboxData['sandboxCard'] = true;
                Util::updateMeta($uc, $sandboxData);
            }

            if (Util::isServer()) {
                IDologyService::ofac($user);
            }
        }

        RegisterStep::updateUser($user);
        // Create braze user
        $attrs = BrazeService::getUserAttributes($user);
        BrazeService::userTrack(null, $newUser ? [
            'external_id' => BrazeService::getExternalPrefix() . $user->getId(),
            'name' => 'Profile Initiated',
            'time' => Carbon::now()->format('Y-m-d\TH:i:s\Z')
        ] : null, null, $attrs);
        SegmentService::track(null, [
            'userId' => BrazeService::getExternalPrefix() . $user->getId(),
            'traits' => $attrs
        ]);

        return new SuccessResponse($this->getRowData($user));
    }

    /**
     * @Route("/admin/spendr/members/export", methods={"POST"})
     * @param Request $request
     *
     * @return SuccessResponse|DeniedResponse
     */
    public function export(Request $request)
    {
		if (SpendrBundle::notAllowedExport()) {
			return new DeniedResponse();
		}
		$fields = [
            'User ID' => 10,
            'First Name' => 20,
            'Last Name' => 20,
            'Email' => 30,
            'Phone' => 20,
            'Active Balance' => 20,
            'Status' => 20,
            'Accept T&Cs At' => 20
        ];
        return $this->commonExport($request, 'Spendr Members', $fields);
    }

	/**
	 * @Route("/admin/spendr/members/export-braze", methods={"POST"})
	 * @param Request $request
	 *
	 * @return SuccessResponse|DeniedResponse
	 * @throws PortalException
	 */
    public function exportBraze(Request $request)
    {
		if (SpendrBundle::notAllowedExport()) {
			return new DeniedResponse();
		}

        Util::longerRequest();

        $query = $this->query($request);
        $params = $this->queryListParams($query, $request);
        if ($request->get('query_count')) {
            $count = $this->queryCountForExport($params);
            return new SuccessResponse($count);
        }

        $from  = (int)$request->get('query_from', 0);
        $limit = (int)$request->get('query_limit', 5000);

        $data = $this->queryListForExport($params, $from, $limit, function (User $entity) {
            return [
                'external_id' => BrazeService::getExternalPrefix() . $entity->getId(),
                'name' => $entity->getName(),
                'email' => $entity->getEmail(),
                'phone' => str_replace([' ', '-'], '', $entity->getMobilephone())
            ];
        });

        $timestamp = $request->get('query_timestamp', time());
        $filename = 'Spendr Zendesk Attributes_' . date('YmdHis', $timestamp);
        $destination = Util::uploadDir('report') . $filename . '.csv';
        $excel = $this->loadOrCreateExcel($destination, $filename);
        $headers = [
            'external_id' => 20,
//            'first_name' => 20,
//            'last_name' => 20,
            'name' => 40,
            'email' => 40,
//            'dob' => 20,
//                'gender' => $user->getGender(),
//            'home_city' => 20,
//            'country' => 10,
            'phone' => 40,
//            'email_subscribe' => 'subscribed',
//            'push_subscribe' => 'subscribed',
//            'onboard_status' => 20,
//            'bank_linked' => 10,
//            'created_at' => 30,
//            'state' => 20,
//            'count_of_returns' => 10,
//            'total $ amount of manual promos, earn rewards, and promo codes received' => 40,
//            'account_balance' => 10,
//            'total_amount_spend' => 10,
//            'avg_spend' => 10,
//            'bank_name' => 40,
//            'zip_code' => 10,
//            'street_address' => 30,
//            'id_doc_loaded' => 10,
//            'count_of_products_loaded' => 10,
//            'funding_status' => 10,
//            'name_of_merchants_purchased_from' => 40
        ];

        $excel = $this->generateSheetAppendToExcel($excel, $from . ' ~ ' . ($from + $limit), $headers, $data,
            function ($col, $row, $excel, $title) {
                return $row[$title] ?? '';
            });
        $this->saveCsvTo($excel, $destination, count($headers));

        return new SuccessResponse('report/' . $filename . '.csv');
    }

	/**
	 * @Route("/admin/spendr/members/load_init")
	 * @param Request $request
	 *
	 * @return FailedResponse|SuccessResponse
	 * @throws PortalException
	 * @throws \PortalBundle\Exception\DeniedException
	 */
    public function initLoad(Request $request)
    {
		if ($this->isViewOnlyRole()) {
			return new DeniedResponse();
		}

		$this->authRoles([
            Role::ROLE_MASTER_ADMIN,
			Role::ROLE_SPENDR_PROGRAM_OWNER,
			Role::ROLE_SPENDR_COMPLIANCE
        ]);
        // get member
        $user = User::find($request->get('id'));

        if (!$user) {
            throw PortalException::temp('Unknown user!');
        }

        $max = $user->getBalance()/100;

        $default = 10;
        $min = 1;

        if ($default > $max) {
            $default = $max;
        }
        if ($min > $max) {
            $min = 0;
        }

        $partnerDummy = UserService::getSpendrBalanceDummyCard('spendr');

		$rs = ConsumerService::isCanTransaction($user);
		$isCanTransaction = is_string($rs) ? false : true;

        return new SuccessResponse([
            'init' => $default,
            'min' => $min,
            'max' => $max,
            'balance' => $user->getBalance(),
            'partnerBalance' => $partnerDummy ? $partnerDummy->getBalance() : null,
            'bankList' => ConsumerService::getUserBankList($user),
			'isLoadAvailable' => $rs
        ]);
    }

	/**
     * Todo: Add a txnId input box to manually handle exceptions in the restrict area
     * 
	 * @Route("/admin/spendr/members/unload_confirm")
	 * @param Request $request
	 *
	 * @return FailedResponse|SuccessResponse
	 * @throws PortalException
	 * @throws \PortalBundle\Exception\DeniedException
	 */
    public function unloadConfirm(Request $request)
    {
		if ($this->isViewOnlyRole()) {
			return new DeniedResponse();
		}

		$this->authRoles([
            Role::ROLE_MASTER_ADMIN,
			Role::ROLE_SPENDR_PROGRAM_OWNER,
			Role::ROLE_SPENDR_COMPLIANCE
        ]);

        $user = User::find($request->get('id'));
        $amount = Money::normalizeAmount($request->get('unloadAmount'), 'USD');
		$bankCard = UserCard::find($request->get('bankId'));

        if (!$user || !$amount || !$bankCard) {
            return new FailedResponse('Invalid parameters.');
        }

        return self::traitUnload($user, $amount, $bankCard, $this->user);
    }

	/**
	 * @Route("/admin/spendr/members/manual-load", methods={"POST"})
	 * @param Request $request
	 * @return FailedResponse|SuccessResponse
	 * @throws \PortalBundle\Exception\DeniedException
	 * @throws PortalException
	 * @throws \Throwable
	 */
    public function manualLoad(Request $request)
	{
		if ($this->isViewOnlyRole()) {
			return new DeniedResponse();
		}

		$this->authRoles([
			Role::ROLE_MASTER_ADMIN,
			Role::ROLE_SPENDR_PROGRAM_OWNER,
			Role::ROLE_SPENDR_COMPLIANCE
		]);

		$member = User::find($request->get('id'));
		if (!$member) {
			return new FailedResponse('Unknown user!');
		}

		return LoadService::consumerManuallyLoad($request, $member, $this->user);
	}

	/**
	 * @Route("/admin/spendr/members/resend-invitation")
	 * @param Request $request
	 * @return FailedResponse|SuccessResponse
	 */
	public function resendRegisterEmail(Request $request)
	{
		if ($this->isViewOnlyRole()) {
			return new DeniedResponse();
		}

		$member = User::find($request->get('id'));
		if (!$member) {
			return new FailedResponse('Unknown user!');
		}

		\CoreBundle\Services\UserService::sendResetPasswordEmail($member);

		return new SuccessResponse();
	}

    /**
     * @Route("/admin/spendr/members/{user}/toggle-status", methods={"POST"})
     * @param Request $request
     * @param User    $user
     *
     * @return FailedResponse|SuccessResponse
     * @throws PortalException
     */
    public function toggleStatusAction(Request $request, User $user)
    {
		if ($this->isViewOnlyRole()) {
			return new DeniedResponse();
		}

		$this->validateUser($user);
        $status = $request->get('Status', 'Active') === 'Active'
            ? User::STATUS_ACTIVE
            : User::STATUS_INACTIVE;
        $user->setStatus($status, true)
            ->persist();

        if ($status === User::STATUS_ACTIVE) {
            $dummy = UserService::getDummyCard($user);
            if ($dummy->getStatus() !== UserCard::STATUS_ACTIVE) {
                $dummy->setStatus(UserCard::STATUS_ACTIVE)
                    ->persist();
            }
        }
        
        if ($request->get('activeRegisterStep', false) && $user->getRegisterStep() === RegisterStep::EMAIL) {
            Util::updateMeta($user, [
                'activeRegisterStep' => true
            ]);
        }
        ConsumerService::updateOnboardStatus($user);
        // Update admin subscribe status
        BrazeService::userTrack(null, null, null, [
            'external_id' => BrazeService::getExternalPrefix() . $user->getId(),
            'email_subscribe' => $status === User::STATUS_ACTIVE ? BrazeService::TYPE_OPTEDIN : BrazeService::TYPE_UNSUBSCRIBED
        ]);
        return new SuccessResponse($this->getRowData($user));
    }

	/**
	 * @Route("/admin/spendr/members/{user}/current-load-status")
	 *
	 * @param Request $request
	 * @param User $user
	 * @return DeniedResponse|FailedResponse|SuccessResponse
	 */
    public function getCurrentLoadStatus(Request $request, User $user)
	{
		$this->authRoles([
			Role::ROLE_MASTER_ADMIN,
			Role::ROLE_SPENDR_PROGRAM_OWNER,
			Role::ROLE_SPENDR_COMPLIANCE
		]);

		$currentStatus = ConsumerService::getConsumerLoadStatus($user);

        $promptMessage = null;
		if ($currentStatus === ConsumerService::LOAD_STATUS_FREEZE) {
            $newStatus = ConsumerService::LOAD_STATUS_PREFUND;
            $promptMessage = 'Are you sure that you want to reactivate load status for this member? And a frozen account can only be changed to prefund status.';
		} else {
            $newStatus = $currentStatus === ConsumerService::LOAD_STATUS_INSTANT ? ConsumerService::LOAD_STATUS_PREFUND : ConsumerService::LOAD_STATUS_INSTANT;
            $promptMessage = 'Are you sure you want to manually change this user\'s load status from "' . $currentStatus . '" to "' . $newStatus . '" ?';
        }

		return new SuccessResponse([
			'currentLoadStatus' => $currentStatus,
			'newLoadStatus' => $newStatus,
            'promptMessage' => $promptMessage
		]);
	}

	/**
	 * @Route("/admin/spendr/members/{user}/toggle-load-status")
	 *
	 * @param Request $request
	 * @param User $user
	 * @return DeniedResponse|FailedResponse|SuccessResponse
	 */
	public function toggleLoadStatus(Request $request, User $user)
	{
        $this->authRoles([
			Role::ROLE_MASTER_ADMIN,
			Role::ROLE_SPENDR_PROGRAM_OWNER,
			Role::ROLE_SPENDR_COMPLIANCE
		]);

		$newLoadStatus = $request->get('newLoadStatus');
		$currentLoadStatus = ConsumerService::getConsumerLoadStatus($user);

		if ($newLoadStatus === $currentLoadStatus) {
			return new SuccessResponse('The current load status is already "' . $currentLoadStatus . '".');
		}

		Util::updateMeta($user, [
			'manuallyChangeLoadStatusTo' => $newLoadStatus,
			'manuallyChangeLoadStatusAt' => Carbon::now()->format('c')
		]);

		$user->addNote(
			'Manually changed load status from "' . $currentLoadStatus . '" to "' . $newLoadStatus . '".',
			true,
			$this->user->getId(),
			$this->user
		);

		return new SuccessResponse();
	}

    /**
     * @Route("/admin/spendr/members/{user}/manual-prefund")
     *
     * @param Request $request
     * @param User $user
     * @return DeniedResponse|FailedResponse|SuccessResponse
     */
    public function changeManualPrefund(Request $request, User $user)
    {
        if ($this->isViewOnlyRole()) {
            return new DeniedResponse();
        }
        $prefund = $request->get('Prefund');

        Util::updateMeta($user, [
            'prefund_manual_link' => $prefund == 'Prefund',
        ]);

        return new SuccessResponse();
    }

    /**
     * @Route("/admin/spendr/members/{user}/disable-identity-match")
     *
     * @param Request $request
     * @param User $user
     * @return DeniedResponse|SuccessResponse
     */
    public function disableIdentityMatchAction(Request $request, User $user)
    {
        if ($this->isViewOnlyRole()) {
            return new DeniedResponse();
        }

        $disable = !!$request->get('disableMatch', true);
        Util::updateMeta($user, [
            'disableIdentityMatch' => $disable,
        ]);
        $user->addNote(
            ($disable ? 'Disabled' : 'Enabled') . ' Plaid identity match.',
            true,
            $this->user->getId(),
            $this->user
        );

        return new SuccessResponse();
    }

    /**
     * @Route("/admin/spendr/members/{user}/allow-sameday", methods={"POST"})
     * @param User $user
     * @return FailedResponse|SuccessResponse
     */
    public function allowSamedayLink(User $user)
    {
        if ($this->isViewOnlyRole()) {
            return new DeniedResponse();
        }
        Data::set('spendr_link_sameday_' . $user->getId(), 1, true, 86400);

        return new SuccessResponse('Operate success');
    }

	/**
	 * @Route("/admin/spendr/members/{user}/reactivate-load-status", methods={"POST"})
	 *
	 * @param Request $request
	 * @param User $user
	 * @return FailedResponse|SuccessResponse
	 * @throws PortalException
	 */
    public function reactivateLoadStatusAction(Request $request, User $user)
	{
		if ($this->isViewOnlyRole()) {
			return new DeniedResponse();
		}

		$this->validateUser($user);

		$spendrConsumerLoadStatus = ConsumerService::getConsumerLoadStatus($user);
		if ($spendrConsumerLoadStatus !== ConsumerService::LOAD_STATUS_FREEZE) {
			return new FailedResponse('The account has not been frozen and does not need to be reactivated.');
		}

		$isReactiveBefore = Util::meta($user, 'reactiveLoadStatusDate');
		if ($isReactiveBefore) {
			return new FailedResponse('The account has been reactive once before and cannot be reactive again.');
		}

		Util::updateMeta($user, [
			'reactiveLoadStatusDate' => Carbon::now()->format('c'),
		]);

		return new SuccessResponse();
	}

    /**
     * @Route("/admin/spendr/members/{user}/handle-delete", methods={"POST"})
     * @param User $user
     * @param Request $request
     * @return FailedResponse|SuccessResponse
     * @throws \CoreBundle\Exception\FailedException
     */
    public function handleDeleteApply(User $user, Request $request)
    {
        if ($this->isViewOnlyRole()) {
            return new DeniedResponse();
        }
        if ($user->getStatus() === User::STATUS_CLOSED) {
            return new FailedResponse('This account has been deleted already!');
        }
        if ($request->get('type') === 'cancel') {
            $meta = Util::meta($user);
            unset($meta['deleteApplyAt']);
            $user->setMeta(Util::j2s($meta))
                ->setClosureReason(null)
                ->persist();
        } else {
            BrazeService::userDelete($user);
            Util::executeCommand('span:spendr:expire-user-token', [
                '--force' => null,
                '--user' => $user->getId()
            ]);
            $ucs = Util::em()->getRepository(UserCard::class)
                ->createQueryBuilder('uc')
                ->join('uc.card', 'cpct')
                ->where(Util::expr()->eq('cpct.cardProgram',':cardProgram'))
                ->setParameter('cardProgram', CardProgram::spendr())
                ->join('uc.user', 'u')
                ->andWhere('uc.status = :status')
                ->andWhere('u.id = :userId')
                ->andWhere('uc.type != :type')
                ->setParameter('type', PrivacyAPI::CARD_TYPE_DUMMY)
                ->setParameter('userId', $user->getId())
                ->setParameter('status', UserCard::STATUS_ACTIVE)
                ->getQuery()
                ->getResult();;
            foreach ($ucs as $uc) {
                $uc->setStatus(UserCard::STATUS_INACTIVE)
                    ->persist();
            }
            $user->setUsername('deleted')
                ->setUsernameCanonical('deleted')
//                ->setFirstName(null)
//                ->setLastName(null)
                ->setEmail('deleted')
                ->setEmailCanonical('deleted')
//                ->setMobilephone(null)
//                ->setBirthday(null)
//                ->setZip(null)
//                ->setAddress(null)
//                ->setCity(null)
//                ->setState(null)
//                ->setCountry(null)
                ->setRegisterStep(RegisterStep::INACTIVE)
                ->setStatus(User::STATUS_CLOSED)
                ->persist();
        }

        return new SuccessResponse('Operate success');
    }
}
