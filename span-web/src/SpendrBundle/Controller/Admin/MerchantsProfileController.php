<?php

namespace SpendrBundle\Controller\Admin;

use Carbon\Carbon;
use ClfBundle\Entity\Transaction;
use CoreBundle\Entity\Attachment;
use CoreBundle\Entity\Notes;
use CoreBundle\Entity\Role;
use CoreBundle\Response\DeniedResponse;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Services\ZipService;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Util;
use SpendrBundle\Entity\Location;
use SpendrBundle\Entity\SpendrMerchant;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use SpendrBundle\Services\UserService;

class MerchantsProfileController extends MerchantsController
{
	/**
	 * @Route("/admin/spendr/merchants/{merchant}/profile")
	 * @param Request $request
	 * @param SpendrMerchant $merchant
	 *
	 * @return SuccessResponse
	 * @throws \Doctrine\ORM\NoResultException
	 * @throws \Doctrine\ORM\NonUniqueResultException
	 * @throws \PortalBundle\Exception\PortalException
	 */
    public function profileAction(Request $request, SpendrMerchant $merchant)
    {
        $data = $this->getRowData($merchant);

        $data['Number of Locations'] = $this->em->getRepository(Location::class)
            ->createQueryBuilder('l')
            ->where('l.merchant = :merchant')
            ->setParameter('merchant', $merchant)
            ->select('count(l)')
            ->getQuery()
            ->getSingleScalarResult();
        $data['Purchases'] = $this->em->getRepository(Transaction::class)
            ->createQueryBuilder('l')
            ->where('l.merchant = :merchant')
            ->setParameter('merchant', $merchant)
            ->select('count(l)')
            ->getQuery()
            ->getSingleScalarResult();
        $data['Average Order'] = Money::formatUSD($this->em->getRepository(Transaction::class)
            ->createQueryBuilder('l')
            ->where('l.merchant = :merchant')
            ->setParameter('merchant', $merchant)
            ->select('avg(l.amount)')
            ->getQuery()
            ->getSingleScalarResult());

        $admin = $merchant->getAdminUser();
        $dummy = UserService::getDummyCard($admin);
        $data['Merchant Balance'] = !$this->user->inTeams([Role::ROLE_SPENDR_BANK]) ? $dummy->getBalanceText() : null;
        $data['Merchant Manager Email'] = $admin->getEmail();
        $data['Merchant Manager Phone'] = $admin->getMobilephone();

        return new SuccessResponse($data);
    }

    /**
     * @Route("/admin/spendr/merchants/{merchant}/notes")
     * @param Request $request
     * @param SpendrMerchant $merchant
     *
     * @return SuccessResponse
     */
    public function notesAction(Request $request, SpendrMerchant $merchant)
    {
        $user = $merchant->getAdminUser();
        $data = Util::toApiArray($user->getNotes());
        return new SuccessResponse($data);
    }

	/**
	 * @Route("/admin/spendr/merchants/{merchant}/notes-del/{note}")
	 * @param Request $request
	 * @param SpendrMerchant $merchant
	 * @param Notes $note
	 *
	 * @return SuccessResponse|DeniedResponse
	 * @throws \Doctrine\ORM\OptimisticLockException
	 */
    public function notesRemoveAction(Request $request, SpendrMerchant $merchant, Notes $note)
    {
		if ($this->isViewOnlyRole()) {
			return new DeniedResponse();
		}

        $user = $merchant->getAdminUser();
        $toThisUser = $note->getToname() === (string)$user->getId();
        $createdByMe = Util::eq($note->getCreatedBy(), $this->user);
        if ($toThisUser && $createdByMe) {
            $this->em->remove($note);
            $this->em->flush();
        }
        return new SuccessResponse();
    }

    /**
     * @Route("/admin/spendr/merchants/{merchant}/notes-add", methods={"POST"})
     * @param Request $request
     * @param SpendrMerchant $merchant
     *
     * @return SuccessResponse|DeniedResponse
     */
    public function notesAddAction(Request $request, SpendrMerchant $merchant)
    {
		if ($this->isViewOnlyRole()) {
			return new DeniedResponse();
		}

        $user = $merchant->getAdminUser();
        $note = $user->addNote($request->get('content'), true, $this->user->getId(), $this->user);
        return new SuccessResponse($note->toApiArray());
    }

	/**
	 * @Route("/admin/spendr/merchants/{merchant}/download-docs")
	 *
	 * @param Request $request
	 * @param SpendrMerchant $merchant
	 * @return FailedResponse|SuccessResponse
	 * @throws \PortalBundle\Exception\PortalException
	 */
	public function downloadDocs(Request $request, SpendrMerchant $merchant)
	{
		if ($this->user->inTeams([Role::ROLE_SPENDR_BANK])) {
			return new DeniedResponse();
		}

		$docs = Util::meta($merchant, 'docs') ?? [];
		if (!$docs) {
			return new FailedResponse('No file to download!');
		}

		$attachIds = [];
		foreach ($docs as $key=>$doc) {
			if ($doc) {
				foreach ($doc as $single) {
					$attachIds[$key] = $single['id'];
				}
			}
		}

		if (!$attachIds) {
			return new FailedResponse('No file to download!');
		}

		$expr = Util::expr();
		$attachs = $this->em->getRepository(Attachment::class)
			->createQueryBuilder('a')
			->where($expr->in('a.id', $attachIds))
			->distinct()
			->getQuery()
			->getResult();

		if (!$attachs) {
			return new FailedResponse('No file to download!');
		}

		$destinations = [];
		/** @var Attachment $attach */
		foreach ($attachs as $attach) {
			$destinations[] = $attach->prepareForLocalRead();
		}

		$dir = Util::uploadDir('merchant_docs');
		$time = Carbon::now()->format('YmdHis');
		$fileName = 'Merchant_' . $merchant->getId() . '_docs_' . $time . '.zip';
		$zip = ZipService::create($dir . $fileName, $destinations);

		if ($zip) {
			return new SuccessResponse('merchant_docs/' . $fileName);
		} else {
			return new FailedResponse('Created zip error!');
		}
	}
}
