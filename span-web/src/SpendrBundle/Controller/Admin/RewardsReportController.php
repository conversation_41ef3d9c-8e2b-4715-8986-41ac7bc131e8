<?php


namespace SpendrB<PERSON>le\Controller\Admin;


use CoreB<PERSON>le\Entity\UserCardLoad;
use CoreBundle\Response\DeniedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\QueryListParams;
use CoreBundle\Utils\Util;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\QueryBuilder;
use SpendrBundle\Services\LoadService;
use SpendrBundle\Services\PromotionService;
use SpendrBundle\SpendrBundle;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class RewardsReportController extends BaseController
{
    public function __construct()
    {
        parent::__construct();

        $this->authAdminsWithoutBank();
    }

    /**
     * @Route("/admin/spendr/rewards-report/list/{page}/{limit}", methods={"GET"}, defaults={"page" = 1,"limit" = 10})
     * @param Request $request
     * @param int $page
     * @param int $limit
     *
     * @return SuccessResponse
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function search(Request $request, $page = 1, $limit = 10)
    {
        $resp = $this->traitSearch($request, [
            'createdAt',
        ], $page, $limit);
        $result = $resp->getResult();
        $query = $this->getResetParameterizedQuery();
        $quick = (clone $query)->andWhere('ucl.type = :type')
            ->andWhere('ucl.loadStatus != :status')
            ->setParameter('type', UserCardLoad::TYPE_LOAD_CARD)
            ->setParameter('status', UserCardLoad::LOAD_STATUS_ERROR)
            ->select('count(distinct ucl) total, sum(ucl.initialAmount) totalAmount')
            ->distinct()
            ->getQuery()
            ->getSingleResult();
        $result['quick'] = [
            'total'  => (int)$quick['total'],
            'rewards_total' => (int)$quick['totalAmount'],
            'revoke_total' => ((clone $query)->andWhere('ucl.type = :type')
                ->andWhere('ucl.loadStatus = :status')
                ->setParameter('type', UserCardLoad::TYPE_UNLOAD)
                ->setParameter('status', UserCardLoad::LOAD_STATUS_LOADED)
                ->select('sum(ucl.initialAmount)')
                ->getQuery()
                ->getSingleScalarResult())
        ];
        return new SuccessResponse($result);
    }

    protected function query(Request $request)
    {
        $expr = Util::expr();
        $query = $this->em->getRepository(UserCardLoad::class)
            ->createQueryBuilder('ucl')
            ->join('ucl.userCard', 'c')
            ->join('c.user', 'u')
            ->join('c.card', 'cpct')
            ->where(Util::expr()->eq('cpct.cardProgram',':cardProgram'))
            ->setParameter('cardProgram', $this->cardProgram)
            ->andWhere($expr->in('ucl.loadType', ':loadTypes'))
            ->setParameter('loadTypes', [
                LoadService::LOAD_TYPE_PROMOTION,
                LoadService::UNLOAD_TYPE_PROMOTION
            ]);
        $type = $request->get('rewardType', 'All');
        if ($type === PromotionService::PROMOTION_TYPE_PROMO_CODE) {
            $query->andWhere(Util::expr()->isNotNull('ucl.promoCode'));
        } elseif ($type === PromotionService::PROMOTION_TYPE_EARN) {
            $query->andWhere('ucl.promoType = :promoType')
                ->setParameter('promoType', $type);
        } elseif ($type === PromotionService::PROMOTION_TYPE_MANUAL) {
            $query->andWhere(Util::expr()->isNull('ucl.promoCode'))
                ->andWhere(Util::expr()->isNull('ucl.promoType'));
        }

        return $this->queryByDateRange($query, 'ucl.createdAt', $request);
    }

    protected function queryListParams(QueryBuilder $query, Request $request)
    {
        $params = new QueryListParams($query, $request, 'ucl', 'count(distinct ucl)');
        $params->distinct = true;
        $params->orderBy = [
            'ucl.id' => 'desc',
        ];
        $params->searchFields = [
            'ucl.id',
            'ucl.loadStatus'
        ];
        return $params;
    }

    /**
     * @param UserCardLoad $entity
     * @return array
     */
    protected function getRowData($entity)
    {
        $amount = $entity->getLoadAmountUSD() ? $entity->getLoadAmountUSD() : $entity->getInitialAmount();

        $failedLoadId = null;
        $failedUnloadId = null;
        $rollbackUnloadId = null;
        $meta = Util::meta($entity);
        if ($meta) {
            $failedLoadId = $meta['Spendr load failed'] ?? null;
            $failedUnloadId = $meta['Spendr unload failed'] ?? null;
            $rollbackUnloadId = $meta['Spendr rollback unload'] ?? null;
        }
        $note = null;
        if ($failedLoadId) {
            $note = 'Refund the amount of failed load. Failed load ID: ' . $failedLoadId;
        }
        if ($failedUnloadId) {
            $note = 'Refund the amount of failed unload. Failed unload ID: ' . $failedUnloadId;
        }
        if ($rollbackUnloadId) {
            $note = 'Rollback unload amount. Unload ID: ' . $rollbackUnloadId;
        }

        $error = null;
        if ($entity->getError() && $entity->getError() !== '[]') {
            $errorArr = Util::s2j($entity->getError());
            if (is_array($errorArr)) {
                $error = isset($errorArr['load']) && $errorArr['load'] ? $errorArr['load'] : null;
            } else {
                $error = $entity->getError();
            }
        }

        $type = 'Manual Promo Loads';
        if ($entity->getPromoCode()) {
            $type = 'Promo Code Rewards';
        } elseif (Util::meta($entity, 'promoType')) {
            $type = 'Earn Rewards';
        }

        return [
            'ID' => $entity->getId(),
            'Date & Time' => Util::formatDateTime(
                $entity->getInitializedAt(),
                Util::DATE_TIME_FORMAT,
                $this->tz
            ),
            'User ID' => $entity->getUserCard()->getUser()->getId(),
            'Full Name' => $entity->getUserCard()->getUser()->getFullName(),
            'Email' => $entity->getUserCard()->getUser()->getEmail(),
            'Amount' => $entity->isUnload() ? -$amount : $amount,
            'Loaded' => Money::format($amount, 'USD', false),
            'Type' => $entity->getType() === UserCardLoad::TYPE_LOAD_CARD ? 'Rewards' : 'Revoke',
            'Load Type' => $type,
            'Error Reason' => $error,
            'Note' => $note,
            'Custom Message' => $meta['rewardTitle'] ?? ($meta['customMessage'] ?? null),
            'Earn Date' => Util::formatDate($entity->getCompletedAt(), $this->tz, Util::DATE_TIME_FORMAT),
            'Status' => $entity->getLoadStatus() ? $entity->getLoadStatus() : 'Pending'
        ];
    }

    /**
     * @Route("/admin/spendr/rewards-report/export", methods={"POST"})
     * @param Request $request
     *
     * @return SuccessResponse|DeniedResponse
     */
    public function export(Request $request)
    {
        if (SpendrBundle::notAllowedExport()) {
            return new DeniedResponse();
        }
        $fields = [
            'ID' => 10,
            'Date & Time' => 20,
            'User ID' => 10,
            'Full Name' => 20,
            'Email' => 30,
            'Amount' => 10,
            'Loaded' => 10,
            'Type' => 20,
            'Load Type' => 20,
            'Error Reason' => 30,
            'Custom Message' => 20,
            'Earn Date' => 20,
            'Status' => 20
        ];
        return $this->commonExport($request, 'Spendr Rewards Report', $fields);
    }
}
