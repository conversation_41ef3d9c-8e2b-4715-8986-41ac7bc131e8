<?php
namespace Spendr<PERSON><PERSON>le\Controller\Admin;

use Carbon\Carbon;
use Clf<PERSON><PERSON>le\Entity\Transaction;
use Clf<PERSON><PERSON>le\Entity\TransactionStatus;
use Clf<PERSON><PERSON>le\Entity\TransactionType;
use CoreB<PERSON>le\Entity\CardProgram;
use CoreB<PERSON>le\Entity\Role;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Entity\UserFeeHistory;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Bundle;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Util;
use PortalBundle\Exception\PortalException;
use SalexUserBundle\Entity\User;
use SpendrBundle\Entity\SpendrMerchant;
use SpendrBundle\Entity\SpendrTip;
use SpendrBundle\Entity\SpendrTransaction;
use SpendrBundle\Services\Bank\ImportTransactionsService;
use SpendrBundle\Services\FeeService;
use SpendrBundle\Services\LoadService;
use SpendrBundle\Services\PromotionService;
use SpendrBundle\Services\UserCardService;
use SpendrBundle\Services\UserService;
use SpendrBundle\SpendrBundle;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use UsUnlockedBundle\Services\Privacy\PrivacyAPI;


class MemberBalanceReportController extends BaseController
{
	public function __construct()
	{
		parent::__construct();

		$this->authAdminsWithoutBank();
	}

	protected function getInvolvedRoles()
	{
		return Bundle::getMemberRoles();
	}

	/**
	 * @Route("/admin/spendr/members/balance-report/list/{page}/{pageSize}", methods={"GET"}, defaults={"page" = 1,"pageSize" = 10})
	 * @param Request $request
	 *
	 * @param bool $return
	 * @param int $page
	 * @param int $pageSize
	 * @return SuccessResponse|array
	 * @throws PortalException
	 * 
	 */
	public function index(Request $request, $return = false, $page = 1, $pageSize = 10)
	{
		$enableCache = false;
		$balanceAdminType = $request->get('balanceAdminType');
		if ($balanceAdminType && in_array($balanceAdminType, [
				'spendr', 'bank', 'tern'
			])) {
			$member = UserService::getSpendrBalanceAdminAccount($balanceAdminType);
			$memberID = $member->getId();
			if ($balanceAdminType === 'spendr') {
				$enableCache = true;
			}
		} else {
			$memberID = $request->get('memberID');
			$member = User::find($memberID);
		}

		$cacheKey = 'cache_spendr_running_balance_list_' . $memberID;
		$force = $request->get('mode') === 'force' || $return;
		if (!Data::has($cacheKey)) {
		   $force = true;
		}

		$data = [];
		if ($force) {
			$data = $this->getTxnList($request, $member, $balanceAdminType, $enableCache, $cacheKey);
			if ($return) {
				return $data;
			}
		}

		if ($balanceAdminType === 'spendr') {
			$data = Data::getArray($cacheKey);
		}

		$total = count($data);
		if ($pageSize > 0) {
			$data = array_slice($data, $pageSize * ($page - 1), $pageSize);
		}

		return new SuccessResponse([
			'data' => $data,
			'count' => $total,
			'quick' => [
				'total' => $total
			]
		]);
	}

	/**
	 * @param Request $request
	 * @param User $member
	 * @param null $balanceAdminType
	 * @param bool $enableCache
	 * @param null $cacheKey
	 * @return array
	 * @throws PortalException
	 * 
	 */
	private function getTxnList(
		Request $request,
		User $member,
		$balanceAdminType = null,
		$enableCache = false,
		$cacheKey = null
	){
		$memberID = $member->getId();
		$this->validateUser($member);

		$list = $this->queryTxnList($request, $member, 'list', $balanceAdminType);

		$items = [];
		foreach ($list as $item) {
			$data = [];
            $tipData = [];
			if ($item['data_from'] === 'transaction') {
				$data = $this->handleTransactionData($item, $member);
                $tipData = $this->handleTransactionTipData($item, $member);
			} else if ($item['data_from'] === 'load') {
				$data = $this->handleLoadData($item, $member, $balanceAdminType);
			} else if ($item['data_from'] === 'fee') {
				$data = $this->handleFeeData($item, $member, $balanceAdminType);
			} else if ($item['data_from'] === 'spendr_transaction') {
				$data = $this->handleSpendrTransactionData($item, $member);
			}
			if ($data) {
                $items[] = $data;
            }
			if ($tipData) {
                $items[] = $tipData;
            }
		}

		if ($balanceAdminType === 'spendr') {
			$pendingFees = $this->handlePendingBalanceData();
			if ($pendingFees) {
				$items = array_merge($items, $pendingFees);
			}
		}

		Util::usort($items, [
			'timestamp' => 'desc',
		]);

		$balance = 0;
		for ($i = count($items) - 1; $i >= 0; $i--) {
			$balance += $items[$i]['Amount Value'];
			$items[$i]['Running Balance'] = Money::format($balance, 'USD', false);
			$items[$i]['Account ID'] = $memberID;
		}

		if ($enableCache) {
			Data::setArray($cacheKey, $items, 0, false);
		}

		return $items;
	}

	/**
	 * @param Request $request
	 * @param User $member
	 * @param string $type
	 * @param null $balanceAdminType
	 * @return array|mixed
	 * @throws PortalException
	 * 
	 */
	private function queryTxnList(Request $request, User $member, $type = 'list', $balanceAdminType = null)
	{
		$date = $request->get('date');

		$dummy = UserService::getDummyCard($member);

		$subSql = '';
		$loadSql = null;
		$txnSql = null;
		$feeSql = null;
		$spendrTxnSql = null;
		$loadSubSql = '';
		$txnSubSql = '';
		$feeSubSql = '';

		if ($date) {
			$start = Carbon::parse($date)->startOfDay();
			$end = Carbon::parse($date)->endOfDay();
			$txnSubSql .= ' AND t.date_time >= "' . $start . '"';
			$txnSubSql .= ' AND t.date_time <= "' . $end . '"';
			$loadSubSql .= ' AND ucl.initialized_at >= "' . $start . '"';
			$loadSubSql .= ' AND ucl.initialized_at <= "' . $end . '"';
			$feeSubSql .= ' AND ufh.time >= "' . $start . '"';
			$feeSubSql .= ' AND ufh.time <= "' . $end . '"';
		}

		if ($member->inTeams(SpendrBundle::getMerchantBalanceAdminRoles())) {
			$merchant = SpendrMerchant::findByAdminUser($member);
			$txnSubSql = ' AND t.merchant_id = ' . $merchant->getId();
		} else if ($member->inTeams(SpendrBundle::getConsumerRoles())) {
			$txnSubSql = ' AND t.consumer_id = ' . $member->getId();
		}

		if ($member->inTeams(SpendrBundle::getMerchantBalanceAdminRoles())) {
			$merchant = SpendrMerchant::findByAdminUser($member);
			$loadSql = $this->normalLoadSql($dummy, $loadSubSql);
			$txnSql = $this->normalTxnSql($txnSubSql);
			$feeSql = $this->merchantFeeSql($member, $merchant);
		} else if ($balanceAdminType === 'spendr') {
			$loadSql = $this->partnerLoadSql($dummy, $loadSubSql);
			$feeSql = $this->partnerFeeSql($member);
			$spendrTxnSql = $this->partnerSpendrTxnSql();
		} else if ($balanceAdminType === 'bank') {
			$feeSql = $this->bankFeeSql($member);
		} else if ($balanceAdminType === 'tern') {
			$feeSql = $this->ternFeeSql($member);
		} else if ($member->inTeams(SpendrBundle::getConsumerRoles())) {
			$loadSql = $this->normalLoadSql($dummy, $loadSubSql);
			$txnSql = $this->normalTxnSql($txnSubSql);
			$feeSql = $this->normalFeeSql($member);
		}

		$subSql .= ' (' . $feeSql . ')';

		if ($spendrTxnSql) {
			$subSql .= ' UNION (' . $spendrTxnSql . ')';
		}

		if ($loadSql) {
			$subSql .= ' UNION (' . $loadSql . ')';
		}

		if ($txnSql) {
			$subSql .= ' UNION (' . $txnSql . ')';
		}

		if ($type === 'count') {
			$sql = '
				SELECT count(*) as total
				FROM ( ' . $subSql . ' ) AS transactions;
			';
		} else {
			$sql = '
				SELECT *
				FROM ( ' . $subSql . ') AS transactions
				ORDER BY date_time desc;
			';
//			LIMIT '.($page - 1) * $pageSize.','.$pageSize.';
		}
		$query = Util::em()->getConnection()->executeQuery($sql);

		return $type === 'count' ? $query->fetchAssociative() : $query->fetchAllAssociative();
	}

	private function normalTxnSql($txnSubSql)
	{
		$txnTypeIdStr = $this->getTxnTypeIdStr();
		$txnStatusIdStr = $this->getTxnStatusIdStr();
		$txnSql = 'SELECT t.id as id, t.date_time as date_time, "transaction" as data_from
					FROM clf_transaction t
					WHERE t.type_id in (' . $txnTypeIdStr . ')
					AND t.status_id in (' . $txnStatusIdStr . ')
					' . $txnSubSql;
		return $txnSql;
	}

	private function normalLoadSql(UserCard $dummy, $loadSubSql)
	{
		$loadSql = 'SELECT ucl.id as id, ucl.load_at as date_time, "load" as data_from
					FROM user_card_load ucl
					INNER JOIN user_card uc ON ucl.user_card_id = uc.id
					JOIN card_program_card_type cpct ON uc.card_id = cpct.id
					JOIN card_program cp ON cpct.card_program_id = cp.id
					WHERE cp.name = "' . CardProgram::NAME_SPENDR . '"
					AND uc.id = ' . $dummy->getId() . '
					AND ucl.load_status IN (
						"' . UserCardLoad::LOAD_STATUS_LOADED . '",
						"' . UserCardLoad::LOAD_STATUS_ERROR . '"
					)
					' . $loadSubSql . '
					AND uc.deleted_at IS NULL';
		return $loadSql;
	}

	private function partnerLoadSql(UserCard $dummy, $loadSubSql)
	{
		$loadSql = 'SELECT ucl.id as id, ucl.load_at as date_time, "load" as data_from
					FROM user_card_load ucl
					INNER JOIN user_card uc ON ucl.user_card_id = uc.id
					JOIN card_program_card_type cpct ON uc.card_id = cpct.id
					JOIN card_program cp ON cpct.card_program_id = cp.id
					JOIN user_role ur ON ur.user_id = uc.user_id
					JOIN role r ON r.id = ur.role_id
					WHERE cp.name = "' . CardProgram::NAME_SPENDR . '"
					AND (
						uc.user_id = '.$dummy->getUser()->getId().'
						OR r.name = "' . Role::ROLE_SPENDR_CONSUMER . '"
						OR (
							r.name = "' . Role::ROLE_SPENDR_MERCHANT_ADMIN . '"
							AND ucl.meta like \'%"' . LoadService::LOAD_TYPE_MANUALLY . '":true%\'
						)
					)
					AND ucl.load_status IN (
						"' . UserCardLoad::LOAD_STATUS_LOADED . '",
						"' . UserCardLoad::LOAD_STATUS_ERROR . '"
					)
					' . $loadSubSql . '
					AND uc.deleted_at IS NULL';

		return $loadSql;
	}

	private function normalFeeSql(User $member)
	{
		$feeSql = 'SELECT ufh.id as id, ufh.time as date_time, "fee" as data_from
					FROM user_fee_history ufh WHERE ufh.user_id = ' . $member->getId();
		return $feeSql;
	}

	private function partnerFeeSql(User $member)
	{
		$feeSql = 'SELECT ufh.id as id, ufh.time as date_time, "fee" as data_from
					FROM user_fee_history ufh
					JOIN user_role ur ON ur.user_id = ufh.user_id
					JOIN role r ON r.id = ur.role_id
					WHERE ufh.user_id = ' . $member->getId()
			. ' OR (
					ufh.fee_name = "' . FeeService::TO_SPENDR_TRANSACTION_FEE . '"
					AND r.name = "' . Role::ROLE_SPENDR_MERCHANT_ADMIN . '"
			)'
			. ' OR (
					ufh.fee_name = "' . FeeService::TO_SPENDR_CONSUMER_ACH_RETURN_FEE . '"
					AND r.name = "' . Role::ROLE_SPENDR_CONSUMER . '"
			)'
			. ' OR (
					ufh.fee_name = "' . FeeService::TO_SPENDR_CONSUMER_MONTHLY_INACTIVITY_FEE . '"
					AND r.name = "' . Role::ROLE_SPENDR_CONSUMER . '"
			)'
            . ' OR (
					ufh.fee_name = "' . FeeService::TO_SPENDR_TIP_FEE . '"
					AND r.name = "' . Role::ROLE_SPENDR_MERCHANT_ADMIN . '"
			)'
		;
		return $feeSql;
	}

	private function ternFeeSql(User $member)
	{
		$feeSql = 'SELECT ufh.id as id, ufh.time as date_time, "fee" as data_from
					FROM user_fee_history ufh
					JOIN user_role ur ON ur.user_id = ufh.user_id
					JOIN role r ON r.id = ur.role_id
					WHERE ufh.user_id = ' . $member->getId()
			. ' OR (
					ufh.fee_name IN (
						"' . FeeService::TO_TERN_CONSUMER_LOAD_FEE . '",
						"' . FeeService::TO_TERN_TRANSACTION_FEE . '",
						"' . FeeService::TO_TERN_MERCHANT_WITHDRAWAL_FEE . '",
						"' . FeeService::TO_TERN_MERCHANT_MONTHLY_ON_FILE_FEE . '"
					)
					AND r.name = "' . Role::ROLE_SPENDR_PROGRAM_OWNER . '"
			)'
		;
		return $feeSql;
	}

	private function bankFeeSql(User $member)
	{
		$feeSql = 'SELECT ufh.id as id, ufh.time as date_time, "fee" as data_from
					FROM user_fee_history ufh
					JOIN user_role ur ON ur.user_id = ufh.user_id
					JOIN role r ON r.id = ur.role_id
					WHERE ufh.user_id = ' . $member->getId()
			. ' OR (
					ufh.fee_name IN (
						"' . FeeService::TO_BANK_CONSUMER_LOAD_FEE . '",
						"' . FeeService::TO_BANK_SPENDR_MONTHLY_ACCOUNT_FEE . '",
						"' . FeeService::TO_BANK_SPENDR_ACH_RETURN_FEE . '",
						"' . FeeService::TO_BANK_SPENDR_WIRE_FEE . '",
						"' . FeeService::TO_BANK_SPENDR_ACH_SETUP_FEE . '"
					)
					AND r.name = "' . Role::ROLE_SPENDR_PROGRAM_OWNER . '"
			)'
		;
		return $feeSql;
	}

	private function merchantFeeSql(User $member, SpendrMerchant $merchant)
	{
		$partner = UserService::getSpendrBalanceAdminAccount('spendr');
		$feeSql = 'SELECT ufh.id as id, ufh.time as date_time, "fee" as data_from
					FROM user_fee_history ufh
					INNER JOIN clf_transaction ct ON ct.id = ufh.entity_id
					WHERE ufh.user_id = ' . $member->getId()
			. ' OR (
					ufh.fee_name = "' . FeeService::TO_MERCHANT_REFUND_TRANSACTION_FEE . '"
					AND ct.merchant_id = ' . $merchant->getId() . '
					AND ufh.user_id = ' . $partner->getId() . '
					)';
		return $feeSql;
	}

	private function partnerSpendrTxnSql()
    {
		$sql = 'SELECT st.id as id, st.import_at as date_time, "spendr_transaction" as data_from
				FROM spendr_transaction st
				WHERE st.status = "' . SpendrTransaction::STATUS_COMPLETED . '"
				AND st.credit > 0
				AND (
					(st.description like "' . ImportTransactionsService::DESC_SPENDR_LLC_ACH . '%")
					OR (st.description like "' . ImportTransactionsService::DESC_SPENDR_INC_ACH . '%")
					OR (st.description like "Wire In%' . ImportTransactionsService::DESC_SPENDR_LLC . '")
					OR (st.description like "Wire In%' . ImportTransactionsService::DESC_SPENDR_INC . '")
					OR (st.meta like \'%"addAmountTo":"partner"%\')
				)
		';
		return $sql;
	}

	private function handleTransactionData($data, User $member)
	{
		if (
			!$data
			|| !is_array($data)
			|| !$data['id']
			|| !$data['data_from']
			|| $data['data_from'] !== 'transaction'
		) {
			return null;
		}

		/** @var Transaction $transaction */
		$transaction = Transaction::find($data['id']);

		if (!$transaction) {
			return null;
		}

		$typeName = $transaction->getType()->getName();
		$amount = $transaction->getAmount();
		$amountText = Money::format($amount, 'USD', false);

		if ($member->inTeams(SpendrBundle::getMerchantBalanceAdminRoles())) {
			if ($typeName === TransactionType::NAME_REFUND) {
				$amount = '-' . $amount;
				$amountText = '-' . $amountText;
			} else {
				$amountText = '+' . $amountText;
			}
		} else {
			if ($typeName === TransactionType::NAME_PURCHASE) {
				$amount = '-' . $amount;
				$amountText = '-' . $amountText;
			} else {
				$amountText = '+' . $amountText;
			}
		}

		$time = $transaction->getTxnTime() ?: $transaction->getDateTime();
		return [
			'Transaction' => $typeName === TransactionType::NAME_PURCHASE ? 'Transaction (Payment)' : 'Transaction (Refund)',
			'Date & Time' => Util::formatDateTime($time, Util::DATE_TIME_FORMAT, $this->tz),
			'Transaction ID' => $data['id'],
			'Amount Value' => $amount,
			'Amount' => $amountText,
			'timestamp' => $time ? $time->getTimestamp() : null
		];
	}

    private function handleTransactionTipData($data, User $member)
    {
        if (
            !$data
            || !is_array($data)
            || !$data['id']
            || !$data['data_from']
            || $data['data_from'] !== 'transaction'
        ) {
            return null;
        }

        /** @var Transaction $transaction */
        $transaction = Transaction::find($data['id']);

        if (!$transaction) {
            return null;
        }

        $tip = $transaction->getTip();
        if (!$tip || $tip->getStatus() !== SpendrTip::STATUS_PAID) {
            return null;
        }

        $amount = $tip->getAmount();
        $amountText = Money::format($amount, 'USD', false);

        if ($member->inTeams(SpendrBundle::getMerchantBalanceAdminRoles())) {
            $amountText = '+' . $amountText;
        } else {
            $amount = '-' . $amount;
            $amountText = '-' . $amountText;
        }

        $time = $tip->getPaidAt() ?: $tip->getCreatedAt();
        return [
            'Transaction' => 'Tipping',
            'Date & Time' => Util::formatDateTime($time, Util::DATE_TIME_FORMAT, $this->tz),
            'Transaction ID' => $tip->getId(),
            'Amount Value' => $amount,
            'Amount' => $amountText,
            'timestamp' => $time ? $time->getTimestamp() : null
        ];
    }

	private function handleLoadData($data, User $member, $balanceAdminType = null)
	{
		if (
			!$data
			|| !is_array($data)
			|| !$data['id']
			|| !$data['data_from']
			|| $data['data_from'] !== 'load'
		) {
			return null;
		}

		// https://app.asana.com/0/1200410643730426/1202358832761532/f
		if (Util::isLive() && ((int)($data['id']) === 417735)) {
			return null;
		}

		/** @var UserCardLoad $load */
		$load = UserCardLoad::find($data['id']);

		if (!$load) {
			return null;
		}

		$amount = $load->getInitialAmount();
		$amountText = Money::format($amount, 'USD', false);
		if ($load->getType() === UserCardLoad::TYPE_LOAD_CARD) {
			if ($balanceAdminType === 'spendr') {
				if (LoadService::isInstantLoad($load)) {
					if (Util::meta($load, 'errorByCheck')) {
						return null;
					} else {
						$amountText = '-' . $amountText;
						$amount = '-' . $amount;
					}
				} else if (LoadService::isRollbackLoad($load)) {
					$amountText = '-' . $amountText;
					$amount = '-' . $amount;
				} else if (
					LoadService::isPrefundLoad($load)
					&& $load->getUserCard()->getUser()->getId() === $member->getId()
				) { // partner load
					if ($load->getLoadStatus() === UserCardLoad::LOAD_STATUS_ERROR) {
						if (Util::meta($load, 'returnPrefundLoadFunds')) {
							$amountText = '+' . $amountText;
						} else {
							return null;
						}
					} else {
						$amountText = '+' . $amountText;
					}
				} else if (
					LoadService::isPromotionLoad($load)
					|| LoadService::isMerchantManuallyLoad($load)
				) {
					if (
						$load->getLoadStatus() === UserCardLoad::LOAD_STATUS_ERROR
						&& (Util::meta($load, 'oldData') || Util::meta($load, 'errorByCheck'))
					) {
						return null;
					} else {
						$amountText = '-' . $amountText;
						$amount = '-' . $amount;
					}
				} else if ( // partner unload failed
					LoadService::isLoadForUnloadFailed($load)
					&& $load->getUserCard()->getUser()->getId() === $member->getId()
				) {
					if ($load->getLoadStatus() === UserCardLoad::LOAD_STATUS_ERROR) {
						return null;
					} else {
						$amountText = '+' . $amountText;
					}
				} else {
					return null;
				}
			} else {
				if (
					LoadService::isPrefundLoad($load)
					&& !$load->getLoadAmount()
					&& !$load->getLoadAt()
				) {
					return null;
				} else if (
					LoadService::isPromotionLoad($load)
					&& !$load->getLoadAmount()
					&& !$load->getLoadAt()
				) {
					return null;
				} else if (
					LoadService::isInstantLoad($load)
					&& Util::meta($load, 'errorByCheck')
				) {
					return null;
				} else {
					$amountText = '+' . $amountText;
				}
			}
		} else if ($load->getType() === UserCardLoad::TYPE_UNLOAD) {
			if ($balanceAdminType === 'spendr') {
				if ($load->getUserCard()->getUser()->getId() === $member->getId()) { // partner unload
					if (LoadService::isUnloadForLoadFailed($load)) {
						if ($load->getLoadStatus() === UserCardLoad::LOAD_STATUS_LOADED) {
							$amountText = '-' . $amountText;
							$amount = '-' . $amount;
						} else {
							return null;
						}
					} else {
						$amountText = '-' . $amountText;
						$amount = '-' . $amount;
					}
				} else { // other(consumer) unload: load falied
					if (
						LoadService::isUnloadForLoadFailed($load)
						&& $load->getLoadStatus() === UserCardLoad::LOAD_STATUS_LOADED
					) {
						$isInstantCancelled = Util::meta($load, 'returnInstantLoadFundsToPartnerBalance');
						if ($isInstantCancelled) {
							$amountText = '+' . $amountText;
						} else {
							return null;
						}
					} else {
						return null;
					}
				}
			} else {
				$amountText = '-' . $amountText;
				$amount = '-' . $amount;
			}
		} else {
			return null;
		}

		if (LoadService::isPrefundLoad($load)) {
			$time = $load->getLoadAt() ?: $load->getReceivedAt() ?: $load->getInitializedAt() ?: $load->getCreatedAt();
		} else {
			$time = $load->getReceivedAt() ?: $load->getInitializedAt() ?: $load->getCreatedAt();
		}

		if ($load->getType() === UserCardLoad::TYPE_LOAD_CARD) {
			$txnType = 'Load';
			if (LoadService::isPrefundLoad($load)) {
				$txnType = 'Load (Prefund)';
			} else if (LoadService::isInstantLoad($load)) {
				$txnType = 'Load (Instant)';
			} else if (LoadService::isPromotionLoad($load)) {
				$txnType = 'Load (Promotion)';
				if ($load->getPromoCode()) {
					$txnType = 'Load (Promotion code)';
				} else if (Util::meta($load, 'promoType') === PromotionService::PROMOTION_TYPE_EARN) {
					$txnType = 'Load (Earn)';
				}
			} else if (LoadService::isMerchantManuallyLoad($load)) {
				$txnType = 'Load (Manually)';
			} else if (LoadService::isLoadForUnloadFailed($load)) {
				$txnType = 'Load (Unload failed)';
			}
		} else {
			$txnType = 'Unload';
			if (LoadService::isUnloadForLoadFailed($load)) {
				$txnType = 'Unload (Load failed)';

				$failedLoadId = Util::meta($load, 'Spendr load failed');
				$failedLoad = UserCardLoad::find($failedLoadId);
				if ($failedLoad) {
					if (LoadService::isInstantLoad($failedLoad)) {
						$txnType = 'Unload (Instant load failed)';
					} else if (LoadService::isPrefundLoad($failedLoad)) {
						$txnType = 'Unload (Prefund load failed)';
					}
				}

				if ($load->getPromoCode()) {
					$txnType = 'Unload (Promo code load failed)';
				} else if (Util::meta($load, 'promoType') === PromotionService::PROMOTION_TYPE_EARN) {
					$txnType = 'Unload (Earn load failed)';
				}
			}
		}

		return [
			'Transaction' => $txnType,
			'Date & Time' => Util::formatDateTime($time, Util::DATE_TIME_FORMAT, $this->tz),
			'Transaction ID' => $data['id'],
			'Amount Value' => $amount,
			'Amount' => $amountText,
			'timestamp' => $time ? $time->getTimestamp() : null
		];
	}

	private function handleFeeData($data, User $member, $balanceAdminType = null)
	{
		if (
			!$data
			|| !is_array($data)
			|| !$data['id']
			|| !$data['data_from']
			|| $data['data_from'] !== 'fee'
		) {
			return null;
		}

		/** @var UserFeeHistory $fee */
		$fee = $this->em->getRepository(UserFeeHistory::class)
			->find($data['id']);

		if (!$fee) {
			return null;
		}

		$amount = $fee->getAmount();
		$amountText = Money::format($amount, 'USD', false);

		if ($fee->getUser()->getId() === $member->getId()) {
			if (
				$balanceAdminType === 'spendr'
				&& $fee->getFeeName() === FeeService::TO_SPENDR_REFUND_MONTHLY_ACCOUNT_FEE
			) {
				$amountText = '+' . $amountText;
			} else {
				$amountText = '-' . $amountText;
				$amount = '-' . $amount;
			}
		} else {
			if (
				$member->inTeams(SpendrBundle::getMerchantBalanceAdminRoles())
				&& ($fee->getFeeName() === FeeService::TO_MERCHANT_REFUND_TRANSACTION_FEE)
			) {
				$amountText = '+' . $amountText;
			} else if ($balanceAdminType === 'spendr') {
				if (in_array($fee->getFeeName(), FeeService::spendrFeeNames())) {
					$amountText = '+' . $amountText;
				} else {
					return null;
				}
			} else if ($balanceAdminType === 'tern') {
				if (in_array($fee->getFeeName(), FeeService::ternFeeNames())) {
					$amountText = '+' . $amountText;
				} else {
					return null;
				}
			} else if ($balanceAdminType === 'bank') {
				if (in_array($fee->getFeeName(), FeeService::bankFeeNames())) {
					$amountText = '+' . $amountText;
				} else if ($fee->getFeeName() === FeeService::TO_SPENDR_REFUND_MONTHLY_ACCOUNT_FEE) {
					$amountText = '-' . $amountText;
					$amount = '-' . $amount;
				} else {
					return null;
				}
			} else {
				return null;
			}
		}

		$time = $fee->getTime();
		return [
			'Transaction' => $fee->getFeeName(),
			'Date & Time' => Util::formatDateTime($time, Util::DATE_TIME_FORMAT, $this->tz),
			'Transaction ID' => $data['id'],
			'Amount Value' => $amount,
			'Amount' => $amountText,
			'timestamp' => $time ? $time->getTimestamp() : null
		];
	}

	private function handleSpendrTransactionData($data, User $member)
	{
		if (
			!$data
			|| !is_array($data)
			|| !$data['id']
			|| !$data['data_from']
			|| $data['data_from'] !== 'spendr_transaction'
		) {
			return null;
		}

		/** @var SpendrTransaction $st */
		$st = $this->em->getRepository(SpendrTransaction::class)
			->find($data['id']);

		if (!$st) {
			return null;
		}

		$description = $st->getDescription();
		$isPartnerLoad = ImportTransactionsService::checkIfPartnerLoadDesc($description, 'all') ? true : false;
		$isAchBatch = ImportTransactionsService::getAchBatch($description) ? true : false;

        $isCorrectData = Util::meta($st, 'addAmountTo') === 'partner';

		$amount = $st->getCredit();
		$amountText = Money::format($amount, 'USD', false);

		$title = null;
		if ($isPartnerLoad) {
			$title = ' (Partner Load)';
		} else if ($isAchBatch) {
			$title = ' (' . $description . ')';
			$meta = Util::meta($st);
			if (
				isset($meta['havePrefundLoads'])
				&& isset($meta['prefundLoadAmount'])
				&& ($meta['prefundLoadAmount'] > 0)
			) {
				$amount = $amount - $meta['prefundLoadAmount'];
				$amountText = Money::format($amount, 'USD', false);
			}
		} else if ($isCorrectData) {
            $title = ' (Correct - Partner Load)';
		} else {
            return null;
        }
		$amountText = '+' . $amountText;
		$time = $st->getImportAt();
		return [
			'Transaction' => 'Bank Ledger ' . $title,
			'Date & Time' => Util::formatDateTime($time, Util::DATE_TIME_FORMAT, $this->tz),
			'Transaction ID' => $data['id'],
			'Amount Value' => $amount,
			'Amount' => $amountText,
			'timestamp' => $time ? $time->getTimestamp() : null
		];
	}

	private function handlePendingBalanceData()
	{
		return UserCardService::getPendingBalanceData('all', 'runningBalance');
	}

	private function getTxnTypeIdStr()
	{
		$types = TransactionType::gets([
			TransactionType::NAME_PURCHASE,
			TransactionType::NAME_REFUND,
		]);
		$ids = [];
		/** @var TransactionType $t */
		foreach ($types as $t) {
			$ids[] = $t->getId();
		}
		$idStr = implode(',', $ids);

		return $idStr;
	}

	private function getTxnStatusIdStr()
	{
		$status = TransactionStatus::gets([
			TransactionStatus::NAME_COMPLETED,
//			TransactionStatus::NAME_CANCELLED,
//			TransactionStatus::NAME_PENDING,
		]);
		$ids = [];
		/** @var TransactionStatus $s */
		foreach ($status as $s) {
			$ids[] = $s->getId();
		}
		$idStr = implode(',', $ids);

		return $idStr;
	}

	protected function validateUser(User $user = null)
	{
		if (
			!$user
//			|| !$user->inTeams(SpendrBundle::getConsumerRoles())
		) {
			throw PortalException::temp('Unknown user!');
		}
	}
}
