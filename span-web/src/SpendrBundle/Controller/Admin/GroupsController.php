<?php


namespace SpendrB<PERSON>le\Controller\Admin;

use Clf<PERSON><PERSON>le\Entity\Merchant;
use Clf<PERSON><PERSON>le\Entity\MerchantStatus;
use CoreBundle\Entity\Role;
use CoreBundle\Response\DeniedResponse;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Services\UserService;
use CoreBundle\Utils\QueryListParams;
use Doctrine\ORM\QueryBuilder;
use SalexUserBundle\Entity\User;
use SpendrBundle\Entity\SpendrGroup;
use SpendrBundle\Entity\SpendrMerchant;
use SpendrBundle\Services\GroupService;
use SpendrBundle\Services\MerchantService;
use SpendrBundle\SpendrBundle;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class GroupsController extends BaseController
{
	public function __construct()
	{
		parent::__construct();

		$this->authAdminsWithoutBank();
	}

	/**
	 * @Route("/admin/spendr/groups/list/{page}/{limit}", methods={"GET"}, defaults={"page" = 1,"limit" = 10})
	 * @param Request $request
	 * @param int $page
	 * @param int $limit
	 *
	 * @return SuccessResponse
	 * @throws \Doctrine\ORM\NoResultException
	 * @throws \Doctrine\ORM\NonUniqueResultException
	 */
    public function search(Request $request, $page = 1, $limit = 10)
    {
        $resp = $this->traitSearch($request, [], $page, $limit);
        $result = $resp->getResult();

        $query = $this->query($request);
        $total = (clone $query)->select('count(distinct g)')
            ->getQuery()
            ->getSingleScalarResult();

        $result['quick'] = [
            'total' => (int)$total
        ];

        return new SuccessResponse($result);
    }

    protected function query(Request $request)
    {
		return $this->em->getRepository(SpendrGroup::class)
			->createQueryBuilder('g')
            ->leftJoin('g.adminUser', 'u');
    }

    protected function queryListParams(QueryBuilder $query, Request $request)
    {
        $params = new QueryListParams($query, $request, 'g', 'count(distinct g)');
        $params->distinct = true;
        $params->orderBy = [
            'g.id' => 'desc',
        ];
        $params->searchFields = [
            'g.id',
            'g.name',
            'u.firstName',
            'u.lastName',
            'u.email',
        ];
        return $params;
    }

    /**
     * @param SpendrGroup $entity
     *
     * @return array
     */
    protected function getRowData($entity)
    {
        $data = $entity->toApiArray();
        $data['Merchants'] = count($entity->getMerchants());
        return $data;
    }

	/**
	 * @Route("/admin/spendr/groups/{group}/detail")
	 *
	 * @param SpendrGroup $group
	 * @return SuccessResponse
	 */
    public function detailAction(SpendrGroup $group)
    {
        $data = $this->getRowData($group);

        $data['merchantList'] = MerchantService::listMerchantsForSelection($group);
        $groupMerchants = $group->getMerchants();
        $data['groupMerchantIds'] = [];
        if ($groupMerchants) {
            foreach ($groupMerchants as $gm) {
                $data['groupMerchantIds'][] = $gm->getId();
            }
        }

        return new SuccessResponse($data);
    }

	/**
	 * @Route("/admin/spendr/groups/save", methods={"POST"})
	 * @param Request $request
	 *
	 * @return SuccessResponse|DeniedResponse
	 * @throws \CoreBundle\Exception\FailedException
	 */
    public function saveAction(Request $request)
    {
		if ($this->user->inTeams([
			Role::ROLE_SPENDR_CUSTOMER_SUPPORT,
			Role::ROLE_SPENDR_ACCOUNTANT
		])) {
			return new DeniedResponse();
		}

		$group = GroupService::saveDetails($request);
        return new SuccessResponse($this->getRowData($group));
    }

	/**
     * Active,Inactive
     * 
	 * @Route("/admin/spendr/groups/{group}/toggle-status", methods={"POST"})
	 * @param Request $request
	 * @param SpendrGroup $group
	 * @return SuccessResponse|DeniedResponse
	 */
    public function toggleStatusAction(Request $request, SpendrGroup $group)
    {
		$this->authAdminsWithoutBank();
		if ($this->isViewOnlyRole()) {
			return new DeniedResponse();
		}

		$status = $request->get('Status', 'Active');
        $oldStatus = $group->getStatus();
        if ($status !== $oldStatus) {
            $group->setStatus($status)
            ->persist();

            $user = $group->getAdminUser();
            $user->addNote('Change group status from ' . $oldStatus . ' to ' . $status . '.', true, $this->user->getId(), $this->user);
            
            if ($status === 'Inactive') {
                $this->changeMerchantStatus($group, $status);
            }
        }

        return new SuccessResponse($this->getRowData($group));
    }

    private function changeMerchantStatus(SpendrGroup $group, $status)
    {
        $merchants = $this->em()->getRepository(SpendrMerchant::class)
        ->createQueryBuilder('sm')
        ->where('sm.group = :group')
        ->setParameter('group', $group)
        ->getQuery()
        ->getResult();

        if ($merchants) {
            /** @var SpendrMerchant $merchant */
            foreach ($merchants as $merchant) {
                $oldStatus = $merchant->getStatus()->getName();
                if ($oldStatus !== $status) {
                    $newStatus = MerchantStatus::get($status);
                    $merchant->setStatus($newStatus)
                    ->persist();
    
                    $user = $merchant->getAdminUser();
                    $user->addNote('Change location status from ' . $oldStatus . ' to ' . $status . '.', true, $this->user->getId(), $this->user);
                }
            }
        }
    }

    /**
     * @Route("/admin/spendr/groups/{user}/resend-invitation", methods={"POST"})
     * @param Request $request
     * @param User    $user
     *
     * @return SuccessResponse|DeniedResponse
     */
    public function resendInvitation(Request $request, User $user)
    {
		$this->authAdminsWithoutBank();
		if ($this->isViewOnlyRole()) {
			return new DeniedResponse();
		}

		UserService::sendResetPasswordEmail($user);

        return new SuccessResponse();
    }

	/**
	 * @Route("/admin/spendr/groups/export", methods={"POST"})
	 * @param Request $request
	 *
	 * @return SuccessResponse|DeniedResponse
	 */
    public function export(Request $request)
    {
		if (SpendrBundle::notAllowedExport()) {
			return new DeniedResponse();
		}

        $fields = [
            'ID'                   => 20,
            'Name'                 => 30,
            'Admin ID'             => 20,
            'Contact Name'         => 20,
            'Contact Email'        => 30,
            'Contact Phone'        => 15,
            'Status'               => 18
        ];
        return $this->commonExport($request, 'Spendr_Groups', $fields);
    }

    /**
	 * @Route("/admin/spendr/groups/merchant-list")
	 *
	 * @param Request $request
	 * @return SuccessResponse|FailedResponse
	 */
    public function merchantList()
    {       
        $merchants = MerchantService::listMerchantsForSelection(null, true);

        if (!$merchants) {
            return new FailedResponse('No merchants.');
        }

        return new SuccessResponse($merchants);
    }
}
