<?php


namespace SpendrBundle\Controller\Admin;


use CoreBundle\Response\DeniedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\QueryListParams;
use CoreBundle\Utils\Util;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\QueryBuilder;
use SalexUserBundle\Entity\User;
use SpendrBundle\Entity\IdentityLog;
use SpendrBundle\SpendrBundle;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class IdentityReportController extends BaseController
{
    public $member = null;

    public function __construct()
    {
        parent::__construct();

        $this->authAdminsWithoutBank();
    }

    /**
     * @Route("/admin/spendr/identity-report/list/{page}/{limit}", methods={"GET"}, defaults={"page" = 1,"limit" = 10})
     * @param Request $request
     * @param int $page
     * @param int $limit
     *
     * @return DeniedResponse|SuccessResponse
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function search(Request $request, $page = 1, $limit = 10)
    {
        $memberId = $request->get('memberID');
        if (!$memberId) {
            return new DeniedResponse();
        }
        $member = User::find($memberId);
        if (!$member || !$member->inTeams(SpendrBundle::getConsumerRoles())) {
            return new DeniedResponse();
        }
        $this->member = $member;
        $resp = $this->traitSearch($request, [], $page, $limit);
        $result = $resp->getResult();

        $query = $this->query($request);
        $total = (clone $query)->select('count(distinct i)')
            ->getQuery()
            ->getSingleScalarResult();
        $result['quick'] = [
            'total' => (int)$total
        ];
        return new SuccessResponse($result);
    }

    protected function query(Request $request)
    {
        return $this->em->getRepository(IdentityLog::class)
            ->createQueryBuilder('i')
            ->where('i.user = :user')
            ->setParameter('user', $this->member);
    }

    protected function queryListParams(QueryBuilder $query, Request $request)
    {
        $params = new QueryListParams($query, $request, 'i', 'count(distinct i)');
        $params->distinct = true;
        $params->orderBy = [
            'i.id' => 'desc',
        ];
        return $params;
    }

    /**
     * @param IdentityLog $entity
     * @return array
     */
    protected function getRowData($entity)
    {
        return [
            'ID' => $entity->getId(),
            'Type' => $entity->getType(),
            'Description' => $entity->getDescription(),
            'Date' => Util::formatDateTime($entity->getCreatedAt(), Util::DATE_TIME_FORMAT, $this->tz)
        ];
    }
}
