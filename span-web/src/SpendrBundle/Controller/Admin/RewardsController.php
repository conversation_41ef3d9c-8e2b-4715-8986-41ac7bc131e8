<?php


namespace Spendr<PERSON><PERSON>le\Controller\Admin;


use Carbon\Carbon;
use CoreBundle\Entity\Role;
use CoreBundle\Response\DeniedResponse;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\QueryListParams;
use CoreBundle\Utils\Util;
use Doctrine\ORM\AbstractQuery;
use Doctrine\ORM\QueryBuilder;
use PortalBundle\Exception\PortalException;
use SpendrBundle\Entity\PromotionCode;
use SpendrBundle\Services\UserService;
use SpendrBundle\SpendrBundle;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class RewardsController extends BaseController
{
    public function __construct()
    {
        parent::__construct();

        $this->authSuperAdminOrAdmins(
            [
                Role::ROLE_SPENDR_PROGRAM_OWNER,
                Role::ROLE_SPENDR_COMPLIANCE,
                Role::ROLE_SPENDR_ACCOUNTANT
            ]
        );
    }

    /**
     * @Route("/admin/spendr/rewards/list/{page}/{limit}", methods={"GET"}, defaults={"page" = 1,"limit" = 10})
     * @param Request $request
     * @param int $page
     * @param int $limit
     *
     * @return SuccessResponse
     * @throws \Doctrine\ORM\NoResultException
     * @throws \Doctrine\ORM\NonUniqueResultException
     */
    public function search(Request $request, $page = 1, $limit = 10)
    {
        $resp = $this->traitSearch($request, [], $page, $limit);
        $result = $resp->getResult();

        $query = $this->query($request);
        $total = (clone $query)->where('c.status = :status')
            ->setParameter('status', PromotionCode::STATUS_ACTIVE)
            ->select('count(distinct c)')
            ->getQuery()
            ->getSingleScalarResult();
        $row = (clone $query)->select('sum(c.budget) budget, sum(c.redeem) redeem, sum(c.promoCodeAmount * c.redeem) redeem_total')
            ->getQuery()
            ->getSingleResult(AbstractQuery::HYDRATE_ARRAY);

        $result['quick'] = [
            'total'  => (int)$total,
            'budget' => (int)$row['budget'],
            'redeem' => (int)$row['redeem'],
            'redeem_total' => (int)$row['redeem_total'],
            'avg_redeem' => $row['redeem'] ? round($row['redeem_total'] / $row['redeem']) : 0
        ];
        return new SuccessResponse($result);
    }

    protected function query(Request $request)
    {
        return $this->em->getRepository(PromotionCode::class)
            ->createQueryBuilder('c');
    }

    protected function queryListParams(QueryBuilder $query, Request $request)
    {
        $params = new QueryListParams($query, $request, 'c', 'count(distinct c)');
        $params->distinct = true;
        $params->orderBy = [
            'c.id' => 'desc',
        ];
        $params->searchFields = [
            'c.id',
            'c.promoCode',
        ];
        return $params;
    }

    /**
     * @Route("/admin/spendr/rewards/filters")
     * @return SuccessResponse
     */
    public function searchFiltersAction()
    {
        $all = [
            'rewardStatuses' => [
                PromotionCode::STATUS_ACTIVE,
                PromotionCode::STATUS_STOPPED,
                PromotionCode::STATUS_COMPLETED
            ]
        ];
        $data = Util::allToFilterOptions($all, [
            'rewardStatuses' => null,
        ]);
        return new SuccessResponse($data);
    }

    /**
     * @param PromotionCode $entity
     * @return array
     */
    protected function getRowData($entity)
    {
        $status = $entity->getStatus();
        $started = Carbon::now()->isAfter($entity->getStartAt());
        $ended = Carbon::now()->isAfter($entity->getEndAt());
        $totalRedeemed = $entity->getRedeem() * $entity->getPromoCodeAmount();
        if ($status == PromotionCode::STATUS_ACTIVE && !$started) {
            $status = 'pending';
        }
        if ($status != PromotionCode::STATUS_COMPLETED && ($totalRedeemed >= $entity->getBudget() || $ended)) {
            $status = PromotionCode::STATUS_COMPLETED;
            $entity->setStatus($status)->persist();
        }
        return [
            'Promo Code ID' => $entity->getId(),
            'Campaign Name' => $entity->getName(),
            'Budget' => $entity->getBudget() / 100,
            'Campaign Budget' => Money::format($entity->getBudget(), 'USD', false),
            'Campaign Description' => $entity->getDescription(),
            'Promo Code' => $entity->getPromoCode(),
            'Amount' => $entity->getPromoCodeAmount() / 100,
            'Promo Amount' => Money::format($entity->getPromoCodeAmount(), 'USD', false),
            '# Of Redemption' => $entity->getRedeem(),
            'Total $ Redeemed' => Money::format($totalRedeemed, 'USD', false),
            'Begin Date' => Util::formatDateTime($entity->getStartAt()),
            'End Date' => Util::formatDateTime($entity->getEndAt()),
            'Status' => $status,
            'Started' => $started,
            'Ended' => Carbon::now()->isAfter($entity->getEndAt())
        ];
    }

    /**
     * @Route("/admin/spendr/rewards/{promo}/detail")
     * @param PromotionCode $promo
     *
     * @return SuccessResponse
     * @throws PortalException
     */
    public function detailAction(PromotionCode $promo)
    {
        if (!$promo) {
            throw PortalException::temp('Unknown Promo Code ID!');
        }
        $data = $this->getRowData($promo);
        return new SuccessResponse($data);
    }

    /**
     * @Route("/admin/spendr/rewards/save", methods={"POST"})
     * @param Request $request
     *
     * @return SuccessResponse|FailedResponse
     * @throws PortalException
     */
    public function saveAction(Request $request)
    {
        if ($this->user->inTeams([Role::ROLE_SPENDR_ACCOUNTANT])) {
            return new DeniedResponse();
        }
        $pid = $request->get('Promo Code ID');
        $code = $request->get('Promo Code');
        $budget = $request->get('Campaign Budget') * 100;
        $amount = $request->get('Promo Amount', 0.01) * 100;
        $begin = Util::toUTC(Util::timeLocal($request->get('Begin Date')));
        $end = Util::toUTC(Util::timeLocal($request->get('End Date')));
        if ($amount > 1000 * 100) {
            return new FailedResponse('The promo code amount should be less than $1000');
        }
        $partnerDummy = UserService::getSpendrBalanceDummyCard('spendr');
        if ($partnerDummy && $partnerDummy->getBalance() < $budget) {
            return new FailedResponse('Partner balance is less than Campaign Budge');
        }
        if ($budget < $amount || intval($budget / $amount) * $amount != $budget) {
            return new FailedResponse('Campaign Budget must be a multiple of Promo Amount');
        }
        if (!$begin->isBefore($end)) {
            return new FailedResponse('Start Date Should be before End Date');
        }
        if (Carbon::now()->isAfter($end)) {
            return new FailedResponse('End Date should be later than now');
        }
        $partnerDummy = UserService::getSpendrBalanceDummyCard('spendr');
        if (!$partnerDummy || $partnerDummy->getBalance() < $budget) {
            return new FailedResponse('Partner balance is less than Compaign Budget');
        }
        if ($pid) {
            /** @var PromotionCode $promo */
            $promo = Util::find(PromotionCode::class, $pid);
            if (!$promo) {
                throw PortalException::temp('Unknown Promo Code ID!');
            }
            if ($promo->getStatus() === PromotionCode::STATUS_COMPLETED || Carbon::now()->isAfter($promo->getEndAt())) {
                return new FailedResponse('This promo code has completed and can\'t edit');
            }
            if ($promo->getRedeem() * $promo->getPromoCodeAmount() >= $budget) {
                return new FailedResponse('The current redeemed has reached the new Campaign Budget');
            }
        } else {
            if (Util::em()->getRepository(PromotionCode::class)
                ->createQueryBuilder('p')
                ->where('p.promoCode = :code')
                ->andWhere('p.status != :status')
                ->setParameter('code', $code)
                ->setParameter('status', PromotionCode::STATUS_COMPLETED)
                ->select('count(p)')
                ->getQuery()
                ->getSingleScalarResult()) {
                throw PortalException::temp('Duplicated data with the same promo code!');
            }
            $promo = new PromotionCode();
            $promo->setRedeem(0)
                ->setPromoCode($code)
                ->setPromoCodeAmount($amount)
                ->setStatus(PromotionCode::STATUS_ACTIVE);
        }
        $promo->setName($request->get('Campaign Name'))
            ->setDescription($request->get('Campaign Description'))
            ->setBudget($budget)
            ->setStartAt($begin)
            ->setEndAt($end)
            ->persist();

        return new SuccessResponse($this->getRowData($promo));
    }

    /**
     * @Route("/admin/spendr/rewards/{promo}/toggle-status", methods={"POST"})
     * @param Request $request
     * @param PromotionCode $promo
     * @return DeniedResponse|SuccessResponse
     */
    public function toggleStatusAction(Request $request, PromotionCode $promo)
    {
        if ($this->user->inTeam(Role::ROLE_SPENDR_ACCOUNTANT)) {
            return new DeniedResponse();
        }
        $status = $request->get('Status', 'active') === 'active'
            ? PromotionCode::STATUS_ACTIVE
            : PromotionCode::STATUS_STOPPED;
        $promo->setStatus($status)
            ->persist();
        return new SuccessResponse($this->getRowData($promo));
    }

    /**
     * @Route("/admin/spendr/rewards/export", methods={"POST"})
     * @param Request $request
     *
     * @return SuccessResponse|DeniedResponse
     */
    public function export(Request $request)
    {
		if (SpendrBundle::notAllowedExport()) {
			return new DeniedResponse();
		}
        $fields = [
            'Promo Code ID' => 10,
            'Campaign Name' => 20,
            'Campaign Budget' => 20,
            'Promo Code' => 30,
            'Promo Amount' => 20,
            '# Of Redemption' => 20,
            'Total $ Redeemed' => 20,
            'Begin Date' => 20,
            'End Date' => 20,
            'Status' => 20
        ];
        return $this->commonExport($request, 'Spendr Promo Codes', $fields);
    }
}
