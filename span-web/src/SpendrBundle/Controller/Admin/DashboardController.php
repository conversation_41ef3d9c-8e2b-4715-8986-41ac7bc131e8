<?php


namespace Spendr<PERSON><PERSON>le\Controller\Admin;


use Clf<PERSON><PERSON>le\Entity\Transaction;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\CardProgramCardType;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserCard;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Services\SSLEncryptionService;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Util;
use Ramsey\Uuid\Uuid;
use SalexUserBundle\Entity\User;
use SalexUserBundle\Entity\UserConfig;
use SpendrBundle\Entity\SpendrMerchant;
use SpendrBundle\Services\LoadService;
use SpendrBundle\Services\MerchantService;
use SpendrBundle\Services\PlaidService;
use SpendrBundle\Services\UserService;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class DashboardController extends BaseController
{
	/**
	 * @Route("/admin/spendr/dashboard/top-five-merchants", methods={"GET"})
	 * @param Request $request
	 * @return SuccessResponse
	 */
	public function topFiveMerchants(Request $request)
	{
		$query = $this->em->getRepository(Transaction::class)
			->createQueryBuilder('t')
			->join('t.merchant', 'm')
			->join('m.adminUser', 'u')
			->join('u.teams', 'r')
			->where('r.name = :role')
			->setParameter('role', Role::ROLE_SPENDR_MERCHANT_ADMIN);
		$query = $this->queryByDateRange($query, 't.dateTime', $request);
		$res = $query->select('sum(t.amount) as totalAmount, count(distinct t) as total, m.name as merchantName')
			->groupBy('m.id')
			->orderBy('totalAmount', 'desc')
			->setMaxResults(5)
			->getQuery()
			->getResult();

		$data = [];
		if ($res) {
			foreach ($res as $re) {
				$data['key'][] = $re['merchantName'];
				$data['data'][] = $re['totalAmount'] / 100;
				$data['count'][] = $re['total'];
			}
		}
		return new SuccessResponse($data);
	}

	/**
	 * @Route("/admin/spendr/dashboard/top-five-locations", methods={"GET"})
	 * @param Request $request
	 * @return SuccessResponse
	 */
	public function topFiveLocations(Request $request)
	{
        $query = $this->em->getRepository(Transaction::class)
			->createQueryBuilder('t')
			->join('t.merchant', 'm')
			->join('t.location', 'l')
			->join('m.adminUser', 'u')
			->join('u.teams', 'r')
			->where('r.name = :role')
			->setParameter('role', Role::ROLE_SPENDR_MERCHANT_ADMIN);
        $query = $this->queryByDateRange($query, 't.dateTime', $request);
		$res = $query->select('sum(t.amount) as totalAmount, count(distinct t) as total, l.name as locationName')
			->groupBy('l.id')
			->orderBy('totalAmount', 'desc')
			->setMaxResults(5)
			->getQuery()
			->getResult();

		$data = [];
		if ($res) {
			foreach ($res as $re) {
				$data['key'][] = $re['locationName'];
				$data['data'][] = $re['totalAmount'] / 100;
				$data['count'][] = $re['total'];
			}
		}
		return new SuccessResponse($data);
	}

	/**
	 * @Route("/admin/spendr/dashboard/partner-load-unload")
	 *
	 * @param Request $request
	 * @return FailedResponse|SuccessResponse
	 * @throws \PortalBundle\Exception\PortalException
	 */
	public function partnerLoad(Request $request)
	{
		$this->authSuperAdminOrAdmins([
			Role::ROLE_SPENDR_PROGRAM_OWNER
		]);

		$type = $request->get('type');
		$amount = $request->get('amount');
		if (!$type || !$amount) {
			return new FailedResponse('Invalid parameter.');
		}

		$res = new SuccessResponse();

		if ($type === 'Load') {
			$res = LoadService::partnerLoad($request, $this->user);
		} else if ($type === 'Unload') {
			$res = LoadService::partnerUnload($request, $this->user);
		}

		return $res;
	}

	/**
	 * @Route("/admin/spendr/dashboard/partner/plaid/create-link-token")
	 *
	 * @return SuccessResponse|FailedResponse
	 * @throws \PortalBundle\Exception\DeniedException
	 * @throws \PortalBundle\Exception\PortalException
	 */
	public function createLinkToken()
	{
		$this->authSuperAdminOrAdmins([
			Role::ROLE_SPENDR_PROGRAM_OWNER
		]);

		$partner = UserService::getSpendrBalanceAdminAccount('spendr');
		$config = $partner->ensureConfig();
		if ($config && $config->getBankAccountId()) {
			$accountId = $config->getBankAccountId();
		} else {
			$accountId = Uuid::uuid4();
			$config->setBankAccountId($accountId)
				->setPlatform(UserConfig::PLATFORM_SPENDR)
				->setReferCode(Uuid::uuid4())
				->persist();
		}
		$webhook = PlaidService::getPartnerWebhook();
        $accessToken = null;
		$pending = UserService::getUserPendingCard($partner);
        if ($pending) {
            $accessToken = $pending->getPlaidAccessToken();
        }
		$token = PlaidService::curlLinkToken(
		    $accountId,
            null,
            $accessToken,
            $webhook
        );
		if (!$token || !isset($token->link_token)) {
			return new FailedResponse('Failed to create link token.', (array)$token);
		}
		$token->env = PlaidService::getEnv();
		$token->product = PlaidService::getProduct($accessToken != null,true);
        $token->webhook = $webhook;
		return new SuccessResponse($token);
	}

	/**
	 * @Route("/admin/spendr/dashboard/partner/plaid/exchange-access-token", methods={"POSt"})
	 *
	 * @param Request $request
	 * @return SuccessResponse|FailedResponse
	 * @throws \PortalBundle\Exception\DeniedException
	 * @throws \PortalBundle\Exception\PortalException
	 */
	public function exchangeAccessToken(Request $request)
	{
		$this->authSuperAdminOrAdmins([
			Role::ROLE_SPENDR_PROGRAM_OWNER
		]);

		$publicToken = $request->get('publicToken');
		if (!$publicToken) {
			return new FailedResponse('Invalid parameter.');
		}
		$token = PlaidService::exchangeAccessToken($publicToken);
		if (!$token) {
			return new FailedResponse('Failed to exchange access token.');
		}

		$partner = UserService::getSpendrBalanceAdminAccount('spendr');

        // If the bank account is manually linked, we should save the account_id and access_token
        $linkData = $request->get('data');
        if ($linkData && $linkData['account']) {
            $account = $linkData['account'];
            if ($account['verification_status'] == PlaidService::VERIFY_STATUS_PENDING ||
                $account['verification_status'] == PlaidService::VERIFY_STATUS_MANUAL_PENDING) {
                $uc = new UserCard();
                $uc->setUser($partner)
                    ->setCard(CardProgramCardType::getForCardProgram(CardProgram::spendr()))
                    ->setStatus(UserCard::STATUS_PENDING)
                    ->setType(UserCard::LL_TYPE_PLAID)
                    ->setPlaidAccessToken($token->access_token)
                    ->setMeta(Util::j2s([
                        'account_id' => $account['id'],
                        'item_id' => $token->item_id,
                        'name' => $account['name'],
                        'mask' => $account['mask'],
                        'verification_status' => $account['verification_status']
                    ]))
                    ->persist();

                return new SuccessResponse(
                    [
                        'bankName' => '',
                        'accountNum' => '',
                        'routing' => '',
                        'pendingCard' => true
                    ],
                    'Your bank account is pending micro-deposits verification. Click on the \'Verify Manual Card\' in 1-2 business days to complete linking to Spendr.'
                );
            } elseif ($account['verification_status'] == PlaidService::VERIFY_STATUS_MANUAL_VERIFIED) {
                $uc = UserService::getUserPendingCard($partner);
                if (!$uc) {
                    return new FailedResponse('Your bank account is not existed.');
                }
                $data = UserService::saveCardInfo($partner, $token->access_token, $uc);
                if (is_string($data)) {
                    return new FailedResponse($data);
                }

                return new SuccessResponse($data);
            } elseif ($account['verification_status']) {
                return new FailedResponse($account['verification_status']);
            }
        }

		$data = UserService::saveCardInfo($partner, $token->access_token);
		if ($data && is_string($data)) {
			return new FailedResponse($data);
		}

		return new SuccessResponse($data);
	}
}
