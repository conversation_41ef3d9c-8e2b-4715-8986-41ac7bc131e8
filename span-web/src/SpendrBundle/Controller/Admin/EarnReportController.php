<?php


namespace SpendrBundle\Controller\Admin;


use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Response\DeniedResponse;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\QueryListParams;
use CoreBundle\Utils\Queue;
use CoreBundle\Utils\Util;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\QueryBuilder;
use PortalBundle\Exception\PortalException;
use SpendrBundle\Services\LoadService;
use SpendrBundle\Services\PromotionService;
use SpendrBundle\SpendrBundle;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class EarnReportController extends BaseController
{
    public function __construct()
    {
        parent::__construct();

        $this->authAdminsWithoutBank();
    }

    /**
     * @Route("/admin/spendr/earn-report/list/{page}/{limit}", methods={"GET"}, defaults={"page" = 1,"limit" = 10})
     * @param Request $request
     * @param int $page
     * @param int $limit
     *
     * @return SuccessResponse
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function search(Request $request, $page = 1, $limit = 10)
    {
        $resp = $this->traitSearch($request, [
            'createdAt',
        ], $page, $limit);
        $result = $resp->getResult();
        $query = $this->query($request);
        $quick = (clone $query)->andWhere('ucl.type = :type')
            ->andWhere('ucl.loadStatus != :status')
            ->setParameter('type', UserCardLoad::TYPE_LOAD_CARD)
            ->setParameter('status', UserCardLoad::LOAD_STATUS_ERROR)
            ->select('count(distinct ucl) total, sum(ucl.initialAmount) totalAmount')
            ->distinct()
            ->getQuery()
            ->getSingleResult();
        $result['quick'] = [
            'total'  => $quick['total'],
            'earn_total' => $quick['totalAmount'],
            'revoke_total' => (int)((clone $query)->andWhere('ucl.type = :type')
                ->andWhere('ucl.loadStatus = :status')
                ->setParameter('type', UserCardLoad::TYPE_UNLOAD)
                ->setParameter('status', UserCardLoad::LOAD_STATUS_LOADED)
                ->select('sum(ucl.initialAmount)')
                ->getQuery()
                ->getSingleScalarResult()),
            'earn_avg' => $quick['total'] ? round($quick['totalAmount'] / $quick['total']) : 0
        ];
        return new SuccessResponse($result);
    }

    protected function query(Request $request)
    {
        $query = $this->em->getRepository(UserCardLoad::class)
            ->createQueryBuilder('ucl')
            ->join('ucl.userCard', 'c')
            ->join('c.user', 'u')
            ->join('c.card', 'cpct')
            ->where('ucl.promoType = :promoType')
            ->andWhere(Util::expr()->eq('cpct.cardProgram',':cardProgram'))
            ->setParameter('promoType', PromotionService::PROMOTION_TYPE_EARN)
            ->setParameter('cardProgram', $this->cardProgram);
        $earnType = $request->get('earnType', 'All');
        if (in_array($earnType, [
            UserCardLoad::EARN_TYPE_BANK_LINK,
            UserCardLoad::EARN_TYPE_SPEND_OVER,
            UserCardLoad::EARN_TYPE_SPEND_REWARD,
            UserCardLoad::EARN_TYPE_REFERRAL
        ])) {
            $query->andWhere('ucl.earnType = :earnType')
                ->setParameter('earnType', $earnType);
        }

        return $this->queryByDateRange($query, 'ucl.createdAt', $request);
    }

    protected function queryListParams(QueryBuilder $query, Request $request)
    {
        $params = new QueryListParams($query, $request, 'ucl', 'count(distinct ucl)');
        $params->distinct = true;
        $params->orderBy = [
            'ucl.id' => 'desc',
        ];
        $params->searchFields = [
            'ucl.id',
            'ucl.loadStatus'
        ];
        return $params;
    }

    /**
     * @param UserCardLoad $entity
     * @return array
     */
    protected function getRowData($entity)
    {
        $amount = $entity->getLoadAmountUSD() ? $entity->getLoadAmountUSD() : $entity->getInitialAmount();
        $amountText = Money::format($amount, 'USD', false);
        return [
            'ID' => $entity->getId(),
            'User ID' => $entity->getUserCard()->getUser()->getId(),
            'First Name' => $entity->getUserCard()->getUser()->getFirstName(),
            'Last Name' => $entity->getUserCard()->getUser()->getLastName(),
            'Email' => $entity->getUserCard()->getUser()->getEmail(),
            'Load Type' => $entity->getType() === UserCardLoad::TYPE_LOAD_CARD
				? PromotionService::PROMOTION_TYPE_EARN : 'Revoke',
            'Earn Type' => Util::meta($entity, 'customMessage') ?? 'Other',
            'Rewards Earned' => ($entity->getType() == UserCardLoad::TYPE_LOAD_CARD ? '' : '-') . $amountText,
            'Earn Date' => Util::formatDate($entity->getCompletedAt(), $this->tz, Util::DATE_TIME_FORMAT),
            'Reward Status' => $entity->getLoadStatus() ? $entity->getLoadStatus() : 'Pending'
        ];
    }

    /**
     * @Route("/admin/spendr/earn-report/export", methods={"POST"})
     * @param Request $request
     *
     * @return SuccessResponse|DeniedResponse
     */
    public function export(Request $request)
    {
		if (SpendrBundle::notAllowedExport()) {
			return new DeniedResponse();
		}
        $fields = [
            'User ID' => 10,
            'First Name' => 20,
            'Last Name' => 20,
            'Email' => 30,
            'Load Type' => 20,
            'Earn Type' => 20,
            'Rewards Earned' => 20,
            'Earn Date' => 20,
            'Reward Status' => 20
        ];
        return $this->commonExport($request, 'Spendr Earn Report', $fields);
    }

    /**
     * @Route("/admin/spendr/promo-load/{earn}/manual-revoke")
     *
     * @param Request $request
     * @param UserCardLoad $earn
     * @return FailedResponse|SuccessResponse
     * @throws PortalException
     */
    public function revokeEarnedRewards(UserCardLoad $earn, Request $request)
    {
        if ($this->user->inTeams([
            Role::ROLE_SPENDR_ACCOUNTANT,
            Role::ROLE_SPENDR_CUSTOMER_SUPPORT
        ])) {
            return new DeniedResponse();
        }
        $reason = $request->get('reason');
        $reason = trim($reason);
        if (!$reason) {
            return new FailedResponse('Please input the revoke reason.');
        }

		$checkRes = LoadService::beforeCancelLoad($earn);
        if (is_string($checkRes)) {
        	return new FailedResponse($checkRes);
		}

        Queue::spendrCancelLoad($earn->getId(), $reason);

        return new SuccessResponse(null, 'Queued, please wait...');
    }
}
