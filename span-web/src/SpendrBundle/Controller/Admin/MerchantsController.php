<?php


namespace SpendrBundle\Controller\Admin;


use Carbon\Carbon;
use CoreBundle\Entity\Email;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserCard;
use CoreBundle\Response\DeniedResponse;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Services\UserService;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\QueryListParams;
use CoreBundle\Utils\Service;
use CoreBundle\Utils\Util;
use Doctrine\ORM\QueryBuilder;
use PortalBundle\Exception\DeniedException;
use PortalBundle\Exception\PortalException;
use SalexUserBundle\Entity\User;
use SpendrBundle\Entity\LocationEmployee;
use SpendrBundle\Entity\MerchantAdmin;
use SpendrBundle\Entity\SpendrGroup;
use SpendrBundle\Entity\SpendrMerchant;
use SpendrBundle\Services\BrazeService;
use SpendrBundle\Services\GroupService;
use SpendrBundle\Services\LoadService;
use SpendrBundle\Services\MerchantService;
use SpendrBundle\Services\Pay\PayService;
use SpendrBundle\SpendrBundle;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use SpendrBundle\Services\UserService as SpendrUserService;

class MerchantsController extends BaseController
{
	/**
	 * @Route("/admin/spendr/merchants/list/{page}/{limit}", methods={"GET"}, defaults={"page" = 1,"limit" = 10})
	 * @param Request $request
	 * @param int $page
	 * @param int $limit
	 *
	 * @return SuccessResponse
	 * @throws \Doctrine\ORM\NoResultException
	 * @throws \Doctrine\ORM\NonUniqueResultException
	 */
    public function search(Request $request, $page = 1, $limit = 10)
    {
        $resp = $this->traitSearch($request, [], $page, $limit);
        $result = $resp->getResult();

        $query = $this->query($request);
        $total = (clone $query)->select('count(distinct m)')
            ->getQuery()
            ->getSingleScalarResult();

		$totalBalance = null;
		$averageBalance = null;
        if (!$this->user->inTeam(Role::ROLE_SPENDR_BANK)) {
			$expr = Util::expr();
			$subQuery = $this->em->getRepository(User::class)
				->createQueryBuilder('u')
				->join('u.merchants', 'm');
			$balanceQuery = $this->em->getRepository(UserCard::class)
				->createQueryBuilder('uuc')
				->join('uuc.card', 'uuct')
				->join('uuct.cardProgram', 'uucpp')
				->where($expr->eq('uuc.user', $expr->any($subQuery->getDQL())))
				->andWhere($expr->eq('uucpp.id', ':cardProgram' ))
				->setParameter('cardProgram', Util::cardProgram()->getId())
				->select('sum(uuc.balance)');

			$balance = Util::moveQueryParameters($balanceQuery, $subQuery)
				->getQuery()
				->getSingleScalarResult();
			$totalBalance = (int)$balance;
			$averageBalance = Util::percent($balance, $total);
		}

        $result['quick'] = [
            'total' => (int)$total,
            'totalBalance' => $totalBalance,
            'averageBalance' => $averageBalance,
            'employeeRoles' => SpendrBundle::getMerchantEmployeeRoles()
        ];

        return new SuccessResponse($result);
    }

    protected function query(Request $request)
    {
        $query = $this->em->getRepository(SpendrMerchant::class)
            ->createQueryBuilder('m')
            ->join('m.adminUser', 'u');

        $testMerchantIds = MerchantService::merchantIdsForTestArray();
        if ($testMerchantIds) {
        	$query->andWhere(Util::expr()->notIn('m.id', $testMerchantIds));
		}
        return $query;
    }

    protected function queryListParams(QueryBuilder $query, Request $request)
    {
        $params = new QueryListParams($query, $request, 'm', 'count(distinct m)');
        $params->distinct = true;
        $params->orderBy = [
            'm.id' => 'desc',
        ];
        $params->searchFields = [
            'm.id',
            'm.name',
            'm.businessType',
            'u.email',
        ];
        return $params;
    }

    /**
     * @param SpendrMerchant $entity
     *
     * @return array
     */
    protected function getRowData($entity)
    {
    	$extra = $this->user->inTeam(Role::ROLE_SPENDR_BANK) ? false : true;
        return $entity->getDetailsArray($extra);
    }

    /**
     * @Route("/admin/spendr/merchants/{merchant}/detail")
     *
     * @return SuccessResponse
     */
    public function detailAction(SpendrMerchant $merchant)
    {
        $data = $this->getRowData($merchant);

        $groups = GroupService::getAllGroups();
        $groupList = [];
        if ($groups) {
            foreach ($groups as $group) {
                $groupList[] = [
                    'label' => $group->getName(),
                    'value' => $group->getId(),
                ];
            }
        }
        $data['groupList'] = $groupList;

        return new SuccessResponse($data);
    }

	/**
	 * @Route("/admin/spendr/merchants/save", methods={"POST"})
	 * @param Request $request
	 *
	 * @return SuccessResponse|DeniedResponse
	 * @throws \CoreBundle\Exception\FailedException
	 */
    public function saveAction(Request $request)
    {
		if ($this->user->inTeams([
			Role::ROLE_SPENDR_CUSTOMER_SUPPORT,
			Role::ROLE_SPENDR_ACCOUNTANT,
			Role::ROLE_SPENDR_BANK
		])) {
			return new DeniedResponse();
		}

		$merchant = MerchantService::saveDetails($request);
        return new SuccessResponse($this->getRowData($merchant));
    }

    /**
     * @Route("/admin/spendr/merchants/{merchant}/toggle-status", methods={"POST"})
     * @param Request        $request
     * @param SpendrMerchant $merchant
     *
     * @return SuccessResponse|DeniedResponse
     */
    public function toggleStatusAction(Request $request, SpendrMerchant $merchant)
    {
		$this->authAdminsWithoutBank();
		if ($this->isViewOnlyRole()) {
			return new DeniedResponse();
		}

        if (MerchantService::isMergedIntoOtherMerchant($merchant)) {
            return new FailedResponse('This merchant has been merged into other merchants and cannot be changed separately.');
        }

		$status = $request->get('Status', 'Active');
        $merchant->updateOnboardingStatus($status)
            ->persist();

        $otherMerchantId = MerchantService::isMergedOtherMerchant($merchant, 'id');
        if ($otherMerchantId) {
            $otherMerchant = SpendrMerchant::find($otherMerchantId);
            $otherMerchant->updateOnboardingStatus($status)
                ->persist();
        }

        return new SuccessResponse($this->getRowData($merchant));
    }

    /**
     * @Route("/admin/spendr/merchants/{user}/resend-invitation", methods={"POST"})
     * @param Request $request
     * @param User    $user
     *
     * @return SuccessResponse|DeniedResponse
     */
    public function resendInvitation(Request $request, User $user)
    {
		$this->authAdminsWithoutBank();
		if ($this->isViewOnlyRole()) {
			return new DeniedResponse();
		}

		UserService::sendResetPasswordEmail($user);

        return new SuccessResponse();
    }

	/**
	 * @Route("/admin/spendr/merchants/{merchant}/review/{action}", methods={"POST"})
	 * @param Request $request
	 * @param SpendrMerchant $merchant
	 * @param                $action
	 *
	 * @return FailedResponse|SuccessResponse
	 * @throws DeniedException
	 * @throws \CoreBundle\Exception\RedirectException
	 */
    public function reviewAction(Request $request, SpendrMerchant $merchant, $action)
    {
        $sa = $this->user->isMasterAdmin();
        $bank = $this->user->inTeam(Role::ROLE_SPENDR_BANK);
        if (!$sa && !$bank) {
            throw new DeniedException();
        }

        $message = $request->get('message');
        if ($action === 'deny' && !$message) {
            return new FailedResponse('Empty deny reason!');
        }

        $do = $sa;
        $status = $merchant->getOnboardingStatus();
        if (!$sa && $bank) {
            $do = in_array($status, [
                SpendrMerchant::ONBOARDING_STATUS_PENDING,
                SpendrMerchant::ONBOARDING_STATUS_APPROVED,
                SpendrMerchant::ONBOARDING_STATUS_DENIED,
            ]);
        }

        if ($do) {
            Util::updateMeta($merchant, 'bankDeniedAt', false);
            Util::updateMeta($merchant, 'bankApprovedAt', false);
            $now = Util::formatDateTime(
            	Carbon::now(),
				Util::DATE_TIME_FORMAT,
				$this->tz
			);

            if ($action === 'approve') {
                $status = SpendrMerchant::ONBOARDING_STATUS_APPROVED;
                Util::updateMeta($merchant, [
                    'bankApprovedAt' => $now,
                    'bankApprovedBy' => $this->user->getId(),
                    'bankApprovedByName' => $this->user->getName(),
                ], false);
            } else if ($action === 'deny') {
                $status = SpendrMerchant::ONBOARDING_STATUS_DENIED;
                Util::updateMeta($merchant, [
                    'bankDeniedAt' => $now,
                    'bankDeniedMessage' => $message,
                    'bankDeniedBy' => $this->user->getId(),
                    'bankDeniedByName' => $this->user->getName(),
                ], false);
            }
            $merchant->setOnboardingStatus($status)
                ->persist();

            if (in_array($action, ['approve', 'deny'])) {
                $url = Util::platform()->host() . '/admin#/h/spendr/merchant/onboard';
                $subject = 'Your onboarding application has been ' . $status;
                $body = '<p>Your onboarding application has been ' . $status . '.</p>';
                if ($message) {
                    $body .= '<p>The message from reviewer: </p><div>' . $message . '</div>';
                }
                $body .= '<p><a href="' . $url . '">Click here to view the details.</a></p>';
                Email::sendWithTemplateToUser($merchant->getAdminUser(), Email::TEMPLATE_BASE_LAYOUT, [
                    'subject' => $subject,
                    'body' => $body,
                ]);
            }
        }

        return new SuccessResponse();
    }

	/**
	 * @Route("/admin/spendr/merchants/export", methods={"POST"})
	 * @param Request $request
	 *
	 * @return SuccessResponse|DeniedResponse
	 */
    public function export(Request $request)
    {
		if (SpendrBundle::notAllowedExport()) {
			return new DeniedResponse();
		}

        $fields = [
            'Merchant ID'             => 20,
            'Merchant Name'           => 30,
            'Merchant Type'           => 20,
            'Email'                   => 30,
            'Locations'               => 15,
            'Balance'                 => 15,
            'Transactions'            => 15,
            'Status'                  => 18,
            'Doing Business As'       => 25,
            'Business Street Address' => 40,
            'City'                    => 20,
            'Postal Code'             => 15,
            'Country'                 => 22,
            'StateName'               => 22,
            'Phone'                   => 22,
            'Tax ID'                  => 22,
            'EIN'                     => 22,
        ];
        return $this->commonExport($request, 'Spendr Merchants', $fields);
    }

    /**
     * @Route("/admin/spendr/merchants/export-braze", methods={"POST"})
     * @param Request $request
     * @return DeniedResponse|SuccessResponse
     * @throws PortalException
     */
    public function exportBraze(Request $request)
    {
        if (SpendrBundle::notAllowedExport()) {
            return new DeniedResponse();
        }
        Util::longerRequest();
        if ($request->get('type') === 'Admins') {
            $query = Util::em()->getRepository(MerchantAdmin::class)
                ->createQueryBuilder('ma')
                ->join('ma.merchant', 'm')
                ->join('ma.user', 'u')
                ->join('u.teams', 't')
                ->where(Util::expr()->isNotNull('m.adminUser'))
                ->andWhere(Util::expr()->in('t.name', ':roles'))
                ->setParameter('roles', SpendrBundle::getMerchantAdminRoles());
            $params = new QueryListParams($query, $request, 'ma, u', 'count(distinct ma)');
            $params->distinct = true;
            $params->orderBy = [
                'u.id' => 'desc',
            ];
            $params->searchFields = [
                'u.id',
                'u.name',
                'u.email',
                't.name',
            ];

            $subscribed = BrazeService::TYPE_OPTEDIN;
        } else {
            $query = Util::em()->getRepository(LocationEmployee::class)
                ->createQueryBuilder('le')
                ->join('le.user', 'u')
                ->join('u.teams', 't')
                ->join('le.merchant', 'm')
                ->where(Util::expr()->isNotNull('m.adminUser'));
            $params = new QueryListParams($query, $request, 'le', 'count(distinct le.user)');
            $params->distinct = true;
            $params->orderBy = [
                'u.id' => 'desc',
            ];
            $params->groupBy = 'u.id';
            $params->searchFields = [
                'u.name'
            ];

            $subscribed = BrazeService::TYPE_UNSUBSCRIBED;
        }
        if ($request->get('query_count')) {
            $count = $this->queryCountForExport($params);
            return new SuccessResponse($count);
        }

        $from  = (int)$request->get('query_from', 0);
        $limit = (int)$request->get('query_limit', 5000);

        $data = $this->queryListForExport($params, $from, $limit, function ($entity) use ($subscribed) {
            $merchant = $entity->getMerchant();
            $dummy = SpendrUserService::getDummyCard($merchant->getAdminUser());
            $user = $entity->getUser();
            $role = $user->getTeamName();
            $sub = Util::meta($merchant, 'Employee Subscribed');
            if ($user->getStatus() !== User::STATUS_ACTIVE) {
                $subscribed = BrazeService::TYPE_UNSUBSCRIBED;
            } elseif ($sub && in_array($role, $sub)) {
                $subscribed = BrazeService::TYPE_OPTEDIN;
            }
            // Cache the merchant admins and employees user id
            Data::instance()->sadd(
                MerchantService::KEY_MERCHANT_ADMIN_AND_EMPLOYEE . ':' . $merchant->getId(),
                $user->getId()
            );

            return [
                'external_id' => BrazeService::getExternalPrefix() . $user->getId(),
                'first_name' => $user->getFirstName(),
                'last_name' => $user->getLastName(),
                'email' => $user->getEmail(),
                'phone' => $user->getMobilephone(),
                'merchant_name' => $merchant->getName(),
                'merchant_role' => $role,
                'merchant_balance' => $dummy ? Money::formatAmountToNumber($dummy->getBalance()) : 0,
                'email_subscribe' => $subscribed
            ];
        });

        $timestamp = $request->get('query_timestamp', time());
        $filename = 'Spendr_Braze_Merchant_' . $request->get('type') . '_' . date('YmdHis', $timestamp);
        $destination = Util::uploadDir('report') . $filename . '.csv';
        $excel = $this->loadOrCreateExcel($destination, $filename);
        $headers = [
            'external_id' => 20,
            'first_name' => 40,
            'last_name' => 40,
            'email' => 40,
            'phone' => 40,
            'merchant_name' => 40,
            'merchant_role' => 40,
            'merchant_balance' => 40,
            'email_subscribe' => 20
        ];

        $excel = $this->generateSheetAppendToExcel($excel, $from . ' ~ ' . ($from + $limit), $headers, $data,
            function ($col, $row, $excel, $title) {
                return $row[$title] ?? '';
            });
        $this->saveCsvTo($excel, $destination, count($headers));

        return new SuccessResponse('report/' . $filename . '.csv');
    }

    /**
     * @Route("/admin/spendr/merchants/config-braze-pushed", methods={"POST"})
     * @param Request $request
     * @return FailedResponse|SuccessResponse
     */
    public function configBrazePushed(Request $request)
    {
        $this->authAdminsWithoutBank();
        if ($this->isViewOnlyRole()) {
            return new DeniedResponse();
        }
        $merchant = SpendrMerchant::find($request->get('Merchant ID'));
        if (!$merchant) {
            return new FailedResponse('Merchant not found.');
        }

        if (MerchantService::isMergedIntoOtherMerchant($merchant)) {
            return new FailedResponse('This merchant has been merged into other merchants and cannot be set up separately.');
        }

        $roles = $request->get('Subscribed');
        if (count($roles) &&
            count($roles) !== count(array_intersect($roles, SpendrBundle::getMerchantEmployeeRoles()))) {
            return new FailedResponse('Invalid employee roles');
        }

        Util::updateMeta($merchant, [
            'Employee Subscribed' => $roles
        ]);
        $query = Util::em()->getRepository(LocationEmployee::class)
            ->createQueryBuilder('le')
            ->join('le.user', 'u')
            ->join('u.teams', 't');

        $query = MerchantService::generateMergeMerchantQuery($query, 'le.merchant', $merchant);

        $employees = $query->getQuery()
            ->getResult();

        $uids = [];
        /** @var LocationEmployee $employee */
        foreach ($employees as $employee) {
            $user = $employee->getUser();
            $subscribe = BrazeService::TYPE_UNSUBSCRIBED;
            if (!in_array($user->getTeamName(), $roles)) {
                $subscribe = BrazeService::TYPE_OPTEDIN;
            }

            array_push($uids, [
                'external_id' => BrazeService::getExternalPrefix() . $user->getId(),
                'email_subscribe' => $subscribe
            ]);
        }

        // Update admin subscribe status
        BrazeService::userTrack(null, null, null, $uids, true);

        return new SuccessResponse();
    }

	// /**
	//  * @Route("/admin/spendr/merchants/{merchant}/withdraw")
	//  *
	//  * @param Request $request
	//  * @param SpendrMerchant $merchant
	//  * @return FailedResponse|SuccessResponse
	//  * @throws DeniedException
	//  * @throws PortalException
	//  * @throws \Throwable
	//  */
    // public function withdraw(Request $request, SpendrMerchant $merchant)
	// {
	// 	$this->authRoles([
	// 		Role::ROLE_MASTER_ADMIN
	// 	]);

	// 	return LoadService::merchantUnload($request, $merchant, $this->user);
	// }

	/**
	 * @Route("/admin/spendr/merchants/{merchant}/send-to-bank")
	 *
	 * @param Request $request
	 * @param SpendrMerchant $merchant
	 * @return SuccessResponse|DeniedResponse
	 */
	public function sendToBank(Request $request, SpendrMerchant $merchant) {
		if ($this->user->inTeams([
			Role::ROLE_SPENDR_CUSTOMER_SUPPORT,
			Role::ROLE_SPENDR_ACCOUNTANT,
			Role::ROLE_SPENDR_BANK
		])) {
			return new DeniedResponse();
		}

		Service::sendAsync('/t/cron/spendr/merchant/' . $merchant->getId() . '/onboard/pending/emails');
		return new SuccessResponse();
	}

    // /**
    //  * @Route("/admin/spendr/merchants/create-balance-info", methods={"POST"})
    //  * @param Request        $request
    //  *
    //  * @return SuccessResponse|DeniedResponse
    //  */
    // public function createLocationBalanceInfo(Request $request)
    // {
    //     $this->authSuperAdmin();

	// 	if (!Util::isTracyUser()) {
    //         return new DeniedResponse();
    //     }

	// 	MerchantService::createLocationBalanceUserForOldMerchant();

    //     return new SuccessResponse();
    // }

    // /**
    //  * @Route("/admin/spendr/merchants/{merchant}/associate-location-admin", methods={"POST"})
    //  * @param Request        $request
    //  * @param SpendrMerchant $merchant
    //  *
    //  * @return SuccessResponse|DeniedResponse
    //  */
    // public function associateLocationAdmin(Request $request, SpendrMerchant $merchant)
    // {
    //     $this->authSuperAdmin();

	// 	if (!Util::isTracyUser()) {
    //         return new DeniedResponse();
    //     }

	// 	MerchantService::associateAllLocationsWithMerchantAdmins($merchant);

    //     return new SuccessResponse();
    // }

    /**
	 * @Route("/admin/spendr/merchants/{merchant}/manually-load")
	 *
	 * @param Request $request
	 * @param SpendrMerchant $merchant
	 * @return FailedResponse|SuccessResponse
	 * @throws DeniedException
	 * @throws PortalException
	 * @throws \Throwable
	 */
    public function manuallyLoad(Request $request, SpendrMerchant $merchant)
	{
		$this->authRoles([
			Role::ROLE_MASTER_ADMIN
		]);

        $amount = Money::normalizeAmount($request->get('Amount', 0), 'USD');
        $customMessage = $request->get('customMessage');

        $res = LoadService::merchantManuallyLoad($merchant, $amount, $customMessage, $this->user);
        if (is_string($res)) {
            return new FailedResponse($res);
        }

		return new SuccessResponse(null, $res['msg']);
	}
}
