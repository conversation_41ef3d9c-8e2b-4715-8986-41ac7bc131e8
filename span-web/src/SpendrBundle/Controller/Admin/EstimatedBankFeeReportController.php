<?php

namespace SpendrBundle\Controller\Admin;

use Carbon\Carbon;
use CoreBundle\Entity\AchBatch;
use CoreBundle\Entity\AchTransactions;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Util;
use SpendrBundle\Entity\SpendrTransaction;
use SpendrBundle\Services\FeeService;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class EstimatedBankFeeReportController extends BaseController
{
	public function __construct()
	{
		parent::__construct();
		$this->authAdminsWithoutBank();
	}

	/**
	 * @Route("/admin/spendr/fee-report/estimated-bank/{page}/{limit}", methods={"GET"}, defaults={"page" = 1,"limit" = 10})
	 * @param Request $request
	 * @param int $page
	 * @param int $limit
	 * @return FailedResponse|SuccessResponse
	 */
	public function index(Request $request, $page = 1, $limit = 10)
	{
		[$start, $end] = $this->getDateRange($request);

		$items = [];

		$loadFees = $this->getLoadFee($start, $end);
		$returnFees = $this->getReturnFee($start, $end);
		$monthlyAccountFees = $this->getMonthlyAccountFee($start, $end);
		$wireFees = $this->getWireFee($start, $end);
		$achSetupFees = $this->getAchSetupFee($start, $end);

		$items = array_merge(
			$items,
			$loadFees['data'],
			$returnFees['data'],
			$monthlyAccountFees['data'],
			$wireFees['data'],
			$achSetupFees['data'],
		);
		$totalAmount = $loadFees['totalAmount']
			+ $returnFees['totalAmount']
			+ $monthlyAccountFees['totalAmount']
			+ $wireFees['totalAmount']
			+ $achSetupFees['totalAmount'];

		Util::usort($items, [
			'timestamp' => 'desc',
		]);

		$total = count($items);
		$items = array_slice($items, $limit * ($page - 1), $limit);

		return new SuccessResponse([
			'count' => $total,
			'data' => $items,
			'quick' => [
				'total' => $total,
				'totalAmount' => $totalAmount,
				'totalAchLoadFee' => $loadFees['totalAmount'],
				'totalAchReturnFee' => $returnFees['totalAmount'],
				'totalMonthlyAccountFee' => $monthlyAccountFees['totalAmount'],
				'totalWireFee' => $wireFees['totalAmount'],
				'totalAchSetupFee' => $achSetupFees['totalAmount'],
			]
		]);
	}

	protected function getDateRange(Request $request) {
		$period  = $request->get('period', 'all');
		$start = $request->get('start') ? Carbon::parse($request->get('start')) : null;
		$end = $request->get('end') ? Carbon::parse($request->get('end'))->endOfDay() : null;

		if (
			($period !== 'custom_range')
			&& ($period !== 'today')
			&& ($period !== 'week')
			&& ($period !== 'month')
			&& ($period !== 'quarter')
			&& ($period !== 'year')
		) {
			return null;
		}

		return [$start, $end];
	}

	protected function getLoadFee(Carbon $start = null, Carbon $end = null)
	{
		$data = [
			'data' => [],
			'totalAmount' => 0
		];

		$loads = FeeService::calculateBankLoadFee($start, $end, 'list');
		if (!$loads) {
			return $data;
		}

		/** @var UserCardLoad $load */
		foreach ($loads as $load) {
			$feeTime = $load->getInitializedAt() ? $load->getInitializedAt() : null;
			$fee = FeeService::consumerLoadFeeToBank($load->getInitialAmount());
			$achTxn = AchTransactions::findTransactionByTranId($load->getTransactionNo());
			$batch = AchBatch::find($achTxn->getBatchId());
			$text = '';
			if ($batch->getProcessType() === AchBatch::ACH_BATCH_PROCESS_TYPE_SAME_DAY) {
				$fee += FeeService::achSameDayFeeToBank();
				$text = ' (Same day ACH)';
			}
			$data['data'][] = [
				'Transaction' => 'ACH Load Fee' . $text,
				'Date & Time' => Util::formatDateTime($feeTime, Util::DATE_TIME_FORMAT, $this->tz),
				'Transaction ID' => $load->getId(),
				'Amount Value' => $fee,
				'Amount' => Money::format($fee, 'USD', false),
				'timestamp' => $feeTime->getTimestamp(),
			];
			$data['totalAmount'] += $fee;
		}

		return $data;
	}

	protected function getReturnFee(Carbon $start = null, Carbon $end = null)
	{
		$data = [
			'data' => [],
			'totalAmount' => 0
		];

		$fee = FeeService::toBankAchReturnFee();
		if (!$fee) {
			return $data;
		}

		$loads = FeeService::calculateBankReturnedLoadFee($start, $end, 'list');
		if (!$loads) {
			return $data;
		}

		/** @var UserCardLoad $load */
		foreach ($loads as $load) {
			$feeTime = $load->getInitializedAt() ? $load->getInitializedAt() : null;
			$data['data'][] = [
				'Transaction' => 'ACH Return Fee',
				'Date & Time' => Util::formatDateTime($feeTime, Util::DATE_TIME_FORMAT, $this->tz),
				'Transaction ID' => $load->getId(),
				'Amount Value' => $fee,
				'Amount' => Money::format($fee, 'USD', false),
				'timestamp' => $feeTime->getTimestamp(),
			];
			$data['totalAmount'] += $fee;
		}

		if (Util::isLive()) {
			$otherWrongReturnedTxnDate = FeeService::calculateOtherWrongReturnedTxn($start, $end, 'list');
			if ($otherWrongReturnedTxnDate) {
				foreach ($otherWrongReturnedTxnDate as $date) {
					// Bank overcharged fees
					$feeTime = $date;
					$data['data'][] = [
						'Transaction' => 'ACH Return Fee (Bank overcharged fees)',
						'Date & Time' => Util::formatDateTime($feeTime, Util::DATE_TIME_FORMAT, $this->tz),
						'Transaction ID' => null,
						'Amount Value' => $fee,
						'Amount' => Money::format($fee, 'USD', false),
						'timestamp' => $feeTime->getTimestamp(),
					];
					$data['totalAmount'] += $fee;
				}
			}
		}

		return $data;
	}

	protected function getMonthlyAccountFee(Carbon $start = null, Carbon $end = null)
	{
		$data = [
			'data' => [],
			'totalAmount' => 0
		];

		$fee = FeeService::calculate(FeeService::TO_BANK_SPENDR_MONTHLY_ACCOUNT_FEE);
		if (!$fee) {
			return $data;
		}

		$feeRecords = FeeService::calculateBankMonthlyAccountFee($start, $end, 'list');

		if (!$feeRecords) {
			return $data;
		}

		$data['data'] = $feeRecords;
		$data['totalAmount'] = count($data['data']) * $fee;

		return $data;
	}

	protected function getWireFee(Carbon $start = null, Carbon $end = null)
	{
		$data = [
			'data' => [],
			'totalAmount' => 0
		];

		$fee = FeeService::calculate(FeeService::TO_BANK_SPENDR_WIRE_FEE);
		if (!$fee) {
			return $data;
		}

		$txns = FeeService::calculateBankWireFee($start, $end, 'list');

		/** @var SpendrTransaction $txn */
		foreach ($txns as $txn) {
			$feeTime = $txn->getImportAt() ? $txn->getImportAt() : null;
			$data['data'][] = [
				'Transaction' => 'Wire Fee',
				'Date & Time' => Util::formatDateTime($feeTime, Util::DATE_TIME_FORMAT, $this->tz),
				'Transaction ID' => $txn->getId(),
				'Amount Value' => $fee,
				'Amount' => Money::format($fee, 'USD', false),
				'timestamp' => $feeTime->getTimestamp(),
			];
			$data['totalAmount'] += $fee;
		}

		return $data;
	}

	protected function getAchSetupFee(Carbon $start = null, Carbon $end = null)
	{
		$data = [
			'data' => [],
			'totalAmount' => 0
		];

		$fee = FeeService::calculate(FeeService::TO_BANK_SPENDR_ACH_SETUP_FEE);
		if (!$fee) {
			return $data;
		}

		$txns = FeeService::calculateBankAchSetupFee($start, $end, 'list');

		/** @var SpendrTransaction $txn */
		foreach ($txns as $txn) {
			$feeTime = $txn->getImportAt() ? $txn->getImportAt() : null;
			$data['data'][] = [
				'Transaction' => 'ACH Setup Fee',
				'Date & Time' => Util::formatDateTime($feeTime, Util::DATE_TIME_FORMAT, $this->tz),
				'Transaction ID' => $txn->getId(),
				'Amount Value' => $fee,
				'Amount' => Money::format($fee, 'USD', false),
				'timestamp' => $feeTime->getTimestamp(),
			];
			$data['totalAmount'] += $fee;
		}

		return $data;
	}
}
