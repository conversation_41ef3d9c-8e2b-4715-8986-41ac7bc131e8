<?php

namespace SpendrBundle\Traits;

use Clf<PERSON><PERSON>le\Entity\Account;
use ClfB<PERSON>le\Entity\Merchant;
use CoreBundle\Entity\Attachment;
use CoreBundle\Entity\Role;
use CoreBundle\Utils\Util;
use Ramsey\Uuid\Uuid;
use SalexUserBundle\Entity\UserConfig;
use SpendrBundle\Entity\LocationEmployee;
use SpendrBundle\Entity\MerchantAdmin;
use SpendrBundle\Entity\SpendrGroup;
use SpendrBundle\Entity\SpendrMerchant;
use SpendrBundle\Services\MerchantService;
use SpendrBundle\SpendrBundle;

trait UserSpendrTrait
{
    /**
     * @return SpendrMerchant
     */
    public function getSpendrAdminMerchant()
    {
		/** @var MerchantAdmin $merchantAdmin */
		$merchantAdmin = Util::em()->getRepository(MerchantAdmin::class)
			->findOneBy([
				'user' => $this
			]);
		if (!$merchantAdmin && $this->inTeams(SpendrBundle::getMerchantBalanceAdminRoles())) {
			$merchant = $this->getClfAdminMerchant();
			if ($merchant) {
				$merchantAdmin = new MerchantAdmin();
				$merchantAdmin->setUser($this)
					->setMerchant($merchant);
				Util::persist($merchantAdmin);
			}
		}

		if ($merchantAdmin) {
			$merchant = $merchantAdmin->getMerchant();
			$mergeIntoMerchant = MerchantService::mergeIntoOtherMerchant($merchant, 'object');
			if ($mergeIntoMerchant) {
				$merchant = $mergeIntoMerchant;
			}
			return $merchant;
		} else {
			return null;
		}
    }

    public function getLocationsByEmployee()
    {
		/* @var LocationEmployee $locationEmployee */
        $locationEmployees = Util::em()->getRepository(\SpendrBundle\Entity\LocationEmployee::class)
			->findBy([ 'user' => $this ]);
        if ($locationEmployees) {
            $temp = [];
            foreach ($locationEmployees as $locationEmployee) {
              $temp[] = $locationEmployee->getLocation();
            }
            return $temp;
        } else {
            return [];
        }
    }

    public function getMerchant()
	{
		/* @var LocationEmployee $locationEmployee */
		$locationEmployee = Util::em()->getRepository(\SpendrBundle\Entity\LocationEmployee::class)
			->findOneBy([ 'user' => $this ]);
		if ($locationEmployee) {
			return $locationEmployee->getMerchant();
		} else {
			return null;
		}
	}

    public function getRole()
    {
        return $this->getTeamName();
    }

    public function getPin()
    {
        /* @var LocationEmployee $locationEmployee */
        $locationEmployee = Util::em()->getRepository(\SpendrBundle\Entity\LocationEmployee::class)
            ->findOneBy([ 'user' => $this ]);
        if ($locationEmployee) {
            return $locationEmployee->getPin();
        } else {
            return null;
        }
    }

    public function getAccount()
	{
		$account = Util::em()->getRepository(Account::class)
			->findOneBy(['adminUser' => $this]);
		if ($account) {
			return $account;
		} else {
			return null;
		}
	}

    public function ensureBankAccountId(): string
    {
        $config = $this->ensureConfig();
        if ($config && $config->getBankAccountId()) {
            $accountId = $config->getBankAccountId();
        } else {
            $accountId = Uuid::uuid4();
            $config->setBankAccountId($accountId)
                ->setPlatform(UserConfig::PLATFORM_SPENDR)
                ->setReferCode(Uuid::uuid4())
                ->persist();
        }
        return $accountId;
    }

	public function getConsumerPinCode()
	{
		$meta = Util::s2j($this->getMeta());
		return isset($meta['pinCode']) ? $meta['pinCode']: null;
	}

	public function isPinEnabled()
	{
		$meta = Util::s2j($this->getMeta());
		return isset($meta['pinCodeEnabled']) ? $meta['pinCodeEnabled']: null;
	}

	public function isBiometricLoginEnabled()
	{
		$meta = Util::s2j($this->getMeta());
		return isset($meta['biometricLoginEnabled']) && $meta['biometricLoginEnabled'];
	}

	public function isViewIdButtonEnabled()
	{
		$meta = Util::s2j($this->getMeta());
		return isset($meta['viewIdButtonEnabled']) && $meta['viewIdButtonEnabled'];
	}

	public function getIdCardImages()
	{
		$meta = Util::s2j($this->getMeta());

		$frontText = 'medicalCardFrontId';
		$backText = 'medicalCardBackId';

		$frontId = isset($meta[$frontText]) ? $meta[$frontText]: null;
		$backId = isset($meta[$backText]) ? $meta[$backText]: null;

		$front = $frontId ? Attachment::find($frontId)?->getAssetUrl(true) : null;
		$back = $backId ? Attachment::find($backId)?->getAssetUrl(true) : null;

		return [
			'ids' => [
				'frontImageId' => $frontId,
				'backImageId' => $backId
			],
			'images' => [
				'frontImage' => $front,
				'backImage' => $back,
			]
		];
	}

    public function isSpendrGroupAdmin()
    {
        return $this->inTeam(Role::ROLE_SPENDR_GROUP_ADMIN);
    }
}
