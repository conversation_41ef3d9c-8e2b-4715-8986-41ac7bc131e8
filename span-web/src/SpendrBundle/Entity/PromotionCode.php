<?php

namespace SpendrBundle\Entity;

use Api<PERSON><PERSON>le\Entity\ApiEntityInterface;
use CoreBundle\Entity\BaseEntity;
use Doctrine\ORM\Mapping as ORM;

/**
 * PromotionCode
 *
 * @ORM\Table(name="spendr_promotion_code")
 * @ORM\Entity(repositoryClass="SpendrBundle\Repository\PromotionCodeRepository")
 */
class PromotionCode extends BaseEntity implements ApiEntityInterface
{
    public const STATUS_ACTIVE = 'active';
    public const STATUS_STOPPED = 'stopped';
    public const STATUS_COMPLETED = 'completed';
    /**
     * @var string
     *
     * @ORM\Column(name="name", type="string", length=180, nullable=true)
     */
    private $name;

    /**
     * @var int
     *
     * @ORM\Column(name="budget", type="integer", nullable=true)
     */
    private $budget;

    /**
     * @var int
     *
     * @ORM\Column(name="redeem", type="integer", nullable=true)
     */
    private $redeem;

    /**
     * @var string
     *
     * @ORM\Column(name="description", type="string", length=180, nullable=true)
     */
    private $description;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=64, nullable=true)
     */
    private $status;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="start_at", type="datetime", nullable=true)
     */
    private $startAt;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="end_at", type="datetime", nullable=true)
     */
    private $endAt;

    /**
     * @var string
     *
     * @ORM\Column(name="promo_code", type="string", length=64, nullable=true)
     */
    private $promoCode;

    /**
     * @var int
     *
     * @ORM\Column(name="promo_code_amount", type="integer", nullable=true)
     */
    private $promoCodeAmount;

    public function toApiArray(bool $extra = false): array
    {
        // TODO: Implement toApiArray() method.
        return [];
    }

    /**
     * Set name
     *
     * @param string $name
     *
     * @return PromotionCode
     */
    public function setName($name)
    {
        $this->name = $name;

        return $this;
    }

    /**
     * Get name
     *
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * Set budget
     *
     * @param integer $budget
     *
     * @return PromotionCode
     */
    public function setBudget($budget)
    {
        $this->budget = $budget;

        return $this;
    }

    /**
     * Get budget
     *
     * @return integer
     */
    public function getBudget()
    {
        return $this->budget;
    }

    /**
     * Set redeem
     *
     * @param integer $redeem
     *
     * @return PromotionCode
     */
    public function setRedeem($redeem)
    {
        $this->redeem = $redeem;

        return $this;
    }

    /**
     * Get redeem
     *
     * @return integer
     */
    public function getRedeem()
    {
        return $this->redeem;
    }

    /**
     * Set description
     *
     * @param string $description
     *
     * @return PromotionCode
     */
    public function setDescription($description)
    {
        $this->description = $description;

        return $this;
    }

    /**
     * Get description
     *
     * @return string
     */
    public function getDescription()
    {
        return $this->description;
    }

    /**
     * Set status
     *
     * @param string $status
     *
     * @return PromotionCode
     */
    public function setStatus($status)
    {
        $this->status = $status;

        return $this;
    }

    /**
     * Get status
     *
     * @return string
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * Set startAt
     *
     * @param \DateTime $startAt
     *
     * @return PromotionCode
     */
    public function setStartAt($startAt)
    {
        $this->startAt = $startAt;

        return $this;
    }

    /**
     * Get startAt
     *
     * @return \DateTime
     */
    public function getStartAt()
    {
        return $this->startAt;
    }

    /**
     * Set endAt
     *
     * @param \DateTime $endAt
     *
     * @return PromotionCode
     */
    public function setEndAt($endAt)
    {
        $this->endAt = $endAt;

        return $this;
    }

    /**
     * Get endAt
     *
     * @return \DateTime
     */
    public function getEndAt()
    {
        return $this->endAt;
    }

    /**
     * Set promoCode
     *
     * @param string $promoCode
     *
     * @return PromotionCode
     */
    public function setPromoCode($promoCode)
    {
        $this->promoCode = $promoCode;

        return $this;
    }

    /**
     * Get promoCode
     *
     * @return string
     */
    public function getPromoCode()
    {
        return $this->promoCode;
    }

    /**
     * Set promoCodeAmount
     *
     * @param integer $promoCodeAmount
     *
     * @return PromotionCode
     */
    public function setPromoCodeAmount($promoCodeAmount)
    {
        $this->promoCodeAmount = $promoCodeAmount;

        return $this;
    }

    /**
     * Get promoCodeAmount
     *
     * @return integer
     */
    public function getPromoCodeAmount()
    {
        return $this->promoCodeAmount;
    }
}
