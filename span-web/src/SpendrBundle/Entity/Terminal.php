<?php

namespace SpendrBundle\Entity;


use ApiB<PERSON>le\Entity\ApiEntityInterface;
use CoreBundle\Entity\BaseEntity;
use CoreBundle\Utils\Util;
use Doctrine\ORM\Mapping as ORM;

/**
 * Terminal
 *
 * @ORM\Table(name="spendr_terminal", options={"Roll up under the Location."})
 * @ORM\Entity(repositoryClass="SpendrBundle\Repository\TerminalRepository")
 */
class Terminal extends BaseEntity implements ApiEntityInterface
{
    public const STATUS_ACTIVE = 'Active';
    public const STATUS_INACTIVE = 'Inactive';

	public function toApiArray(bool $extra = false): array
	{
		return [
			'id' => $this->getId(),
			'name' => $this->getName(),
			'terminalId' => $this->getNumber(),
			'deviceId' => $this->getDeviceId(),
			'status' => $this->getStatus(),
			'location' => $this->getLocation()->toApiArray(),
		];
	}

	public function generateNumber()
	{
		$default = 1000;
		$l = Util::em()->getRepository(self::class)
			->createQueryBuilder('t')
			->orderBy('t.id', 'desc')
			->setMaxResults(1)
			->getQuery()
			->getResult();
		if ($l && $l[0]) {
			$location = $l[0];
			if ($location->getNumber()) {
				return $location->getNumber() + 1;
			}
		}
		return $default;
	}

	/**
	 * Terminal constructor.
	 */
	public function __construct()
	{
		$this->setNumber($this->generateNumber())
            ->setStatus(self::STATUS_ACTIVE);
	}

	/**
	 * @param $id
	 *
	 * @return Terminal|object
	 */
	public static function find($id)
	{
		if (!$id) {
			return null;
		}
		return Util::em()->getRepository(self::class)
			->find($id);
	}

	/**
	 * @var string
	 *
	 * @ORM\Column(name="name", type="string", length=180)
	 */
	private $name;

	/**
	 * @var Location
	 *
	 * @ORM\ManyToOne(targetEntity="SpendrBundle\Entity\Location")
	 */
	private $location;

	/**
	 * @var string
	 *
     * @ORM\Column(name="status", type="string", length=255, nullable=true)
	 */
	private $status;

	/**
	 * @var string
	 *
	 * @ORM\Column(name="number", type="string", length=180, nullable=true)
	 */
	private $number;

	/**
	 * @var string
	 *
	 * @ORM\Column(name="device_id", type="string", length=180, unique=true, nullable=true)
	 */
	private $deviceId;

	/**
     * Set name
     *
     * @param string $name
     *
     * @return Terminal
     */
    public function setName($name)
    {
        $this->name = $name;

        return $this;
    }

    /**
     * Get name
     *
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * Set location
     *
     * @param Location $location
     *
     * @return Terminal
     */
    public function setLocation(Location $location = null)
    {
        $this->location = $location;

        return $this;
    }

    /**
     * Get location
     *
     * @return Location
     */
    public function getLocation()
    {
        return $this->location;
    }

	/**
	 * Set number
	 *
	 * @param string $number
	 *
	 * @return Terminal
	 */
	public function setNumber($number)
	{
		$this->number = $number;

		return $this;
	}

	/**
	 * Get number
	 *
	 * @return string
	 */
	public function getNumber()
	{
		return $this->number;
	}

	/**
	 * Set deviceId
	 *
	 * @param string $deviceId
	 *
	 * @return Terminal
	 */
	public function setDeviceId($deviceId)
	{
		$this->deviceId = $deviceId;

		return $this;
	}

	/**
	 * Get deviceId
	 *
	 * @return string
	 */
	public function getDeviceId()
	{
		return $this->deviceId;
	}

    /**
     * Set status
     *
     * @param string $status
     *
     * @return Terminal
     */
    public function setStatus($status)
    {
        $this->status = $status;

        return $this;
    }

    /**
     * Get status
     *
     * @return string
     */
    public function getStatus()
    {
        return $this->status;
    }
}
