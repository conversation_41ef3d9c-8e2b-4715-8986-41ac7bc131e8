<?php

namespace SpendrBundle\Entity;

use <PERSON><PERSON><PERSON><PERSON>le\Entity\MerchantStatus;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Entity\Transaction;
use C<PERSON><PERSON><PERSON>le\Entity\TransactionStatus;
use CoreBundle\Entity\Address;
use CoreBundle\Entity\Platform;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Util;
use Doctrine\ORM\Mapping as ORM;
use C<PERSON><PERSON><PERSON>le\Entity\MerchantType as ClfMerchantType;
use C<PERSON><PERSON>undle\Entity\Merchant as ClfMerchant;
use SalexUserBundle\Entity\User;
use SpendrBundle\Services\MerchantService;

/**
 * SpendrMerchant
 *
 * @ORM\Table(name="spendr_merchant")
 * @ORM\Entity(repositoryClass="SpendrBundle\Repository\SpendrMerchantRepository")
 */
class SpendrMerchant extends ClfMerchant
{
    public const BUSINESS_TYPE_CANNABIS = 'Cannabis';
    public const BUSINESS_TYPE_NON_CANNABIS = 'Non-Cannabis';

    public const ONBOARDING_STATUS_INITIAL = 'Initial';
    public const ONBOARDING_STATUS_PENDING = 'Pending';
    public const ONBOARDING_STATUS_APPROVED = 'Approved'; // Bank has approved
    public const ONBOARDING_STATUS_DENIED = 'Denied';
    public const ONBOARDING_STATUS_INACTIVE = 'Inactive'; // Merchant Access revoked to app and dashboard.
    public const ONBOARDING_STATUS_ARCHIVED = 'Archived'; // Merchant Access revoked to app and dashboard. Hidden from merchant list unless the archived filter is selected.

	public const allBusinessType = [
		self::BUSINESS_TYPE_NON_CANNABIS,
		self::BUSINESS_TYPE_CANNABIS
	];

	public function toApiArray(bool $extra = FALSE): array
	{
		$all = parent::toApiArray($extra);
		if ($extra) {
			$all['onboardingStatus'] = $this->getOnboardingStatus();
			$all['balance'] = $this->getBalance();
			$all['businessType'] = $this->getBusinessType();
		}
		return $all;
	}

    public function getUserCard()
    {
        $admin = $this->getAdminUser();
        return $admin ? $admin->getUserCard(Platform::spendr()) : null;
    }

    public function getBalance()
    {
        $uc = $this->getUserCard();
        return $uc ? $uc->getBalance() : 0;
    }

    public function setBalance($value)
    {
        $uc = $this->getUserCard();
        if ($uc) {
            $uc->setBalance($value);
        }
        return $this;
    }

    public function updateOnboardingStatus($status)
    {
        if ($status === 'Active') {
            $status = self::ONBOARDING_STATUS_INITIAL;
            if (Util::meta($this, 'bankSubmittedAt')) {
                $status = self::ONBOARDING_STATUS_PENDING;
            }
            if (Util::meta($this, 'bankDeniedAt')) {
                $status = self::ONBOARDING_STATUS_DENIED;
            }
            if (Util::meta($this, 'bankApprovedAt')) {
                $status = self::ONBOARDING_STATUS_APPROVED;
            }
        }
        return $this->setOnboardingStatus($status);
    }

    public function isApproved()
    {
        return Util::meta($this, 'bankApprovedAt');
    }

    public function isApprovedAndActive()
    {
        return $this->getOnboardingStatus() === self::ONBOARDING_STATUS_APPROVED && $this->isApproved();
    }

    public function isActive()
	{
		return $this->isApprovedAndActive() && $this->getStatus() === MerchantStatus::active();
	}

    private function getLocationQueryByMerchant () {
        $query = Util::em()->getRepository(Location::class)
            ->createQueryBuilder('l')
            ->join('l.address', 'a')
            ->join('l.merchant', 'm');
        $query = MerchantService::generateMergeMerchantQuery($query, 'l.merchant', $this);
        return $query;
    }

    private function getTransactionQueryByMerchant () {
        $query = Util::em()->getRepository(Transaction::class)
            ->createQueryBuilder('t')
            ->where('t.status = :status')
            ->setParameter('status', TransactionStatus::get(TransactionStatus::NAME_COMPLETED));
        $query = MerchantService::generateMergeMerchantQuery($query, 't.merchant', $this);
        return $query;
    }

    public function getDetailsArray(bool $extra = FALSE)
    {
        $entity = $this;
        $admin = $entity->getAdminUser();
        $uc = $admin ? $admin->getCurrentPlatformCard() : null;
        $address = $entity->getAddress() ?? new Address();
        $merchantId = $entity->getId();
        $data = [
            'id' => $entity->getId(),
            'email' => $address->getEmail(),
            'Merchant ID' => $entity->getId(),
            'Merchant Name' => $entity->getName(),
            'Merchant Type' => $entity->getBusinessType(),
            'Email' => $address->getEmail(),
            'Status' => $entity->getOnboardingStatus(),
            'Approved' => $entity->isApproved(),
            'Doing Business As' => $entity->getDba(),
            'Business Street Address' => $address->getAddress1(),
            'City' => $address->getCity(),
            'Postal Code' => $address->getPostalCode(),
            'Country' => Util::field($address->getCountry()),
            'CountryId' => Util::field($address->getCountry(), 'id'),
            'State' => Util::field($address->getState(), 'id'),
            'StateName' => Util::field($address->getState()),
            'Phone' => $address->getPhone(),
            'Tax ID' => $entity->getTaxId(),
            'EIN' => $entity->getEin(),
            'Average Ticket Estimate Amount' => $entity->getAverageTicketEstimateAmount()
				? Money::formatAmountToNumber($entity->getAverageTicketEstimateAmount(), 'USD') : null,
            'Monthly Ticket Estimate Count' => $entity->getMonthlyTicketEstimateCount(),
            'Admin ID' => Util::field($admin, 'id'),
            'Last Login' => $admin ? $admin->getLastLogin(true) : null,
			'Created At' => Util::formatDateTime(
				$entity->getCreatedAt(),
				Util::DATE_TIME_FORMAT,
				Util::timezone()
			),
            'canConfigTip' => true,
            'canConfigBrazePush' => true,
            'canChangeStatus' => true,
            'needManuallyLoad' => Util::meta($entity, 'needManuallyLoad'),
            'Employee Subscribed' => Util::meta($entity, 'Employee Subscribed') ?? [],
			'Group' => $this->getgroup() ? $this->getgroup()->getName() : null,
            'Group ID' => $this->getGroup() ? $this->getGroup()->getId() : null
        ];

        if (MerchantService::isMergedIntoOtherMerchant($this)) {
            $data['canConfigTip'] = false;
            $data['canConfigBrazePush'] = false;
            $data['canChangeStatus'] = false;
        }

        if ($extra) {
            $balance = MerchantService::getMerchantBalance($this);
        	$extraData = [
				'Locations' => $this->getLocationQueryByMerchant()
					->select('count(distinct l)')
					->getQuery()
					->getSingleScalarResult(),
				'Balance' => Money::formatWhen($balance),
				'Transactions' => $this->getTransactionQueryByMerchant()
					->select('count(distinct t)')
					->getQuery()
					->getSingleScalarResult(),
			];
        	$data = array_merge($data, $extraData);
		}

        return $data;
    }

    public static function findByAdminUser(User $user)
	{
		$rs = Util::em()->getRepository(self::class)
			->createQueryBuilder('sm')
			->where('sm.adminUser = :user')
			->setParameter('user', $user)
			->setMaxResults(1)
			->getQuery()
			->getResult();
		if (!$rs) {
			return null;
		}
		return $rs[0];
	}

	/**
	 * Grower constructor.
	 */
	public function __construct()
	{
		parent::__construct();

		$this->setType(ClfMerchantType::get(ClfMerchantType::NAME_SPENDR_MERCHANT));
	}

	/**
	 * @var string
	 *
     * @ORM\Column(name="business_type", type="string", length=255, nullable=true)
	 */
	private $businessType;

	/**
	 * @var string
	 *
     * @ORM\Column(name="onboarding_status", type="string", length=255, nullable=true)
	 */
	private $onboardingStatus;

	/**
	 * @var string
	 *
	 * @ORM\Column(name="number", type="string", length=180, nullable=true)
	 */
	private $number;

	/**
	 * @var int
	 *
	 * @ORM\Column(name="average_ticket_estimate_amount", type="bigint", nullable=true)
	 */
	private $averageTicketEstimateAmount;

	/**
	 * @var int
	 *
	 * @ORM\Column(name="monthly_ticket_estimate_count", type="integer", nullable=true)
	 */
	private $monthlyTicketEstimateCount;

    /**
	 * @var SpendrGroup
	 *
	 * @ORM\ManyToOne(targetEntity="SpendrBundle\Entity\SpendrGroup")
	 */
	private $group;

	/**
	 * Set number
	 *
	 * @param string $number
	 *
	 * @return SpendrMerchant
	 */
	public function setNumber($number)
	{
		$this->number = $number;

		return $this;
	}

	/**
	 * Get number
	 *
	 * @return string
	 */
	public function getNumber()
	{
		return $this->number;
	}

    /**
     * Set onboardingStatus
     *
     * @param string $onboardingStatus
     *
     * @return SpendrMerchant
     */
    public function setOnboardingStatus($onboardingStatus)
    {
        $this->onboardingStatus = $onboardingStatus;

        return $this;
    }

    /**
     * Get onboardingStatus
     *
     * @return string
     */
    public function getOnboardingStatus()
    {
        return $this->onboardingStatus;
    }

    /**
     * Set businessType
     *
     * @param string $businessType
     *
     * @return SpendrMerchant
     */
    public function setBusinessType($businessType)
    {
        $this->businessType = $businessType;

        return $this;
    }

    /**
     * Get businessType
     *
     * @return string
     */
    public function getBusinessType()
    {
        return $this->businessType;
    }

    /**
     * Set averageTicketEstimateAmount
     *
     * @param string $averageTicketEstimateAmount
     *
     * @return SpendrMerchant
     */
    public function setAverageTicketEstimateAmount($averageTicketEstimateAmount)
    {
        $this->averageTicketEstimateAmount = $averageTicketEstimateAmount;

        return $this;
    }

    /**
     * Get averageTicketEstimateAmount
     *
     * @return string
     */
    public function getAverageTicketEstimateAmount()
    {
        return $this->averageTicketEstimateAmount;
    }

    /**
     * Set monthlyTicketEstimateCount
     *
     * @param string $monthlyTicketEstimateCount
     *
     * @return SpendrMerchant
     */
    public function setMonthlyTicketEstimateCount($monthlyTicketEstimateCount)
    {
        $this->monthlyTicketEstimateCount = $monthlyTicketEstimateCount;

        return $this;
    }

    /**
     * Get monthlyTicketEstimateCount
     *
     * @return string
     */
    public function getMonthlyTicketEstimateCount()
    {
        return $this->monthlyTicketEstimateCount;
    }

    /**
	 * Set group
	 *
	 * @param SpendrGroup $group
	 *
	 * @return SpendrMerchant
	 */
	public function setGroup(SpendrGroup $group = null)
	{
		$this->group = $group;

		return $this;
	}

	/**
	 * Get group
	 *
	 * @return SpendrGroup
	 */
	public function getGroup()
	{
		return $this->group;
	}
}
