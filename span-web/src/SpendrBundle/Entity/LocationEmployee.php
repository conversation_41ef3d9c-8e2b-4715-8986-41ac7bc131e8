<?php

namespace SpendrBundle\Entity;


use Api<PERSON><PERSON>le\Entity\ApiEntityInterface;
use CoreBundle\Entity\BaseEntity;
use Doctrine\ORM\Mapping as ORM;
use SalexUserBundle\Entity\User;
use CoreBundle\Utils\Util;

/**
 * LocationEmployee
 *
 * @ORM\Table(name="spendr_location_employee", options={"comment":"Used to represent the relationship between the user of the 'merchant manager/merchant assistant manager/clerk' role and the location"})
 * @ORM\Entity(repositoryClass="SpendrBundle\Repository\LocationEmployeeRepository")
 */
class LocationEmployee extends BaseEntity implements ApiEntityInterface
{
    public static function getMerchantForUser(User $user)
    {
        $rs = Util::em()->getRepository(self::class)
            ->createQueryBuilder('le')
            ->where('le.user = :user')
            ->setParameter('user', $user)
            ->setMaxResults(1)
            ->getQuery()
            ->getResult();
        if (!$rs) {
            return null;
        }
        /** @var static $le */
        $le = $rs[0];
        return $le->getMerchant();
    }

    public function toApiArray(bool $extra = false): array
    {
        return [
            'id' => $this->getId(),
            'user' => $this->getUser()->toApiArray(),
            'location' => $this->getLocation()->toApiArray(),
            'merchant' => $this->getMerchant()->toApiArray(),
            'pin' => $this->getPin()
        ];
    }

    /**
     * @param $id
     * @return Location|null
     */
    public static function find($id)
    {
        return Util::em()->getRepository(self::class)->find($id);
    }

	/**
	 * @ORM\ManyToOne(targetEntity="SalexUserBundle\Entity\User")
	 * @ORM\JoinColumn(name="user_id", referencedColumnName="id")
	 */
	private $user;

	/**
	 * @ORM\ManyToOne(targetEntity="SpendrBundle\Entity\Location")
	 * @ORM\JoinColumn(name="location_id", referencedColumnName="id")
	 */
	private $location;

	/**
	 * @ORM\ManyToOne(targetEntity="SpendrBundle\Entity\SpendrMerchant")
	 * @ORM\JoinColumn(name="merchant_id", referencedColumnName="id")
	 */
	private $merchant;

    /**
     * @var string
     *
     * @ORM\Column(name="pin", type="string", nullable=true, length=180)
     */
    private $pin;

    /**
     * Set user
     *
     * @param User $user
     *
     * @return LocationEmployee
     */
    public function setUser(User $user)
    {
        $this->user = $user;

        return $this;
    }

    /**
     * Get user
     *
     * @return User
     */
    public function getUser()
    {
        return $this->user;
    }

    /**
     * Set location
     *
     * @param Location $location
     *
     * @return LocationEmployee
     */
    public function setLocation(Location $location)
    {
        $this->location = $location;

        return $this;
    }

    /**
     * Get location
     *
     * @return Location
     */
    public function getLocation()
    {
        return $this->location;
    }

    /**
     * Set merchant
     *
     * @param SpendrMerchant $merchant
     *
     * @return LocationEmployee
     */
    public function setMerchant(SpendrMerchant $merchant)
    {
        $this->merchant = $merchant;

        return $this;
    }

    /**
     * Get merchant
     *
     * @return SpendrMerchant
     */
    public function getMerchant()
    {
        return $this->merchant;
    }


    /**
     * Set pin
     *
     * @param string $pin
     *
     * @return LocationEmployee
     */
    public function setPin($pin)
    {
        $this->pin = $pin;

        return $this;
    }

    /**
     * Get pin
     *
     * @return string
     */
    public function getPin()
    {
        return $this->pin;
    }
}
