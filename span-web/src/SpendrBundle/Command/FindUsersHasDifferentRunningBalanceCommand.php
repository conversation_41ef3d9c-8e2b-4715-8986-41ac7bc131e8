<?php

namespace Spendr<PERSON><PERSON>le\Command;

use CoreBundle\Command\BaseCommand;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Util;
use SalexUserBundle\Entity\User;
use SpendrBundle\Controller\Admin\MemberBalanceReportController;
use SpendrBundle\Services\SlackService;
use SpendrBundle\Services\UserService;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\HttpFoundation\Request;
use UsUnlockedBundle\Services\Privacy\PrivacyAPI;
use CoreBundle\Utils\Log;

class FindUsersHasDifferentRunningBalanceCommand extends BaseCommand
{
	protected $singletonMinutes = null;
    protected function configure()
    {
        $this
            ->setName('span:spendr:find-users-diff-running-balance')
            ->setDescription('Find users have different running balance compared to the account balance')
            ->addOption('test', null, InputOption::VALUE_NONE, 'Do not alert in Slack')
            ->addOption('balanceAdminType', null, InputOption::VALUE_OPTIONAL, 'Check the specified "Balance Admin", including Spendr, Bank, Tern')
            ->addOption('userId', null, InputOption::VALUE_OPTIONAL, 'User ID')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        Util::$platform = Platform::spendr();
        Util::$cardProgram = CardProgram::spendr();

        $this->prepare($input, $output, true);

        Util::longRequest(30000, Util::isLive() ? '16384M' : '1024M');

        $exceptions = [
            -1,
			*********, // vitta
			*********, // tracy
        ];
		$spendrBalanceAdmin = UserService::getSpendrBalanceAdminAccount('spendr');
		if ($spendrBalanceAdmin) {
			$exceptions[] = $spendrBalanceAdmin->getId();
		}

        $expr = Util::expr();

        $users = [];
		$balanceAdminType = $input->getOption('balanceAdminType');
        $userId = $input->getOption('userId');
        if (!$balanceAdminType) {
			$query = Util::em()->getRepository(User::class)
				->createQueryBuilder('u')
				->join('u.cards', 'uc')
				->join('uc.loads', 'ucl')
				->join('uc.card', 'c')
				->join('c.cardProgram', 'cp')
				->where('uc.type = :type')
				->andWhere($expr->in('ucl.loadStatus', ':loadStatus'))
				->andWhere(Util::expr()->notIn('u.id', ':exceptions'))
				->andWhere('cp.platform = :platform')
				->setParameter('platform', Platform::spendr())
				->setParameter('type', PrivacyAPI::CARD_TYPE_DUMMY)
				->setParameter('loadStatus', [
					UserCardLoad::LOAD_STATUS_LOADED,
					UserCardLoad::LOAD_STATUS_ERROR,
				])
				->setParameter('exceptions', $exceptions);

            if ($userId) {
                $query->andWhere('u.id = :userId')
                    ->setParameter('userId', $userId);
            }

			$users = $query->distinct()
				->getQuery()
				->getResult();
		} else {
			$balanceAdmin = UserService::getSpendrBalanceAdminAccount($balanceAdminType);
			array_push($users, $balanceAdmin);
		}

        $count = count($users);
        $this->line('found ' . $count . ' users');

        /** @var MemberBalanceReportController $ctrl */
        $ctrl = Util::get('app.spendr.member_balance_report_controller');
        $request = Request::createFromGlobals();
        $request->query->set('mode', 'force');
        $userIds = [];

        /** @var User $user */
        foreach ($users as $i => $user) {
            if (($i + 1) % 100 === 0) {
                $this->line('checked ' . ($i + 1) . '/' . $count . ' users...');
            }
//            Log::debug('Get Dummy Card for spendr User with diff balance');
            $uc = UserService::getDummyCard($user);
            $request->request->set('memberID', $user->getId());
            $request->request->set('balanceAdminType', $balanceAdminType);
            $items = $ctrl->index($request, true);
            if (!$items) {
                continue;
            }
            foreach ($items as $item) {
                if (!array_key_exists('Running Balance', $item)) {
                    continue;
                }
                $balance = Money::format($uc->getBalance(), 'USD', false);
                if ($item['Running Balance'] !== $balance) {
                    $this->line('User ' . $user->getId() . '\'s account balance '
                                . $balance . ' differs from running balance ' . $item['Running Balance']);

                    $accountBalance = Money::normalizeAmountFromUsdFormat($balance);
                    $runningBalance = Money::normalizeAmountFromUsdFormat($item['Running Balance']);
                    $userIds[$user->getId()] = [
                        'account' => $accountBalance,
                        'running' => $runningBalance,
                        'diff' => $runningBalance - $accountBalance,
                    ];
                }
                break;
            }
        }

//        Data::setArray('unmatched_account_running_balance_users_detail', $userIds);

        if (!$input->getOption('test')) {
            if ($userIds) {
                $alerts = [];
                foreach ($userIds as $userId => $d) {
                    $alerts[$userId] = 'deviation: ' . Money::format($d['diff'], 'USD');
                }
                SlackService::warning(
                	'Below users have unmatched account & running balance.',
					$alerts,
					SlackService::GROUP_DEVS
				);
            } else {
                SlackService::check('All accounts have matched account & running balance.');
            }
        }

        $this->done();
        return 0;
    }

}
