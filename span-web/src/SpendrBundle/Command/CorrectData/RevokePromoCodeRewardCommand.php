<?php


namespace Spendr<PERSON><PERSON>le\Command\CorrectData;


use Carbon\Carbon;
use CoreBundle\Command\BaseCommand;
use Core<PERSON>undle\Entity\CardProgram;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Utils\Queue;
use CoreB<PERSON>le\Utils\Util;
use SpendrB<PERSON>le\Entity\PromotionCode;
use SpendrBundle\Services\LoadService;
use SpendrBundle\SpendrBundle;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class RevokePromoCodeRewardCommand extends BaseCommand
{
    protected $singletonMinutes = null;
    protected function configure()
    {
        $this->setName('span:spendr:revoke-promo-code-reward')
            ->setDescription('Revoke the consumer promo code rewards who earned it but not purchased before the limit time')
            ->addOption('test', null, InputOption::VALUE_NONE, 'test')
            ->addOption('reward', null, InputOption::VALUE_REQUIRED, 'Reward id')
            ->addOption('date', null, InputOption::VALUE_OPTIONAL, 'limit time');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $this->prepare($input, $output, true);
        if (!$input->getOption('reward')) {
            $this->line('The reward id is required');
            $this->done();
            return 0;
        }
        /** @var PromotionCode $reward */
        $reward = Util::em()->find(PromotionCode::class, $input->getOption('reward'));
        if (!$reward) {
            $this->line('The reward is not exists');
            $this->done();
            return 0;
        }
        $date = $input->getOption('date') ?? Carbon::now()->toDateTimeString();
        $redeemed = Util::em()->getConnection()
            ->executeQuery('
                select distinct t.consumer_id from clf_transaction t
                join user_card_load ucl on t.consumer_id = ucl.create_by
                WHERE ucl.promo_code_id = ' . $reward->getId() . '
                AND t.txn_time > ucl.load_at
                AND t.txn_time < "' . $date . '"
                AND t.type_id = 3
                AND t.status_id = 2')
            ->fetchAllAssociative();
        $ids = array_column($redeemed, 'consumer_id');
        $this->line('The purchased consumer count: ' . count($ids));

        $loads = Util::em()->getRepository(UserCardLoad::class)
            ->createQueryBuilder('ucl')
            ->join('ucl.userCard', 'c')
            ->join('c.card', 'cpct')
            ->where(Util::expr()->eq('cpct.cardProgram',':cardProgram'))
            ->andWhere('ucl.type = :type')
            ->andWhere('ucl.status = :status')
            ->andWhere('ucl.loadStatus = :loadStatus')
            ->andWhere('ucl.promoCode = :promo')
            ->setParameter('cardProgram', CardProgram::spendr())
            ->setParameter('type', UserCardLoad::TYPE_LOAD_CARD)
            ->setParameter('status', UserCardLoad::STATUS_COMPLETED)
            ->setParameter('loadStatus', UserCardLoad::LOAD_STATUS_LOADED)
            ->setParameter('promo', $reward)
            ->getQuery()
            ->getResult();
        $this->line('The earned consumer count: ' . count($loads));
        $revoke = [];
        /** @var UserCardLoad $load */
        foreach ($loads as $load) {
            $uid = $load->getUserCard()->getUser()->getId();
            if (in_array($uid, $ids)) continue;
            if (!$input->getOption('test')) {
                $checkRes = LoadService::beforeCancelLoad($load);
                if (is_string($checkRes)) {
                    $this->line('cannot revoke ' . $load->getId() . ' because: ' . $checkRes);
                    continue;
                }

                Queue::spendrCancelLoad($load->getId(), 'Not purchased before end time');
            }
            array_push($revoke, $uid);
        }

        $this->line(implode(',', $revoke));
        $this->done();
        return 0;
    }
}
