<?php

namespace ClfBundle\Entity;

use ClfBundle\Entity\Common\EntityTrait;
use Doctrine\ORM\Mapping as ORM;

/**
 * TransactionType
 *
 * @ORM\Table(name="clf_transaction_type")
 * @ORM\Entity(repositoryClass="ClfBundle\Repository\TransactionTypeRepository")
 */
class TransactionType
{
    use EntityTrait;

    const NAME_LOAD = 'Account Load';
    const NAME_UNLOAD = 'Account Unload';
    const NAME_PURCHASE = 'In-store Purchase';
    const NAME_REFUND = 'In-store Refund';
    const NAME_DAILY_REVENUE_SETTLEMENT = 'Daily Revenue Settlement';
    const NAME_PAYROLL_DEDUCTION = 'Payroll Deduction';
    const NAME_INVOICE_INCOME = 'Invoice Income';
    const NAME_INVOICE_OUTGO = 'Invoice Outgo';
    const NAME_REWARD = 'Account Reward';

    public function isMinusForMerchant()
    {
        return in_array($this->getName(), [
            self::NAME_UNLOAD,
            self::NAME_REFUND,
            self::NAME_PAYROLL_DEDUCTION,
            self::NAME_INVOICE_OUTGO,
        ]);
    }

    public static function allTypes ()
	{
		return [
			'load' => self::NAME_LOAD,
			'unload' => self::NAME_UNLOAD,
			'purchase' => 'Payment',
			'refund' => 'Refund',
			'daily_revenue_settlement' => self::NAME_DAILY_REVENUE_SETTLEMENT,
			'payroll_deduction' => self::NAME_PAYROLL_DEDUCTION,
			'invoice_income' => self::NAME_INVOICE_INCOME,
			'invoice_outgo' => self::NAME_INVOICE_OUTGO,
			'reward' => self::NAME_REWARD,
		];
	}

	public function displayName ()
	{
		$typeName = null;
    	switch ($this->getName()) {
			case self::NAME_LOAD:
				$typeName = 'Deposit';
				break;
			case self::NAME_UNLOAD:
				$typeName = 'Unload';
				break;
			case self::NAME_PURCHASE:
				$typeName = 'Payment';
				break;
			case self::NAME_REFUND:
				$typeName = 'Refund';
				break;
			case self::NAME_REWARD:
				$typeName = 'Reward';
				break;
		}
		return $typeName;
	}

    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @var string
     *
     * @ORM\Column(name="name", type="string", length=180)
     */
    private $name;

    /**
     * @var string
     *
     * @ORM\Column(name="description", type="string", length=255, nullable=true)
     */
    private $description;


    /**
     * Get id
     *
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set name
     *
     * @param string $name
     *
     * @return TransactionType
     */
    public function setName($name)
    {
        $this->name = $name;

        return $this;
    }

    /**
     * Get name
     *
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * Set description
     *
     * @param string $description
     *
     * @return TransactionType
     */
    public function setDescription($description)
    {
        $this->description = $description;

        return $this;
    }

    /**
     * Get description
     *
     * @return string
     */
    public function getDescription()
    {
        return $this->description;
    }
}

