<?php

namespace ClfBundle\Entity;

use ApiB<PERSON>le\Entity\ApiEntityInterface;
use Carbon\Carbon;
use CoreBundle\Entity\BaseState;
use CoreBundle\Entity\Currency;
use CoreBundle\Entity\UserCard;
use CoreB<PERSON>le\Entity\UserCardLoad;
use CoreBundle\Entity\UserCardTransaction;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Util;
use Doctrine\ORM\Mapping as ORM;
use SalexUserBundle\Entity\User;
use SpendrBundle\Entity\Location;
use SpendrBundle\Entity\SpendrTip;
use SpendrBundle\Entity\Terminal;
use SpendrBundle\Entity\TransactionMeta;
use SpendrBundle\Services\Pay\PayService;
use SpendrBundle\Services\ConsumerService;

/**
 * Transaction
 *
 * @ORM\Table(name="clf_transaction")
 * @ORM\Entity(repositoryClass="ClfBundle\Repository\TransactionRepository")
 */
class Transaction implements ApiEntityInterface
{
    const TXN_AREA_TYPE_RESTRICT = 'restrict';
    const TXN_AREA_TYPE_NON_RESTRICT = 'non-restrict';

    public function toApiArray(bool $extra = FALSE): array
    {
        $all = [
            'id' => $this->getId(),
            'date' => Util::formatDateTime($this->getDateTime()),
            'txnDateTime' => Util::formatDateTime($this->getTxnTime()),
            'txnTime' => $this->getTxnTime(),
            'description' => $this->getDescription(),
            'subDescription' => $this->getSubDescription(),
            'type' => $this->getTypeName(),
            'typeDisplayName' => $this->getTypeName(),
            'status' => Util::field($this->getStatus()),
			'location' => $this->getLocation() ? $this->getLocation()->getName() : null,
			'locationEmployee' => $this->getLocationEmployee() ? $this->getLocationEmployee()->getFullName() : null,
			'terminal' => $this->getTerminal() ? $this->getTerminal()->getName() : null,
			'consumer' => $this->getConsumer() ? $this->getConsumer()->getFullName() : null,
			'orderId' => sprintf("%s", $this->getOrderId() ? $this->getOrderId() : $this->getId()),
			'spendrFee' => Money::format($this->getSpendrFee(), $this->getCurrencyCode(), false),
			'ternFee' => Money::format($this->getTernFee(), $this->getCurrencyCode(), false),
			'RefundFee' => Money::format($this->getRefundFee(), $this->getCurrencyCode(), false),
            'fromPayApi' => false
        ];
		$platform = Util::platform();
        $amount = Money::format($this->getAmount(), $this->getCurrencyCode(), false);
        $status = $this->getStatus()->getName();
        $type = $this->getType();
        if ($status === TransactionStatus::NAME_PENDING) {
            $all['estArrival'] = Util::formatDate(Carbon::instance($this->getDateTime())->addDays(2));
            $all = array_merge($all, [
                'amount' => $amount,
                'status' => $platform && $platform->isSpendr() ? TransactionStatus::NAME_PENDING : 'Estimated Arrival ' . $all['estArrival'],
            ]);
        } else if ($status === TransactionStatus::NAME_CANCELLED) {
            $all = array_merge($all, [
                'amount' => $amount,
                'status' => TransactionStatus::NAME_CANCELLED,
            ]);
        } else if ($status === TransactionStatus::NAME_COMPLETED) {
            $all = array_merge($all, [
                'debit' => $type->isOneOf([
                    TransactionType::NAME_UNLOAD,
                    TransactionType::NAME_PURCHASE,
                ]) ? $amount : '',
                'credit' => $type->isOneOf([
                    TransactionType::NAME_LOAD,
                    TransactionType::NAME_REFUND,
                ]) ? $amount : '',
                'balance' => Money::format($this->getConsumerBalanceAfter(), $this->getCurrencyCode(), false),
				'amount' => $amount, // For mobile app front-end use
            ]);
        }

        $type = $type->getName();
        if (in_array($type, [
            TransactionType::NAME_LOAD,
            TransactionType::NAME_UNLOAD,
        ])) {
            Util::disableSoftDeletable();
            $card = $this->getCard();
            $all['card'] = $card ? $card->getName() : '';
            Util::enableSoftDeletable();
        }

        if ($platform && $platform->isSpendr()) {
			$all['canRefund'] = false;
			if (
				$status === TransactionStatus::NAME_COMPLETED
				&& in_array($type, [
					TransactionType::NAME_PURCHASE
				])
				&& !$this->getRefundFrom()
			) {
				$all['canRefund'] = true;
			}

            $needShowBankFundsAndRewardFunds = $this->needShowBankFundsAndRewardFunds();
            $bankFunds = 0;
            $rewardFunds = 0;
            if ($needShowBankFundsAndRewardFunds) {
                $bankFunds = $this->getBankFunds();
                $rewardFunds = $this->getRewardFunds();
            }

            $all['paymentSource'] = $this->getPaymentSource($needShowBankFundsAndRewardFunds, $bankFunds, $rewardFunds);
            $all['disclaimer'] = $this->getDisclaimer();

			$all['notifyMsg'] = $all['typeDisplayName'] . ' ' . $all['status'];
			$all['displayList'] = [
				[
					'title' => 'Payment Source',
					'value' => $all['paymentSource'],
				],
				[
					'title' => 'Store Name',
					'value' => $all['location'],
				],
			];
			$all['fromPayApi'] = $this->isFromPayApi();
			if (!$all['fromPayApi']) {
			    $all['displayList'][] = [
                    'title' => 'Clerk Name',
                    'value' => $all['locationEmployee'],
                ];
            } else {
			    if ($this->getTm()) {
			        $all['redirect_url'] = $this->getTm()->getRedirectUrl();
                }
            }
            $all['paymentAmount'] = $amount;
			if ($this->getTip() && $status !== TransactionStatus::NAME_PENDING) {
                $all['displayList'][] = [
                    'title' => 'Subtotal',
                    'value' => $amount,
                ];
                $tipAmount = Money::format($this->getTip()->getAmount(), 'USD', false);
                $amount = Money::format($this->getAmount() + $this->getTip()->getAmount(), 'USD', false);
                $all['tipAmount'] = $tipAmount;
                $all['amount'] = $amount;
                $all['displayList'][] = [
                    'title' => 'Tip Amount',
                    'value' => $tipAmount,
                ];
            }
 			$all['displayList'][] = [
                'title' => 'Payment Total',
                'value' => $amount,
            ];

            if ($needShowBankFundsAndRewardFunds) {
                if ($bankFunds > 0) {
                    $all['displayList'][] = [
                        'title' => 'Bank Funds',
                        'value' => Money::format($bankFunds, 'USD', false)
                    ];
                }

                if ($rewardFunds > 0) {
                    $all['displayList'][] = [
                        'title' => 'Reward Funds',
                        'value' => Money::format($rewardFunds, 'USD', false)
                    ];
                }
            }
		}

        if ($extra) {
        	$all['token'] = $this->getToken();
		}

        return $all;
    }

    public function needShowBankFundsAndRewardFunds()
    {
        if (
            $this->getStatus()->getName() === TransactionStatus::NAME_COMPLETED
            && $this->getConsumer()
            && (
                $this->getTxnAreaType() === self::TXN_AREA_TYPE_RESTRICT || (
                    $this->isFromPayApi() &&
                    $this->getRestrictLoad() &&
                    $this->getAmount() !== $this->getRestrictLoad()->getInitialAmount()
                )
            )
        ) {
            return true;
        }
        return false;
    }

    public function getBankFunds()
    {
        $restrictLoad = $this->getRestrictLoad();
        $bankFunds = $restrictLoad ? $restrictLoad->getInitialAmount() : 0;
        return $bankFunds;
    }

    public function getRewardFunds()
    {
        $bankFunds = $this->getBankFunds();

        $amount = $this->getAmount();
        $tip = $this->getTip();
        $totalAmount = $tip ? $amount + $tip->getAmount() : $amount;

        $rewardFunds = $totalAmount - $bankFunds;

        return $rewardFunds;
    }

    public function getPaymentSource($needShowBankFundsAndRewardFunds = false, $bankFunds = 0, $rewardFunds = 0)
    {
        $paymentSource = 'Spendr Balance';
        if ($needShowBankFundsAndRewardFunds) {
            if ($rewardFunds > 0) {
                // continue
            } else {
                if ($bankFunds > 0) {
                    $restrictLoad = $this->getRestrictLoad();
                    $ucTxn = UserCardTransaction::findByTranId($restrictLoad->getTransactionNo());
                    if ($ucTxn) {
                        Util::disableSoftDeletable();
                        $bankCard = $ucTxn->getUserCard();
                        $mask = Util::meta($bankCard, 'mask');
                        $bankName = $bankCard->getBankName();
                        $paymentSource = $bankName . '(***'. $mask .')';
                        Util::enableSoftDeletable();
                    }
                }
            }
        }

        return $paymentSource;
    }

    public function getDisclaimer()
    {
        if ($this->getStatus()->getName() === TransactionStatus::NAME_COMPLETED) {
            // show disclaimer if the location state is new york
            $newYork = BaseState::find(3998);
            if ($this->getLocation() &&
                $this->getLocation()->getAddress() &&
                $this->getLocation()->getAddress()->getState() === $newYork
            ) {
                return 'Payment processed by Spendr as agent of payee. Payments processed by Spendr are deemed received by merchant at the time of processing.';
            }
        }
        return null;
    }

    public function getDescription()
    {
        $type = $this->getType();
        if (!$type) {
            return '';
        }
        if ($type->isOneOf([
            TransactionType::NAME_LOAD,
            TransactionType::NAME_UNLOAD,
        ])) {
            return $type->getName();
        }
        if ($type->isOneOf([
            TransactionType::NAME_PURCHASE,
            TransactionType::NAME_REFUND,
        ])) {
            $merchant = $this->getMerchant();
            return $merchant ? $merchant->getName() : '';
        }
        return $type->getName();
    }

    public function getSubDescription()
    {
        $type = $this->getType();
        if (!$type) {
            return null;
        }
        if ($type->isOneOf([
            TransactionType::NAME_PURCHASE,
            TransactionType::NAME_REFUND,
        ])) {
            return $type->getName();
        }
        return null;
    }

    public function getCurrencyCode()
    {
        $currency = $this->getCurrency();
        return $currency ? $currency->getCurCode() : 'USD';
    }

    public function getSignedAmount()
    {
        $amount = $this->getAmount();
        $sign = $this->getType()->isMinusForMerchant() ? -1 : 1;
        return $sign * $amount;
    }

    public function isFromPayApi()
    {
        $terminal = $this->getTerminal();
        $deviceId = $terminal ? $terminal->getDeviceId() : '';
        return Util::startsWith($deviceId, PayService::DUMMY_TERMINAL_PREFIX);
    }

    public function getTypeName($display = false)
	{
        $api = $this->isFromPayApi();
        $prefix = $api ? 'Online ' : '';

        $type = $this->getType();
		$typeName = $type->getName();
		if ($typeName === TransactionType::NAME_PURCHASE) {
			return $prefix . 'Payment';
		}
        if ($typeName === TransactionType::NAME_REFUND) {
			return $prefix . 'Refund';
		}
        if ($display) {
            return Util::ensurePrefix($type->displayName(), $prefix);
        }
		return Util::ensurePrefix($typeName, $prefix);
	}

    public function getTypeDisplayName()
    {
        return $this->getTypeName(true);
    }

	public function isRefund()
    {
        return $this->getType()->getName() === TransactionType::NAME_REFUND;
    }

	/**
	 * @param $token
	 *
	 * @return Transaction|null
	 */
	public static function findByToken($token)
	{
		$rs = Util::em()->getRepository(self::class)
			->createQueryBuilder('t')
			->where('t.token = :token')
			->setParameter('token', $token)
			->setMaxResults(1)
			->getQuery()
			->getResult();
		if (!$rs) {
			return null;
		}
		return $rs[0];
	}

	/**
	 * @param $id
	 * @return static
	 */
	public static function find($id)
	{
		return Util::em()->getRepository(self::class)->find($id);
	}

    /**
     * Transaction constructor.
     */
    public function __construct()
    {
        $this->setDateTime(new \DateTime())
        ->setStatus(TransactionStatus::get(TransactionStatus::NAME_PENDING))
        ->setApp(App::default())
        ->setCurrency(Currency::find('USD'))
        ->setToken(Util::guid());
    }

    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="date_time", type="datetime")
     */
    private $dateTime;

    /**
     * @var string
     *
     * @ORM\Column(name="order_id", type="string", length=255, nullable=true)
     */
    private $orderId;

    /**
     * @var string
     *
     * @ORM\Column(name="order_description", type="string", length=255, nullable=true)
     */
    private $orderDescription;

    /**
     * @var TransactionType
     *
     * @ORM\ManyToOne(targetEntity="ClfBundle\Entity\TransactionType")
     */
    private $type;

    /**
     * @var TransactionStatus
     *
     * @ORM\ManyToOne(targetEntity="ClfBundle\Entity\TransactionStatus")
     */
    private $status;

    /**
     * @var Merchant
     *
     * @ORM\ManyToOne(targetEntity="ClfBundle\Entity\Merchant")
     */
    private $merchant;

    /**
     * @var string
     *
     * @ORM\Column(name="merchant_terminal", type="string", length=5, nullable=true)
     */
    private $merchantTerminal;

    /**
     * @var Terminal
     *
     * @ORM\ManyToOne(targetEntity="SpendrBundle\Entity\Terminal")
     */
    private $terminal;

    /**
     * @var Location
     *
     * @ORM\ManyToOne(targetEntity="SpendrBundle\Entity\Location")
     */
    private $location;

    /**
     * @var MerchantEmployee
     *
     * @ORM\ManyToOne(targetEntity="ClfBundle\Entity\MerchantEmployee")
     */
    private $merchantEmployee;

    /**
	 * Location employee of Spendr platform
	 *
     * @var User
     *
     * @ORM\ManyToOne(targetEntity="SalexUserBundle\Entity\User")
     */
    private $locationEmployee;

    /**
     * @var Bank
     *
     * @ORM\ManyToOne(targetEntity="ClfBundle\Entity\Bank")
     */
    private $merchantBank;

    /**
     * @var Transaction
     *
     * @ORM\ManyToOne(targetEntity="ClfBundle\Entity\Transaction")
     */
    private $refundFrom;

    /**
     * @var User
     *
     * @ORM\ManyToOne(targetEntity="SalexUserBundle\Entity\User")
     */
    private $consumer;

    /**
     * @var Account
     *
     * @ORM\ManyToOne(targetEntity="ClfBundle\Entity\Account")
     */
    private $account;

    /**
     * @var Bank
     *
     * @ORM\ManyToOne(targetEntity="ClfBundle\Entity\Bank")
     */
    private $accountBank;

    /**
     * @var Currency
     *
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\Currency")
     * @ORM\JoinColumn(name="currency", referencedColumnName="cur_code", onDelete="set null")
     */
    private $currency;

    /**
     * @var Card
     *
     * @ORM\ManyToOne(targetEntity="ClfBundle\Entity\Card")
     */
    private $card;

    /**
     * @var App
     *
     * @ORM\ManyToOne(targetEntity="ClfBundle\Entity\App")
     */
    private $app;

    /**
     * @var int
     *
     * @ORM\Column(name="amount", type="bigint", nullable=true)
     */
    private $amount;

    /**
     * @var int
     *
     * @ORM\Column(name="consumer_balance_before", type="bigint", nullable=true)
     */
    private $consumerBalanceBefore;

    /**
     * @var int
     *
     * @ORM\Column(name="consumer_balance_after", type="bigint", nullable=true)
     */
    private $consumerBalanceAfter;

    /**
     * @var int
     *
     * @ORM\Column(name="producer_balance_before", type="bigint", nullable=true)
     */
    private $producerBalanceBefore;

    /**
     * @var int
     *
     * @ORM\Column(name="producer_balance_after", type="bigint", nullable=true)
     */
    private $producerBalanceAfter;

    /**
     * @var string
     *
     * @ORM\Column(name="token", type="string", length=255, nullable=true)
     */
    private $token;

    /**
     * @var Invoice
     *
     * @ORM\ManyToOne(targetEntity="ClfBundle\Entity\Invoice")
     */
    private $invoice;

	/**
	 * The id used to communicate with third parties during payment, avoid using real bank accounts
	 *
	 * @var string
	 *
	 * @ORM\Column(name="consumer_bank_account_id", type="string", length=255, nullable=true)
	 */
	private $consumerBankAccountId;

	/**
	 * The id used to communicate with third parties during payment, avoid using real bank accounts
	 *
	 * @var string
	 *
	 * @ORM\Column(name="producer_bank_account_id", type="string", length=255, nullable=true)
	 */
	private $producerBankAccountId;

	/**
	 * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\UserCard")
	 * @ORM\JoinColumn(name="user_card_id", referencedColumnName="id", onDelete="cascade")
	 */
	private $userCard;

	/**
	 * @var int
	 *
	 * @ORM\Column(name="spendr_fee", type="bigint", nullable=true)
	 */
	private $spendrFee;

	/**
	 * @var int
	 *
	 * @ORM\Column(name="tern_fee", type="bigint", nullable=true)
	 */
	private $ternFee;

	/**
	 * @var int
	 *
	 * @ORM\Column(name="refund_fee", type="bigint", nullable=true, options={"comment":"When refunding, the Spendr needs to refund part of the transaction fee to the merchant."})
	 */
	private $refundFee;

	/**
	 * @var string
	 * @ORM\Column(name="meta", type="text", nullable=true)
	 */
	private $meta;

	/**
	 * @var \DateTime
	 *
	 * @ORM\Column(name="txn_time", type="datetime", nullable=true)
	 */
	private $txnTime;

    /**
     * @var SpendrTip
     * @ORM\OneToOne(targetEntity="SpendrBundle\Entity\SpendrTip", inversedBy="transaction")
     * @ORM\JoinColumn(name="tip_id", referencedColumnName="id", nullable=true)
     */
	private $tip;

    /**
     * @var UserCardLoad
     * @ORM\OneToOne(targetEntity="CoreBundle\Entity\UserCardLoad")
     * @ORM\JoinColumn(name="restrict_load_id", referencedColumnName="id", nullable=true)
     */
    private $restrictLoad;

    /**
     * @var string
     *
     * @ORM\Column(name="txn_area_type", type="string", length=255, nullable=true, options={"comment":"restrict, non-restrict"})
     */
    private $txnAreaType;

    /**
     * @var TransactionMeta
     *
     * @ORM\OneToOne(targetEntity="SpendrBundle\Entity\TransactionMeta", mappedBy="transaction")
     */
    protected $tm;

	/**
     * Get id
     *
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set dateTime
     *
     * @param \DateTime $dateTime
     *
     * @return Transaction
     */
    public function setDateTime($dateTime)
    {
        $this->dateTime = $dateTime;

        return $this;
    }

    /**
     * Get dateTime
     *
     * @return \DateTime
     */
    public function getDateTime()
    {
        return $this->dateTime;
    }

    /**
     * Set orderId
     *
     * @param string $orderId
     *
     * @return Transaction
     */
    public function setOrderId($orderId)
    {
        $this->orderId = $orderId;

        return $this;
    }

    /**
     * Get orderId
     *
     * @return string
     */
    public function getOrderId()
    {
        return $this->orderId;
    }

    /**
     * Set orderDescription
     *
     * @param string $orderDescription
     *
     * @return Transaction
     */
    public function setOrderDescription($orderDescription)
    {
        $this->orderDescription = $orderDescription;

        return $this;
    }

    /**
     * Get orderDescription
     *
     * @return string
     */
    public function getOrderDescription()
    {
        return $this->orderDescription;
    }

    /**
     * Set merchantTerminal
     *
     * @param string $merchantTerminal
     *
     * @return Transaction
     */
    public function setMerchantTerminal($merchantTerminal)
    {
        $this->merchantTerminal = $merchantTerminal;

        return $this;
    }

    /**
     * Get merchantTerminal
     *
     * @return string
     */
    public function getMerchantTerminal()
    {
        return $this->merchantTerminal;
    }

    /**
     * Set amount
     *
     * @param integer $amount
     *
     * @return Transaction
     */
    public function setAmount($amount)
    {
        $this->amount = $amount;

        return $this;
    }

    /**
     * Get amount
     *
     * @return int
     */
    public function getAmount()
    {
        return $this->amount;
    }

    /**
     * Set token
     *
     * @param string $token
     *
     * @return Transaction
     */
    public function setToken($token)
    {
        $this->token = $token;

        return $this;
    }

    /**
     * Get token
     *
     * @return string
     */
    public function getToken()
    {
        return $this->token;
    }

    /**
     * Set type
     *
     * @param \ClfBundle\Entity\TransactionType $type
     *
     * @return Transaction
     */
    public function setType(\ClfBundle\Entity\TransactionType $type = null)
    {
        $this->type = $type;

        return $this;
    }

    /**
     * Get type
     *
     * @return \ClfBundle\Entity\TransactionType
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * Set status
     *
     * @param \ClfBundle\Entity\TransactionStatus $status
     *
     * @return Transaction
     */
    public function setStatus(\ClfBundle\Entity\TransactionStatus $status = null)
    {
        $this->status = $status;

        return $this;
    }

    /**
     * Get status
     *
     * @return \ClfBundle\Entity\TransactionStatus
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * Set merchant
     *
     * @param \ClfBundle\Entity\Merchant $merchant
     *
     * @return Transaction
     */
    public function setMerchant(\ClfBundle\Entity\Merchant $merchant = null)
    {
        $this->merchant = $merchant;

        return $this;
    }

    /**
     * Get merchant
     *
     * @return \ClfBundle\Entity\Merchant
     */
    public function getMerchant()
    {
        return $this->merchant;
    }

    /**
     * Set merchantBank
     *
     * @param \ClfBundle\Entity\Bank $merchantBank
     *
     * @return Transaction
     */
    public function setMerchantBank(\ClfBundle\Entity\Bank $merchantBank = null)
    {
        $this->merchantBank = $merchantBank;

        return $this;
    }

    /**
     * Get merchantBank
     *
     * @return \ClfBundle\Entity\Bank
     */
    public function getMerchantBank()
    {
        return $this->merchantBank;
    }

    /**
     * Set consumer
     *
     * @param User $consumer
     *
     * @return Transaction
     */
    public function setConsumer(User $consumer = null)
    {
        $this->consumer = $consumer;

        return $this;
    }

    /**
     * Get consumer
     *
     * @return User
     */
    public function getConsumer()
    {
        return $this->consumer;
    }

    /**
     * Set account
     *
     * @param \ClfBundle\Entity\Account $account
     *
     * @return Transaction
     */
    public function setAccount(\ClfBundle\Entity\Account $account = null)
    {
        $this->account = $account;

        return $this;
    }

    /**
     * Get account
     *
     * @return \ClfBundle\Entity\Account
     */
    public function getAccount()
    {
        return $this->account;
    }

    /**
     * Set accountBank
     *
     * @param \ClfBundle\Entity\Bank $accountBank
     *
     * @return Transaction
     */
    public function setAccountBank(\ClfBundle\Entity\Bank $accountBank = null)
    {
        $this->accountBank = $accountBank;

        return $this;
    }

    /**
     * Get accountBank
     *
     * @return \ClfBundle\Entity\Bank
     */
    public function getAccountBank()
    {
        return $this->accountBank;
    }

    /**
     * Set currency
     *
     * @param \CoreBundle\Entity\Currency $currency
     *
     * @return Transaction
     */
    public function setCurrency(\CoreBundle\Entity\Currency $currency = null)
    {
        $this->currency = $currency;

        return $this;
    }

    /**
     * Get currency
     *
     * @return \CoreBundle\Entity\Currency
     */
    public function getCurrency()
    {
        return $this->currency;
    }

    /**
     * Set card
     *
     * @param \ClfBundle\Entity\Card $card
     *
     * @return Transaction
     */
    public function setCard(\ClfBundle\Entity\Card $card = null)
    {
        $this->card = $card;

        return $this;
    }

    /**
     * Get card
     *
     * @return \ClfBundle\Entity\Card
     */
    public function getCard()
    {
        return $this->card;
    }

    /**
     * Set app
     *
     * @param \ClfBundle\Entity\App $app
     *
     * @return Transaction
     */
    public function setApp(\ClfBundle\Entity\App $app = null)
    {
        $this->app = $app;

        return $this;
    }

    /**
     * Get app
     *
     * @return \ClfBundle\Entity\App
     */
    public function getApp()
    {
        return $this->app;
    }

    /**
     * Set merchantEmployee
     *
     * @param \ClfBundle\Entity\MerchantEmployee $merchantEmployee
     *
     * @return Transaction
     */
    public function setMerchantEmployee(\ClfBundle\Entity\MerchantEmployee $merchantEmployee = null)
    {
        $this->merchantEmployee = $merchantEmployee;

        return $this;
    }

    /**
     * Get merchantEmployee
     *
     * @return \ClfBundle\Entity\MerchantEmployee
     */
    public function getMerchantEmployee()
    {
        return $this->merchantEmployee;
    }

    /**
     * Set consumerBalanceBefore
     *
     * @param integer $consumerBalanceBefore
     *
     * @return Transaction
     */
    public function setConsumerBalanceBefore($consumerBalanceBefore)
    {
        $this->consumerBalanceBefore = $consumerBalanceBefore;

        return $this;
    }

    /**
     * Get consumerBalanceBefore
     *
     * @return integer
     */
    public function getConsumerBalanceBefore()
    {
        return $this->consumerBalanceBefore;
    }

    /**
     * Set consumerBalanceAfter
     *
     * @param integer $consumerBalanceAfter
     *
     * @return Transaction
     */
    public function setConsumerBalanceAfter($consumerBalanceAfter)
    {
        $this->consumerBalanceAfter = $consumerBalanceAfter;

        return $this;
    }

    /**
     * Get consumerBalanceAfter
     *
     * @return integer
     */
    public function getConsumerBalanceAfter()
    {
        return $this->consumerBalanceAfter;
    }

    /**
     * Set producerBalanceBefore
     *
     * @param integer $producerBalanceBefore
     *
     * @return Transaction
     */
    public function setProducerBalanceBefore($producerBalanceBefore)
    {
        $this->producerBalanceBefore = $producerBalanceBefore;

        return $this;
    }

    /**
     * Get producerBalanceBefore
     *
     * @return integer
     */
    public function getProducerBalanceBefore()
    {
        return $this->producerBalanceBefore;
    }

    /**
     * Set producerBalanceAfter
     *
     * @param integer $producerBalanceAfter
     *
     * @return Transaction
     */
    public function setProducerBalanceAfter($producerBalanceAfter)
    {
        $this->producerBalanceAfter = $producerBalanceAfter;

        return $this;
    }

    /**
     * Get producerBalanceAfter
     *
     * @return integer
     */
    public function getProducerBalanceAfter()
    {
        return $this->producerBalanceAfter;
    }

    /**
     * Set invoice
     *
     * @param \ClfBundle\Entity\Invoice $invoice
     *
     * @return Transaction
     */
    public function setInvoice(\ClfBundle\Entity\Invoice $invoice = null)
    {
        $this->invoice = $invoice;

        return $this;
    }

    /**
     * Get invoice
     *
     * @return \ClfBundle\Entity\Invoice
     */
    public function getInvoice()
    {
        return $this->invoice;
    }

    /**
     * Set refundFrom
     *
     * @param Transaction $refundFrom
     *
     * @return Transaction
     */
    public function setRefundFrom(Transaction $refundFrom = null)
    {
        $this->refundFrom = $refundFrom;

        return $this;
    }

    /**
     * Get refundFrom
     *
     * @return Transaction
     */
    public function getRefundFrom()
    {
        return $this->refundFrom;
    }

    /**
     * Set terminal
     *
     * @param Terminal $terminal
     *
     * @return Transaction
     */
    public function setTerminal(Terminal $terminal = null)
    {
        $this->terminal = $terminal;

        return $this;
    }

    /**
     * Get terminal
     *
     * @return Terminal
     */
    public function getTerminal()
    {
        return $this->terminal;
    }

    /**
     * Set location
     *
     * @param Location $location
     *
     * @return Transaction
     */
    public function setLocation(Location $location = null)
    {
        $this->location = $location;

        return $this;
    }

    /**
     * Get location
     *
     * @return Location
     */
    public function getLocation()
    {
        return $this->location;
    }

    /**
     * Set locationEmployee
     *
     * @param User $locationEmployee
     *
     * @return Transaction
     */
    public function setLocationEmployee(User $locationEmployee = null)
    {
        $this->locationEmployee = $locationEmployee;

        return $this;
    }

    /**
     * Get locationEmployee
     *
     * @return User
     */
    public function getLocationEmployee()
    {
        return $this->locationEmployee;
    }

	/**
	 * Set userCard
	 *
	 * @param UserCard $userCard
	 *
	 * @return Transaction
	 */
	public function setUserCard(UserCard $userCard = null)
	{
		$this->userCard = $userCard;

		return $this;
	}

	/**
	 * Get userCard
	 *
	 * @return UserCard
	 */
	public function getUserCard()
	{
		return $this->userCard;
	}

	/**
	 * Set spendr fee
	 *
	 * @param $fee
	 * @return Transaction
	 */
	public function setSpendrFee($fee)
	{
		$this->spendrFee = $fee;

		return $this;
	}

	/**
	 * Get spendr fee
	 *
	 * @return int
	 */
	public function getSpendrFee()
	{
		return $this->spendrFee;
	}

	/**
	 * Set tern fee
	 *
	 * @param $fee
	 * @return Transaction
	 */
	public function setTernFee($fee)
	{
		$this->ternFee = $fee;

		return $this;
	}

	/**
	 * Get tern fee
	 *
	 * @return int
	 */
	public function getTernFee()
	{
		return $this->ternFee;
	}

	/**
	 * Set refund fee
	 *
	 * @param $fee
	 * @return Transaction
	 */
	public function setRefundFee($fee)
	{
		$this->refundFee = $fee;

		return $this;
	}

	/**
	 * Get refund fee
	 *
	 * @return int
	 */
	public function getRefundFee()
	{
		return $this->refundFee;
	}

	/**
	 * Set meta
	 *
	 * @param string $meta
	 *
	 * @return Transaction
	 */
	public function setMeta($meta)
	{
		$this->meta = $meta;

		return $this;
	}

	/**
	 * Get meta
	 *
	 * @return string
	 */
	public function getMeta()
	{
		return $this->meta;
	}

	/**
	 * Set txnTime
	 *
	 * @param \DateTime $txnTime
	 *
	 * @return Transaction
	 */
	public function setTxnTime($txnTime)
	{
		$this->txnTime = $txnTime;

		return $this;
	}

	/**
	 * Get txnTime
	 *
	 * @return \DateTime
	 */
	public function getTxnTime()
	{
		return $this->txnTime;
	}

    /**
     * Set tip
     * @param SpendrTip $tip
     * @return Transaction
     */
    public function setTip($tip)
    {
        $this->tip = $tip;

        return $this;
    }

    /**
     * Get tip
     * @return SpendrTip
     */
    public function getTip()
    {
        return $this->tip;
    }

    /**
     * Set restrictLoad
     * @param UserCardLoad $load
     * @return Transaction
     */
    public function setRestrictLoad($load)
    {
        $this->restrictLoad = $load;

        return $this;
    }

    /**
     * Get restrictLoad
     * @return UserCardLoad
     */
    public function getRestrictLoad()
    {
        return $this->restrictLoad;
    }

    /**
     * Set txnAreaType
     *
     * @param string $txnAreaType
     *
     * @return Transaction
     */
    public function setTxnAreaType($txnAreaType)
    {
        $this->txnAreaType = $txnAreaType;

        return $this;
    }

    /**
     * Get txnAreaType
     *
     * @return string
     */
    public function getTxnAreaType()
    {
        return $this->txnAreaType;
    }

    /**
     * Set tm
     * @param TransactionMeta $tm
     * @return Transaction
     */
    public function setTm($tm)
    {
        $this->tm = $tm;

        return $this;
    }

    /**
     * Get tm
     * @return TransactionMeta
     */
    public function getTm()
    {
        return $this->tm;
    }
}
