{"type": "project", "license": "proprietary", "minimum-stability": "dev", "prefer-stable": true, "require": {"php": "^8.2", "ext-bcmath": "*", "ext-ctype": "*", "ext-curl": "*", "ext-exif": "*", "ext-ftp": "*", "ext-gd": "*", "ext-iconv": "*", "ext-imap": "*", "ext-json": "*", "ext-openssl": "*", "ext-pdo": "*", "ext-pdo_mysql": "*", "ext-redis": "*", "ext-soap": "*", "ext-xsl": "*", "ext-zip": "*", "adbario/php-dot-notation": "^3.3", "alternativepayments/alternativepayments": "^1.1", "aws/aws-sdk-php-symfony": "^2.6", "azjezz/psl": "^2.8", "beberlei/doctrineextensions": "^1.3", "devwing/cuzzle": "dev-master", "doctrine/annotations": "^2.0", "doctrine/doctrine-bundle": "^2.9", "doctrine/doctrine-migrations-bundle": "^3.2", "doctrine/orm": "^2.15", "drupol/phpermutations": "^1.4", "enqueue/async-event-dispatcher": "^0.10.18", "enqueue/enqueue-bundle": "^0.10.18", "enqueue/monitoring": "^0.10.18", "enqueue/redis": "^0.10.18", "erusev/parsedown": "^1.7", "fakerphp/faker": "^1.22", "firebase/php-jwt": "^6.5", "friendsofsymfony/rest-bundle": "^3.5", "friendsofsymfony/user-bundle": "^3.1", "giggsey/libphonenumber-for-php": "^8.13", "gmostafa/php-graphql-client": "^1.13", "gmostafa/php-graphql-oqm": "^1.5", "google/apiclient": "^2.18", "google/auth": "^1.31", "google/recaptcha": "^1.3", "guzzlehttp/guzzle": "^7.7", "hisorange/browser-detect": "^4.5", "icicleio/icicle": "^0.9.3", "illuminate/support": "^10.20", "intervention/image": "^2.7", "jms/serializer-bundle": "^5.3", "knplabs/knp-paginator-bundle": "^6.2", "laravel/helpers": "^1.6", "lstrojny/functional-php": "^1.17", "malkusch/lock": "^2.2", "mikehaertl/phpwkhtmltopdf": "^2.5", "moneyphp/money": "^4.1", "nacha/file-generator": "^1.9", "nacmartin/phpexecjs": "^3.1", "nelexa/zip": "^4.0", "nelmio/api-doc-bundle": "^4.12", "nelmio/cors-bundle": "^2.3", "nesbot/carbon": "^2.67", "nick322/secure-spreadsheet": "*", "onelogin/php-saml": "^4.1", "oro/doctrine-extensions": "^2.0", "php-http/guzzle7-adapter": "^1.0", "php-mqtt/client": "^1.8", "phpdocumentor/reflection-docblock": "^5.3", "phpoffice/phpspreadsheet": "^1.29.1", "phpro/soap-client": "^3.0", "phpseclib/mcrypt_compat": "^2.0", "phpseclib/phpseclib": "^3.0", "phpseclib/phpseclib2_compat": "^1.0", "phpstan/phpdoc-parser": "^1.21", "pragmarx/google2fa": "*", "predis/predis": "^2.1", "psr/http-message": "^1.1", "secit-pl/imap-bundle": "^2.0", "segmentio/analytics-php": "^3.7", "sgh/pdfbox": "^1.0", "snc/redis-bundle": "^4.6", "spatie/array-to-xml": "^3.1", "stof/doctrine-extensions-bundle": "^1.8", "symfony/asset": "6.4.*", "symfony/cache": "6.4.*", "symfony/console": "6.4.*", "symfony/doctrine-messenger": "6.4.*", "symfony/dotenv": "6.4.*", "symfony/expression-language": "6.4.*", "symfony/flex": "^2", "symfony/form": "6.4.*", "symfony/framework-bundle": "6.4.*", "symfony/http-client": "6.4.*", "symfony/intl": "6.4.*", "symfony/lock": "6.4.*", "symfony/mailer": "6.4.*", "symfony/mime": "6.4.*", "symfony/monolog-bundle": "^3.0", "symfony/notifier": "6.4.*", "symfony/postmark-mailer": "6.4.*", "symfony/process": "6.4.*", "symfony/property-access": "6.4.*", "symfony/property-info": "6.4.*", "symfony/runtime": "6.4.*", "symfony/security-bundle": "6.4.*", "symfony/serializer": "6.4.*", "symfony/string": "6.4.*", "symfony/translation": "6.4.*", "symfony/twig-bundle": "6.4.*", "symfony/validator": "6.4.*", "symfony/web-link": "6.4.*", "symfony/yaml": "6.4.*", "symfonycasts/reset-password-bundle": "^1.17", "symfonycasts/verify-email-bundle": "^1.13", "tecnickcom/tcpdf": "^6.7.5", "tetranz/select2entity-bundle": "^3.1", "tomorrow-ideas/plaid-sdk-php": "^1.0", "twig/extra-bundle": "^2.12|^3.0", "twig/twig": "^2.12|^3.0", "twilio/sdk": "^7.5", "vich/uploader-bundle": "^2.2", "voku/stringy": "^6.5", "wildbit/postmark-php": "^4.0", "worldline-global-collect/connect-sdk-php": "^7.1", "zircote/swagger-php": "*"}, "config": {"allow-plugins": {"php-http/discovery": true, "symfony/flex": true, "symfony/runtime": true, "phpstan/extension-installer": true}, "sort-packages": true, "vendor-dir": "./vendor"}, "autoload": {"psr-4": {"AdminBundle\\": "src/AdminBundle", "ApiBundle\\": "src/ApiBundle", "AppBundle\\": "src/AppBundle", "CashOnWebBundle\\": "src/CashOnWebBundle", "ClfBundle\\": "src/ClfBundle", "CoreBundle\\": "src/CoreBundle", "DevBundle\\": "src/DevBundle", "EsSoloBundle\\": "src/EsSoloBundle", "FaasBundle\\": "src/FaasBundle", "FisBundle\\": "src/FisBundle", "LeafLinkBundle\\": "src/LeafLinkBundle", "MobileBundle\\": "src/MobileBundle", "PortalBundle\\": "src/PortalBundle", "PTOBundle\\": "src/PTOBundle", "SalexUserBundle\\": "src/SalexUserBundle", "SkuxBundle\\": "src/SkuxBundle", "SpendrBundle\\": "src/SpendrBundle", "TransferMexBundle\\": "src/TransferMexBundle", "UsUnlockedBundle\\": "src/UsUnlockedBundle", "WilenBundle\\": "src/WilenBundle", "App\\": "src/", "GraphQL\\SchemaObject\\": "src/SkuxBundle/SkuxAPI"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/", "Tests\\": "tests/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php73": "*", "symfony/polyfill-php74": "*", "symfony/polyfill-php80": "*", "symfony/polyfill-php81": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR% --no-cleanup": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "docker": true, "require": "6.4.*"}, "phpstan": {"includes": ["extension.neon"]}, "public-dir": "web"}, "require-dev": {"codeception/module-asserts": "^3.0", "codeception/module-doctrine": "^3.1", "codeception/module-phpbrowser": "^3.0", "codeception/module-rest": "^3.3", "codeception/module-symfony": "^3.1", "codeception/verify": "^3.0", "friendsofphp/php-cs-fixer": "^3.22", "phpstan/extension-installer": "^1.4", "phpstan/phpstan": "^2", "phpstan/phpstan-doctrine": "^2.0", "phpstan/phpstan-symfony": "^2.0", "phpunit/phpunit": "^10.2", "roave/security-advisories": "dev-latest", "symfony/browser-kit": "6.4.*", "symfony/css-selector": "6.4.*", "symfony/debug-bundle": "6.4.*", "symfony/maker-bundle": "^1.0", "symfony/phpunit-bridge": "^6.2", "symfony/stopwatch": "6.4.*", "symfony/web-profiler-bundle": "6.4.*"}}