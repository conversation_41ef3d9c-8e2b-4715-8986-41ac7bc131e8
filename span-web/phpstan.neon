includes:
    - phpstan-baseline-main-2.php

parameters:
    level: 2
    paths:
        - src
    scanFiles:
        - web/index.php
        - web/secure.php
        - vendor/symfony/dependency-injection/Loader/Configurator/ContainerConfigurator.php
#    scanDirectories:
#        - /var/cache/span/dev/Symfony/Config
    symfony:
        containerXmlPath: /var/cache/span/dev/App_KernelDevDebugContainer.xml
    doctrine:
        ormRepositoryClass: CoreBundle\Repository\BaseRepository
        objectManagerLoader: tests/object-manager.php
    ignoreErrors:
        - '#Property .+ type mapping mismatch: database can contain .+ but property expects .+\.#'
        - '#Property .+ type mapping mismatch: property can contain .+ but database expects .+\.#'
#    excludePaths:
#        analyse:
#            - src/*/DataFixtures/*
#            - src/CoreBundle/Utils/BaseFixture.php
